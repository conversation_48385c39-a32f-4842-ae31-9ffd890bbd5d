<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />

    <application android:hardwareAccelerated="true">
        <activity
            android:name=".groupcall.view.fragment.AppraiseActivity"
            android:configChanges="keyboard|keyboardHidden|screenSize|orientation|smallestScreenSize|screenLayout"
            android:exported="false"
            android:launchMode="singleTask" />
        <activity
            android:name=".privatecall.view.activity.RealTimeCallActivity"
            android:configChanges="keyboard|keyboardHidden|screenSize|orientation|smallestScreenSize|screenLayout"
            android:exported="false"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:supportsPictureInPicture="true"
            android:taskAffinity="com.interfun.buz.RealTimeCallActivity"
            android:theme="@style/RouterHostTheme"
            tools:targetApi="n" />

    </application>
</manifest>