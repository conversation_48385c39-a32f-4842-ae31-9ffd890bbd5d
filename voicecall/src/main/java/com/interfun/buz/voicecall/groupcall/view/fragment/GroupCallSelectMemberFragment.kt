package com.interfun.buz.voicecall.groupcall.view.fragment

import android.graphics.Rect
import android.os.Bundle
import android.text.Editable
import android.view.View
import android.view.animation.OvershootInterpolator
import androidx.core.widget.doAfterTextChanged
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.buz.idl.group.bean.GroupMember
import com.drakeet.multitype.MultiTypeAdapter
import com.interfun.buz.base.ktx.*
import com.interfun.buz.base.utils.BusUtil
import com.interfun.buz.common.base.binding.BaseBindingFragment
import com.interfun.buz.common.bean.chat.JumpStartRealTimeCallEntrance
import com.interfun.buz.common.bean.voicecall.CallType
import com.interfun.buz.common.bean.voicecall.VoiceCallRoom
import com.interfun.buz.common.constants.KEY_CALL_TYPE
import com.interfun.buz.common.constants.KEY_SOURCE
import com.interfun.buz.common.constants.RouterParamKey
import com.interfun.buz.common.constants.RouterParamKey.Group.KEY_GROUP_ID
import com.interfun.buz.common.eventbus.BackPressEvent
import com.interfun.buz.common.eventbus.BackPressKey
import com.interfun.buz.common.eventbus.group.GroupMembersChangeEvent
import com.interfun.buz.common.ktx.getLongDefault
import com.interfun.buz.common.ktx.isSuccess
import com.interfun.buz.common.manager.voicecall.RoomLifecycle
import com.interfun.buz.common.manager.voicecall.VoiceCallPortal
import com.interfun.buz.common.voicecall.GroupCallStaterImpl
import com.interfun.buz.common.widget.button.CommonButtonColor
import com.interfun.buz.common.widget.recyclerview.itemanimators.ScaleInAnimator
import com.interfun.buz.voicecall.R
import com.interfun.buz.voicecall.databinding.GroupcallFragmentSelectMemberBinding
import com.interfun.buz.voicecall.groupcall.constants.SelectMemberSource
import com.interfun.buz.voicecall.groupcall.view.dialog.GroupCallSelectMemberDialog
import com.interfun.buz.voicecall.groupcall.view.itemdelegate.SelectMemberItemView
import com.interfun.buz.voicecall.groupcall.view.itemdelegate.SelectedMemberItemView
import com.interfun.buz.voicecall.groupcall.viewmodel.SelectMemberViewModel
import com.interfun.buz.voicecall.util.VoiceCallTracker
import com.yibasan.lizhifm.sdk.platformtools.ResUtil
import kotlinx.coroutines.sync.Mutex

/**
 * <AUTHOR>
 * @date 2024/2/6
 * @desc Fragment for group call members select page
 */
class GroupCallSelectMemberFragment : BaseBindingFragment<GroupcallFragmentSelectMemberBinding>() {

    companion object {
        const val TAG = "GroupCallSelectMemberItemView"

        fun newInstance(
            groupId: Long,
            inCallingUsers: LongArray,
            callType: @CallType Int,
            source: Int,
            entrance: @JumpStartRealTimeCallEntrance Int
        ): GroupCallSelectMemberFragment {
            return GroupCallSelectMemberFragment().apply {
                arguments = Bundle().apply {
                    putLong(KEY_GROUP_ID, groupId)
                    putLongArray(RouterParamKey.Group.KEY_USER_IDS, inCallingUsers)
                    putInt(RouterParamKey.Group.KEY_SOURCE, source)
                    putInt(RouterParamKey.Group.KEY_CALL_TYPE, callType)
                    putInt(RouterParamKey.Group.KEY_FROM_ENTRANCE, entrance)
                }
            }
        }
    }

    private var cacheMembers: List<GroupMember>? = null

    private val groupId by lazy {
        arguments?.getLong(KEY_GROUP_ID).getLongDefault()
    }
    private val inCallingUsers by lazy {
        arguments?.getLongArray(RouterParamKey.Group.KEY_USER_IDS)?: longArrayOf()
    }
    private val callType by lazy {
        arguments?.getInt(RouterParamKey.Group.KEY_CALL_TYPE)?: CallType.TYPE_VOICE
    }
    private val source by lazy {
        arguments?.getInt(RouterParamKey.Group.KEY_SOURCE)?: SelectMemberSource.START_CALL
    }

    private val entrance by lazy {
        arguments?.getInt(RouterParamKey.Group.KEY_FROM_ENTRANCE)
            ?: JumpStartRealTimeCallEntrance.GROUP_CALL_SELECT
    }

    private var mAdapter: MultiTypeAdapter = MultiTypeAdapter()

    // 选中的成员列表
    private val refreshMutex = Mutex()
    private val mSelectedAdapter by lazy {
        MultiTypeAdapter {
            register(SelectedMemberItemView(onRemoveCallback = {
                selectMemberViewModel.updateMemberSelected(it, callType)
            }))
        }
    }

    private val selectMemberViewModel by fragment.activityViewModels<SelectMemberViewModel>()
    private val groupCallStarter = GroupCallStaterImpl(this)

    override fun initData() {
        super.initData()
        selectMemberViewModel.initInCallingUsers(inCallingUsers)

        // Observe data change
        selectMemberViewModel.pageState.collectIn(viewLifecycleOwner) {
            logDebug(TAG, "pageState: $it")
            when(it){
                is SelectMemberViewModel.PageState.Loading -> {
                    binding.clLoading.visible()
                    binding.listLoading.setColorType(CommonButtonColor.COLOR_SECONDARY)
                    binding.listLoading.startLoading()
                    binding.llReloadLayout.gone()
                }
                is SelectMemberViewModel.PageState.Error -> {
                    binding.listLoading.stopLoading()
                    binding.clLoading.gone()
                    binding.llReloadLayout.visible()
                }
                is SelectMemberViewModel.PageState.Success,
                is SelectMemberViewModel.PageState.SearchResult -> {
                    val data = if (it is SelectMemberViewModel.PageState.SearchResult) {
                        it.data
                    } else {
                        (it as SelectMemberViewModel.PageState.Success).data
                    }
                    binding.listLoading.stopLoading()
                    binding.clLoading.gone()
                    binding.llReloadLayout.gone()
                    mAdapter.items = data
                    mAdapter.notifyDataSetChanged()
                    updateUI()
                }
            }
        }

        // Observe member selected change
        selectMemberViewModel.selectedMemberFlow.collectIn(viewLifecycleOwner) { selectedMembers ->
            mSelectedAdapter.asyncSubmitItems(
                fragment.viewLifecycleScope,
                refreshMutex,
                selectedMembers
            ) {
                if (selectMemberViewModel.insertMemberFlow.value) {
                    binding.rvSelectedMembers.smoothScrollToEnd()
                }
            }
        }


        binding.llReloadLayout.click {
            selectMemberViewModel.requestGroupMember()
        }


        selectMemberViewModel.groupMemberUpdateLiveData.observe(viewLifecycleOwner) {
            mAdapter.notifyDataSetChanged()
            updateUI()
        }

        // Set content value (title, button, list)
        if (groupId != 0L) {
            selectMemberViewModel.setGroupId(groupId, cacheMembers)

            binding.tvTitle.text = ResUtil.getString(R.string.select_members)

            BusUtil.observe<GroupMembersChangeEvent>(this) {
                //Get group member list
                if (groupId == it.groupId) {
                    selectMemberViewModel.requestGroupMember()
                }
            }
        }

        if(source != SelectMemberSource.START_CALL) {
            VoiceCallPortal.roomLifecycle.collectIn(viewLifecycleOwner) {
                if(it.first.isAtLeast(RoomLifecycle.INIT)){
                    val room = VoiceCallPortal.currentRoomValue
                    observeRoomEvent(room)
                }
            }
        }

        updateUI()
    }

    override fun initView() {
        binding.spaceStatusBar.initStatusBarHeight()

        // Back function
        binding.iftvLeftBack.click {
            (parentFragment as GroupCallSelectMemberDialog).dismiss()
        }
        BusUtil.observe<BackPressEvent>(BackPressKey.GROUP_INFO_DIALOG,this){
            (parentFragment as GroupCallSelectMemberDialog).dismiss()
        }

        binding.startCallBtn.click {
            startCall()
        }

        initGroupMemberRvList()
        initSelectedMemberRvList()

        binding.clRootLayout.click {  }

        updateUI()

        VoiceCallTracker.onGroupRealTimeCallSelectMember(
            groupId.toString(),
            source == SelectMemberSource.IN_CALLING
        )
        if (CallType.isVideoCall(callType)) {
            binding.startCallBtn.setIconFontText(R.string.ic_video.asString())
            binding.startCallBtn.setText(R.string.rtc_start_videocall.asString())
        } else {
            binding.startCallBtn.setIconFontText(R.string.ic_tel.asString())
            binding.startCallBtn.setText(R.string.rtc_start_voicecall.asString())
        }

        initSearchBar()
    }

    private fun initSearchBar() {
        binding.etSearch.apply {
            doAfterTextChanged {
                val keyword = it.toString().trim()
                if (keyword.isNotEmpty()) {
                    selectMemberViewModel.searchMember(keyword)
                    binding.iftvClear.visible()
                } else {
                    selectMemberViewModel.clearSearch()
                    binding.iftvClear.gone()
                }
            }

            onFocusChangeListener = View.OnFocusChangeListener { _, focus ->
                updateSearchBarUI()
                if (focus) {
                    showKeyboard()
                }
            }
            doOnKeyActionSearch {
                cancelSearch(false)
            }

            click(clickIntervals = 0) {
                binding.etSearch.requestFocus()
            }
        }


        binding.iftvClear.click(clickIntervals = 0) {
            binding.etSearch.text = Editable.Factory.getInstance().newEditable("")
        }


        binding.rlvContact.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                if (newState == RecyclerView.SCROLL_STATE_DRAGGING) {
                    cancelSearch(false)
                }
            }
        })

        binding.rvSelectedMembers.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                if (newState == RecyclerView.SCROLL_STATE_DRAGGING) {
                    cancelSearch(false)
                }
            }
        })

        binding.tvCancel.click {
            cancelSearch(true)
        }
    }

    private fun initGroupMemberRvList() {
        // Display group members in recycler view
        mAdapter =
            MultiTypeAdapter(ArrayList<MutableList<SelectMemberViewModel.GroupMemberWithCheckboxStatus>>()).apply {
                register(SelectMemberItemView(object :
                    SelectMemberItemView.OnCallBackListener {
                    override fun onMemberClick(user: SelectMemberViewModel.GroupMemberWithCheckboxStatus) {
                        selectMemberViewModel.updateMemberSelected(user, callType)
                    }

                    override fun getSearchText(): String? = binding.etSearch.text.toString().trim()
                }))
            }

        binding.rlvContact.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
            adapter = mAdapter
            itemAnimator = ScaleInAnimator(OvershootInterpolator()).apply {
                addDuration = 0
                removeDuration = 0
                moveDuration = 0
                changeDuration = 0
            }
            click {
                binding.rlvContact.requestFocus()
            }
            addItemDecoration(object : RecyclerView.ItemDecoration() {
                override fun getItemOffsets(
                    outRect: Rect,
                    view: View,
                    parent: RecyclerView,
                    state: RecyclerView.State
                ) {
                    super.getItemOffsets(outRect, view, parent, state)
                    // 获取当前item的位置
                    val position = parent.getChildAdapterPosition(view)

                    // 获取总item数量
                    val itemCount = parent.adapter?.itemCount ?: 0

                    // 如果是最后一个item，底部增加指定像素的间距,让其可以选中底部的
                    if (position == itemCount - 1) {
                        outRect.bottom = 100.dp
                    }
                }
            })
        }
    }

    private fun initSelectedMemberRvList(){
        binding.rvSelectedMembers.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            adapter = mSelectedAdapter
        }
    }

    private fun cancelSearch(clear: Boolean) {
        binding.etSearch.clearFocus()
        binding.etSearch.hideKeyboard()
        if (clear) {
            binding.etSearch.text = Editable.Factory.getInstance().newEditable("")
            binding.tvCancel.visibility = View.GONE
        } else {
            if (binding.etSearch.text.toString().isEmpty()) {
                binding.tvCancel.visibility = View.GONE
            }
        }

    }

    private fun updateSearchBarUI() {
        val isSearching = binding.etSearch.isFocused || binding.etSearch.text.toString().trim().isNotEmpty()
        binding.tvCancel.visibility = if (isSearching) View.VISIBLE else View.GONE
        binding.iftvClear.visibility = if (binding.etSearch.text.toString().isEmpty()) View.GONE else View.VISIBLE
    }

    private var hasObserver = false

    private fun startCall(){
        val selectedUserList = selectMemberViewModel.getNewSelectedRoomUsers()
        if(source == SelectMemberSource.START_CALL) {
            if (CallType.isVideoCall(callType)) {
                groupCallStarter.startGroupVideoCall(
                    context = fragment.requireContext(),
                    groupId = groupId,
                    selectMember = selectedUserList,
                    entrance = entrance
                ) {
                    dismiss()
                }
            } else {
                groupCallStarter.startGroupVoiceCall(
                    context = fragment.requireContext(),
                    groupId = groupId,
                    selectMember = selectedUserList,
                    entrance = entrance
                ) {
                    dismiss()
                }
            }
        }else{
            // 需求：正在进行的语音房间内再邀请其他人的话  选完人后点击确认需要等待请求成功后再把邀请人的界面消除
            val room = VoiceCallPortal.currentRoomValue
            if (room == null){
                logWarn(TAG, "currentRoom is null")
            }else {
                room.inviteJoinRoom(selectMemberViewModel.getNewSelectedUserIdList())
            }
        }
        VoiceCallTracker.onClickGroupRealTimeCallNextStep(
            groupId.toString(),
            selectedUserList.size,
            source == SelectMemberSource.IN_CALLING
        )
    }


    private fun observeRoomEvent(room: VoiceCallRoom?) {
        room?: return
        if (hasObserver) return
        hasObserver = true

        room.viewModel.inviteJoinRoomResult.collectIn(viewLifecycleOwner) {
            if(it.isSuccess){
                logInfo(TAG, "inviteJoinRoomResult success")
                dismiss()
            }
        }
    }

    private fun dismiss() {
        (parentFragment as GroupCallSelectMemberDialog).dismiss()
    }

    // Update UI info (call button, member count)
    private fun updateUI() {
        val maxMemberCount = selectMemberViewModel.getMaxGroupMemberCount()
        val selectedMemberCount = selectMemberViewModel.getSelectedMemberCount()

        binding.startCallBtn.isEnabled = selectedMemberCount > 1

        val countText = "$selectedMemberCount/$maxMemberCount"
        binding.tvMemberCount.text = countText

        updateSearchBarUI()
    }
}