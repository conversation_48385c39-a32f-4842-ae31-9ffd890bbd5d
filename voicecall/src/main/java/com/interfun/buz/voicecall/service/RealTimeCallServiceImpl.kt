package com.interfun.buz.voicecall.service

import android.app.Activity
import android.content.Context
import androidx.lifecycle.LifecycleOwner
import com.alibaba.android.arouter.facade.annotation.Route
import com.interfun.buz.base.ktx.LogLine
import com.interfun.buz.base.ktx.activityList
import com.interfun.buz.base.ktx.collectIn
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.base.ktx.resumedActivity
import com.interfun.buz.base.ktx.topActivity
import com.interfun.buz.common.R
import com.interfun.buz.common.arouter.startActivityByRouter
import com.interfun.buz.common.bean.chat.OnlineChatJumpInfo
import com.interfun.buz.common.bean.chat.OnlineChatJumpType
import com.interfun.buz.common.bean.voicecall.CallConflictState
import com.interfun.buz.common.bean.voicecall.CallRoomUser
import com.interfun.buz.common.bean.voicecall.CallType
import com.interfun.buz.common.bean.voicecall.ChannelType
import com.interfun.buz.common.bean.voicecall.VoiceCallRoom
import com.interfun.buz.common.constants.IncomingRealTimeCallSource
import com.interfun.buz.common.constants.JUMP_INFO
import com.interfun.buz.common.constants.PATH_ACTIVITY_REAL_TIME_CALL
import com.interfun.buz.common.constants.PATH_REAL_TIME_SERVICE
import com.interfun.buz.common.constants.RouterParamKey
import com.interfun.buz.common.interfaces.IVideoCallFloatTargetManager
import com.interfun.buz.common.ktx.getBooleanDefault
import com.interfun.buz.common.manager.realtimecall.VoiceCallMinimizeBlock
import com.interfun.buz.common.manager.voicecall.RoomLifecycle
import com.interfun.buz.common.manager.voicecall.RoomLifecycle.DESTROY
import com.interfun.buz.common.manager.voicecall.VoiceCallPortal
import com.interfun.buz.common.service.CallInterface
import com.interfun.buz.common.service.RealTimeCallService
import com.interfun.buz.im.signal.HeartBeatManager
import com.interfun.buz.voicecall.core.MinimizeStateHelper
import com.interfun.buz.voicecall.manager.RealTimeCallManager
import com.interfun.buz.voicecall.manager.VideoCallFloatTargetManager
import com.interfun.buz.voicecall.privatecall.view.activity.RealTimeCallActivity
import com.interfun.buz.voicecall.util.VoiceCallTracker
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.StateFlow
import org.json.JSONObject

@Route (path = PATH_REAL_TIME_SERVICE)
class RealTimeCallServiceImpl:RealTimeCallService,CallInterface{
    private val TAG = "RealTimeCallServiceImpl"

    // override fun startRealTimeCall(userIdList: List<Long>, groupId: Long?, channelType: Int, callType :Int, traceId : String?, onResult: (Boolean, Long?) -> Unit) {
    //     RealTimeCallManager.startRealTimeCall(userIdList, groupId, channelType,callType, traceId, onResult)
    // }
    override fun checkPermission(context: Context, targetId: Long, userIdList: List<Long>, channelType: Int, callType: Int, traceId: String?, onResult: (Boolean, Long?) -> Unit) {
        RealTimeCallManager.checkPermission(context,targetId, userIdList,channelType,callType,traceId,onResult)
    }


    override fun init(context: Context?) {

    }

    /**
     * remember check friendship first
     */
    override fun startRealTimeCall(
        userList: List<CallRoomUser>,
        channelType: @ChannelType Int,
        callType: @CallType Int,
        groupId: Long?,
        traceId: String
    ): CallConflictState {
        return VoiceCallPortal.startRealTimeCall(userList, channelType, callType, groupId, traceId, HeartBeatManager)
    }

    override fun joinRealTimeCall(
        channelId: Long,
        targetId: Long,
        channelType: @ChannelType Int,
        callType: @CallType Int,
        source: Int
    ): CallConflictState {
        return VoiceCallPortal.joinRealTimeCall(channelId, targetId, channelType, callType, source, voiceCallRoomSignal = HeartBeatManager)
    }

    override fun hangUp() {
        VoiceCallPortal.hangUp()
    }

    override fun reject(channelId: Long) {
        VoiceCallPortal.reject(channelId)
    }

    override fun onReceivePush(op: Int, sendTimestamp: Long, data: JSONObject?) {
        VoiceCallPortal.handlePush(op, sendTimestamp, data)
    }

    override fun getVoiceCallRoomIfExist(): VoiceCallRoom? {
        return VoiceCallPortal.currentRoomValue
    }

    override fun isOnRealTimeCall(): Boolean {
        return VoiceCallPortal.isOnRealTimeCall()
    }

    override fun isOnRealTimeCallEnhance(): Boolean {
        return VoiceCallPortal.isOnRealTimeCallEnhance()
    }

    override fun isInRealTimeCallPage(): Boolean {
        return topActivity is RealTimeCallActivity && resumedActivity == topActivity
    }

    override fun isVoiceCallPageExist(): Activity? {
        return activityList.find { it is RealTimeCallActivity }
    }

    override fun getMinimizeState(): StateFlow<VoiceCallMinimizeBlock.MinimizeState> {
        return MinimizeStateHelper.finalMinimizeState
    }

    override fun getMinimizeReenterInfo(): OnlineChatJumpInfo? {
        return MinimizeStateHelper.getMinimizeReenterInfo()
    }

    override fun getVoiceCallPortalScope(): CoroutineScope {
        return VoiceCallPortal.voiceCallScope
    }

    override fun registerRoomLifecycle(owner: LifecycleOwner, onDestroyed: ((channelId: Long?) -> Unit)?) {
        VoiceCallPortal.roomLifecycle.collectIn(owner){
            if (it.first == DESTROY){
                onDestroyed?.invoke(it.second)
            }
        }
    }

    override fun roomLifecycleFlow() : StateFlow<Pair<RoomLifecycle, Long?>> {
        return VoiceCallPortal.roomLifecycle
    }

    override fun onCallComeInPageView(
        channelId: Long,
        channelType: Int,
        targetId: Long,
        callType: @CallType Int,
        source: IncomingRealTimeCallSource
    ) {
        if (!ChannelType.isVoiceCallType(channelType)) return
        VoiceCallTracker.onCallIncomingCallPageView(channelId, channelType, targetId, callType, source)
    }

    override fun onClickPendAnswerEvent(
        channelId: Long,
        channelType: Int,
        callType: Int,
        isAnswer: Boolean,
        source: IncomingRealTimeCallSource
    ) {
        if (!ChannelType.isVoiceCallType(channelType)) return
        VoiceCallTracker.onClickPendAnswerEvent(
            channelId = channelId,
            channelType = channelType,
            callType = callType,
            isAnswer = isAnswer,
            source = source
        )
    }

    override fun onClickDeviceSwitchEvent(deviceType: Int) {
        VoiceCallTracker.onClickDeviceSwitchEvent(deviceType)
    }

    override fun notifyMinimizeInvited(
        shouldShow: Boolean,
        channelId: Long,
        targetId: Long,
        channelType: Int,
        callType: @CallType Int
    ) {
        if (!ChannelType.isVoiceCallType(channelType)) return
        MinimizeStateHelper.notifyMinimizeInvited(shouldShow, channelId, targetId, channelType, callType)
    }

    override fun isPipModeExist(): Boolean {
        return VoiceCallPortal.currentRoomValue?.isPipModeShowing.getBooleanDefault()
    }

    override fun getVoiceCallFloatTargetManager(): IVideoCallFloatTargetManager {
        return VideoCallFloatTargetManager()
    }

    override fun renterRealTimeCallRoom() {
        val jumpInfo = getMinimizeReenterInfo()?.copy(jumpType = OnlineChatJumpType.reentryFromMinimize) ?: return
        logInfo(TAG, "renterRealTimeCallRoom: $jumpInfo", logLine = LogLine.RTC_CALL)
        startActivityByRouter(
            path = PATH_ACTIVITY_REAL_TIME_CALL,
            buildPostcardBlock = {
                withParcelable(
                    RouterParamKey.Chat.JUMP_INFO, jumpInfo
                )
                withTransition(R.anim.anim_dialog_theme_enter, R.anim.anim_dialog_theme_out)
            }
        )
    }
}