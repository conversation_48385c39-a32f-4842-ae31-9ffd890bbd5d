package com.interfun.buz.voicecall.common.di

import com.interfun.buz.common.di.RealtimeCallCanonicalName
import com.interfun.buz.voicecall.privatecall.view.activity.RealTimeCallActivity
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
class VoiceCallModel {
    @Singleton
    @Provides
    @RealtimeCallCanonicalName
    fun provideRealtimeCallCanonicalName(): String{
        return RealTimeCallActivity::class.java.canonicalName
    }
}