package com.interfun.buz.voicecall.groupcall.constants

import androidx.annotation.IntDef
import kotlin.annotation.AnnotationRetention.SOURCE

/**
 * 文件名：CheckBoxUIStatus
 * 作用：组员UI选择状态
 * 作者：<PERSON><PERSON>
 * 创建日期：2024/2/7
 */

@Target(
    AnnotationTarget.VALUE_PARAMETER,
    AnnotationTarget.TYPE,
    AnnotationTarget.FIELD,
    AnnotationTarget.PROPERTY
)
@IntDef(
    CheckBoxUIStatus.NONE,
    CheckBoxUIStatus.UNSELECTED,
    CheckBoxUIStatus.SELECTED,
    CheckBoxUIStatus.SELECTED_DISABLE,
    CheckBoxUIStatus.UNSELECTED_DISABLE
)
@Retention(SOURCE)
annotation class CheckBoxUIStatus {
    companion object {
        const val NONE = 0                  // 不需要展示
        const val UNSELECTED = 1            // 可选
        const val SELECTED = 2              // 已选
        const val SELECTED_DISABLE = 3      // 已选不可编辑
        const val UNSELECTED_DISABLE = 4    // 不可选
    }
}