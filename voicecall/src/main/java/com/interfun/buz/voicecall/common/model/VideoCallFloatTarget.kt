package com.interfun.buz.voicecall.common.model

import androidx.palette.graphics.Palette
import com.interfun.buz.base.coroutine.CloseableCoroutineScope
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.bean.voicecall.*
import com.interfun.buz.common.interfaces.BackgroundColor
import com.interfun.buz.common.interfaces.IVideoCallFloatTarget
import com.interfun.buz.common.ktx.uid
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.manager.voicecall.VoiceCallPortal
import com.interfun.buz.common.widget.portrait.PortraitUtil
import com.interfun.buz.voicecall.R
import com.interfun.buz.voicecall.util.BitmapUtil
import com.interfun.buz.voicecall.util.VCPaletteColor
import kotlinx.coroutines.*
import kotlin.reflect.KMutableProperty0


/**
 * Author: ChenYouSheng
 * Date: 2025/2/25
 * Email: <EMAIL>
 * Desc:
 */
class VideoCallFloatTarget(
    override val isGroup: Boolean,
    override val currentUserId: Long,
    override val targetUserId: Long
) : IVideoCallFloatTarget {
    private val targetScope = CloseableCoroutineScope(
        SupervisorJob() + Dispatchers.Main.immediate + CoroutineName("VideoCallFloatTarget")
    )

    private var getTargetBackgroundJob: Job? = null
    private var getMyBackgroundJob: Job? = null
    private val callRoom get() = VoiceCallPortal.currentRoomValue
    private val members get() = callRoom?.members?.value ?: emptyList()
    private val defaultColor = appContext.getCompatColor(R.color.overlay_grey_10)
    private var targetBackgroundColor: BackgroundColor? = null
    private var myBackgroundColor: BackgroundColor? = null

    private val userInfo: CallRoomUser? get() = callRoom?.members?.value?.find { it.userId == currentUserId }
    private val targetInfo: CallRoomUser? get() = callRoom?.members?.value?.find { it.userId == targetUserId }

    //产品临时解决方案：如果群通话中其他成员都是弱网状态，则自己也展示弱网提示 [copy from GroupVoiceCallViewModel]
    private val fakeMySelfPoorNetwork: Boolean
        get() {
            val weakConnectionSize = members.filter { cRoomUser ->
                cRoomUser.userId != UserSessionManager.uid && cRoomUser.netWorkStatus >= NetworkStatus.NOT_GOOD
            }.size
            return weakConnectionSize == members.size - 1 && weakConnectionSize > 0
        }

    override fun getBackgroundColor(
        myBackgroundCallback: OneParamCallback<BackgroundColor>,
        targetBackgroundCallback: OneParamCallback<BackgroundColor>
    ) {
        getUserBackgroundColor(
            cachedColor = targetBackgroundColor,
            userPortrait = targetInfo?.portrait,
            jobRef = ::getTargetBackgroundJob,
            onResult = targetBackgroundCallback
        ) { targetBackgroundColor = it }

        getUserBackgroundColor(
            cachedColor = myBackgroundColor,
            userPortrait = userInfo?.portrait,
            jobRef = ::getMyBackgroundJob,
            onResult = myBackgroundCallback
        ) { myBackgroundColor = it }
    }

    override fun dispatchRoomUser(
        personalCallback: OneParamCallback<CallRoomUser>?,
        targetCallback: OneParamCallback<CallRoomUser>?
    ) {
        targetInfo?.let {
            targetCallback?.invoke(it)
        }
        userInfo?.let {
            val netWorkStatus = if (fakeMySelfPoorNetwork) NetworkStatus.BAD else it.netWorkStatus
            personalCallback?.invoke(it.copy(netWorkStatus = netWorkStatus))
        }
    }


    private fun getUserBackgroundColor(
        cachedColor: BackgroundColor?,
        userPortrait: String?,
        jobRef: KMutableProperty0<Job?>,
        onResult: OneParamCallback<BackgroundColor>,
        onCacheUpdate: (BackgroundColor) -> Unit
    ) {
        if (cachedColor != null) {
            return onResult(cachedColor)
        }

        if (userPortrait.isNull()) {
            return onResult(BackgroundColor(color = defaultColor))
        }

        jobRef.get()?.takeIf { it.isActive }?.cancel()
        val job = targetScope.launch {
            runCatching {
                val resizeUrl = PortraitUtil.resizePortraitUrl(userPortrait)
                val bitmap = BitmapUtil.getBitmap(resizeUrl)
                    ?: throw IllegalStateException("Bitmap is null")
                val palette = withIOContext { Palette.from(bitmap).generate() }

                val defaultColor = appContext.getCompatColor(R.color.color_646464)
                val rgbColor = VCPaletteColor.rebuildRGBColor(
                    VCPaletteColor.getPriorityColor(
                        palette, defaultColor
                    )
                )

                if (rgbColor == defaultColor) throw IllegalStateException("RGB color is default")
                val backgroundColor = BackgroundColor(
                    color = rgbColor,
                    overlay = appContext.getCompatColor(R.color.vc_palette_mask),
                )
                onCacheUpdate(backgroundColor)
                backgroundColor
            }.onSuccess(onResult).onFailure {
                onResult(BackgroundColor(color = defaultColor))
            }
        }
        jobRef.set(job)
    }


    override fun destroy() {
        targetScope.close()
    }

    override fun atLeastOneCameraOpen(): Boolean {
        return callRoom?.members?.value?.any {
            it.cameraStatus == CameraStatus.OPEN
        } == true
    }

    override fun toString(): String {
        return "[target@{userid:${targetInfo?.userId},rtcUserId:${targetInfo?.rtcUserId},cameraState:${targetInfo?.cameraStatus}}," +
                "self@{userid:${userInfo?.userId},rtcUserId:${userInfo?.rtcUserId},cameraState:${userInfo?.cameraStatus}}]"
    }

}


