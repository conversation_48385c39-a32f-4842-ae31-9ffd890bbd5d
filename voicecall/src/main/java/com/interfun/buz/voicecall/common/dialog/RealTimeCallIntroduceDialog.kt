package com.interfun.buz.voicecall.common.dialog

import android.content.DialogInterface
import android.view.View
import androidx.fragment.app.FragmentActivity
import coil.load
import com.buz.idl.common.bean.PopWindow
import com.buz.idl.common.bean.RouterInfo
import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.base.ktx.click
import com.interfun.buz.base.ktx.log
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.constants.CommonMMKV
import com.interfun.buz.common.ktx.getLongDefault
import com.interfun.buz.common.manager.AppConfigRequestManager
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.manager.router.RouterManager
import com.interfun.buz.common.service.LoginService
import com.interfun.buz.common.widget.dialog.BasePriorityBottomSheetDialogFragment
import com.interfun.buz.voicecall.R
import com.interfun.buz.voicecall.databinding.RtcDialogBinding
import com.lizhi.component.itnet.base.getVersionCodeFromManifest
import com.lizhi.component.itnet.base.getVersionNameFromManifest
import com.lizhi.im5.proto.Common


/**
 * <AUTHOR>
 * @date 2024/2/6
 * @desc Show voice call introduction pop up
 */
class RealTimeCallIntroduceDialog : BasePriorityBottomSheetDialogFragment() {

    private val binding by lazy {
        RtcDialogBinding.inflate(layoutInflater)
    }

    private var mPopWindow: PopWindow? = null
    private var routeInfo: RouterInfo? = null

    fun setVoiceCallData(popWindow: PopWindow) {
        this.mPopWindow = popWindow
    }

    override fun onCreateView(): View {
        return binding.root
    }

    companion object {
        fun newInstance(priority: Int = 0): RealTimeCallIntroduceDialog =
            RealTimeCallIntroduceDialog().apply {
                this.priority = priority
            }
    }

    override fun initView(view: View?) {
        binding.ivImage.load(R.drawable.voicecall_dialog)
    }


    /*
      * latest Application Version = -1
      * If application version is equal to the current version means it can found the previous version
      * If not equal to the current version, means new download
      * If updated version, display dialog
      * If new downloaded, return false
      *
    */
    override suspend fun canShowNow(hostActivity: FragmentActivity): Boolean {
        return (AppConfigRequestManager.enableGroupVoiceCall || AppConfigRequestManager.enableFriendVoiceCall) && CommonMMKV.showRealTimeCallDialog && !CommonMMKV.isRealTimeCallDialogShowed && setIfNeedShowVoiceCallDialog().also {
            CommonMMKV.isRealTimeCallDialogShowed = it
        }
    }


    private fun setIfNeedShowVoiceCallDialog(): Boolean {
        val currentVersion = getVersionCodeFromManifest()
        val lastVersion = CommonMMKV.lastClearNotificationChannelVersion
        val isNewUser = routerServices<LoginService>().value?.isNewEditedInfoRegister() ?: false
        val isFirstLaunch = UserSessionManager.isFirstTimeLoginAtThisDevice
        val isVersionUpdated = currentVersion != lastVersion || lastVersion != -1
        CommonMMKV.latestApplicationVersion = currentVersion

        return if (isVersionUpdated && !isNewUser && !isFirstLaunch) {
            true
        } else {
            CommonMMKV.showRealTimeCallDialogInThisVersion = false
            CommonMMKV.showRealTimeCallDialog = false
            false
        }

    }

    override fun initListener(view: View?) {
        binding.btnGotIt.click {
            dismiss()
        }

        binding.ivArrow.click {
            dismiss()
        }
    }

    override fun onShow(dialog: DialogInterface) {
        super.onShow(dialog)
        savePopHistory()
    }

    private fun savePopHistory() {
        val cacheIds = CommonMMKV.voiceCallDialogPopIds?.toMutableSet() ?: mutableSetOf()
        cacheIds.add("${mPopWindow?.id.getLongDefault()}")
        CommonMMKV.voiceCallDialogPopIds = cacheIds
    }
}
