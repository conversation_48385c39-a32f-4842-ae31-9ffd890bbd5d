package com.interfun.buz.voicecall.privatecall.view.fragment

import android.app.Dialog
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.fragment.app.DialogFragment
import androidx.hilt.navigation.compose.hiltViewModel
import com.interfun.buz.base.ktx.fragmentViewModels
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.base.ktx.toast
import com.interfun.buz.base.ktx.toastOnlyForeground
import com.interfun.buz.base.ktx.viewLifecycleScope
import com.interfun.buz.compose.components.*
import com.interfun.buz.compose.components.bottomsheet.BuzModalBottomSheet
import com.interfun.buz.compose.ktx.HorizontalSpace
import com.interfun.buz.compose.ktx.VerticalSpace
import com.interfun.buz.compose.styles.TextStyles
import com.interfun.buz.voicecall.R
import com.interfun.buz.voicecall.privatecall.viewmodel.CallUserState
import com.interfun.buz.voicecall.privatecall.viewmodel.IInviteGroupMember
import com.interfun.buz.voicecall.privatecall.viewmodel.InviteGroupMemberViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach

@Composable
@Preview
private fun MainContentPreview() {
//    MainContent()
}


@Composable
@Preview
private fun UserItemPreview() {
//    com.yibasan.lizhifm.sdk.platformtools.ApplicationContext.init(LocalContext.current)
//    MMKV.initialize(LocalContext.current)
//    MmkvSharedPreferences.getSharedPreferences()
    InitPreview()
    UserItem("https://www.xx.com", "userName", attachInfo = {})
}

@Composable
private fun ShowCalling() {
    Text(
        text = stringResource(R.string.rtc_calling),
        style = TextStyles.bodyMedium(),
        modifier = Modifier.fillMaxWidth(),
        maxLines = 1,
        color = colorResource(R.color.color_text_highlight_default),
        overflow = TextOverflow.Ellipsis
    )
}

@Composable
private fun UserItem(
    portraitUrl: String,
    userName: String,
    attachInfo: @Composable () -> Unit = {},
    showJoin: Boolean = false,
    showBtnLoading: Boolean = false,
    onBtnClick: () -> Unit = {}
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier
            .fillMaxWidth()
            .height(64.dp)
    ) {
        HorizontalSpace(20.dp)
        PortraitImage(modifier = Modifier.size(48.dp), url = portraitUrl)
        HorizontalSpace(16.dp)
        Column(
            modifier = Modifier
                .weight(1f)
                .fillMaxHeight(),
            verticalArrangement = Arrangement.Center
        ) {
            Text(
                text = userName,
                style = TextStyles.labelLarge(),
                modifier = Modifier.fillMaxWidth(),
                color = colorResource(R.color.color_text_white_primary),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )

            attachInfo()
        }
        if (showJoin) {
            JoinBtn(
                onClick = { onBtnClick.invoke() },
                iconRes = R.string.ic_video,
                showLoading = showBtnLoading
            )
        }
        HorizontalSpace(20.dp)

    }
}

@Composable
private fun JoinBtn(
    modifier: Modifier = Modifier,
    iconRes: Int = R.string.ic_video,
    showLoading: Boolean = false,
    onClick: () -> Unit
) {
    CommonButton(
        showLoading = showLoading,
        modifier = modifier
            .wrapContentWidth()
            .height(32.dp),
        iconContent = {
            IconFontText(
                iconRes = iconRes,
                iconSize = CommonButtonType.MAIN_SMALL.size.iconSize.dp,
                iconColor = CommonButtonType.MAIN_MEDIUM.color.getTextColor()

            )
            Spacer(modifier = Modifier.width(4.dp))
        },
        type = CommonButtonType.MAIN_SMALL,
        text = stringResource(R.string.call),
        onClick = onClick
    )
}

@Composable
@OptIn(ExperimentalMaterial3Api::class)
private fun MainContent(inviteGroupMember: IInviteGroupMember, onClose: () -> Unit) {
    val memberListState = inviteGroupMember.userListFLow().collectAsState()
    val groupNameState = inviteGroupMember.obtainGroupMember().collectAsState()
    val (inCallList, notInCallList) = memberListState.value.partition {
        it.state == CallUserState.IN_CALLED
    }
    BuzModalBottomSheet(
        modifier = Modifier
            .fillMaxWidth()
            .statusBarsPadding()
            .fillMaxHeight(),
        onDismissRequest = { onClose.invoke() },
        nestedScrollEnable = true
    ) {
        Column(Modifier.fillMaxSize()) {
            Box(
                modifier = Modifier
                    .height(64.dp)
                    .fillMaxWidth()
            ) {
                IconFontBack(onClick = onClose, modifier = Modifier.align(Alignment.CenterStart))
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 70.dp)
                        .align(Alignment.Center),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = stringResource(R.string.select_members),
                        maxLines = 1,
                        textAlign = TextAlign.Center,
                        overflow = TextOverflow.Ellipsis,
                        style = TextStyles.titleMedium(),
                        color = colorResource(R.color.color_foreground_neutral_important_default)
                    )
                    Text(
                        text = groupNameState.value,
                        maxLines = 1,
                        textAlign = TextAlign.Center,
                        overflow = TextOverflow.Ellipsis,
                        style = TextStyles.bodyMedium(),
                        color = colorResource(R.color.color_text_white_tertiary)
                    )
                }
            }
            VerticalSpace(12.dp)
            LazyColumn(modifier = Modifier.weight(1f)) {
                item {
                    Text(
                        text = stringResource(R.string.rtc_in_this_call).uppercase(),
                        modifier = Modifier.padding(start = 20.dp),
                        style = TextStyles.bodySmall(),
                        color = colorResource(R.color.color_text_white_tertiary)
                    )
                    VerticalSpace(12.dp)
                }
                items(inCallList) { item ->
                    UserItem(item.portraitUrl, item.userName)
                }
                if (notInCallList.isNotEmpty()) {
                    item {
                        VerticalSpace(26.dp)
                        Text(
                            text = stringResource(R.string.rtc_who_have_not_join).uppercase(),
                            modifier = Modifier.padding(start = 20.dp),
                            style = TextStyles.bodySmall(),
                            color = colorResource(R.color.color_text_white_tertiary),
                        )
                        VerticalSpace(6.dp)
                    }
                }
                items(notInCallList) { item ->
                    UserItem(
                        portraitUrl = item.portraitUrl,
                        userName = item.userName,
                        showJoin = item.state != CallUserState.BE_CALLED,
                        showBtnLoading = item.isShowLoading,
                        attachInfo = {
                            if (item.state == CallUserState.BE_CALLED) {
                                ShowCalling()
                            }
                        },
                        onBtnClick = {
                            inviteGroupMember.inviteUser(item.uid)
                        })
                }
            }
        }
    }
}

@AndroidEntryPoint
class InviteGroupMemberDialogFragment : DialogFragment() {


    //    val inviteGroupMemberViewModel by fragmentViewModels<InviteGroupMemberViewModel>()
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return ComposeView(requireContext()).apply {
            setContent {
                val inviteGroupMemberViewModel = hiltViewModel<InviteGroupMemberViewModel>()
                LaunchedEffect(Unit) {
                    inviteGroupMemberViewModel.toastFlow?.collect { toastMsg ->
                        toastMsg.toastOnlyForeground()
                        logInfo(TAG, "onCreateDialog() called with: toastMsg = $toastMsg")
                    }
                }
                inviteGroupMemberViewModel.initRequest(target)
                MainContent(inviteGroupMemberViewModel, onClose = { dismiss() })
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

    }


    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {

        return super.onCreateDialog(savedInstanceState)
    }

    private val target by lazy { requireArguments().getLong(KEY_TARGETS, -1) }

    companion object {
        const val KEY_TARGETS = "KEY_TARGETS"
        val TAG = "InviteGroupMemberDialogFragment"
        fun newInstance(minimizeTargetId: Long): InviteGroupMemberDialogFragment {
            val args = Bundle()
            args.putLong(KEY_TARGETS, minimizeTargetId)
            val fragment = InviteGroupMemberDialogFragment()
            fragment.arguments = args
            return fragment
        }
    }

}