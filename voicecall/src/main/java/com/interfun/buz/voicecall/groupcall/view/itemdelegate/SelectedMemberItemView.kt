package com.interfun.buz.voicecall.groupcall.view.itemdelegate

import com.interfun.buz.base.ktx.OneParamCallback
import com.interfun.buz.base.ktx.click
import com.interfun.buz.base.ktx.visibleIf
import com.interfun.buz.common.base.binding.BaseBindingDelegate
import com.interfun.buz.voicecall.databinding.GroupcallSelectedMemberItemViewBinding
import com.interfun.buz.voicecall.groupcall.constants.CheckBoxUIStatus
import com.interfun.buz.voicecall.groupcall.viewmodel.SelectMemberViewModel

/**
 * Author: ChenYouSheng
 * Date: 2025/4/16
 * Email: <EMAIL>
 * Desc:
 */
class SelectedMemberItemView(val onRemoveCallback: OneParamCallback<SelectMemberViewModel.GroupMemberWithCheckboxStatus>) :
    BaseBindingDelegate<SelectMemberViewModel.GroupMemberWithCheckboxStatus,
            GroupcallSelectedMemberItemViewBinding>() {

    override fun onBindViewHolder(
        binding: GroupcallSelectedMemberItemViewBinding,
        item: SelectMemberViewModel.GroupMemberWithCheckboxStatus,
        position: Int
    ) {
        binding.ivPortrait.setPortrait(item.member.userInfo.portrait)
        binding.iftClose.visibleIf(item.status == CheckBoxUIStatus.SELECTED)

        binding.clRoot.click(clickIntervals = 500) {
            if (item.status == CheckBoxUIStatus.SELECTED) {
                onRemoveCallback.invoke(item)
            }
        }
    }

}