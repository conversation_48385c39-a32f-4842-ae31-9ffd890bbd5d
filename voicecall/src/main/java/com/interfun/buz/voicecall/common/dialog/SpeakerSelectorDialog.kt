package com.interfun.buz.voicecall.common.dialog

import android.os.Bundle
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.drakeet.multitype.MultiTypeAdapter
import com.interfun.buz.base.ktx.OneParamCallback
import com.interfun.buz.base.ktx.parcelableArrayList
import com.interfun.buz.common.bean.voicecall.AudioDevice
import com.interfun.buz.common.bean.voicecall.DeviceInfoItemOption
import com.interfun.buz.common.databinding.CommonBottomListDialogBinding
import com.interfun.buz.common.widget.dialog.BaseBottomSheetDialogFragment
import com.interfun.buz.voicecall.common.dialog.DeviceInfoItemView.ItemClickListenerCallback

/**
 * @Desc
 * @Author:<EMAIL>
 * @Date: 2024/3/13
 */
class SpeakerSelectorDialog: BaseBottomSheetDialogFragment() {

    companion object{
        const val KEY_DIALOG_LIST = "dialog_list"

        fun create(
            optionList: ArrayList<DeviceInfoItemOption>,
            dialogCallBack: (select: DeviceInfoItemOption) -> Unit
        ): SpeakerSelectorDialog {
            return SpeakerSelectorDialog().apply {
                arguments = Bundle().apply {
                    putParcelableArrayList(
                        KEY_DIALOG_LIST,
                        ArrayList<DeviceInfoItemOption>(optionList)
                    )
                }
                onOptionClick = { deviceInfo ->
                    dialogCallBack.invoke(deviceInfo)
                }
            }
        }
    }

    private val binding by lazy { CommonBottomListDialogBinding.inflate(layoutInflater) }
    private val optionList by lazy {
        arguments?.parcelableArrayList<DeviceInfoItemOption>(KEY_DIALOG_LIST) ?: emptyList()
    }
    private var deviceAdapter: MultiTypeAdapter? = null
    private var onOptionClick: OneParamCallback<DeviceInfoItemOption>? = null

    fun setDeviceChanged(router: Int){
        val deviceInfoItemOption = optionList.find {
            it.type == router
        }
        deviceInfoItemOption?.apply{
            showLoading = false
            selected = true
        }
        deviceAdapter?.notifyDataSetChanged()
        dismiss()
    }

    override fun onCreateView() = binding.root

    override fun initView(view: View?) {
        deviceAdapter = MultiTypeAdapter(optionList).apply {
            register(
                DeviceInfoItemOption::class.java,
                DeviceInfoItemView(object :
                    ItemClickListenerCallback {
                    override fun onItemClick(item: DeviceInfoItemOption) {
                        if (item.selected){
                            return
                        }
                        if (item.type != AudioDevice.AUDIO_ROUTE_BLUETOOTH_DEVICE.routing){
                            optionList.forEach {
                                it.showLoading = it.type == item.type
                                if (it.selected){
                                    it.selected = false
                                }
                            }
                            deviceAdapter?.notifyDataSetChanged()
                        }
                        onOptionClick?.invoke(item)
                    }
                })
            )
        }
        binding.rvDialogList.let {
            it.layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
            it.adapter = deviceAdapter
        }
    }

    override fun initListener(view: View?) {
    }

}