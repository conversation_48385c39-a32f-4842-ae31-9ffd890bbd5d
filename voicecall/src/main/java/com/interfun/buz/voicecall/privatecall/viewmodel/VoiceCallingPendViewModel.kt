package com.interfun.buz.voicecall.privatecall.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.interfun.buz.base.ktx.logError
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.bean.chat.OnlineChatJumpInfo
import com.interfun.buz.common.bean.voicecall.*
import com.interfun.buz.common.database.entity.UserRelationInfo
import com.interfun.buz.common.database.entity.chat.GroupInfoBean
import com.interfun.buz.common.manager.cache.group.GroupInfoCacheManager
import com.interfun.buz.common.manager.cache.user.UserRelationCacheManager
import com.interfun.buz.common.manager.voicecall.VoiceCallPortal
import com.interfun.buz.onair.standard.IGlobalOnAirController
import com.interfun.buz.voicecall.privatecall.bean.RealTimeCallPendState
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

class VoiceCallingPendViewModel : ViewModel() {

    val wtAnswerStateFlow = MutableSharedFlow<RealTimeCallPendState>()

    val baseInfo = MutableStateFlow<BaseInfo?>(null)

    private val _micStatus = MutableStateFlow(MicStatus.OPEN)
    val micStatus get() = _micStatus.asStateFlow()
    private val _cameraState = MutableStateFlow(CameraState.FRONT)
    val cameraState get() = _cameraState.asStateFlow()
    private val _cameraStatus = MutableStateFlow(CameraStatus.CLOSE)
    val cameraStatus get() = _cameraStatus.asStateFlow()
    val realTimeCallPendState = RealTimeCallPendState(
        cameraStatus = null,
        cameraState = null,
        micStatus = null
    )

    data class BaseInfo(
        val userInfo: UserRelationInfo?, val groupInfoBean: GroupInfoBean? = null
    )

    fun rejectCall(channelId: Long?, channelType: @ChannelType Int) {
        if (ChannelType.isVoiceCallType(channelType)) {
            channelId?.let {
                VoiceCallPortal.reject(it)
            }
        }else if (ChannelType.isLivePlaceType(channelType)){
            channelId?.let {
                routerServices<IGlobalOnAirController>().value?.hanUp(it)
            }
        }
    }

    fun initJumpInfo(onlineChatJumpInfo: OnlineChatJumpInfo?, groupCallerUserId: Long?) {
        onlineChatJumpInfo ?: return
        viewModelScope.launch(Dispatchers.IO) {
            val targetId = onlineChatJumpInfo.targetId

            when (onlineChatJumpInfo.channelType) {
                ChannelType.TYPE_LIVE_PLACE_GROUP,
                ChannelType.TYPE_VOICE_CALL_GROUP -> {
                    val groupInfo = GroupInfoCacheManager.getGroupInfoBeanByIdSync(targetId)
                    val userInfo = UserRelationCacheManager.getUserRelationInfoByUidSync(
                        groupCallerUserId ?: 0L
                    )
                    baseInfo.emit(BaseInfo(userInfo = userInfo, groupInfoBean = groupInfo))
                }
                ChannelType.TYPE_LIVE_PLACE_PRIVATE,
                ChannelType.TYPE_VOICE_CALL_1V1 -> {
                    UserRelationCacheManager.getUserRelationInfoByUidSync(targetId)
                        ?.let { userRelationInfo ->
                            baseInfo.emit(BaseInfo(userInfo = userRelationInfo))
                        }
                }

                else -> {
                    logError(
                        "VoiceCallingPendViewModel",
                        "initJumpInfo: onlineChatJumpInfo both userid and groupid is null"
                    )
                }
            }
        }
    }

    fun updateCameraStatus(cameraStatus: @CameraStatus Int) {
        realTimeCallPendState.cameraStatus = cameraStatus
        _cameraStatus.value = cameraStatus
    }

    fun updateCameraState(cameraState: @CameraState Int) {
        realTimeCallPendState.cameraState = cameraState
        _cameraState.value = cameraState
    }

    fun updateMicStatus(micStatus: @MicStatus Int) {
        realTimeCallPendState.micStatus = micStatus
        _micStatus.value = micStatus
    }
}