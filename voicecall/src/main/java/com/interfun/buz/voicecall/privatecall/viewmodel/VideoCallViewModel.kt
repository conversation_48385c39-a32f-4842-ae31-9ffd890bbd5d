package com.interfun.buz.voicecall.privatecall.viewmodel

import android.app.Activity
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.buz.idl.realtimecall.request.RequestGetGroupRealTimeCallInfo
import com.buz.idl.realtimecall.service.BuzNetRealTimeCallServiceClient
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.bean.voicecall.*
import com.interfun.buz.common.database.entity.UserRelationInfo
import com.interfun.buz.common.database.entity.chat.GroupInfoBean
import com.interfun.buz.common.eventbus.voicecall.ExcGroupOnCallDialogEvent
import com.interfun.buz.common.ktx.CODE_NO_REAL_TIME_CALL
import com.interfun.buz.common.ktx.isMe
import com.interfun.buz.common.ktx.isSuccess
import com.interfun.buz.common.manager.cache.group.GroupInfoCacheManager
import com.interfun.buz.common.manager.cache.user.UserRelationCacheManager
import com.interfun.buz.common.manager.voicecall.VoiceCallPortal
import com.interfun.buz.common.net.withConfig
import com.interfun.buz.onair.helper.SeatBgPaletteHelper
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch

class VideoCallViewModel : ViewModel() {

    companion object {
        private const val TAG = "VideoCallViewModel"
    }
    private val client by lazy {
        BuzNetRealTimeCallServiceClient().withConfig()
    }
    private val tempTextureViewStateList = mutableListOf<TextureViewStateInfo>()
    private val _textureViewStateSharedFlow = MutableSharedFlow<List<TextureViewStateInfo>?>()
    val textureViewStateSharedFlow: SharedFlow<List<TextureViewStateInfo>?> = _textureViewStateSharedFlow.asSharedFlow()

    private val _userSize: MutableStateFlow<Int?> = MutableStateFlow(null)
    val userSize: StateFlow<Int?> = _userSize.asStateFlow()

    private val _updateUserState = MutableSharedFlow<Unit>()
    val updateUserState: SharedFlow<Unit> = _updateUserState.asSharedFlow()

    private val _baseInformation: MutableStateFlow<BaseInformation?> = MutableStateFlow(null)
    val baseInformation: StateFlow<BaseInformation?> = _baseInformation.asStateFlow()


    private val _stateToastInformationList = mutableListOf<StateToastInformation>()
    private val _stateToastInsertShareFlow = MutableSharedFlow<Int>()
    private val _stateToastRemoveShareFlow = MutableSharedFlow<Int>()
    private val _stateToastNotifyDataChangedShareFlow = MutableSharedFlow<Unit>()

    val stateToastInformationList: List<StateToastInformation> get() = _stateToastInformationList
    val stateToastInsertShareFlow: SharedFlow<Int> get() = _stateToastInsertShareFlow.asSharedFlow()
    val stateToastRemoveShareFlow: SharedFlow<Int> get() = _stateToastRemoveShareFlow.asSharedFlow()
    val stateToastNotifyDataSetChangedShareFlow: SharedFlow<Unit> get() = _stateToastNotifyDataChangedShareFlow.asSharedFlow()

    private val _myMicStatus = MutableSharedFlow<Boolean>()
    val myMicStatus: SharedFlow<Boolean> = _myMicStatus.asSharedFlow()

    private val _myNetworkStatus = MutableSharedFlow<Boolean>()
    val myNetworkStatus: SharedFlow<Boolean> = _myNetworkStatus.asSharedFlow()

    private val _myCamStatus = MutableSharedFlow<Boolean>()

    private val _userMicStatus = MutableSharedFlow<CallRoomUser>()
    val userMicStatus: SharedFlow<CallRoomUser> = _userMicStatus.asSharedFlow()

    private val _userNetworkStatus = MutableSharedFlow<CallRoomUser>()
    val userNetworkStatus: SharedFlow<CallRoomUser> = _userNetworkStatus.asSharedFlow()

    private val _userCamStatus = MutableSharedFlow<CallRoomUser>()
    val userCamStatus: SharedFlow<CallRoomUser> = _userCamStatus.asSharedFlow()

    val seatsVisibleFlow = channelFlow {
        VoiceCallPortal.currentRoom.collectLatest { room ->
            if (room.isNull()) return@collectLatest
            room?.members?.collectLatest { members ->
                val onCallMember = members.filter { member -> member.isOnCall() }
                if (onCallMember.size <= 2) {
                    send(false)
                    logInfo(TAG, "VideoCallViewModel seatsVisibleFlow send(false)", logLine = LogLine.MY_RTC_CAMERA_STATE)
                } else {
                    send(true)
                    logInfo(TAG, "VideoCallViewModel seatsVisibleFlow send(true)", logLine = LogLine.MY_RTC_CAMERA_STATE)
                }
            }
        }
    }.stateIn(viewModelScope, SharingStarted.Lazily, false)

    val myCamStatus = combine(_myCamStatus, seatsVisibleFlow, VoiceCallPortal.isPipModeShowingFlow) { myCamStatus, multiMember, isPipModeShowing ->
        logInfo(
            TAG,
            "VideoCallViewModel myCamStatus combine myCamStatus=${myCamStatus},multiMember=${multiMember},isPipModeShowing=${isPipModeShowing}",
            logLine = LogLine.MY_RTC_CAMERA_STATE
        )
        return@combine if (!multiMember && !isPipModeShowing) {
            myCamStatus
        } else null
    }.onEach {
        logInfo(TAG, "VideoCallViewModel myCamStatus combine onEach result isCameraOpen = $it", logLine = LogLine.MY_RTC_CAMERA_STATE)
    }.shareIn(viewModelScope, SharingStarted.Lazily)


    fun checkJoinOrExist(
        activity: Activity, targetId: Long, callType: @CallType Int, callback: DefaultCallback
    ) {
        val request = RequestGetGroupRealTimeCallInfo(targetId)
        viewModelScope.launch {
            val response = client.getGroupRealTimeCallInfo(request)
            logInfo(TAG, "check:groupId = $targetId,,code=${response.code}")
            if (response.isSuccess) {
                val channelId = response.data?.groupCallInfo?.channelId
                if (channelId.isNull()) {
                    ExcGroupOnCallDialogEvent.post(activity = activity)
                } else {
                    ExcGroupOnCallDialogEvent.post(
                        activity = activity,
                        type = ExcGroupOnCallDialogEvent.TYPE_JOIN_SAME_ROOM,
                        channelId = channelId,
                        targetId = targetId,
                        callType = callType
                    )
                }
            } else if (response.code == CODE_NO_REAL_TIME_CALL) {
                ExcGroupOnCallDialogEvent.post(activity = activity)
            }
            callback.invoke()
        }
    }

    /**
     * Fetch base information of user or group
     */
    fun fetchBaseInformation(targetId: Long, isPrivate: Boolean) {
        viewModelScope.launch {
            if (isPrivate) {
                UserRelationCacheManager.getUserRelationInfoByUidSync(targetId)?.let {
                    _baseInformation.value = BaseInformation(
                        userRelationInfo = it,
                        seatColor = it.portrait?.let { portrait ->
                            SeatBgPaletteHelper.obtainColorFromRelation(portrait)
                        }
                    )
                }
            } else {
                GroupInfoCacheManager.getGroupInfoBeanByIdSync(targetId)?.let {
                    _baseInformation.value = BaseInformation(
                        groupInfoBean = it
                    )
                }
            }
        }
    }

    fun addStateToastInformation(info: StateToastInformation) {
        if (stateToastInformationList.contains(info)) return
        if (RealTimeCallToastInfo.requireUsername(info.toastStyle) && info.username == null) return
        launch {
            _stateToastInformationList.add(info)
            logDebug(TAG, "addStateToastInformation: $info at index: ${stateToastInformationList.size - 1}")
            _stateToastInsertShareFlow.emit(stateToastInformationList.size - 1)
        }
    }

    fun removeStateToastInformation(info: StateToastInformation) {
        val index = stateToastInformationList.indexOf(info)
        if (index == -1) return
        launch {
            logDebug(TAG, "removeStateToastInformation: $info at index: $index")
            _stateToastInformationList.remove(info)
            _stateToastRemoveShareFlow.emit(index)
        }
    }

    fun clearToastInformation() {
        launch {
            logDebug(TAG, "clearToastInformation: ${stateToastInformationList.size}")
            _stateToastInformationList.clear()
            _stateToastNotifyDataChangedShareFlow.emit(Unit)
        }
    }

    // 添加视频预览状态
    fun addTextureViewStateList(userId: Long) {
        if (tempTextureViewStateList.findSafety { it.userId == userId }.isNotNull()) return
        val textureViewState = TextureViewStateInfo(userId)
        tempTextureViewStateList.add(textureViewState)
        logDebug(TAG, "addTextureViewStateList: ${tempTextureViewStateList.size}")
    }

    fun clearTextureViewStateList() {
        tempTextureViewStateList.clear()
        launch {
            _textureViewStateSharedFlow.emit(tempTextureViewStateList)
        }
    }

    fun getTextureViewState(userId: Long): @TextureViewState Int? {
        return tempTextureViewStateList.findSafety { it.userId == userId }?.state
    }

    fun getTargetTextureViewState(): @TextureViewState Int? {
        return tempTextureViewStateList.findSafety { it.userId.isMe().not() }?.state
    }

    fun getMyTextureViewState(): @TextureViewState Int? {
        return tempTextureViewStateList.findSafety { it.userId.isMe() }?.state
    }
    
    fun findCollapseStateUserId():Long?{
        return tempTextureViewStateList.findSafety { it.isExpand().not() }?.userId
    }

    // 互换个人和对方视频的大小屏状态
    fun switchTextureViewState() {
        logDebug(TAG, "switchTextureViewState")
        tempTextureViewStateList.forEach { textureViewState ->
            textureViewState.apply {
                playAnimation = false
                state = TextureViewState.toggle(state)
            }
        }
        launch {
            _textureViewStateSharedFlow.emit(tempTextureViewStateList)
        }
    }

    // 更新TextureView的状态，并播放动画
    fun updateTextureViewState(textureViewStateInfo: List<Pair<Long, @TextureViewState Int>>, playAnimation: Boolean = true) {
        for (info in textureViewStateInfo) {
            logDebug(TAG, "updateTextureViewStateWithAnimation(${info.first}): ${info.second}")
            tempTextureViewStateList.findSafety { it.userId == info.first }?.apply {
                this.state = info.second
                this.playAnimation = playAnimation
            }
        }
        logDebug(TAG, "tempTextureViewStateList(${tempTextureViewStateList.size}): ${tempTextureViewStateList.map { it.userId }}")
        launch {
            _textureViewStateSharedFlow.emit(tempTextureViewStateList)
        }
    }

    fun updateUserState() {
        launch {
            _updateUserState.emit(Unit)
        }
    }

    fun updateMyMicStatus(isMuted: Boolean) {
        launch {
            _myMicStatus.emit(isMuted)
        }
    }

    fun updateMyNetworkState(isPoorNetwork: Boolean) {
        launch {
            _myNetworkStatus.emit(isPoorNetwork)
        }
    }

    fun updateMyCamStatus(isOpen: Boolean) {
        launch {
            logInfo(TAG,"updateMyCamStatus isOpen = $isOpen", logLine = LogLine.MY_RTC_CAMERA_STATE)
            _myCamStatus.emit(isOpen)
        }
    }

    fun updateUserMicStatus(user: CallRoomUser) {
        launch {
            _userMicStatus.emit(user)
        }
    }

    fun updateUserNetworkState(user: CallRoomUser) {
        launch {
            _userNetworkStatus.emit(user)
        }
    }

    fun updateUserCamStatus(user: CallRoomUser) {
        launch {
            _userCamStatus.emit(user)
        }
    }

    fun updateMemberSize(userSize: Int) {
        _userSize.value = userSize
    }
}

data class BaseInformation(
    val userRelationInfo: UserRelationInfo? = null,
    val groupInfoBean: GroupInfoBean? = null,
    val seatColor: Int? = null
)

data class StateToastInformation(
    val toastStyle: @RealTimeCallToastInfo String,
    val username: String? = null,
    val portrait: String? = null
)

data class TextureViewStateInfo(
    val userId: Long,
    var state: @TextureViewState Int = TextureViewState.EXPAND,
    var playAnimation: Boolean = false
) {
    fun isExpand() = state == TextureViewState.EXPAND
}

data class VideoContainerAnimationInfo(
    val width: Pair<Float, Float>,
    val height: Pair<Float, Float>,
    val x: Pair<Float, Float>,
    val y: Pair<Float, Float>,
    val radius: Pair<Float, Float>? = null
)

