package com.interfun.buz.voicecall.common.view.widget

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.View
import android.view.ViewConfiguration
import com.interfun.buz.base.ktx.DefaultCallback
import com.interfun.buz.base.ktx.logError
import com.interfun.buz.base.ktx.logWarn
import kotlin.math.sqrt

class DraggableRealTimeCallUserCard @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null)
    : RealTimeCallUserCard(context, attrs) {

    companion object {
        const val TAG = "DraggableConstraintLayout"
    }

    private var dX = 0f
    private var dY = 0f
    private var startX = 0f
    private var startY = 0f

    private var isDragEnable = true
    private var isSnapToSideEnable = true

    private val dragThreshold = ViewConfiguration.get(context).scaledTouchSlop / 2

    // The view that defines the draggable area
    var draggableArea: View? = null

    private val gestureDetector =
        GestureDetector(context, object : GestureDetector.SimpleOnGestureListener() {
            override fun onSingleTapUp(event: MotionEvent): Boolean {
                performClick()
                return true
            }
        })

    override fun onInterceptTouchEvent(event: MotionEvent): Boolean {
        return true // Intercepts touch events to allow child to handle them
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(event: MotionEvent): Boolean {
        if (!isDragEnable) return super.onTouchEvent(event)

        if (isClickEnable() && gestureDetector.onTouchEvent(event)) {
            return true
        }

        if (draggableArea == null) {
            logError(TAG, "draggableArea is null! Dragging may not work correctly.")
        }

        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                parent?.requestDisallowInterceptTouchEvent(true)
                dX = event.rawX - x
                dY = event.rawY - y
                startX = event.rawX
                startY = event.rawY
            }

            MotionEvent.ACTION_MOVE -> {
                draggableArea?.let { area ->
                    val newX = event.rawX - dX
                    val newY = event.rawY - dY

                    val minX = area.x
                    val maxX =
                        maxOf(area.x, area.x + area.width - width) // Ensure maxX is at least minX

                    val minY = area.y
                    val maxY =
                        maxOf(area.y, area.y + area.height - height) // Ensure maxY is at least minY

                    x = newX.coerceIn(minX, maxX)
                    y = newY.coerceIn(minY, maxY)
                }
            }

            MotionEvent.ACTION_UP -> {
                snapToSide { handleClickEvent(event) }
                parent?.requestDisallowInterceptTouchEvent(false)
            }
        }
        return true
    }

    private fun handleClickEvent(event: MotionEvent) {
        if (!isDrag(event) && isClickEnable()) {
            performClick() // Trigger click if not a drag
        }
    }

    private fun isDrag(event: MotionEvent): Boolean {
        val distanceX = event.rawX - startX
        val distanceY = event.rawY - startY

        // Calculate true movement distance
        val totalDistance = sqrt(distanceX * distanceX + distanceY * distanceY)

        return totalDistance > dragThreshold
    }

    private fun isClickEnable(): Boolean {
        draggableArea?.let { area ->
            val areaLeft = area.x
            val areaRight = area.x + area.width
            val areaLeftClickableArea = areaLeft + dragThreshold
            val areaRightClickableArea = areaRight - dragThreshold

            return x <= areaLeftClickableArea || x + width >= areaRightClickableArea
        }
        return false // Avoid excessive logging
    }

    fun setDragEnable(enable: Boolean) {
        isDragEnable = enable
    }

    fun setSnapToSideEnable(enable: Boolean) {
        isSnapToSideEnable = enable
    }

    fun snapToSide(finishCallback: DefaultCallback? = null) {
        if (!isSnapToSideEnable) {
            finishCallback?.invoke()
            return
        }
        draggableArea?.let { area ->
            val areaLeft = area.x
            val areaRight = area.x + area.width
            val areaTop = area.y
            val areaBottom = area.y + area.height

            val leftDistance = x - areaLeft
            val rightDistance = areaRight - (x + width)
            val snapX = if (leftDistance < rightDistance) areaLeft else (areaRight - width)

            val topDistance = areaTop
            val bottomDistance = areaBottom - height
            if (bottomDistance < topDistance) {
                logWarn(
                    TAG,
                    "Invalid range: topDistance = $topDistance > bottomDistance = $bottomDistance"
                )
                return
            }
            val snapY = y.coerceIn(topDistance, bottomDistance)

            animate()
                .x(snapX)
                .y(snapY)
                .setDuration(150)
                .withEndAction(finishCallback)
                .start()
        } ?: logError(TAG, "draggableArea is null")
    }
}

