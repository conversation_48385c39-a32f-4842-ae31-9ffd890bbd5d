package com.interfun.buz.voicecall.groupcall.view.itemdelegate

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.drakeet.multitype.ItemViewBinder
import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.base.ktx.asColor
import com.interfun.buz.common.ktx.getDisplayNameWithLocal
import com.interfun.buz.common.ktx.getHighLightWord
import com.interfun.buz.voicecall.R
import com.interfun.buz.voicecall.databinding.GroupcallItemSelectMemberBinding
import com.interfun.buz.voicecall.groupcall.constants.CheckBoxUIStatus
import com.interfun.buz.voicecall.groupcall.viewmodel.SelectMemberViewModel
import com.yibasan.lizhifm.sdk.platformtools.ResUtil

/**
 * <AUTHOR>
 * @date 2024/2/6
 * @desc Adapter for group call members select page
 */
class SelectMemberItemView(var callBackListener : OnCallBackListener?): ItemViewBinder<SelectMemberViewModel.GroupMemberWithCheckboxStatus, SelectMemberItemView.MyViewHolder>() {

    override fun onCreateViewHolder(inflater: LayoutInflater, parent: ViewGroup): MyViewHolder {
        return MyViewHolder(inflater.inflate(R.layout.groupcall_item_select_member, parent, false))
    }

    override fun onBindViewHolder(holder: MyViewHolder, item: SelectMemberViewModel.GroupMemberWithCheckboxStatus) {
        holder.setData(item)
    }

    inner class MyViewHolder(holderItemView: View): RecyclerView.ViewHolder(holderItemView){

        var binding: GroupcallItemSelectMemberBinding = GroupcallItemSelectMemberBinding.bind(holderItemView)

        @SuppressLint("ClickableViewAccessibility")
        fun setData(user: SelectMemberViewModel.GroupMemberWithCheckboxStatus) {
            // Change user profile image (portrait)
            if (binding.ivPortrait.tag != user.member.userInfo.portrait || user.member.userInfo.portrait.isNullOrEmpty()) {
                binding.ivPortrait.setPortrait(user.member.userInfo.portrait)
                binding.ivPortrait.tag = user.member.userInfo.portrait
            }

            // Change adapter background when touch down
            binding.clRoot.setOnTouchListener { _, event ->
                binding.clRoot.setBackgroundColor((
                        when (event.action) {
                            MotionEvent.ACTION_DOWN, MotionEvent.ACTION_MOVE -> R.color.overlay_white_6
                            else -> R.color.transparent
                        }).asColor(appContext)
                )

                if (event.action == MotionEvent.ACTION_UP) {
                    callBackListener?.onMemberClick(user)
                }

                true
            }

            // Set adapter disable if user status is selected disable
            binding.clRoot.isEnabled = (user.status == CheckBoxUIStatus.SELECTED_DISABLE).not()

            // Set member name
            val name = user.member.userInfo.getDisplayNameWithLocal()
            val searchText = callBackListener?.getSearchText()
            binding.tvUserName.text = if (searchText.isNullOrEmpty()) name else getHighLightWord(
                name,
                searchText,
                R.color.basic_primary,
                ignoreCase = true
            )

            // Set member name color
            binding.tvUserName.setTextColor(when (user.status) {
                CheckBoxUIStatus.SELECTED -> ResUtil.getColor(R.color.basic_primary)
                CheckBoxUIStatus.UNSELECTED_DISABLE -> ResUtil.getColor(R.color.text_white_disable)
                else -> ResUtil.getColor(R.color.text_white_main)
            })

            // Set checkbox status
            if (user.status == CheckBoxUIStatus.NONE) {
                binding.iftCheck.visibility = View.GONE
            } else {
                binding.iftCheck.visibility = View.VISIBLE

                binding.iftCheck.text = when (user.status) {
                    CheckBoxUIStatus.UNSELECTED -> ResUtil.getString(R.string.ic_correct_empty)
                    CheckBoxUIStatus.SELECTED -> ResUtil.getString(R.string.ic_correct_solid)
                    CheckBoxUIStatus.SELECTED_DISABLE -> ResUtil.getString(R.string.ic_correct_solid)
                    CheckBoxUIStatus.UNSELECTED_DISABLE -> ResUtil.getString(R.string.ic_correct_empty)
                    else -> ""
                }

                binding.iftCheck.setTextColor(when (user.status) {
                    CheckBoxUIStatus.UNSELECTED -> ResUtil.getColor(R.color.text_white_main)
                    CheckBoxUIStatus.SELECTED -> ResUtil.getColor(R.color.basic_primary)
                    CheckBoxUIStatus.SELECTED_DISABLE -> ResUtil.getColor(R.color.text_white_disable)
                    CheckBoxUIStatus.UNSELECTED_DISABLE -> ResUtil.getColor(R.color.text_white_disable)
                    else -> 0
                })
            }
        }
    }

    interface OnCallBackListener {
        fun onMemberClick(user: SelectMemberViewModel.GroupMemberWithCheckboxStatus)
        fun getSearchText(): String?
    }
}