package com.interfun.buz.voicecall.common.block

import androidx.fragment.app.Fragment
import com.interfun.buz.base.ktx.*
import com.interfun.buz.base.utils.VibratorUtil
import com.interfun.buz.common.base.BaseVoiceCallBindingBlock
import com.interfun.buz.common.bean.chat.OnlineChatJumpInfo
import com.interfun.buz.common.bean.chat.OnlineChatJumpType
import com.interfun.buz.common.bean.voicecall.*
import com.interfun.buz.common.ktx.getContactFirstName
import com.interfun.buz.common.ktx.isMe
import com.interfun.buz.common.manager.cache.group.GroupInfoCacheManager
import com.interfun.buz.common.manager.cache.user.UserRelationCacheManager
import com.interfun.buz.common.manager.voicecall.RoomLifecycle
import com.interfun.buz.common.manager.voicecall.VoiceCallPortal
import com.interfun.buz.common.utils.OnCallActionStatus
import com.interfun.buz.common.utils.OnCallActionType
import com.interfun.buz.common.utils.VoiceCallNotificationTracker
import com.interfun.buz.common.utils.combineView
import com.interfun.buz.voicecall.R
import com.interfun.buz.voicecall.common.viewmodel.CameraSwitchType
import com.interfun.buz.voicecall.common.viewmodel.PipModeViewModel
import com.interfun.buz.voicecall.databinding.VoicecallTopTitleBarBinding
import com.interfun.buz.voicecall.privatecall.bean.RealTimeCallPendState
import com.interfun.buz.voicecall.privatecall.view.fragment.InviteGroupMemberDialogFragment
import com.interfun.buz.voicecall.privatecall.viewmodel.VoiceCallingPendViewModel
import com.interfun.buz.voicecall.util.VoiceCallTracker
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch

class TopTitleBarBlock(
    private val fragment: Fragment,
    binding: VoicecallTopTitleBarBinding,
    private val onlineChatJumpInfo: OnlineChatJumpInfo,
    private val realTimeCallPendState: RealTimeCallPendState?,
    ) : BaseVoiceCallBindingBlock<VoicecallTopTitleBarBinding>(binding) {

    companion object{
        private const val TAG = "TopTitleBarBlock"
    }

    private val channelType by lazy { onlineChatJumpInfo.channelType }
    private val callType by lazy { onlineChatJumpInfo.callType }
    private val isVideoChannel get() = CallType.isVideoCall(callType)
    private val isPrivateChannel get() = ChannelType.isPrivateChannel(channelType)
    private val voiceCallingPendViewModel by fragment.activityViewModels<VoiceCallingPendViewModel>()
    private val pipModeViewModel by fragment.activityViewModels<PipModeViewModel>()
    private var job: Job? = null
    private var callingMembersSize = 0
    private var dotCount = 0


    override fun initView() {
        super.initView()
        binding.root.layoutMarginTop(fragment.requireContext.statusBarHeight)
        when (onlineChatJumpInfo.jumpType) {
            OnlineChatJumpType.callInvitation,
            OnlineChatJumpType.joinChannel,
            OnlineChatJumpType.reentryFromMinimize -> {
                binding.iftvInvite.visibleIf(isPrivateChannel.not())
                binding.tvStatusTitle.inVisibleIf(onlineChatJumpInfo.jumpType != OnlineChatJumpType.reentryFromMinimize)
                binding.iftvSwitchCamera.visibleIf(shouldShowCameraSwitch())
            }
            OnlineChatJumpType.pendAnswer,
            OnlineChatJumpType.pendAnswerByHome -> {
                binding.iftvClose.visible()
                binding.iftvSwitchCamera.visibleIf(isVideoChannel)
                binding.tvUserNameTitle.gone()
                binding.tvStatusTitle.gone()
                binding.iftvInvite.gone()
                binding.iftvCollapse.gone()
            }
        }
        setUserInfo(onlineChatJumpInfo.targetId)
        setupClickListeners()
    }

    private fun shouldShowCameraSwitch(): Boolean {
        return when (onlineChatJumpInfo.jumpType) {
            OnlineChatJumpType.reentryFromMinimize ->
                CameraStatus.isOpen(
                    VoiceCallPortal.currentRoomValue?.myCamStatus?.value
                )
            OnlineChatJumpType.joinChannel ->
                realTimeCallPendState?.cameraStatus?.let {
                    CameraStatus.isOpen(it)
                } ?: isVideoChannel
            else -> isVideoChannel
        }
    }

    override fun doOnRoomExist(room: VoiceCallRoom) {
        super.doOnRoomExist(room)
        room.apply {
            when (room.currentLifecycle) {
                RoomLifecycle.INIT,
                RoomLifecycle.START,
                RoomLifecycle.WAITING -> showWaiting(room)
                RoomLifecycle.CONNECTING -> showConnecting()
                RoomLifecycle.CONNECTED -> showConnected(room)
                RoomLifecycle.DESTROY -> {}
            }

            members.collectLatestIn(fragment.viewLifecycleOwner) {
                val membersList = room.members.value
                val beCalledMembers = membersList.filter { it.callStatus == CallStatus.BE_CALLED }
                val onCallMembers = membersList.filter { it.callStatus == CallStatus.ON_CALL && !it.userId.isMe() }

                logInfo(
                    TAG,
                    "callingMembersSize: $callingMembersSize,LifeCycle:${getLifeCycle().state};" +
                            "membersList(${membersList.size}): ${membersList.map { it.userId }};" +
                            "beCalledMembers(${beCalledMembers.size}): ${beCalledMembers.map { it.userId }};" +
                            "onCallMembers(${onCallMembers.size}): ${onCallMembers.map { it.userId }}"
                )

                if (onCallMembers.isNotEmpty()) return@collectLatestIn
                if (beCalledMembers.isEmpty()) return@collectLatestIn
                if (beCalledMembers.size == callingMembersSize) return@collectLatestIn

                callingMembersSize = beCalledMembers.size
                showWaiting(room)
            }

            callDuration.collectLatestIn(fragment.viewLifecycleOwner) { callDuration ->
                updateCallDuration(room, callDuration)
            }
            myCamStatus.collectLatestIn(fragment.viewLifecycleOwner) { cameraStatus ->
                binding.iftvSwitchCamera.visibleIf(CameraStatus.isOpen(cameraStatus))
            }
        }
    }

    override fun onRoomConnecting(room: VoiceCallRoom) {
        super.onRoomConnecting(room)
        showConnecting()
    }

    override fun onRoomConnected(room: VoiceCallRoom) {
        super.onRoomConnected(room)
        showConnected(room)
    }

    override fun onRoomDestroy(room: VoiceCallRoom, reason: @CallEndType Int) {
        super.onRoomDestroy(room, reason)
        if (reason != CallEndType.HANG_UP_BY_ME && reason != CallEndType.HANG_UP) {
            combineView(
                binding.iftvClose,
                binding.iftvInvite,
                binding.iftvSwitchCamera,
                binding.iftvCollapse,
                binding.tvUserNameTitle,
                binding.tvStatusTitle
            ).gone()
        }
        when(reason){
            CallEndType.EXC_BUSY,
            CallEndType.REJECT -> {
                showBusy()
            }
            CallEndType.UNKNOWN ->{
                showNetWorkError()
            }
        }
    }

    private fun showWaiting(room: VoiceCallRoom) {
        val baseText = if (isPrivateChannel) R.string.rtc_calling.asString() else {
            val callingMembers = room.members.value.filter { it.callStatus == CallStatus.BE_CALLED }
            if (callingMembers.isEmpty()) {
                R.string.rtc_calling.asString()
            } else if (callingMembers.size == 1) {
                val userName = callingMembers.first { it.userId.isMe().not() }.userName
                context.getString(R.string.rtc_calling_x, userName)
            } else {
                context.getString(R.string.rtc_calling_people, callingMembers.size.toString())
            }
        }

        binding.tvStatusTitle.setTextColor(R.color.color_background_highlight_1_default.asColor())
        binding.tvStatusTitle.visible()

        // Cancel any previous animation job
        job?.cancel()

        // Start coroutine for animating dots
        animatedRoomStatusText(baseText)
    }

    private fun showBusy(){
        binding.iftvClose.visible()
        binding.tvStatusTitle.visible()
        binding.tvStatusTitle.text = R.string.call_busy.asString()
        binding.tvStatusTitle.setTextColor(R.color.color_background_highlight_1_default.asColor())
    }

    private fun showNetWorkError(){
        binding.iftvClose.visible()
        binding.tvStatusTitle.visible()
        binding.tvStatusTitle.text = R.string.voice_call_network_error.asString()
        binding.tvStatusTitle.setTextColor(R.color.color_background_highlight_1_default.asColor())
    }

    private fun showConnecting(){
        val baseText = R.string.rtc_connecting.asString()

        // Cancel any previous animation job
        job?.cancel()

        // Start coroutine for animating dots
        animatedRoomStatusText(baseText)
        binding.tvStatusTitle.visible()
        binding.tvStatusTitle.setTextColor(R.color.color_background_highlight_1_default.asColor())
    }

    private fun showConnected(room: VoiceCallRoom) {
        binding.tvStatusTitle.visible()
        val callLatestDuration = room.callDuration.value
        updateCallDuration(room, callLatestDuration)
    }

    private fun updateCallDuration(room: VoiceCallRoom, duration: String){
        if (room.members.value.count { it.isOnCall() } == 1) {
            showWaiting(room)
            return
        }
        if (duration.isNotEmpty()){
            job?.cancel()
            binding.tvStatusTitle.text = duration
            binding.tvStatusTitle.setTextColor(R.color.alpha_white_50.asColor())
        }
    }

    private fun animatedRoomStatusText(baseText: String) {
        job = fragment.viewLifecycleScope.launch {
            while (isActive) {
                dotCount = (dotCount + 1) % 4
                binding.tvStatusTitle.text = buildString {
                    append(baseText)
                    append(".".repeat(dotCount))
                }
                delay(300)  // Update every 300ms
            }
        }
    }

    private fun setUserInfo(userId: Long) {
        fragment.viewLifecycleScope.launch {
            if (isPrivateChannel) {
                UserRelationCacheManager.getUserRelationInfoByUidSync(userId)?.let { userInfo ->
                    binding.tvUserNameTitle.text = userInfo.getContactFirstName()
                }
            } else {
                GroupInfoCacheManager.getGroupInfoBeanByIdSync(userId)?.let { groupInfo ->
                    binding.tvUserNameTitle.text = groupInfo.groupName
                }
            }
        }
    }

    private fun setupClickListeners() {
        with(binding) {
            iftvClose.click { onCloseClicked() }
            iftvInvite.click { onInviteClicked() }
            iftvSwitchCamera.click(clickIntervals = 0) { onSwitchCameraClicked() }
            iftvCollapse.click { onCollapseClicked() }
        }
    }

    private fun onCloseClicked() {
        VibratorUtil.vibrator(TAG)
        finishActivity()
    }

    private fun onInviteClicked() {
        VibratorUtil.vibrator(TAG)
        currentRoom?.let { room ->
            fragment.childFragmentManager.beginTransaction().apply {
                add(InviteGroupMemberDialogFragment.newInstance(onlineChatJumpInfo.targetId),InviteGroupMemberDialogFragment::class.java.simpleName)
                commitNow()
            }

            val isConnected = room.getLifeCycle().state.isAtLeast(RoomLifecycle.CONNECTED)
            if (isConnected) {
                VoiceCallNotificationTracker.onConnectedUIOperationEvent(
                    action = OnCallActionType.InviteGroupMember,
                    channelType = onlineChatJumpInfo.channelType,
                    channelId = onlineChatJumpInfo.channelId ?: 0,
                    callType = onlineChatJumpInfo.callType,
                )
            }
        }
    }

    private fun onSwitchCameraClicked() {
        VibratorUtil.vibrator(TAG)
        if (onlineChatJumpInfo.jumpType == OnlineChatJumpType.pendAnswer||onlineChatJumpInfo.jumpType == OnlineChatJumpType.pendAnswerByHome) {
            val cameraState = CameraState.toggle(voiceCallingPendViewModel.cameraState.value)
            voiceCallingPendViewModel.updateCameraState(cameraState)
            return
        }

        currentRoom?.let {
            val cameraState = CameraState.toggle(it.myCamState.value)
            val isConnected = it.getLifeCycle().state.isAtLeast(RoomLifecycle.CONNECTED)
            if (CameraStatus.isOpen(it.myCamStatus.value)) {
                it.changeCamState(cameraState)
                if (isConnected) {
                    VoiceCallNotificationTracker.onConnectedUIOperationEvent(
                        action = OnCallActionType.SwitchCamera,
                        pageStatus = if (CameraState.isFront(cameraState)) OnCallActionStatus.Front else OnCallActionStatus.Back,
                        channelType = onlineChatJumpInfo.channelType,
                        channelId = onlineChatJumpInfo.channelId ?: 0,
                        callType = onlineChatJumpInfo.callType,
                    )
                }
            }
        }
    }

    private fun onCollapseClicked() {
        VibratorUtil.vibrator(TAG)
        VoiceCallTracker.onClickVoiceCallMinimize(
            isGroup = ChannelType.isPrivateChannel(onlineChatJumpInfo.channelType).not(),
            isSpeaking = VoiceCallPortal.roomLifecycle.value.first.isAtLeast(RoomLifecycle.CONNECTING)
        )

        val isConnected = currentRoom?.getLifeCycle()?.state?.isAtLeast(RoomLifecycle.CONNECTED)
        if (isConnected == true) {
            VoiceCallNotificationTracker.onConnectedUIOperationEvent(
                action = OnCallActionType.Minimize,
                pageStatus = OnCallActionStatus.Minimize,
                channelType = onlineChatJumpInfo.channelType,
                channelId = onlineChatJumpInfo.channelId ?: 0,
                callType = onlineChatJumpInfo.callType,
            )
        }

        val success = pipModeViewModel.setPipModeEnabled(enabled = true, channelType = channelType)
        logInfo(TAG,"onCollapseClicked setPipModeEnabled success=$success", logLine = LogLine.RTC_CALL)
        if (!success) {
            if (!isPrivateChannel) {
                // 群聊退出页面，关闭摄像头
                pipModeViewModel.notifyCameraCaptureState(
                    isOpen = false,
                    cameraSwitchType = CameraSwitchType.CollapseClicked
                )
            }
            finishActivity()
        }
    }

    private fun finishActivity(){
        fragment.activity?.finish()
    }

}