package com.interfun.buz.voicecall.privatecall.bean

import android.os.Parcelable
import android.view.TextureView
import com.interfun.buz.common.bean.voicecall.*
import kotlinx.parcelize.Parcelize


data class OnlineCallDetail(
    val channelUserId: Long? = null,
    val userId: Long? = null,
    val portrait: String? = "",
    val userName: String? = "",
    val micStatus: @MicStatus Int = MicStatus.OPEN,
    var voiceStatus: @CallStatus Int = CallStatus.CALLING,
    val isSpeaking: Boolean = false
)


data class CallUserInfo(
    val userId: Long,
    val portrait: String,
    val firstName: String? = "",
    val lastName: String? = "",
    val joinedTime: Long,
    var callStatus: @CallStatus Int = CallStatus.CALLING,
    val micStatus: Int? = MicStatus.OPEN,
    val updateTime: Long? = null,
    val callType: Int? = null
)

@Parcelize
data class RealTimeCallPendState(
    var cameraStatus: @CameraStatus Int?,
    var cameraState: @CameraState Int?,
    var micStatus: @MicStatus Int?
) : Parcelable


