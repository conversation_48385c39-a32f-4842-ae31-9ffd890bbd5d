package com.interfun.buz.voicecall.privatecall.view.activity


import android.Manifest
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.view.WindowManager
import com.alibaba.android.arouter.facade.annotation.Route
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.bean.chat.JumpVoiceCallPageFrom
import com.interfun.buz.common.bean.chat.OnlineChatJumpType
import com.interfun.buz.common.bean.voicecall.CallType
import com.interfun.buz.common.bean.voicecall.ChannelType
import com.interfun.buz.common.constants.PATH_ACTIVITY_REAL_TIME_CALL
import com.interfun.buz.common.constants.RouterParamKey
import com.interfun.buz.common.manager.CallNotificationCache
import com.interfun.buz.common.manager.chat.CallPendStatus
import com.interfun.buz.common.manager.chat.ChannelInviteManager
import com.interfun.buz.common.manager.chat.ChannelPendStatusManager
import com.interfun.buz.common.manager.voicecall.DoreRTCEnginManager
import com.interfun.buz.common.manager.voicecall.VoiceCallPortal
import com.interfun.buz.common.utils.*
import com.interfun.buz.onair.standard.*
import com.interfun.buz.voicecall.R
import com.interfun.buz.voicecall.common.fragment.VoiceCallingPendFragment
import com.interfun.buz.voicecall.common.view.activity.BaseRealTimeCallActivity
import com.interfun.buz.voicecall.privatecall.view.fragment.RealTimeCallFragment
import com.interfun.buz.voicecall.privatecall.viewmodel.VoiceCallingPendViewModel
import dagger.hilt.android.AndroidEntryPoint
@Route(path = PATH_ACTIVITY_REAL_TIME_CALL)
@AndroidEntryPoint
class RealTimeCallActivity : BaseRealTimeCallActivity() {
    private val TAG = "RealTimeCallActivity"

    private val permissionHelper: PermissionHelper = PermissionHelper(this)
    private val callingPendViewModel by activityViewModels<VoiceCallingPendViewModel>()

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        logInfo(TAG,"onNewIntent: ",logLine = LogLine.RTC_CALL)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        logInfo(TAG,"onCreate: ",logLine = LogLine.RTC_CALL)
        super.onCreate(savedInstanceState)
        keepScreenOn()
        overridePendingTransition(
            R.anim.design_bottom_sheet_slide_in,
            R.anim.design_bottom_sheet_slide_out
        )

        val onlineChatJumpInfo = super.onlineChatJumpInfo
        logInfo(TAG,"onCreate:jumpInfo = $onlineChatJumpInfo ")

        if (onlineChatJumpInfo?.jumpType == OnlineChatJumpType.joinChannel
            && ChannelType.isLivePlaceType(onlineChatJumpInfo.channelType)
        ) {
            if (!com.interfun.buz.base.ktx.isPermissionGranted(Manifest.permission.RECORD_AUDIO)){
                onlineChatJumpInfo.jumpType = OnlineChatJumpType.pendAnswer
            }else{
                finish()
                val channelId = onlineChatJumpInfo.channelId?:0
                ChannelPendStatusManager.changeStatus(
                    CallPendStatus.IDLE,
                    null
                )
                ChannelInviteManager.dismiss(channelId.toString(), emitCancel = true)
                val notifyId = CallNotificationCache.getVoiceCallNotifiIdByChannelId(channelId)
                if (notifyId != null) {
                    NotificationUtil.cancelVoiceCallNotificationAndUpdatePendStatus(notifyId,onlineChatJumpInfo.channelType)
                }

                val airType = ChannelType.getLivePlaceType(onlineChatJumpInfo.channelType)!!
                val param = RoomParam(
                    airType,
                    ActivityReason.JOIN_ROOM,
                    onlineChatJumpInfo.targetId,
                    true,
                    source = LivePlaceSource.SOURCE_INVITE_NOTIFY,
                    joinROOMParam = JoinRoomParam(channelId,  1, 1)
                )
                routerServices<IGlobalOnAirController>().value?.enterOnAir(param,this)
                CommonTracker.onClickAnswerOnAirInvite(true,channelId.toString(),true)
                return
            }
        }

        if (onlineChatJumpInfo != null) {
            val enterCall:(DefaultCallback)->Unit = {defaultCallback->
                if (!isPermissionGranted(Manifest.permission.RECORD_AUDIO)) {
                    logWarn(TAG, "${onlineChatJumpInfo.channelType} END ==> jumpType: ${onlineChatJumpInfo.jumpType}, reason: ${Manifest.permission.RECORD_AUDIO} permission is not granted.")
                    checkPermissionDialog(
                        requestPermission = Manifest.permission.RECORD_AUDIO,
                        onPermissionDenyListener = {
                            showAskRecordPermission(context)
                            defaultCallback.invoke()
                        }
                    )
                    onlineChatJumpInfo.jumpType = OnlineChatJumpType.pendAnswer
                    addPaddingFragment()
                } else if (CallType.isVideoCall(onlineChatJumpInfo.callType) && !isPermissionGranted(Manifest.permission.CAMERA)) {
                    logWarn(TAG, "${onlineChatJumpInfo.channelType} END ==> jumpType: ${onlineChatJumpInfo.jumpType}, reason: ${Manifest.permission.CAMERA} permission is not granted.")
                    checkPermissionDialog(
                        requestPermission = Manifest.permission.CAMERA,
                        onPermissionDenyListener = {
                            showAskCameraPermissionDialog(context)
                            defaultCallback.invoke()
                        }
                    )
                    onlineChatJumpInfo.jumpType = OnlineChatJumpType.pendAnswer
                    addPaddingFragment()
                } else {
                    supportFragmentManager.beginTransaction().replace(
                        containerId,
                        RealTimeCallFragment.newInstance(
                            onlineChatJumpInfo = onlineChatJumpInfo,
                            jumpFrom = intent.getIntExtra(
                                RouterParamKey.ChannelInvite.KEY_JUMP_INVITE_PAGE_FROM,
                                JumpVoiceCallPageFrom.CALL_STYLE
                            ),
                        )
                    ).commitNow()
                }
            }
            when (onlineChatJumpInfo.jumpType) {
                OnlineChatJumpType.pendAnswer,
                OnlineChatJumpType.pendAnswerByHome -> {
                    addPaddingFragment()
                }

                OnlineChatJumpType.callInvitation -> {
                    enterCall.invoke {}
                }

                OnlineChatJumpType.reentryFromMinimize -> {
                    enterCall.invoke {
                        VoiceCallPortal.hangUp()
                    }
                }

                OnlineChatJumpType.joinChannel ->{
                    enterCall.invoke {}
                }
            }
        }
    }

    private fun addPaddingFragment() {
        supportFragmentManager.beginTransaction().replace(
            containerId,
            VoiceCallingPendFragment.newInstance(intent.extras)
        ).commitNow()
    }

    override fun initData() {
        super.initData()
        callingPendViewModel.wtAnswerStateFlow.collectIn(this) { state ->
            onlineChatJumpInfo?.let { onlineChatJumpInfo ->
                if ( ChannelType.isLivePlaceType(onlineChatJumpInfo.channelType) ) return@collectIn
                supportFragmentManager.beginTransaction().replace(
                    containerId,
                    RealTimeCallFragment.newInstance(
                        onlineChatJumpInfo = onlineChatJumpInfo.apply {
                            jumpType = OnlineChatJumpType.joinChannel
                        },
                        jumpFrom = JumpVoiceCallPageFrom.PENDING_PAGE,
                        realTimeCallPendState = state
                    )
                ).commitNow()
            }
        }
    }

    private fun checkPermissionDialog(
        requestPermission: String,
        onPermissionGrantedListener: DefaultCallback? = null,
        onPermissionDenyListener: DefaultCallback? = null
    ) {
        permissionHelper.request(
            this,
            false,
            requestPermission
        ) { result ->
            val item = result.resultMap[requestPermission]
            if (item?.isGranted == true) {
                logInfo(TAG, "Permission Granted")
                onPermissionGrantedListener?.invoke()
            } else if (item?.hasSysDialogShown == false) {
                logInfo(TAG, "Permission Deny，hasSysDialogShown: false")
                onPermissionDenyListener?.invoke()
            } else {
                logInfo(TAG, "Permission Granted，hasSysDialogShown:${item?.hasSysDialogShown}")
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        logInfo(TAG,"onDestroy: ")
        allowScreenOff()
        DoreRTCEnginManager.stopCameraCapture(from = "RealTimeCallActivity onDestroy")
    }

    override fun finish() {
        // 清除最近任务使用栈
        finishAndRemoveTask()
        overridePendingTransition(0, R.anim.design_bottom_sheet_slide_out)
    }

    // 保持屏幕常亮
    private fun keepScreenOn() {
        activity.window?.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O_MR1) {
            setTurnScreenOn(true)
        }
    }

    // 允许屏幕变暗和锁定
    private fun allowScreenOff() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O_MR1) {
            setTurnScreenOn(false)
        }
      activity.window?.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)

    }

    // 允许屏幕在锁屏时显示
    private fun displayInLockScreen() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O_MR1) {
            setShowWhenLocked(true)
        } else {
            activity.window?.addFlags(
                WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED
                        or WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON
            )
        }
    }

    // 不允许屏幕在锁屏时显示
    private fun removeDisplayInLockScreen() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O_MR1) {
            setShowWhenLocked(false)
        } else {
            activity.window?.clearFlags(
                WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED
                        or WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON
            )
        }
    }
}