package com.interfun.buz.voicecall.common.utils

import com.interfun.buz.common.constants.TrackConstant
import com.interfun.buz.common.utils.BuzTracker
import com.yibasan.lizhifm.liveinteractive.CameraStopReason

object VCTracker {
    fun cameraStopInVc(reason: CameraStopReason?){
        BuzTracker.onTechTrack {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "TT2025033101")
            put(TrackConstant.KEY_EVENT_NAME, "CameraCaptureStopped")
            put(
                TrackConstant.KEY_CONTENT_1, "${reason?.ordinal?:0}"
            )
        }
    }
}