package com.interfun.buz.voicecall.common.block

import android.Manifest
import android.os.Build
import android.os.Build.VERSION_CODES
import androidx.core.app.ActivityCompat
import com.interfun.buz.base.ktx.*
import com.interfun.buz.base.utils.VibratorUtil
import com.interfun.buz.common.audio.AudioManagerHelper
import com.interfun.buz.common.base.BaseVoiceCallBindingBlock
import com.interfun.buz.common.bean.chat.JumpStartRealTimeCallEntrance
import com.interfun.buz.common.bean.chat.OnlineChatJumpInfo
import com.interfun.buz.common.bean.chat.OnlineChatJumpType
import com.interfun.buz.common.bean.voicecall.*
import com.interfun.buz.common.helper.CameraPermissionHelper
import com.interfun.buz.common.helper.RecordVoicePermissionHelper
import com.interfun.buz.common.manager.AppConfigRequestManager.enableVideoCall
import com.interfun.buz.common.manager.OnlineChatRingtoneManager
import com.interfun.buz.common.manager.voicecall.RoomLifecycle
import com.interfun.buz.common.manager.voicecall.VoiceCallPortal
import com.interfun.buz.common.manager.voicecall.VoiceCallViewModel
import com.interfun.buz.common.utils.*
import com.interfun.buz.common.voicecall.ActionType
import com.interfun.buz.voicecall.R
import com.interfun.buz.voicecall.common.dialog.SpeakerSelectorDialog
import com.interfun.buz.voicecall.common.interfaces.VoiceCallUI
import com.interfun.buz.voicecall.common.viewmodel.CameraSwitchType
import com.interfun.buz.voicecall.common.viewmodel.PipModeViewModel
import com.interfun.buz.voicecall.common.viewmodel.UserLeaveHintSource
import com.interfun.buz.voicecall.databinding.VoicecallBottomControllerPanelBinding
import com.interfun.buz.voicecall.util.VoiceCallTracker
import com.yibasan.lizhifm.audio.BaseAudioRouterType
import com.yibasan.lizhifm.liveinteractive.enity.DeviceRouteInfo
import com.yibasan.lizhifm.utilities.audiomanager.AudioManagerImpl
import kotlinx.coroutines.launch

class BottomControllerPanelBlock(
    private val voiceCallUI: VoiceCallUI,
    binding: VoicecallBottomControllerPanelBinding,
    private var onlineChatJumpInfo: OnlineChatJumpInfo,
) : BaseVoiceCallBindingBlock<VoicecallBottomControllerPanelBinding>(binding) {

    companion object {
        private const val TAG = "BottomControllerPanelBlock"
    }

    private val fragment = voiceCallUI.fragment

    private val recordPermissionHelper = RecordVoicePermissionHelper(fragment)
    private val cameraPermissionHelper = CameraPermissionHelper(fragment)
    private val pipModeViewModel by fragment.activityViewModels<PipModeViewModel>()

    private val callType by lazy { onlineChatJumpInfo.callType }
    private val isVideoCall by lazy { CallType.isVideoCall(callType) }
    private val isPrivateChannel by lazy { ChannelType.isPrivateChannel(onlineChatJumpInfo.channelType) }

    private var deviceSelectorDialog: SpeakerSelectorDialog? = null
    private val roomViewModel get() = currentRoom?.viewModel
    private val bluetoothPermissionInterceptor get() = voiceCallUI.getBluetoothPermissionInterceptor()
    private val checkCameraPermission get() = isPermissionGranted(Manifest.permission.CAMERA)
    private val checkRecordPermission get() = isPermissionGranted(Manifest.permission.RECORD_AUDIO)
    private var isSwitchBluetoothByUser = false //是否由用户主动切换蓝牙

    override fun initView() {
        super.initView()
        binding.root.layoutMarginBottom(fragment.requireContext.navigationBarHeight + 14.dp)
        binding.noAnswerGroup.gone()
        binding.busyGroup.gone()
        binding.channelJoinedGroup.gone()
    }

    override fun doOnRoomExist(room: VoiceCallRoom) {
        super.doOnRoomExist(room)
        showJoinUI()
        room.apply {
            addVoiceCallListener(this)
            setupClickListeners(this)
            updateCameraBtnState(
                isOpen = CameraStatus.isOpen(room.myCamStatus.value),
                isEnable = getLifeCycle().state.isAtLeast(RoomLifecycle.CONNECTED) && enableVideoCall
            )
            // 切换到画中画的时候，如果有蓝牙弹窗，需要关闭蓝牙弹窗
            isPipModeShowingFlow.collectIn(fragment.viewLifecycleOwner) { showing ->
                if (showing && deviceSelectorDialog?.isDialogShowing() == true) {
                    deviceSelectorDialog?.dismiss()
                }
            }
        }
    }

    override fun onRoomConnected(room: VoiceCallRoom) {
        super.onRoomConnected(room)
        if (onlineChatJumpInfo.channelId.isNull()) {
            onlineChatJumpInfo.channelId = room.roomChannelId
        }
        showJoinUI()
        updateCameraBtnState(
            isOpen = CameraStatus.isOpen(room.myCamStatus.value),
            isEnable = enableVideoCall
        )
    }

    private fun addVoiceCallListener(room: VoiceCallRoom) {
        val roomViewModel = room.viewModel
        roomViewModel.apply {
            onAudioDeviceChangeFlow.collectIn(fragment.viewLifecycleOwner) { audioDevice ->
                val deviceInfos = audioDeviceRouters.value
                val devicesInfo = deviceInfos?.firstOrNull { it.value == audioDevice.routing }
                logInfo(
                    TAG,
                    "onAudioDeviceChangeFlow currentDevice:${audioDevice},info:${devicesInfo?.getLogMessage()}"
                )
                deviceSelectorDialog?.setDeviceChanged(audioDevice.routing)
                val isConnected = room.getLifeCycle().state.isAtLeast(RoomLifecycle.CONNECTED)
                if (isConnected && isSwitchBluetoothByUser) {
                    isSwitchBluetoothByUser = false
                    VoiceCallNotificationTracker.onConnectedUIOperationEvent(
                        action = OnCallActionType.SwitchOutputDevice,
                        pageStatus = OnCallActionStatus.outputDeviceType(audioDevice.routing),
                        channelType = onlineChatJumpInfo.channelType,
                        channelId = onlineChatJumpInfo.channelId ?: 0,
                        callType = onlineChatJumpInfo.callType,
                    )
                }
                updateSpeakerBtnState(audioDevice)
            }
            showVoiceDeviceSelectorFlow.collectIn(fragment.viewLifecycleOwner) {
                if (it.isNullOrEmpty()) return@collectIn
                logInfo(TAG, "showVoiceDeviceSelectorFlow devicesRouteInfo: $it",
                    logLine = LogLine.RTC_CALL)
                showAudioSelector(roomViewModel, it)
            }
        }

        room.apply {
            isPeripheralForAgora.collectIn(fragment.viewLifecycleOwner) {
                binding.iftvSpeaker.isClickable = it.not()
            }
            lieErrorCode.collectIn(fragment.viewLifecycleOwner) { err ->
                logInfo(TAG, "lieErrorCode err: $err}", logLine = LogLine.RTC_CALL)
                if (err == AudioManagerImpl.ERROR_SWITCH_ROUTE) {
                    R.string.output_device_switch_failed.toast()
                } else if (err == AudioManagerImpl.ERROR_BT_PERMISSION) {
                    if (bluetoothPermissionInterceptor.isAllPermissionGranted()) return@collectIn
                    bluetoothPermissionInterceptor.requestPermission()
                }
            }
            myCamStatus.collectLatestIn(fragment.viewLifecycleOwner) { cameraStatus ->
                updateCameraBtnState(
                    isOpen = CameraStatus.isOpen(cameraStatus),
                    isEnable = getLifeCycle().state.isAtLeast(RoomLifecycle.CONNECTED) && enableVideoCall
                )
            }
            myMicStatus.collectLatestIn(fragment.viewLifecycleOwner) { micStatus ->
                updateMuteBtnState(micStatus)
            }

            pipModeViewModel.cameraCaptureState.collect(fragment) { (isOpen, switchType) ->
                PipModeViewModel.printLog("cameraCaptureState:${isOpen},${switchType}")
                if (!isOpen && myCamStatus.value == CameraStatus.OPEN) {
                    val isMinimizeAction = switchType == CameraSwitchType.CollapseClicked
                            || switchType == CameraSwitchType.SystemRecommend
                            || switchType == CameraSwitchType.BackPressed
                    if (!isPrivateChannel && isMinimizeAction) {
                        // 群视频通话切换到最小化时，有toast
                        toastIconFontMsg(
                            message = R.string.rtc_camera_turn_off_in_group_call.asString(),
                            textColor = R.color.text_white_default.asColor(),
                            iconFont = R.string.ic_video_off.asString(),
                            iconFontColor = R.color.text_white_important.asColor(),
                            style = IconToastStyle.ICON_LEFT_TEXT_RIGHT
                        )
                    }
                    updateCameraStatus(
                        room = room,
                        cameraSwitchStatus = CameraStatus.CLOSE,
                        from = "pipModeViewModel.cameraCaptureState change by $switchType"
                    )
                }
            }
        }

        bluetoothPermissionInterceptor.apply {
            setOnPermissionGrantedListener {
                roomViewModel.switchAudioDevice(DeviceRouteInfo.TYPE_BLUETOOTH)
            }
            setOnPermissionDenyListener {
                val activity = fragment.activity ?: return@setOnPermissionDenyListener
                if (!it.containsValue(true)) {
                    voiceCallUI.showBluetoothPermissionTipsDialog(activity) {
                        voiceCallUI.getAppSettingLauncher().launch()
                    }
                }
            }
        }
    }

    override fun onRoomDestroy(room: VoiceCallRoom, reason: @CallEndType Int) {
        super.onRoomDestroy(room, reason)
        logInfo(TAG, "onRoomDestroy: channelId = ${room.roomChannelId}, reason = $reason")
        if (isPrivateChannel) {
            handlePrivateRoomDestroy(reason)
        } else {
            handleGroupRoomDestroy(reason)
        }
    }

    private fun handlePrivateRoomDestroy(reason: Int) {
        when (reason) {
            CallEndType.UNKNOWN,
            CallEndType.REJECT,
            CallEndType.EXC_BUSY,
            CallEndType.EXC_BEING_BLOCKED -> {
                //busy
                showBusyUI(reason)
            }

            CallEndType.TIME_OUT -> {
                //no answer
                showNoAnswerUI(reason)
            }
        }
    }

    private fun handleGroupRoomDestroy(reason: Int) {
        when (reason) {
            CallEndType.REJECT,
            CallEndType.EXC_BUSY,
            CallEndType.EXC_BEING_BLOCKED,
            CallEndType.UNKNOWN,
            CallEndType.TIME_OUT -> {
                showNoAnswerUI(reason)
            }
        }
    }

    /**
     * isReRequest 点的显示列表的时候，传true，发现列表为空时重新请求下，其他情况传false
     */
    private fun showAudioSelector(
        roomViewModel: VoiceCallViewModel,
        deviceInfos: Array<BaseAudioRouterType>?,
        isReRequest: Boolean = false
    ) {
        if (deviceInfos == null) {
            return
        }
        val switchable = roomViewModel.audioDeviceSwitchable.value
        if (!switchable) {
            logWarn(TAG, "showAudioSelector switchable false")
            return
        }
        val currentDevice = roomViewModel.onAudioDeviceChangeFlow.value
        val currentSelectedDeviceInfo = deviceInfos.firstOrNull {
            it.value == currentDevice.routing
        }

        val hasBTConnectPermission = if (Build.VERSION.SDK_INT >= VERSION_CODES.S) {
            isPermissionGranted(Manifest.permission.BLUETOOTH_CONNECT)
        } else {
            true
        }
        val permissionTryRequest =
            if (hasBTConnectPermission.not() && Build.VERSION.SDK_INT >= VERSION_CODES.S) {
                fragment.activity?.let {
                    ActivityCompat.shouldShowRequestPermissionRationale(
                        it,
                        Manifest.permission.BLUETOOTH_CONNECT
                    )
                } ?: true
            } else {
                true
            }
        logInfo(
            TAG,
            "hasBTConnectPermission = $hasBTConnectPermission, permissionTryRequest = $permissionTryRequest, deviceInfos.size = ${deviceInfos.size}"
        )
        if (deviceInfos.size == 2 && hasBTConnectPermission) {
            val index = deviceInfos.indexOfFirst { it.value == currentDevice.routing }
            if (index != -1) {
                val other = if (index == 0) 1 else 0
                currentRoom?.switchAudioOutputDevice(deviceInfos[other].value)
            }
            return
        }
        val deviceList = mutableListOf<DeviceInfoItemOption>()
        if (hasBTConnectPermission.not()) {
            val btRouteInfo = deviceInfos.find {
                it.value == AudioDevice.AUDIO_ROUTE_BLUETOOTH_DEVICE.routing
            }
            if (btRouteInfo == null) {
                //当没有蓝牙连接权限的时候，需要默认显示有蓝牙选项，当用户点击时再动态申请权限
                val bluetoothRoute =
                    AudioDevice.getDefaultBluetoothDeviceRouteInfo()
                deviceList.add(
                    DeviceInfoItemOption(
                        type = bluetoothRoute.value,
                        name = R.string.Bluetooth.asString(),
                        selected = false,
                        showLoading = false
                    )
                )
            }
        }
        deviceInfos.forEach { device ->
            val name = device.getName()
            if (name != null && name.isNotEmpty()) {
                val selected = currentSelectedDeviceInfo?.value == device.value
                deviceList.add(DeviceInfoItemOption(device.value, name, selected, false))
            }
        }
        if (deviceList.isEmpty() && isReRequest) {
            logInfo(TAG, "has no devices, request getDeviceRoutes")
            roomViewModel.getDeviceRoutes(true)
            return
        }

        deviceSelectorDialog =
            SpeakerSelectorDialog.create(arrayListOf<DeviceInfoItemOption>().apply {
                addAll(deviceList)
            }) {
                if (it.type == AudioDevice.AUDIO_ROUTE_BLUETOOTH_DEVICE.routing && Build.VERSION.SDK_INT >= VERSION_CODES.S) {
                    val isPermissionGranted =
                        bluetoothPermissionInterceptor.isAllPermissionGranted()
                    if (isPermissionGranted.not()) {
                        logInfo(
                            TAG,
                            "has no permission.BLUETOOTH_CONNECT, start checkBluetoothPermission",
                            logLine = LogLine.RTC_CALL
                        )
                        pipModeViewModel.addUserLeaveHintSource(UserLeaveHintSource.BluetoothPermissionApply)
                        bluetoothPermissionInterceptor.requestPermission()
                        deviceSelectorDialog?.dismiss()

                        VoiceCallNotificationTracker.onConnectedUIOperationEvent(
                            action = OnCallActionType.SwitchOutputDevice,
                            pageStatus = OnCallActionStatus.outputDeviceType(it.type),
                            channelType = onlineChatJumpInfo.channelType,
                            channelId = onlineChatJumpInfo.channelId ?: 0,
                            callType = onlineChatJumpInfo.callType,
                            success = false,
                            failReason = "NO_BLUETOOTH_CONNECT_PERMISSION"
                        )
                        return@create
                    }
                }
                currentRoom?.switchAudioOutputDevice(it.type)
            }
        deviceSelectorDialog?.show(fragment.childFragmentManager, "VoiceDeviceSelector")
    }

    private fun setupClickListeners(room: VoiceCallRoom) {
        with(binding) {
            iftvCancel.click { onCancelCallClicked() }
            iftvCallAgain.click { onCallAgainClicked() }
            iftvVideo.click { onCameraClicked(room) }
            iftvMic.click { onMicClicked(room) }
            iftvSpeaker.click { onSpeakerClicked(room) }
            iftvEnd.click { onEndClicked(room) }
        }
    }

    private fun onCancelCallClicked() {
        VibratorUtil.vibrator(TAG)
        fragment.activity?.finish()
    }

    private fun onCallAgainClicked() {
        VibratorUtil.vibrator(TAG)
        VoiceCallPortal.hangUp()
        fragment.finishActivity()
        if (isPrivateChannel) {
            voiceCallUI.getPrivateCallStart().startRealTimeCall(
                targetId = onlineChatJumpInfo.targetId,
                callType = onlineChatJumpInfo.callType,
                entrance = JumpStartRealTimeCallEntrance.RETRY
            ) {
                val callConflictState = it.startHelper.startCall()
                if (callConflictState == CallConflictState.NO_CONFLICT) {
                    it.jumpHelper.startJump(OnlineChatJumpType.callInvitation)
                }
            }
        } else {
            voiceCallUI.getGroupCallStart().showGroupCallSelectMemberDialog(
                context = context,
                groupId = onlineChatJumpInfo.targetId,
                callType = onlineChatJumpInfo.callType,
                actionType = ActionType.START,
                entrance = JumpStartRealTimeCallEntrance.RETRY
            )
        }
    }

    private fun onCameraClicked(room: VoiceCallRoom) {
        VibratorUtil.vibrator(TAG)
        val currentMyCameraStatus = room.myCamStatus.value
        val cameraSwitchStatus = CameraStatus.toggle(currentMyCameraStatus)
        if (CameraStatus.isOpen(cameraSwitchStatus)) {
            if (!checkCameraPermission) {
                cameraPermissionHelper.apply {
                    setOnPermissionGrantedListener {
                        logInfo(TAG, "onCameraClicked: Permission Granted")
                        updateCameraStatus(
                            room = room,
                            cameraSwitchStatus = cameraSwitchStatus,
                            from = "OnPermissionGranted"
                        )
                    }
                    setOnPermissionDenyListener {
                        logWarn(TAG, "onCameraClicked: Permission Denied")
                        showAskCameraPermissionDialog(context)
                    }
                    logInfo(TAG, "onCameraClicked: Requesting Permission...")
                    pipModeViewModel.addUserLeaveHintSource(UserLeaveHintSource.CameraPermissionApply)
                    requestCameraPermission(false)
                }
                val isConnected = room.getLifeCycle().state.isAtLeast(RoomLifecycle.CONNECTED)
                if (isConnected) {
                    VoiceCallNotificationTracker.onConnectedUIOperationEvent(
                        action = OnCallActionType.Camera,
                        pageStatus = OnCallActionStatus.Open,
                        channelType = onlineChatJumpInfo.channelType,
                        channelId = onlineChatJumpInfo.channelId ?: 0,
                        callType = onlineChatJumpInfo.callType,
                        success = false,
                        failReason = "no_camera_permission"
                    )
                }
                return
            }
            //开启摄像机时，外设换成扬声器
            roomViewModel?.apply {
                if (onAudioDeviceChangeFlow.value == AudioDevice.AUDIO_ROUTE_HANDSET) {
                    switchAudioDevice(AudioDevice.AUDIO_ROUTE_SPEAKER.routing)
                }
            }
        }
        val isConnected = room.getLifeCycle().state.isAtLeast(RoomLifecycle.CONNECTED)
        if (isConnected) {
            VoiceCallNotificationTracker.onConnectedUIOperationEvent(
                action = OnCallActionType.Camera,
                pageStatus = if (CameraStatus.isOpen(cameraSwitchStatus)) OnCallActionStatus.Open else OnCallActionStatus.Close,
                channelType = onlineChatJumpInfo.channelType,
                channelId = onlineChatJumpInfo.channelId ?: 0,
                callType = onlineChatJumpInfo.callType,
            )
        }
        updateCameraStatus(
            room = room,
            cameraSwitchStatus = cameraSwitchStatus,
            from = "onCameraClicked"
        )
    }

    private fun onMicClicked(room: VoiceCallRoom) {
        VibratorUtil.vibrator(TAG)
        fragment.viewLifecycleScope.launch {
            AudioManagerHelper.muteChange(false)
        }
        val currentMyMicStatus = room.myMicStatus.value
        val micSwitchStatus = MicStatus.toggle(currentMyMicStatus)
        val isConnected = room.getLifeCycle().state.isAtLeast(RoomLifecycle.CONNECTED)
        if (MicStatus.isMicOpen(micSwitchStatus) && !checkRecordPermission) {
            recordPermissionHelper.apply {
                setOnPermissionGrantedListener {
                    logInfo(TAG, "onMicClicked: Permission Granted")
                    updateMicStatus(room, micSwitchStatus)
                }
                setOnPermissionDenyListener {
                    logWarn(TAG, "onMicClicked: Permission Denied")
                    showAskRecordPermission(context)
                }
                logInfo(TAG, "onMicClicked: Requesting Permission...")
                requestRecordPermission(false)
            }
            if (isConnected) {
                VoiceCallNotificationTracker.onConnectedUIOperationEvent(
                    action = OnCallActionType.Mic,
                    pageStatus = OnCallActionStatus.Open,
                    channelType = onlineChatJumpInfo.channelType,
                    channelId = onlineChatJumpInfo.channelId ?: 0,
                    callType = onlineChatJumpInfo.callType,
                    success = false,
                    failReason = "no_mic_permission"
                )
            }
            return
        }
        if (isConnected) {
            VoiceCallNotificationTracker.onConnectedUIOperationEvent(
                action = OnCallActionType.Mic,
                pageStatus = if (MicStatus.isMicOpen(micSwitchStatus)) OnCallActionStatus.Open else OnCallActionStatus.Close,
                channelType = onlineChatJumpInfo.channelType,
                channelId = onlineChatJumpInfo.channelId ?: 0,
                callType = onlineChatJumpInfo.callType,
            )
        }
        updateMicStatus(room, micSwitchStatus)
        VoiceCallTracker.onClickMicSwitchEvent(micSwitchStatus == MicStatus.CLOSE)
    }

    private fun onSpeakerClicked(room: VoiceCallRoom) {
        VibratorUtil.vibrator(TAG)
        logInfo(TAG, "onSpeakerClicked:after connecting ")
        val roomViewModel = room.viewModel
        val audioDeviceRouters = roomViewModel.audioDeviceRouters.value
        if (roomViewModel.onAudioDeviceChangeFlow.value == AudioDevice.AUDIO_ROUTE_WIRED_EARPHONE) return
        isSwitchBluetoothByUser = true
        if (!audioDeviceRouters.isNullOrEmpty()) {
            showAudioSelector(roomViewModel, audioDeviceRouters, isReRequest = true)
        } else {
            roomViewModel.getDeviceRoutes(true)
        }
    }

    private fun onEndClicked(room: VoiceCallRoom) {
        VibratorUtil.vibrator(TAG)
        room.hangUp()
        OnlineChatRingtoneManager.playShortVibration()
        handleEndCallTracker()
    }

    private fun updateCameraStatus(
        room: VoiceCallRoom,
        cameraSwitchStatus: @CameraStatus Int,
        from: String = ""
    ) {
        logInfo(TAG, "updateCameraStatus:cameraSwitchStatus=${cameraSwitchStatus}, from=${from}",
            logLine = LogLine.MY_RTC_CAMERA_STATE)
        room.roomChannelId?.let { channelId ->
            room.changeMyCamStatus(channelId, cameraSwitchStatus, from = from)
        }
    }

    private fun updateMicStatus(room: VoiceCallRoom, micSwitchStatus: @MicStatus Int) {
        room.roomChannelId?.let { channelId ->
            room.changeMyMicStatus(channelId, micSwitchStatus)
        }
    }

    private fun handleEndCallTracker() {
        val channelId = onlineChatJumpInfo.channelId ?: currentRoom?.roomChannelId
        logInfo(TAG, "onEndClicked channelId: $channelId")
        VoiceCallTracker.onClickHangUpEvent(onlineChatJumpInfo.channelType, channelId.toString())
    }

    private fun showBusyUI(reason: Int) {
        binding.noAnswerGroup.gone()
        binding.busyGroup.visible()
        binding.channelJoinedGroup.gone()
        if (reason == CallEndType.REJECT) {
            binding.iftvCallAgain.gone()
            binding.tvCallAgainLabel.gone()
        }
    }

    private fun showNoAnswerUI(reason: Int) {
        binding.iftvCallAgain.text =
            if (isVideoCall) R.string.ic_video.asString() else R.string.ic_tel.asString()
        binding.busyGroup.gone()
        binding.noAnswerGroup.visible()
        binding.channelJoinedGroup.gone()
        if (reason == CallEndType.REJECT || reason==CallEndType.EXC_BUSY) {
            binding.iftvCallAgain.gone()
            binding.tvCallAgainLabel.gone()
        }
    }

    private fun showJoinUI() {
        binding.noAnswerGroup.gone()
        binding.channelJoinedGroup.visible()
    }

    private fun updateSpeakerBtnState(audioDevice: AudioDevice) {
        binding.iftvSpeaker.apply {
            text = if (audioDevice == AudioDevice.AUDIO_ROUTE_HANDSET) {
                R.string.ic_sound_open.asString()
            } else {
                audioDevice.getIconFontRes()
            }
            when (audioDevice) {
                AudioDevice.AUDIO_ROUTE_HANDSET,
                AudioDevice.UNKNOWN,
                AudioDevice.OTHERS -> {
                    setTextColor(R.color.color_text_white_important.asColor())
                    setBackgroundResource(R.color.alpha_white_20)
                }

                else -> {
                    setTextColor(R.color.color_text_black_primary.asColor())
                    setBackgroundResource(R.color.color_background_light_default)
                }
            }
        }
    }

    private fun updateMuteBtnState(micStatus: @MicStatus Int) {
        binding.iftvMic.apply {
            when (micStatus) {
                MicStatus.OPEN -> {
                    text = R.string.ic_mic_open.asString()
                    setTextColor(R.color.color_text_black_primary.asColor())
                    setBackgroundResource(R.color.color_background_light_default)
                }

                MicStatus.CLOSE -> {
                    text = R.string.ic_mic_close.asString()
                    setTextColor(R.color.color_text_white_important.asColor())
                    setBackgroundResource(R.color.alpha_white_20)
                }
            }
        }
    }

    private fun updateCameraBtnState(isOpen: Boolean, isEnable: Boolean) {
        binding.iftvVideo.apply {
            if (!enableVideoCall) {
                gone()
                return
            }
            var status = if (isOpen) CameraStatus.OPEN else CameraStatus.CLOSE
            status = if (isEnable.not()) CameraStatus.disable(status) else status
            when (status) {
                CameraStatus.OPEN -> {
                    enable()
                    text = R.string.ic_video.asString()
                    setTextColor(R.color.color_text_black_primary.asColor())
                    setBackgroundResource(R.color.color_background_light_default)
                }

                CameraStatus.CLOSE -> {
                    enable()
                    text = R.string.ic_video_off.asString()
                    setTextColor(R.color.color_text_white_important.asColor())
                    setBackgroundResource(R.color.alpha_white_20)
                }

                CameraStatus.DISABLE_OPEN -> {
                    disable()
                    text = R.string.ic_video.asString()
                    setTextColor(R.color.alpha_white_10.asColor())
                    setBackgroundResource(R.color.alpha_white_10)
                }

                CameraStatus.DISABLE_CLOSE -> {
                    disable()
                    text = R.string.ic_video_off.asString()
                    setTextColor(R.color.alpha_white_10.asColor())
                    setBackgroundResource(R.color.alpha_white_10)
                }
            }
        }
    }
}