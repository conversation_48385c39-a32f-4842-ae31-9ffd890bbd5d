package com.interfun.buz.voicecall.privatecall.view.fragment

import android.Manifest.permission
import android.media.AudioManager
import android.os.Build
import android.os.Build.VERSION_CODES
import android.os.Bundle
import androidx.activity.result.ActivityResultLauncher
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.audio.AudioManagerHelper
import com.interfun.buz.common.base.binding.BaseBindingFragment
import com.interfun.buz.common.bean.chat.JumpVoiceCallPageFrom
import com.interfun.buz.common.bean.chat.OnlineChatJumpInfo
import com.interfun.buz.common.bean.chat.OnlineChatJumpType
import com.interfun.buz.common.bean.voicecall.CallConflictState.NO_CONFLICT
import com.interfun.buz.common.bean.voicecall.CallEndType
import com.interfun.buz.common.bean.voicecall.CallType
import com.interfun.buz.common.bean.voicecall.CameraStatus
import com.interfun.buz.common.bean.voicecall.ChannelType
import com.interfun.buz.common.bean.voicecall.MicStatus
import com.interfun.buz.common.constants.JUMP_INFO
import com.interfun.buz.common.constants.RouterParamKey
import com.interfun.buz.common.eventbus.voicecall.VoiceCallUserInfoChangeEvent
import com.interfun.buz.common.ktx.toastCallEnded
import com.interfun.buz.common.ktx.toastNetworkErrorTips
import com.interfun.buz.common.manager.CallNotificationCache
import com.interfun.buz.common.manager.chat.CallPendInfo
import com.interfun.buz.common.manager.chat.CallPendStatus
import com.interfun.buz.common.manager.chat.ChannelInviteManager
import com.interfun.buz.common.manager.chat.ChannelPendStatusManager
import com.interfun.buz.common.manager.voicecall.RoomLifecycle.DESTROY
import com.interfun.buz.common.manager.voicecall.VoiceCallPortal
import com.interfun.buz.common.utils.AcceptVoiceCallFailReason.Companion.PERMISSION_LIMIT_OR_ON_CALL
import com.interfun.buz.common.utils.NotificationUtil
import com.interfun.buz.common.utils.PermissionInterceptor
import com.interfun.buz.common.utils.VoiceCallNotificationTracker
import com.interfun.buz.common.voicecall.GroupCallStater
import com.interfun.buz.common.voicecall.GroupCallStaterImpl
import com.interfun.buz.common.voicecall.PrivateCallStater
import com.interfun.buz.common.voicecall.PrivateCallStaterImpl
import com.interfun.buz.im.signal.HeartBeatManager
import com.interfun.buz.voicecall.common.block.BottomControllerPanelBlock
import com.interfun.buz.voicecall.common.block.TopTitleBarBlock
import com.interfun.buz.voicecall.common.interfaces.VoiceCallUI
import com.interfun.buz.voicecall.databinding.VoicecallFragmentPrivateOnlinechatBinding
import com.interfun.buz.voicecall.privatecall.bean.RealTimeCallPendState
import com.interfun.buz.voicecall.privatecall.block.RoomVoiceCallStateBlock
import com.interfun.buz.voicecall.privatecall.block.SeatVoiceCallStateBlock
import com.interfun.buz.voicecall.privatecall.block.UserVoiceCallStateBlock
import com.interfun.buz.voicecall.privatecall.viewmodel.RealTimeCallViewModel
import com.jeremyliao.liveeventbus.LiveEventBus
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 1v1 voice call fragment
 */
class RealTimeCallFragment : BaseBindingFragment<VoicecallFragmentPrivateOnlinechatBinding>(), VoiceCallUI {

    companion object {
        const val TAG = "RealTimeCallFragment"
        fun newInstance(
            onlineChatJumpInfo: OnlineChatJumpInfo,
            jumpFrom: Int = 0,
            realTimeCallPendState: RealTimeCallPendState? = null
        ): RealTimeCallFragment {
            return RealTimeCallFragment().apply {
                arguments = Bundle().apply {
                    putParcelable(
                        RouterParamKey.ChannelInvite.JUMP_INFO,
                        onlineChatJumpInfo
                    )
                    putInt(
                        RouterParamKey.ChannelInvite.KEY_JUMP_INVITE_PAGE_FROM,
                        jumpFrom
                    )
                    putParcelable(
                        RouterParamKey.ChannelInvite.KEY_REAL_TIME_CALL_PEND_STATE,
                        realTimeCallPendState
                    )
                }
            }
        }
    }

    override val fragment: Fragment
        get() = this

    private var onlineChatJumpInfo: OnlineChatJumpInfo? = null
    private var jumpFrom:Int = 0
    private var _binding: VoicecallFragmentPrivateOnlinechatBinding? = null

    private val voiceCallViewModel by fragmentViewModels<RealTimeCallViewModel>()
    private val roomViewModel get() = VoiceCallPortal.currentRoomValue?.viewModel

    private val appSettingLauncher = fragment.appSettingsLauncher{
        roomViewModel?.getDeviceRoutes(false)
    }
    private val bluetoothPermissionInterceptor = if (Build.VERSION.SDK_INT >= VERSION_CODES.S){
        PermissionInterceptor(this, listOf(permission.BLUETOOTH_CONNECT),false)
    }else{
        PermissionInterceptor(this, listOf(permission.BLUETOOTH_ADMIN),false )
    }

    private val privateCallStater = PrivateCallStaterImpl(fragment)
    private val groupCallStater = GroupCallStaterImpl(fragment)


    override fun initView() {
        val realTimeCallPendState =
            arguments?.getParcelable<RealTimeCallPendState>(RouterParamKey.ChannelInvite.KEY_REAL_TIME_CALL_PEND_STATE)
        onlineChatJumpInfo = arguments?.getParcelable(RouterParamKey.Common.JUMP_INFO)
        jumpFrom = arguments?.getInt(RouterParamKey.ChannelInvite.KEY_JUMP_INVITE_PAGE_FROM) ?: 0
        logInfo(TAG, "OnlineChatJumpInfo = $onlineChatJumpInfo")
        onlineChatJumpInfo?.let { onlineChatJumpInfo ->
            RoomVoiceCallStateBlock(
                this,
                binding,
                onlineChatJumpInfo,
                jumpFrom
            ).bind(this)
            SeatVoiceCallStateBlock(
                this,
                binding,
                onlineChatJumpInfo
            ).bind(this)
            //
            UserVoiceCallStateBlock(
                this,
                binding,
                onlineChatJumpInfo,
                realTimeCallPendState
            ).bind(this)
            BottomControllerPanelBlock(
                this,
                binding.vcButtonContainer,
                onlineChatJumpInfo
            ).bind(this)
            TopTitleBarBlock(
                this,
                binding.vcTitleContainer,
                onlineChatJumpInfo,
                realTimeCallPendState
            ).bind(this)

            val channelId = onlineChatJumpInfo.channelId
            val channelType = onlineChatJumpInfo.channelType
            val callType = onlineChatJumpInfo.callType
            val jumpType = onlineChatJumpInfo.jumpType
            val targetId = onlineChatJumpInfo.targetId
            logInfo(TAG, "initView: targetId = $targetId")

            // 重建页面时，如果发现不在通话中，直接结束页面
            if (channelId == null && VoiceCallPortal.isOnRealTimeCall().not()) {
                toastNetworkErrorTips()
                fragment.activity?.finish()
                return@let
            }

            channelId?.let {
                voiceCallViewModel.fetchUserDetails(channelId)
                if (jumpType == OnlineChatJumpType.joinChannel) {
                    ChannelPendStatusManager.changeStatus(
                        CallPendStatus.CONNECTING,
                        CallPendInfo(
                            channelId,
                            channelType,
                            targetId,
                            callType = callType
                        )
                    )
                    ChannelInviteManager.dismiss(channelId.toString(), emitCancel = true)
                    joinInCallRoom(
                        channelId = channelId,
                        targetId = targetId,
                        channelType = channelType,
                        callType = callType,
                        source = 1,
                        cameraStatus = realTimeCallPendState?.cameraStatus,
                        micStatus = realTimeCallPendState?.micStatus
                    )
                }
            }
        }
        observeEvents()
    }

    override fun onResume() {
        super.onResume()
        arguments?.getParcelable<OnlineChatJumpInfo>(RouterParamKey.Common.JUMP_INFO)?.channelId?.let { channelId ->
            val voiceCallNotifiId = CallNotificationCache.getVoiceCallNotifiIdByChannelId(channelId)
            if (voiceCallNotifiId != null) {
                NotificationUtil.cancelVoiceCallNotificationAndUpdatePendStatus(voiceCallNotifiId,onlineChatJumpInfo!!.channelType)
            }
        }
        viewLifecycleScope.launch {
            AudioManagerHelper.muteChange(false)
        }
    }


    private fun joinInCallRoom(
        channelId: Long,
        targetId: Long,
        channelType: @ChannelType Int,
        callType: @CallType Int,
        source: Int,
        cameraStatus: @CameraStatus Int? = null,
        micStatus: @MicStatus Int? = null
    ) {
        logInfo(TAG, "RealTimeCallFragment#joinInCallRoom", logLine = LogLine.RTC_CALL)
        val result = VoiceCallPortal.joinRealTimeCall(
            channelId,
            targetId,
            channelType,
            callType,
            source,
            cameraStatus,
            micStatus,
            HeartBeatManager
        )
        if (jumpFrom != JumpVoiceCallPageFrom.GROUP_ITEM_CALL_CLICK && result != NO_CONFLICT){
            VoiceCallNotificationTracker.onAcceptCallResult(
                channelId,
                false,
                channelType,
                VoiceCallNotificationTracker.getNotifyWay(jumpFrom),
                PERMISSION_LIMIT_OR_ON_CALL
            )
        }
    }

    private fun observeEvents() {
        logInfo(TAG,"observeEvents: ")
        LiveEventBus.get(VoiceCallUserInfoChangeEvent::class.java).observe(viewLifecycleOwner) { event ->
            voiceCallViewModel.fetchUserDetails(event.channelId)
            voiceCallViewModel.startVoiceStatusCheck()
        }
    }

    override fun delayThenFinishActivity(delayTime: Long, reason: @CallEndType Int) {
        lifecycleScope.launch {
            delay(delayTime)
            if (isAdded && activity != null && VoiceCallPortal.roomLifecycle.value.first == DESTROY) {
                toastCallEnded(reason)
                activity?.finish()
            }
        }
    }

    override fun getAppSettingLauncher(): ActivityResultLauncher<Unit> {
        return appSettingLauncher
    }

    override fun getBluetoothPermissionInterceptor(): PermissionInterceptor {
        return bluetoothPermissionInterceptor
    }

    override fun getPrivateCallStart(): PrivateCallStater {
        return privateCallStater
    }

    override fun getGroupCallStart(): GroupCallStater {
        return groupCallStater
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null

    }

    override fun onDestroy() {
        super.onDestroy()
        logInfo(TAG,"onDestroy: ")
    }
}
