package com.interfun.buz.voicecall.groupcall.view.dialog

import android.app.Dialog
import android.os.Bundle
import androidx.fragment.app.DialogFragment
import com.alibaba.android.arouter.facade.annotation.Route
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.common.R.string
import com.interfun.buz.common.bean.voicecall.CallType
import com.interfun.buz.common.bean.voicecall.ChannelType
import com.interfun.buz.common.constants.KEY_TYPE
import com.interfun.buz.common.constants.PATH_GROUPCALL_EXIST_DIALOG
import com.interfun.buz.common.constants.RouterParamKey
import com.interfun.buz.common.eventbus.voicecall.ExcGroupOnCallDialogEvent
import com.interfun.buz.common.manager.CallNotificationCache
import com.interfun.buz.common.manager.cache.group.GroupInfoCacheManager
import com.interfun.buz.common.manager.chat.CallPendStatus
import com.interfun.buz.common.manager.chat.ChannelPendStatusManager
import com.interfun.buz.common.utils.NotificationUtil
import com.interfun.buz.common.voicecall.GroupCallStaterImpl
import com.interfun.buz.common.widget.dialog.CommonAlertDialog
import com.interfun.buz.voicecall.R

/**
 * Author: ChenYouSheng
 * Date: 2025/3/26
 * Email: <EMAIL>
 * Desc:
 */
@Route(path = PATH_GROUPCALL_EXIST_DIALOG)
class GroupCallExistDialog : DialogFragment() {

    private val groupCallStarter = GroupCallStaterImpl(this)

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val targetId = arguments?.getLong(RouterParamKey.ChannelInvite.KEY_TARGET_ID)
        val channelId = arguments?.getLong(RouterParamKey.ChannelInvite.KEY_CHANNEL_ID)
        val callType = arguments?.getInt(RouterParamKey.ChannelInvite.KEY_CALL_TYPE)
        val type = arguments?.getInt(RouterParamKey.ChannelInvite.KEY_TYPE)
        val dialog = when (type) {
            ExcGroupOnCallDialogEvent.TYPE_JOIN_CALL_FAILURE -> {
                createJoinCallFailedDialog()
            }

            ExcGroupOnCallDialogEvent.TYPE_JOIN_SAME_ROOM -> {
                createGroupCallExistDialog(
                    channelId = channelId ?: 0,
                    groupId = targetId ?: 0,
                    callType = callType ?: 0
                )
            }

            else -> super.onCreateDialog(savedInstanceState)
        }
        return dialog ?: super.onCreateDialog(savedInstanceState)
    }


    private fun createJoinCallFailedDialog() = CommonAlertDialog(
        context = requireActivity(),
        title = R.string.failed_to_join_voice_call.asString(),
        positiveText = R.string.ok.asString(),
        positiveCallback = {
            it.dismiss()
        }
    )

    private fun createGroupCallExistDialog(
        channelId: Long,
        groupId: Long,
        callType: @CallType Int
    ): CommonAlertDialog? {
        GroupInfoCacheManager.getGroupInfoBeanFromCache(groupId)?.groupName ?: return null
        val title = if (CallType.isVoiceCall(callType)) {
            R.string.rtc_stop_groupvoicecall_to_new_call.asString()
        } else {
            R.string.rtc_stop_groupvideocall_to_new_call.asString()
        }
        return CommonAlertDialog(
            context = requireActivity(),
            title = title,
            negativeText = string.cancel.asString(),
            negativeCallback = {
                if (ChannelPendStatusManager.isCalledPendState()) {
                    ChannelPendStatusManager.changeStatus(CallPendStatus.IDLE, null)
                }
                it.dismiss()
            },
            positiveText = string.join.asString(),
            positiveCallback = {
                it.dismiss()
                //check and cancel call style notification if exist
                val voiceCallNotifiId =
                    CallNotificationCache.getVoiceCallNotifiIdByChannelId(channelId)
                if (voiceCallNotifiId != null) {
                    NotificationUtil.cancelVoiceCallNotificationAndUpdatePendStatus(
                        voiceCallNotifiId,
                        channelType = ChannelType.TYPE_VOICE_CALL_GROUP
                    )
                }
                groupCallStarter.joinGroupVoiceCall(
                    context = requireActivity(),
                    channelId = channelId,
                    groupId = groupId,
                    callType = callType
                )
            },
            canceledOnTouchOutside = false,
            cancelable = false,
        )
    }

    override fun onStart() {
        super.onStart()
        dialog?.window?.setWindowAnimations(R.style.FadeDialogAnimation)
    }

}


