package com.interfun.buz.voicecall.core.manager

import android.content.Context
import com.google.android.flexbox.FlexboxLayoutManager
import com.interfun.buz.base.ktx.dp

class RealCallFlexboxLayoutManager(context: Context) : FlexboxLayoutManager(context) {
    private val itemSpacing = 2.dp
    private val columns: Int
        get() = when {
            itemCount <= 6 -> 2
            else -> 3
        }

    private val rows: Int
        get() {
            val fullRow = itemCount / columns
            val moreRow = if (itemCount % columns > 0) 1 else 0
            return fullRow + moreRow
        }


    // Avoid scroll
    override fun canScrollVertically(): Boolean {
        return false
    }

    // Avoid scroll
    override fun canScrollHorizontally(): <PERSON><PERSON>an {
        return false
    }

    override fun getChildWidthMeasureSpec(widthSpec: Int, padding: Int, childDimension: Int): Int {
        val width = (width - (itemSpacing * columns)) / columns
        return super.getChildWidthMeasureSpec(widthSpec, padding, width)
    }

    override fun getChildHeightMeasureSpec(
        heightSpec: Int,
        padding: Int,
        childDimension: Int
    ): Int {
        val itemHeight = (height - (itemSpacing * rows)) / rows
        return super.getChildHeightMeasureSpec(heightSpec, padding, itemHeight)
    }
}