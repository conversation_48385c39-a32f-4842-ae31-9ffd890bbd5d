package com.interfun.buz.voicecall.privatecall.viewmodel

import android.os.SystemClock
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.buz.idl.group.bean.GroupMember
import com.buz.idl.group.request.RequestGetGroupMembers
import com.buz.idl.group.service.BuzNetGroupServiceClient
import com.interfun.buz.base.ktx.LogLine
import com.interfun.buz.base.ktx.collectLatestIn
import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.base.ktx.logError
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.base.ktx.logWarn
import com.interfun.buz.common.bean.voicecall.CallRoomUser
import com.interfun.buz.common.bean.voicecall.CallStatus
import com.interfun.buz.common.bean.voicecall.ChannelType
import com.interfun.buz.common.bean.voicecall.VoiceCallRoom
import com.interfun.buz.common.database.entity.chat.isBigGroup
import com.interfun.buz.common.ktx.isMe
import com.interfun.buz.common.ktx.isSuccess
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.manager.cache.group.GroupInfoCacheManager
import com.interfun.buz.common.manager.voicecall.RoomLifecycle
import com.interfun.buz.common.manager.voicecall.VoiceCallPortal
import com.interfun.buz.common.net.withConfig
import com.interfun.buz.common.utils.PromptUtil
import com.interfun.buz.voicecall.R
import com.interfun.buz.voicecall.groupcall.view.fragment.GroupCallSelectMemberFragment
import com.interfun.buz.voicecall.privatecall.block.SeatVoiceCallStateBlock
import com.interfun.buz.voicecall.util.VoiceCallTracker
import com.yibasan.lizhifm.sdk.platformtools.ResUtil
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.collections.immutable.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import java.util.concurrent.atomic.AtomicBoolean
import javax.inject.Inject
import kotlin.collections.firstOrNull


interface IInviteGroupMember {
    fun initRequest(id: Long)
    fun inviteUser(id: Long)
    fun userListFLow(): StateFlow<PersistentList<CallUserInfo>>
    fun obtainGroupMember(): StateFlow<String>
}

enum class CallUserState {
    NORMAL,
    IN_CALLED,
    BE_CALLED,
}

data class CallUserInfo(
    val userName: String,
    val portraitUrl: String,
    val state: CallUserState,
    val uid: Long,
    val isShowLoading: Boolean = false
)

@HiltViewModel
class InviteGroupMemberViewModel @Inject constructor() : ViewModel(), IInviteGroupMember {

    private val TAG = "InviteGroupMemberViewModel"
    private val initRequest = AtomicBoolean(false)
    private var currentRoom: VoiceCallRoom? = null
    private val _membersFlow: MutableStateFlow<PersistentList<CallRoomUser>> =
        MutableStateFlow(persistentListOf())
    val membersFlow = _membersFlow.asStateFlow()
    private val groupService by lazy { BuzNetGroupServiceClient().withConfig() }
    private val _groupMemberFlow = MutableStateFlow<PersistentList<GroupMember>>(persistentListOf())
    private val groupMemberFlow = _groupMemberFlow.asStateFlow()
    private val _uiUserListFlow =
        MutableStateFlow<PersistentList<CallUserInfo>>(persistentListOf())
    private val uiUserListFlow = _uiUserListFlow.asStateFlow()

    @OptIn(ExperimentalCoroutinesApi::class)
    val toastFlow = VoiceCallPortal.currentRoom.value?.members?.flatMapLatest { members ->
        members.asFlow()
    }?.filter {
        it.callStatus == CallStatus.REJECT
                || it.callStatus == CallStatus.TIMEOUT
    }?.map {user->
        when (user.callStatus) {
            CallStatus.TIMEOUT -> ResUtil.getString(R.string.rtc_x_no_answer,user.userName)
            else -> ResUtil.getString(R.string.rtc_x_busy, user.userName)
        }
    }

fun requestGroupMember(groupId: Long) {
    logDebug(TAG, "requestGroupMember")
    viewModelScope.launch(Dispatchers.IO) {
//            logDebug(TAG, "requestGroupMember mutex")
        val isBigGroup =
            GroupInfoCacheManager.getGroupInfoBeanByIdSync(groupId ?: 0L)?.isBigGroup == true
        val queryType = if (isBigGroup) 1 else 0
        // Fetch the group member details from server
        val resp = groupService.getGroupMembers(
            RequestGetGroupMembers(
                groupId,
                queryType,
                null
            )
        )
        PromptUtil.parse(resp.data?.prompt)

        if (resp.isSuccess) {
            _groupMemberFlow.emit(
                resp.data?.groupMemberList?.toPersistentList() ?: persistentListOf()
            )
        } else {
            logError(TAG, "requestGroupMember fail")

        }
    }
}


override fun initRequest(id: Long) {
    if (!initRequest.compareAndSet(false, true)) {
        return
    }
    requestGroupMember(id)
    VoiceCallPortal.currentRoom.collectLatestIn(viewModelScope) { room ->
        currentRoom = room
//            doOnRoomObserver(room)
//            if (null == room) return@collectLatestIn
//            doOnRoomExist(room)
//            room.roomDestroyReason.collectLatestIn(scope) { reason ->
//                onRoomDestroy(room, reason)
//            }
        if (room != null && room.channelType == ChannelType.TYPE_VOICE_CALL_GROUP) {
            room.members.onEach { dataList ->
                // do something
                _membersFlow.value = dataList.toPersistentList()
                logDebug(TAG, "membersFlow: ${dataList}")
            }.launchIn(viewModelScope)
        }
//            room.getLifeCycle().observeLifecycleForever(lifecycleStateObserver)
    }


    viewModelScope.launch {
        _groupNameFlow.value =
            GroupInfoCacheManager.getGroupInfoBeanByIdSync(id)?.groupName ?: ""
    }
    combine(
        membersFlow,
        groupMemberFlow,
        showLoadingUserListFlow
    ) { onCallUser, groupUser, loadingList ->
        val uid2UserInCallMap = onCallUser.associateBy { it.userId }
        groupUser.map { usr ->
            val callRoomUser = uid2UserInCallMap[usr.userInfo.userId]
            val usrState = if (usr.userInfo.userId == UserSessionManager.getSessionUid()) {
                CallUserState.IN_CALLED
            } else {
                if (callRoomUser != null) {
                    //需要判断是否在 CALL
                    if (callRoomUser.callStatus == CallStatus.BE_CALLED) {
                        CallUserState.BE_CALLED
                    } else if (callRoomUser.callStatus == CallStatus.ON_CALL) {
                        CallUserState.IN_CALLED
                    } else {
                        CallUserState.NORMAL
                    }
                } else {
                    CallUserState.NORMAL
                }
            }

            CallUserInfo(
                userName = usr.userInfo.userName ?: "",
                portraitUrl = usr.userInfo.portrait ?: "",
                state = usrState,
                uid = usr.userInfo.userId ?: 0L,
                isShowLoading = loadingList.contains(usr.userInfo.userId)
            )
        }.toPersistentList()
    }.onEach {
        _uiUserListFlow.emit(it)
    }.launchIn(viewModelScope)
}


private val showLoadingUserListFlow = MutableStateFlow<PersistentSet<Long>>(persistentSetOf())

override fun inviteUser(id: Long) {
    viewModelScope.launch {
        // 需求：正在进行的语音房间内再邀请其他人的话  选完人后点击确认需要等待请求成功后再把邀请人的界面消除
        val startRequestTime = SystemClock.uptimeMillis()
        val room = VoiceCallPortal.currentRoomValue
        if (room == null) {
            logWarn(GroupCallSelectMemberFragment.TAG, "currentRoom is null")
        } else {
            showLoadingUserListFlow.value = showLoadingUserListFlow.value.add(id)
            val inviteResponse = room.inviteJoinRoomSuspend(listOf(id))

            VoiceCallTracker.resultBackRB2025031205(
                "${room.minimizeTargetId}",
                room.callType,
                "${room.roomChannelId}",
                inviteResponse?.code ?: 0
            )
            val endRequestTime = SystemClock.uptimeMillis()
            if (endRequestTime - startRequestTime < 1000) {
                // 如果请求时间小于1s，等待1s
                delay(1000)
            }
            showLoadingUserListFlow.value = showLoadingUserListFlow.value.remove(id)
        }
    }
}

override fun userListFLow(): StateFlow<PersistentList<CallUserInfo>> {
    return uiUserListFlow
}

private val _groupNameFlow = MutableStateFlow<String>("")
override fun obtainGroupMember(): StateFlow<String> {
    return _groupNameFlow.asStateFlow()
}


}