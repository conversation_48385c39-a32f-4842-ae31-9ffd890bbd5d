package com.interfun.buz.voicecall.groupcall.view.itemdelegate

import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.base.binding.BaseBindingDelegate
import com.interfun.buz.common.bean.voicecall.*
import com.interfun.buz.common.ktx.alphaAnim
import com.interfun.buz.common.ktx.animatorPlayWithFraction
import com.interfun.buz.common.ktx.fractionChangeAnim
import com.interfun.buz.common.ktx.isMe
import com.interfun.buz.common.manager.voicecall.RoomLifecycle
import com.interfun.buz.voicecall.R
import com.interfun.buz.voicecall.databinding.GroupVoicecallMemberItemViewBinding
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job

/**
 * @Desc itemView for online chat
 * @Author:<EMAIL>
 * @Date: 2023/03/04
 */
class VoiceChatGroupItemView(
    private val room: VoiceCallRoom,
) : BaseBindingDelegate<CallRoomUser, GroupVoicecallMemberItemViewBinding>() {

    companion object {
        private const val TAG = "VoiceChatGroupItemView"
    }


    override fun onViewHolderCreated(holder: BindingViewHolder<GroupVoicecallMemberItemViewBinding>) {
        super.onViewHolderCreated(holder)
        holder.binding.realTimeCallUserCard.apply {
            showUserName()
            setIconEnabled(true)
            setCanShowSpeakingAnim(false)
            animatorPlayWithFraction(
                duration = 300L,
                updateListener = { fraction ->
                    alpha = fractionChangeAnim(fraction, 0f, 1f)
                    scaleX = fractionChangeAnim(fraction, 0.5f, 1.0f)
                    scaleY = fractionChangeAnim(fraction, 0.5f, 1.0f)
                }
            )
        }
    }

    override fun onBindViewHolder(
        scope: CoroutineScope?,
        binding: GroupVoicecallMemberItemViewBinding,
        item: CallRoomUser,
        position: Int,
        payloads: List<Any>
    ) {
        super.onBindViewHolder(scope, binding, item, position, payloads)
        binding.realTimeCallUserCard.apply {
            if (!isInitialised) {
                initCard(
                    user = item,
                    applyTextureView = false, room.rtcVcResManager
                )
//                if (scope!=null) {
//                    bindVolumeFlow(
//                        scope,
//                        item,
//                        room.speakingFlow
//                    )
//                }
            }
        }
        if (payloads.isNotEmpty()) {
            logInfo(
                TAG,
                "onBindViewHolder(${item.userName})==>payloads: ${payloads.joinToString { it.toString() }}"
            )
            for (payload in payloads) {
                if (payload is CallPayload) {
                    handlePayloads(binding, item, payload)
                } else if (payload is CallPayloadType) {
                    handlePayload(binding, item, payload)
                } else if (payload is CameraPayload) {
                    //CameraPayload
                    handlePayload(binding, item, payload)
                }
            }
        } else {
            logInfo(
                TAG,
                "onBindViewHolder(${item.userName})==>position: ${adapterItems.indexOf(item)}, payloads: bindAll"
            )
            bindAll(binding, item)
            handlePayload(binding,item,CallPayloadType.SHOW_USERNAME)
            handlePayload(binding,item,CallPayloadType.UPDATE_CAMERA_STATUS)
        }
    }

    private fun bindAll(binding: GroupVoicecallMemberItemViewBinding, item: CallRoomUser) {
        animateItemVisible(binding)
        handleUserInfo(binding, item)
        handleMicStatus(binding, MicStatus.isMicMuted(item.micStatus))
        handleCameraStatus(binding, item, from = "bindAll")
        handleNetworkStatus(binding, NetworkStatus.isBadNetwork(item.netWorkStatus))
    }

    private fun handlePayloads(
        binding: GroupVoicecallMemberItemViewBinding,
        item: CallRoomUser,
        payload: CallPayload
    ) {
        for (type in payload.types) {
            handlePayload(binding, item, type)
        }
    }

    private fun handlePayload(
        binding: GroupVoicecallMemberItemViewBinding,
        item: CallRoomUser,
        type: CameraPayload
    ) {
        logDebug(TAG, "handlePayload(${item.userName}: type=$type")
        when (type) {
            is CaptureStarted -> {
                binding.realTimeCallUserCard.cameraCaptureStartedReact()
            }
            is DeviceChanged -> {
                binding.realTimeCallUserCard.cameraDeviceChangedReact()
            }
            is CaptureStopped -> {
                binding.realTimeCallUserCard.cameraCaptureStoppedReact(type.reason)
            }
        }
    }

    private fun handlePayload(
        binding: GroupVoicecallMemberItemViewBinding,
        item: CallRoomUser,
        type: CallPayloadType
    ) {
        logDebug(TAG, "handlePayload(${item.userName}: type=$type")
        when (type) {
            CallPayloadType.UPDATE_CAMERA_STATUS -> handleCameraStatus(
                binding,
                item,
                from = "UPDATE_CAMERA_STATUS"
            )

            CallPayloadType.UPDATE_CAMERA_STATE -> {
                binding.realTimeCallUserCard.switchCameraAnim()

            }
            CallPayloadType.UPDATE_MIC_STATUS -> handleMicStatus(
                binding,
                MicStatus.isMicMuted(item.micStatus)
            )

            CallPayloadType.UPDATE_USER_INFO -> handleUserInfo(binding, item)
            CallPayloadType.UPDATE_NETWORK_STATUS -> handleNetworkStatus(
                binding,
                NetworkStatus.isBadNetwork(item.netWorkStatus)
            )

            CallPayloadType.UPDATE_SPEAKING_STATUS -> handleSpeakingStatus(binding, item.isSpeaking)
            CallPayloadType.SHOW_USERNAME -> binding.realTimeCallUserCard.showUserName(true)
            CallPayloadType.HIDE_USERNAME -> binding.realTimeCallUserCard.hideUserName(true)
            CallPayloadType.RELEASE_CAMERA -> binding.realTimeCallUserCard.releaseCamera(room.rtcVcResManager)


            CallPayloadType.NO_ACTION -> {}
        }
    }

    private fun handleUserInfo(binding: GroupVoicecallMemberItemViewBinding, item: CallRoomUser) {
        logDebug(TAG, "handleUserInfo: user=${item.userName}")
        binding.realTimeCallUserCard.setUserInfo(item)
    }

    private fun handleMicStatus(
        binding: GroupVoicecallMemberItemViewBinding,
        isMuted: Boolean
    ) {
        logDebug(TAG, "handleMicStatus: isMuted=$isMuted")
        binding.realTimeCallUserCard.updateMuteStatus(isMuted)
    }

    private fun handleCameraStatus(
        binding: GroupVoicecallMemberItemViewBinding,
        item: CallRoomUser,
        from: String = ""
    ) {
        logDebug(
            TAG,
            "handleCameraStatus(${item.userName}): cameraStatus=${item.cameraStatus} ${room.currentLifecycle} ${adapterItems.size} "
        )
        if (item.userId.isMe()) {
            logInfo(
                TAG,
                "VoiceChatGroupItemView#handleCameraStatus: cameraStatus=${item.cameraStatus},from=${from}",
                logLine = LogLine.MY_RTC_CAMERA_STATE
            )
        }
        //这行如果删除要小心，打开摄像头和关闭摄像头只有在>=2人时才由该卡片控制，否则由另外的卡片控制
//        if (adapterItems.size <= 2) return
        binding.realTimeCallUserCard.apply {
            if (item.userId.isMe()) {
                if (CameraStatus.isOpen(item.cameraStatus)) {
                    startCameraCaptureNew(
                        isFront = CameraState.isFront(room.myCamState.value),
                        from = "VoiceChatGroupItemView#handleCameraStatus", room.rtcVcResManager
                    )
                } else {
                    stopCameraCapture(
                        from = "VoiceChatGroupItemView#handleCameraStatus",
                        roomVCResManager = room.rtcVcResManager
                    )
                }
            } else  {
                bindRemoteVideoView(item, room.rtcVcResManager)
            }
        }
    }

    private fun handleNetworkStatus(
        binding: GroupVoicecallMemberItemViewBinding,
        isPoorNetwork: Boolean
    ) {
        logDebug(TAG, "handleNetworkStatus: isPoorNetwork=$isPoorNetwork")
        binding.realTimeCallUserCard.updateNetworkStatus(isPoorNetwork)
    }

    private fun handleSpeakingStatus(
        binding: GroupVoicecallMemberItemViewBinding,
        isSpeaking: Boolean
    ) {
        logDebug(TAG, "handleSpeakingStatus: isSpeaking=$isSpeaking")
        binding.speakingBorder.visibleIf(isSpeaking)
    }

    override fun onViewRecycled(holder: BindingViewHolder<GroupVoicecallMemberItemViewBinding>) {
        detachView(holder)
        super.onViewRecycled(holder)
    }

    override fun onViewDetachedFromWindow(holder: BindingViewHolder<GroupVoicecallMemberItemViewBinding>) {
        detachView(holder)
        super.onViewDetachedFromWindow(holder)
    }

    override fun onFailedToRecycleView(holder: BindingViewHolder<GroupVoicecallMemberItemViewBinding>): Boolean {
        detachView(holder)
        return super.onFailedToRecycleView(holder)
    }

    private fun animateItemVisible(
        binding: GroupVoicecallMemberItemViewBinding,
    ) {
        binding.realTimeCallUserCard.visible()
        binding.realTimeCallUserCard.alphaAnim(150L, binding.realTimeCallUserCard.alpha, 1f).start()
    }

    private fun detachView(holder: BindingViewHolder<GroupVoicecallMemberItemViewBinding>) {
        logDebug(TAG, "detachView")
        holder.binding.apply {
            root.apply {
                getTag(R.id.palette_job)?.let {
                    if (it is Job && it.isActive) {
                        it.cancel()
                    }
                }
                setTag(R.id.palette_job, null)
            }
            realTimeCallUserCard.releaseCamera(room.rtcVcResManager)
        }
    }
}