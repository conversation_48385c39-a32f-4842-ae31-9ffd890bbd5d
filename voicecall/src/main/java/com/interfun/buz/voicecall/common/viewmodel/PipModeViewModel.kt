package com.interfun.buz.voicecall.common.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.bean.voicecall.CallRoomUser
import com.interfun.buz.common.bean.voicecall.ChannelType
import com.interfun.buz.common.interfaces.IVideoCallFloatTarget
import com.interfun.buz.common.interfaces.TargetChangeType
import com.interfun.buz.common.ktx.uid
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.manager.voicecall.RoomLifecycle
import com.interfun.buz.common.manager.voicecall.TargetChangeInfo
import com.interfun.buz.common.manager.voicecall.VoiceCallPortal
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.*


data class CallUserInfo(
    val callUser: CallRoomUser,
    val isPersonal: <PERSON><PERSON><PERSON>,
    val isCalling: Boolean
)

data class CameraSwitchInfo(
    val isCameraOpen: <PERSON>olean,
    val cameraSwitchType: CameraSwitchType
)

sealed interface CameraSwitchType {
    data object CollapseClicked : CameraSwitchType
    data object CameraCaptureStarted : CameraSwitchType
    data object CameraCaptureStopped : CameraSwitchType
    data object SystemRecommend : CameraSwitchType
    data object BackPressed : CameraSwitchType
    data object OnStopHint : CameraSwitchType
}

/**非Home键触发[BaseRealTimeCallActivity]进入onUserLeaveHint的来源*/
sealed class UserLeaveHintSource {
    data object CameraPermissionApply : UserLeaveHintSource()
    data object BluetoothPermissionApply : UserLeaveHintSource()
}

/**
 * Author: ChenYouSheng
 * Date: 2025/2/28
 * Email: <EMAIL>
 * Desc:
 */
class PipModeViewModel : ViewModel() {

    companion object {

        const val TAG = "PipModeViewModel"
        fun printLog(msg: String) {
            val callerClassName =
                Throwable().stackTrace.firstOrNull { !it.className.contains("Companion") }?.className?.substringAfterLast(
                    '.'
                ) ?: "Unknown"
            logDebug("PipModeLog", "from ${callerClassName}#Log:${msg}")
        }
    }

    private val _isPipModeEnabled = MutableSharedFlow<Boolean>()
    val isPipModeEnabled: SharedFlow<Boolean> = _isPipModeEnabled.asSharedFlow()


    // 用户信息
    private val _personalUserState = MutableSharedFlow<CallRoomUser>()
    private val _targetUserState = MutableSharedFlow<CallRoomUser>()

    // 房间生命周期
    private val _roomLifecycle: StateFlow<RoomLifecycle> =
        VoiceCallPortal.roomLifecycle.map { it.first }
            .stateIn(viewModelScope, SharingStarted.Eagerly, RoomLifecycle.INIT)

    val roomLifecycle: StateFlow<RoomLifecycle> = _roomLifecycle

    val isGroupChannelType: StateFlow<Boolean> = VoiceCallPortal.currentRoom
        .filterNotNull()
        .map { room ->
            ChannelType.isPrivateChannel(room.channelType).not()
        }.stateIn(viewModelScope, SharingStarted.Eagerly, false)

    private val _cameraCaptureState = MutableSharedFlow<CameraSwitchInfo>()
    val cameraCaptureState: SharedFlow<CameraSwitchInfo> = _cameraCaptureState.asSharedFlow()

    // 标记onUserLeaveHint的来源
    private val userLeaveHintSources: MutableSet<UserLeaveHintSource> = mutableSetOf()

    // 对方首次加入或者断网重新加入房间
    @OptIn(ExperimentalCoroutinesApi::class)
    val userJoinFlow: StateFlow<Boolean> = VoiceCallPortal.currentRoom
        .filterNotNull()
        .flatMapLatest { room ->
            combine(
                room.userJoinedStateFlow,
                callUsersState
            ) { joinUserIds, callUsers ->
                val targetUser = callUsers.find { !it.isPersonal }
                val rtcUid = targetUser?.callUser?.rtcUserId?.toLong() ?: -1L
                val joined = joinUserIds.contains(rtcUid)
                printLog("userJoinFlow combine rtcUid(${rtcUid}) joined=$joined，joinUserRtcIds:${joinUserIds}")
                joined
            }
        }.stateIn(viewModelScope, SharingStarted.Lazily, false)


    // 自己首次加入或者断网重新加入房间
    @OptIn(ExperimentalCoroutinesApi::class)
    val myJoinFlow: StateFlow<Boolean> = VoiceCallPortal.currentRoom
        .filterNotNull()
        .flatMapLatest { room ->
            room.myJoinedStateFlow.onEach { joined ->
                printLog("myJoinFlow joined=$joined")
            }
        }
        .stateIn(viewModelScope, SharingStarted.Lazily, true)


    // 判断是否要展示Calling UI， 如果没有任何远端用户加入或者房间处于calling状态，都展示Calling UI
    @OptIn(ExperimentalCoroutinesApi::class)
    val shouldShowCallingState: StateFlow<Boolean> = VoiceCallPortal.currentRoom.filterNotNull()
        .flatMapLatest { room ->
            combine(
                room.userJoinedStateFlow,
                _roomLifecycle
            ) { joinUserIds, roomLifecycle ->
                joinUserIds.isEmpty() || roomLifecycle.isAtMost(RoomLifecycle.CONNECTING)
            }
        }.stateIn(viewModelScope, SharingStarted.Lazily, true)

    // 画中画模式下通话对象信息
    @OptIn(ExperimentalCoroutinesApi::class)
    val pipModeTargetStateFlow: StateFlow<TargetChangeInfo?> =
        VoiceCallPortal.currentRoom.filterNotNull()
            .flatMapLatest { room ->
                room.targetStateChangeFlow
            }.stateIn(viewModelScope, SharingStarted.Lazily, null)

    // 视频通话用户列表
    val callUsersState: StateFlow<List<CallUserInfo>> = combine(
        _personalUserState,
        _targetUserState,
        _roomLifecycle,
        shouldShowCallingState
    ) { personalCallUser, targetCallUser, roomLifecycle, showCalling ->
        printLog("callUsersState combine roomLifecycle=${roomLifecycle}，" +
                "showCalling=${showCalling}")
        if (roomLifecycle == RoomLifecycle.DESTROY || roomLifecycle == RoomLifecycle.INIT) {
            printLog("callUsersState combine emptyList")
            emptyList()
        } else {
            val list = generalCallUserInfo(
                personalCallUser = personalCallUser,
                targetCallUser = targetCallUser,
                isCalling = showCalling,
            )
            printLog("callUsersState combine list(${list.size}): ${list.map { "${it.callUser.userId}-${it.callUser.rtcUserId}-${it.callUser.cameraStatus}" }}，" +
                    "myUserId:${UserSessionManager.uid}")
            list
        }
    }.stateIn(viewModelScope, SharingStarted.Eagerly, emptyList())


    fun setPipModeEnabled(enabled: Boolean, channelType: @ChannelType Int?): Boolean {
        logInfo(
            TAG, "setPipModeEnabled: $enabled, channelType: $channelType",
            logLine = LogLine.RTC_CALL
        )
        if (!enabled) {
            // 直接退出 PIP 模式，无需检查任何条件
            updatePipModeValue(false)
            return true
        }

        // 设备不支持 PIP , 不会进入 PIP 模式
        if (!canEnterPipMode()) {
            logInfo(TAG, "setPipModeEnabled not support, return", logLine = LogLine.RTC_CALL)
            return false
        }

        val roomValue = VoiceCallPortal.currentRoomValue
        if (roomValue == null) {
            logInfo(TAG, "setPipModeEnabled roomValue is null, return", logLine = LogLine.RTC_CALL)
            return false
        }

        val currentTarget = roomValue.currentTarget
        if (currentTarget == null) {
            logInfo(
                TAG, "setPipModeEnabled currentTarget is null, return true",
                logLine = LogLine.RTC_CALL
            )
            // 和MJ确认后，这种边界情况直接进入画中画
            updatePipModeValue(true)
            return true
        }
        // 至少一人开启了摄像头才能进入PIP模式
        if (!currentTarget.atLeastOneCameraOpen()) {
            logInfo(
                TAG, "setPipModeEnabled atLeastOneCameraOpen is false, return",
                logLine = LogLine.RTC_CALL
            )
            return false
        }
        updatePipModeValue(true)
        return true
    }

    fun notifyCameraCaptureState(isOpen: Boolean, cameraSwitchType: CameraSwitchType) {
        _cameraCaptureState.emitInScope(
            viewModelScope,
            CameraSwitchInfo(isCameraOpen = isOpen, cameraSwitchType = cameraSwitchType)
        )
    }


    private fun updatePipModeValue(enabled: Boolean) {
        if (_isPipModeEnabled.replayCache.firstOrNull() != enabled) {
            _isPipModeEnabled.emitInScope(viewModelScope, enabled)
        }
    }


    /**
     * 部分刷新
     */
    fun updateChanges(target: IVideoCallFloatTarget, changeType: TargetChangeType) {
        printLog("updateChanges target=${target}, changeType=${changeType}")
        target.dispatchRoomUser(personalCallback = { callUser ->
            if (changeType.isMyChange()) {
                printLog("updateChanges dispatchSelfRoomUser rtcId:${callUser.rtcUserId},userId:${callUser.userId}")
                emitValue(callUser = callUser)
            }
        }, targetCallback = { callUser ->
            if (changeType.isTargetChange()) {
                printLog("updateChanges dispatchTargetRoomUser rtcId:${callUser.rtcUserId},userId:${callUser.userId}")
                emitValue(isPersonal = false, callUser = callUser)
            }
        })
    }

    /**
     * 添加onUserLeaveHint的触发来源
     */
    fun addUserLeaveHintSource(source: UserLeaveHintSource) {
        userLeaveHintSources.add(source)
    }

    /**
     * 判断onUserLeaveHint是否是由[UserLeaveHintSource]触发的，返回结果的同时清空[userLeaveHintSources]
     */
    fun getAndSetUserLeaveHintSourceEmpty(): Boolean {
        printLog("getAndSetUserLeaveHintSourceEmpty userLeaveHintSources:${userLeaveHintSources}")
        val isEmpty = userLeaveHintSources.isEmpty()
        if (!isEmpty) {
            userLeaveHintSources.clear()
        }
        return isEmpty
    }

    private fun emitValue(
        isPersonal: Boolean = true,
        callUser: CallRoomUser,
    ) {
        if (isPersonal) {
            _personalUserState.emitInScope(
                viewModelScope, callUser
            )
        } else {
            _targetUserState.emitInScope(
                viewModelScope, callUser
            )
        }
    }

    /**
     * 除了calling状态，都展示上下分屏
     */
    private fun generalCallUserInfo(
        personalCallUser: CallRoomUser,
        targetCallUser: CallRoomUser,
        isCalling: Boolean,
    ): List<CallUserInfo> {
        return listOfNotNull(
            // 非拨号下，可以展示对方的头像
            CallUserInfo(
                callUser = targetCallUser,
                isPersonal = false,
                isCalling = isCalling
            ).takeIf { !isCalling },

            CallUserInfo(
                callUser = personalCallUser,
                isPersonal = true,
                isCalling = isCalling
            )
        )
    }

}

