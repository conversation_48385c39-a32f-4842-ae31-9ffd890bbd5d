package com.interfun.buz.voicecall.privatecall.viewmodel

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.buz.idl.realtimecall.request.RequestGetRealTimeCallUserList
import com.buz.idl.realtimecall.service.BuzNetRealTimeCallServiceClient
import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.common.bean.voicecall.CallRoomUser
import com.interfun.buz.common.bean.voicecall.CallStatus
import com.interfun.buz.common.ktx.isSuccess
import com.interfun.buz.common.manager.voicecall.VoiceCallPortal
import com.interfun.buz.common.net.withConfig
import com.interfun.buz.common.widget.toast.BuzToast
import com.interfun.buz.im.signal.HeartBeatManager
import com.interfun.buz.voicecall.R
import com.interfun.buz.voicecall.privatecall.bean.CallUserInfo
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class RealTimeCallViewModel : ViewModel() {

    private val _userList = MutableStateFlow<List<CallUserInfo>>(emptyList())
    val userList: StateFlow<List<CallUserInfo>> = _userList

    private val client by lazy {
        BuzNetRealTimeCallServiceClient().withConfig()
    }

    private val _channelId = MutableStateFlow<Long?>(null)
    val channelId: StateFlow<Long?> = _channelId

    fun fetchUserDetails(channelId: Long) {
        viewModelScope.launch {
            val request = RequestGetRealTimeCallUserList(channelId)
            val response = try {
                client.getRealTimeCallUserList(request)
            } catch (e: Exception) {
                Log.e("VoiceCallViewModel", "Network request failed", e)
                BuzToast.showToast(appContext, R.string.tips_network_error.asString())
                null
            }
            response?.let {
                if (response.isSuccess && it.data?.callUserInfoList != null) {
                    Log.d("VoiceCallViewModel", "Fetching Details : ${response.data?.callUserInfoList}")

                    val userDetails = it.data!!.callUserInfoList?.map { callUserInfo ->
                        val fullName = listOfNotNull(callUserInfo.firstName, callUserInfo.lastName).joinToString(" ").ifEmpty { "Unknown" }
                        CallUserInfo(
                            userId = callUserInfo.userId,
                            portrait = callUserInfo.portrait,
                            firstName = fullName,
                            lastName = callUserInfo.lastName,
                            joinedTime = callUserInfo.joinedTime,
                            callStatus = callUserInfo.callStatus,
                            micStatus = callUserInfo.micStatus,
                            updateTime = callUserInfo.updateTime,
                            callType = callUserInfo.callType
                        )
                    }
                    withContext(Dispatchers.Main) {
                        if (userDetails != null) {
                            _userList.value = userDetails
                        }
                    }
                } else {
                    withContext(Dispatchers.Main) {
                        Log.e("VoiceCallViewModel", "Error fetching user details: ${response.data?.prompt ?: "Unknown error"}")
                    }
                }
            }
        }
    }

    fun startVoiceStatusCheck() {
        viewModelScope.launch {
            delay(30000)
            _userList.value.getOrNull(1)?.let { user ->
                if (user.callStatus == CallStatus.ON_CALL) {
                    val updatedUserList = _userList.value.toMutableList()
                    updatedUserList[1] = user.copy(callStatus = CallStatus.TIMEOUT)
                    _userList.value = updatedUserList
                }
            }
        }
    }

    /**
     * call again Only available for 1v1 call
     */
    fun callAgain(
        userList: List<CallRoomUser>,
        channelType: Int,
        callType: Int,
        groupId: Long? = null,
        traceId: String
    ) {
        VoiceCallPortal.startRealTimeCall(
            userList = userList,
            channelType = channelType,
            callType = callType,
            groupId = groupId,
            traceId = traceId,
            HeartBeatManager
        )
    }
}

