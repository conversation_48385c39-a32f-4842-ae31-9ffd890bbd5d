package com.interfun.buz.voicecall.common.dialog

import android.view.animation.AnimationUtils
import com.interfun.buz.base.ktx.BindingViewHolder
import com.interfun.buz.base.ktx.asColor
import com.interfun.buz.base.ktx.onItemClick
import com.interfun.buz.base.ktx.visibleIf
import com.interfun.buz.common.base.binding.BaseBindingDelegate
import com.interfun.buz.common.bean.voicecall.AudioDevice
import com.interfun.buz.common.bean.voicecall.DeviceInfoItemOption
import com.interfun.buz.voicecall.R
import com.interfun.buz.voicecall.databinding.VoicecallDeviceSelectorItemViewBinding

class DeviceInfoItemView(private val itemCallback: ItemClickListenerCallback) :
    BaseBindingDelegate<DeviceInfoItemOption, VoicecallDeviceSelectorItemViewBinding>() {

    interface ItemClickListenerCallback {
        fun onItemClick(item: DeviceInfoItemOption)
    }

    override fun onBindViewHolder(
        binding: VoicecallDeviceSelectorItemViewBinding,
        item: DeviceInfoItemOption,
        position: Int
    ) {
        val device = AudioDevice.fromRouting(item.type)
        binding.tvDialogItemTitle.text = device.getLabel(item)
        binding.iftvDialogItemIcon.text = device.getIconFontRes()
        binding.iftvSelect.visibleIf(item.selected)

        if (item.selected){
            binding.iftvDialogItemIcon.setTextColor(R.color.basic_primary.asColor())
            binding.tvDialogItemTitle.setTextColor(R.color.basic_primary.asColor())
        }else{
            binding.iftvDialogItemIcon.setTextColor(R.color.text_white_main.asColor())
            binding.tvDialogItemTitle.setTextColor(R.color.text_white_main.asColor())
        }
        binding.ivLoading.visibleIf(item.showLoading)
        if (item.showLoading){
            val loadAnimation =
                AnimationUtils.loadAnimation(binding.ivLoading.context, R.anim.anim_loading_rotate)
            binding.ivLoading.startAnimation(loadAnimation)
        }else{
            binding.ivLoading.clearAnimation()
        }
    }

    override fun onViewHolderCreated(holder: BindingViewHolder<VoicecallDeviceSelectorItemViewBinding>) {
        super.onViewHolderCreated(holder)
        holder.onItemClick(this) { binding, item, pos ->
            itemCallback.onItemClick(item)
        }
    }
}