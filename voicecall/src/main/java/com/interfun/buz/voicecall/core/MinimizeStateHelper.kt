package com.interfun.buz.voicecall.core

import android.Manifest
import android.os.Build
import androidx.lifecycle.ProcessLifecycleOwner
import com.interfun.buz.base.ktx.isAppInForeground
import com.interfun.buz.base.ktx.isPermissionGranted
import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.base.ktx.logWarn
import com.interfun.buz.base.utils.BusUtil
import com.interfun.buz.common.bean.chat.OnlineChatJumpInfo
import com.interfun.buz.common.bean.chat.OnlineChatJumpType
import com.interfun.buz.common.bean.voicecall.CallType
import com.interfun.buz.common.bean.voicecall.ChannelType
import com.interfun.buz.common.eventbus.voicecall.CancelVoiceCallInviteEvent
import com.interfun.buz.common.ktx.getContactFirstName
import com.interfun.buz.common.manager.CallNotificationCache
import com.interfun.buz.common.manager.cache.group.GroupInfoCacheManager
import com.interfun.buz.common.manager.cache.user.UserRelationCacheManager
import com.interfun.buz.common.manager.chat.CallPendStatus
import com.interfun.buz.common.manager.chat.ChannelPendStatusManager
import com.interfun.buz.common.manager.realtimecall.VoiceCallMinimizeBlock
import com.interfun.buz.common.manager.voicecall.RoomLifecycle
import com.interfun.buz.common.manager.voicecall.VoiceCallPortal
import com.interfun.buz.common.utils.NotificationUtil
import com.lizhi.component.itnet.base.addForegroundWatcher
import com.lizhi.component.itnet.base.isForeground
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch

object MinimizeStateHelper {

    private val scope = GlobalScope

    private val _minimizeStateWithoutTime: MutableStateFlow<VoiceCallMinimizeBlock.MinimizeState> =
        MutableStateFlow(VoiceCallMinimizeBlock.MinimizeState.Dismiss())

    private val _minimizeState: MutableStateFlow<VoiceCallMinimizeBlock.MinimizeState> =
        MutableStateFlow(_minimizeStateWithoutTime.value)
    val finalMinimizeState: StateFlow<VoiceCallMinimizeBlock.MinimizeState> = _minimizeState

    private const val TAG = "MinimizeStateHelper"

    init {
        scope.launch {
            if (isAppInForeground) {
                checkVoiceCallNotification()
            }

            addForegroundWatcher {
                logDebug(TAG, "app in foreground: $it")
                if (it) {
                    checkVoiceCallNotification()
                }
            }
        }
        scope.launch {
            finalMinimizeState.collectLatest {
                logDebug(TAG, "collected state with time for log(minimizeStateWithTime): $it")
            }
        }
        scope.launch {
            launch {
                VoiceCallPortal.roomLifecycle.collectLatest {
                    logDebug(TAG, "collected room lifecycle: $it")

                    val room = VoiceCallPortal.currentRoomValue

                    if (it.first == RoomLifecycle.INIT || it.first == RoomLifecycle.DESTROY || room == null) {
                        _minimizeStateWithoutTime.value = VoiceCallMinimizeBlock.MinimizeState.Dismiss(room?.callType)
                        return@collectLatest
                    }

                    val callType = room.callType
                    logDebug(TAG,"callType = $callType")

                    val targetId = room.minimizeTargetId

                    val name = when (room.channelType) {
                        ChannelType.TYPE_VOICE_CALL_1V1 -> {
                            val userInfo =
                                UserRelationCacheManager.getUserRelationInfoByUid(targetId)
                            userInfo?.getContactFirstName()
                        }

                        ChannelType.TYPE_VOICE_CALL_GROUP -> {
                            val groupInfo = GroupInfoCacheManager.getGroupInfoBeanById(targetId)
                            groupInfo?.groupName
                        }

                        else -> {
                            logWarn(TAG, "unknown channelType : ${room.channelType}")
                            return@collectLatest
                        }
                    }

                    if (name.isNullOrBlank()) {
                        logWarn(TAG, "name is null or blank")
                        return@collectLatest
                    }


                    if (it.first.isAtLeast(RoomLifecycle.INIT)) {
                        observeCallDuration()
                    }

                    when (it.first) {
                        RoomLifecycle.START, RoomLifecycle.WAITING, RoomLifecycle.CONNECTING -> {
                            _minimizeStateWithoutTime.value =
                                VoiceCallMinimizeBlock.MinimizeState.Waiting(
                                    name = name,
                                    errorNetwork = false,
                                    callType = room.callType,
                                )
                        }

                        RoomLifecycle.CONNECTED -> {
                            _minimizeStateWithoutTime.value =
                                VoiceCallMinimizeBlock.MinimizeState.VoiceCall(
                                    name = name,
                                    timeString = "",
                                    callType = room.callType,
                                    channelId = room.roomChannelId ?: 0,
                                    channelType = room.channelType
                                )
                        }

                        else -> {
                            _minimizeStateWithoutTime.value =
                                VoiceCallMinimizeBlock.MinimizeState.Dismiss()
                        }
                    }
                }
            }

            launch {
                _minimizeStateWithoutTime.collectLatest {
                    logDebug(TAG, "collected state without time for log: $it")
                    if (it !is VoiceCallMinimizeBlock.MinimizeState.VoiceCall) {
                        // 用combine组合发现没有正常触发值，先这样处理着
                        _minimizeState.value = it
                    }
                }
            }
        }

        // 主动挂断不会走这个逻辑，还得额外处理：
        BusUtil.observeForever<CancelVoiceCallInviteEvent> {
            logDebug(
                TAG,
                "received CancelVoiceCallInviteEvent, channelId=${it.channelId}, current state=${_minimizeState.value}"
            )
            val state = _minimizeState.value
            if (state is VoiceCallMinimizeBlock.MinimizeState.Invited && it.channelId == state.channelId) {
                _minimizeStateWithoutTime.value = VoiceCallMinimizeBlock.MinimizeState.Dismiss(callType = it.callType)
            }
        }

        scope.launch {
            ChannelPendStatusManager.statusFlow.collectLatest {
                val state = it.first
                val info = it.second

                logInfo(TAG, "collected statusFlow: $state $info")
                if (it.first != CallPendStatus.BEING_INVITED && _minimizeStateWithoutTime.value is VoiceCallMinimizeBlock.MinimizeState.Invited) {
                    _minimizeStateWithoutTime.value = VoiceCallMinimizeBlock.MinimizeState.Dismiss(callType = info?.callType)
                }
            }
        }

    }

    private fun checkVoiceCallNotification() {
        CallNotificationCache.anyVoiceCallNotifyIdIsExist().let {
            logDebug(TAG, "notification exists: $it")

            val isNotificationPermissionGranted = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                isPermissionGranted(Manifest.permission.POST_NOTIFICATIONS)
            } else {
                NotificationUtil.isNotifyOpen()
            }

            if (it && isNotificationPermissionGranted) {
                val callPendState = ChannelPendStatusManager.getCallPendState()
                val callPendInfo = callPendState.second
                if (callPendInfo == null) {
                    logWarn(
                        TAG,
                        "notification exists but callPendInfo is null, state=$callPendState"
                    )
                    return@let
                }
                val channelId = callPendInfo.channelId
                val targetId = callPendInfo.targetId
                val channelType = callPendInfo.channelType
                val callType = callPendInfo.callType
                if (ChannelType.isVoiceCallType(channelType)) {
                    notifyMinimizeInvited(
                        true,
                        channelId,
                        targetId,
                        channelType,
                        callType = callType
                    )
                }
            }
        }
    }

    private var observerJob: Job? = null
    private fun observeCallDuration() {
        observerJob?.let {
            if (it.isActive) {
                it.cancel()
            }
        }
        observerJob = scope.launch {
            val currentRoom = VoiceCallPortal.currentRoomValue
            if (currentRoom == null) {
                logWarn(TAG, "currentRoom is null")
                return@launch
            }
            // 额外补充时间的收集
            currentRoom.callDuration.combine(
                _minimizeStateWithoutTime
            ) { duration, state ->
                if (state is VoiceCallMinimizeBlock.MinimizeState.VoiceCall) {
                    state.copy(timeString = duration)
                } else {
                    state
                }
            }.collectLatest {
                logDebug(TAG, "collected state with time: $it")
                _minimizeState.value = it
            }
        }
    }

    fun getMinimizeReenterInfo(): OnlineChatJumpInfo? {
        val room = VoiceCallPortal.currentRoomValue ?: return null
        val targetId = room.minimizeTargetId

        logDebug(
            TAG,
            "getMinimizeReenterInfo: room=$room, targetId=$targetId, callType=${room.channelType}"
        )

        return when (room.channelType) {
            ChannelType.TYPE_VOICE_CALL_1V1 -> {
                OnlineChatJumpInfo(
                    channelId = room.roomChannelId,
                    channelType = ChannelType.TYPE_VOICE_CALL_1V1,
                    callType = room.callType,
                    targetId = targetId,
                    jumpType = OnlineChatJumpType.reentryFromMinimize,
                )
            }

            ChannelType.TYPE_VOICE_CALL_GROUP -> {
                OnlineChatJumpInfo(
                    channelId = room.roomChannelId,
                    channelType = ChannelType.TYPE_VOICE_CALL_GROUP,
                    callType = room.callType,
                    targetId = targetId,
                    jumpType = OnlineChatJumpType.reentryFromMinimize
                )
            }

            else -> {
                logWarn(TAG, "unknown call type: ${room.channelType}")
                return null
            }
        }
    }

    fun notifyMinimizeInvited(
        shouldShow: Boolean,
        channelId: Long,
        targetId: Long,
        channelType: Int,
        callType: @CallType Int
    ) {
        if (!ChannelType.isVoiceCallType(channelType)) return

        if (shouldShow.not() && _minimizeStateWithoutTime.value is VoiceCallMinimizeBlock.MinimizeState.Invited) {
            _minimizeStateWithoutTime.value = VoiceCallMinimizeBlock.MinimizeState.Dismiss(callType = callType)
            return
        }

        logDebug(
            TAG,
            "state: ${ProcessLifecycleOwner.get().lifecycle.currentState}")
        if (isForeground().not()) {
            logDebug(TAG, "notifyMinimizeInvited app is not in foreground, ignore")
            // 后台不处理
            return
        }


        if (_minimizeStateWithoutTime.value is VoiceCallMinimizeBlock.MinimizeState.VoiceCall
            || _minimizeStateWithoutTime.value is VoiceCallMinimizeBlock.MinimizeState.Waiting
        ) {
            logDebug(TAG, "notifyMinimizeInvited already in voice call or waiting, ignore")
            return
        }

        _minimizeStateWithoutTime.value = VoiceCallMinimizeBlock.MinimizeState.Invited(
            targetId = targetId,
            isGroup = channelType == ChannelType.TYPE_VOICE_CALL_GROUP,
            channelId = channelId,
            callType = callType
        )
    }
}