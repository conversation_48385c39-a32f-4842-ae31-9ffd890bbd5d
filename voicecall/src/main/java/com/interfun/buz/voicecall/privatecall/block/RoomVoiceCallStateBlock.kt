package com.interfun.buz.voicecall.privatecall.block

import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.base.BaseVoiceCallBindingBlock
import com.interfun.buz.common.bean.chat.JumpVoiceCallPageFrom
import com.interfun.buz.common.bean.chat.OnlineChatJumpInfo
import com.interfun.buz.common.bean.chat.OnlineChatJumpType
import com.interfun.buz.common.bean.voicecall.*
import com.interfun.buz.common.constants.RouterParamKey
import com.interfun.buz.common.constants.RouterSchemes
import com.interfun.buz.common.database.entity.UserRelationInfo
import com.interfun.buz.common.database.entity.chat.GroupInfoBean
import com.interfun.buz.common.ktx.getContactFirstName
import com.interfun.buz.common.ktx.getLongDefault
import com.interfun.buz.common.ktx.isSuccess
import com.interfun.buz.common.ktx.toastCallEnded
import com.interfun.buz.common.manager.AppConfigRequestManager
import com.interfun.buz.common.manager.AppConfigRequestManager.enableVideoCall
import com.interfun.buz.common.manager.router.RouterManager
import com.interfun.buz.common.manager.voicecall.RoomLifecycle
import com.interfun.buz.common.manager.voicecall.VoiceCallPortal
import com.interfun.buz.common.utils.AcceptVoiceCallFailReason
import com.interfun.buz.common.utils.VoiceCallNotificationTracker
import com.interfun.buz.common.utils.combineView
import com.interfun.buz.common.utils.toJson
import com.interfun.buz.common.widget.dialog.CommonAlertDialog
import com.interfun.buz.voicecall.R
import com.interfun.buz.voicecall.common.interfaces.VoiceCallUI
import com.interfun.buz.voicecall.databinding.VoicecallFragmentPrivateOnlinechatBinding
import com.interfun.buz.voicecall.privatecall.viewmodel.VideoCallViewModel
import com.interfun.buz.voicecall.util.VoiceCallTracker
import org.json.JSONObject

/**
 * @Desc Handle room status and info
 * @Author:<EMAIL>
 * @Date: 2024/3/7
 */
class RoomVoiceCallStateBlock(
    private val voiceCallUI: VoiceCallUI,
    binding: VoicecallFragmentPrivateOnlinechatBinding,
    private val onlineChatJumpInfo: OnlineChatJumpInfo,
    private val jumpFrom: Int,
) : BaseVoiceCallBindingBlock<VoicecallFragmentPrivateOnlinechatBinding>(binding) {

    companion object {
        const val TAG = "RoomVoiceCallStateBlock"
    }

    private var roomExistFlag = false
    private var backgroundColor: Int? = null
    private val fragment = voiceCallUI.fragment

    private val channelType by lazy { onlineChatJumpInfo.channelType }
    private val callType by lazy { onlineChatJumpInfo.callType }
    private val isPrivateChannel
        get() = ChannelType.isPrivateChannel(channelType)
    private val isVideoCall
        get() = CallType.isVideoCall(callType)

    private val videoCallViewModel by fragment.activityViewModels<VideoCallViewModel>()
    private val networkObserver = NetworkAvailableLiveData()

    override fun initView() {
        super.initView()
        binding.ivGroupPortrait.visibleIf(!isVideoCall)
        if (isVideoCall && !enableVideoCall) {
            showUnsupportedVideoCallToast()
        }
        val targetId = if (onlineChatJumpInfo.targetId > 0) {
            onlineChatJumpInfo.targetId
        } else {
            currentRoom?.minimizeTargetId ?: 0L
        }
        //检测下房间是否还存在,边界情况下从最小化进入时刚好房间被销毁的case处理
        if (onlineChatJumpInfo.jumpType == OnlineChatJumpType.reentryFromMinimize) {
            if (VoiceCallPortal.isOnRealTimeCall().not()) {
                logWarn(
                    TAG,
                    "onlineChatJumpType reentry from Minimize, but current RealTimeCall is not exist, so finish activity immediately"
                )
                finishActivity()
                return
            }
        }
        videoCallViewModel.fetchBaseInformation(targetId, isPrivateChannel)
    }

    override fun initData() {
        super.initData()

        videoCallViewModel.apply {
            baseInformation.collectLatestIn(fragment.viewLifecycleOwner) { baseInfo ->
                backgroundColor = baseInfo?.seatColor
                if (isPrivateChannel) {
                    val userRelationInfo = baseInfo?.userRelationInfo ?: return@collectLatestIn
                    setUserInfo(userRelationInfo)
                } else {
                    val groupInfoBean = baseInfo?.groupInfoBean ?: return@collectLatestIn
                    setGroupInfo(groupInfoBean)
                }
            }
        }
        networkObserver.observe(fragment.viewLifecycleOwner) { hasNetwork ->
            val quality = if (hasNetwork.not()) NetworkStatus.WORST else NetworkStatus.BEST
            currentRoom?.setNetworkQuality(quality)
        }
    }

    private fun setUserInfo(userRelationInfo: UserRelationInfo) {
        binding.userPortraitIV.setPortrait(userRelationInfo.portrait)
        binding.ivGroupPortrait.setPortrait(userRelationInfo.portrait)
        binding.tvUserName.text = userRelationInfo.getContactFirstName()
    }

    private fun setGroupInfo(groupInfoBean: GroupInfoBean) {
        binding.userPortraitIV.setBackgroundColor(R.color.alpha_white_20.asColor())
        binding.userPortraitIV.setGroupInfoBean(groupInfoBean)
        binding.ivGroupPortrait.setBackgroundResource(R.drawable.voicecall_groupcall_portrait_background)
        binding.ivGroupPortrait.setGroupInfoBean(groupInfoBean)
        binding.tvUserName.text = groupInfoBean.groupName
    }

    private fun handlePageViewTracker() {
        val (roomLifecycle, _) = VoiceCallPortal.roomLifecycle.value
        when (roomLifecycle) {
            RoomLifecycle.INIT,
            RoomLifecycle.START,
            RoomLifecycle.WAITING -> {
                VoiceCallTracker.onCallWaitingPageView(
                    traceId = onlineChatJumpInfo.traceId,
                    channelType = onlineChatJumpInfo.channelType,
                    callType = onlineChatJumpInfo.callType,
                    targetId = onlineChatJumpInfo.targetId
                )
            }

            RoomLifecycle.CONNECTING -> {
                VoiceCallTracker.onCallConnectingPageView(
                    channelId = onlineChatJumpInfo.channelId.getLongDefault(),
                    channelType = onlineChatJumpInfo.channelType,
                    callType = onlineChatJumpInfo.callType
                )
            }

            RoomLifecycle.CONNECTED -> {
                VoiceCallTracker.onCallConnectedPageView(
                    channelId = onlineChatJumpInfo.channelId.getLongDefault(),
                    channelType = onlineChatJumpInfo.channelType,
                    callType = onlineChatJumpInfo.callType,
                    targetId = onlineChatJumpInfo.targetId
                )
            }

            else -> {}
        }
    }

    override fun doOnRoomObserver(room: VoiceCallRoom?) {
        super.doOnRoomObserver(room)
        logInfo(
            TAG,
            "doOnRoomObserver, onlineChatJumpInfo.jumpType: ${onlineChatJumpInfo.jumpType}, room = ${room?.roomChannelId}"
        )
        if (onlineChatJumpInfo.jumpType == OnlineChatJumpType.reentryFromMinimize || onlineChatJumpInfo.jumpType == OnlineChatJumpType.callInvitation) {
            //边界情况: 从最小化进入时刚好房间被销毁（room为空）
            if (room == null && roomExistFlag.not()) {
                logWarn(
                    TAG,
                    "onlineChatJumpType reentry from Minimize or StartRealTimeCall return callEnd code before room init, but current room is null, so finish activity immediately"
                )
                finishActivity()
            }
        }
    }

    override fun doOnRoomExist(room: VoiceCallRoom) {
        super.doOnRoomExist(room)
        logInfo(TAG, "doOnRoomExist")
        if (roomLifecycle == RoomLifecycle.CONNECTED) {
            connected=true
        }
        roomExistFlag = true
        if (jumpFrom != JumpVoiceCallPageFrom.GROUP_ITEM_CALL_CLICK && onlineChatJumpInfo.jumpType == OnlineChatJumpType.joinChannel) {
            logInfo(TAG, "doOnRoomExist ==> jumpFrom: $jumpFrom ")
            room.viewModel.joinRoomResultStateFlow.collectIn(fragment.viewLifecycleOwner) {
                if (null == it) return@collectIn
                if (it.isSuccess) {
                    VoiceCallNotificationTracker.onAcceptCallResult(
                        onlineChatJumpInfo.channelId ?: 0,
                        true,
                        channelType,
                        VoiceCallNotificationTracker.getNotifyWay(jumpFrom),
                    )
                } else {
                    VoiceCallNotificationTracker.onAcceptCallResult(
                        onlineChatJumpInfo.channelId ?: 0,
                        false,
                        channelType,
                        VoiceCallNotificationTracker.getNotifyWay(jumpFrom),
                        AcceptVoiceCallFailReason.REQUEST_FAIL,
                        it.code.toString()
                    )
                }
            }
        }
    }

    override fun onRoomConnecting(room: VoiceCallRoom) {
        super.onRoomConnecting(room)
        // 避免和onResume触发重复上报
        if (onlineChatJumpInfo.jumpType != OnlineChatJumpType.reentryFromMinimize) {
            VoiceCallTracker.onCallConnectingPageView(
                channelId = onlineChatJumpInfo.channelId.getLongDefault(),
                channelType = onlineChatJumpInfo.channelType,
                callType = onlineChatJumpInfo.callType
            )
        }
    }

    override fun onRoomConnected(room: VoiceCallRoom) {
        super.onRoomConnected(room)
        connected = true
        // 避免和onResume触发重复上报
        if (onlineChatJumpInfo.jumpType != OnlineChatJumpType.reentryFromMinimize) {
            VoiceCallTracker.onCallConnectedPageView(
                channelId = onlineChatJumpInfo.channelId.getLongDefault(),
                channelType = onlineChatJumpInfo.channelType,
                callType = onlineChatJumpInfo.callType,
                targetId = onlineChatJumpInfo.targetId
            )
        }
    }

    override fun onRoomDestroy(room: VoiceCallRoom, reason: @CallEndType Int) {
        logInfo(TAG, "onRoomDestroy ==> reason: $reason")
        super.onRoomDestroy(room, reason)
        handleRoomDestroy(room,reason)
        room.rtcVcResManager.stopCameraCapture(from = "onRoomDestroy")

    }

    override fun onResume() {
        super.onResume()
        logInfo(
            TAG,
            "onlineChatJumpInfo.jumpType:${OnlineChatJumpType.typeString(onlineChatJumpInfo.jumpType)}"
        )
        handlePageViewTracker()
    }

    /**
     * 标记是否成功链接过
     */
    var connected: Boolean = false
    private fun handleRoomDestroy(room: VoiceCallRoom,reason: Int) {
        val isVideoCall = room.callType == CallType.TYPE_VIDEO
        val baseInfo = videoCallViewModel.baseInformation.value
        val activity = fragment.activity
        logInfo(TAG, "handleRoomDestroy: reason: $reason ，connected=${connected}，" +
                "roomPipMode:${currentRoom?.isPipModeShowingFlow?.value}," +
                "activityPipMode:${activity?.isInPipMode}，" +
                "主动挂断是否展示:${AppConfigRequestManager.showEvaluationPage}", logLine = LogLine.RTC_CALL)
        if (baseInfo != null && (baseInfo.userRelationInfo != null || baseInfo.groupInfoBean != null)
            && connected
            && (reason != CallEndType.HANG_UP_BY_ME || AppConfigRequestManager.showEvaluationPage)
            && activity != null && currentRoom?.isPipModeShowingFlow?.value != true
        ) {
            val callRoomInfo = currentRoom?.generateCallRoomInfo(reason)
            logInfo(
                TAG,
                "handleRoomDestroy: 非pip模式下显示评价弹窗:callRoomInfo=${callRoomInfo}",
                logLine = LogLine.RTC_CALL
            )
            RouterManager.handle(
                activity,
                RouterSchemes.VoiceCall.APPRAISE_DLG,
                JSONObject(
                    mapOf(
                        RouterParamKey.VC.KEY_GROUP_INFO to baseInfo.groupInfoBean?.toJson(),
                        RouterParamKey.VC.KEY_USER_INFO to baseInfo.userRelationInfo?.toJson(),
                        RouterParamKey.VC.KEY_ROOM_INFO to callRoomInfo?.toJson(),
                        RouterParamKey.VC.KEY_H5_FEEDBACK_SOURCE to if (isVideoCall) RouterParamKey.Feedback.FEEDBACK_SOURCE_VIDEO_CALL else RouterParamKey.Feedback.FEEDBACK_SOURCE_VOICE_CALL
                    )
                ).toString(), null, null
            )

            finishActivity()
            return
        }
        if (currentRoom?.isPipModeShowingFlow?.value==true) {
            logInfo(TAG, "handleRoomDestroy: pip模式下只显示toast", logLine = LogLine.RTC_CALL)
            toastCallEnded(reason)
            finishActivity()
            return
        }
        when (reason) {
            CallEndType.EXC_BUSY,
            CallEndType.REJECT,
            CallEndType.EXC_BEING_BLOCKED -> {
                displayErrorUI()
                binding.tvStatus.text = R.string.call_busy.asString()
                voiceCallUI.delayThenFinishActivity(reason = reason)
            }

            CallEndType.TIME_OUT -> {
                // No Answer
                displayErrorUI()
                binding.tvStatus.text = R.string.rtc_no_answer.asString()
                voiceCallUI.delayThenFinishActivity(reason = reason)
            }

            CallEndType.UNKNOWN -> {
                // Network Error
                displayErrorUI()
                binding.tvStatus.text = R.string.voice_call_network_error.asString()
                voiceCallUI.delayThenFinishActivity(reason = reason)
            }

            CallEndType.EXC_GROUP_ON_CALL -> {
                if (!isPrivateChannel) handleExcGroupOnCall()
            }

            CallEndType.EXC_CONFLICT_WITH_ON_AIR -> {
                // OnAir Conflict
                showNoSupportVoiceCallDuringOnAirDialog()
            }

            else -> {
                toastCallEnded(reason)
                finishActivity()
            }
        }
    }

    private fun displayErrorUI() {
        combineView(
            binding.rvCallList,
            binding.rvStatusContainer,
            binding.vShadowBackground,
            binding.viewMyCameraMask,
            binding.flPersonalVideoContainer,
            binding.flTargetVideoContainer,
            binding.ivGroupPortrait
        ).gone()
        binding.groupRoomEnded.visible()
        if (isPrivateChannel) {
            backgroundColor?.let {
                binding.clPrivateVoiceCall.setBackgroundColor(it)
            }
            binding.viewBgMask.setBackgroundResource(R.color.color_overlay_black_medium)
        } else {
            binding.clPrivateVoiceCall.setBackgroundResource(R.color.color_background_3_default)
            binding.viewBgMask.setBackgroundResource(R.drawable.voicecall_groupcall_background)
        }
        binding.viewBgMask.visible()
    }

    private fun handleExcGroupOnCall() {
        videoCallViewModel.checkJoinOrExist(
            activity = fragment.requireActivity(),
            targetId = onlineChatJumpInfo.targetId,
            callType = onlineChatJumpInfo.callType
        ) {
            finishActivity()
        }
    }

    private fun showNoSupportVoiceCallDuringOnAirDialog() {
        CommonAlertDialog(
            context = fragment.requireContext(),
            title = R.string.rtc_onlyone_of_liveplace_and_call_canbeuse.asString(),
            positiveText = R.string.ok.asString(),
            positiveCallback = {
                it.dismiss()
                fragment.finishActivity()
            },
            canceledOnTouchOutside = false,
            cancelable = false
        ).show()
    }

    private fun showUnsupportedVideoCallToast() {
        CommonAlertDialog(
            context = fragment.requireContext(),
            title = R.string.rtc_videocall_not_support_area.asString(),
            positiveText = R.string.ok.asString(),
            positiveCallback = {
                it.dismiss()
                fragment.finishActivity()
            },
            canceledOnTouchOutside = false,
            cancelable = false
        )
    }

    private fun finishActivity() {
        logDebug(TAG, "finishActivity")
        fragment.activity?.finish()
    }
}