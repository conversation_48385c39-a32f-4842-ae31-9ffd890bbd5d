package com.interfun.buz.voicecall.common.view.activity

import android.annotation.SuppressLint
import android.app.PictureInPictureParams
import android.content.Intent
import android.content.res.Configuration
import android.graphics.Rect
import android.os.Build
import android.os.Bundle
import android.util.Rational
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import androidx.activity.SystemBarStyle
import androidx.activity.enableEdgeToEdge
import androidx.compose.ui.platform.ComposeView
import androidx.lifecycle.lifecycleScope
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.base.SimpleContainerActivity
import com.interfun.buz.common.bean.chat.OnlineChatJumpInfo
import com.interfun.buz.common.bean.voicecall.ChannelType
import com.interfun.buz.common.bean.voicecall.VoiceCallRoom
import com.interfun.buz.common.constants.JUMP_INFO
import com.interfun.buz.common.constants.RouterParamKey
import com.interfun.buz.common.interfaces.TargetChangeType
import com.interfun.buz.common.manager.voicecall.RoomLifecycle
import com.interfun.buz.common.manager.voicecall.VoiceCallPortal
import com.interfun.buz.common.service.RealTimeCallService
import com.interfun.buz.voicecall.R
import com.interfun.buz.voicecall.common.utils.VCTracker
import com.interfun.buz.voicecall.common.view.floating.VideoCallMinimizeScreen
import com.interfun.buz.voicecall.common.viewmodel.CameraSwitchType
import com.interfun.buz.voicecall.common.viewmodel.PipModeViewModel
import com.interfun.buz.voicecall.common.viewmodel.PipModeViewModel.Companion.printLog
import com.interfun.buz.voicecall.privatecall.block.IImmersiveBridge
import com.yibasan.lizhifm.liveinteractive.CameraEventHandler
import com.yibasan.lizhifm.liveinteractive.CameraStopReason
import com.yibasan.lizhifm.lzlogan.Logz
import java.util.concurrent.atomic.AtomicBoolean

/**
 * Author: ChenYouSheng
 * Date: 2025/2/28
 * Email: <EMAIL>
 * Desc: 实时通话基类，处理画中画逻辑
 */
open class BaseRealTimeCallActivity : SimpleContainerActivity() {

    private val pipModeViewModel by activityViewModels<PipModeViewModel>()
    private val container by lazy { (findViewById<View>(android.R.id.content) as ViewGroup) }
    private var originalView: View? = null
    private val floatingView by lazy { ComposeView(this) }
    private val logoView by lazy {
        FrameLayout(this).apply {
            layoutParams = FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.MATCH_PARENT
            )
            setBackgroundColor(R.color.color_background_4_default.asColor())
            val logoSize = context.screenWidth / 2
            val imageView = ImageView(context).apply {
                setImageResource(R.mipmap.buz_launcher_round)
                layoutParams = FrameLayout.LayoutParams(logoSize, logoSize).apply {
                    gravity = Gravity.CENTER
                }
                scaleType = ImageView.ScaleType.FIT_CENTER
                adjustViewBounds = true
                translationY = -statusBarHeight.toFloat()
            }
            addView(imageView)
        }
    }
    private var wasInPipMode: Boolean = false
    // 标记用户点击画中画的关闭按钮
    private var mayExitedPipByClickCloseButton: Boolean = false

    private val realTimeCallService by lazy { routerServices<RealTimeCallService>() }

    protected val onlineChatJumpInfo: OnlineChatJumpInfo?
        get() = intent.getParcelableExtra(
            RouterParamKey.Common.JUMP_INFO
        )
    protected val channelType: Int? get() = onlineChatJumpInfo?.channelType
    protected val callType: Int? get() = onlineChatJumpInfo?.callType
    protected val isGroupCall: Boolean get() = channelType == ChannelType.TYPE_VOICE_CALL_GROUP
    val isInPipMode: Boolean get() = realTimeCallService.value?.isPipModeExist() == true
    protected val room: VoiceCallRoom? get() = VoiceCallPortal.currentRoomValue
    private val isInit = AtomicBoolean(false)


    companion object {
        const val TAG = "BaseRealTimeCallActivity"
    }

    private val cameraEventHandler = object : CameraEventHandler {
        override fun onCameraCaptureStarted() {
            printLog("onCameraCaptureStarted")
            pipModeViewModel.notifyCameraCaptureState(
                isOpen = true,
                cameraSwitchType = CameraSwitchType.CameraCaptureStarted
            )
        }

        override fun onCameraDeviceChanged(isFront: Boolean) {
            printLog("onCameraDeviceChanged isFront=$isFront")
        }

        // 摄像头被占用时回调
        override fun onCameraCaptureStopped(reason: CameraStopReason?) {
            printLog("onCameraCaptureStopped reason=${reason}")
            pipModeViewModel.notifyCameraCaptureState(
                isOpen = false,
                cameraSwitchType = CameraSwitchType.CameraCaptureStopped
            )
            VCTracker.cameraStopInVc(reason)
        }
    }

    var roomWrapper: VoiceCallRoom?=null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        VoiceCallPortal.currentRoom.collect(lifecycleOwner) { room ->
            if (roomWrapper==null && room!=null) {
                roomWrapper=room
                room.rtcVcResManager.addCameraEventHandler(cameraEventHandler)
                floatingView.setContent {
                    VideoCallMinimizeScreen(pipModeViewModel = pipModeViewModel,voiceCallRoom=room)
                }
            }
        }
    }

    override fun setSystemBarImmersive() {
        enableEdgeToEdge(
            statusBarStyle = SystemBarStyle.dark(android.graphics.Color.TRANSPARENT),
            navigationBarStyle = SystemBarStyle.dark(android.graphics.Color.TRANSPARENT),
        )
    }

    override fun onViewCreated() {
        super.onViewCreated()
        originalView = container.getChildAt(0)

        pipModeViewModel.isPipModeEnabled.collectInScope(lifecycleScope) { isEnabled ->
            val target = room?.currentTarget
            logInfo(TAG,"isPipModeEnabled collectIn isEnabled=$isEnabled，target=${target}",
                logLine = LogLine.RTC_CALL)
            if (isEnabled) {
                enterPiPMode()
            }
        }
    }

    private fun initListener() {
        lifecycleScope.apply {
            pipModeViewModel.pipModeTargetStateFlow.collectInScope(this) { info ->
                printLog("targetStateChangeFlow collectIn isInPipMode=${isInPipMode},info=${info}")
                if (!isInPipMode || info == null) {
                    return@collectInScope
                }
                pipModeViewModel.updateChanges(info.target, info.changeType)
            }

            pipModeViewModel.roomLifecycle.collectInScope(this) {
                logInfo(TAG,"roomLifecycle collectIn isInPipMode=${isInPipMode},roomLifecycle=${it}",
                    logLine = LogLine.RTC_CALL)
                if (it == RoomLifecycle.DESTROY && isInPipMode) {
                    finish()
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
        // 回到全屏的情况，说明不是点击关闭PIP退出的
        mayExitedPipByClickCloseButton = false
        printLog("onResume")
    }

//    override fun onUserLeaveHint() {
//        super.onUserLeaveHint()
//        logInfo(TAG,"onUserLeaveHint", logLine = LogLine.RTC_CALL)
//        // 申请权限弹窗也会导致onUserLeaveHint触发，避免进入画中画模式
//        // 所以这里特殊判断一下，不是很优雅，暂时没有想到更好的办法
//        printLog("onUserLeaveHint")
//        if (!pipModeViewModel.getAndSetUserLeaveHintSourceEmpty()) {
//            return
//        }
//        tryEnterPipMode(cameraSwitchType = CameraSwitchType.UserLeaveHint)
//    }

    override fun onPictureInPictureRequested(): Boolean {
        if (!pipModeViewModel.getAndSetUserLeaveHintSourceEmpty()) {
            logInfo(TAG,"onPictureInPictureRequested return false，权限申请触发导致",
                logLine = LogLine.RTC_CALL)
            return false
        }
        val enter = tryEnterPipMode(cameraSwitchType = CameraSwitchType.SystemRecommend)
        logInfo(TAG,"onPictureInPictureRequested enter=${enter}", logLine = LogLine.RTC_CALL)
        return enter
    }

    private fun tryEnterPipMode(needFinish: Boolean = false, cameraSwitchType: CameraSwitchType): Boolean {
        val success = pipModeViewModel.setPipModeEnabled(enabled = true, channelType = channelType)
        if (isGroupCall && !success) {
            // 最小化的3个入口，右上角，按返回键，按home键，在群聊视频通话下都自动关闭摄像头并toast
            pipModeViewModel.notifyCameraCaptureState(
                isOpen = false,
                cameraSwitchType = cameraSwitchType
            )
            if (needFinish) {
                finish()
            }
        }
        return success
    }

    @SuppressLint("MissingSuperCall")
    override fun onBackPressed() {
        tryEnterPipMode(needFinish = true, cameraSwitchType = CameraSwitchType.BackPressed)
    }

    override fun onPictureInPictureModeChanged(
        isInPictureInPictureMode: Boolean, newConfig: Configuration
    ) {
        super.onPictureInPictureModeChanged(isInPictureInPictureMode, newConfig)
        VoiceCallPortal.currentRoomValue?.isPipModeOpening = false
        printLog("onPictureInPictureModeChanged：${isInPictureInPictureMode}，" +
                "mayExitedPipByClickCloseButton：${mayExitedPipByClickCloseButton}," +
                "wasInPipMode:${wasInPipMode}")
        if (wasInPipMode && !isInPictureInPictureMode && mayExitedPipByClickCloseButton) {
            // 如果是主动点击关闭按钮退出画中画则关闭页面
            printLog("onPictureInPictureModeChanged finish activity by click pip window‘s close button")
            finish()
            return
        }
        wasInPipMode = isInPictureInPictureMode
        if (isInPictureInPictureMode) {
            showPipModeUI()
        } else {
            showNormalModeUI()
        }
    }

    override fun onStop() {
        printLog("onStop: wasInPipMode==>$wasInPipMode，，isFinishing=${isFinishing},,isScreenOff=${isScreenOff}")
        pipModeViewModel.notifyCameraCaptureState(
            isOpen = false,
            cameraSwitchType = CameraSwitchType.OnStopHint
        )
        mayExitedPipByClickCloseButton = true
        super.onStop()
    }

    override fun onPause() {
        super.onPause()
        printLog("onPause: isFinishing=${isFinishing}")
    }

    private fun showNormalModeUI() {
        printLog("showNormalModeUI")
        VoiceCallPortal.currentRoomValue?.isPipModeShowing = false
        pipModeViewModel.setPipModeEnabled(enabled = false, channelType = channelType)
        originalView?.visible()
        floatingView.removeFromParent()
    }

    private fun showPipModeUI() {
        val target = room?.currentTarget
        printLog("showPipModeUI start target=${target}")
        VoiceCallPortal.currentRoomValue?.isPipModeShowing = true
        if (isInit.compareAndSet(false, true)) {
            initListener()
        }
        if (null != target) {
            pipModeViewModel.updateChanges(target, TargetChangeType.All)
        }
        floatingView.removeFromParent()
        logoView.removeFromParent()
        originalView?.gone()
        container.addView(
            floatingView,
            FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.MATCH_PARENT
            )
        )
    }

    private fun enterPiPMode() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val aspectRatio = Rational(9, 16)
            // 不传的话，默认会有白屏
            var rect = Rect(0, 0, screenWidth, screenHeight)
            printLog("enterPiPMode rect=$rect")
            val params = PictureInPictureParams.Builder()
                .setAspectRatio(aspectRatio)
                .setSourceRectHint(rect)
            try {
                val success = enterPictureInPictureMode(params.build())
                logInfo(TAG, "enterPiPMode success=$success", logLine = LogLine.RTC_CALL)
                if (!success) {
                    throw Exception("can not enter picture in picture mode")
                } else {
                    doOnStartPipModeSuccess()
                }
            } catch (e: Exception) {
                Logz.tag(TAG).e(e, "enterPiPMode failed")
                floatingView.removeFromParent()
                finish()
            }
        }
    }

    private fun doOnStartPipModeSuccess() {
        logoView.removeFromParent()
        container.addView(
            logoView,
            FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.MATCH_PARENT
            )
        )
        VoiceCallPortal.currentRoomValue?.isPipModeOpening = true
        obtainRegisterInterface2(IImmersiveBridge::class.java)?.openImmersive()
    }

    // fix：从画中画打开的页面不会出现在画中画中
    override fun startActivityForResult(intent: Intent, requestCode: Int) {
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        super.startActivityForResult(intent, requestCode)
    }

    override fun onDestroy() {
        VoiceCallPortal.currentRoomValue?.isPipModeShowing = false
        roomWrapper?.rtcVcResManager?.removeCameraEventHandler(cameraEventHandler)
        super.onDestroy()
    }
}