package com.interfun.buz.voicecall.groupcall.view.dialog

import android.os.Bundle
import android.view.View
import android.view.WindowInsets
import androidx.activity.addCallback
import androidx.appcompat.app.AppCompatDialog
import androidx.core.view.WindowInsetsCompat
import com.alibaba.android.arouter.facade.annotation.Route
import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.base.ktx.requireContext
import com.interfun.buz.base.utils.ScreenUtil.getScreenHeightReal
import com.interfun.buz.common.bean.chat.JumpStartRealTimeCallEntrance
import com.interfun.buz.common.bean.voicecall.CallType
import com.interfun.buz.common.constants.KEY_CALL_TYPE
import com.interfun.buz.common.constants.KEY_SOURCE
import com.interfun.buz.common.constants.PATH_GROUPCALL_DIALOG_FRAGMENT
import com.interfun.buz.common.constants.RouterParamKey
import com.interfun.buz.common.constants.RouterParamKey.Group.KEY_GROUP_ID
import com.interfun.buz.common.constants.RouterParamKey.Group.KEY_USER_IDS
import com.interfun.buz.common.ktx.getLongDefault
import com.interfun.buz.common.widget.dialog.BaseBottomSheetDialogFragment
import com.interfun.buz.voicecall.databinding.GroupcallFragmentSelectMemberDialogBinding
import com.interfun.buz.voicecall.groupcall.constants.SelectMemberSource
import com.interfun.buz.voicecall.groupcall.view.fragment.GroupCallSelectMemberFragment

/**
 * @Desc: 群聊信息
 * @Author: <EMAIL>
 * @Date: 2022/6/23
 */

@Route(path = PATH_GROUPCALL_DIALOG_FRAGMENT)
class GroupCallSelectMemberDialog() : BaseBottomSheetDialogFragment() {

    override val TAG = "GroupCallSelectMemberDialog"

    private val groupId by lazy {
        arguments?.getLong(KEY_GROUP_ID).getLongDefault()
    }
    private val inCallingUsers by lazy {
        arguments?.getLongArray(KEY_USER_IDS) ?: longArrayOf()
    }
    private val callType by lazy {
        arguments?.getInt(RouterParamKey.Group.KEY_CALL_TYPE) ?: CallType.TYPE_VOICE
    }
    private val source by lazy {
        arguments?.getInt(RouterParamKey.Group.KEY_SOURCE) ?: SelectMemberSource.START_CALL
    }

    // 用于打点的来源
    private val entrance by lazy {
        arguments?.getInt(RouterParamKey.Group.KEY_FROM_ENTRANCE)
            ?: JumpStartRealTimeCallEntrance.GROUP_CALL_SELECT
    }

    val binding: GroupcallFragmentSelectMemberDialogBinding by lazy {
        GroupcallFragmentSelectMemberDialogBinding.inflate(layoutInflater)
    }

    override fun onCreateView(): View {
        return binding.root
    }

    // Bottom back button pressed
    override fun onCreateDialog(savedInstanceState: Bundle?) =

        super.onCreateDialog(savedInstanceState).also { dialog ->
            dialog?.window?.decorView?.setOnApplyWindowInsetsListener(object : View.OnApplyWindowInsetsListener {
                override fun onApplyWindowInsets(
                    v: View,
                    insets: WindowInsets
                ): WindowInsets {
                    val windowInsetsCompat = WindowInsetsCompat.toWindowInsetsCompat(insets)
                    val navHeight = windowInsetsCompat.getInsets(WindowInsetsCompat.Type.navigationBars())
                    val layoutParams = binding.navPad.layoutParams
                    layoutParams.height=navHeight.bottom
                    binding.navPad.layoutParams=layoutParams
//                val params = (MarginLayoutParams) bottomView . getLayoutParams ();
//                params.bottomMargin = bottomGestureInset;
//                bottomView.setLayoutParams(params);
                    return insets;
                }
            })
            (dialog as AppCompatDialog).onBackPressedDispatcher.addCallback(this) {
                dismiss()
            }
        }

    override fun initView(view: View?) {
        // Set fragment (default: Choose member)
        logDebug(TAG, "groupId: $groupId")

        if (groupId == 0L) {
            dismiss()
        }

//        binding.ivHandle.click {
//            dismiss()
//        }

        childFragmentManager.beginTransaction().apply {
            add(
                binding.flContainer.id,
                GroupCallSelectMemberFragment.newInstance(
                    groupId = groupId,
                    inCallingUsers = inCallingUsers,
                    callType = callType,
                    source = source,
                    entrance = entrance
                ),
                GroupCallSelectMemberFragment::class.java.simpleName
            )
            commit()
        }


    }

    override fun initListener(view: View?) {}
    override fun draggable(): Boolean {
        return false
    }

    // Set dialog height
    override fun getHeight(): Int {
        return getScreenHeightReal(requireContext)
    }
}