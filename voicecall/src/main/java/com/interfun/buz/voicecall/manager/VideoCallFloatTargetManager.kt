package com.interfun.buz.voicecall.manager

import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.common.bean.voicecall.CallRoomUser
import com.interfun.buz.common.bean.voicecall.CallStatus
import com.interfun.buz.common.bean.voicecall.CameraStatus
import com.interfun.buz.common.bean.voicecall.ChannelType
import com.interfun.buz.common.interfaces.IVideoCallFloatTarget
import com.interfun.buz.common.interfaces.IVideoCallFloatTargetManager
import com.interfun.buz.common.interfaces.TargetChangeType
import com.interfun.buz.common.ktx.uid
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.voicecall.common.model.VideoCallFloatTarget

/**
 * Author: ChenYouSheng
 * Date: 2025/2/25
 * Email: <EMAIL>
 * Desc:
 */
class VideoCallFloatTargetManager : IVideoCallFloatTargetManager {

    companion object {
        const val TAG = "PipModeLog"
    }

    private var lastCallTarget: IVideoCallFloatTarget? = null
    private var lastTargetCallUser: CallRoomUser? = null
    private var lastMyCallUser: CallRoomUser? = null

    /**
     * targetUserId的创建规则：
     * - 如果有人说话中，就展示“最后一个开始说话”的人；
     * - 如果没人说话了，就继续展示最后说话的那个人（即 不切换）。
     * - 一开始默认对准除了自己以外，第一个进通话的对象
     */
    override fun createTarget(
        members: List<CallRoomUser>, channelType: Int
    ): Pair<Boolean, IVideoCallFloatTarget>? {

        val myUserId = UserSessionManager.uid
        val otherMembers = members.filter { it.userId != myUserId }

        // 优先找“正在说话”并且开始说话时间最近的人
        val targetUserId = otherMembers.maxByOrNull { it.startSpeakingTime }?.userId
            // 如果没人说话，找“最早加入房间的人”
            ?: otherMembers.minByOrNull { it.joinTime }?.userId
            ?: return null

        val isGroup = ChannelType.isPrivateChannel(channelType).not()
        return createAndReplace(
            isGroup = isGroup,
            currentUserId = myUserId,
            targetUserId = targetUserId
        )
    }

    private fun createAndReplace(
        isGroup: Boolean,
        currentUserId: Long,
        targetUserId: Long,
    ): Pair<Boolean, IVideoCallFloatTarget> {
        val lastTarget = lastCallTarget
        if (lastTarget?.currentUserId == currentUserId && lastTarget.targetUserId == targetUserId && lastTarget.isGroup == isGroup) {
            logDebug(TAG, "from VideoCallFloatTargetManager createAndReplace#Log:reused lastTarget")
            return false to lastTarget
        }
        val newTarget = VideoCallFloatTarget(
            currentUserId = currentUserId,
            targetUserId = targetUserId,
            isGroup = isGroup
        )
        logDebug(TAG, "from VideoCallFloatTargetManager createAndReplace#Log:create newTarget")
        lastTarget?.destroy()
        newTarget.dispatchRoomUser(
            personalCallback = { callUser ->
                lastMyCallUser = callUser
            },
            targetCallback = { callUser ->
                lastTargetCallUser = callUser
            }
        )
        this.lastCallTarget = newTarget
        return true to newTarget
    }

    override fun dispatchTargetChanges(
        newTarget: IVideoCallFloatTarget,
        onStateChange: (TargetChangeType) -> Unit
    ) {
        logDebug(
            TAG,
            "from VideoCallFloatTargetManager dispatchTargetChanges#Log:newTarget=${newTarget}" +
                    ",lastCallTarget=${lastCallTarget}"
        )
        if (lastCallTarget == null) {
            onStateChange(TargetChangeType.All)
            return
        }
        // 监听状态变化
        val changedStates = mutableSetOf<TargetChangeType>()

        newTarget.dispatchRoomUser(
            personalCallback = { newMyCallUser ->
                lastMyCallUser?.let { oldMyCallUser ->
                    if (oldMyCallUser.isSpeaking != newMyCallUser.isSpeaking) {
                        changedStates.add(TargetChangeType.MySpeaking)
                    }
                    if (oldMyCallUser.micStatus != newMyCallUser.micStatus) {
                        changedStates.add(TargetChangeType.MyMic)
                    }
                    if (oldMyCallUser.cameraStatus != newMyCallUser.cameraStatus) {
                        changedStates.add(TargetChangeType.MyCamera)
                    }
                    if (oldMyCallUser.netWorkStatus != newMyCallUser.netWorkStatus) {
                        changedStates.add(TargetChangeType.MyNetState)
                    }
                    if (oldMyCallUser.rtcUserId != newMyCallUser.rtcUserId) {
                        changedStates.add(TargetChangeType.MyJoinState)
                    }
                }
                // 更新 lastMyCallUser
                lastMyCallUser = newMyCallUser
            },
            targetCallback = { newTargetCallUser ->
                lastTargetCallUser?.let { oldTargetCallUser ->
                    if (oldTargetCallUser.isSpeaking != newTargetCallUser.isSpeaking) {
                        changedStates.add(TargetChangeType.TargetSpeaking)
                    }
                    if (oldTargetCallUser.micStatus != newTargetCallUser.micStatus) {
                        changedStates.add(TargetChangeType.TargetMic)
                    }
                    if (oldTargetCallUser.cameraStatus != newTargetCallUser.cameraStatus) {
                        changedStates.add(TargetChangeType.TargetCamera)
                    }
                    if (oldTargetCallUser.netWorkStatus != newTargetCallUser.netWorkStatus) {
                        changedStates.add(TargetChangeType.TargetNetState)
                    }
                    if (oldTargetCallUser.rtcUserId != newTargetCallUser.rtcUserId) {
                        changedStates.add(TargetChangeType.TargetJoinState)
                    }
                }
                // 更新 lastTargetCallUser
                lastTargetCallUser = newTargetCallUser
            }
        )

        logDebug(
            TAG,
            "from VideoCallFloatTargetManager dispatchTargetChanges#Log:changedStates=$changedStates"
        )
        // 触发状态变更回调
        changedStates.forEach { onStateChange(it) }
    }

    override fun destroyTarget() {
        logDebug(TAG, "from VideoCallFloatTargetManager dispatchTargetChanges#Log:destroyTarget")
        lastCallTarget?.destroy()
        lastCallTarget = null
        lastTargetCallUser = null
        lastMyCallUser = null
    }
}

