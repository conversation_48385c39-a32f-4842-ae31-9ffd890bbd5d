package com.interfun.buz.voicecall.common.interfaces

import android.content.Context
import androidx.activity.result.ActivityResultLauncher
import androidx.fragment.app.Fragment
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.common.R.string
import com.interfun.buz.common.bean.voicecall.CallEndType
import com.interfun.buz.common.utils.PermissionInterceptor
import com.interfun.buz.common.voicecall.GroupCallStater
import com.interfun.buz.common.voicecall.PrivateCallStater
import com.interfun.buz.common.widget.dialog.CommonAlertDialog

interface VoiceCallUI {
    val fragment: Fragment

    fun delayThenFinishActivity(delayTime:Long = 6*1000L, reason: @CallEndType Int)
    fun getAppSettingLauncher():ActivityResultLauncher<Unit>

    fun getBluetoothPermissionInterceptor(): PermissionInterceptor

    fun showBluetoothPermissionTipsDialog(context: Context, positiveCallback: ()->Unit) {
        CommonAlertDialog(
            context = context,
            title = string.chat_permission_tips_dialog_title.asString(),
            tips = string.bluetooth_access_tip.asString(),
            positiveText = string.settings.asString(),
            negativeText = string.cancel.asString(),
            positiveCallback = {
                positiveCallback.invoke()
                it.dismiss()
            },
            negativeCallback = {
                it.dismiss()
            }
        ).show()
    }

    fun getPrivateCallStart(): PrivateCallStater

    fun getGroupCallStart(): GroupCallStater
}