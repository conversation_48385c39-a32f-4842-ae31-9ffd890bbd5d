package com.interfun.buz.voicecall.manager

import android.Manifest
import android.content.Context
import android.telephony.TelephonyManager
import com.buz.idl.realtimecall.request.RequestInviteRealTimeCall
import com.buz.idl.realtimecall.request.RequestStartRealTimeCall
import com.buz.idl.realtimecall.service.BuzNetRealTimeCallServiceClient
import com.interfun.buz.base.ktx.isPermissionGranted
import com.interfun.buz.base.ktx.log
import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.bean.user.BuzUserRelationValue
import com.interfun.buz.common.ktx.getContactFirstName
import com.interfun.buz.common.ktx.isSuccess
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.net.withConfig
import com.interfun.buz.common.service.ChatService
import com.interfun.buz.common.service.ContactsService
import com.interfun.buz.common.widget.toast.BuzToast
import com.interfun.buz.voicecall.privatecall.bean.OnlineCallDetail
import com.lizhi.itnet.lthrift.service.ITResponse
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

object RealTimeCallManager {

    const val TAG = "RealTimeCallManager"

    private val client by lazy {
        BuzNetRealTimeCallServiceClient().withConfig()
    }

    private val managerScope = CoroutineScope(Dispatchers.IO)


    private val _channelId = MutableStateFlow<Long?>(null)
    val channelId: StateFlow<Long?> = _channelId

    private val _userList = MutableStateFlow<List<OnlineCallDetail>?>(null)
    val userList: Flow<List<OnlineCallDetail>?> = _userList

    private val hasCheckPhoneStatePermission get() = isPermissionGranted(Manifest.permission.READ_PHONE_STATE)



    private val chatService by lazy {
        routerServices<ChatService>().value
    }


    fun startRealTimeCall(userIdList: List<Long>, groupId: Long?, channelType: Int, callType: Int, traceId : String?,onResult: (Boolean, Long?) -> Unit) {
        managerScope.launch(Dispatchers.IO) {
            _userList.value = createPresetUserInfo(userIdList)
            val response = client.startRealTimeCall(
                RequestStartRealTimeCall(
                    userIdList = userIdList,
                    groupId = groupId,
                    channelType = channelType,
                    callType = callType,
                    traceId = traceId
                )
            )
            logResponse("startVoiceCall", response)

            if (response.isSuccess && response.data != null) {
                _channelId.value = response.data?.channelId
                log(TAG, "Voice call started successfully. Channel ID: ${_channelId.value}")
                log(TAG, "User Status :  ${response.data!!.callUserInfoList}")
                onResult(true, _channelId.value)
            } else {
                log(TAG, "Failed to start voice call. Error: ${response.code}")
                onResult(false, null)
            }
        }
    }

    fun inviteVoiceCall(userIdList: List<Long>, channelId: Long, onResult: (Boolean, List<Long>?) -> Unit) {
        managerScope.launch {
            val response = client.inviteRealTimeCall(
                RequestInviteRealTimeCall(
                userIdList = userIdList,
                channelId = channelId
            )
            )
            logResponse("inviteVoiceCall", response)

            if (response.isSuccess) {
                logDebug(TAG, "Invite voice call successful.")
                val rejectedUserId = response.data?.busyUserInfoList as? List<Long>
                onResult(true, rejectedUserId)
            } else {
                logInfo(TAG, "Failed to invite to voice call: ${response.msg}")
                onResult(false, null)
            }
        }
    }


    suspend fun createPresetUserInfo(userIdList: List<Long>): List<OnlineCallDetail> {
        val list = ArrayList<OnlineCallDetail>()
        UserSessionManager.userProfile?.let {
            OnlineCallDetail(
                0L,
                it.userId,
                it.portrait,
                it.userName
            ).also {
                list.add(it)
            }
        }
        userIdList.mapNotNull { getLocalUserInfo(it) }.also {
            list.addAll(it)
        }

        log(TAG, "createPresetUserInfo: $list")

        return list

    }

    private suspend fun getLocalUserInfo(userId: Long): OnlineCallDetail? {
        val service = routerServices<ContactsService>().value
        val localRelation = service?.getUserRelation(userId)
        if (localRelation != null) {
            return OnlineCallDetail(
                0L,
                localRelation.userId,
                localRelation.portrait,
                localRelation.getContactFirstName()
            )
        }
        return null

    }

    fun checkPermission(context: Context, targetId: Long, userIdList: List<Long>, channelType: Int, callType: Int, traceId: String?, onResult: (Boolean, Long?) -> Unit){
        val chatService = routerServices<ChatService>().value
        if (chatService?.isPlayingVoiceMsg() == true) {
            chatService.stopVoiceMessagePlayback()
            log(TAG, "Voice message playback stopped. Proceeding to check if user is a friend.")
        } else {
            log(TAG, "No voice message playback to stop. Proceeding to check if user is a friend.")
        }
        if (channelType == 1){
            // 1v1
            isUserMyFriend(context, targetId, onResult)
        }else{
            // group voice call
            checkCurrentPhoneState(context, userIdList, targetId, channelType, callType, traceId, onResult)
        }
    }


    fun isUserMyFriend(context: Context, userId: Long, onResult: (Boolean, Long?) -> Unit) {
        CoroutineScope(Dispatchers.IO).launch {
            val contactsService = routerServices<ContactsService>().value
            val userRelationInfo = contactsService?.getUserRelation(userId)
            val isFriend = userRelationInfo?.serverRelation == BuzUserRelationValue.FRIEND.value

            withContext(Dispatchers.Main) {
                if (!isFriend) {
                    BuzToast.showToast(context, "Please Add Friend First")
                    log(TAG, "The user is not your friend.")
                    onResult(false, null)
                } else {
                    log(TAG, "Target User Is Your Friend, Proceed to Check Phone State")
//                    checkCurrentPhoneState(context, userIdList, null, channelType, callType, traceId, onResult)
                }
            }
        }
    }


    fun checkCurrentPhoneState(
        context: Context,
        userIdList: List<Long>,
        groupId: Long?,
        channelType: Int,
        callType: Int,
        traceId: String?,
        onResult: (Boolean, Long?) -> Unit
    ) {
        CoroutineScope(Dispatchers.IO).launch {
            val telephonyManager =
                context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager
            val isCallActive = telephonyManager.callState != TelephonyManager.CALL_STATE_IDLE
            withContext(Dispatchers.Main) {
                if (isCallActive) {
                    BuzToast.showToast(
                        context,
                        "User is currently on a call. Please try again later."
                    )
                    log(TAG, "User is currently on a call.")
                    onResult(false, null)
                } else {
                    log(TAG, "User is not on call. Proceed to start real time call")
                    onResult(true, null)
                }
            }
        }
    }

    private fun logResponse(funcName: String, response: ITResponse<*>) {
        logDebug(TAG, "[$funcName] response: code: ${response.code}, msg: ${response.msg}, data: ${response.data}")
    }
}
