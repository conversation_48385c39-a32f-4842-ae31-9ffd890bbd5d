package com.interfun.buz.voicecall.common.view.floating

import android.util.Log
import android.view.TextureView
import android.view.ViewGroup
import androidx.annotation.StringRes
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.SideEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.key
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.blur
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import com.interfun.buz.base.ktx.LogLine
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.base.ktx.removeFromParent
import com.interfun.buz.base.ktx.visible
import com.interfun.buz.common.bean.voicecall.MicStatus
import com.interfun.buz.common.bean.voicecall.NetworkStatus
import com.interfun.buz.common.bean.voicecall.VoiceCallRoom
import com.interfun.buz.common.bean.voicecall.isCameraClose
import com.interfun.buz.common.bean.voicecall.isCameraOpen
import com.interfun.buz.compose.components.IconFontText
import com.interfun.buz.compose.components.PortraitImage
import com.interfun.buz.compose.ktx.asColor
import com.interfun.buz.compose.styles.TextStyles
import com.interfun.buz.voicecall.R
import com.interfun.buz.voicecall.common.viewmodel.CallUserInfo
import com.interfun.buz.voicecall.common.viewmodel.PipModeViewModel
import com.yibasan.lizhifm.audio.LiveTextureView
import com.yibasan.lizhifm.audio.MediaContentType
import kotlinx.collections.immutable.toPersistentMap

/**
 * 创建并配置TextureView
 * @param rtcUserId 用户ID
 * @param callUserInfo 用户信息
 * @param voiceCallRoom 语音通话房间
 * @param context 上下文
 * @param videoCallUsers 视频通话用户列表
 * @return 配置好的LiveTextureView
 */
private fun createAndConfigureTextureView(
    rtcUserId: Int,
    voiceCallRoom: VoiceCallRoom,
    context: android.content.Context,
    videoCallUsers: List<CallUserInfo>
): LiveTextureView {
    PipModeViewModel.printLog("为用户创建RTCView: rtcUserId=$rtcUserId")
    val textureView = voiceCallRoom.rtcVcResManager.createTextureView(context, "pipModeViewModel")

    // 根据用户类型绑定不同的视频源
    val userInfo = videoCallUsers.firstOrNull { it.callUser.rtcUserId == rtcUserId }
    if (userInfo?.isPersonal == true) {
        logInfo(
            "VideoCallView",
            "VideoCallView-绑定自己摄像头 rtcUserId=${rtcUserId}#true#${textureView.textureView.id}#${textureView.viewID}",
            logLine = LogLine.RTC_CALL
        )
        voiceCallRoom.rtcVcResManager.updateCameraView(textureView)
    } else {
        PipModeViewModel.printLog("VideoCallView-绑定远程摄像头 rtcUserId=${rtcUserId}#false#${textureView.textureView.id}#${textureView.viewID}")
        voiceCallRoom.rtcVcResManager.bindRemoteVideoView(rtcUserId.toLong(), MediaContentType.camera, textureView)
    }

    // 设置布局参数
    textureView.textureView.layoutParams = ViewGroup.LayoutParams(
        ViewGroup.LayoutParams.MATCH_PARENT,
        ViewGroup.LayoutParams.MATCH_PARENT
    )
    textureView.textureView.removeFromParent()

    return textureView
}

/**
 * Author: ChenYouSheng
 * Date: 2025/3/4
 * Email: <EMAIL>
 * Desc: 视频通话画中画界面
 */
@Composable
fun VideoCallMinimizeScreen(
    modifier: Modifier = Modifier,
    pipModeViewModel: PipModeViewModel,
    voiceCallRoom: VoiceCallRoom
) {
    val videoCallUsers = pipModeViewModel.callUsersState.collectAsState()
    SideEffect {
        PipModeViewModel.printLog("VideoCallMinimizeScreen:${videoCallUsers.value.map { it.callUser.rtcUserId to it.callUser.cameraStatus }}")
    }
    val context = LocalContext.current
    // 使用remember来保存当前的RTCViews状态
    val rtcViewsState = remember { mutableStateOf<Map<Int, LiveTextureView>>(emptyMap()) }
    logInfo("fmy","VideoCallMinimizeScreen root refresh")

    // 监听用户列表变化，管理RTCViews的生命周期
    DisposableEffect(key1 = videoCallUsers.value.map { it.callUser.rtcUserId }.joinToString()) {
        logInfo("fmy","VideoCallMinimizeScreen DisposableEffect ")
        val currentViews = rtcViewsState.value
        val currentUserIds = videoCallUsers.value.map { it.callUser.rtcUserId }.toSet()
        val existingUserIds = currentViews.keys.toSet()
        
        // 分析需要复用、回收和新建的Views
        val reusableViews = currentViews.filterKeys { it in currentUserIds }
        val viewsToRecycle = currentViews.filterKeys { it !in currentUserIds }
        val userIdsToCreate = currentUserIds - existingUserIds
        
        PipModeViewModel.printLog("RTCViews管理: 可复用=${reusableViews.keys}, 需回收=${viewsToRecycle.keys}, 需新建=$userIdsToCreate")
        
        // 回收不再需要的Views
        viewsToRecycle.forEach { (rtcUserId, view) ->
            PipModeViewModel.printLog("回收RTCView: rtcUserId=$rtcUserId")
            voiceCallRoom.rtcVcResManager.recycleLiveTextureView(view, true)
        }
        
        // 为新用户创建Views
        val newlyCreatedViews = userIdsToCreate.associateWith { rtcUserId ->
            createAndConfigureTextureView(rtcUserId, voiceCallRoom, context, videoCallUsers.value)
        }
        
        // 更新Views状态
        rtcViewsState.value = reusableViews + newlyCreatedViews
        
        onDispose {
            logInfo("fmy","VideoCallMinimizeScreen DisposableEffect onDispose")
            val currentUserIds = videoCallUsers.value.map { it.callUser.rtcUserId }.toSet()
            // 只清理不在currentUserIds中的Views
            val viewsToRemove = rtcViewsState.value.filterKeys { rtcUserId -> rtcUserId !in currentUserIds }
            viewsToRemove.forEach { (rtcUserId, view) ->
                PipModeViewModel.printLog("清理不需要的RTCView: rtcUserId=$rtcUserId")
                voiceCallRoom.rtcVcResManager.recycleLiveTextureView(view, true)
            }
            // 更新状态，保留仍在使用的Views
            rtcViewsState.value = rtcViewsState.value.filterKeys { rtcUserId -> rtcUserId in currentUserIds }
        }
    }

    Column(
        modifier = modifier
            .fillMaxSize()
            .background(colorResource(R.color.color_background_4_default))
    ) {
        val currentViews = rtcViewsState.value
        
        videoCallUsers.value.forEach { callUserInfo ->
            val rtcUserId = callUserInfo.callUser.rtcUserId
            
            // 获取或创建TextureView，统一处理逻辑
            val textureView = currentViews[rtcUserId] ?: run {
                // 兜底处理：说话人变化时展示的card突然要变成另一个
                PipModeViewModel.printLog("警告: 为用户 $rtcUserId 临时创建TextureView")
                val tempTextureView = createAndConfigureTextureView(rtcUserId, voiceCallRoom, context, videoCallUsers.value)
                // 更新状态以避免重复创建
                rtcViewsState.value = rtcViewsState.value + (rtcUserId to tempTextureView)
                tempTextureView
            }
            
            key(rtcUserId) {
                logInfo("fmy","VideoCallMinimizeScreen VideoCallCardView $rtcUserId")
                VideoCallCardView(
                    callUserInfo = callUserInfo,
                    pipModeViewModel = pipModeViewModel,
                    modifier = Modifier.weight(1f),
                    voiceCallRoom = voiceCallRoom,
                    textureView
                )
            }
        }
    }
}

@Composable
fun VideoCallCardView(
    callUserInfo: CallUserInfo,
    pipModeViewModel: PipModeViewModel,
    modifier: Modifier = Modifier,
    voiceCallRoom: VoiceCallRoom,
    textureViews: LiveTextureView
) {
    val isPersonal = callUserInfo.isPersonal
    val isConnecting = callUserInfo.isCalling
    val cameraStatus = callUserInfo.callUser.cameraStatus
    val micStatus = callUserInfo.callUser.micStatus
    val rtcUserId = callUserInfo.callUser.rtcUserId
    val portrait = callUserInfo.callUser.portrait
    val netWorkStatus = callUserInfo.callUser.netWorkStatus
    val isSpeaking = callUserInfo.callUser.isSpeaking

    val personJoined = pipModeViewModel.myJoinFlow.collectAsState().value
    val targetJoined = pipModeViewModel.userJoinFlow.collectAsState().value
    val showTextureView = cameraStatus.isCameraOpen() && if (isPersonal) personJoined else targetJoined

    val showPortrait = cameraStatus.isCameraClose() || showTextureView.not()

    SideEffect {
        PipModeViewModel.printLog("VideoCallCardView，rtcUserId=${rtcUserId}-$isPersonal,isCameraOpen=${cameraStatus.isCameraOpen()}，" +
                "showTextureView=${showTextureView},personJoined=${personJoined}")
    }

    // 默认背景色
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(
                color = if (isPersonal) colorResource(R.color.color_background_4_default)
                else colorResource(R.color.color_background_3_default)
            )
    ) {
        // 显示头像 开摄像头默认黑屏。关摄像头默认头像
        AnimatedVisibility(visible = showPortrait, enter = fadeIn(), exit = fadeOut()) {
            PortraitImageView(portrait = portrait)
        }

        // 视频画面
        AnimatedVisibility(visible = showTextureView, enter = fadeIn(), exit = fadeOut()) {
            VideoCallView(
                rtcUserId = rtcUserId,
                isPersonal = isPersonal,
                voiceCallRoom = voiceCallRoom,
                textureViews
            )
        }

        // 覆盖在画面上的元素
        OverlayUI(
            isSpeaking = isSpeaking,
            micStatus = micStatus,
            netWorkStatus = netWorkStatus,
            isConnecting = isConnecting,
            isPersonal = isPersonal
        )
    }
}

@Composable
private fun OverlayUI(
    isSpeaking: Boolean,
    micStatus: @MicStatus Int,
    netWorkStatus: @NetworkStatus Int,
    isConnecting: Boolean,
    isPersonal: Boolean
) {
    SideEffect {
        PipModeViewModel.printLog("OverlayUI")
    }
    BoxWithConstraints(
        modifier = Modifier.fillMaxSize()
    ) {

        val micIconSize = <EMAIL> * (14f / 125f)
        val micBgSize = maxWidth * (24f / 125f)
        val dp4 = maxWidth * (4f / 125f)
        val dp10 = maxWidth * (10f / 125f)
        val dp7 = maxWidth * (7f / 125f)


        if (isConnecting && isPersonal) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(R.color.alpha_black_30.asColor())
            ) {
                Text(
                    modifier = Modifier
                        .align(Alignment.Center)
                        .padding(start = dp10, bottom = dp7, end = dp4),
                    text = stringResource(R.string.waiting),
                    style = TextStyles.labelMedium(),
                    color = colorResource(R.color.color_text_white_primary),
                )
            }
        } else {
            Row(
                modifier = Modifier
                    .padding(end = dp4, bottom = dp4)
                    .align(Alignment.BottomEnd)
                    .wrapContentHeight()
            ) {

                // 弱网情况
                NetworkStateView(
                    visible = NetworkStatus.isBadNetwork(netWorkStatus), micBgSize = micBgSize
                )

                Spacer(modifier = Modifier.width(dp4))

                Box {
                    // 麦克风已关闭
                    MicStateView(
                        visible = MicStatus.isMicMuted(micStatus),
                        micBgSize = micBgSize,
                        micIconSize = micIconSize,
                        iconRes = R.string.ic_mic_close,
                        iconColor = colorResource(R.color.color_text_white_important)
                    )

                    // 说话中
                    MicStateView(
                        visible = MicStatus.isMicOpen(micStatus) && isSpeaking,
                        micBgSize = micBgSize,
                        micIconSize = micIconSize,
                        iconRes = R.string.ic_mic_open,
                        iconColor = colorResource(R.color.color_text_highlight_default)
                    )
                }
            }
        }
    }
}

@Composable
private fun PortraitImageView(portrait: String?) {
    SideEffect {
        PipModeViewModel.printLog("PortraitImageView:portrait=$portrait")
    }
    BoxWithConstraints(
        modifier = Modifier.fillMaxSize()
    ) {
        val imageSize = <EMAIL> * (75f / 125f)
        PortraitImage(
            modifier = Modifier
                .size(imageSize)
                .align(Alignment.Center), url = portrait
        )
    }
}

@Composable
private fun MicStateView(
    visible: Boolean,
    micBgSize: Dp,
    micIconSize: Dp,
    @StringRes iconRes: Int,
    iconColor: Color,
) {
    SideEffect {
        PipModeViewModel.printLog("MicStateView")
    }
    AnimatedVisibility(visible = visible, enter = fadeIn(), exit = fadeOut()) {
        Box(modifier = Modifier.size(micBgSize)) {
            Box(
                modifier = Modifier
                    .matchParentSize()
                    .clip(CircleShape)
                    .background(color = colorResource(R.color.alpha_black_20))
                    .blur(10.dp)
            )
            IconFontText(
                modifier = Modifier.align(Alignment.Center),
                iconRes = iconRes,
                iconSize = micIconSize,
                iconColor = iconColor
            )
        }
    }
}

@Composable
private fun RowScope.NetworkStateView(
    visible: Boolean, micBgSize: Dp
) {
    SideEffect {
        PipModeViewModel.printLog("NetworkStateView")
    }
    AnimatedVisibility(visible = visible, enter = fadeIn(), exit = fadeOut()) {
        Box(modifier = Modifier.size(micBgSize)) {
            Box(
                modifier = Modifier
                    .matchParentSize()
                    .clip(CircleShape)
                    .background(color = colorResource(R.color.alpha_black_20))
                    .blur(10.dp)
            )
            Icon(
                modifier = Modifier.align(Alignment.Center),
                painter = painterResource(id = R.drawable.ic_poor_network),
                contentDescription = "Poor Network Icon",
                tint = Color.Unspecified
            )
        }
    }
}


@Composable
private fun VideoCallView(
    rtcUserId: Int,
    isPersonal: Boolean,
    voiceCallRoom: VoiceCallRoom,
    liveTextureView: LiveTextureView
) {
    SideEffect {
//        PipModeViewModel.printLog("VideoCallView: rtcUserId=${rtcUserId}#$isPersonal#${liveTextureView.textureView.id}#${liveTextureView.viewID}")
    }
    val textureView = liveTextureView?.textureView ?: return
    textureView.visible()

    if (isPersonal) {
        voiceCallRoom.rtcVcResManager.updateCameraView(liveTextureView)
    }else{
        voiceCallRoom.rtcVcResManager.bindRemoteVideoView(rtcUserId.toLong(), MediaContentType.camera, liveTextureView)
    }
    key(rtcUserId, isPersonal, textureView.id) {
        logInfo("fmy","VideoCallView  $rtcUserId")

        AndroidView(factory = {
            logInfo("fmy","VideoCallView  AndroidView $rtcUserId  viewId= ${textureView.id}")

            logInfo(
                "VideoCallView",
                "VideoCallView-AndroidView-factory: rtcUserId=${rtcUserId}#$isPersonal#${liveTextureView.textureView.id}#${liveTextureView.viewID}",
                logLine = LogLine.RTC_CALL
            )
            PipModeViewModel.printLog("VideoCallView-AndroidView-factory: rtcUserId=${rtcUserId}#$isPersonal#${liveTextureView.textureView.id}#${liveTextureView.viewID}")
            textureView.removeFromParent()
            textureView
        }, modifier = Modifier.fillMaxSize(), update = {
            textureView.visible()
          // 避免sdk复用后修改了可见效果
            logInfo(
                "VideoCallView",
                "VideoCallView-AndroidView-update: rtcUserId=${rtcUserId}#$isPersonal#${liveTextureView.textureView.id}#${liveTextureView.viewID}",
                logLine = LogLine.RTC_CALL
            )
        })
    }
}



