package com.interfun.buz.voicecall.common.fragment

import android.Manifest
import android.content.Context
import android.os.Bundle
import androidx.core.view.marginEnd
import androidx.core.view.marginStart
import androidx.lifecycle.Observer
import androidx.lifecycle.lifecycleScope
import com.interfun.buz.base.ktx.*
import com.interfun.buz.base.utils.BusUtil
import com.interfun.buz.base.utils.VibratorUtil
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.base.BaseActivity
import com.interfun.buz.common.base.binding.BaseBindingFragment
import com.interfun.buz.common.bean.chat.OnlineChatJumpInfo
import com.interfun.buz.common.bean.chat.OnlineChatJumpType
import com.interfun.buz.common.bean.voicecall.*
import com.interfun.buz.common.bean.voicecall.MicStatus
import com.interfun.buz.common.constants.IncomingRealTimeCallSource
import com.interfun.buz.common.constants.JUMP_INFO
import com.interfun.buz.common.constants.RouterParamKey
import com.interfun.buz.common.eventbus.voicecall.CancelVoiceCallInviteEvent
import com.interfun.buz.common.helper.CameraPermissionHelper
import com.interfun.buz.common.helper.RecordVoicePermissionHelper
import com.interfun.buz.common.ktx.getContactFirstName
import com.interfun.buz.common.ktx.getDisplayName
import com.interfun.buz.common.manager.CallNotificationCache
import com.interfun.buz.common.manager.ChannelStatusManager
import com.interfun.buz.common.manager.chat.CallPendInfo
import com.interfun.buz.common.manager.chat.CallPendStatus
import com.interfun.buz.common.manager.chat.ChannelInviteManager
import com.interfun.buz.common.manager.chat.ChannelPendStatusManager
import com.interfun.buz.common.manager.voicecall.DoreRTCEnginManager
import com.interfun.buz.common.manager.voicecall.RoomVCResManager
import com.interfun.buz.common.manager.voicecall.VoiceCallPortal
import com.interfun.buz.common.service.RealTimeCallService
import com.interfun.buz.common.utils.*
import com.interfun.buz.common.widget.dialog.CommonAlertDialog
import com.interfun.buz.onair.helper.SeatBgPaletteHelper
import com.interfun.buz.onair.standard.*
import com.interfun.buz.voicecall.R
import com.interfun.buz.voicecall.common.block.TopTitleBarBlock
import com.interfun.buz.voicecall.databinding.VoicecallFragmentCallingPendBinding
import com.interfun.buz.voicecall.privatecall.viewmodel.VoiceCallingPendViewModel
import com.interfun.buz.voicecall.util.VoiceCallTracker
import com.yibasan.lizhifm.liveinteractive.CameraEventHandler
import com.yibasan.lizhifm.liveinteractive.CameraStopReason
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * @Desc
 * @Author:<EMAIL>
 * @Date: 2024/3/5
 */
class VoiceCallingPendFragment : BaseBindingFragment<VoicecallFragmentCallingPendBinding>(),CameraEventHandler {

    private val callingPendViewModel by activityViewModels<VoiceCallingPendViewModel>()
    private val onlineChatJumpInfo by lazy {
        arguments?.getParcelable<OnlineChatJumpInfo>(RouterParamKey.ChannelInvite.JUMP_INFO)
    }
    private val groupCallerUserId by lazy {
        arguments?.getLong(RouterParamKey.ChannelInvite.KEY_CALLER_USER_ID)
    }
    private val channelType get() = onlineChatJumpInfo?.channelType ?: 0
    private val callType get() =  onlineChatJumpInfo?.callType ?: 0
    private val isVideoChannel by lazy { CallType.isVideoCall(callType) }
    private val myCallRoomUser by lazy { createMyCallRoomInfo() }
    private val recordPermissionHelper = RecordVoicePermissionHelper(this)
    private val roomVCResManager:RoomVCResManager by lazy { RoomVCResManager() }
    private val cameraPermissionHelper = CameraPermissionHelper(this)
    private val checkCameraPermission get() =  isPermissionGranted(Manifest.permission.CAMERA)
    private val checkRecordPermission get() =  isPermissionGranted(Manifest.permission.RECORD_AUDIO)

    private val cancelVoiceCallObserver: Observer<CancelVoiceCallInviteEvent> = Observer {
        logInfo(
            TAG,
            "Subscribe the event for CancelVoiceCallInviteEvent, " + "current calling pend channelId = ${onlineChatJumpInfo?.channelId} and busEvent channelId = ${it.channelId}"
        )
        if (it.channelId == onlineChatJumpInfo?.channelId) {
            endPendAnswer(it.channelId)
            activity?.finish()
        }
    }

    companion object {
        const val TAG = "VoiceCallingPendFragment"
        fun newInstance(bundle: Bundle?): VoiceCallingPendFragment {
            return VoiceCallingPendFragment().apply {
                arguments = bundle
            }
        }
    }

    override fun initView() {
        super.initView()
        logInfo(TAG, "initView:onlineChatJumpInfo = $onlineChatJumpInfo ")
        onlineChatJumpInfo?.let {
            updateCallType()
            checkPermission()
            setClickListener()

            TopTitleBarBlock(
                this,
                binding.vcTitleContainer,
                it,
                null
            ).bind(this)

            // Update UI
            logInfo(TAG, "initView [channelId = ${it.channelId}, channelType = ${channelType}, callType = $callType]")

            if (onlineChatJumpInfo?.jumpType != OnlineChatJumpType.pendAnswerByHome) {
                if (onlineChatJumpInfo?.jumpType == OnlineChatJumpType.pendAnswer) {
                    if (ChannelInviteManager.containsInvite(it.channelId.toString()).not()) {
                        logError(TAG, "[channelId = ${it.channelId} ] voice call invite it doesn't exist anymore")
                        finishActivity()
                        return
                    }
                }
                ChannelPendStatusManager.changeStatus(
                    CallPendStatus.BEING_INVITED, CallPendInfo(
                        it.channelId ?: 0, channelType, it.targetId, useAppSoundEffect = true,
                        callType = it.callType
                    )
                )
                interceptOnBackPressed(fragment) {
                    suspendVoiceCallPendAnswerState()
                }
            }
            // 这里是为了解决后台点击通知栏进入通话界面，会重复出现应用内通知的问题。需注意不关闭震动和铃声
            ChannelInviteManager.dismiss(
                it.toString(), emitCancel = true, cancelVibratorAndRing = false
            )
            //无论是jumpType 为 pendAnswer 还是 pendAnswerByHome 都要取消邀请的notification显示
            sendCancelVoiceCallBroadcast(onlineChatJumpInfo)

            if(ChannelType.isLivePlaceType(channelType)){
                CommonTracker.onOnAirInviteExpose(
                    ChannelType.isPrivateChannel(onlineChatJumpInfo?.channelType!!),
                    (onlineChatJumpInfo?.targetId ?: 0).toString(),
                    (onlineChatJumpInfo?.channelId ?: 0).toString()
                )
            }

            if (it.jumpType == OnlineChatJumpType.pendAnswer && ChannelType.isVoiceCallType(channelType)) {
                routerServices<RealTimeCallService>().value?.onCallComeInPageView(
                    onlineChatJumpInfo?.channelId ?: 0,
                    channelType,
                    it.targetId,
                    callType,
                    IncomingRealTimeCallSource.CallKit
                )
            }

            if (onlineChatJumpInfo?.jumpType == OnlineChatJumpType.pendAnswerByHome) {
                binding.groupBottomButton.invisible()
                binding.btnJoin.visible()
                binding.btnJoin.setOnClickListener {
                    onPendingAnswerClicked()
                }
            }
        }

        binding.realTimeCallUserCard.initCard(
            user = myCallRoomUser,
            applyTextureView = isVideoChannel,
            cameraManager = roomVCResManager
        )
    }

    override fun initData() {
        super.initData()
        lifecycleScope.launch {
            ChannelStatusManager.convChannelInfoChangeList.collect{ changeInfoList->
                val targetId = onlineChatJumpInfo?.targetId
                //在首页主动加入预览期间,如果房间已经解散,则关闭页面
                if (targetId!=null &&
                    onlineChatJumpInfo?.jumpType == OnlineChatJumpType.pendAnswerByHome &&
                    onlineChatJumpInfo?.channelType==ChannelType.TYPE_VOICE_CALL_GROUP
                    ) {
                    if (!changeInfoList.map { it.convTargetId }.contains(targetId)){
                        logInfo(TAG,"close pendPreview by channel not found.")
                        activity?.finish()
                    }else{

                        val voiceCallInfo = ChannelStatusManager.getConvChannelInfo(targetId)
                        if (voiceCallInfo!=null&&voiceCallInfo.channelId<=0) {
                           logInfo(TAG,"close pendPreview by channel close.")
                            activity?.finish()
                        }
//                        if (null == voiceCallInfo){
//                            statusBinding.root.gone()
//                            return
//                        }
                    }
                }
            }

        }
        callingPendViewModel.apply {
            baseInfo.collectLatestIn(viewLifecycleOwner) {
                it ?: return@collectLatestIn
                if (it.groupInfoBean == null) {
                    it.userInfo?.let { userRelationInfo ->
                        val size = maxOf(90.dp, binding.portraitImageViewPending.width)
                        userRelationInfo.portrait?.let { portrait ->
                            val seatColor = SeatBgPaletteHelper.obtainColorFromRelation(portrait)
                            binding.clPendingView.setBackgroundColor(seatColor)
                            binding.viewBgMask.setBackgroundResource(R.color.color_overlay_black_medium)
                        }
                        binding.portraitImageViewPending.setPortrait(userRelationInfo.portrait, size)
                        updateUserNameView(userRelationInfo.getContactFirstName())
                        binding.tvPendingDesc.text = R.string.calling_you.asString()
                    }
                }

                it.groupInfoBean?.let { groupInfo ->
                    binding.portraitImageViewPending.setGroupInfoBean(groupInfo)
                    updateUserNameView(groupInfo.groupName ?: "")
                    val desc = if (onlineChatJumpInfo?.jumpType == OnlineChatJumpType.pendAnswerByHome) {
                        if (isVideoChannel) {
                            R.string.rtc_group_videocall.asString()
                        } else {
                            R.string.rtc_group_voicecall.asString()
                        }
                    } else {
                        val inviteUserName = it.userInfo?.getDisplayName()
                        if (isVideoChannel) {
                            getString(R.string.rtc_x_invite_you_to_join_videocall, inviteUserName)
                        } else {
                            getString(R.string.rtc_x_invite_you_to_join_voicecall, inviteUserName)
                        }
                    }
                    binding.tvPendingDesc.text = desc
                    binding.clPendingView.setBackgroundResource(R.color.color_background_3_default)
                    binding.viewBgMask.setBackgroundResource(R.drawable.voicecall_groupcall_background)
                }
            }
            micStatus.collectLatestIn(viewLifecycleOwner) { micStatus ->
                logDebug(TAG, "micStatus = $micStatus")
                handleMicStatus(micStatus)
            }
            cameraStatus.collectLatestIn(viewLifecycleOwner) { cameraStatus ->
                logDebug(TAG, "cameraStatus = $cameraStatus")
                handleCameraStatus(cameraStatus)
            }
            cameraState.collectLatestIn(viewLifecycleOwner) { cameraState ->
                logDebug(TAG, "cameraState = $cameraState")
                roomVCResManager.switchCamera(CameraState.isFront(cameraState))
                binding.realTimeCallUserCard.switchCameraAnim()
            }
        }

        VoiceCallPortal.rejectResult.collectIn(viewLifecycleOwner) {
            logInfo(TAG, "hangUpResult collectIn value is $it")
            val rejectChannelId = it.second
            if (rejectChannelId != onlineChatJumpInfo?.channelId) {
                return@collectIn
            }
            val rCode = it.first
            if (rCode == 0 || rCode == 4004) {
                onlineChatJumpInfo?.channelId?.let { channelId ->
                    endPendAnswer(channelId)
                }
                activity?.finish()
            }
        }

        callingPendViewModel.initJumpInfo(onlineChatJumpInfo, groupCallerUserId)
        BusUtil.observeForever(cancelVoiceCallObserver)
    }


    private fun checkPermission() {
        if (!checkRecordPermission) {
            recordPermissionHelper.apply {
                setOnPermissionGrantedListener { }
                setOnPermissionDenyListener {
                    logInfo(TAG, "initView: OnRecordPermissionDenyListener")
                    activity?.let { showRecordPermissionTipsDialog(it) }
                }
                requestRecordPermission(false)
            }
        }
        if (isVideoChannel && !checkCameraPermission) {
            cameraPermissionHelper.apply {
                setOnPermissionGrantedListener {
                    callingPendViewModel.updateCameraStatus(CameraStatus.OPEN)
                }
                setOnPermissionDenyListener {
                    logInfo(TAG, "initView: OnCameraPermissionDenyListener")
                    activity?.let { showCameraPermissionTipsDialog(it) }
                }
                requestCameraPermission(false)
            }
        }
    }

    private fun setClickListener() {
        binding.iftvPendingAnswer.click { onPendingAnswerClicked() }
        binding.iftvPendingReject.click { onPendingRejectClicked() }
        binding.iftvVideo.click { onCameraClicked() }
        binding.iftvMic.click { onMicClicked() }
    }

    private fun onPendingAnswerClicked() {
        VibratorUtil.vibrator(TAG)
        logInfo(TAG, "setClickListener: pendingAnswer clicked ==>info = $onlineChatJumpInfo")
        val conflictState = handleAcceptClickCheck()
        when (conflictState) {
            CallConflictState.NO_CONFLICT -> {
                answer()
            }

            CallConflictState.IS_LIVE_PLACE_CHANNEL -> {
                handleOnAirAnswer()
                CommonTracker.onClickAnswerOnAirInvite(
                    ChannelType.isPrivateChannel(channelType),
                    (onlineChatJumpInfo?.channelId ?: 0).toString(),
                    false
                )
            }

            CallConflictState.NO_RECORD_PERMISSION -> {
                recordPermissionHelper.apply {
                    setOnPermissionGrantedListener { onPendingAnswerClicked() }
                    setOnPermissionDenyListener { showAskRecordPermission(fragment.requireContext()) }
                    requestRecordPermission(false)
                }
                VoiceCallNotificationTracker.onClickAcceptCallResult(
                    channelId = onlineChatJumpInfo?.channelId ?: 0,
                    channelType = channelType,
                    callUserId = groupCallerUserId ?: 0L,
                    callType = callType,
                    success = false,
                    failReason = "no_record_permission"
                )
            }

            CallConflictState.NO_CAMERA_PERMISSION -> {
                cameraPermissionHelper.apply {
                    setOnPermissionGrantedListener { onPendingAnswerClicked() }
                    setOnPermissionDenyListener { showAskCameraPermissionDialog(fragment.requireContext()) }
                    requestCameraPermission(false)
                }
                VoiceCallNotificationTracker.onClickAcceptCallResult(
                    channelId = onlineChatJumpInfo?.channelId ?: 0,
                    channelType = channelType,
                    callType = callType,
                    callUserId = groupCallerUserId ?: 0L,
                    success = false,
                    failReason = "no_camera_permission"
                )
            }

            CallConflictState.NETWORK_ERROR->{
                VoiceCallNotificationTracker.onClickAcceptCallResult(
                    channelId = onlineChatJumpInfo?.channelId ?: 0,
                    channelType = channelType,
                    callType = callType,
                    callUserId = groupCallerUserId ?: 0L,
                    success = false,
                    failReason = "network_error"
                )
            }

            else -> {}
        }
    }

    private fun handleAcceptClickCheck(): CallConflictState {
        logInfo(TAG, "setClickListener: pendingAnswer clicked ==>info = $onlineChatJumpInfo")
        if (isNetworkAvailable.not()) {
            return CallConflictState.NETWORK_ERROR
        }

        if (ChannelType.isLivePlaceType(onlineChatJumpInfo?.channelType ?: 0)) {
            return CallConflictState.IS_LIVE_PLACE_CHANNEL
        }

        if (ChannelType.isVoiceCallType(channelType).not()) {
            return CallConflictState.IS_NOT_RTC_CHANNEL
        }

        if (!checkRecordPermission) {
            return CallConflictState.NO_RECORD_PERMISSION
        }

        if (isVideoChannel && !checkCameraPermission) {
            return CallConflictState.NO_CAMERA_PERMISSION
        }

        return CallConflictState.NO_CONFLICT
    }

    private fun onPendingRejectClicked() {
        VibratorUtil.vibrator(TAG)
        reject()
        if (ChannelType.isLivePlaceType(channelType)) {
            CommonTracker.onClickAnswerOnAirInvite(
                ChannelType.isPrivateChannel(channelType),
                (onlineChatJumpInfo?.channelId ?: 0).toString(),
                false
            )
        }
    }

    private fun onCameraClicked() {
        VibratorUtil.vibrator(TAG)
        val cameraStatus = CameraStatus.toggle(callingPendViewModel.cameraStatus.value)
        if (CameraStatus.isOpen(cameraStatus) && !checkCameraPermission) {
            cameraPermissionHelper.apply {
                setOnPermissionGrantedListener {
                    logInfo(TAG, "onCameraClicked: Permission Granted")
                    updateCameraStatus(cameraStatus)
                }
                setOnPermissionDenyListener {
                    logWarn(TAG, "onCameraClicked: Permission Denied")
                    showAskCameraPermissionDialog(fragment.requireContext())
                }
                logInfo(TAG, "onCameraClicked: Requesting Permission...")
                requestCameraPermission(false)
            }
            return
        }
        updateCameraStatus(cameraStatus)
    }

    private fun onMicClicked() {
        VibratorUtil.vibrator(TAG)
        val micStatus = MicStatus.toggle(callingPendViewModel.micStatus.value)
        if (MicStatus.isMicOpen(micStatus) && !checkRecordPermission) {
            recordPermissionHelper.apply {
                setOnPermissionGrantedListener {
                    logInfo(TAG, "onMicClicked: Permission Granted")
                    updateMicStatus(micStatus)
                }
                setOnPermissionDenyListener {
                    logWarn(TAG, "onMicClicked: Permission Denied")
                    showAskRecordPermission(fragment.requireContext())
                }
                logInfo(TAG, "onMicClicked: Requesting Permission...")
                requestRecordPermission(false)
            }
            return
        }
        updateMicStatus(micStatus)
    }

    private fun updateCameraStatus(cameraStatus: @CameraStatus Int) {
        callingPendViewModel.updateCameraStatus(cameraStatus)
    }

    private fun updateMicStatus(micStatus: @MicStatus Int) {
        callingPendViewModel.updateMicStatus(micStatus)
    }

    private fun handleCameraStatus(cameraStatus: @CameraStatus Int) {
        binding.iftvVideo.apply {
            when (cameraStatus) {
                CameraStatus.OPEN -> {
                    text = R.string.ic_video.asString()
                    setBackgroundResource(R.color.alpha_white_20)
                    openCamera()
                }

                CameraStatus.CLOSE -> {
                    text = R.string.ic_video_off.asString()
                    setBackgroundResource(R.color.alpha_white_10)
                    closeCamera()
                }
            }
        }
    }

    private fun handleMicStatus(micStatus: @MicStatus Int) {
        binding.iftvMic.apply {
            when (micStatus) {
                MicStatus.OPEN -> {
                    text = R.string.ic_mic_open.asString()
                    setBackgroundResource(R.color.alpha_white_20)
                }

                MicStatus.CLOSE -> {
                    text = R.string.ic_mic_close.asString()
                    setBackgroundResource(R.color.alpha_white_10)
                }
            }
        }
    }

    private fun updateCallType() {
        if (isVideoChannel) {
            binding.groupBottomVideoButton.visible()
            if (checkCameraPermission) {
                logDebug(TAG, "updateCallType: open camera")
                callingPendViewModel.updateCameraStatus(CameraStatus.OPEN)
                return
            }
        } else {
            binding.groupBottomVideoButton.invisible()
        }
    }

    private fun openCamera() {
        binding.vcTitleContainer.iftvSwitchCamera.visible()
        binding.realTimeCallUserCard.startCameraCaptureNew(CameraState.isFront(callingPendViewModel.cameraState.value), TAG,roomVCResManager)
        roomVCResManager.addCameraEventHandler(this)
        binding.realTimeCallUserCard.visible()
        binding.portraitImageViewPending.invisible()
        binding.viewCameraMask.visible()
        binding.viewBgMask.gone()
    }

    private fun closeCamera() {
        roomVCResManager.removeCameraEventHandler(this)
        binding.vcTitleContainer.iftvSwitchCamera.gone()
        binding.portraitImageViewPending.visible()
        binding.realTimeCallUserCard.gone()
        binding.realTimeCallUserCard.stopCameraCapture(from = "VoiceCallingPendFragment closeCamera",roomVCResManager)
        binding.viewCameraMask.gone()
        binding.viewBgMask.visible()
    }

    private fun answer() {
        this.viewLifecycleScope.launch(Dispatchers.IO) {
            callingPendViewModel.wtAnswerStateFlow.emit(callingPendViewModel.realTimeCallPendState)
            ChannelPendStatusManager.changeStatus(CallPendStatus.ANSWER)
            onlineChatJumpInfo?.let {
                endPendAnswer(it.channelId ?: 0)
                VoiceCallTracker.onClickPendAnswerEvent(
                    channelId = it.channelId ?: 0,
                    channelType = channelType,
                    callType = callType,
                    isAnswer = true,
                    source = IncomingRealTimeCallSource.CallKit
                )

                VoiceCallNotificationTracker.onClickAcceptCallResult(
                    channelId = it.channelId ?: 0,
                    channelType = channelType,
                    callType = callType,
                    callUserId = groupCallerUserId ?: 0L,
                    success = true
                )
            }
        }
    }

    private fun reject() {
        ChannelPendStatusManager.changeStatus(CallPendStatus.DECLINE)
        this.viewLifecycleScope.launch(Dispatchers.Main) {
            callingPendViewModel.rejectCall(
                onlineChatJumpInfo?.channelId,
                onlineChatJumpInfo?.channelType ?: 0
            )
            if (ChannelType.isLivePlaceType(channelType)){
                finishActivity()
            }
        }
        onlineChatJumpInfo?.let {
            endPendAnswer(it.channelId ?: 0)
            VoiceCallTracker.onClickPendAnswerEvent(
                channelId = it.channelId ?: 0,
                channelType = channelType,
                callType = callType,
                isAnswer = false,
                source = IncomingRealTimeCallSource.CallKit
            )
        }
    }

    private fun handleOnAirAnswer() {
        logInfo(TAG, "handleOnAirAnswer: ")

        if (!checkRecordPermission) {
            recordPermissionHelper.setOnPermissionGrantedListener { }
            recordPermissionHelper.setOnPermissionDenyListener {
                logInfo(TAG, "handleOnAirAnswer: OnPermissionDenyListener")
                activity?.let { showRecordPermissionTipsDialog(it) }
            }
            recordPermissionHelper.requestRecordPermission(false)
            return
        }

        val onAirService = routerServices<IGlobalOnAirController>().value ?: return
        if (channelType == 0) return
        val channelId = onlineChatJumpInfo?.channelId ?: return
        val livePlaceType =
            if (channelType == ChannelType.TYPE_LIVE_PLACE_PRIVATE) LivePlaceType.PRIVATE else LivePlaceType.GROUP
        val roomParam = RoomParam(
            livePlaceType,
            ActivityReason.JOIN_ROOM,
            onlineChatJumpInfo!!.targetId,
            true,
            source = LivePlaceSource.SOURCE_INVITE_NOTIFY,
           joinROOMParam =  JoinRoomParam(channelId, 1, 1)
        )
        (activity as? BaseActivity)?.let {
            onAirService.enterOnAir(roomParam, it, object : EnterRetCallback() {
                override fun onResult(result: JoinRet, param: RoomParam) {
                    super.onResult(result, param)
                    logInfo(TAG, "onResult: $result")
                    if (result == JoinRet.JoinSuccess) {
                        activity?.finish()
                        answer()
                    }
                }
            })
        }
    }

    private fun suspendVoiceCallPendAnswerState() {
        val channelType = onlineChatJumpInfo?.channelType?:return
        if (ChannelType.isLivePlaceType(channelType)){
           reject()
        }

        if (ChannelType.isVoiceCallType(channelType)) {
            onlineChatJumpInfo?.channelId?.let {
                val voiceCallInvite = ChannelInviteManager.getVoiceCallInvite(it.toString())
                voiceCallInvite?.let { resetInviteInfo ->
                    ChannelInviteManager.resendInviteInfo(resetInviteInfo)
                }
            }
            activity?.finish()
        }
    }

    /**
     * 布局使用android:gravity="center" & android:ellipsize="end" 时会出现布局异常问题，手动调整
     */
    private fun updateUserNameView(name: String) {
        val screenWidth = requireContext.screenWidthReal
        val availableWidth =
            screenWidth - binding.tvPendingGroupName.marginStart - binding.tvPendingGroupName.marginEnd

        val strWidth = binding.tvPendingGroupName.paint.measureText(name)
        val eachWordWidth = strWidth / name.length
        val maxCharsPerLine = (availableWidth / eachWordWidth).toInt()
        val maxCharsForTwoLines = maxCharsPerLine * 2

        val exceedsTwoLines = name.length > maxCharsForTwoLines
        val endIndex = if (exceedsTwoLines) maxCharsForTwoLines else name.length
        val truncatedName = if (exceedsTwoLines) name.substring(0, endIndex) + "..." else name

        binding.tvPendingGroupName.text = truncatedName
        binding.tvPendingGroupName.maxLines = 2
    }

    /**
     * send voice call broadcast to dismiss notification
     */
    private fun sendCancelVoiceCallBroadcast(onlineChatJumpInfo: OnlineChatJumpInfo?) {
        onlineChatJumpInfo?.channelId?.let { channelId ->
            val voiceCallNotifiId = CallNotificationCache.getVoiceCallNotifiIdByChannelId(channelId)
            if (voiceCallNotifiId != null) {
                NotificationUtil.cancelVoiceCallNotificationAndUpdatePendStatus(
                    voiceCallNotifiId, channelType, stopRingtone = false
                )
            }
        }
    }


    override fun onDestroyView() {
        super.onDestroyView()
        BusUtil.removeObserver(cancelVoiceCallObserver)
    }

    private fun endPendAnswer(channelId: Long) {
        ChannelPendStatusManager.changeStatus(CallPendStatus.IDLE)
        ChannelInviteManager.removeInviteMapByChannelId(channelId.toString())
    }

    private fun showRecordPermissionTipsDialog(context: Context) {
        CommonAlertDialog(
            context = context,
            title = com.interfun.buz.common.R.string.chat_permission_tips_dialog_title.asString(),
            tips = com.interfun.buz.common.R.string.chat_record_permission_denied_dialog_text.asString(),
            positiveText = com.interfun.buz.common.R.string.settings.asString(),
            negativeText = com.interfun.buz.common.R.string.cancel.asString(),
            positiveCallback = {
                recordPermissionHelper.setOnPermissionDenyListener {}
                recordPermissionHelper.setOnPermissionGrantedListener { }
                recordPermissionHelper.requestRecordPermission()
                it.dismiss()
            },
            negativeCallback = {
                it.dismiss()
            },
            canceledOnTouchOutside = false,
            cancelable = false
        ).show()
    }

    private fun showCameraPermissionTipsDialog(context: Context) {
        CommonAlertDialog(
            context = context,
            title = com.interfun.buz.common.R.string.chat_permission_tips_dialog_title.asString(),
            tips = com.interfun.buz.common.R.string.chat_record_permission_denied_dialog_text.asString(),
            positiveText = com.interfun.buz.common.R.string.settings.asString(),
            negativeText = com.interfun.buz.common.R.string.cancel.asString(),
            positiveCallback = {
                cameraPermissionHelper.apply {
                    setOnPermissionDenyListener {}
                    setOnPermissionGrantedListener {
                        callingPendViewModel.updateCameraStatus(CameraStatus.OPEN)
                    }
                    requestCameraPermission()
                }
                it.dismiss()
            },
            negativeCallback = {
                it.dismiss()
            },
            canceledOnTouchOutside = false,
            cancelable = false
        ).show()
    }

    override fun onDestroy() {
        super.onDestroy()
        roomVCResManager.removeCameraEventHandler(this)
        obtainNullableBinding()?.realTimeCallUserCard?.releaseCamera(roomVCResManager)
        if (ChannelPendStatusManager.statusFlow.value.first != CallPendStatus.ANSWER) {
            roomVCResManager.stopCameraCapture(from = "VoiceCallingPendFragment onDestroy")
        }
        roomVCResManager.manualClearRes()
    }

    override fun onCameraCaptureStarted() {
        binding.realTimeCallUserCard.cameraCaptureStartedReact()
    }

    override fun onCameraDeviceChanged(isFront: Boolean) {
        binding.realTimeCallUserCard.cameraDeviceChangedReact()
    }

    override fun onCameraCaptureStopped(reason: CameraStopReason?) {
        binding.realTimeCallUserCard.cameraCaptureStoppedReact(reason)
    }

}