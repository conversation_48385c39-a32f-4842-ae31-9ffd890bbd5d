package com.interfun.buz.voicecall.common.model

import com.interfun.buz.common.bean.voicecall.CallStatus
import com.interfun.buz.common.bean.voicecall.MicStatus
import com.interfun.buz.common.bean.voicecall.CameraStatus
import com.interfun.buz.common.bean.voicecall.CallPayloadType

/**
 * 文件名：OnlineChatUser
 * 作用：语音通话列表的用户信息实体类
 * 作者：huangtianhao
 * 创建日期：2022/8/30
 */
data class RealTimeCallUser(
    val channelUserId: Long? = null,
    val userId: Long? = null,
    val portrait: String? = "",
    val userName: String? = "",
    val micStatus: @MicStatus Int = MicStatus.OPEN,
    val cameraStatus: @CameraStatus Int = CameraStatus.CLOSE,
    var voiceStatus: @CallStatus Int = CallStatus.CALLING,
    val isSpeaking: Boolean = false,
    val poorNetwork: Boolean = false,
    var rgbColor: Int? = null
)