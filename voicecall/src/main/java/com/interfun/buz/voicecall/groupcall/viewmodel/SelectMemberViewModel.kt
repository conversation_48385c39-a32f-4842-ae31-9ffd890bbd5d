package com.interfun.buz.voicecall.groupcall.viewmodel

import android.view.Gravity
import android.widget.Toast
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.buz.idl.group.bean.GroupMember
import com.buz.idl.group.request.RequestGetGroupMembers
import com.buz.idl.group.service.BuzNetGroupServiceClient
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.bean.voicecall.CallRoomUser
import com.interfun.buz.common.bean.voicecall.CallStatus
import com.interfun.buz.common.bean.voicecall.CallType
import com.interfun.buz.common.bean.voicecall.convert2RTCUser
import com.interfun.buz.common.database.entity.chat.isBigGroup
import com.interfun.buz.common.ktx.getDisplayNameWithLocal
import com.interfun.buz.common.ktx.getLongDefault
import com.interfun.buz.common.ktx.isMe
import com.interfun.buz.common.ktx.isSuccess
import com.interfun.buz.common.manager.AppConfigRequestManager.groupCallMaxMemberNum
import com.interfun.buz.common.manager.cache.group.GroupInfoCacheManager
import com.interfun.buz.common.net.withConfig
import com.interfun.buz.common.utils.PromptUtil
import com.interfun.buz.voicecall.R
import com.interfun.buz.voicecall.groupcall.constants.CheckBoxUIStatus
import com.yibasan.lizhifm.sdk.platformtools.ResUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock

/**
 * <AUTHOR>
 * @date 2024/2/6
 * @desc
 */
open class SelectMemberViewModel : ViewModel() {

    companion object {
        const val TAG = "GroupCall - SelectMemberViewModel"
    }

    /**
     * group members list with status
     *
     * Status: NONE(0), UNSELECTED(1), SELECTED(2), SELECTED_DISABLE(3)
     */
    // var groupMemberList: MutableList<GroupMemberWithCheckboxStatus> = mutableListOf()
    @JvmInline
    value class Index(val value: Int)

    var groupMemberUpdateLiveData = MutableLiveData<Index>()

    val pageState = MutableStateFlow<PageState>(PageState.Loading)

    private val groupMemberList:MutableList<GroupMemberWithCheckboxStatus> = mutableListOf()

    private val _selectedMemberFlow = MutableStateFlow<List<GroupMemberWithCheckboxStatus>>(emptyList())
    val selectedMemberFlow: StateFlow<List<GroupMemberWithCheckboxStatus>> = _selectedMemberFlow

    private val _insertMemberFlow = MutableStateFlow<Boolean>(false)
    val insertMemberFlow = _insertMemberFlow.asStateFlow()

    private val defaultMaxMemberCount = 12
    private var groupId: Long? = null
    private val groupService by lazy { BuzNetGroupServiceClient().withConfig() }

    private var inCallingUsers: LongArray = longArrayOf()

    private val mutex = Mutex()

    sealed class PageState {
        data object Loading : PageState()
        data class Success(val data: List<GroupMemberWithCheckboxStatus>) : PageState()
        data class SearchResult(val keyword: String, val data: List<GroupMemberWithCheckboxStatus>) : PageState()
        data object Error : PageState()
    }


    /**
     * Get group member list
     */
    fun requestGroupMember(cacheMembers: List<GroupMember>? = null) {
        logDebug(TAG, "requestGroupMember")

        viewModelScope.launch(Dispatchers.IO) {
            mutex.withLock {
                logDebug(TAG, "requestGroupMember mutex")

                if(pageState.value !is PageState.Success){
                    // 非刷新的场景，显示loading
                    pageState.value = PageState.Loading
                }

                val isBigGroup = GroupInfoCacheManager.getGroupInfoBeanByIdSync(groupId?:0L)?.isBigGroup == true
                val queryType = if (isBigGroup) 1 else 0

                if(cacheMembers?.isNotEmpty() == true){
                    logInfo(TAG, "requestGroupMember cache")
                    onGetMembersSuccess(cacheMembers)
                }else {
                    // Fetch the group member details from server
                    val resp = groupService.getGroupMembers(
                        RequestGetGroupMembers(
                            getGroupId() ?: 0L,
                            queryType,
                            null
                        )
                    )
                    PromptUtil.parse(resp.data?.prompt)

                    if (resp.isSuccess) {
                        onGetMembersSuccess(resp.data?.groupMemberList ?: emptyList())
                    } else {
                        logError(TAG, "requestGroupMember fail")
                        if (pageState.value !is PageState.Success) {
                            // 非刷新的场景，显示error
                            pageState.value = PageState.Error
                        }
                    }
                }
            }
        }
    }

    private fun onGetMembersSuccess(result: List<GroupMember>) {
        val previousList = groupMemberList

        val resultList = result.map {
            val userId = it.userInfo.userId

            // If user is me, set to selected disable and put in front
            if (userId.isMe()) {
                GroupMemberWithCheckboxStatus(
                    member = it,
                    status = CheckBoxUIStatus.SELECTED_DISABLE
                )

            } else if (userId != null && userId in inCallingUsers) {
                // If the user is already in call, set to unselected disable\
                GroupMemberWithCheckboxStatus(
                    member = it,
                    status = CheckBoxUIStatus.SELECTED_DISABLE
                )
            } else {
                GroupMemberWithCheckboxStatus(
                    member = it,
                    status = previousList.find { it.member.userInfo.userId == userId }?.status
                        ?: CheckBoxUIStatus.UNSELECTED
                )
            }
        }.sortedBy { !it.member.userInfo.userId.isMe() }

        groupMemberList.clear()
        groupMemberList.addAll(resultList)
        updateSelectedMembers()
        pageState.value = PageState.Success(groupMemberList)
    }

    fun setGroupId(groupId: Long, cacheMembers: List<GroupMember>?) {
        this.groupId = groupId
        requestGroupMember(cacheMembers)
    }

    private fun getGroupId(): Long? {
        return this.groupId
    }

    /**
     * Total member is selected (including self + 1)
     */
    fun getSelectedMemberCount(): Int {
        return groupMemberList.filter {
            it.status == CheckBoxUIStatus.SELECTED || it.status == CheckBoxUIStatus.SELECTED_DISABLE
        }.size
    }

    /**
     * Get selected user id list
     *
     * filter out the selected user list that is new selected only
     */
    fun getNewSelectedUserIdList(): List<Long> {
        val selectUserList = groupMemberList.filter {
            it.status == CheckBoxUIStatus.SELECTED
        }
        return selectUserList.map {
            it.member.userInfo.userId.getLongDefault()
        }.filter {
            it > 0L
        }
    }

    fun getNewSelectedRoomUsers(): List<CallRoomUser> {
        return groupMemberList.filter {
            it.status == CheckBoxUIStatus.SELECTED && it.member.userInfo.userId.isNotNull()
        }.map { it.member.convert2RTCUser(CallStatus.BE_CALLED) }
    }

    /**
     * Get maximum number of member can be called
     */
    open fun getMaxGroupMemberCount(): Int {
        val configMaxNumber = groupCallMaxMemberNum
        return if (configMaxNumber > 0) {
            configMaxNumber
        } else {
            defaultMaxMemberCount
        }.also {
            logInfo(TAG, "getMaxGroupMemberCount: $it")
        }
    }


    fun initInCallingUsers(inCallingUsers: LongArray) {
        this.inCallingUsers = inCallingUsers
    }

    /**
     * If exceed maximum call member count, toast warning message
     */
    open fun toastExceedMaxMemberCount(callType: @CallType Int) {
        // Create toast view design
        val message =  ResUtil.getString(R.string.call_supports_maximum_tips,getMaxGroupMemberCount())

        toastIconFontMsg(
            message = message,
            textColor = R.color.text_white_default.asColor(),
            iconFont = R.string.ic_warning_solid.asString(),
            iconFontColor = R.color.text_white_important.asColor(),
            gravity = Gravity.CENTER,
            duration = Toast.LENGTH_SHORT,
            style = IconToastStyle.ICON_TOP_TEXT_BOTTOM
        )
    }

    /**
     * Change checkbox status when clicked
     */
    fun updateMemberSelected(user: GroupMemberWithCheckboxStatus, callType: @CallType Int): Boolean {
        val state = user.status

        // Check whether the member is selected or not
        val selected = state == CheckBoxUIStatus.SELECTED

        // When the selection exceeds the maximum number of member, toast message
        if (!selected && getSelectedMemberCount() >= getMaxGroupMemberCount()) {
            toastExceedMaxMemberCount(callType)
            return false
        }

        if (state == CheckBoxUIStatus.SELECTED_DISABLE || state == CheckBoxUIStatus.UNSELECTED_DISABLE || state == CheckBoxUIStatus.NONE) {
            return false
        }

        val index = groupMemberList.indexOf(user)
        user.status = if (selected) {
            CheckBoxUIStatus.UNSELECTED
        } else {
            CheckBoxUIStatus.SELECTED
        }
        user.updateTime = System.currentTimeMillis()

        if (getSelectedMemberCount() >= getMaxGroupMemberCount()) {
            groupMemberList.let { memberList ->
                memberList.forEach { member ->
                    if (member.status == CheckBoxUIStatus.UNSELECTED) {
                        member.status = CheckBoxUIStatus.UNSELECTED_DISABLE
                    }
                }
            }
        } else {
            groupMemberList.let { memberList ->
                memberList.forEach { member ->
                    if (member.status == CheckBoxUIStatus.UNSELECTED_DISABLE) {
                        member.status = CheckBoxUIStatus.UNSELECTED
                    }
                }
            }
        }

        updateSelectedMembers()

        if (index >= 0) {
            groupMemberUpdateLiveData.postValue(Index(index))
        }
        return true
    }

    private fun updateSelectedMembers() {
        val previousList = _selectedMemberFlow.value
        val newList = groupMemberList.filter {
            it.status == CheckBoxUIStatus.SELECTED ||
                    it.status == CheckBoxUIStatus.SELECTED_DISABLE
        }.sortedBy { it.updateTime }
        _selectedMemberFlow.value = newList

        _insertMemberFlow.value= newList.size > previousList.size && previousList.isNotEmpty()
    }

    inner class GroupMemberWithCheckboxStatus(
        var member: GroupMember,
        var status: Int,
        var updateTime: Long = System.currentTimeMillis()
    )

    fun searchMember(keyword: String) {
        viewModelScope.launch(Dispatchers.Default) {
            val result = groupMemberList.filter {
                it.member.userInfo.getDisplayNameWithLocal().contains(keyword, ignoreCase = true)
            }
            pageState.value = PageState.SearchResult(keyword = keyword, data = result)
        }
    }

    fun clearSearch() {
        val original = pageState.value
        if (original is PageState.SearchResult) {
            pageState.value = PageState.Success(groupMemberList)
        }
    }


}