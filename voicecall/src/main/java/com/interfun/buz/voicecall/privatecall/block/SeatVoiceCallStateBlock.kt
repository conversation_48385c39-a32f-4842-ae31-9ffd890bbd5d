package com.interfun.buz.voicecall.privatecall.block

import android.animation.Animator
import android.view.View
import android.view.animation.OvershootInterpolator
import androidx.activity.ComponentActivity
import androidx.core.app.PictureInPictureModeChangedInfo
import androidx.core.util.Consumer
import androidx.lifecycle.lifecycleScope
import com.drakeet.multitype.MultiTypeAdapter
import com.google.android.flexbox.AlignItems
import com.google.android.flexbox.FlexDirection
import com.google.android.flexbox.FlexWrap
import com.google.android.flexbox.JustifyContent
import com.interfun.buz.base.ktx.LogLine
import com.interfun.buz.base.ktx.activityViewModels
import com.interfun.buz.base.ktx.asColor
import com.interfun.buz.base.ktx.asyncSubmitItems
import com.interfun.buz.base.ktx.click
import com.interfun.buz.base.ktx.collect
import com.interfun.buz.base.ktx.collectDistinctIn
import com.interfun.buz.base.ktx.collectLatestIn
import com.interfun.buz.base.ktx.collectLatestInUnique
import com.interfun.buz.base.ktx.deviceWidth
import com.interfun.buz.base.ktx.dp
import com.interfun.buz.base.ktx.dpFloat
import com.interfun.buz.base.ktx.findSafety
import com.interfun.buz.base.ktx.gone
import com.interfun.buz.base.ktx.invisible
import com.interfun.buz.base.ktx.isVisible
import com.interfun.buz.base.ktx.layoutMargin
import com.interfun.buz.base.ktx.layoutMarginBottom
import com.interfun.buz.base.ktx.layoutMarginTop
import com.interfun.buz.base.ktx.layoutSize
import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.base.ktx.navigationBarHeight
import com.interfun.buz.base.ktx.notEmpty
import com.interfun.buz.base.ktx.registerInterfaceBridge
import com.interfun.buz.base.ktx.requireContext
import com.interfun.buz.base.ktx.screenHeightReal
import com.interfun.buz.base.ktx.screenWidthReal
import com.interfun.buz.base.ktx.statusBarHeight
import com.interfun.buz.base.ktx.unregisterInterfaceBridge
import com.interfun.buz.base.ktx.viewLifecycleScope
import com.interfun.buz.base.ktx.visible
import com.interfun.buz.base.ktx.visibleIf
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.base.BaseVoiceCallBindingBlock
import com.interfun.buz.common.bean.chat.OnlineChatJumpInfo
import com.interfun.buz.common.bean.voicecall.CallEndType
import com.interfun.buz.common.bean.voicecall.CallPayload
import com.interfun.buz.common.bean.voicecall.CallPayloadType
import com.interfun.buz.common.bean.voicecall.CallRoomUser
import com.interfun.buz.common.bean.voicecall.CallStatus
import com.interfun.buz.common.bean.voicecall.CaptureStarted
import com.interfun.buz.common.bean.voicecall.CaptureStopped
import com.interfun.buz.common.bean.voicecall.ChannelType
import com.interfun.buz.common.bean.voicecall.DeviceChanged
import com.interfun.buz.common.bean.voicecall.RealTimeCallToastInfo
import com.interfun.buz.common.bean.voicecall.TextureViewState
import com.interfun.buz.common.bean.voicecall.VoiceCallRoom
import com.interfun.buz.common.bean.voicecall.createMyCallRoomInfo
import com.interfun.buz.common.bean.voicecall.getMyRoomInfo
import com.interfun.buz.common.ktx.alphaAnim
import com.interfun.buz.common.ktx.animatorPlayWithFraction
import com.interfun.buz.common.ktx.fractionChangeAnim
import com.interfun.buz.common.ktx.getDefaultIfZero
import com.interfun.buz.common.ktx.getFloatDefault
import com.interfun.buz.common.ktx.isMe
import com.interfun.buz.common.ktx.uid
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.manager.voicecall.RoomLifecycle
import com.interfun.buz.common.service.RealTimeCallService
import com.interfun.buz.common.utils.OnCallActionStatus
import com.interfun.buz.common.utils.OnCallActionType
import com.interfun.buz.common.utils.VoiceCallNotificationTracker
import com.interfun.buz.common.widget.recyclerview.itemanimators.ScaleInAnimator
import com.interfun.buz.voicecall.R
import com.interfun.buz.voicecall.common.view.widget.DraggableRealTimeCallUserCard
import com.interfun.buz.voicecall.common.view.widget.RealTimeCallUserCard
import com.interfun.buz.voicecall.core.manager.RealCallFlexboxLayoutManager
import com.interfun.buz.voicecall.databinding.VoicecallFragmentPrivateOnlinechatBinding
import com.interfun.buz.voicecall.groupcall.view.itemdelegate.VoiceChatGroupItemView
import com.interfun.buz.voicecall.privatecall.view.fragment.RealTimeCallFragment
import com.interfun.buz.voicecall.privatecall.viewmodel.StateToastInformation
import com.interfun.buz.voicecall.privatecall.viewmodel.TextureViewStateInfo
import com.interfun.buz.voicecall.privatecall.viewmodel.VideoCallViewModel
import com.interfun.buz.voicecall.privatecall.viewmodel.VideoContainerAnimationInfo
import com.yibasan.lizhifm.liveinteractive.CameraEventHandler
import com.yibasan.lizhifm.liveinteractive.CameraStopReason
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlin.math.abs
import kotlin.math.max

interface IImmersiveBridge {

    fun openImmersive()
}

/**
 * @Desc Handle user seat position + immersive
 * @Author:<EMAIL>
 * @Date: 2024/3/7
 */
class SeatVoiceCallStateBlock(
    private val fragment: RealTimeCallFragment,
    binding: VoicecallFragmentPrivateOnlinechatBinding,
    private val onlineChatJumpInfo: OnlineChatJumpInfo
) : BaseVoiceCallBindingBlock<VoicecallFragmentPrivateOnlinechatBinding>(binding), IImmersiveBridge,
    Consumer<PictureInPictureModeChangedInfo>, CameraEventHandler {

    companion object {
        const val TAG = "SeatVoiceCallStateBlock"
    }

    private val videoCallViewModel by fragment.activityViewModels<VideoCallViewModel>()
    private val mutex = Mutex()
    private var spaceContentMarginTop = 0
    private var spaceContentMarginBottom = 0
    private var isImmersive = false

    //    private var isImmersiveEnabled = false
    private var minimizeViewAtTop = false
    private var minimizeViewAtBottom = false
    private var isMoreUser = false
    private var savedMinimizeX: Float? = null
    private var savedMinimizeY: Float? = null
    private var participantsTransitionAnim: Animator? = null
    private var targetVideoContainerUserId: Long = -1L
    private lateinit var mUserAdapter: MultiTypeAdapter
    private val isPrivateChannel
        get() = ChannelType.isPrivateChannel(onlineChatJumpInfo.channelType)
    private val isInPipMode
        get() = routerServices<RealTimeCallService>().value?.isPipModeExist() == true
    private var rvCallListAlphaAnim: Animator? = null
    private val statusBarHeight get() = fragment.requireContext.statusBarHeight
    private val navigationBarHeight get() = fragment.requireContext.navigationBarHeight
    private val is1v1
        get() = currentRoom?.let { room ->
            ChannelType.isPrivateChannel(onlineChatJumpInfo.channelType) ||
                    room.members.value.count { it.isOnCall() } <= 2
        } == true

    override fun onCreate() {
        super.onCreate()
        fragment.activity?.registerInterfaceBridge(IImmersiveBridge::class.java, this)
        (fragment.activity as? ComponentActivity)?.addOnPictureInPictureModeChangedListener(this)
    }

    override fun initData() {
        super.initData()
        videoCallViewModel.apply {
            // for 2 users(including group call) to change the position and size of the video container
            textureViewStateSharedFlow.collectLatestIn(
                scope = fragment.viewLifecycleScope
            ) { textureViewStateList ->
                if (textureViewStateList.isNullOrEmpty()) return@collectLatestIn
                textureViewStateList.forEach { textureViewState ->
                    handleTextureViewState(textureViewState)
                }
            }
            userSize.collectLatestIn(fragment.viewLifecycleOwner) {
                logInfo(TAG, "userSize: $it")
                // 当在call别人的时候，屏幕只显示自己的摄像头，需要增加一个遮罩
                // 但当其他人接听后，就不再需要这个遮罩了
                binding.viewMyCameraMask.visibleIf(it == null)
                val userSize = it ?: return@collectLatestIn
                if (userSize == 0) return@collectLatestIn
                when (userSize) {
                    1 -> handleSingleParticipant()
                    2 -> handleTargetParticipants()
                    else -> handleMultipleParticipants()
                }
            }
        }
        observeAndRvCallListVisibility()
    }

    override fun initView() {
        super.initView()
        binding.clPrivateVoiceCall.click {
            val lifecycle = roomLifecycle ?: return@click
            if (lifecycle.isAtLeast(RoomLifecycle.CONNECTED)) handleImmersiveMode()
        }
        initFullVideoView()
    }

    private fun initFullVideoView() {
        val targetRatio = 9f / 16f // 目标宽高比9:16（竖屏）
//            val targetRatio = 19f / 16f // 目标宽高比9:16（竖屏）

        // 首先确保宽高至少满足最小要求
        var finalWidth = fragment.requireContext.screenWidthReal
        var finalHeight = fragment.requireContext.screenHeightReal

        // 计算当前比例
        val currentRatio = finalWidth.toFloat() / finalHeight

        // 调整尺寸，使比例尽可能接近9:16
        if (currentRatio > targetRatio) {
            // 当前比例过宽，增加高度使其接近9:16
            finalHeight = (finalWidth / targetRatio).toInt()
        } else if (currentRatio < targetRatio) {
            // 当前比例过高，增加宽度使其接近9:16
            finalWidth = (finalHeight * targetRatio).toInt()
        }

        binding.flPersonalVideoContainer.layoutSize(finalWidth, finalHeight)
        binding.flTargetVideoContainer.layoutSize(finalWidth, finalHeight)
    }

    override fun onResume() {
        super.onResume()
        if (!is1v1) return
        videoCallViewModel.apply {
            val list = mutableListOf<Pair<Long, Int>>()
            getTargetTextureViewState()?.let {
                list.add(targetVideoContainerUserId to it)
            }
            getMyTextureViewState()?.let {
                list.add(UserSessionManager.uid to it)
            }
            logInfo(
                TAG,
                "targetVideoContainerUserId:${targetVideoContainerUserId},list:${list.size}"
            )
            updateTextureViewState(list)
        }
    }

    override fun onPause() {
        super.onPause()
        if (this.fragment.activity?.isFinishing != true) {
            onFinishExitImmersiveMode()
        }
    }

    override fun doOnRoomExist(room: VoiceCallRoom) {
        super.doOnRoomExist(room)
        initRecyclerView(room)
        val myRoomInfo = getMyRoomInfo(room) ?: createMyCallRoomInfo()
        handleVideoContainer(binding.flPersonalVideoContainer, myRoomInfo, room)
        if (videoCallViewModel.getMyTextureViewState() == TextureViewState.EXPAND) {
            videoCallViewModel.updateTextureViewState(
                listOf(UserSessionManager.uid to TextureViewState.EXPAND)
            )
        }
        binding.vcTitleContainer.root.post {
            spaceContentMarginTop = statusBarHeight + binding.vcTitleContainer.root.height + 10.dp
            binding.spaceContent.layoutMarginTop(spaceContentMarginTop)
        }
        binding.vcButtonContainer.root.post {
            spaceContentMarginBottom =
                navigationBarHeight + 14.dp + binding.vcButtonContainer.root.height + 10.dp
            binding.spaceContent.layoutMarginBottom(spaceContentMarginBottom)
        }
        observeRoomMember(room)

        room.isPipModeShowingFlow.collect(fragment.viewLifecycleOwner) { showing ->
            if (!showing) {
                handleSeatChange(room = room, from = "isPipModeShowingFlow.collect no pip mode")
            }
        }
    }

    override fun onRoomConnected(room: VoiceCallRoom) {
        super.onRoomConnected(room)
        handleSeatChange(room = room, from = "onRoomConnected")
    }

    private fun handleSeatChange(room: VoiceCallRoom, from: String) {
        logInfo(TAG, "handleSeatChange:from=${from}", logLine = LogLine.RTC_CALL)
        if (videoCallViewModel.userSize.value == 2) resetTextureView()
        else {
            val memberList = room.members.value.filter { it.isOnCall() }
                .sortedBy { it.joinTime }
                .sortedByDescending { it.userId.isMe() }
            updateAdapter(room, memberList)
        }
    }

    override fun onRoomDestroy(room: VoiceCallRoom, reason: @CallEndType Int) {
        releaseTextureView()
        super.onRoomDestroy(room, reason)
    }

    // 只有2个人时，重新设置布局，超过2个人时，不调用
    private fun resetTextureView() {
        logInfo(TAG, "resetTextureView")
        val room = currentRoom ?: return

        val myRoomInfo = getMyRoomInfo(room) ?: createMyCallRoomInfo()
        handleVideoContainer(binding.flPersonalVideoContainer, myRoomInfo, room)

        val user = room.members.value.filter { it.isOnCall() }.findSafety { it.userId.isMe().not() }
            ?: return
        targetVideoContainerUserId = user.userId
        handleVideoContainer(binding.flTargetVideoContainer, user, room)
        if (room.getLifeCycle().state.isAtLeast(RoomLifecycle.CONNECTED)) {
            binding.flTargetVideoContainer.bindRemoteVideoView(user, room.rtcVcResManager)
        }
        binding.flTargetVideoContainer.visible()
        binding.ivGroupPortrait.gone()

        videoCallViewModel.apply {
            if (getMyTextureViewState() == TextureViewState.EXPAND && getTargetTextureViewState() == TextureViewState.EXPAND) {
                updateTextureViewState(
                    listOf(
                        targetVideoContainerUserId to TextureViewState.EXPAND,
                        UserSessionManager.uid to TextureViewState.COLLAPSE
                    )
                )
            }
        }
    }

    private fun initRecyclerView(room: VoiceCallRoom) {
        val memberList = room.members.value.filter { it.isOnCall() }
        mUserAdapter = MultiTypeAdapter(memberList).apply {
            register(
                VoiceChatGroupItemView(room)
            )
        }
        room.rtcVcResManager.addCameraEventHandler(this)
        binding.rvCallList.apply {
            layoutMargin(top = statusBarHeight + 10.dp, bottom = navigationBarHeight + 91.dp)
//            layoutMarginTop(statusBarHeight + 10.dp)
            setHasFixedSize(true)
            layoutManager = RealCallFlexboxLayoutManager(context).apply {
                flexDirection = FlexDirection.ROW// 主轴横向布局
                setFlexWrap(FlexWrap.WRAP)// 允许换行
                justifyContent = JustifyContent.CENTER// 主轴居中
                alignItems = AlignItems.CENTER// 交叉轴居中
            }
            itemAnimator = ScaleInAnimator(OvershootInterpolator()).apply {
                changeDuration = 0
            }
            adapter = mUserAdapter
        }

        room.isPipModeShowingFlow.collectLatestIn(fragment.viewLifecycleOwner) { showing ->
            if (!showing) {
                mUserAdapter.notifyItemRangeChanged(
                    0,
                    mUserAdapter.itemCount,
                    CallPayload(
                        listOf(
                            CallPayloadType.UPDATE_CAMERA_STATUS
                        )
                    )
                )
            }
        }
    }

    private fun observeAndRvCallListVisibility() {
        videoCallViewModel.seatsVisibleFlow.collectDistinctIn(fragment.viewLifecycleOwner) {
            if (it == binding.rvCallList.isVisible()) return@collectDistinctIn
            if (it) {
                binding.rvCallList.visible()
            }

            rvCallListAlphaAnim?.cancel()
            rvCallListAlphaAnim =
                binding.rvCallList.alphaAnim(150L, if (it) 0f else 1f, if (it) 1f else 0f) {
                    binding.rvCallList.visibleIf(videoCallViewModel.seatsVisibleFlow.value)
                    binding.rvCallList.alpha =
                        if (videoCallViewModel.seatsVisibleFlow.value) 1f else 0f
                    logInfo(
                        TAG, "rvCallListAlphaAnim end alpha=${binding.rvCallList.alpha}, " +
                                "visible=${binding.rvCallList.isVisible()}"
                    )
                }
            rvCallListAlphaAnim?.start()
        }
    }

    /* -------------------- Handle User Count Animation -------------------- */
    // User = 1 only show personal video container
    private fun handleSingleParticipant() {
        logInfo(TAG, "handleSingleParticipant")
        stopParticipantTransitionAnimation()
        isMoreUser = false
        videoCallViewModel.clearToastInformation()
        binding.flTargetVideoContainer.releaseCamera(currentRoom?.rtcVcResManager)
        binding.flTargetVideoContainer.invisible()
        binding.ivGroupPortrait.visible()
        videoCallViewModel.updateTextureViewState(
            listOf(UserSessionManager.uid to TextureViewState.EXPAND)
        )
    }

    // User = 2 hide recyclerView ui and show 1v1 ui
    private fun handleTargetParticipants() {
        logInfo(TAG, "handleTargetParticipants")
        stopParticipantTransitionAnimation()
        isMoreUser = false
        resetTextureView()
    }

    // User > 2 hide 1v1 ui and show recyclerView ui
    private fun handleMultipleParticipants() {
        logInfo(TAG, "handleMultipleParticipants")
        if (isMoreUser) return
        isMoreUser = true
        binding.ivGroupPortrait.gone()
        videoCallViewModel.clearToastInformation()
        videoCallViewModel.clearTextureViewStateList()
        if (handleParticipantsTransition()) return
        handleMultipleParticipantEnd()
    }

    private fun handleParticipantsTransition(): Boolean {
        val room = currentRoom ?: return false
        if (isInPipMode) return false
        if (!binding.flPersonalVideoContainer.isVisible() || !binding.flTargetVideoContainer.isVisible()) return false

        val members = room.members.value
        val personalIndex = members.indexOfFirst { it.userId.isMe() }
        val targetIndex = members.indexOfFirst { it.userId == targetVideoContainerUserId }

        if (personalIndex == -1 || targetIndex == -1) return false

        val personalViewHolder =
            binding.rvCallList.findViewHolderForAdapterPosition(personalIndex) ?: return false
        val targetViewHolder =
            binding.rvCallList.findViewHolderForAdapterPosition(targetIndex) ?: return false

        prepareAnimation(binding.flPersonalVideoContainer)
        prepareAnimation(binding.flTargetVideoContainer)

        val personalAnimationInfo =
            createAnimationInfo(binding.flPersonalVideoContainer, personalViewHolder.itemView)
        val targetAnimationInfo =
            createAnimationInfo(binding.flTargetVideoContainer, targetViewHolder.itemView)

        stopParticipantTransitionAnimation()
        participantsTransitionAnim = animatorPlayWithFraction(
            duration = 300L,
            updateListener = { fraction ->
                binding.flPersonalVideoContainer.doAnimation(fraction, personalAnimationInfo)
                binding.flTargetVideoContainer.doAnimation(fraction, targetAnimationInfo)
            }
        )
        participantsTransitionAnim?.start()

        return true
    }

    private fun prepareAnimation(videoContainer: DraggableRealTimeCallUserCard) {
        videoContainer.apply {
            showUserName()
            radius = 16.dpFloat
            setIconEnabled(true)
            setDragEnable(false)
            setCanShowSpeakingAnim(false)
        }
    }

    private fun createAnimationInfo(
        videoContainer: RealTimeCallUserCard,
        itemView: View
    ): VideoContainerAnimationInfo {
        val originalX = videoContainer.x
        val originalY = videoContainer.y

        val endX = itemView.x + binding.rvCallList.x
        val endY = itemView.y + binding.rvCallList.y

        val originalWidth = videoContainer.width.getDefaultIfZero(itemView.width).toFloat()
        val originalHeight = videoContainer.height.getDefaultIfZero(itemView.height).toFloat()

        val endWidth = itemView.width.toFloat()
        val endHeight = itemView.height.toFloat()

        return VideoContainerAnimationInfo(
            width = originalWidth to endWidth,
            height = originalHeight to endHeight,
            x = originalX to endX,
            y = originalY to endY
        )
    }

    private fun RealTimeCallUserCard.doAnimation(
        fraction: Float,
        animationInfo: VideoContainerAnimationInfo
    ) {
        x = fractionChangeAnim(fraction, animationInfo.x.first, animationInfo.x.second)
        y = fractionChangeAnim(fraction, animationInfo.y.first, animationInfo.y.second)
        cardElevation = fractionChangeAnim(fraction, cardElevation, 0f)
        val width =
            fractionChangeAnim(fraction, animationInfo.width.first, animationInfo.width.second)
        val height =
            fractionChangeAnim(fraction, animationInfo.height.first, animationInfo.height.second)
        layoutSize(width.toInt(), height.toInt())
    }

    // when more than 2 user in group call, hide 1v1(including group call with only 2 users) ui
    private fun handleMultipleParticipantEnd() {
        logInfo(TAG, "handleMultipleParticipantEnd")
        binding.flPersonalVideoContainer.invisible()
        binding.flTargetVideoContainer.invisible()
        binding.flPersonalVideoContainer.releaseCamera(currentRoom?.rtcVcResManager)
        binding.flTargetVideoContainer.releaseCamera(currentRoom?.rtcVcResManager)
        binding.flPersonalVideoContainer.hideUserName()
        binding.flTargetVideoContainer.hideUserName()
    }

    private fun stopParticipantTransitionAnimation() {
        participantsTransitionAnim?.cancel()
        if (isMoreUser) {
            handleMultipleParticipantEnd()
        }
    }

    /* -------------------- Observe User -------------------- */
    private fun observeRoomMember(room: VoiceCallRoom) {
        room.members.collectLatestInUnique(fragment.viewLifecycleOwner) { members ->
            handleBusyOrNoAnswerMembers(members)
            if (room.getLifeCycle().state.isAtMost(RoomLifecycle.WAITING)) return@collectLatestInUnique
            logInfo(
                TAG,
                "doOnRoomExist observeRoomMember(size=${members.size}) cameraStatus= ${members.firstOrNull { it.userId.isMe() }?.cameraStatus}",
                logLine = LogLine.MY_RTC_CAMERA_STATE
            )
            val memberList = room.members.value.filter { it.isOnCall() }
                .sortedBy { it.joinTime }
                .sortedByDescending { it.userId.isMe() }
            videoCallViewModel.updateMemberSize(memberList.size)
            updateAdapter(room, memberList)
        }
    }

    private fun updateAdapter(room: VoiceCallRoom, memberList: List<CallRoomUser>) {
        logInfo(TAG, "updateAdapter size=${memberList.size},isInPipMode=${isInPipMode}")
        if (isInPipMode) {
            logInfo(TAG, "updateAdapter return cos it is in pip mode")
            return
        }

        if (::mUserAdapter.isInitialized.not()) {
            initRecyclerView(room)
        }
        //之前存在一个问题如果当前 3 人变成 2 人.这里不在使用list展示了.但是这里的逻辑依旧存在
        var memberList: List<CallRoomUser> = if (memberList.size <= 2) {
            emptyList()
        } else {
            memberList
        }
        mUserAdapter.asyncSubmitItems(
            lifecycleCoroutineScope = fragment.viewLifecycleScope,
            mutex = mutex,
            newItems = memberList,
            areItemsTheSame = { oldItem, newItem -> oldItem.userId == newItem.userId },
            areContentsTheSame = { oldItem, newItem -> oldItem == newItem },
            changePayload = { oldItem, newItem ->
                val list = newItem.createPayload(oldItem)
                if (list.isEmpty()) {
                    null
                } else {
                    CallPayload(list)
                }
            },
            finishCallback = {
//                if (memberList.notEmpty()) {
//                    mUserAdapter.notifyItemRangeChanged(
//                        0,
//                        memberList.size,
//                        CallPayload(
//                            listOf(
//                                CallPayloadType.UPDATE_CAMERA_STATUS, CallPayloadType.SHOW_USERNAME
//                            )
//                        )
//                    )
//                }
            }
        )
    }

    private fun handleBusyOrNoAnswerMembers(members: List<CallRoomUser>) {
        if (isPrivateChannel) return
        members.filter {
            it.callStatus == CallStatus.REJECT
                    || it.callStatus == CallStatus.TIMEOUT
        }.forEach {
            fragment.viewLifecycleScope.launch {
                val style =
                    if (it.callStatus == CallStatus.TIMEOUT) RealTimeCallToastInfo.TARGET_NO_ANSWER
                    else RealTimeCallToastInfo.TARGET_BUSY
                val info = StateToastInformation(
                    toastStyle = style,
                    username = it.contactFirstName,
                    portrait = it.portrait
                )
                videoCallViewModel.addStateToastInformation(info)
                delay(5000)
                videoCallViewModel.removeStateToastInformation(info)
            }
        }
    }

    private fun handleVideoContainer(
        container: RealTimeCallUserCard,
        member: CallRoomUser,
        room: VoiceCallRoom
    ) {
        logInfo(
            TAG,
            "handleVideoContainer(${container.id})==>isInitialised: ${container.isInitialised}"
        )
        if (container.isInitialised.not()) {
            container.initCard(member, cameraManager = room.rtcVcResManager)
            container.bindVolumeFlow(
                fragment.lifecycleScope,
                member,
                room.speakingFlow
            )
        }
        if (videoCallViewModel.getTextureViewState(member.userId) == null) {
            videoCallViewModel.addTextureViewStateList(member.userId)
        }
    }

    /* -------------------- TextureView State -------------------- */
    private fun handleTextureViewState(textureViewState: TextureViewStateInfo) {
        val textureViewContainer = getTextureViewByUserId(textureViewState.userId)
        logInfo(
            TAG, "handleTextureViewState(${textureViewState.userId}，" +
                    "isMe: ${textureViewState.userId.isMe()}) ==> state: ${textureViewState.state}",
            logLine = LogLine.RTC_CALL
        )
        textureViewContainer.apply {
            draggableArea = null
            setOnClickListener(null)
        }
        if (textureViewState.isExpand()) {
            // 全屏通话视频
            handleTextureViewExpandState(textureViewContainer, textureViewState)
        } else {
            // 缩小通话视频
            handleTextureViewMinimizeState(textureViewContainer, textureViewState)
        }
    }

    private fun getTextureViewByUserId(userId: Long): DraggableRealTimeCallUserCard {
        return when (userId) {
            UserSessionManager.uid -> binding.flPersonalVideoContainer
            else -> binding.flTargetVideoContainer
        }
    }

    private fun handleTextureViewExpandState(
        textureViewContainer: DraggableRealTimeCallUserCard,
        textureViewState: TextureViewStateInfo
    ) {
        logDebug(TAG, "handleTextureViewExpandState")
        textureViewContainer.apply {
            val originalX = translationX
            val originalY = translationY
            val originalWidth = width
            val originalHeight = height
            val originalRadius = radius
            val targetRatio = 9f / 16f // 目标宽高比9:16（竖屏）
//            val targetRatio = 19f / 16f // 目标宽高比9:16（竖屏）

            // 首先确保宽高至少满足最小要求
            var finalWidth = max(width, fragment.requireContext.screenWidthReal)
            var finalHeight = max(height, fragment.requireContext.screenHeightReal)

            // 计算当前比例
            val currentRatio = finalWidth.toFloat() / finalHeight

            // 调整尺寸，使比例尽可能接近9:16
            if (currentRatio > targetRatio) {
                // 当前比例过宽，增加高度使其接近9:16
                finalHeight = (finalWidth / targetRatio).toInt()
            } else if (currentRatio < targetRatio) {
                // 当前比例过高，增加宽度使其接近9:16
                finalWidth = (finalHeight * targetRatio).toInt()
            }

            setTextureViewExpandStart(this)
            logDebug(
                TAG,
                "handleTextureViewExpandState(${textureViewState.userId})==> originalX:$originalX -> 0, originalY:$originalY -> 0, width:$width -> $finalWidth, height:$height->$finalHeight"
            )
            if (isInPipMode) {
                doTextureViewStateAnimation(
                    fraction = 1f,
                    animInfo = VideoContainerAnimationInfo(
                        width = originalWidth.toFloat() to finalWidth.toFloat(),
                        height = originalHeight.toFloat() to finalHeight.toFloat(),
                        x = originalX to 0f,
                        y = originalY to 0f,
                        radius = originalRadius to 0.dpFloat
                    ),
                )
                setTextureViewExpandEnd(this)
                return
            }
            animatorPlayWithFraction(
                duration = 200L,
                updateListener = { fraction ->
                    doTextureViewStateAnimation(
                        fraction = fraction,
                        animInfo = VideoContainerAnimationInfo(
                            width = originalWidth.toFloat() to finalWidth.toFloat(),
                            height = originalHeight.toFloat() to finalHeight.toFloat(),
                            x = originalX to 0f,
                            y = originalY to 0f,
                            radius = originalRadius to 0.dpFloat
                        ),
                    )
                },
                finishCallback = {
                    setTextureViewExpandEnd(this)
                }
            ).start()
        }
    }

    private fun handleTextureViewMinimizeState(
        textureViewContainer: DraggableRealTimeCallUserCard,
        textureViewState: TextureViewStateInfo
    ) {
        logDebug(TAG, "handleTextureViewMinimizeState")
        textureViewContainer.apply {
            val originalX = translationX
            val originalY = translationY
            val originalWidth = width
            val originalHeight = height
            val originalRadius = radius
            val viewRatio = 9f / 16f
            val finalWidth = (deviceWidth / 3).coerceAtMost(180.dp)
            val finalHeight = (finalWidth / viewRatio).toInt()
            val diff = (deviceWidth - finalWidth) / 2f - 10.dp
            savedMinimizeX = savedMinimizeX ?: diff
            savedMinimizeY =
                savedMinimizeY ?: (binding.vcTitleContainer.root.bottom + 10.dp).toFloat()
            setTextureViewMinimizeStart(this)
            logDebug(
                TAG,
                "handleTextureViewMinimizeState(hashCode ${textureViewContainer.hashCode()}) (${textureViewState.userId})==> originalX:$originalX -> $savedMinimizeX, originalY:$originalY -> $savedMinimizeY, width:$width -> $finalWidth, height:$height->$finalHeight deviceWidth:${deviceWidth} "
            )
            if (isInPipMode) {
                doTextureViewStateAnimation(
                    fraction = 1f,
                    animInfo = VideoContainerAnimationInfo(
                        width = originalWidth.toFloat() to finalWidth.toFloat(),
                        height = originalHeight.toFloat() to finalHeight.toFloat(),
                        x = originalX to (savedMinimizeX ?: 0f),
                        y = originalY to (savedMinimizeY ?: 0f),
                        radius = originalRadius to 16.dpFloat
                    )
                )
                setTextureViewMinimizeEnd(this, textureViewState)
                return
            }
            animatorPlayWithFraction(
                duration = 200L,
                updateListener = { fraction ->
                    doTextureViewStateAnimation(
                        fraction = fraction,
                        animInfo = VideoContainerAnimationInfo(
                            width = originalWidth.toFloat() to finalWidth.toFloat(),
                            height = originalHeight.toFloat() to finalHeight.toFloat(),
                            x = originalX to (savedMinimizeX ?: 0f),
                            y = originalY to (savedMinimizeY ?: 0f),
                            radius = originalRadius to 16.dpFloat
                        )
                    )
                },
                finishCallback = {
                    setTextureViewMinimizeEnd(this, textureViewState)
                }
            ).start()
        }
    }

    private fun RealTimeCallUserCard.doTextureViewStateAnimation(
        fraction: Float,
        animInfo: VideoContainerAnimationInfo,
    ) {
        translationX = fractionChangeAnim(fraction, animInfo.x.first, animInfo.x.second)
        translationY = fractionChangeAnim(fraction, animInfo.y.first, animInfo.y.second)
        animInfo.radius?.let { radius ->
            setRadius(fractionChangeAnim(fraction, radius.first, radius.second))
        }
        val width =
            fractionChangeAnim(fraction, animInfo.width.first, animInfo.width.second).toInt()
        val height =
            fractionChangeAnim(fraction, animInfo.height.first, animInfo.height.second).toInt()
        layoutSize(width, height)
    }

    private fun setTextureViewExpandStart(textureViewContainer: DraggableRealTimeCallUserCard) {
        textureViewContainer.apply {
            isClickable = false
            isFocusable = false
            cardElevation = 0f
            setIconEnabled(false)
            setDragEnable(false)
            setCardBackgroundColor(R.color.alpha_black_100.asColor())
        }
    }

    private fun setTextureViewExpandEnd(textureViewContainer: RealTimeCallUserCard) {
        logDebug(TAG, "setTextureViewExpandEnd")
        textureViewContainer.apply {
            translationX = 0f
            translationY = 0f
            radius = 0.dpFloat
            videoCallViewModel.updateUserState()
            setCanShowSpeakingAnim(true)
        }
    }

    private fun setTextureViewMinimizeStart(textureViewContainer: DraggableRealTimeCallUserCard) {
        textureViewContainer.apply {
            setCanShowSpeakingAnim(false)
            cardElevation = 20.dpFloat
            draggableArea = binding.spaceContent
            setCardBackgroundColor(R.color.color_background_3_default.asColor())
        }
    }

    private fun setTextureViewMinimizeEnd(
        textureViewContainer: DraggableRealTimeCallUserCard,
        textureViewState: TextureViewStateInfo
    ) {
        logDebug(TAG, "setTextureViewMinimizeEnd")
        textureViewContainer.apply {
            isFocusable = true
            isClickable = true
            translationX = savedMinimizeX ?: 0f
            translationY = savedMinimizeY ?: 0f
            radius = 16.dpFloat
            setDragEnable(true)
            setIconEnabled(true)
            click {
                savedMinimizeX = translationX
                savedMinimizeY = translationY
                videoCallViewModel.switchTextureViewState()

                val isConnected =
                    currentRoom?.getLifeCycle()?.state?.isAtLeast(RoomLifecycle.CONNECTED)
                if (isConnected == true) {
                    VoiceCallNotificationTracker.onConnectedUIOperationEvent(
                        action = OnCallActionType.BigSmallScreenChanged,
                        pageStatus = if (textureViewState.userId.isMe()) OnCallActionStatus.BigScreen else OnCallActionStatus.SmallScreen,
                        channelType = onlineChatJumpInfo.channelType,
                        channelId = onlineChatJumpInfo.channelId ?: 0,
                        callType = onlineChatJumpInfo.callType,
                    )
                }
            }
        }
    }

    /* -------------------- Immersive Mode -------------------- */
    private fun handleImmersiveMode() {
        val minimizeTextureView = getMinimizeTextureView()
        if (isImmersive) {
            exitImmersiveMode(minimizeTextureView)
        } else {
            enterImmersiveMode(minimizeTextureView)
        }
        isImmersive = !isImmersive
    }

    private fun getMinimizeTextureView(): RealTimeCallUserCard? {
        return when {
            videoCallViewModel.getTargetTextureViewState() == TextureViewState.COLLAPSE -> binding.flTargetVideoContainer
            videoCallViewModel.getMyTextureViewState() == TextureViewState.COLLAPSE -> binding.flPersonalVideoContainer
            else -> null
        }
    }

    private fun enterImmersiveMode(minimizeTextureView: RealTimeCallUserCard?) {
        updateUserAdapter(CallPayloadType.HIDE_USERNAME)
        prepareImmersiveLayout(minimizeTextureView, true)

        animateLayout(
            shadowAlpha = 0f,
            titleAlpha = 0f,
            buttonAlpha = 0f,
            buttonTranslation = 10f,
            statusTranslation = 57.dpFloat,
            minimizeViewTop = statusBarHeight + 10.dpFloat,
            minimizeViewBottom = navigationBarHeight + 10.dpFloat,
            minimizeTextureView = minimizeTextureView,
            onFinish = {
                binding.vcTitleContainer.root.invisible()
                binding.vcButtonContainer.root.invisible()
                binding.spaceContent.apply {
                    layoutMarginTop(statusBarHeight + 10.dp)
                    layoutMarginBottom(navigationBarHeight + 10.dp)
                }
            }
        )
    }

    private fun exitImmersiveMode(minimizeTextureView: RealTimeCallUserCard?) {
        updateUserAdapter(CallPayloadType.SHOW_USERNAME)
        prepareImmersiveLayout(minimizeTextureView, false)
        resetLayoutVisibility()
        binding.rvStatusContainer.translationY = 57.dpFloat

        animateLayout(
            shadowAlpha = 1f,
            titleAlpha = 1f,
            buttonAlpha = 1f,
            buttonTranslation = 0f,
            statusTranslation = 0f,
            minimizeViewTop = spaceContentMarginTop.toFloat(),
            minimizeViewBottom = spaceContentMarginBottom.toFloat(),
            minimizeTextureView = minimizeTextureView,
            onFinish = {
                onFinishExitImmersiveMode()
            }
        )
    }

    private fun onFinishExitImmersiveMode() {
        resetLayoutVisibility()
        binding.rvStatusContainer.translationY = 0f
        binding.vcTitleContainer.root.alpha = 1f
        binding.vcButtonContainer.root.alpha = 1f
        binding.spaceContent.apply {
            layoutMarginTop(spaceContentMarginTop)
            layoutMarginBottom(spaceContentMarginBottom)
        }
    }

    private fun updateUserAdapter(payloadType: CallPayloadType) {
        if (::mUserAdapter.isInitialized) {
            mUserAdapter.notifyItemRangeChanged(0, mUserAdapter.itemCount, payloadType)
        }
    }

    private fun prepareImmersiveLayout(
        minimizeTextureView: RealTimeCallUserCard?,
        enterImmersive: Boolean
    ) {
        minimizeTextureView?.apply {
            val viewY = y.toInt()
            val viewBottomY = viewY + height

            val spaceY = binding.spaceContent.y.toInt()
            val spaceBottomY = spaceY + binding.spaceContent.height

            minimizeViewAtTop =
                if (enterImmersive) abs(viewY - spaceY) <= 5
                else viewY <= spaceContentMarginTop
            minimizeViewAtBottom =
                if (enterImmersive) abs(viewBottomY - spaceBottomY) <= 5
                else viewBottomY >= fragment.requireContext.screenHeightReal - spaceContentMarginBottom
        }
    }

    private fun resetLayoutVisibility() {
        binding.vcTitleContainer.root.visible()
        binding.vcButtonContainer.root.visible()
    }

    private fun animateLayout(
        shadowAlpha: Float,
        titleAlpha: Float,
        buttonAlpha: Float,
        buttonTranslation: Float,
        statusTranslation: Float,
        minimizeViewTop: Float,
        minimizeViewBottom: Float,
        minimizeTextureView: RealTimeCallUserCard?,
        onFinish: () -> Unit
    ) {
        val minimizeViewOriginalY = minimizeTextureView?.y.getFloatDefault()

        animatorPlayWithFraction(
            duration = 150L,
            updateListener = { fraction ->
                binding.vShadowBackground.alpha =
                    fractionChangeAnim(fraction, binding.vShadowBackground.alpha, shadowAlpha)
                binding.vcTitleContainer.root.alpha =
                    fractionChangeAnim(fraction, binding.vcTitleContainer.root.alpha, titleAlpha)
                binding.vcButtonContainer.root.alpha =
                    fractionChangeAnim(fraction, binding.vcButtonContainer.root.alpha, buttonAlpha)
                binding.vcButtonContainer.root.translationY = fractionChangeAnim(
                    fraction,
                    binding.vcButtonContainer.root.translationY,
                    buttonTranslation
                )
                binding.rvStatusContainer.translationY = fractionChangeAnim(
                    fraction,
                    binding.rvStatusContainer.translationY,
                    statusTranslation
                )

                minimizeTextureView?.apply {
                    if (minimizeViewAtTop) {
                        y = fractionChangeAnim(fraction, minimizeViewOriginalY, minimizeViewTop)
                    } else if (minimizeViewAtBottom) {
                        y = fractionChangeAnim(
                            fraction,
                            minimizeViewOriginalY,
                            fragment.requireContext.screenHeightReal - (minimizeViewBottom + height)
                        )
                    }
                }
            },
            finishCallback = onFinish
        ).start()
    }

    private fun releaseTextureView() {
        logInfo(TAG, "releaseTextureView")
        updateUserAdapter(CallPayloadType.RELEASE_CAMERA)
        binding.flPersonalVideoContainer.releaseCamera(currentRoom?.rtcVcResManager)
        binding.flTargetVideoContainer.releaseCamera(currentRoom?.rtcVcResManager)
    }

    override fun onDestroy() {
        releaseTextureView()
        super.onDestroy()
        currentRoom?.rtcVcResManager?.removeCameraEventHandler(this)
        (fragment.activity as? ComponentActivity)?.removeOnPictureInPictureModeChangedListener(this)
        fragment.activity?.unregisterInterfaceBridge(IImmersiveBridge::class.java)
    }

    override fun openImmersive() {
        if (!isImmersive) {
            handleImmersiveMode()
        }
    }

    override fun accept(value: PictureInPictureModeChangedInfo) {
        if (value.isInPictureInPictureMode) {
            if (!isImmersive) {
                handleImmersiveMode()
            }
        } else {
            if (isImmersive) {
                handleImmersiveMode()
            }
        }
    }


    override fun onCameraCaptureStarted() {
        val index = mimeSeatIndex()
        if (index>=0) {
            mUserAdapter.notifyItemChanged(index,CaptureStarted)
        }
    }

    private fun mimeSeatIndex(): Int {
        val index = mUserAdapter.items.indexOfFirst { usr ->
            (usr is CallRoomUser) && usr.userId.isMe()
        }
        return index
    }

    override fun onCameraDeviceChanged(isFront: Boolean) {
        val index = mimeSeatIndex()
        if (index>=0) {
            mUserAdapter.notifyItemChanged(index,DeviceChanged)
        }
    }

    override fun onCameraCaptureStopped(reason: CameraStopReason?) {
        val index = mimeSeatIndex()
        if (index>=0) {
            mUserAdapter.notifyItemChanged(index,CaptureStopped(reason))
        }
    }
}