package com.interfun.buz.voicecall.util

import android.graphics.Color
import androidx.palette.graphics.Palette
import com.interfun.buz.base.ktx.log

/**
 * @Desc voice call color palette
 * @Author:<EMAIL>
 * @Date: 2024/4/7
 */

object VCPaletteColor {

    /**
     *  the priority color is DominantColor > DarkMutedColor > MutedColor > defaultColor
     */
    fun getPriorityColor(palette: Palette?, defaultColor: Int): Int{
        if (palette != null) {
            val dominantColor = palette.getDominantColor(defaultColor)
            if (dominantColor != defaultColor) {
                return dominantColor
            }
            val darkMutedColor = palette.getDarkMutedColor(defaultColor)
            if (darkMutedColor != defaultColor) {
                return darkMutedColor
            }
            return palette.getMutedColor(defaultColor)
        }else{
            return defaultColor
        }
    }

    fun rebuildRGBColor(color: Int): Int {
        val red = Color.red(color)
        val green = Color.green(color)
        val blue = Color.blue(color)
        if (red < 60 && green < 60 && blue < 60){
            val newRed = red + 100
            val newGreen = green + 100
            val newBlue = blue + 100
            log(
                "VCPaletteColor",
                "Palette red = $red, green = $green, blue = $blue, newRed = $newRed, newGreen = $newGreen, newBlue = $newBlue"
            )
            return Color.rgb(newRed, newGreen, newBlue)
        }else{
            log(
                "VCPaletteColor",
                "Palette red = $red, green = $green, blue = $blue， and not need to translate RGB"
            )
            return color
        }
    }

}