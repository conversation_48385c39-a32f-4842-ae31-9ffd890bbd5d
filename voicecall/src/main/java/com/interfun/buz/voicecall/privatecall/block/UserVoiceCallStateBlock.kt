package com.interfun.buz.voicecall.privatecall.block


import androidx.recyclerview.widget.LinearLayoutManager
import com.drakeet.multitype.MultiTypeAdapter
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.base.BaseVoiceCallBindingBlock
import com.interfun.buz.common.bean.chat.OnlineChatJumpInfo
import com.interfun.buz.common.bean.chat.OnlineChatJumpType
import com.interfun.buz.common.bean.voicecall.CallRoomUser
import com.interfun.buz.common.bean.voicecall.CallStatus
import com.interfun.buz.common.bean.voicecall.CallType
import com.interfun.buz.common.bean.voicecall.CameraState
import com.interfun.buz.common.bean.voicecall.CameraStatus
import com.interfun.buz.common.bean.voicecall.ChannelType
import com.interfun.buz.common.bean.voicecall.MicStatus
import com.interfun.buz.common.bean.voicecall.NetworkStatus
import com.interfun.buz.common.bean.voicecall.RealTimeCallToastInfo
import com.interfun.buz.common.bean.voicecall.TextureViewState
import com.interfun.buz.common.bean.voicecall.VoiceCallRoom
import com.interfun.buz.common.ktx.getBooleanDefault
import com.interfun.buz.common.ktx.isMe
import com.interfun.buz.common.manager.voicecall.CamStatusListener
import com.interfun.buz.common.manager.voicecall.MicStatusListener
import com.interfun.buz.common.manager.voicecall.NetworkStatusListener
import com.interfun.buz.common.manager.voicecall.RoomLifecycle
import com.interfun.buz.common.manager.voicecall.RoomVCResManager
import com.interfun.buz.common.manager.voicecall.VoiceCallPortal
import com.interfun.buz.common.service.RealTimeCallService
import com.interfun.buz.voicecall.common.view.itemdelegate.RealTimeCallStateToastItemView
import com.interfun.buz.voicecall.databinding.VoicecallFragmentPrivateOnlinechatBinding
import com.interfun.buz.voicecall.privatecall.bean.RealTimeCallPendState
import com.interfun.buz.voicecall.privatecall.view.fragment.RealTimeCallFragment
import com.interfun.buz.voicecall.privatecall.viewmodel.StateToastInformation
import com.interfun.buz.voicecall.privatecall.viewmodel.VideoCallViewModel
import com.yibasan.lizhifm.liveinteractive.CameraEventHandler
import com.yibasan.lizhifm.liveinteractive.CameraStopReason

/**
 * @Desc Handle user status (network, mic, camera)
 * @Author:<EMAIL>
 * @Date: 2024/3/7
 */
class UserVoiceCallStateBlock(
    private val fragment: RealTimeCallFragment,
    binding: VoicecallFragmentPrivateOnlinechatBinding,
    private val onlineChatJumpInfo: OnlineChatJumpInfo,
    private val realTimeCallPendState: RealTimeCallPendState?
) : BaseVoiceCallBindingBlock<VoicecallFragmentPrivateOnlinechatBinding>(binding),CameraEventHandler {

    companion object {
        const val TAG = "UserVoiceCallStateBlock"
    }

    private val isCameraFront
        get() = currentRoom?.myCamState?.value?.let { CameraState.isFront(it) }.getBooleanDefault(true)
    private val isVideoCall
        get() = CallType.isVideoCall(onlineChatJumpInfo.callType)
    private val isInPipMode 
        get() = routerServices<RealTimeCallService>().value?.isPipModeExist() == true
    private val is1v1
        get() = currentRoom?.let { room ->
            ChannelType.isPrivateChannel(onlineChatJumpInfo.channelType) ||
                    room.members.value.count { it.isOnCall() } <= 2
        } == true
    private val videoCallViewModel by fragment.activityViewModels<VideoCallViewModel>()
    private lateinit var mToastAdapter: MultiTypeAdapter

    private val micStatusListener = object : MicStatusListener {
        override fun onMicStatusChange(member: CallRoomUser, status: Int) {
            logInfo(
                TAG,
                "onMicStatusChange CallRoomUser = ${member.userName}, micStatus = $status"
            )
            videoCallViewModel.updateUserMicStatus(member)
        }

        override fun myMicStatusChange(status: Int) {
            logInfo(TAG, "myMicStatusChange micStatus = $status")
            videoCallViewModel.updateMyMicStatus(MicStatus.isMicMuted(status))
        }
    }

    private val camStatusListener = object : CamStatusListener {
        override fun onCamStatusChange(
            member: CallRoomUser,
            status: @CameraStatus Int
        ) {
            logInfo(TAG, "onCamStatusChange CallRoomUser = ${member.userName}, camStatus = $status")
            videoCallViewModel.updateUserCamStatus(member)
        }

        override fun myCamStatusChange(status: @CameraStatus Int) {
            logInfo(TAG, "myCamStatusChange camStatus = $status", logLine = LogLine.MY_RTC_CAMERA_STATE)
            videoCallViewModel.updateMyCamStatus(CameraStatus.isOpen(status))
        }
    }

    private val networkStatusListener = object : NetworkStatusListener {
        override fun onNetworkStatusChange(member: CallRoomUser, status: Int) {
            videoCallViewModel.updateUserNetworkState(member)
        }

        override fun myNetworkStatusChange(status: Int) {
            videoCallViewModel.updateMyNetworkState(NetworkStatus.isBadNetwork(status))
        }
    }

    override fun initView() {
        super.initView()
        initToastRecyclerView()
    }

    override fun initData() {
        super.initData()
        videoCallViewModel.apply {
            updateUserState.collectLatestIn(fragment.viewLifecycleOwner) {
                currentRoom?.let { room -> updateUserState(room) }
            }
            stateToastInsertShareFlow.collect(fragment.viewLifecycleOwner) { index ->
                mToastAdapter.notifyItemInserted(index)
            }
            stateToastRemoveShareFlow.collect(fragment.viewLifecycleOwner) { index ->
                mToastAdapter.notifyItemRemoved(index)
            }
            stateToastNotifyDataSetChangedShareFlow.collect(fragment.viewLifecycleOwner) {
                mToastAdapter.notifyDataSetChanged()
            }
            myMicStatus.collectLatestIn(fragment.viewLifecycleOwner) { myMicStatus ->
                updateMyMuteState(myMicStatus)
            }
            myCamStatus.collectLatestIn(fragment.viewLifecycleOwner) { myCamStatus ->
                val cameraStatus = myCamStatus ?: return@collectLatestIn
                //检查对象是否为空
                updateMyCameraStatus(cameraStatus,currentRoom?.rtcVcResManager!!)
            }
            myNetworkStatus.collectLatestIn(fragment.viewLifecycleOwner) { myNetworkStatus ->
                updateMyNetwork(myNetworkStatus)
            }
            userMicStatus.collectLatestIn(fragment.viewLifecycleOwner) { user ->
                updateUserMuteState(user)
            }
            userCamStatus.collectLatestIn(fragment.viewLifecycleOwner) { user ->
                updateUserCameraStatus(user)
            }
            userNetworkStatus.collectLatestIn(fragment.viewLifecycleOwner) { user ->
                updateUserNetwork(user)
            }
        }
    }

    private fun initToastRecyclerView() {
        mToastAdapter = MultiTypeAdapter(videoCallViewModel.stateToastInformationList).apply {
            register(RealTimeCallStateToastItemView())
        }
        binding.rvStatusContainer.apply {
            layoutMarginBottom(fragment.requireContext.navigationBarHeight + 101.dp)
            isNestedScrollingEnabled = false
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false).apply {
                stackFromEnd = true      // Items start from the bottom
                reverseLayout = false    // Keeps the normal item order
            }
            adapter = mToastAdapter
        }
    }

    private fun handleSelfCameraInit() {
        val shouldOpenCamera = when (onlineChatJumpInfo.jumpType) {
            OnlineChatJumpType.reentryFromMinimize -> CameraStatus.isOpen(VoiceCallPortal.currentRoomValue?.myCamStatus?.value)
            OnlineChatJumpType.joinChannel -> CameraStatus.isOpen(realTimeCallPendState?.cameraStatus)
            else -> isVideoCall
        }
        if (shouldOpenCamera) {
            binding.flPersonalVideoContainer.apply {
                startCameraCaptureNew(true, "$TAG\$initView",VoiceCallPortal.currentRoomValue!!.rtcVcResManager)
                currentRoom?.rtcVcResManager?.addCameraEventHandler(this@UserVoiceCallStateBlock)
            }
        }
    }

    override fun onResume() {
        super.onResume()
        videoCallViewModel.updateUserState()
    }

    override fun doOnRoomExist(room: VoiceCallRoom) {
        super.doOnRoomExist(room)
        handleSelfCameraInit()

        updateUserState(room)
        room.apply {
            myCamState.collectLatestIn(fragment.viewLifecycleOwner) {myCamStateValue->
                room.rtcVcResManager.switchCamera(CameraState.isFront(myCamStateValue))
                binding.flPersonalVideoContainer.switchCameraAnim( videoCallViewModel.getMyTextureViewState() == TextureViewState.COLLAPSE)
            }
            registerMicStatusListener(fragment.viewLifecycleOwner, micStatusListener)
            registerCamStatusListener(fragment.viewLifecycleOwner, camStatusListener)
            registerNetworkStatusListener(fragment.viewLifecycleOwner, networkStatusListener)
        }
    }

    override fun onRoomWaiting(room: VoiceCallRoom) {
        super.onRoomWaiting(room)
        setRoomState(room)
    }

    private fun updateUserState(room: VoiceCallRoom) {
        if (!is1v1) return
        room.apply {
            members.value.filter { it.isOnCall() }.forEach { user ->
                if (user.userId.isMe()) {
                    val isCameraOn = CameraStatus.isOpen(myCamStatus.value)
                    val isMuted = MicStatus.isMicMuted(myMicStatus.value)
                    val isPoorNetwork = NetworkStatus.isBadNetwork(myNetwork.value)
                    videoCallViewModel.updateMyCamStatus(isCameraOn)
                    videoCallViewModel.updateMyMicStatus(isMuted)
                    videoCallViewModel.updateMyNetworkState(isPoorNetwork)
                } else {
                    videoCallViewModel.updateUserMicStatus(user)
                    videoCallViewModel.updateUserCamStatus(user)
                    videoCallViewModel.updateUserNetworkState(user)
                }
            }
        }
    }

    private fun updateUserMuteState(user: CallRoomUser) {
        val isMuted = MicStatus.isMicMuted(user.micStatus)
        binding.flTargetVideoContainer.updateMuteStatus(isMuted)
        val targetTextureViewState = videoCallViewModel.getTargetTextureViewState()
        val showToast = targetTextureViewState == TextureViewState.EXPAND
                && isMuted
                && is1v1
                && roomLifecycle?.isAtLeast(RoomLifecycle.CONNECTING) == true
        val info = StateToastInformation (
            toastStyle = RealTimeCallToastInfo.TARGET_MICROPHONE_OFF,
            username = user.contactFirstName
        )
        if (showToast) {
            videoCallViewModel.addStateToastInformation(info)
        } else {
            videoCallViewModel.removeStateToastInformation(info)
        }
    }

    private fun updateMyMuteState(isMuted: Boolean) {
        binding.flPersonalVideoContainer.updateMuteStatus(isMuted)
        val personalTextureViewState = videoCallViewModel.getMyTextureViewState()
        val showToast = personalTextureViewState == TextureViewState.EXPAND
                && isMuted
                && is1v1
                && binding.flPersonalVideoContainer.isVisible()
                && roomLifecycle?.isAtLeast(RoomLifecycle.CONNECTING) == true
        val info = StateToastInformation(
            toastStyle = RealTimeCallToastInfo.MY_MICROPHONE_OFF,
        )
        if (showToast) {
            videoCallViewModel.addStateToastInformation(info)
        } else {
            videoCallViewModel.removeStateToastInformation(info)
        }
    }

    private fun updateUserNetwork(user: CallRoomUser) {
        val isPoorNetwork = NetworkStatus.isBadNetwork(user.netWorkStatus)
        binding.flTargetVideoContainer.updateNetworkStatus(isPoorNetwork)
        val targetTextureViewState = videoCallViewModel.getTargetTextureViewState()
        val showToast = targetTextureViewState == TextureViewState.EXPAND
                && isPoorNetwork
                && is1v1
                && roomLifecycle?.isAtLeast(RoomLifecycle.CONNECTING) == true
        val info = StateToastInformation (
            toastStyle = RealTimeCallToastInfo.TARGET_POOR_NETWORK,
            username = user.contactFirstName
        )
        if (showToast) {
            videoCallViewModel.addStateToastInformation(info)
        } else {
            videoCallViewModel.removeStateToastInformation(info)
        }
    }

    private fun updateMyNetwork(isPoorNetwork: Boolean) {
        binding.flPersonalVideoContainer.updateNetworkStatus(isPoorNetwork)
        videoCallViewModel.getMyTextureViewState()
        val info = StateToastInformation(
            toastStyle = RealTimeCallToastInfo.MY_POOR_NETWORK,
        )
        val showToast = isPoorNetwork
                && is1v1
                && roomLifecycle?.isAtLeast(RoomLifecycle.CONNECTING) == true
        if (showToast) {
            videoCallViewModel.addStateToastInformation(info)
        } else {
            videoCallViewModel.removeStateToastInformation(info)
        }
    }

    private fun updateUserCameraStatus(user: CallRoomUser) {
        val isCameraOpen = CameraStatus.isOpen(user.cameraStatus)
        logDebug(TAG, "updateUserCameraStatus: $isCameraOpen, isInPipMode: $isInPipMode, is1v1: $is1v1")
        if(isInPipMode || !is1v1 || user.callStatus != CallStatus.ON_CALL) return
        binding.flTargetVideoContainer.apply {
            showTextureViewIf(isCameraOpen)
        }
    }

    private fun updateMyCameraStatus(isCameraOpen: Boolean, rtcVcResManager: RoomVCResManager) {
        logInfo(TAG,"updateMyCameraStatus isCameraOpen=${isCameraOpen}, isInPipMode=${isInPipMode}",
            logLine = LogLine.MY_RTC_CAMERA_STATE)
        binding.flPersonalVideoContainer.apply {
            if (isCameraOpen) {
//                startCameraCapture(isFront = isCameraFront, from = "updateMyCameraStatus",rtcVcResManager)
                startCameraCaptureNew(isFront = isCameraFront, from = "updateMyCameraStatus",rtcVcResManager)
                rtcVcResManager.addCameraEventHandler(this@UserVoiceCallStateBlock)
                visible()
            } else {
                rtcVcResManager.removeCameraEventHandler(this@UserVoiceCallStateBlock)
                if (videoCallViewModel.getTargetTextureViewState() == TextureViewState.COLLAPSE) {
                    videoCallViewModel.switchTextureViewState()
                }
                invisible()
                if (!videoCallViewModel.seatsVisibleFlow.value){
                    stopCameraCapture(
                        from = "updateMyCameraStatus",
                        roomVCResManager = rtcVcResManager
                    )
                    //打个补丁，现在没啥办法
                    if (roomLifecycle?.isAtLeast(RoomLifecycle.CONNECTED) != true) {
                        binding.ivGroupPortrait.visible()
                    }
                }
            }
        }
    }

    private fun setRoomState(room: VoiceCallRoom) {
        room.apply {
            val cameraStatus = realTimeCallPendState?.cameraStatus ?: myCamStatus.value
            val cameraState = realTimeCallPendState?.cameraState ?: myCamState.value
            val micStatus = realTimeCallPendState?.micStatus ?: myMicStatus.value
            logDebug(TAG, "setRoomState: cameraStatus = $cameraStatus, cameraState = $cameraState, micStatus = $micStatus")
            roomChannelId?.let {roomChannelId->
                changeMyCamStatus(roomChannelId, cameraStatus, from = "onRoomWaiting setRoomState")
                changeCamState(cameraState)
                changeMyMicStatus(roomChannelId, micStatus)
            }
        }
    }

    override fun onCameraCaptureStarted() {
        binding.flPersonalVideoContainer.cameraCaptureStartedReact()
    }

    override fun onCameraDeviceChanged(isFront: Boolean) {
       binding.flPersonalVideoContainer.cameraDeviceChangedReact()
    }

    override fun onCameraCaptureStopped(reason: CameraStopReason?) {
        binding.flPersonalVideoContainer.cameraCaptureStoppedReact(reason)
    }
}