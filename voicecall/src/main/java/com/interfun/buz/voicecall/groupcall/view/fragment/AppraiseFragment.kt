package com.interfun.buz.voicecall.groupcall.view.fragment

import android.app.Activity
import android.os.Bundle
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.text.BasicText
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.ContentDrawScope
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.node.DrawModifierNode
import androidx.compose.ui.node.ModifierNodeElement
import androidx.compose.ui.node.invalidateDraw
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextLayoutResult
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.interfun.buz.base.ktx.OneParamCallback
import com.interfun.buz.base.ktx.toast
import com.interfun.buz.common.bean.feedback.H5FeedbackSource
import com.interfun.buz.common.bean.voicecall.CallRoomInfoBean
import com.interfun.buz.common.constants.KEY_SOURCE
import com.interfun.buz.common.constants.RouterParamKey
import com.interfun.buz.common.database.entity.UserRelationInfo
import com.interfun.buz.common.database.entity.chat.GroupInfoBean
import com.interfun.buz.common.manager.AppConfigRequestManager
import com.interfun.buz.common.manager.FeedbackManager
import com.interfun.buz.common.manager.router.converter.WebViewRouterArgs
import com.interfun.buz.common.web.WebViewActivity
import com.interfun.buz.compose.R
import com.interfun.buz.compose.base.BaseComposeFragment
import com.interfun.buz.compose.components.IconFontText
import com.interfun.buz.compose.components.InitPreview
import com.interfun.buz.compose.components.PortraitImage
import com.interfun.buz.compose.ktx.asColor
import com.interfun.buz.compose.styles.TextStyles
import com.interfun.buz.onair.helper.SeatBgPaletteHelper
import com.interfun.buz.voicecall.util.VoiceCallTracker
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@Composable
@Preview
private fun VoiceCallAppraisePreview() {
    InitPreview()
//    VoiceCallAppraise(userRelationInfo, groupInfoBean)
}


/**
 * 用户在非主动结束房间的情况下且已经完成链接那么弹出评分界面
 */
//@Route(path = PATH_FRAGMENT_APPRAISE)
@AndroidEntryPoint
class AppraiseFragment : BaseComposeFragment() {

    val userRelationInfo: UserRelationInfo? by lazy {
        arguments?.getParcelable(RouterParamKey.VC.KEY_USER_INFO)
    }

    val groupInfoBean: GroupInfoBean? by lazy {
        arguments?.getParcelable(RouterParamKey.VC.KEY_GROUP_INFO)
    }

    val roomInfoBean: CallRoomInfoBean? by lazy {
        arguments?.getParcelable(RouterParamKey.VC.KEY_ROOM_INFO)
    }
    val h5FeedbackSource by lazy {
        arguments?.getInt(RouterParamKey.VC.KEY_H5_FEEDBACK_SOURCE,RouterParamKey.Feedback.FEEDBACK_SOURCE_UN_KNOWN) ?: RouterParamKey.Feedback.FEEDBACK_SOURCE_UN_KNOWN
    }


    @Composable
    fun Modifier.voiceCallBg(portrait: String?): Modifier {
        val overlayColor = colorResource(R.color.color_overlay_black_medium)
        return this.then(
            VCCallBgNodeElement(portrait, overlayColor,colorResource(R.color.color_background_3_default))
        )
    }
    data class VCCallBgNodeElement(
        val portrait: String?,
        val overlayColor: Color,
        val nullOverlayColor:Color
    ) : ModifierNodeElement<VCCallBgNode>() {
        override fun create(): VCCallBgNode {
            return VCCallBgNode(portrait, overlayColor,nullOverlayColor)
        }

        override fun update(node: VCCallBgNode) {
            node.overlayColor = overlayColor
            node.nullOverlayColor = nullOverlayColor
            node.updatePortrait(portrait)
        }
    }

    data class VCCallBgNode(
        var portrait: String?,
        var overlayColor: Color,
        var nullOverlayColor: Color
    ) : Modifier.Node(), DrawModifierNode {

        private var bgColor by mutableStateOf(Color.Transparent)
        private var fetchJob: Job? = null

        override fun onAttach() {
            super.onAttach()
            fetchColor()
        }

        fun updatePortrait(newPortrait: String?) {
            if (newPortrait != portrait) {
                portrait = newPortrait
                fetchColor()
            }
        }

        private fun fetchColor() {
            fetchJob?.cancel()
            fetchJob = coroutineScope.launch {
                val portraitBackup = portrait
                if (portraitBackup.isNullOrEmpty()) {
                    bgColor= Color.Red
                }else{
                    val seatColor = SeatBgPaletteHelper.obtainColorFromRelation(portraitBackup)
                    bgColor = Color(seatColor)
                }
                invalidateDraw()
            }
        }

        override fun ContentDrawScope.draw() {
            if (portrait?.isNotEmpty()==true) {
                drawRect(color = bgColor)
                drawRect(color = overlayColor)
            }else{
                drawRect(color = nullOverlayColor)

                // Replicate the XML radial gradient using Pair<Float, Color> for colorStops
                val gradientBrush = Brush.radialGradient(
                    // Pass color stops as vararg Pair arguments
                    0.0f to Color(0x1A_C4_F3_53), // Stop 0: #1AC4F353
                    0.5f to Color(0x1A_F3_FF_A8), // Stop 0.5: #1AF3FFA8
                    1.0f to Color(0x00_F3_FF_A8), // Stop 1: #00F3FFA8
                    center = Offset(size.width * 1.4893f, size.height * 1.0f),
                    radius = maxOf(size.width, size.height) * 1.8f // 180% of the larger dimension
                    // No separate 'colors' or 'colorStops' list arguments
                )
                drawRect(brush = gradientBrush)
            }
            drawContent()
        }
    }

    @Composable
    fun AppriseItem(
        icon: Int,
        title: String,
        modifier: Modifier = Modifier,
        onClick: () -> Unit = {}
    ) {
        Column(modifier = modifier) {
            Box(
                modifier = Modifier
                    .background(
                        colorResource(R.color.alpha_white_20),
                        shape = CircleShape
                    )
                    .align(Alignment.CenterHorizontally)
                    .size(52.dp)
                    .clip(CircleShape)
                    .clickable(onClick=onClick)
            ) {
                IconFontText(
                    modifier = Modifier
                        .align(Alignment.Center),
                    iconRes = icon,
                    iconSize = 24.dp,
                    iconColor = R.color.color_text_white_important.asColor(),
                    size = 24.dp,
                )
            }
            Spacer(modifier = Modifier.size(10.dp))
            Text(
                text = title,
                maxLines = 1,
                style = TextStyles.labelMedium(),
                overflow = TextOverflow.Ellipsis,
                modifier = Modifier
                    .align(Alignment.CenterHorizontally),
                color = colorResource(R.color.alpha_white_60),
            )
        }
    }

    @Composable
    private fun VoiceCallAppraise(
        userRelationInfo: UserRelationInfo?,
        groupInfoBean: GroupInfoBean?,
        onFeedbackClick: OneParamCallback<Int>,
        onOpenWeb: () -> Unit
    ) {

        val bgColor = remember { mutableStateOf(Color.Transparent) }
        LaunchedEffect(Unit) {
            val portrait = userRelationInfo?.portrait
            if (portrait != null) {
                val seatColor = SeatBgPaletteHelper.obtainColorFromRelation(portrait)
                bgColor.value = Color(seatColor)
            }
        }


        Box(modifier = Modifier
            .fillMaxSize()
            .voiceCallBg(userRelationInfo?.portrait)) {
            Column(
                modifier = Modifier
                    .statusBarsPadding()
                    .fillMaxSize()
            ) {
                IconFontText(
                    iconRes = R.string.ic_exit,
                    iconSize = 24.dp,
                    iconColor = R.color.color_text_white_important.asColor(),
                    size = 64.dp,
                    onClick = {
                        finishWithSlideDown(activity)
                    }
                )
                Spacer(modifier = Modifier.height(60.dp))

                if (groupInfoBean != null) {
                    PortraitImage(
                        groupInfoBean = groupInfoBean,
                        modifier = Modifier
                            .align(Alignment.CenterHorizontally)
                            .size(140.dp)
                    )
                }

                if (userRelationInfo != null) {
                    PortraitImage(
                        data = userRelationInfo.portrait,
                        modifier = Modifier
                            .align(Alignment.CenterHorizontally)
                            .size(140.dp)
                    )
                }

                Spacer(modifier = Modifier.size(30.dp))
                Text(
                    text = stringResource(R.string.evaluate_page_call_end),
                    maxLines = 3,
                    style = TextStyles.titleLarge(),
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier
                        .padding(horizontal = 20.dp)
                        .align(Alignment.CenterHorizontally),
                    color = colorResource(R.color.color_text_white_important),
                )
                Spacer(modifier = Modifier.weight(1f))
                Column(modifier = Modifier.fillMaxWidth()) {
                    Text(
                        text = stringResource(R.string.quality_of_call),
                        maxLines = 3,
                        style = TextStyles.bodyLarge(),
                        overflow = TextOverflow.Ellipsis,
                        textAlign = TextAlign.Center,
                        modifier = Modifier
                            .padding(horizontal = 20.dp)
                            .align(Alignment.CenterHorizontally),
                        color = colorResource(R.color.color_text_white_important),
                    )
                    Spacer(modifier = Modifier.size(30.dp))
                    Row(
                        modifier = Modifier
                            .fillMaxWidth(), horizontalArrangement = Arrangement.Center
                    ) {
                        AppriseItem(
                            R.string.ic_thumb_up,
                            stringResource(id = com.interfun.buz.common.R.string.call_evaluate_good),
                            onClick = {
                                onFeedbackClick(1)
                                toast(R.string.call_evaluate_feedback_thanks_toast)
                                finishWithSlideDown(activity)
                            })
                        Spacer(modifier = Modifier.size(60.dp))
                        AppriseItem(
                            R.string.ic_thumb_down,
                            stringResource(id = com.interfun.buz.common.R.string.call_evaluate_not_good),
                            onClick = {
                                onFeedbackClick(0)
                                finishWithSlideDown(activity)
                                FeedbackManager.jumpToFeedbackWebView(activity, h5FeedbackSource)
                            })
                    }
                }
                Spacer(modifier = Modifier.weight(1f))
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .align(Alignment.CenterHorizontally),
                    contentAlignment = Alignment.Center
                ) {

                    val clickablePart = stringResource(R.string.call_evaluate_privacy_policy) // "隐私协议"
                    val fullText = stringResource(R.string.call_evaluate_bottom_tip,clickablePart) // "你好好欢迎使用,请点击查阅隐私协议"

                    val clickableStartIndex = fullText.indexOf(clickablePart)
                    // 构建带注解的字符串
                    val annotatedString = buildAnnotatedString {
                        append(fullText)

                        // 为可点击部分添加注解和样式
                        if (clickableStartIndex >= 0) {
                            addStyle(
                                style = SpanStyle(
                                    color = colorResource(R.color.blue_100),
//                                textDecoration = TextDecoration.Underline
                                ),
                                start = clickableStartIndex,
                                end = clickableStartIndex + clickablePart.length
                            )

                            // 添加点击注解
                            addStringAnnotation(
                                tag = "CLICKABLE",
                                annotation = "privacy_policy", // 可以是URL或其他标识符
                                start = clickableStartIndex,
                                end = clickableStartIndex + clickablePart.length
                            )
                        }
                    }

                    val layoutResult = remember { mutableStateOf<TextLayoutResult?>(null) }

                    BasicText(
                        text = annotatedString,
                        style = TextStyles.bodySmall()
                            .copy(color = colorResource(R.color.color_text_black_tertiary), textAlign = TextAlign.Center),
                        onTextLayout = { layoutResult.value = it },
                        modifier = Modifier.padding(horizontal = 20.dp).pointerInput(Unit) {
                            detectTapGestures { offset ->
                                layoutResult.value?.let { textLayoutResult ->
                                    val position = textLayoutResult.getOffsetForPosition(offset)
                                    annotatedString.getStringAnnotations(
                                        tag = "CLICKABLE",
                                        start = 0,
                                        end = annotatedString.length
                                    ).find { it.start <= position && position <= it.end }?.let {
                                        // 处理点击事件 打开H5
                                        activity?.let { act ->
                                            onOpenWeb()
                                            openWeb(act, AppConfigRequestManager.privacyPolicyUrl)
                                        }
                                    }
                                }
                            }
                        }
                    )
                }
                Spacer(modifier = Modifier.size(44.dp))
            }
        }
    }



    private fun openWeb(context: Activity, url: String, showNativeTitleBar: Boolean? = null) {
        if (showNativeTitleBar != null) {
            WebViewActivity.start(
                context,
                WebViewRouterArgs(url, showNativeTitleBar = showNativeTitleBar)
            )
        } else {
            WebViewActivity.start(context, url)
        }
        context.overridePendingTransition(R.anim.anim_nav_enter, R.anim.anim_nav_exit)

    }

    // 定义新的私有函数
    private fun finishWithSlideDown(activity: Activity?) {
        activity?.finish()
        activity?.overridePendingTransition(0, R.anim.anim_slide_out_bottom)
    }

    @Composable
    override fun ComposeContent() {
        val webOpened = remember { mutableStateOf(false) }

        LaunchedEffect(Unit) {
            delay(8 * 1000L)
            if (!webOpened.value) {
                activity?.let { act ->
                    finishWithSlideDown(act)
                }
            }
        }
        VoiceCallAppraise(userRelationInfo, groupInfoBean, onFeedbackClick = { rating ->
            VoiceCallTracker.resultBackRB2025031206(
                channelType = roomInfoBean?.channelType ?: 0,
                callType = roomInfoBean?.callType ?: 0,
                channelId = roomInfoBean?.channelId ?: 0,
                memberCount = roomInfoBean?.memberCount ?: 1,
                hasOpenedCamera = roomInfoBean?.hasOpenedCamera == true,
                duration = roomInfoBean?.duration ?: 0,
                rating = rating,
                endReason = roomInfoBean?.reason ?: ""
            )
        }, onOpenWeb = {
            webOpened.value = true
        })
    }

    companion object {

        /**
         * @param source see[H5FeedbackSource]
         */
        fun newInstance(
            userRelationInfo: UserRelationInfo?,
            groupInfoBean: GroupInfoBean?,
            roomInfoBean: CallRoomInfoBean?,
            h5FeedbackSource: Int?,
        ): AppraiseFragment {
            if (userRelationInfo == null && groupInfoBean == null) {
                throw IllegalArgumentException("userRelationInfo and groupInfoBean can not be null at the same time")
            }
            val args = Bundle()
            args.putParcelable(RouterParamKey.VC.KEY_USER_INFO, userRelationInfo)
            args.putParcelable(RouterParamKey.VC.KEY_GROUP_INFO, groupInfoBean)
            args.putParcelable(RouterParamKey.VC.KEY_ROOM_INFO, roomInfoBean)
            h5FeedbackSource?.let {
                args.putInt(RouterParamKey.VC.KEY_H5_FEEDBACK_SOURCE,h5FeedbackSource)
            }
            val fragment = AppraiseFragment()
            fragment.arguments = args
            return fragment
        }
    }
}