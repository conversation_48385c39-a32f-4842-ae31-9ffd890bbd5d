<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/clRootLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_background_1_default"
    android:splitMotionEvents="false">

    <Space
        android:id="@+id/spaceStatusBar"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_height="44dp" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/titleBar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/title_bar_new_height"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/spaceStatusBar">

        <com.interfun.buz.common.widget.view.IconFontTextView
            android:id="@+id/iftvLeftBack"
            style="@style/iconfont_24"
            android:layout_width="64dp"
            android:layout_height="0dp"
            android:text="@string/ic_back"
            android:textColor="@color/color_foreground_neutral_important_default"
            android:textSize="24sp"
            app:autoRTL="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvTitle"
            style="@style/text_title_medium"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/select_members"
            android:textColor="@color/color_foreground_neutral_important_default"
            app:layout_constraintBottom_toTopOf="@+id/tvMemberCount"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed" />

        <TextView
            android:id="@+id/tvMemberCount"
            style="@style/text_body_medium"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tvTitle"
            android:gravity="center"
            android:paddingBottom="5dp"
            android:textColor="@color/alpha_white_60"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvTitle"
            app:layout_constraintVertical_chainStyle="packed"
            tools:text="0/20" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clSearchBar"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:paddingHorizontal="20dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/titleBar">

        <EditText
            android:id="@+id/etSearch"
            style="@style/body"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_gravity="center_vertical"
            android:autofillHints="username"
            android:background="@drawable/common_rect_background_3_default_radius_16"
            android:gravity="start|center_vertical"
            android:hint="@string/search_your_friends_and_group"
            android:imeOptions="actionDone"
            android:inputType="text"
            android:maxLength="50"
            android:maxLines="1"
            android:paddingStart="48dp"
            android:paddingEnd="10dp"
            android:textColor="@color/text_white_main"
            android:textColorHint="@color/text_white_disable"
            android:textCursorDrawable="@drawable/common_edittext_cursor"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/tvCancel"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.interfun.buz.common.widget.view.IconFontTextView
            android:id="@+id/iftvSearch"
            style="@style/iconfont_base"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="20dp"
            android:text="@string/ic_search_input"
            android:textColor="@color/text_white_secondary"
            android:textSize="18dp"
            app:layout_constraintBottom_toBottomOf="@+id/etSearch"
            app:layout_constraintStart_toStartOf="@+id/etSearch"
            app:layout_constraintTop_toTopOf="@+id/etSearch"
            app:pressEffect="false" />

        <com.interfun.buz.common.widget.view.IconFontTextView
            android:id="@+id/iftvClear"
            style="@style/iconfont_base"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:gravity="center"
            android:text="@string/ic_clear_input_solid"
            android:textColor="@color/text_white_secondary"
            android:textSize="18dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/etSearch"
            app:layout_constraintEnd_toEndOf="@+id/etSearch"
            app:layout_constraintTop_toTopOf="@+id/etSearch"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tvCancel"
            style="@style/body"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:gravity="center"
            android:paddingStart="10dp"
            android:text="@string/cancel"
            android:textColor="@color/text_white_default"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/etSearch"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/etSearch"
            tools:visibility="visible" />


    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvSelectedMembers"
        android:layout_width="match_parent"
        android:layout_height="80dp"
        android:layout_marginStart="13dp"
        android:layout_marginEnd="3dp"
        app:layout_constraintEnd_toEndOf="@+id/clSearchBar"
        app:layout_constraintStart_toStartOf="@+id/clSearchBar"
        app:layout_constraintTop_toBottomOf="@+id/clSearchBar" />

    <View
        android:id="@+id/viewDivider"
        android:layout_width="0dp"
        android:layout_height="0.5dp"
        android:layout_marginHorizontal="20dp"
        android:background="@color/color_outline_1_default"
        app:layout_constraintBottom_toBottomOf="@+id/rvSelectedMembers"
        app:layout_constraintEnd_toEndOf="@+id/rvSelectedMembers"
        app:layout_constraintStart_toStartOf="@+id/rvSelectedMembers" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rlvContact"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/rvSelectedMembers" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clLoading"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="@id/rlvContact"
        app:layout_constraintEnd_toEndOf="@id/rlvContact"
        app:layout_constraintStart_toStartOf="@id/rlvContact"
        app:layout_constraintTop_toTopOf="@id/rlvContact">

        <com.interfun.buz.common.widget.view.loading.CircleLoadingView
            android:id="@+id/listLoading"
            android:layout_width="20dp"
            android:layout_height="20dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tvLoading"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="14dp"
            android:letterSpacing="0.03"
            android:text="@string/loading"
            android:textColor="@color/text_white_main"
            android:textSize="16sp"
            app:layout_constraintBottom_toBottomOf="@id/listLoading"
            app:layout_constraintStart_toEndOf="@id/listLoading"
            app:layout_constraintTop_toTopOf="@id/listLoading" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:id="@+id/llReloadLayout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/rlvContact"
        app:layout_constraintEnd_toEndOf="@id/rlvContact"
        app:layout_constraintStart_toStartOf="@id/rlvContact"
        app:layout_constraintTop_toTopOf="@id/rlvContact"
        tools:visibility="visible">

        <com.interfun.buz.common.widget.view.IconFontTextView
            android:id="@+id/iftReload"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:background="@drawable/add_channel_reload_background"
            android:gravity="center"
            android:text="@string/ic_refresh"
            android:textColor="@color/basic_vanta"
            android:textSize="20dp" />

        <TextView
            style="@style/regular"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:text="@string/click_to_retry"
            android:textColor="@color/text_white_secondary" />
    </LinearLayout>

    <com.interfun.buz.common.widget.button.CommonButton
        android:id="@+id/startCallBtn"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="40dp"
        android:layout_marginEnd="40dp"
        android:layout_marginBottom="34dp"
        app:disable="true"
        app:iconFont="@string/ic_video"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:text="@string/video_start"
        app:type="primary_larger" />


</androidx.constraintlayout.widget.ConstraintLayout>