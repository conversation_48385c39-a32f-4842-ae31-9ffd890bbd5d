<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:keepScreenOn="true"
    android:id="@+id/clPrivateVoiceCall"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/alpha_black_100">

    <View
        android:id="@+id/viewBgMask"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        tools:background="@drawable/voicecall_groupcall_background"/>

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupRoomEnded"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="userPortraitIV, tvUserName, tvStatus"
        tools:visibility="gone"/>

    <com.interfun.buz.common.widget.view.PortraitImageView
        android:id="@+id/userPortraitIV"
        android:layout_width="140dp"
        android:layout_height="140dp"
        android:layout_marginTop="60dp"
        android:background="@drawable/common_user_default_portrait_round"
        app:layout_constraintDimensionRatio="1.0"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/vcTitleContainer" />

    <TextView
        android:id="@+id/tvUserName"
        style="@style/text_title_large"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="30dp"
        android:layout_marginStart="40dp"
        android:layout_marginEnd="40dp"
        android:gravity="center"
        android:ellipsize="end"
        android:textColor="@color/text_white_important"
        android:textSize="24sp"
        app:layout_constrainedWidth="true"
        app:layout_constraintTop_toBottomOf="@+id/userPortraitIV"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:text="Anna Huang" />

    <TextView
        android:id="@+id/tvStatus"
        style="@style/text_body_large"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:gravity="center"
        android:textColor="@color/alpha_white_60"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvUserName"
        tools:text="No Answer"/>

    <com.interfun.buz.common.widget.view.PortraitImageView
        android:id="@+id/ivGroupPortrait"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/common_user_default_portrait_round"
        app:layout_constraintWidth_percent="0.6"
        app:layout_constraintWidth_max="200dp"
        app:layout_constraintHeight_max="200dp"
        app:layout_constraintDimensionRatio="1.0"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <com.interfun.buz.common.widget.view.UnTouchableRecyclerView
        android:id="@+id/rvCallList"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone"
        android:background="@color/alpha_black_100"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <com.interfun.buz.voicecall.common.view.widget.DraggableRealTimeCallUserCard
        android:id="@+id/flTargetVideoContainer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:visibility="invisible"
        />

    <com.interfun.buz.voicecall.common.view.widget.DraggableRealTimeCallUserCard
        android:id="@+id/flPersonalVideoContainer"
        android:layout_width="match_parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_height="match_parent"
        android:visibility="invisible"
        />

    <View
        android:id="@+id/viewMyCameraMask"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        android:background="@drawable/voicecall_pending_call_gradient_mask"/>

    <View
        android:id="@+id/vShadowBackground"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@drawable/voicecall_on_call_gradient_mask"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHeight_percent="0.5"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.0"
        tools:visibility="gone" />

    <View
        android:id="@+id/spaceContent"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="gone" />

    <com.interfun.buz.common.widget.view.UnTouchableRecyclerView
        android:id="@+id/rvStatusContainer"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginBottom="101dp"
        android:elevation="30dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/vcTitleContainer"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        tools:listitem="@layout/voicecall_state_toast_item_view"
        tools:itemCount="2"/>

    <include
        android:id="@+id/vcTitleContainer"
        layout="@layout/voicecall_top_title_bar"/>

    <include
        android:id="@+id/vcButtonContainer"
        layout="@layout/voicecall_bottom_controller_panel"/>


    <FrameLayout
        android:layout_width="match_parent"
        android:id="@+id/showAppraiseContain"
        android:layout_height="match_parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>