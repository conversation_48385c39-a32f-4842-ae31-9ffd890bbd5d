<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="186dp"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:background="#1F1F1F"
    android:paddingTop="20dp">

    <com.interfun.buz.common.widget.view.RoundIconFontView
        android:id="@+id/iftvEnd"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_centerInParent="true"
        android:layout_marginTop="40dp"
        android:background="@color/color_background_consequential_default"
        android:gravity="center"
        android:text="@string/ic_invite_denied"
        android:textColor="@color/white"
        android:textSize="30sp"
        android:visibility="visible"
        app:round_radius="40dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- TextView for End Call Label -->
    <TextView
        android:id="@+id/endButtonLabel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="8dp"
        android:text="End"
        android:textColor="#666666"
        android:textSize="12sp"
        android:visibility="visible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iftvEnd" />

    <!-- IconFontTextView for Dismiss Call Label -->

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftvDismiss"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_marginTop="40dp"
        android:layout_centerInParent="true"
        android:text="@string/ic_exit"
        android:textColor="@color/white"
        android:background="@drawable/chat_selector_common_background"
        android:gravity="center"
        android:textSize="30sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:visibility="gone"/>

    <!-- TextView for Dismiss Call Label -->
    <TextView
        android:id="@+id/dismissButtonLabel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="8dp"
        android:text="Cancel"
        android:textColor="#666666"
        android:textSize="12sp"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@+id/iftvDismiss"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <!--HIHIHI-->
    <!-- Cancel Button -->
    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftvCancel"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_centerInParent="true"
        android:layout_marginStart="58dp"
        android:layout_marginTop="40dp"
        android:layout_marginEnd="258dp"
        android:background="@drawable/chat_selector_common_background"
        android:gravity="center"
        android:text="@string/ic_exit"
        android:textColor="@color/white"
        android:textSize="30sp"
        android:visibility="visible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvCancelLabel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:gravity="center"
        android:text="Cancel"
        android:textColor="#666666"
        android:textSize="12sp"
        android:visibility="visible"
        app:layout_constraintEnd_toEndOf="@id/iftvCancel"
        app:layout_constraintStart_toStartOf="@id/iftvCancel"
        app:layout_constraintTop_toBottomOf="@id/iftvCancel" />

    <!-- Call Again Button -->


    <com.interfun.buz.common.widget.view.RoundIconFontView
        android:id="@+id/iftvCallAgain"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_centerInParent="true"
        android:layout_marginStart="258dp"
        android:layout_marginTop="40dp"
        android:layout_marginEnd="58dp"
        android:background="@color/overlay_grey_20"
        android:gravity="center"
        android:text="@string/ic_tel"
        android:textColor="@color/white"
        android:textSize="30sp"
        android:visibility="visible"
        app:round_radius="30dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvCallAgainLabel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Call Again"
        android:textSize="12sp"
        android:textColor="#666666"
        app:layout_constraintTop_toBottomOf="@id/iftvCallAgain"
        app:layout_constraintStart_toStartOf="@id/iftvCallAgain"
        app:layout_constraintEnd_toEndOf="@id/iftvCallAgain"
        android:gravity="center"
        android:layout_marginTop="8dp"
        android:visibility="visible"/>


</androidx.constraintlayout.widget.ConstraintLayout>