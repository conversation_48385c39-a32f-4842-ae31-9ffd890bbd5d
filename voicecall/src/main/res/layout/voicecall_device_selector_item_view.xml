<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="64dp"
    android:background="@drawable/common_bg_dialog_bottom_list"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingHorizontal="20dp"
    tools:background="@color/black_90">

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftvDialogItemIcon"
        style="@style/iconfont_base"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/text_white_main"
        android:textSize="22sp"
        android:text="@string/ic_sound_open"
        app:if_enablePressEffect="false"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <TextView
        android:id="@+id/tvDialogItemTitle"
        style="@style/main_body"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="start"
        android:maxLines="2"
        android:textColor="@color/text_white_main"
        android:textSize="16sp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        app:layout_constrainedWidth="true"
        app:layout_constraintStart_toEndOf="@+id/iftvDialogItemIcon"
        app:layout_constraintEnd_toStartOf="@+id/iftvSelect"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:text="item listdddd" />

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftvSelect"
        style="@style/iconfont_base"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="20dp"
        android:text="@string/ic_check"
        android:textColor="@color/basic_primary"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:if_enablePressEffect="false" />

    <ImageView
        android:id="@+id/ivLoading"
        android:layout_width="22dp"
        android:layout_height="22dp"
        android:layout_marginEnd="20dp"
        android:visibility="gone"
        android:src="@drawable/common_loading_small"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:visibility="visible"/>

</androidx.constraintlayout.widget.ConstraintLayout>