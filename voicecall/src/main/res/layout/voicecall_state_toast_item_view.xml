<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/vMyNetworkPoorStateBackground"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="6dp"
    android:layout_marginHorizontal="20dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:minHeight="30dp">

        <com.interfun.buz.base.widget.round.RoundView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@color/alpha_black_60"
            app:round_radius="30dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <com.interfun.buz.common.widget.view.PortraitImageView
            android:id="@+id/ivPortrait"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginStart="4dp"
            android:layout_marginEnd="8dp"
            android:layout_marginVertical="5dp"
            android:background="@drawable/common_user_default_portrait_round"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@id/iconBarrier"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="gone"/>

        <ImageView
            android:id="@+id/ivIcon"
            android:layout_width="14dp"
            android:layout_height="14dp"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="4dp"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@id/iconBarrier"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/ic_poor_network"
            tools:visibility="visible"/>

        <com.interfun.buz.common.widget.view.IconFontTextView
            style="@style/body"
            android:id="@+id/iftvIcon"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="4dp"
            android:gravity="center"
            android:textColor="@color/color_text_white_important"
            android:textAlignment="center"
            app:pressEffect="false"
            app:autoSizeTextType="uniform"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@id/iconBarrier"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="@string/ic_mic_close"
            tools:visibility="gone"/>

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/iconBarrier"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:barrierDirection="end"
            app:constraint_referenced_ids="ivPortrait, ivIcon, iftvIcon" />

        <TextView
            android:id="@+id/tvStatus"
            style="@style/text_label_medium"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="12dp"
            android:textColor="@color/color_text_white_important"
            android:textSize="14sp"
            android:lines="1"
            android:ellipsize="end"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/iconBarrier"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="@string/voicecall_my_network_poor_toast" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</FrameLayout>