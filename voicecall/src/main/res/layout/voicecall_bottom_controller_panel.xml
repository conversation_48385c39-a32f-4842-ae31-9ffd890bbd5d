<?xml version="1.0" encoding="utf-8"?>
<com.interfun.buz.base.widget.round.RoundConstraintLayout
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rclBottomPanel"
    android:layout_width="0dp"
    android:layout_height="wrap_content"
    android:background="@color/transparent"
    android:elevation="30dp"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintEnd_toEndOf="parent"
    tools:showIn="@layout/voicecall_fragment_private_onlinechat">

    <!-- No answer -->
    <androidx.constraintlayout.widget.Group
        android:id="@+id/noAnswerGroup"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="iftvCancel,tvCancelLabel,iftvCallAgain,tvCallAgainLabel"
        tools:visibility="gone" />

    <!-- Busy -->
    <androidx.constraintlayout.widget.Group
        android:id="@+id/busyGroup"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="iftvCancel,tvCancelLabel"
        tools:visibility="gone" />

    <!-- Cancel Button -->
    <com.interfun.buz.common.widget.view.RoundIconFontView
        android:id="@+id/iftvCancel"
        android:layout_width="72dp"
        android:layout_height="72dp"
        android:layout_marginBottom="8dp"
        android:background="@color/alpha_white_20"
        android:gravity="center"
        android:text="@string/ic_exit"
        android:textColor="@color/color_text_white_important"
        android:textSize="32sp"
        app:round_radius="72dp"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@id/iftvCallAgain"
        app:layout_constraintBottom_toTopOf="@id/tvCancelLabel" />

    <TextView
        style="@style/text_label_medium"
        android:id="@+id/tvCancelLabel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="60dp"
        android:gravity="center"
        android:text="@string/cancel"
        android:textColor="@color/alpha_white_60"
        app:layout_constraintEnd_toEndOf="@id/iftvCancel"
        app:layout_constraintStart_toStartOf="@id/iftvCancel"
        app:layout_constraintBottom_toBottomOf="parent" />

    <!-- Call Again Button -->
    <com.interfun.buz.common.widget.view.RoundIconFontView
        android:id="@+id/iftvCallAgain"
        android:layout_width="72dp"
        android:layout_height="72dp"
        android:layout_marginBottom="8dp"
        android:layout_marginStart="96dp"
        android:background="@color/alpha_white_20"
        android:gravity="center"
        android:text="@string/ic_tel"
        android:textColor="@color/color_text_white_important"
        android:textSize="32sp"
        app:round_radius="72dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/iftvCancel"
        app:layout_constraintBottom_toTopOf="@id/tvCallAgainLabel" />

    <TextView
        style="@style/text_label_medium"
        android:id="@+id/tvCallAgainLabel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="60dp"
        android:gravity="center"
        android:text="@string/rtc_retry"
        android:textColor="@color/alpha_white_60"
        app:layout_constraintEnd_toEndOf="@id/iftvCallAgain"
        app:layout_constraintStart_toStartOf="@id/iftvCallAgain"
        app:layout_constraintBottom_toBottomOf="parent" />

    <!-- During a call -->
    <com.interfun.buz.base.widget.round.RoundLinearLayout
        android:id="@+id/channelJoinedGroup"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@color/alpha_black_80"
        android:padding="12dp"
        app:round_radius="100dp"
        app:layout_constraintVertical_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <com.interfun.buz.common.widget.view.RoundIconFontView
            android:id="@+id/iftvVideo"
            android:layout_width="52dp"
            android:layout_height="52dp"
            android:layout_marginEnd="20dp"
            android:background="@color/color_background_light_default"
            android:gravity="center"
            android:text="@string/ic_video"
            android:textColor="@color/color_text_black_primary"
            android:textSize="26sp"
            app:round_radius="52dp" />

        <com.interfun.buz.common.widget.view.RoundIconFontView
            android:id="@+id/iftvMic"
            android:layout_width="52dp"
            android:layout_height="52dp"
            android:layout_marginEnd="20dp"
            android:background="@color/color_background_light_default"
            android:gravity="center"
            android:text="@string/ic_mic_open"
            android:textColor="@color/color_text_black_primary"
            android:textSize="26sp"
            app:round_radius="52dp"/>

        <com.interfun.buz.common.widget.view.RoundIconFontView
            android:id="@+id/iftvSpeaker"
            android:layout_width="52dp"
            android:layout_height="52dp"
            android:layout_marginEnd="20dp"
            android:background="@color/color_background_light_default"
            android:gravity="center"
            android:text="@string/ic_phone_receiver"
            android:textColor="@color/color_text_black_primary"
            android:textSize="26sp"
            app:round_radius="52dp" />

        <com.interfun.buz.common.widget.view.RoundIconFontView
            android:id="@+id/iftvEnd"
            android:layout_width="52dp"
            android:layout_height="52dp"
            android:background="@color/color_background_consequential_default"
            android:gravity="center"
            android:text="@string/ic_invite_denied"
            android:textColor="@color/color_text_white_important"
            android:textSize="26sp"
            app:round_radius="52dp"/>

    </com.interfun.buz.base.widget.round.RoundLinearLayout>

</com.interfun.buz.base.widget.round.RoundConstraintLayout>