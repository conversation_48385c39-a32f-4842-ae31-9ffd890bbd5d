<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:orientation="vertical"
    android:layout_height="match_parent"
    android:background="@color/color_background_1_default">

<!--    &lt;!&ndash;标题栏区域&ndash;&gt;-->
<!--    <androidx.legacy.widget.Space-->
<!--        android:id="@+id/spaceTitleBar"-->
<!--        android:layout_width="0dp"-->
<!--        android:layout_height="@dimen/title_bar_height"-->
<!--        app:layout_constraintEnd_toEndOf="parent"-->
<!--        app:layout_constraintStart_toStartOf="parent"-->
<!--        app:layout_constraintTop_toTopOf="parent" />-->

<!--    <ImageView-->
<!--        android:id="@+id/ivHandle"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:paddingHorizontal="20dp"-->
<!--        android:paddingVertical="20dp"-->
<!--        android:src="@drawable/common_dialog_heat_drag_icon"-->
<!--        app:layout_constraintBottom_toBottomOf="@+id/spaceTitleBar"-->
<!--        app:layout_constraintEnd_toEndOf="parent"-->
<!--        app:layout_constraintStart_toStartOf="parent"-->
<!--        app:layout_constraintTop_toTopOf="@+id/spaceTitleBar" />-->

    <FrameLayout
        android:id="@+id/flContainer"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
         />
    <View
        android:id="@+id/navPad"
        android:layout_width="match_parent"
        android:layout_height="0dp"/>
</androidx.appcompat.widget.LinearLayoutCompat>