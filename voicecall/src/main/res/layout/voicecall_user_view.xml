<?xml version="1.0" encoding="utf-8"?>
<merge
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/container"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:visibility="visible"
    android:clickable="false"
    android:focusable="false"
    app:cardCornerRadius="16dp"
    tools:layout_width="185dp"
    tools:layout_height="329dp"
    tools:background="@color/overlay_grey_26"
    tools:ignore="SpUsage">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clContent"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.Group
            android:id="@+id/speakingAnim"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:constraint_referenced_ids="lowLayer, topLayer"/>

        <ImageView
            android:id="@+id/lowLayer"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/onair_volume_anim_low_layer"
            android:scaleType="fitCenter"
            app:layout_constraintBottom_toBottomOf="@+id/userPortraitIV"
            app:layout_constraintEnd_toEndOf="@+id/userPortraitIV"
            app:layout_constraintStart_toStartOf="@+id/userPortraitIV"
            app:layout_constraintTop_toTopOf="@+id/userPortraitIV"/>

        <ImageView
            android:id="@+id/topLayer"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/onair_volume_anim_top_layer"
            android:scaleType="fitCenter"
            android:scaleX="1.05"
            android:scaleY="1.05"
            app:layout_constraintBottom_toBottomOf="@+id/userPortraitIV"
            app:layout_constraintEnd_toEndOf="@+id/userPortraitIV"
            app:layout_constraintStart_toStartOf="@+id/userPortraitIV"
            app:layout_constraintTop_toTopOf="@+id/userPortraitIV"/>

        <com.interfun.buz.common.widget.view.PortraitImageView
            android:id="@+id/userPortraitIV"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@drawable/common_user_default_portrait_round"
            app:layout_constraintWidth_percent="0.6"
            app:layout_constraintWidth_max="200dp"
            app:layout_constraintHeight_max="200dp"
            app:layout_constraintDimensionRatio="1.0"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

        <View
            android:id="@+id/vTextureViewBackground"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/voicecall_user_card_video_loading_background"
            android:visibility="gone"
            tools:visibility="visible" />

<!--        <ImageView-->
<!--            android:id="@+id/ivBlurMask"-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="match_parent"-->
<!--            android:elevation="1dp"-->
<!--            android:visibility="gone"/>-->

        <View
            android:id="@+id/vBottomContent"
            android:layout_width="0dp"
            android:layout_height="32dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <View
            android:id="@+id/vShadowBackground"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@drawable/voicecall_user_card_gradient_mask"
            android:elevation="1dp"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="@id/vBottomContent"
            app:layout_constraintEnd_toEndOf="@id/vBottomContent"
            app:layout_constraintTop_toTopOf="@id/vBottomContent"
            app:layout_constraintBottom_toBottomOf="@id/vBottomContent"/>

        <TextView
            android:id="@+id/tvUserName"
            style="@style/text_label_medium"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="4dp"
            android:elevation="1dp"
            android:textColor="@color/color_text_white_important"
            android:ellipsize="end"
            android:maxLines="1"
            android:visibility="gone"
            app:layout_constrainedWidth="true"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintEnd_toStartOf="@id/iftvNetwork"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/vBottomContent"
            app:layout_constraintBottom_toBottomOf="@id/vBottomContent"
            tools:text="Anna"
            tools:visibility="visible"/>

        <com.interfun.buz.common.widget.view.RoundIconFontView
            android:id="@+id/iftvMuted"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginEnd="4dp"
            android:elevation="1dp"
            android:gravity="center"
            android:background="@color/alpha_black_20"
            android:visibility="gone"
            android:text="@string/ic_mic_close"
            android:textSize="@dimen/general_font_size_14"
            android:textColor="@color/text_white_important"
            app:pressEffect="false"
            app:round_radius="24dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/vBottomContent"
            app:layout_constraintBottom_toBottomOf="@id/vBottomContent"
            tools:visibility="visible"/>

        <com.interfun.buz.base.widget.round.RoundImageView
            android:id="@+id/iftvNetwork"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginEnd="4dp"
            android:elevation="1dp"
            android:padding="5dp"
            android:gravity="center"
            android:background="@color/alpha_black_20"
            android:src="@drawable/ic_poor_network"
            android:visibility="gone"
            app:round_radius="24dp"
            app:layout_constraintEnd_toStartOf="@id/iftvMuted"
            app:layout_constraintTop_toTopOf="@id/vBottomContent"
            app:layout_constraintBottom_toBottomOf="@id/vBottomContent"
            tools:visibility="visible"/>

        <View
            android:id="@+id/speakingBorder"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:elevation="1dp"
            android:background="@drawable/voicecall_speaking_border"
            android:visibility="gone"
            tools:visibility="visible"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</merge>