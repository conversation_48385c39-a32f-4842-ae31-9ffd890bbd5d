<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/clPendingView"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_background_3_default"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintTop_toTopOf="parent"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintStart_toStartOf="parent">

    <com.interfun.buz.voicecall.common.view.widget.RealTimeCallUserCard
        android:id="@+id/realTimeCallUserCard"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        app:cardElevation="0dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:visibility="visible"/>

    <View
        android:id="@+id/viewBgMask"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/voicecall_groupcall_background"/>

    <View
        android:id="@+id/viewCameraMask"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/voicecall_pending_call_gradient_mask"/>

    <View
        android:id="@+id/vContent"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="30dp"
        app:layout_constraintTop_toBottomOf="@id/vcTitleContainer"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <TextView
        android:id="@+id/tvPendingGroupName"
        style="@style/large_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="40dp"
        android:gravity="center"
        android:ellipsize="end"
        android:maxLines="2"
        tools:text="Buz Team"
        app:layout_constrainedWidth="true"
        android:textColor="@color/color_text_white_important"
        app:layout_constraintTop_toTopOf="@id/vContent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/tvPendingDesc"
        style="@style/regular"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="40dp"
        android:gravity="center"
        android:layout_marginTop="6dp"
        tools:text="Anna invites you to join call"
        android:textColor="@color/white_60"
        android:maxLines="2"
        android:ellipsize="end"
        app:layout_constraintTop_toBottomOf="@id/tvPendingGroupName"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>

    <com.interfun.buz.common.widget.view.PortraitImageView
        android:id="@+id/portraitImageViewPending"
        android:layout_width="180dp"
        android:layout_height="180dp"
        tools:background="@drawable/common_user_default_portrait_round"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvPendingDesc"
        app:layout_constraintBottom_toTopOf="@id/clBottomController"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clBottomController"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/portraitImageViewPending"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <androidx.constraintlayout.widget.Group
            android:id="@+id/groupBottomButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="visible"
            app:constraint_referenced_ids="iftvPendingAnswer, iftvPendingReject"
            tools:visibility="invisible"/>

        <androidx.constraintlayout.widget.Group
            android:id="@+id/groupBottomVideoButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="invisible"
            app:constraint_referenced_ids="iftvVideo, iftvMic"
            tools:visibility="visible"/>

        <com.interfun.buz.common.widget.view.RoundIconFontView
            android:id="@+id/iftvPendingReject"
            android:layout_width="72dp"
            android:layout_height="72dp"
            android:layout_centerInParent="true"
            android:background="@color/color_background_consequential_default"
            android:gravity="center"
            android:text="@string/ic_invite_denied"
            android:layout_marginStart="60dp"
            android:textColor="@color/white"
            android:textSize="32sp"
            android:visibility="visible"
            app:round_radius="40dp"
            app:layout_constraintHorizontal_chainStyle="spread_inside"
            app:layout_constraintEnd_toStartOf="@id/iftvPendingAnswer"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <com.interfun.buz.common.widget.view.RoundIconFontView
            android:id="@+id/iftvPendingAnswer"
            android:layout_width="72dp"
            android:layout_height="72dp"
            android:layout_centerInParent="true"
            android:layout_marginEnd="60dp"
            android:background="@color/color_background_highlight_1_default"
            android:gravity="center"
            android:text="@string/ic_tel"
            android:textColor="@color/black"
            android:textSize="32sp"
            android:visibility="visible"
            app:round_radius="375dp"
            app:layout_constraintStart_toEndOf="@id/iftvPendingReject"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="@id/iftvPendingReject"/>


        <com.interfun.buz.common.widget.view.RoundIconFontView
            android:id="@+id/iftvVideo"
            android:layout_width="52dp"
            android:layout_height="52dp"
            android:layout_centerInParent="true"
            android:background="@color/alpha_white_20"
            android:gravity="center"
            android:text="@string/ic_video"
            android:layout_marginBottom="40dp"
            android:textColor="@color/text_white_important"
            android:textSize="24sp"
            android:visibility="visible"
            app:round_radius="30dp"
            app:layout_constraintStart_toStartOf="@id/iftvPendingReject"
            app:layout_constraintEnd_toEndOf="@id/iftvPendingReject"
            app:layout_constraintBottom_toTopOf="@id/iftvPendingReject" />

        <com.interfun.buz.common.widget.view.RoundIconFontView
            android:id="@+id/iftvMic"
            android:layout_width="52dp"
            android:layout_height="52dp"
            android:layout_centerInParent="true"
            android:layout_marginBottom="40dp"
            android:background="@color/alpha_white_20"
            android:gravity="center"
            android:text="@string/ic_mic_open"
            android:textColor="@color/text_white_important"
            android:textSize="24sp"
            android:visibility="visible"
            app:round_radius="30dp"
            app:layout_constraintStart_toStartOf="@id/iftvPendingAnswer"
            app:layout_constraintEnd_toEndOf="@id/iftvPendingAnswer"
            app:layout_constraintBottom_toTopOf="@id/iftvPendingAnswer"/>
        <com.interfun.buz.common.widget.button.CommonButton
            android:layout_width="0dp"
            app:type="primary_larger"
            android:id="@+id/btnJoin"
            android:visibility="gone"
            tools:visibility="visible"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:text="@string/join"
            app:iconFont="@string/ic_video"
            android:layout_marginStart="60dp"
            android:layout_marginEnd="60dp"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_height="wrap_content"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <include
        android:id="@+id/vcTitleContainer"
        layout="@layout/voicecall_top_title_bar"/>


</androidx.constraintlayout.widget.ConstraintLayout>