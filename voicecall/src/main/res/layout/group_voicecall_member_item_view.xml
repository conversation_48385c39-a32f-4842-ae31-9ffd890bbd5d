<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_margin="0.5dp"
    tools:layout_width="185dp"
    tools:layout_height="329dp">

    <com.interfun.buz.voicecall.common.view.widget.RealTimeCallUserCard
        android:id="@+id/realTimeCallUserCard"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:layout_margin="0.5dp"
        app:cardElevation="0dp"
        app:cardBackgroundColor="@color/color_background_3_default"
        app:cardCornerRadius="16dp"
        tools:background="@color/alpha_black_30" />

    <View
        android:id="@+id/speakingBorder"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:background="@drawable/voicecall_speaking_border"
        android:visibility="gone"
        tools:visibility="visible"/>

</FrameLayout>
