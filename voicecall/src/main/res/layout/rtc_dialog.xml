<?xml version="1.0" encoding="utf-8"?>
<com.interfun.buz.base.widget.round.RoundConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:background="@color/overlay_grey_10"
    app:round_top_right_radius="@dimen/bottom_sheet_dialog_top_radius"
    app:round_top_left_radius="@dimen/bottom_sheet_dialog_top_radius"
    tools:ignore="MissingDefaultResource">


    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivArrow"
        android:layout_width="wrap_content"
        android:layout_height="34dp"
        android:paddingHorizontal="20dp"
        android:src="@drawable/common_dialog_heat_drag_icon"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <ImageView
        android:id="@+id/ivImage"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginBottom="40dp"
        app:layout_constraintBottom_toTopOf="@+id/tvTitle"
        app:layout_constraintDimensionRatio="375:225"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <com.interfun.buz.base.widget.round.RoundTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dialog_new_feature_text_view_margin"
        android:layout_marginTop="@dimen/dialog_new_feature_text_view_margin"
        android:background="@color/basic_primary"
        android:gravity="center"
        android:paddingHorizontal="6dp"
        android:paddingVertical="4dp"
        android:fontFamily="@font/nunito_extra_bold"
        android:text="@string/chat_new_feature"
        android:textColor="@color/basic_vanta"
        android:textSize="10sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:round_radius="@dimen/dialog_new_feature_text_view_radius" />

    <TextView
        android:id="@+id/tvTitle"
        style="@style/text_title_large"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="10dp"
        android:gravity="center"
        android:paddingHorizontal="20dp"
        android:text="@string/enjoy_voice_call"
        android:textColor="@color/white_80"
        android:textSize="24sp"
        app:layout_constraintBottom_toTopOf="@+id/tvDesc"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/tvDesc"
        style="@style/text_body_large"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="40dp"
        android:gravity="center"
        android:paddingHorizontal="20dp"
        android:text="@string/voice_call_feature_desc"
        android:textColor="@color/text_white_default"
        android:textSize="16sp"
        app:layout_constraintBottom_toTopOf="@+id/btnGotIt"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <com.interfun.buz.common.widget.button.CommonButton
        android:id="@+id/btnGotIt"
        android:layout_width="0dp"
        android:layout_height="56dp"
        android:layout_marginHorizontal="20dp"
        android:layout_marginBottom="30dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:text="@string/ai_game_new_function_got_it"
        app:type="primary_medium"
        tools:background="@drawable/common_r8_basic_primary" />
</com.interfun.buz.base.widget.round.RoundConstraintLayout>