<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rclTopTitleBar"
    android:layout_width="0dp"
    android:layout_height="64dp"
    android:background="@color/transparent"
    android:elevation="30dp"
    app:layout_constraintTop_toTopOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintEnd_toEndOf="parent"
    tools:showIn="@layout/voicecall_fragment_private_onlinechat">

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftvCollapse"
        style="@style/iconfont_24"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:gravity="center"
        android:layout_marginEnd="10dp"
        android:text="@string/ic_minimize"
        android:textSize="22sp"
        android:textColor="@color/color_text_white_important"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible"/>

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftvSwitchCamera"
        style="@style/iconfont_24"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginEnd="4dp"
        android:gravity="center"
        android:text="@string/ic_switch_camera"
        android:textSize="22sp"
        android:visibility="gone"
        android:textColor="@color/color_text_white_important"
        app:layout_goneMarginEnd="10dp"
        app:layout_constraintEnd_toStartOf="@id/iftvCollapse"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:visibility="visible"/>

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftvInvite"
        style="@style/iconfont_24"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginEnd="4dp"
        android:gravity="center"
        android:text="@string/ic_contact_add_solid"
        android:textSize="22sp"
        android:visibility="visible"
        android:textColor="@color/color_text_white_important"
        app:layout_constraintEnd_toStartOf="@id/iftvSwitchCamera"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:visibility="visible"/>

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftvClose"
        style="@style/iconfont_24"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:gravity="center"
        android:layout_marginStart="20dp"
        android:text="@string/ic_exit"
        android:textSize="24sp"
        android:textColor="@color/color_foreground_neutral_important_default"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:visibility="gone"/>

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/barrierNameEnd"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="start"
        app:constraint_referenced_ids="iftvCollapse,iftvSwitchCamera, iftvInvite"/>

    <TextView
        android:id="@+id/tvUserNameTitle"
        style="@style/text_title_medium"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="12dp"
        android:ellipsize="end"
        android:gravity="start"
        android:maxLines="1"
        android:textAlignment="viewStart"
        android:textColor="@color/color_foreground_neutral_important_default"
        android:textDirection="locale"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toTopOf="@+id/tvStatusTitle"
        app:layout_constraintEnd_toStartOf="@id/barrierNameEnd"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="Anna"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvStatusTitle"
        style="@style/text_body_small"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="start"
        android:maxLines="1"
        android:textAlignment="viewStart"
        android:textColor="@color/color_background_highlight_1_default"
        android:textDirection="locale"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/tvUserNameTitle"
        app:layout_constraintStart_toStartOf="@id/tvUserNameTitle"
        app:layout_constraintTop_toBottomOf="@id/tvUserNameTitle"
        tools:text="Calling..."
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>