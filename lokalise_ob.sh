# 使用语法sh lokalise_ob.sh 迭代名字(lokalise平台上的tag)
# rm -rf $tmpName
# echo "--------创建临时文件夹...---------"
# mkdir $tmpName


# echo "--------正在下载多语言文件...---------"
# curl -o "$tmpName/string.zip" -H "User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" -H "X-Requested-With: XMLHttpRequest" -H "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" -H "Accept-Encoding: gzip, deflate, br" -H "Accept-Language: zh-CN,zh;q=0.9" -H "Cookie:token=64aa9e4bd73e4a568d1187d5b92a5e05" --compressed "https://i18n.buz-app.com/i18nText/exportMobileI18nText?businessCode=$1&platformCode=2&businessName=$1&fileNamePattern=String%2F%7B%E8%AF%AD%E8%A8%80%E7%BC%96%E7%A0%81%7D.lproj%2F$1.strings&escape=true"

# cd $tmpName
# pwd
# unzip ./string.zip
python3 lokalise_download.py $1
rm -rf temp

