package com.interfun.buz.base.anim

import android.animation.Animator
import android.animation.ArgbEvaluator
import android.animation.ValueAnimator
import android.graphics.Color
import android.view.ViewGroup
import android.widget.TextView
import androidx.annotation.NonNull
import androidx.annotation.Nullable
import androidx.transition.Transition
import androidx.transition.TransitionValues

/**
 * TextColorTransition
 * 文本颜色过渡动画
 * 参考 TextScale 实现
 */
class TextColorTransition : Transition() {

    companion object {
        private const val PROPNAME_TEXT_COLOR = "android:textcolortransition:textcolor"
    }

    override fun captureStartValues(@NonNull transitionValues: TransitionValues) {
        captureValues(transitionValues)
    }

    override fun captureEndValues(@NonNull transitionValues: TransitionValues) {
        captureValues(transitionValues)
    }

    private fun captureValues(@NonNull transitionValues: TransitionValues) {
        if (transitionValues.view is TextView) {
            val textView = transitionValues.view as TextView
            transitionValues.values[PROPNAME_TEXT_COLOR] = textView.currentTextColor
        }
    }

    override fun createAnimator(
        @NonNull sceneRoot: ViewGroup,
        @Nullable startValues: TransitionValues?,
        @Nullable endValues: TransitionValues?
    ): Animator? {
        if (startValues == null
            || endValues == null
            || startValues.view !is TextView
            || endValues.view !is TextView) {
            return null
        }

        val view = endValues.view as TextView
        val startVals = startValues.values
        val endVals = endValues.values

        val startColor = startVals[PROPNAME_TEXT_COLOR] as? Int ?: Color.BLACK
        val endColor = endVals[PROPNAME_TEXT_COLOR] as? Int ?: Color.BLACK

        if (startColor == endColor) {
            return null
        }

        val animator = ValueAnimator.ofObject(ArgbEvaluator(), startColor, endColor)

        animator.addUpdateListener { valueAnimator ->
            val animatedValue = valueAnimator.animatedValue as Int
            view.setTextColor(animatedValue)
        }

        return animator
    }
}