package com.interfun.buz.base.anim

import android.animation.Animator
import android.transition.Transition
import android.transition.TransitionValues
import android.view.ViewGroup
import android.widget.TextView

class TextColorTransition : androidx.transition.Transition() {

    override fun captureStartValues(startValues: TransitionValues?) {
        val textView = startValues?.view as? TextView
        startValues?.values?.set("textColor", textView?.currentTextColor)
    }

    override fun captureEndValues(endValues: TransitionValues?) {
        val textView = endValues?.view as? TextView
        endValues?.values?.set("textColor", textView?.currentTextColor)
    }

    override fun createAnimator(
        sceneRoot: ViewGroup,
        startValues: androidx.transition.TransitionValues?,
        endValues: androidx.transition.TransitionValues?
    ): Animator? {
        return super.createAnimator(sceneRoot, startValues, endValues)
    }

    override fun createAnimator(
        sceneRoot: ViewGroup,
        startValues: TransitionValues?,
        endValues: TransitionValues?
    ): Animator? {
        return super.createAnimator(sceneRoot, startValues, endValues)
    }
}