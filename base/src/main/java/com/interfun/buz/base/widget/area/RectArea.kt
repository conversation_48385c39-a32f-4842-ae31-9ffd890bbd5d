package com.interfun.buz.base.widget.area

import androidx.compose.runtime.Stable
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.layout.LayoutCoordinates
import androidx.compose.ui.layout.positionInParent

/**
 * <AUTHOR>
 * @date 2025/5/30
 * @desc
 */
open class RectArea(override val start: Float, override val top: Float, override val end: Float, override val bottom: Float): IArea {

    companion object {
        @Stable
        val Zero = CircleArea(0f, 0f, 0f, 0f)

        fun convertFrom(coordinates: LayoutCoordinates): RectArea {
            val topStart = coordinates.positionInParent()
            val size = coordinates.size.width
            return RectArea(
                start = topStart.x,
                top = topStart.y,
                end = topStart.x + size,
                bottom = topStart.y + size,
            )
        }
    }

    override fun isInArea(position: Offset?, offsetX: Float, offsetY: Float): Boolean {
        position ?: return false
        val realX = position.x + offsetX
        val realY = position.y + offsetY
        return realX in start..end && realY in top..bottom
    }

    override fun toString() = "start:$start, top:$top, end:$end, bottom:$bottom"
}