package com.interfun.buz.base.coroutine

/*
 copy from https://gist.github.com/elizarov/9a48b9709ffd508909d34fab6786acfe
 see https://github.com/Kotlin/kotlinx.coroutines/issues/1686 for more detail
*/

import kotlinx.coroutines.*
import kotlinx.coroutines.sync.*
import kotlin.coroutines.*

val mutex = Mutex()

suspend fun foo() {
    mutex.withReentrantLock {
        doSomeWork()
    }
}

suspend fun doSomeWork() {
    mutex.withReentrantLock {
        println("Working!")
    }
}

suspend fun main() {
    foo()
}

suspend fun <T> Mutex.withReentrantLock(owner:Any?=null,block: suspend () -> T): T {
    val key = ReentrantMutexContextKey(this)
    // call block directly when this mutex is already locked in the context
    if (coroutineContext[key] != null) return block()
    // otherwise add it to the context and lock the mutex
    return withContext(ReentrantMutexContextElement(key)) {
        withLock(owner) { block() }
    }
}

class ReentrantMutexContextElement(
    override val key: ReentrantMutexContextKey
) : CoroutineContext.Element

data class ReentrantMutexContextKey(
    val mutex: Mutex
) : CoroutineContext.Key<ReentrantMutexContextElement>