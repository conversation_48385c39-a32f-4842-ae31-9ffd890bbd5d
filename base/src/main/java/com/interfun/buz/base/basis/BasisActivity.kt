package com.interfun.buz.base.basis

import android.app.Activity
import android.content.pm.ActivityInfo
import android.content.pm.ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED
import android.content.res.TypedArray
import android.os.Build
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.interfun.buz.base.ktx.doOnLifecycle
import com.interfun.buz.base.ktx.log
import java.lang.reflect.Field
import java.lang.reflect.Method


/**
 * <AUTHOR>
 *
 * @date 2022/7/6
 *
 * @desc 纯代码基类，避免使用。实际使用请用Common下的业务通用基类BaseActivity
 */
abstract class BasisActivity : AppCompatActivity(), BasisInitInterface {

   private val BASISACTIVITY_TAG="BasisActivity"
    override fun onCreate(savedInstanceState: Bundle?) {
        if (Build.VERSION.SDK_INT == Build.VERSION_CODES.O && isTranslucentOrFloating()) {
            val result = fixOrientation()
        }
        initPreOnCreate()
        super.onCreate(savedInstanceState)
        initArguments()
        log(BASISACTIVITY_TAG,"$this onCreate")
        //在onCreate完全执行完后，再执行onViewCreated方法
        doOnLifecycle(onCreate = { onViewCreated() })
    }

    override fun onResume() {
        super.onResume()
        log(BASISACTIVITY_TAG,"$this onResume")
    }
    override fun onStop() {
        super.onStop()
        log(BASISACTIVITY_TAG,"$this onStop")
    }

    override fun onDestroy() {
        super.onDestroy()
        log(BASISACTIVITY_TAG,"$this onDestroy")
    }

    override fun onRestart() {
        super.onRestart()
        log(BASISACTIVITY_TAG,"$this onRestart")
    }

    override fun onPause() {
        super.onPause()
        log(BASISACTIVITY_TAG,"$this onPause")
    }
    abstract fun initPreOnCreate()

    open fun onViewCreated(){
        initView()
        initBlock()
        initData()
    }

    override fun setRequestedOrientation(requestedOrientation: Int) {
        if (Build.VERSION.SDK_INT == Build.VERSION_CODES.O && isTranslucentOrFloating()) {
            return
        }
        super.setRequestedOrientation(requestedOrientation)
    }

    private fun isTranslucentOrFloating(): Boolean {
        var isTranslucentOrFloating = false
        try {
            val styleableRes = Class.forName("com.android.internal.R\$styleable").getField("Window")[null] as IntArray
            val ta = obtainStyledAttributes(styleableRes)
            val m: Method = ActivityInfo::class.java.getMethod("isTranslucentOrFloating", TypedArray::class.java)
            m.isAccessible = true
            isTranslucentOrFloating = m.invoke(null, ta) as Boolean
            m.isAccessible = false
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return isTranslucentOrFloating
    }

    private fun fixOrientation(): Boolean {
        try {
            val field: Field = Activity::class.java.getDeclaredField("mActivityInfo")
            field.isAccessible = true
            val o = field.get(this) as ActivityInfo
            o.screenOrientation = SCREEN_ORIENTATION_UNSPECIFIED
            field.isAccessible = false
            return true
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
        return false
    }

}