pluginManagement {
    includeBuild("plugins")
}

rootProject.name = "buz"
include ':app'
include ':base'
include ':base-float'
include ':common'
include ':common-compose'
include ':startup'
include ':login'
include ':im'
include ':chat'
include ':contacts'
include ':demo'
include ':push'
include ':user'
include ':feedback'
include ':float'
include ':sharedmedia'
include ':campaign'
include ':voicecall'
include ':benchmark-startup'
include ':media'
include ':album'
include ':base-photopreview'
include ':pictureselector'
include ':handleReceiveShare'
include ':storage'
include ':onair'
include ':home'


include ':feature:voicepreview'
include ':feature:notification'

include ':domain-ui:social'
include ':domain-ui:chat'
include ':domain-ui:record' // 依赖录音+声音滤镜的 bizCenter层 + core-widget层，由feature层依赖

include ':biz-center:voicemoji'
include ':biz-center:voicefilter'
include ':biz-center:voicerecord'
include ':biz-center:liveplace'
include ':biz-center:user'
include ':biz-center:social'
include ':biz-center:translator'

include ':component:hilt'
include ':component:download'
include ':domain:im-social'
include ':domain:voiceemoji-im'
include ':core:widget_liveplace'
include ':core:widget_record'
