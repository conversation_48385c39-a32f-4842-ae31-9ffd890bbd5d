package com.interfun.buz.core.widget_liveplace.state

import com.interfun.buz.common.database.entity.ExistPlaceType
import com.interfun.buz.common.database.entity.ExistPlaceType.*
import com.interfun.buz.common.database.entity.PlaceType
import com.interfun.buz.common.database.entity.UserRelationInfo
import com.interfun.buz.common.database.entity.UserStatus
import com.interfun.buz.common.database.entity.chat.GroupInfoBean
import com.interfun.buz.common.database.entity.chat.isBigGroup
import com.interfun.buz.common.ktx.*
import com.interfun.buz.common.manager.AppConfigRequestManager
import com.interfun.buz.common.manager.ChannelStatusManager
import com.interfun.buz.common.manager.cache.group.GroupInfoCacheManager
import com.interfun.buz.common.manager.cache.user.UserRelationCacheManager

/**
 * <AUTHOR>
 * @date 2025/1/8
 * @desc
 */
sealed interface LivePlaceEntranceUIState {
    val isLive: Boolean
    // 还没有请求成功过服务端的数据，当前是否有创建过LivePlace是未知的，对应 UI 是什么都不展示
    data object Unknown : LivePlaceEntranceUIState {
        override val isLive: Boolean get() = false
    }

    // 拉取远端数据为还没有创建过LivePlace，对应 UI 展示创建按钮
    data object NotCreated : LivePlaceEntranceUIState {
        override val isLive: Boolean get() = false
    }

    // 拉取远端数据为还没有创建过LivePlace，并且根据 config 配置等条件不能展示创建入口
    data object NotEnable : LivePlaceEntranceUIState {
        override val isLive: Boolean get() = false
    }

    // 正在拉取远端数据，对应 UI 展示加载中（或占位）
    data class Loading(override val isLive: Boolean) : LivePlaceEntranceUIState

    // 已经创建过LivePlace，对应 UI 展示LivePlace信息
    data class Created(
        override val isLive: Boolean,
        val topic: String,
        val name: String?,
        val bgImgUrl: String?,
        val isCustomizeBg: Boolean,
        val btnText: String,
        val channelId: Long? = null,
        val memberNum: Int = 0,
        val topMemberPortraits: List<String>? = null,
    ) : LivePlaceEntranceUIState
}

/**
 * todo：在 core 层不应该实现下面的业务逻辑，可能的合适实现是新建一个类 domain 层，待后续确认后再移走这部分代码
 */
fun ExistPlaceType.toEntranceUIState(
    targetId: Long,
    placeType: PlaceType,
): LivePlaceEntranceUIState {
    // 此处获取的 enable 代表的是用户在正常状态下（好友或群的关系正常、没有被拉黑退群等情况），是否可以看到入口（受 config 配置决定)
    val enable = when(placeType){
        PlaceType.PRIVATE -> {
            if (targetId.isMe()) {
                // 自己的入口由 config 决定
                AppConfigRequestManager.livePlaceEnablePersonalLivePlace
            } else {
                val userInfo = UserRelationCacheManager.getUserRelationFromMem(targetId)
                // 如果好友关系异常，那么直接也不展示入口
                if (!isUserLivePlaceEnable(userInfo)) {
                    return LivePlaceEntranceUIState.NotEnable
                }
                // 好友关系正常，那么由 config 决定
                AppConfigRequestManager.livePlaceEnablePersonalLivePlace
            }
        }
        PlaceType.GROUP -> {
            val groupInfo = GroupInfoCacheManager.getGroupInfoFromMem(targetId)
            // 如果群状态异常，那么直接也不展示入口
            if (!isGroupLivePlaceEnable(groupInfo)) {
                return LivePlaceEntranceUIState.NotEnable
            }
            // 群状态正常，那么由 config 决定
            AppConfigRequestManager.livePlaceEnableGroupLivePlace
        }
        PlaceType.UNKNOWN -> false
    }
    // 最终由上面得到的 enable 和是否有创建过空间 来共同决定最终的入口展示与否
    return getFinalUIState(targetId, this, enable)
}

suspend fun ExistPlaceType.toEntranceUIStateSync(
    targetId: Long,
    placeType: PlaceType
): LivePlaceEntranceUIState {
    // 此处获取的 enable 代表的是用户在正常状态下（好友或群的关系正常、没有被拉黑退群等情况），是否可以看到入口（受 config 配置决定)
    val enable = when(placeType){
        PlaceType.PRIVATE -> {
            if (targetId.isMe()) {
                // 自己的入口由 config 决定
                AppConfigRequestManager.livePlaceEnablePersonalLivePlace
            } else {
                val userInfo = UserRelationCacheManager.getUserRelationInfoByUidSync(targetId)
                // 如果好友关系异常，那么直接也不展示入口
                if (!isUserLivePlaceEnable(userInfo)) {
                    return LivePlaceEntranceUIState.NotEnable
                }
                // 好友关系正常，那么由 config 决定
                AppConfigRequestManager.livePlaceEnablePersonalLivePlace
            }
        }
        PlaceType.GROUP -> {
            val groupInfo = GroupInfoCacheManager.getGroupInfoBeanByIdSync(targetId)
            // 如果群状态异常，那么直接也不展示入口
            if (!isGroupLivePlaceEnable(groupInfo)) {
                return LivePlaceEntranceUIState.NotEnable
            }
            // 群状态正常，那么由 config 决定
            AppConfigRequestManager.livePlaceEnableGroupLivePlace
        }
        PlaceType.UNKNOWN -> false
    }
    // 最终由上面得到的 enable 和是否有创建过空间 来共同决定最终的入口展示与否
    return getFinalUIState(targetId, this, enable)
}

private fun getFinalUIState(targetId: Long, existType: ExistPlaceType, isEnable: Boolean) =
    when (existType) {
        // 已经创建过空间，那么直接展示入口（此处先展示 Loading 高度占位)
        // 不需要受 enable 影响，也就是 config 配置不会影响已经创建的空间
        EXIST -> LivePlaceEntranceUIState.Loading(ChannelStatusManager.isOpenLivePlace(targetId))
        // 正在拉远端数据中
        PENDING -> LivePlaceEntranceUIState.Unknown
        // 没有创建空间，那么则受 enable 决定是否展示创建入口还是完全不展示
        NO_EXIST -> if (isEnable) {
            LivePlaceEntranceUIState.NotCreated
        } else {
            LivePlaceEntranceUIState.NotEnable
        }
    }

private fun isUserLivePlaceEnable(userInfo: UserRelationInfo?): Boolean {
    return userInfo != null
        && userInfo.isFriend
        && !userInfo.isRobot
        && !userInfo.isOfficial
        && userInfo.userStatus == UserStatus.STATUS_NORMAL
}

private fun isGroupLivePlaceEnable(groupInfo: GroupInfoBean?): Boolean {
    return groupInfo != null
        && groupInfo.isInGroup()
        && !groupInfo.isBigGroup
}
