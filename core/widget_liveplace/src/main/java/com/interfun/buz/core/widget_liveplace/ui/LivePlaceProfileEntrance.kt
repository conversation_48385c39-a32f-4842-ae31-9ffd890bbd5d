package com.interfun.buz.core.widget_liveplace.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.basicMarquee
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.onPlaced
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.interfun.buz.compose.components.haze.HazeState
import com.interfun.buz.compose.components.haze.hazeSource
import com.interfun.buz.compose.ktx.VerticalSpace
import com.interfun.buz.compose.ktx.asColor
import com.interfun.buz.compose.ktx.asString
import com.interfun.buz.compose.ktx.rememberMutableBoolean
import com.interfun.buz.compose.modifier.applyIf
import com.interfun.buz.compose.modifier.debouncedClickable
import com.interfun.buz.compose.styles.TextStyles
import com.interfun.buz.core.widget_liveplace.R
import com.interfun.buz.core.widget_liveplace.state.LivePlaceEntranceUIState

/**
 * <AUTHOR>
 * @date 2025/1/8
 * @desc
 */
@Composable
fun LivePlaceProfileEntrance(
    uiStateLambda: () -> LivePlaceEntranceUIState,
    heightLambda:()-> Dp,
    showSofaIcon: Boolean,
    isMyProfile: Boolean,
    onBtnClick: (LivePlaceEntranceUIState.Created) -> Unit,
    modifier: Modifier = Modifier
) {
    val uiState = uiStateLambda()
    val rootHeight = heightLambda()
    var isBackgroundPlaced by rememberMutableBoolean()
    val hazeState = remember { HazeState() }

    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(rootHeight)
            .clip(shape = RoundedCornerShape(30.dp))
    ) {
        if (uiState !is LivePlaceEntranceUIState.Created) return@Box
        Box(
            modifier = Modifier
                .fillMaxSize()
                // 有一开始没有出现高斯模糊效果的问题，先等待背景图出现后再添加高斯模糊效果
                .applyIf(isBackgroundPlaced){
                    this.hazeSource(hazeState)
                }
        ) {
            LivePlaceBackground(
                bgImgUrl = uiState.bgImgUrl,
                isUserCustom = uiState.isCustomizeBg,
                modifier = Modifier
                    .fillMaxSize()
                    .onPlaced { isBackgroundPlaced = true }
            )
            val maskColor = if (isMyProfile) {
                R.color.color_background_1_default.asColor()
            } else {
                R.color.color_background_2_default.asColor()
            }
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .alpha(0.2f)
                    .background(color = maskColor)
            )
        }
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight()
                .padding(horizontal = 40.dp)
                .align(Alignment.Center),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            if (isMyProfile) {
                LivePlaceTag(isLive = uiState.isLive)
                VerticalSpace(16.dp)
            }
            Text(
                text = uiState.topic,
                style = TextStyles.titleMedium(),
                color = R.color.color_foreground_neutral_important_default.asColor(),
                maxLines = 2,
                textAlign = TextAlign.Center
            )
            if (uiState.isLive && !uiState.name.isNullOrEmpty()) {
                VerticalSpace(10.dp)
                Text(
                    text = stringResource(R.string.live_place_owner_title, uiState.name),
                    style = TextStyles.bodyMedium(),
                    color = R.color.alpha_white_60.asColor(),
                    maxLines = 1,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.basicMarquee()
                )
            }
            val portraitList = uiState.topMemberPortraits
            if (uiState.isLive && !portraitList.isNullOrEmpty()) {
                VerticalSpace(rootHeight * 0.0888f)
                LivePlacePortraits(
                    memberCount = uiState.memberNum,
                    portraitList = portraitList
                )
                VerticalSpace(rootHeight * 0.0888f)
            } else {
                VerticalSpace(20.dp)
            }
            LivePlaceEntranceButton(
                textRes = uiState.btnText,
                showSofaIcon = showSofaIcon,
                isMyProfile = isMyProfile,
                hazeState = hazeState,
                isLiveLambda = { uiState.isLive && !isMyProfile },
                modifier = Modifier.debouncedClickable{
                    onBtnClick.invoke(uiState)
                }
            )
        }
    }
}

@Composable
@Preview
private fun PreviewLivePlaceEntrance() {
    val list = mutableListOf<String>()
    repeat(12) {
        list.add("")
    }
    val infoUIState = LivePlaceEntranceUIState.Created(
        channelId = 0,
        topic = "User Live Place Topic, User Live Place Topic, User Live Place Topic",
        name = "XXXX",
        bgImgUrl = "",
        isCustomizeBg = false,
        isLive = true,
        memberNum = 12,
        topMemberPortraits = list,
        btnText = R.string.join_live_place.asString()
    )
    LivePlaceProfileEntrance(
        uiStateLambda = { infoUIState },
        heightLambda = { 300.dp },
        showSofaIcon = true,
        isMyProfile = true,
        onBtnClick = {},
        modifier = Modifier.height(300.dp)
    )
}