package com.interfun.buz.core.widget_liveplace.ui

import android.net.Uri
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.interfun.buz.compose.ktx.asColor
import com.interfun.buz.core.widget_liveplace.R

/**
 * <AUTHOR>
 * @date 2024/12/24
 * @desc
 */
@Composable
fun LivePlaceBackground(
    bgImgUrl: String?,
    isUserCustom: Boolean = false,
    modifier: Modifier = Modifier,
) = LivePlaceBackground(
    data = bgImgUrl,
    isUserCustom = isUserCustom,
    modifier = modifier
)

@Composable
fun LivePlaceBackground(
    bgImgUri: Uri?,
    isUserCustom: Boolean = false,
    modifier: Modifier = Modifier,
) = LivePlaceBackground(
    data = bgImgUri,
    isUserCustom = isUserCustom,
    modifier = modifier
)

@Composable
fun LivePlaceBackground(
    data: Any?,
    isUserCustom: Boolean = false,
    modifier: Modifier = Modifier,
) {
    //添加一个黑色兜底
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(color = Color.Black)
    )
    if (data == null) {
        val gradient = Brush.linearGradient(
            colors = listOf(
                Color(255, 255, 255, 0x05),
                Color(255, 255, 255, 0x14),
                Color(255, 255, 255, 0x05)
            ),
        )
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(gradient)
        )
        return
    }
    Box(
        modifier = modifier
            .fillMaxSize()
    ) {
        val request = ImageRequest.Builder(LocalContext.current)
            .data(data)
            .placeholder(R.drawable.common_rect_background_3_default)
            .crossfade(true)
            .build()
        AsyncImage(
            modifier = Modifier.fillMaxSize(),
            model = request,
            contentScale = ContentScale.Crop,
            contentDescription = null,
        )
        // 在任何位置，自定义上传图片都将比预设颜色图片多出一个默认蒙层
        if (isUserCustom) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(color = R.color.alpha_black_50.asColor())
            )
        }
    }
}