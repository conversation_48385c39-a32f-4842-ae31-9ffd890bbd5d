package com.interfun.buz.core.widget_liveplace.ui

import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.togetherWith
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec.Asset
import com.airbnb.lottie.compose.LottieConstants
import com.airbnb.lottie.compose.animateLottieCompositionAsState
import com.airbnb.lottie.compose.rememberLottieComposition
import com.interfun.buz.compose.ktx.asPainter
import com.interfun.buz.core.widget_liveplace.R

/**
 * <AUTHOR>
 * @date 2024/12/24
 * @desc
 */
@Composable
fun LivePlaceTag(
    isLive: Boolean,
    showTurnOnAnim: Boolean = true,
    tagWidth: Dp = 42.dp,
    tagHeight: Dp = 20.dp,
    modifier: Modifier = Modifier
) {
    AnimatedContent(
        targetState = isLive,
        transitionSpec = {
            (fadeIn(animationSpec = tween(220)))
                .togetherWith(fadeOut(animationSpec = tween(90)))
        },
        modifier = modifier
            .width(tagWidth)
            .height(tagHeight),
    ) {
        if (it) {
            Box(Modifier.fillMaxSize()) {
                val compTagOn by rememberLottieComposition(Asset("lottie/liveplace_tag_on.json"))
                LottieAnimation(
                    composition = compTagOn,
                    iterations = LottieConstants.IterateForever,
                    modifier = Modifier.fillMaxSize(),
                )
                if (showTurnOnAnim) {
                    val compTagTurnOn by rememberLottieComposition(Asset("lottie/liveplace_tag_turn_on.json"))
                    val state = animateLottieCompositionAsState(composition = compTagTurnOn)
                    if (state.isPlaying) {
                        LottieAnimation(
                            composition = compTagTurnOn,
                            progress = { state.progress },
                            modifier = Modifier.fillMaxSize(),
                        )
                    }
                }
            }
        } else {
            Image(
                painter = R.drawable.liveplace_tag_off.asPainter(),
                contentDescription = "LiveplaceOff",
                modifier = Modifier.fillMaxSize()
            )
        }
    }
}

@Composable
@Preview
private fun PreviewLivePlaceTagOn() {
    LivePlaceTag(isLive = true)
}

@Composable
@Preview
private fun PreviewLivePlaceTagOff() {
    LivePlaceTag(isLive = false)
}