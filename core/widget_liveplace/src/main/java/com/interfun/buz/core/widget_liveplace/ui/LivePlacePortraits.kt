package com.interfun.buz.core.widget_liveplace.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Outline
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.PathOperation
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.layout.Layout
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.*
import com.interfun.buz.compose.components.PortraitImage
import com.interfun.buz.compose.ktx.asColor
import com.interfun.buz.compose.ktx.asPainter
import com.interfun.buz.compose.ktx.dpToPx
import com.interfun.buz.compose.modifier.applyIf
import com.interfun.buz.compose.styles.TextStyles
import com.interfun.buz.core.widget_liveplace.R

/**
 * <AUTHOR>
 * @date 2024/12/27
 * @desc
 */
@Composable
fun LivePlacePortraits(
    memberCount: Int,
    portraitList: List<String>,
    showLight: Boolean = true,
    maxPortraitCount: Int = 7,
    portraitSize: Dp = 40.dp,
    transparentBorderWidth: Dp = 4.dp,
    countTextStyle: TextStyle = TextStyles.labelMedium(),
    modifier: Modifier = Modifier,
) {
    if (portraitList.isEmpty()) return
    val count = portraitList.size.coerceIn(0, maxPortraitCount)
    val itemSize = portraitSize * (58f / 40f)
    OverlappingRow(
        itemOverlap = itemSize - portraitSize + transparentBorderWidth,
        modifier = modifier
    ) {
        repeat(count) { index ->
            LivePlacePortrait(
                portrait = portraitList[index],
                portraitSize = portraitSize,
                transparentBorderWidth = transparentBorderWidth,
                showLight = showLight,
                modifier = Modifier.size(itemSize)
            )
        }
        LivePlacePortraitsCount(
            count = memberCount,
            countTextStyle = countTextStyle,
            portraitSize = portraitSize,
            modifier = Modifier.size(itemSize)
        )
    }
}

@Composable
private fun OverlappingRow(
    itemOverlap: Dp = 22.dp,
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit,
) {
    Layout(
        content = content,
        modifier = modifier
    ) { measurables, constraints ->
        // Measure each child
        val itemConstraints = constraints.copy(minWidth = 0, minHeight = 0)
        val placeables = measurables.map { measurable ->
            measurable.measure(itemConstraints)
        }

        // Calculate the total width and max height
        val totalWidth = placeables.fold(0) { acc, placeable ->
            acc + placeable.width
        } - (placeables.size - 1) * itemOverlap.roundToPx()

        val maxHeight = placeables.maxOfOrNull { it.height } ?: 0

        layout(width = maxOf(totalWidth, constraints.minWidth), height = maxHeight) {
            var xOffset = 0
            placeables.forEach { placeable ->
                placeable.placeRelative(IntOffset(xOffset, 0))
                xOffset += placeable.width - itemOverlap.roundToPx()
            }
        }
    }
}

@Composable
private fun LivePlacePortraitsCount(
    count: Int,
    countTextStyle: TextStyle,
    portraitSize: Dp,
    modifier: Modifier = Modifier,
) {
    Box(modifier = modifier) {
        Box(
            modifier = Modifier
                .size(portraitSize)
                .align(Alignment.Center)
                .clip(shape = CircleShape)
                .background(color = R.color.alpha_white_20.asColor())
        )
        Text(
            text = "$count",
            style = countTextStyle,
            color = R.color.alpha_white_60.asColor(),
            textAlign = TextAlign.Center,
            modifier = Modifier.align(Alignment.Center)
        )
    }
}

@Composable
private fun LivePlacePortrait(
    portrait: String,
    portraitSize: Dp,
    transparentBorderWidth: Dp,
    showLight: Boolean = true,
    clip: Boolean = true,
    modifier: Modifier = Modifier,
) {
    val isRtl = LocalLayoutDirection.current == LayoutDirection.Rtl
    val direction = if (isRtl) -1 else 1
    Box(modifier = modifier) {
        if (showLight) {
            Image(
                painter = R.drawable.liveplace_bg_portrait.asPainter(),
                contentDescription = "",
                modifier = Modifier
                    .fillMaxSize()
                    .align(Alignment.Center),
            )
        }
        PortraitImage(
            url = portrait,
            modifier = Modifier
                .size(portraitSize)
                .align(Alignment.Center)
                .applyIf(clip) {
                    clip(
                        shape = BittenCircleShape(
                            biteCircleSize = (portraitSize + transparentBorderWidth * 2).dpToPx(),
                            biteCircleCenterXGap = (portraitSize - transparentBorderWidth).dpToPx() * direction,
                        )
                    )
                }
        )
    }
}

/**
 * 自定义形状，用于创建一个圆被另一个圆 "咬掉一口" 的效果
 *
 * @param biteCircleSize 缺口圆的直径
 * @param biteCircleCenterXGap 缺口圆中心到本体圆中心的x距离
 * @param biteCircleCenterYGap 缺口圆中心到本体圆中心的y距离
 */
private class BittenCircleShape(
    private val biteCircleSize: Float,
    private val biteCircleCenterXGap: Float,
    private val biteCircleCenterYGap: Float = 0f,
) : Shape {
    override fun createOutline(
        size: Size,
        layoutDirection: LayoutDirection,
        density: Density,
    ): Outline {
        val mainCirclePath = Path().apply {
            // Draw the main circle
            addOval(Rect(0f, 0f, size.width, size.height))
        }

        val biteCirclePath = Path().apply {
            // Define the bite circle
            addOval(
                Rect(
                    center = Offset(
                        x = size.width / 2f + biteCircleCenterXGap,
                        y = size.height / 2f + biteCircleCenterYGap
                    ),
                    radius = biteCircleSize / 2f
                )
            )
        }

        // Subtract the bite circle from the main circle path
        val resultPath = Path()
        resultPath.op(mainCirclePath, biteCirclePath, PathOperation.Difference)

        return Outline.Generic(resultPath)
    }
}

@Composable
@Preview
fun LivePlacePortraitsPreview() {
    val list = mutableListOf<String>()
    repeat(12) {
        list.add("")
    }
    LivePlacePortraits(
        memberCount = 12,
        portraitList = list
    )
}

@Composable
@Preview(
    name = "RTL Preview",
    locale = "ar",
)
fun LivePlacePortraitsRTLPreview() {
    CompositionLocalProvider(LocalLayoutDirection provides LayoutDirection.Rtl) {
        val list = mutableListOf<String>()
        repeat(12) { list.add("") }
        LivePlacePortraits(
            memberCount = 12,
            portraitList = list
        )
    }
}

@Composable
@Preview
fun LivePlacePortraitsPreviewOnlyOne() {
    val list = mutableListOf<String>()
    repeat(1) {
        list.add("")
    }
    LivePlacePortraits(
        memberCount = 1,
        portraitList = list
    )
}

@Composable
@Preview
fun LivePlacePortraitsPreviewSmall() {
    val list = mutableListOf<String>()
    repeat(4) {
        list.add("")
    }
    LivePlacePortraits(
        memberCount = 8,
        portraitList = list,
        portraitSize = 24.dp,
        showLight = false,
        countTextStyle = TextStyles.labelSmall(),
    )
}