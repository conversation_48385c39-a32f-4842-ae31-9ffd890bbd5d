package com.interfun.buz.core.widget_liveplace.ui

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec.Asset
import com.airbnb.lottie.compose.LottieConstants
import com.airbnb.lottie.compose.rememberLottieComposition
import com.interfun.buz.compose.components.IconFontText
import com.interfun.buz.compose.components.haze.HazeState
import com.interfun.buz.compose.components.haze.hazeEffect
import com.interfun.buz.compose.ktx.HorizontalSpace
import com.interfun.buz.compose.ktx.asColor
import com.interfun.buz.compose.ktx.asString
import com.interfun.buz.compose.styles.TextStyles
import com.interfun.buz.core.widget_liveplace.R

/**
 * <AUTHOR>
 * @date 2024/12/23
 * @desc
 */
@Composable
fun LivePlaceEntranceButton(
    textRes: String,
    showSofaIcon: Boolean = false,
    isNotCreated: Boolean = false,
    isMyProfile: Boolean = false,
    hazeState: HazeState? = null,
    isLiveLambda: () -> Boolean = { false },
    modifier: Modifier = Modifier
) {
    val isLive = isLiveLambda()
    val textColor by animateColorAsState(
        targetValue = if (isLive) {
            R.color.color_foreground_onColor_1_default.asColor()
        } else {
            R.color.neutral_white.asColor()
        }
    )
    val buttonHeight by animateDpAsState(
        targetValue = if(isLive || isMyProfile) 44.dp else 32.dp
    )
    val horizontalPadding = if (isMyProfile) {
        24.dp
    } else {
        if (isLive) 20.dp else 16.dp
    }
    Box(
        modifier = modifier
            .height(buttonHeight)
            .clip(shape = RoundedCornerShape(buttonHeight / 2))
    ) {
        if (hazeState != null && !isLive) {
            Box(modifier = Modifier
                .matchParentSize()
                .hazeEffect(hazeState))
        } else {
            val backgroundColor = if (isLive) {
                R.color.color_background_highlight_1_default.asColor()
            } else {
                R.color.alpha_white_20.asColor()
            }
            Box(modifier = Modifier
                .matchParentSize()
                .background(backgroundColor))
        }
        Row(
            modifier = Modifier
                .fillMaxHeight()
                .padding(horizontal = horizontalPadding),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            if (isLive) {
                val compTagOn by rememberLottieComposition(Asset("lottie/liveplace_tag_on.json"))
                LottieAnimation(
                    composition = compTagOn,
                    iterations = LottieConstants.IterateForever,
                    modifier = Modifier
                        .width(42.dp)
                        .height(20.dp),
                )
                HorizontalSpace(8.dp)
            } else {
                if (showSofaIcon) {
                    val iconColor = if (isNotCreated) {
                        R.color.color_text_highlight_default.asColor()
                    } else {
                        R.color.neutral_white.asColor()
                    }
                    IconFontText(
                        iconRes = R.string.ic_sofa_multi,
                        iconColor = iconColor,
                        iconSize = if (isMyProfile) 18.dp else 12.dp,
                    )
                    HorizontalSpace(if (isMyProfile) 8.dp else 4.dp)
                }
            }
            Text(
                text = textRes,
                color = textColor,
                style = if (isLive || isMyProfile) {
                    TextStyles.labelLarge()
                } else{
                    TextStyles.labelMedium()
                }
            )
        }
    }
}

@Composable
@Preview
private fun PreviewLivePlaceEntranceButton() {
    LivePlaceEntranceButton(
        textRes = R.string.join_live_place.asString(),
        showSofaIcon = true,
        isLiveLambda = { true }
    )
}