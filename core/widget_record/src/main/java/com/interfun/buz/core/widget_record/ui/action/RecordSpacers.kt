package com.interfun.buz.core.widget_record.ui.action

import androidx.compose.foundation.layout.Spacer
import androidx.compose.runtime.Composable
import androidx.compose.runtime.SideEffect
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.unit.dp
import androidx.constraintlayout.compose.ConstrainedLayoutReference
import androidx.constraintlayout.compose.ConstraintLayoutScope
import androidx.constraintlayout.compose.Dimension
import com.interfun.buz.base.widget.area.CircleArea
import com.interfun.buz.compose.ktx.getScreenHeight
import com.interfun.buz.compose.ktx.linkToRef
import com.interfun.buz.compose.ktx.measureTextHeightDp
import com.interfun.buz.compose.styles.TextStyles
import com.interfun.buz.compose.utils.RecomposeCountHelper
import com.interfun.buz.core.widget_record.entity.LocalRecordUIConfig
import com.interfun.buz.core.widget_record.entity.RecordAreaType

/**
 * <AUTHOR>
 * @date 2025/5/30
 * @desc 用来辅助定位的各种Space，并且也会获取和保存录音、取消、预览按钮的对应触摸区域，用于手势触摸判断位置
 */
@Composable
internal fun ConstraintLayoutScope.RecordSpacers(
    refSpaceRecord: ConstrainedLayoutReference,
    refSpaceRecordLottie: ConstrainedLayoutReference,
    refSpaceRecording: ConstrainedLayoutReference,
    refSpaceRecordCenter: ConstrainedLayoutReference,
    refSpaceCancel: ConstrainedLayoutReference,
    refSpacePreview: ConstrainedLayoutReference,
    refSpaceLockGuide: ConstrainedLayoutReference,
    refSpaceCountDown: ConstrainedLayoutReference,
    refSpaceVFLottieType1: ConstrainedLayoutReference,
    refSpaceVFLottieType2: ConstrainedLayoutReference,
    onAreaUpdate: (RecordAreaType, CircleArea) -> Unit,
) {
    SideEffect {
        RecomposeCountHelper.increment("RecordSpacers")
    }
    val (
        refRecordTop,
        refRecordLottieTop,
        refSpaceCountDownSingleLine
    ) = createRefs()
    val screenHeight = getScreenHeight()
    // 默认状态的录音按钮的上部位置
    Spacer(modifier = Modifier.constrainAs(refRecordTop) {
        bottom.linkTo(refSpaceRecording.top)
        start.linkTo(refSpaceRecording.start)
        end.linkTo(refSpaceRecording.end)
        width = Dimension.value(0.dp)
        height = Dimension.value(screenHeight * 0.0246f)
    })
    // 录音中Lottie动画的顶部
    Spacer(modifier = Modifier.constrainAs(refRecordLottieTop) {
        bottom.linkTo(refSpaceRecordLottie.top)
        height = Dimension.value(0.dp)
    })

    RecordBtnSpacers(
        refSpaceRecord = refSpaceRecord,
        refSpaceRecordLottie = refSpaceRecordLottie,
        refSpaceRecording = refSpaceRecording,
        refSpaceRecordCenter = refSpaceRecordCenter,
        onAreaUpdate = onAreaUpdate,
    )

    ActionBtnSpacers(
        refRecordTop = refRecordTop,
        refSpaceCancel = refSpaceCancel,
        refSpacePreview = refSpacePreview,
        refSpaceRecording = refSpaceRecording,
        onAreaUpdate = onAreaUpdate,
    )

    CountDownTextSpacer(
        refRecordLottieTop = refRecordLottieTop,
        refSpaceCountDown = refSpaceCountDown,
        refSpaceCountDownSingleLine = refSpaceCountDownSingleLine,
    )

    LockSpacers(
        refRecordTop = refRecordTop,
        refSpaceLockGuide = refSpaceLockGuide,
        refSpaceCountDownSingleLine = refSpaceCountDownSingleLine,
        onAreaUpdate = onAreaUpdate,
    )
    VoiceFilterLottieSpacers(
        refSpaceRecord = refSpaceRecord,
        refSpaceRecordCenter = refSpaceRecordCenter,
        refSpaceVFLottieType1 = refSpaceVFLottieType1,
        refSpaceVFLottieType2 = refSpaceVFLottieType2,
    )
}

@Composable
private fun ConstraintLayoutScope.RecordBtnSpacers(
    refSpaceRecord: ConstrainedLayoutReference,
    refSpaceRecordLottie: ConstrainedLayoutReference,
    refSpaceRecording: ConstrainedLayoutReference,
    refSpaceRecordCenter: ConstrainedLayoutReference,
    onAreaUpdate: (RecordAreaType, CircleArea) -> Unit,
) {
    val config = LocalRecordUIConfig.current

    // 录音按钮的上边距
    val recordPaddingTop = createGuidelineFromTop(fraction = config.recordTopPaddingPercent)
    // 录音按钮的下边距
    val recordPaddingBottom =
        createGuidelineFromBottom(fraction = config.recordBottomPaddingPercent)

    // 录音按钮对应的位置
    Spacer(
        modifier = Modifier
            .onGloballyPositioned { layoutCoordinates ->
                val areaRecord = CircleArea.convertFrom(layoutCoordinates)
                onAreaUpdate.invoke(RecordAreaType.Record, areaRecord)
                val areaRecording = areaRecord * config.recordBtnScaleWhenPress
                onAreaUpdate.invoke(RecordAreaType.Recording, areaRecording)
                val areaVoiceFilterRecord = areaRecord * config.voiceFilterUnRecordBtnSizeScale
                onAreaUpdate.invoke(RecordAreaType.VoiceFilterRecord, areaVoiceFilterRecord)
                val areaRecordingWhileLocking = areaRecording * config.recordLockingScale
                onAreaUpdate.invoke(RecordAreaType.RecordingWhileLocking, areaRecordingWhileLocking)
            }
            .constrainAs(refSpaceRecord) {
                top.linkTo(recordPaddingTop)
                bottom.linkTo(recordPaddingBottom)
                start.linkTo(parent.start)
                end.linkTo(parent.end)
                width = Dimension.ratio("1:1")
                height = Dimension.percent(config.recordBtnHeightPercent)
            })

    // 录音按钮Lottie位置
    Spacer(modifier = Modifier.constrainAs(refSpaceRecordLottie) {
        linkToRef(refSpaceRecord)
        width = Dimension.ratio("1:1")
        height = Dimension.percent(config.recordBtnHeightPercent * config.recordLottieScale)
    })

    // 录音按钮中间的位置，用来定位 VoiceFilter 动画
    Spacer(modifier = Modifier.constrainAs(refSpaceRecordCenter) {
        linkToRef(refSpaceRecord)
        height = Dimension.value(0.dp)
    })

    // 录音中按钮对应的位置
    Spacer(
        modifier = Modifier.constrainAs(refSpaceRecording) {
            linkToRef(refSpaceRecord)
            width = Dimension.ratio("1:1")
            height =
                Dimension.percent(config.recordBtnHeightPercent * config.recordBtnScaleWhenPress)
        }
    )
}

@Composable
private fun ConstraintLayoutScope.LockSpacers(
    refRecordTop: ConstrainedLayoutReference,
    refSpaceLockGuide: ConstrainedLayoutReference,
    refSpaceCountDownSingleLine: ConstrainedLayoutReference,
    onAreaUpdate: (RecordAreaType, CircleArea) -> Unit,
) {
    val config = LocalRecordUIConfig.current
    val screenHeight = getScreenHeight()
    val (refSpaceLockBtn, refSpaceLockGuideTop) = createRefs()
    Spacer(modifier = Modifier.constrainAs(refSpaceLockGuideTop) {
        if (config.showRecordCountDown) {
            bottom.linkTo(refSpaceCountDownSingleLine.top, margin = 22.dp)
        } else {
            bottom.linkTo(refRecordTop.top)
        }
        height = Dimension.value(screenHeight * 0.043f)
    })
    // Lock按钮对应的位置
    Spacer(
        modifier = Modifier
            .onGloballyPositioned { layoutCoordinates ->
                onAreaUpdate.invoke(
                    RecordAreaType.Lock, CircleArea.convertFrom(layoutCoordinates)
                )
            }
            .constrainAs(refSpaceLockBtn) {
                bottom.linkTo(refSpaceLockGuideTop.top)
                start.linkTo(parent.start)
                end.linkTo(parent.end)
                height =
                    Dimension.percent(config.actionBtnHeightPercent * config.actionBtnScalePercent)
                width = Dimension.ratio("1:1")
            })
    // LockGuide对应的位置
    Spacer(
        modifier = Modifier
            .constrainAs(refSpaceLockGuide) {
                top.linkTo(refSpaceLockBtn.top)
                start.linkTo(refSpaceLockBtn.start)
                end.linkTo(refSpaceLockBtn.end)
                bottom.linkTo(refSpaceLockGuideTop.bottom)
                height = Dimension.fillToConstraints
                width = Dimension.fillToConstraints
            })

}

@Composable
private fun ConstraintLayoutScope.VoiceFilterLottieSpacers(
    refSpaceRecord: ConstrainedLayoutReference,
    refSpaceRecordCenter: ConstrainedLayoutReference,
    refSpaceVFLottieType1: ConstrainedLayoutReference,
    refSpaceVFLottieType2: ConstrainedLayoutReference,
) {
    val config = LocalRecordUIConfig.current
    Spacer(modifier = Modifier.constrainAs(refSpaceVFLottieType1) {
        start.linkTo(refSpaceRecord.start)
        end.linkTo(refSpaceRecord.end)
        top.linkTo(refSpaceRecord.top)
        bottom.linkTo(refSpaceRecord.bottom)
        height = Dimension.percent(config.recordBtnHeightPercent * config.voiceFilterRecordLottie1Scale)
        width = Dimension.ratio("1:1")
    })
    Spacer(modifier = Modifier.constrainAs(refSpaceVFLottieType2) {
        start.linkTo(refSpaceRecord.start)
        end.linkTo(refSpaceRecord.end)
        bottom.linkTo(refSpaceRecordCenter.bottom)
        height = Dimension.percent(config.recordBtnHeightPercent * config.voiceFilterRecordLottie2Scale)
        width = Dimension.ratio("5:4")
        verticalBias = 0f
    })
}

@Composable
private fun ConstraintLayoutScope.CountDownTextSpacer(
    refRecordLottieTop: ConstrainedLayoutReference,
    refSpaceCountDown: ConstrainedLayoutReference,
    refSpaceCountDownSingleLine: ConstrainedLayoutReference,
) {
    val lineHeight = measureTextHeightDp("🤣0:00", TextStyles.bodyMedium()).coerceIn(14.dp, 22.dp)
    // 倒计时的位置
    Spacer(modifier = Modifier.constrainAs(refSpaceCountDownSingleLine) {
        top.linkTo(refRecordLottieTop.top)
        bottom.linkTo(refRecordLottieTop.top)
        start.linkTo(parent.start)
        end.linkTo(parent.end)
        height = Dimension.value(lineHeight)
        width = Dimension.value(0.dp)
    })
    Spacer(modifier = Modifier.constrainAs(refSpaceCountDown) {
        start.linkTo(parent.start, margin = 20.dp)
        end.linkTo(parent.end, margin = 20.dp)
        top.linkTo(parent.top)
        bottom.linkTo(refSpaceCountDownSingleLine.bottom)
        width = Dimension.fillToConstraints
        height = Dimension.fillToConstraints
    })
}

@Composable
private fun ConstraintLayoutScope.ActionBtnSpacers(
    refRecordTop: ConstrainedLayoutReference,
    refSpaceCancel: ConstrainedLayoutReference,
    refSpacePreview: ConstrainedLayoutReference,
    refSpaceRecording: ConstrainedLayoutReference,
    onAreaUpdate: (RecordAreaType, CircleArea) -> Unit,
) {
    val (
        refSpaceStart,
        refSpaceStartGuideLine,
        refSpaceStartBoundary,
        refSpaceEnd,
        refSpaceEndGuideLine,
        refSpaceEndBoundary,
    ) = createRefs()

    val barrierStart = createEndBarrier(refSpaceStartGuideLine, refSpaceStartBoundary)
    val barrierEnd = createStartBarrier(refSpaceEndGuideLine, refSpaceEndBoundary)
    val config = LocalRecordUIConfig.current
    // Cancel按钮对应显示区域
    Spacer(
        modifier = Modifier
            .onGloballyPositioned { layoutCoordinates ->
                onAreaUpdate.invoke(
                    RecordAreaType.Cancel, CircleArea.convertFrom(layoutCoordinates)
                )
            }
            .constrainAs(refSpaceCancel) {
                start.linkTo(barrierStart)
                top.linkTo(refRecordTop.top)
                height = Dimension.percent(config.actionBtnHeightPercent)
                width = Dimension.ratio("1:1")
            })

    // Preview按钮对应显示位置
    Spacer(
        modifier = Modifier
            .onGloballyPositioned { layoutCoordinates ->
                onAreaUpdate.invoke(
                    RecordAreaType.Preview, CircleArea.convertFrom(layoutCoordinates)
                )
            }
            .constrainAs(refSpacePreview) {
                end.linkTo(barrierEnd)
                top.linkTo(refRecordTop.top)
                height = Dimension.percent(config.actionBtnHeightPercent)
                width = Dimension.ratio("1:1")
            })
    // 用来定位按钮的start位置
    Spacer(modifier = Modifier.constrainAs(refSpaceStart) {
        end.linkTo(refSpaceRecording.start, margin = config.btnCancelOrPreviewHorizontalMargin)
        top.linkTo(refRecordTop.top)
        height = Dimension.percent(config.actionBtnHeightPercent)
        width = Dimension.ratio("1:1")
    })
    Spacer(modifier = Modifier.constrainAs(refSpaceStartGuideLine) {
        start.linkTo(refSpaceStart.start)
        width = Dimension.value(0.dp)
    })
    Spacer(modifier = Modifier.constrainAs(refSpaceStartBoundary) {
        start.linkTo(parent.start)
        width = Dimension.value(24.dp)
    })
    // 用来定位按钮的end位置
    Spacer(modifier = Modifier.constrainAs(refSpaceEnd) {
        start.linkTo(refSpaceRecording.end, margin = config.btnCancelOrPreviewHorizontalMargin)
        top.linkTo(refRecordTop.top)
        height = Dimension.percent(config.actionBtnHeightPercent)
        width = Dimension.ratio("1:1")
    })
    Spacer(modifier = Modifier.constrainAs(refSpaceEndGuideLine) {
        end.linkTo(refSpaceEnd.end)
        width = Dimension.value(0.dp)
    })
    Spacer(modifier = Modifier.constrainAs(refSpaceEndBoundary) {
        end.linkTo(parent.end)
        width = Dimension.value(24.dp)
    })
}