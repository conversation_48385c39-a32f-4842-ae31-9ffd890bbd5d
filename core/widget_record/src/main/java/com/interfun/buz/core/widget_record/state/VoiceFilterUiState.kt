package com.interfun.buz.core.widget_record.state

import com.interfun.buz.compose.components.TabOption

/**
 * <AUTHOR>
 * @date 2025/6/24
 * @desc
 */
data class VoiceFilterUiState(
    val isShowTabs: Bo<PERSON>an,
    val tabOptionList: List<TabOption>,
    val voiceFilterList: List<VoiceFilterUiData>,
    val currentSelectTabType: Int,
    val currentSelectVoiceFilter: VoiceFilterUiData?
) {
    companion object {
        val Empty = VoiceFilterUiState(
            isShowTabs = false,
            tabOptionList = emptyList(),
            voiceFilterList = listOf(VoiceFilterUiData.NoFilter),
            currentSelectTabType = TabOption.allOptionTab.type,
            currentSelectVoiceFilter = null
        )
    }
}