package com.interfun.buz.core.widget_record.ui

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.constraintlayout.compose.ConstraintLayout
import com.interfun.buz.base.ktx.calculateValue
import com.interfun.buz.base.widget.area.IArea
import com.interfun.buz.compose.components.InitPreview
import com.interfun.buz.compose.ktx.fillToRef
import com.interfun.buz.compose.ktx.linkToRef
import com.interfun.buz.compose.ktx.scale
import com.interfun.buz.compose.utils.RecomposeCountHelper
import com.interfun.buz.core.widget_record.entity.LocalRecordUIConfig
import com.interfun.buz.core.widget_record.entity.RecordAreaType
import com.interfun.buz.core.widget_record.entity.RecordOperateStatus
import com.interfun.buz.core.widget_record.entity.RecordUIConfig
import com.interfun.buz.core.widget_record.state.VoiceFilterUiData
import com.interfun.buz.core.widget_record.ui.action.RecordSpacers

/**
 * <AUTHOR>
 * @date 2024/11/14
 * @desc
 */
@Composable
fun RecordScreen(
    isRecording: () -> Boolean,
    isShowAIBg: () -> Boolean,
    isShowTabs: () -> Boolean,
    isInterceptGestureLambda: () -> Boolean,
    isInVoiceFilterMode: () -> Boolean,
    switchToVFProgressLambda: () -> Float,
    currentPressAreaLambda: () -> RecordAreaType,
    recordOperateStatusLambda: () -> RecordOperateStatus,
    lastStartRecordTimeLambda: () -> Long,
    lockGuideProgressLambda: () -> Float,
    currentVoiceFilter: () -> VoiceFilterUiData?,
    getAreaLambda: (RecordAreaType) -> IArea,
    onAreaUpdate: (RecordAreaType, IArea) -> Unit,
    modifier: Modifier = Modifier,
) {
    SideEffect {
        RecomposeCountHelper.increment("RecordScreen")
    }
    val config = LocalRecordUIConfig.current
    ConstraintLayout(
        modifier = modifier
            .fillMaxSize()
            .graphicsLayer {
                translationY = calculateValue(
                    start = 0f,
                    end = if (isShowTabs()) config.recordTransitionYWhenShowTabs else 0f,
                    progress = switchToVFProgressLambda()
                )
            }
    ) {
        val (
            refSpaceRecord,
            refSpaceRecordLottie,
            refSpaceRecording,
            refSpaceRecordCenter,
            refSpaceCancel,
            refSpacePreview,
            refSpaceLockGuide,
            refSpaceCountDown,
            refSpaceVFLottieType1,
            refSpaceVFLottieType2,
        ) = createRefs()

        RecordSpacers(
            refSpaceRecord = refSpaceRecord,
            refSpaceRecordLottie = refSpaceRecordLottie,
            refSpaceRecording = refSpaceRecording,
            refSpaceRecordCenter = refSpaceRecordCenter,
            refSpaceCancel = refSpaceCancel,
            refSpacePreview = refSpacePreview,
            refSpaceLockGuide = refSpaceLockGuide,
            refSpaceCountDown = refSpaceCountDown,
            onAreaUpdate = onAreaUpdate,
            refSpaceVFLottieType1 = refSpaceVFLottieType1,
            refSpaceVFLottieType2 = refSpaceVFLottieType2,
        )

        val (
            refBtnRecord,
            refBtnCancel,
            refBtnPreview,
            refMicIcon,
            refLottieRecording,
            refLottieVoiceFilter,
            refLockingProgress,
            refLockGuide,
            refCountDown,
        ) = createRefs()

        RecordButton(
            isRecording = isRecording,
            isShowAIBg = isShowAIBg,
            isRvWTScrolling = isInterceptGestureLambda,
            isInVoiceFilterMode = isInVoiceFilterMode,
            switchToVFProgressLambda = switchToVFProgressLambda,
            currentPressAreaLambda = currentPressAreaLambda,
            recordOperateStatusLambda = recordOperateStatusLambda,
            lastStartRecordTimeLambda = lastStartRecordTimeLambda,
            currentVoiceFilter = currentVoiceFilter,
            recordLottieModifier = Modifier.constrainAs(refLottieRecording) {
                fillToRef(refSpaceRecordLottie)
            },
            recordButtonModifier = Modifier.constrainAs(refBtnRecord) {
                fillToRef(refSpaceRecord)
            },
            recordMicIconModifier = Modifier.constrainAs(refMicIcon) {
                linkToRef(refSpaceRecord)
            },
            recordLockingProgressModifier = Modifier.constrainAs(refLockingProgress) {
                fillToRef(refSpaceRecording)
            }
        )

        VoiceFilterRecordingLottie(
            isRecording = isRecording,
            isInCancelOrPreviewArea = {
                currentPressAreaLambda().let {
                    it == RecordAreaType.Cancel || it == RecordAreaType.Preview
                }
            },
            isInVoiceFilterMode = isInVoiceFilterMode,
            currentVoiceFilter = currentVoiceFilter,
            modifierLambda = {
                if (currentVoiceFilter()?.recordingAnimationType == 2) {
                    Modifier.constrainAs(refLottieVoiceFilter) {
                        fillToRef(refSpaceVFLottieType2)
                    }
                } else {
                    Modifier.constrainAs(refLottieVoiceFilter) {
                        fillToRef(refSpaceVFLottieType1)
                    }
                }
            },
        )

        CancelButton(
            isVisibleLambda = isRecording,
            isInCancelAreaLambda = { currentPressAreaLambda() == RecordAreaType.Cancel },
            modifier = Modifier.constrainAs(refBtnCancel) {
                fillToRef(refSpaceCancel)
            }
        )

        PreviewButton(
            isVisibleLambda = isRecording,
            isInPreviewAreaLambda = { currentPressAreaLambda() == RecordAreaType.Preview },
            modifier = Modifier.constrainAs(refBtnPreview) {
                fillToRef(refSpacePreview)
            }
        )

        LockGuide(
            isVisibleLambda = { isRecording() && !recordOperateStatusLambda().isLocking },
            isInLockAreaLambda = { currentPressAreaLambda() == RecordAreaType.Lock },
            getLockAreaLambda = { getAreaLambda(RecordAreaType.Lock) },
            lockGuideProgressLambda = { lockGuideProgressLambda().coerceIn(0f, 1f) },
            isInVoiceFilterModeLambda = isInVoiceFilterMode,
            currentVoiceFilterLambda = currentVoiceFilter,
            modifier = Modifier.constrainAs(refLockGuide) {
                fillToRef(refSpaceLockGuide)
            }
        )

        InnerRecordCountDown(
            modifier = Modifier.constrainAs(refCountDown) {
                fillToRef(refSpaceCountDown)
            },
            isRecordingLambda = isRecording,
            isLockingLambda = { recordOperateStatusLambda().isLocking },
            currentPressAreaLambda = currentPressAreaLambda,
            voiceFilterLambda = {
                currentVoiceFilter().takeIf {
                    isInVoiceFilterMode() && it?.filterId != VoiceFilterUiData.NO_FILTER_ID
                }
            }
        )
    }
}

@Composable
private fun RecordButton(
    isRecording: () -> Boolean,
    isShowAIBg: () -> Boolean,
    isRvWTScrolling: () -> Boolean,
    isInVoiceFilterMode: () -> Boolean,
    switchToVFProgressLambda: () -> Float,
    currentPressAreaLambda: () -> RecordAreaType,
    recordOperateStatusLambda: () -> RecordOperateStatus,
    lastStartRecordTimeLambda: () -> Long,
    currentVoiceFilter: () -> VoiceFilterUiData?,
    recordLottieModifier: Modifier,
    recordButtonModifier: Modifier,
    recordMicIconModifier: Modifier,
    recordLockingProgressModifier: Modifier,
) {
    SideEffect {
        RecomposeCountHelper.increment("RecordButton")
    }
    val switchVoiceFilterProgress  = switchToVFProgressLambda()
    val isSwitchFinished = if (isInVoiceFilterMode()) {
        switchVoiceFilterProgress == 1f
    } else {
        switchVoiceFilterProgress == 0f
    }
    val isPressingOrLocking = recordOperateStatusLambda().isPressingOrLocking
    val showBigRecordProgress by animateFloatAsState(
        targetValue = if (isInVoiceFilterMode() && isPressingOrLocking) 1f else 0f,
    )

    val isShowBigRecord = if (isInVoiceFilterMode()) {
        showBigRecordProgress > 0f || !isSwitchFinished
    } else {
        true
    }
    if (!isShowBigRecord) return

    val isInActionBtnArea by remember {
        derivedStateOf {
            currentPressAreaLambda().let {
                it == RecordAreaType.Cancel || it == RecordAreaType.Preview || it == RecordAreaType.Lock
            }
        }
    }
    val config = LocalRecordUIConfig.current
    
    RecordLottie(
        isRecording = isRecording,
        isInActionBtnArea = { isInActionBtnArea },
        recordOperateStatusLambda = recordOperateStatusLambda,
        voiceFilterBgColor = {
            if (isInVoiceFilterMode()) {
                currentVoiceFilter()?.backgroundColor
            } else {
                null
            }
        },
        modifier = recordLottieModifier
    )
    val scaleWhenPress by animateFloatAsState(
        if (!isInVoiceFilterMode() && isPressingOrLocking) config.recordBtnScaleWhenPress else 1f)

    val scaleWhenRvWTScrolling by animateFloatAsState(
        if (isRvWTScrolling()) config.recordBtnScaleWhenPress else 1f
    )

    val scaleWhenLocking by animateFloatAsState(
        if (recordOperateStatusLambda().isLocking) config.recordLockingScale else 1f
    )

    val getCurrentValuableVoiceFilter = {
        if (isSwitchFinished) {
            val filter = currentVoiceFilter()
            if (isInVoiceFilterMode()
                && filter != null
                && filter.filterId != VoiceFilterUiData.NO_FILTER_ID
            ) {
                filter
            } else {
                null
            }
        } else {
            null
        }
    }

    RecordButtonBg(
        isShowAIBg = isShowAIBg,
        isRecording = isRecording,
        isInActionBtnArea = { isInActionBtnArea },
        recordOperateStatusLambda = recordOperateStatusLambda,
        currentVoiceFilter = getCurrentValuableVoiceFilter,
        modifier = recordButtonModifier.graphicsLayer {
            val recordingScale = if (isSwitchFinished) {
                if (showBigRecordProgress > 0f) {
                    calculateValue(
                        start = config.voiceFilterUnRecordBtnSizeScale,
                        end = config.recordBtnScaleWhenPress,
                        progress = showBigRecordProgress
                    )
                } else {
                    scaleWhenPress * scaleWhenRvWTScrolling
                }
            } else {
                calculateValue(
                    start = 1f,
                    end = config.voiceFilterUnRecordBtnSizeScale,
                    progress = switchVoiceFilterProgress
                )
            }
            val finalScale = recordingScale * scaleWhenLocking
            scale(finalScale)
        }
    )

    RecordButtonIcon(
        modifier = recordMicIconModifier,
        isRecordingLambda = isRecording,
        isInActionBtnAreaLambda = { isInActionBtnArea },
        isInVoiceFilterModeLambda = isInVoiceFilterMode,
        recordOperateStatusLambda = recordOperateStatusLambda,
        currentVoiceFilterLambda = getCurrentValuableVoiceFilter
    )

    RecordButtonLockingProgress(
        modifier = recordLockingProgressModifier,
        isLockingLambda = { recordOperateStatusLambda().isLocking },
        lastStartRecordTimeLambda = lastStartRecordTimeLambda,
        currentVoiceFilterLambda = getCurrentValuableVoiceFilter,
    )
}

@Composable
private fun PreviewRecordScreenDefault(
    isEnable: Boolean = true,
    isPressing: Boolean = false,
    isLocking: Boolean = false,
    isRecording: Boolean = false,
    isShowAIBg: Boolean = false,
    isInCancelArea: Boolean = false,
    isInPreviewArea: Boolean = false,
    isInLockArea: Boolean = false,
) {
    CompositionLocalProvider(LocalRecordUIConfig provides RecordUIConfig()) {
        Box(modifier = Modifier.height(300.dp)) {
            RecordScreen(
                isRecording = { isRecording },
                isInterceptGestureLambda = { false },
                isShowAIBg = { isShowAIBg },
                isShowTabs = { false },
                currentPressAreaLambda = {
                    if (isInCancelArea) {
                        RecordAreaType.Cancel
                    } else if (isInPreviewArea) {
                        RecordAreaType.Preview
                    } else if (isInLockArea) {
                        RecordAreaType.Lock
                    } else {
                        RecordAreaType.None
                    }
                },
                recordOperateStatusLambda = {
                    RecordOperateStatus(
                        isEnable,
                        isPressing,
                        isLocking
                    )
                },
                lastStartRecordTimeLambda = { 0L },
                lockGuideProgressLambda = { 0f },
                isInVoiceFilterMode = { false },
                currentVoiceFilter = { null },
                getAreaLambda = { IArea.Empty },
                onAreaUpdate = { _, _ -> },
                switchToVFProgressLambda = { 0f }
            )
        }
    }
}

@Composable
@Preview(heightDp = 1200)
private fun PreviewRecordScreen() {
    InitPreview()
    Column {
        PreviewRecordScreenDefault()
        PreviewRecordScreenDefault(isShowAIBg = true)
        PreviewRecordScreenDefault(isPressing = true, isRecording = true)
        PreviewRecordScreenDefault(isPressing = true, isRecording = true, isInCancelArea = true)
    }
}
