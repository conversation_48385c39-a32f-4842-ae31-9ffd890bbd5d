package com.interfun.buz.core.widget_record.ui

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.interfun.buz.compose.components.CommonRedDot
import com.interfun.buz.compose.components.IconFontText
import com.interfun.buz.compose.ktx.asColor
import com.interfun.buz.compose.ktx.isRtl
import com.interfun.buz.compose.modifier.debouncedClickable
import com.interfun.buz.core.widget_record.R
import com.interfun.buz.core.widget_record.entity.LocalRecordUIConfig

/**
 * <AUTHOR>
 * @date 2025/5/12
 * @desc
 */
@Composable
fun InnerVoiceFilterEntry(
    modifier: Modifier = Modifier,
    isPressingOrLockingLambda: () -> Boolean,
    isInVoiceFilterMode: () -> Boolean,
    showRedDotLambda: () -> Boolean,
    onClick: () -> Unit = {}
) {
    val config = LocalRecordUIConfig.current
    if (!config.enableVoiceFilterMode() || !config.showInnerVoiceFilterEntry) return
    AnimatedVisibility(
        modifier = modifier,
        visible = !isPressingOrLockingLambda() && !isInVoiceFilterMode(),
        enter = fadeIn(),
        exit = fadeOut()
    ) {
        Box {
            Box(
                modifier = Modifier
                    .size(48.dp)
                    .border(
                        width = 1.dp,
                        color = R.color.color_outline_3_default.asColor(),
                        shape = CircleShape
                    )
                    .debouncedClickable(onClick = onClick)
            ) {
                IconFontText(
                    modifier = Modifier.align(Alignment.Center),
                    iconRes = R.string.ic_wt_speaking,
                    iconSize = 20.dp,
                    iconColor = R.color.color_text_white_primary.asColor()
                )
            }

            if (showRedDotLambda()) {
                CommonRedDot(
                    modifier = Modifier
                        .align(Alignment.TopEnd)
                        .offset(
                            x = if (isRtl()) (-5).dp else 5.dp,
                            y = (-5).dp
                        ),
                    strokeColor = R.color.color_background_4_default.asColor()
                )
            }
        }
    }
}