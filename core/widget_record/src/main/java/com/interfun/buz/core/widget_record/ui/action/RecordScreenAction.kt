package com.interfun.buz.core.widget_record.ui.action

import com.buz.idl.common.bean.ActionInfo
import com.interfun.buz.compose.components.TabOption
import com.interfun.buz.core.widget_record.entity.VoiceFilterSelectType
import com.interfun.buz.core.widget_record.state.VoiceFilterUiData

/**
 * <AUTHOR>
 * @date 2024/12/2
 * @desc
 */
sealed interface RecordScreenAction {
    data object OnSelectVoiceExposure : RecordScreenAction
    data object OnClickPreviewVoiceFilter : RecordScreenAction
    data object OnClickQuit : RecordScreenAction
    data object OnClickVoiceFilterEntry : RecordScreenAction
    data object OnRequestRecordPermission : RecordScreenAction
    data object OnClickRecordWhenNotEnabled : RecordScreenAction
    data class OnClickClear(
        val tabType: Int,
        val voiceFilterId: Long?
    ) : RecordScreenAction
    data class OnSelectVoiceFilter(
        val tabType: Int,
        val voiceFilter: VoiceFilterUiData,
        val selectType: VoiceFilterSelectType
    ) : RecordScreenAction
    data class OnSelectVoiceFilterTab(val tab: TabOption) : RecordScreenAction
    data class OnClickCampaignEntry(val action: ActionInfo?) : RecordScreenAction
    data class OnVoiceFilterScrollingChanged(val isScrolling: Boolean) : RecordScreenAction
}