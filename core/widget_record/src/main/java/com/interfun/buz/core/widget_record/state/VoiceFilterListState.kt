package com.interfun.buz.core.widget_record.state

import androidx.compose.animation.core.tween
import androidx.compose.foundation.gestures.ScrollableState
import androidx.compose.foundation.gestures.animateScrollBy
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.runtime.*
import androidx.compose.ui.input.nestedscroll.NestedScrollConnection
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.Velocity
import androidx.compose.ui.unit.dp
import com.buz.idl.common.bean.ActionInfo
import com.interfun.buz.base.ktx.DefaultCallback
import com.interfun.buz.base.ktx.calculateValue
import com.interfun.buz.base.ktx.getOrNull
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.compose.ktx.dpToPx
import com.interfun.buz.compose.ktx.rememberMutableBoolean
import com.interfun.buz.core.widget_record.entity.RecordUIConfig
import com.interfun.buz.core.widget_record.entity.VoiceFilterSelectType
import com.interfun.buz.core.widget_record.ui.action.RecordScreenAction
import com.interfun.buz.core.widget_record.ui.action.RecordScreenAction.*
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.launch
import kotlin.math.abs
import kotlin.math.floor
import kotlin.math.roundToInt

/**
 * <AUTHOR>
 * @date 2025/6/27
 * @desc
 */
@Stable
internal class VoiceFilterListState(
    val itemWidthMax: Dp,
    val itemWidthMin: Dp,
    val nestedScrollConnection: NestedScrollConnection,
    val lazyListStateLambda: () -> LazyListState,
    val totalScrollDistanceLambda: () -> Float,
    val currentScrollPosLambda: () -> Float,
    val currentScrollIndexLambda: () -> Int,
    val isListScrollingLambda: () -> Boolean,
    val onScrollToIndex: suspend (Int) -> Unit,
    val onClickItem: (Int) -> Unit,
    val onClickName: (Int) -> Unit,
    val onClickClear: () -> Unit,
    val onClickQuit: () -> Unit,
    val onCampaignEntryClick: (ActionInfo?) -> Unit,
)

private const val TAG = "VoiceFilterScrollState"

@Composable
internal fun rememberVoiceFilterListState(
    scope: CoroutineScope,
    config: RecordUIConfig,
    maxHeight: Dp,
    voiceFilterUiStateLambda: () -> VoiceFilterUiState,
    isInVoiceFilterModeLambda: () -> Boolean,
    isItemTapEnableLambda: () -> Boolean,
    voiceFilterAction: (RecordScreenAction) -> Unit,
    onSetPressDownCallback: (DefaultCallback) -> Unit,
): VoiceFilterListState {
    // 为每个tab维护独立的LazyListState
    val tabListStateMap = remember { mutableMapOf<Int, LazyListState>() }
    val listStateLambda = remember {
        {
            val tabType = voiceFilterUiStateLambda().currentSelectTabType
            tabListStateMap.getOrPut(tabType) { LazyListState() }
        }
    }

    val btnRecordSize = maxHeight * config.recordBtnHeightPercent
    val itemWidthMax = btnRecordSize * config.voiceFilterItemSizeScaleMax
    val itemWidthMin = (itemWidthMax * config.voiceFilterItemSizeScaleMin).coerceAtLeast(minimumValue = 60.dp)
    val itemWidthMinPx = itemWidthMin.dpToPx()

    val totalScrollDistanceLambda = {
        val firstVisItemDistance = listStateLambda().firstVisibleItemIndex * itemWidthMinPx
        val firstVisItemScrollOffset = listStateLambda().firstVisibleItemScrollOffset
        firstVisItemDistance + firstVisItemScrollOffset
    }
    val currentScrollPosLambda = { totalScrollDistanceLambda() / itemWidthMinPx }
    val currentScrollIndexLambda = { (totalScrollDistanceLambda() / itemWidthMinPx).roundToInt() }

    // 检查并更新当前居中滤镜的函数
    val onCheckAndUpdateCenteredVoiceFilter = {
        val currentCenteredIndex = currentScrollIndexLambda()
        val centeredVoiceFilter =
            voiceFilterUiStateLambda().voiceFilterList.getOrNull(currentCenteredIndex)
        val currentSelectedVoiceFilter = voiceFilterUiStateLambda().currentSelectVoiceFilter

        logInfo(
            TAG, "onCheckAndUpdateCenteredVoiceFilter: " +
                "centerIndex = $currentCenteredIndex, " +
                "centerId = ${centeredVoiceFilter?.filterId}, " +
                "currentId = ${currentSelectedVoiceFilter?.filterId}"
        )
        if (centeredVoiceFilter != null && centeredVoiceFilter != currentSelectedVoiceFilter) {
            logInfo(TAG, "updateCurrentSelectVoiceFilter: ${centeredVoiceFilter.filterId}")
            voiceFilterAction.invoke(
                OnSelectVoiceFilter(
                    tabType = voiceFilterUiStateLambda().currentSelectTabType,
                    voiceFilter = centeredVoiceFilter,
                    selectType = VoiceFilterSelectType.USER_SWIPE,
                )
            )
        }
    }

    // 滑动停止后触发居中滤镜更新
    val updateCenteredVoiceFilterAfterFling = suspend {
        val currentPosition = currentScrollPosLambda()
        val fractionalPart = currentPosition - floor(currentPosition)
        val offsetFraction = if (fractionalPart < 0.5) {
            -fractionalPart
        } else {
            1 - fractionalPart
        }
        val offsetDistance = offsetFraction * itemWidthMinPx
        if (offsetDistance != 0f) {
            listStateLambda().animateScrollBy(offsetDistance)
        }
        // 完成自动滚动后回调更新当前居中选中的滤镜
        onCheckAndUpdateCenteredVoiceFilter()
    }

    var isListAnimateScrolling by rememberMutableBoolean()

    val onScrollToIndex: suspend (Int) -> Unit = { index ->
        try {
            isListAnimateScrolling = true
            listStateLambda().animateScrollToIndex(
                targetIndex = index,
                listSize = voiceFilterUiStateLambda().voiceFilterList.size,
                totalScrollDistanceLambda = totalScrollDistanceLambda,
                itemWidth = itemWidthMinPx
            )
        } finally {
            isListAnimateScrolling = false
        }
    }

    // 滑动停止后，获取停止时的位置，然后自动滚到最相近的item中间，实现Snap的效果
    val nestedScrollConnection = object : NestedScrollConnection {
        override suspend fun onPreFling(available: Velocity): Velocity {
            // Check if we're at the edge of the list
            val isAtStart = listStateLambda().firstVisibleItemIndex == 0 &&
                listStateLambda().firstVisibleItemScrollOffset == 0
            val isAtEnd = listStateLambda().layoutInfo.visibleItemsInfo.lastOrNull()?.index ==
                voiceFilterUiStateLambda().voiceFilterList.size - 1

            // If at edge and fling velocity is in the direction of the edge
            if ((isAtStart && available.x > 0) || (isAtEnd && available.x < 0)) {
                logInfo(TAG, "updateCenteredVoiceFilter when in edge")
                updateCenteredVoiceFilterAfterFling()
            }
            return super.onPreFling(available)
        }

        override suspend fun onPostFling(consumed: Velocity, available: Velocity): Velocity {
            updateCenteredVoiceFilterAfterFling()
            return super.onPostFling(consumed, available)
        }
    }

    val isListScrollingLambda = {
        listStateLambda().isScrollInProgress || isListAnimateScrolling
    }

    val onClickItem: (Int) -> Unit = { index ->
        if (isItemTapEnableLambda() && !isListScrollingLambda() && currentScrollIndexLambda() != index) {
            scope.launch {
                onScrollToIndex(index)
                voiceFilterUiStateLambda().voiceFilterList.getOrNull(index)?.let {
                    voiceFilterAction.invoke(
                        OnSelectVoiceFilter(
                            tabType = voiceFilterUiStateLambda().currentSelectTabType,
                            voiceFilter = it,
                            selectType = VoiceFilterSelectType.USER_CLICK,
                        )
                    )
                }

            }
        }
    }

    val onClickName: (Int) -> Unit = { index ->
        if (isItemTapEnableLambda() && !isListScrollingLambda()) {
            if (currentScrollIndexLambda() == index) {
                voiceFilterAction.invoke(OnClickPreviewVoiceFilter)
            } else {
                onClickItem.invoke(index)
            }
        }
    }

    val onClickClear: () -> Unit = {
        scope.launch {
            onScrollToIndex(0)
            voiceFilterAction.invoke(
                OnClickClear(
                    tabType = voiceFilterUiStateLambda().currentSelectTabType,
                    voiceFilterId = voiceFilterUiStateLambda().currentSelectVoiceFilter?.filterId,
                )
            )
        }
    }

    val onClickQuit: () -> Unit = {
        scope.launch {
            onScrollToIndex(0)
            voiceFilterAction.invoke(OnClickQuit)
        }
    }

    val onCampaignEntryClick: (ActionInfo?) -> Unit = {
        voiceFilterAction.invoke(OnClickCampaignEntry(it))
    }

    LaunchedEffect(Unit) {
        // 手指按下时触发居中滤镜兜底更新
        val onPressDownCallback = {
            if (isInVoiceFilterModeLambda()) {
                onCheckAndUpdateCenteredVoiceFilter()
            }
        }
        onSetPressDownCallback.invoke(onPressDownCallback)
        // 监听滚动状态并更新到 ViewModel
        snapshotFlow {
            isListScrollingLambda()
        }.distinctUntilChanged().collectLatest { isScrolling ->
            // 通过 action 通知 ViewModel 更新滚动状态
            voiceFilterAction.invoke(OnVoiceFilterScrollingChanged(isScrolling))
        }
    }

    return remember {
        VoiceFilterListState(
            itemWidthMax = itemWidthMax,
            itemWidthMin = itemWidthMin,
            nestedScrollConnection = nestedScrollConnection,
            lazyListStateLambda = listStateLambda,
            totalScrollDistanceLambda = totalScrollDistanceLambda,
            currentScrollPosLambda = currentScrollPosLambda,
            currentScrollIndexLambda = currentScrollIndexLambda,
            isListScrollingLambda = isListScrollingLambda,
            onScrollToIndex = onScrollToIndex,
            onClickItem = onClickItem,
            onClickName = onClickName,
            onClickClear = onClickClear,
            onClickQuit = onClickQuit,
            onCampaignEntryClick = onCampaignEntryClick,
        )
    }
}

private suspend fun ScrollableState.animateScrollToIndex(
    targetIndex: Int,
    listSize: Int,
    totalScrollDistanceLambda: () -> Float,
    itemWidth: Float,
    minDuration: Float = 200f,
    maxDuration: Float = 500f,
) {
    val targetDistance = targetIndex * itemWidth
    val distanceGap = targetDistance - totalScrollDistanceLambda()
    val absGap = abs(distanceGap)
    // 防止小数点误差
    if (absGap > 1f) {
        val maxDistance = (listSize - 1) * itemWidth
        val duration = calculateValue(
            start = minDuration,
            end = maxDuration,
            progress = absGap / maxDistance
        ).roundToInt()
        this.animateScrollBy(
            value = distanceGap,
            animationSpec = tween(duration)
        )
        // 防止没滑到位，再兜底3次滑完剩下的距离
        var finalScrollCount = 0
        while (targetDistance - totalScrollDistanceLambda() != 0f && finalScrollCount++ < 3) {
            this.animateScrollBy(
                value = targetDistance - totalScrollDistanceLambda(),
                animationSpec = tween(50)
            )
        }
    }
}