package com.interfun.buz.core.widget_record.ui

import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import com.interfun.buz.compose.components.OptionTabs
import com.interfun.buz.compose.components.TabOption
import com.interfun.buz.core.widget_record.entity.LocalRecordUIConfig
import com.interfun.buz.core.widget_record.entity.RecordOperateStatus
import com.interfun.buz.core.widget_record.state.VoiceFilterUiState

/**
 * <AUTHOR>
 * @date 2025/6/18
 * @desc
 */
@Composable
fun VoiceFilterTabs(
    modifier: Modifier = Modifier,
    voiceFilterUiStateLambda: () -> VoiceFilterUiState,
    recordOperateStatusLambda: () -> RecordOperateStatus,
    isVoiceFilterListScrollingLambda: () -> <PERSON>olean,
    onTabChangeClick: (TabOption) -> Unit,
) {
    if (!voiceFilterUiStateLambda().isShowTabs) return

    val config = LocalRecordUIConfig.current

    // 记录待执行的tab点击操作
    var pendingTabOption by remember { mutableStateOf<TabOption?>(null) }

    val onTabClickLambda: (TabOption) -> Unit = {
        if (it.type != voiceFilterUiStateLambda().currentSelectTabType) {
            onTabChangeClick.invoke(it)
        }
    }

    // 监听滚动状态变化，当滚动结束时执行待执行的操作
    LaunchedEffect(isVoiceFilterListScrollingLambda()) {
        val isScrolling = isVoiceFilterListScrollingLambda()
        if (!isScrolling && pendingTabOption != null) {
            // 滚动结束且有待执行的操作
            val tabToExecute = pendingTabOption
            pendingTabOption = null
            tabToExecute?.let { onTabClickLambda(it) }
        }
    }

    OptionTabs(
        modifier = modifier.padding(top = config.voiceFilterTabTopMargin),
        options = voiceFilterUiStateLambda().tabOptionList,
        currentSelectTabTypeLambda = { voiceFilterUiStateLambda().currentSelectTabType },
        onSelectionChange = { tabOption ->
            if (isVoiceFilterListScrollingLambda()) {
                // 如果正在滚动，记录待执行的操作
                pendingTabOption = tabOption
            } else {
                // 如果没有滚动，直接执行
                onTabClickLambda(tabOption)
            }
        },
        isEnableLambda = {
            !recordOperateStatusLambda().isPressingOrLocking
        }
    )
}