package com.interfun.buz.core.widget_record.ui

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.BoxWithConstraintsScope
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import com.interfun.buz.base.ktx.DefaultCallback
import com.interfun.buz.compose.components.BackHandler
import com.interfun.buz.compose.ktx.rememberMutableBoolean
import com.interfun.buz.compose.utils.RecomposeCountHelper
import com.interfun.buz.core.widget_record.entity.LocalRecordUIConfig
import com.interfun.buz.core.widget_record.entity.RecordOperateStatus
import com.interfun.buz.core.widget_record.state.VoiceFilterUiState
import com.interfun.buz.core.widget_record.state.rememberVoiceFilterListState
import com.interfun.buz.core.widget_record.ui.action.RecordScreenAction
import com.interfun.buz.core.widget_record.ui.action.RecordScreenAction.*
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

/**
 * <AUTHOR>
 * @date 2024/11/13
 * @desc
 */

@Composable
fun BoxWithConstraintsScope.VoiceFilterScreen(
    voiceFilterUiStateLambda: () -> VoiceFilterUiState,
    isShowAIBgLambda: () -> Boolean,
    isInVoiceFilterModeLambda: () -> Boolean,
    isVoiceFilterPreviewingLambda: () -> Boolean,
    isInterceptGestureLambda: () -> Boolean,
    hasSwitchToVFModeFinishedLambda: () -> Boolean,
    switchToVFProgressLambda: () -> Float,
    recordOperateStatusLambda: () -> RecordOperateStatus,
    showBadgeMapLambda: () -> Map<Long, Boolean>,
    voiceFilterAction: (RecordScreenAction) -> Unit,
    onSetPressDownCallback: (DefaultCallback) -> Unit,
) {
    SideEffect {
        RecomposeCountHelper.increment("VoiceFilterScreen")
    }
    val config = LocalRecordUIConfig.current
    val scope = rememberCoroutineScope()

    // 初始化状态\配置\Callback
    val listState = rememberVoiceFilterListState(
        scope = scope,
        config = config,
        maxHeight = maxHeight,
        voiceFilterUiStateLambda = voiceFilterUiStateLambda,
        isInVoiceFilterModeLambda = isInVoiceFilterModeLambda,
        isItemTapEnableLambda = {
            isInVoiceFilterModeLambda()
                && recordOperateStatusLambda().isEnable
                && !recordOperateStatusLambda().isLocking
        },
        voiceFilterAction = voiceFilterAction,
        onSetPressDownCallback = onSetPressDownCallback
    )

    BackHandler(
        enabledLambda = {
            isInVoiceFilterModeLambda() && !recordOperateStatusLambda().isPressingOrLocking
        }
    ) {
        scope.launch {
            listState.onScrollToIndex(0)
            voiceFilterAction.invoke(OnClickQuit)
        }
    }

    // 切换到滤镜模式后，并且完成了自动滚动后，此标记为计为true
    var switchToVFAndScrollToLastItemFinished by rememberMutableBoolean()

    HandleOnSwitchToVFModeFinished(
        scope = scope,
        voiceFilterAction = voiceFilterAction,
        voiceFilterUiStateLambda = voiceFilterUiStateLambda,
        hasSwitchToVFModeFinishedLambda = hasSwitchToVFModeFinishedLambda,
        onScrollToIndex = listState.onScrollToIndex,
        totalScrollDistanceLambda = listState.totalScrollDistanceLambda,
        onFinish = { switchToVFAndScrollToLastItemFinished = it }
    )

    HandleScroll(
        listStateLambda = listState.lazyListStateLambda,
        isInVoiceFilterMode = isInVoiceFilterModeLambda,
        voiceFilterUiStateLambda = voiceFilterUiStateLambda,
        currentScrollIndexLambda = listState.currentScrollIndexLambda,
        switchToVFAndScrollToLastItemFinished = { switchToVFAndScrollToLastItemFinished }
    )

    VoiceFilterRootBox(
        recordOperateStatusLambda = recordOperateStatusLambda,
        switchToVFProgressLambda = switchToVFProgressLambda,
    ) {
        VoiceFilterConstraintLayout(
            listState = listState,
            voiceFilterUiStateLambda = voiceFilterUiStateLambda,
            recordOperateStatusLambda = recordOperateStatusLambda,
            isInterceptGestureLambda = isInterceptGestureLambda,
            isVoiceFilterPreviewingLambda = isVoiceFilterPreviewingLambda,
            isShowAIBgLambda = isShowAIBgLambda,
            switchToVFProgressLambda = switchToVFProgressLambda,
            showBadgeMapLambda = showBadgeMapLambda,
        )

        VoiceFilterTabs(
            voiceFilterUiStateLambda = voiceFilterUiStateLambda,
            recordOperateStatusLambda = recordOperateStatusLambda,
            isVoiceFilterListScrollingLambda = listState.isListScrollingLambda,
            onTabChangeClick = { voiceFilterAction.invoke(OnSelectVoiceFilterTab(it)) }
        )
    }
}

@Composable
private fun VoiceFilterRootBox(
    modifier: Modifier = Modifier,
    recordOperateStatusLambda: () -> RecordOperateStatus,
    switchToVFProgressLambda: () -> Float,
    content: @Composable BoxScope.() -> Unit
) {
    val alphaWhenNotEnable by animateFloatAsState(
        if (!recordOperateStatusLambda().isEnable) 0.4f else 1f
    )
    val alphaWhenPressingOrLocking by animateFloatAsState(
        if (recordOperateStatusLambda().isPressingOrLocking) 0f else 1f
    )
    val alphaWhenSwitching = switchToVFProgressLambda()
    Box(
        modifier = modifier.graphicsLayer {
            alpha = minOf(alphaWhenNotEnable, alphaWhenPressingOrLocking, alphaWhenSwitching)
        },
        contentAlignment = Alignment.TopCenter,
        content = content
    )
}

@Composable
private fun HandleScroll(
    listStateLambda: () -> LazyListState,
    isInVoiceFilterMode: () -> Boolean,
    voiceFilterUiStateLambda: () -> VoiceFilterUiState,
    currentScrollIndexLambda: () -> Int,
    switchToVFAndScrollToLastItemFinished: () -> Boolean,
) {
    // 正常情况下是先由录音页切换到滤镜页，会有一个动画过程，但也可能初始态就已经是滤镜页了
    // 比如首页打开滤镜页后再从聊天页中打开录音面板，就是直接到滤镜页，跳过录音页
    // 使用下面这个变量来记录区分这种情况，在直接跳转的情况下，将上次选择的滤镜直接对准
    var hasShowRecordPageBefore by rememberMutableBoolean()

    LaunchedEffect(
        key1 = isInVoiceFilterMode(),
        key2 = voiceFilterUiStateLambda()
    ) {
        if (isInVoiceFilterMode()) {
            // 在之前已经显示过录音页的情况下，代表需要等待switch的动画走完才能执行自动滚动到上次选中的位置
            // 原因是在 onSwitchFinished 时会触发带滚动动画的定位到之前选中的滤镜的逻辑，和这里冲突
            val ableToAutoScroll = if (hasShowRecordPageBefore) {
                switchToVFAndScrollToLastItemFinished()
            } else {
                true
            }
            if (ableToAutoScroll) {
                // 确保当选中滤镜变更后，列表对应位置发生变化（主要是为了在不同页面间同步列表位置)
                val list = voiceFilterUiStateLambda().voiceFilterList
                val filter = voiceFilterUiStateLambda().currentSelectVoiceFilter
                val index = list.indexOf(filter).coerceAtLeast(0)
                if (index != currentScrollIndexLambda()) {
                    listStateLambda().scrollToItem(index)
                }
            }
        } else {
            hasShowRecordPageBefore = true
            if (currentScrollIndexLambda() != 0) {
                listStateLambda().scrollToItem(0)
            }
        }
    }
}

@Composable
private fun HandleOnSwitchToVFModeFinished(
    scope: CoroutineScope,
    voiceFilterAction: (RecordScreenAction) -> Unit,
    voiceFilterUiStateLambda: () -> VoiceFilterUiState,
    hasSwitchToVFModeFinishedLambda: () -> Boolean,
    onScrollToIndex: suspend (Int) -> Unit,
    totalScrollDistanceLambda: () -> Float,
    onFinish: (Boolean) -> Unit,
) {
    LaunchedEffect(hasSwitchToVFModeFinishedLambda()) {
        if (hasSwitchToVFModeFinishedLambda()) {
            scope.launch {
                // 当切换到滤镜模式的动画完成后，恢复位置到之前选中的滤镜

                val voiceFilter = voiceFilterUiStateLambda().currentSelectVoiceFilter
                if (voiceFilter != null) {
                    val index = voiceFilterUiStateLambda().voiceFilterList.indexOf(voiceFilter)
                    if (index > 0) {
                        onScrollToIndex(index)
                    }
                } else {
                    // 恢复列表位置，否则在销毁重建的情况下，viewModel已选中滤镜已经空了，但列表还对齐着之前选中的
                    if (totalScrollDistanceLambda() > 0f) {
                        onScrollToIndex(0)
                    }
                }
                onFinish.invoke(true)
            }
            voiceFilterAction.invoke(OnSelectVoiceExposure)
        } else {
            onFinish.invoke(false)
        }
    }
}