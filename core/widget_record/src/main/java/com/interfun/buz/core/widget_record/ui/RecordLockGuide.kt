package com.interfun.buz.core.widget_record.ui

import androidx.compose.animation.*
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Popup
import androidx.constraintlayout.compose.ConstraintLayoutScope
import com.interfun.buz.base.widget.area.IArea
import com.interfun.buz.compose.components.IconFontText
import com.interfun.buz.compose.ktx.*
import com.interfun.buz.compose.utils.RecomposeCountHelper
import com.interfun.buz.core.widget_record.R
import com.interfun.buz.core.widget_record.entity.LocalRecordUIConfig
import com.interfun.buz.core.widget_record.state.VoiceFilterUiData
import kotlinx.coroutines.delay

/**
 * <AUTHOR>
 * @date 2025/6/6
 * @desc
 */
@Composable
internal fun ConstraintLayoutScope.LockGuide(
    modifier: Modifier = Modifier,
    isVisibleLambda: () -> Boolean,
    isInLockAreaLambda: () -> Boolean,
    getLockAreaLambda: () -> IArea,
    lockGuideProgressLambda: () -> Float,
    isInVoiceFilterModeLambda: () -> Boolean,
    currentVoiceFilterLambda: () -> VoiceFilterUiData?,
) {
    SideEffect {
        RecomposeCountHelper.increment("LockGuide")
    }
    var lockBtnSize by rememberMutableFloat()
    var centerYDistance by rememberMutableFloat()
    lockBtnSize = getLockAreaLambda().height
    val density = LocalDensity.current
    val config = LocalRecordUIConfig.current
    val finalTransitionY = remember {
        derivedStateOf {
            -(centerYDistance * lockGuideProgressLambda()).toInt()
        }
    }
    var isPopupVisible by rememberMutableBoolean()
    LaunchedEffect(isVisibleLambda()) {
        if (!isVisibleLambda()) {
            delay(200)
            isPopupVisible = false
        } else {
            isPopupVisible = true
        }
    }

    BoxWithConstraints(modifier = modifier) {
        LaunchedEffect(lockBtnSize) {
            if (lockBtnSize > 0) {
                centerYDistance = <EMAIL>(density) - lockBtnSize
            }
        }
        if (config.showLockInPopup) {
            if (!isPopupVisible) return@BoxWithConstraints
            Popup {
                Box(
                    modifier = Modifier
                        .width(maxWidth)
                        .height(maxHeight)
                ) {
                    AnimatedVisibility(
                        visible = isVisibleLambda(),
                        enter = fadeIn(),
                        exit = fadeOut(),
                        modifier = Modifier
                            .align(Alignment.BottomCenter)
                            .offset {
                                IntOffset(x = 0, y = finalTransitionY.value)
                            },
                    ) {
                        LockGuideContent(
                            lockBtnSizeLambda = { lockBtnSize },
                            isInLockAreaLambda = isInLockAreaLambda,
                            isInVoiceFilterModeLambda = isInVoiceFilterModeLambda,
                            currentVoiceFilterLambda = currentVoiceFilterLambda
                        )
                    }
                }
            }
        } else {
            AnimatedVisibility(
                visible = isVisibleLambda(),
                enter = fadeIn(),
                exit = fadeOut(),
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .offset {
                        IntOffset(x = 0, y = finalTransitionY.value)
                    }
            ) {
                LockGuideContent(
                    lockBtnSizeLambda = { lockBtnSize },
                    isInLockAreaLambda = isInLockAreaLambda,
                    isInVoiceFilterModeLambda = isInVoiceFilterModeLambda,
                    currentVoiceFilterLambda = currentVoiceFilterLambda
                )
            }
        }
    }
}

@Composable
private fun LockGuideContent(
    modifier: Modifier = Modifier,
    lockBtnSizeLambda: () -> Float,
    isInLockAreaLambda: () -> Boolean,
    isInVoiceFilterModeLambda: () -> Boolean,
    currentVoiceFilterLambda: () -> VoiceFilterUiData?,
) {
    SideEffect {
        RecomposeCountHelper.increment("LockGuideContent")
    }
    val showLockBtn = isInLockAreaLambda()
    val config = LocalRecordUIConfig.current
    val userVoiceFilterColor = isInVoiceFilterModeLambda()
        && currentVoiceFilterLambda() != null
        && (currentVoiceFilterLambda()?.filterId
            ?: VoiceFilterUiData.NO_FILTER_ID) != VoiceFilterUiData.NO_FILTER_ID
    val bgColor by animateColorAsState(
        targetValue = if (showLockBtn) {
            if (userVoiceFilterColor) {
                currentVoiceFilterLambda()?.backgroundColor
                    ?: R.color.color_background_highlight_1_default.asColor()
            } else {
                R.color.color_background_highlight_1_default.asColor()
            }
        } else {
            config.actionBtnBgColorRes.asColor()
        }
    )
    val fraction by animateFloatAsState(targetValue = if (showLockBtn) 1f else 0f)
    val lockBtnSize = lockBtnSizeLambda().pxToDp()
    val animatedWidth = calculateDp(28.dp, lockBtnSize, fraction)
    val animatedHeight = calculateDp(56.dp, lockBtnSize, fraction)
    val animatedCornerRadius = calculateDp(14.dp, lockBtnSize / 2f, fraction)
    val iconMarginTop = calculateDp(10.dp, (lockBtnSize - 24.dp) / 2f, fraction)

    Column(
        modifier = modifier
            .size(animatedWidth, animatedHeight)
            .clip(RoundedCornerShape(animatedCornerRadius))
            .background(bgColor),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        VerticalSpace(iconMarginTop)
        AnimatedContent(
            modifier = Modifier.size(24.dp),
            targetState = showLockBtn,
            transitionSpec = { ContentTransform(fadeIn(), fadeOut()) }
        ) {
            if (it) {
                IconFontText(
                    iconRes = R.string.ic_lock_solid,
                    iconColor = if (userVoiceFilterColor) {
                        currentVoiceFilterLambda()?.microphoneColor
                            ?: R.color.color_text_black_primary.asColor()
                    } else {
                        R.color.color_text_black_primary.asColor()
                    }
                )
            } else {
                IconFontText(
                    iconRes = R.string.ic_un_lock,
                    iconColor = config.actionBtnIconColorRes.asColor()
                )
            }
        }

        AnimatedVisibility(
            modifier = Modifier.padding(top = 2.dp),
            visible = !showLockBtn,
            enter = fadeIn(),
            exit = fadeOut()
        ) {
            IconFontText(
                iconRes = R.string.ic_towards_up,
                iconSize = 16.dp,
                iconColor = config.actionBtnIconColorRes.asColor()
            )
        }
    }
}