package com.interfun.buz.core.widget_record.ui

import androidx.compose.foundation.Canvas
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.unit.dp
import com.interfun.buz.common.constants.CommonConstant.MAX_RECORD_DURATION
import com.interfun.buz.compose.ktx.asColor
import com.interfun.buz.compose.utils.RecomposeCountHelper
import com.interfun.buz.core.widget_record.R
import com.interfun.buz.core.widget_record.state.VoiceFilterUiData

/**
 * <AUTHOR>
 * @date 2025/6/12
 * @desc
 */
@Composable
internal fun RecordButtonLockingProgress(
    modifier: Modifier = Modifier,
    isLockingLambda: () -> <PERSON><PERSON><PERSON>,
    lastStartRecordTimeLambda: () -> Long,
    currentVoiceFilterLambda: () -> VoiceFilterUiData?,
) {
    SideEffect {
        RecomposeCountHelper.increment("RecordButtonLockingProgress")
    }
    val isLocking = isLockingLambda()
    if (!isLocking) return
    val getCurrentAngle = remember {
        {
            val currentDuration = (System.currentTimeMillis() - lastStartRecordTimeLambda())
                .coerceIn(0L, MAX_RECORD_DURATION * 1000L)
            currentDuration.toFloat() / (MAX_RECORD_DURATION * 1000L) * 360f
        }
    }
    val arcColor = currentVoiceFilterLambda()?.backgroundColor
        ?: R.color.color_background_highlight_1_default.asColor()
    var animatedAngle by remember { mutableFloatStateOf(getCurrentAngle()) }

    LaunchedEffect(Unit) {
        while (animatedAngle < 360f) {
            // 逐帧绘制
            withFrameMillis {
                animatedAngle = getCurrentAngle()
            }
        }
    }

    Canvas(modifier = modifier) {
        drawArc(
            color = arcColor,
            startAngle = -90f,
            sweepAngle = animatedAngle,
            useCenter = false,
            style = Stroke(
                width = 3.dp.toPx(),
                cap = StrokeCap.Round
            ),
            size = Size(size.width, size.height)
        )
    }
}