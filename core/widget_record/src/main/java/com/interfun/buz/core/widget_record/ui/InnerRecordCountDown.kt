package com.interfun.buz.core.widget_record.ui

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.interfun.buz.compose.ktx.asString
import com.interfun.buz.compose.styles.TextStyles
import com.interfun.buz.core.widget_record.R
import com.interfun.buz.core.widget_record.entity.LocalRecordUIConfig
import com.interfun.buz.core.widget_record.entity.RecordAreaType
import com.interfun.buz.core.widget_record.state.VoiceFilterUiData

/**
 * <AUTHOR>
 * @date 2025/5/16
 * @desc
 */
@Composable
fun InnerRecordCountDown(
    modifier: Modifier = Modifier,
    isRecordingLambda: () -> Boolean,
    isLockingLambda: () -> Boolean,
    currentPressAreaLambda: () -> RecordAreaType,
    voiceFilterLambda: () -> VoiceFilterUiData?,
) {
    if (!LocalRecordUIConfig.current.showRecordCountDown) return
    Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.BottomCenter
    ) {
        AnimatedVisibility(
            visible = isRecordingLambda(),
            enter = fadeIn(),
            exit = fadeOut()
        ) {
            RecordCountDownScreen(
                modifier = Modifier
                    .fillMaxWidth()
                    .wrapContentHeight(),
                centerSpaceHeight = 2.dp,
                showReleaseToSendHint = false,
                cancelHint = R.string.chat_history_release_to_cancel.asString(),
                previewHint = R.string.chat_history_release_to_preview.asString(),
                lockHint = R.string.release_to_record_hands_free.asString(),
                hintTextStyle = TextStyles.bodyMedium(),
                isRecordingLambda = isRecordingLambda,
                isLockingLambda = isLockingLambda,
                currentPressAreaLambda = currentPressAreaLambda,
                voiceFilterLambda = voiceFilterLambda
            )
        }
    }
}