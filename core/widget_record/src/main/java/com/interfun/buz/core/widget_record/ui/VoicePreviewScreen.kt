package com.interfun.buz.core.widget_record.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.airbnb.lottie.compose.*
import com.interfun.buz.common.utils.TimeUtils
import com.interfun.buz.compose.components.IconFontText
import com.interfun.buz.compose.ktx.HorizontalSpace
import com.interfun.buz.compose.ktx.VerticalSpace
import com.interfun.buz.compose.ktx.asColor
import com.interfun.buz.compose.ktx.asString
import com.interfun.buz.compose.modifier.debouncedClickable
import com.interfun.buz.compose.styles.TextStyles
import com.interfun.buz.core.widget_record.R
import com.interfun.buz.core.widget_record.entity.FilterState.FilterFail
import com.interfun.buz.core.widget_record.entity.FilterState.FilterIng
import com.interfun.buz.core.widget_record.entity.FilterState.FilterSuccess
import com.interfun.buz.core.widget_record.state.VoiceFilterUiData
import com.interfun.buz.core.widget_record.ui.VoicePreviewSource.HOME

enum class VoicePreviewSource {
    HOME, CHAT
}

sealed class VoicePreviewAction {
    data object CancelAction : VoicePreviewAction()
    data object PlayOrFilterAction : VoicePreviewAction()
    data object SendAction : VoicePreviewAction()
    data object SendActionAnimFinish : VoicePreviewAction()
}

@Composable
fun VoicePreviewContent(
    modifier: Modifier = Modifier,
    previewSource: VoicePreviewSource,
    filterState: Int,
    isPlaying: Boolean?,
    voiceDuration: Int,
    progress: Float,
    voiceFilter: VoiceFilterUiData? = null,
    action: (VoicePreviewAction) -> Unit,
) {
    Column(modifier = modifier, horizontalAlignment = Alignment.CenterHorizontally) {
        VoiceProgressBar(previewSource = previewSource, filterState = filterState, isPlaying = isPlaying, progress = progress)
        VerticalSpace(dp = 20.dp)
        VoiceMessage(duration = voiceDuration, voiceFilter = voiceFilter)
        VerticalSpace(dp = 26.dp)
        OperateButton(filterState = filterState, isPlaying = isPlaying, action = action)
    }
}

@Preview
@Composable
private fun PreviewVoicePreviewContent(modifier: Modifier = Modifier) {
    VoicePreviewContent(
        modifier = modifier,
        previewSource = HOME,
        filterState = 2,
        isPlaying = true,
        voiceDuration = 30,
        progress = 0.5f,
        voiceFilter = null,
        action = { }
    )
}

@Composable
private fun VoiceProgressBar(
    previewSource: VoicePreviewSource,
    filterState: Int,
    isPlaying: Boolean?,
    progress: Float,
) {
    val boxModifier = remember(filterState) {
        if (filterState == FilterFail) {
            Modifier
                .padding(horizontal = 7.dp)
                .wrapContentHeight()
                .fillMaxWidth()
        } else {
            Modifier
                .padding(horizontal = 7.dp)
                .height(IntrinsicSize.Min)
                .fillMaxWidth()
        }
    }

    Box(modifier = boxModifier) {
        if (filterState == FilterFail) {
            Row(
                Modifier
                    .fillMaxWidth()
                    .height(40.dp)
                    .align(Alignment.Center),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center
            ) {
                IconFontText(
                    iconText = R.string.ic_warning_solid.asString(),
                    iconSize = 16.dp,
                    iconColor = colorResource(R.color.text_white_secondary)
                )
                HorizontalSpace(4.dp)
                Text(
                    text = stringResource(R.string.unable_to_load_filter),
                    style = TextStyles.bodyMedium(),
                    color = colorResource(R.color.color_text_white_secondary),
                    textAlign = TextAlign.Center
                )
            }
            return
        }


        if (isPlaying != true) {
            Spacer(
                modifier = Modifier
                    .matchParentSize()
                    .background(colorResource(id = R.color.text_white_secondary))
            )
        } else {
            Spacer(
                Modifier
                    .matchParentSize()
                    .background(colorResource(id = R.color.text_white_disable))
            )
        }

        Spacer(
            Modifier
                .fillMaxHeight()
                .fillMaxWidth(progress)
                .background(colorResource(id = R.color.text_white_main))
        )

        val waveDrawable =  R.drawable.voice_preview_wave_chat
        val filteringWaveDrawable =  R.drawable.voice_preview_filtering_chat
        val imageRes = remember(filterState, isPlaying) {
            if (filterState == FilterSuccess && isPlaying != null) {
                waveDrawable
            } else {
                filteringWaveDrawable
            }
        }
        Image(
            painterResource(id = imageRes),
            contentDescription = "",
            modifier = Modifier.fillMaxWidth(),
            contentScale = ContentScale.FillWidth
        )
    }
}

@Composable
private fun VoiceMessage(
    duration: Int,
    voiceFilter: VoiceFilterUiData? = null,
) {
    val textColor = voiceFilter?.backgroundColor ?: R.color.color_text_white_secondary.asColor()
    Row(
        Modifier
            .clip(RoundedCornerShape(100.dp))
            .background(colorResource(id = R.color.color_background_5_default))
            .padding(horizontal = 10.dp, vertical = 4.dp)

    ) {
        voiceFilter?.let {
            RecordVoiceFilterInfo(textColor, it)
        }
        Text(
            text = TimeUtils.formatVoiceMsgMillisToTimeString(duration),
            color = textColor,
            fontSize = 12f.sp,
            style = TextStyles.bodyMedium(),
            modifier = Modifier.align(Alignment.CenterVertically)
        )
    }
}

@Composable
private fun OperateButton(
    filterState: Int,
    isPlaying: Boolean?,
    action: (VoicePreviewAction) -> Unit,
) {
    Row {
        IconText(
            modifier = Modifier
                .debouncedClickable {
                    action.invoke(VoicePreviewAction.CancelAction)
                },
            icon = stringResource(id = R.string.ic_delete_solid),
            lottie = null,
            iconColor = colorResource(
                id = R.color.color_text_white_important
            ),
            text = stringResource(id = R.string.cancel),
            textColor = colorResource(id = R.color.text_white_secondary)
        )

        val icon = when (filterState) {
            FilterIng -> null
            FilterFail -> stringResource(R.string.ic_refresh)
            FilterSuccess -> {
                when (isPlaying) {
                    true -> stringResource(R.string.ic_square_solid)
                    false -> stringResource(R.string.ic_play_solid)
                    else -> null
                }
            }

            else -> null
        }


        IconText(
            modifier = Modifier.debouncedClickable {
                action.invoke(VoicePreviewAction.PlayOrFilterAction)
            },
            icon = icon,
            lottie = R.raw.chat_lottie_msg_sending_loading,
            iconColor = colorResource(
                id = if (filterState != FilterIng) {
                    R.color.color_text_white_important
                } else {
                    R.color.color_foreground_neutral_important_disable
                }
            ),
            text = stringResource(
                id = when {
                    filterState != FilterSuccess -> R.string.retry
                    isPlaying == true -> R.string.vf_stop
                    isPlaying == false -> R.string.play
                    else -> R.string.retry
                }
            ),
            textColor = colorResource(id = R.color.text_white_secondary)
        )

        IconText(
            modifier = Modifier
                .debouncedClickable {
                    action.invoke(VoicePreviewAction.SendAction)
                },
            icon = stringResource(id = R.string.ic_message),
            lottie = null,
            iconColor = colorResource(
                id = R.color.color_text_highlight_default
            ),
            text = stringResource(id = R.string.ve_start_send),
            textColor = colorResource(id = R.color.color_text_highlight_default)
        )
    }
}

@Composable
private fun RowScope.IconText(
    modifier: Modifier = Modifier,
    iconModifier: Modifier = Modifier,
    icon: String?,
    lottie: Int?,
    iconColor: Color,
    text: String,
    textColor: Color,
) {
    Column(modifier.weight(1f)) {
        if (icon != null) {
            IconFontText(
                iconModifier.align(Alignment.CenterHorizontally),
                iconText = icon,
                iconSize = 30.dp,
                iconColor = iconColor
            )
        } else if (lottie != null) {
            val composition by rememberLottieComposition(LottieCompositionSpec.RawRes(lottie))
            val progress by animateLottieCompositionAsState(
                composition,
                iterations = LottieConstants.IterateForever
            )
            LottieAnimation(
                composition = composition,
                progress = { progress },
                modifier = iconModifier
                    .size(30.dp)
                    .align(Alignment.CenterHorizontally)
            )
        }

        VerticalSpace(dp = 6.dp)
        Text(
            text = text,
            Modifier.align(Alignment.CenterHorizontally),
            color = textColor,
            style = TextStyles.bodyMedium(),
            fontSize = 12f.sp,
            textAlign = TextAlign.Center
        )
    }
}

@Composable
fun RecordVoiceFilterInfo(
    textColor: Color,
    voiceFilter: VoiceFilterUiData,
) {
    VoiceFilterIcon(
        iconOrUrl = voiceFilter.filterIconUrl,
        iconSize = 16.dp,
    )
    HorizontalSpace(3.dp)
    Text(
        text = voiceFilter.filterName,
        color = textColor,
        style = TextStyles.bodyMedium()
    )
    Box(
        modifier = Modifier
            .size(16.dp)
            .padding(all = 6.dp)
            .background(
                color = textColor,
                shape = CircleShape
            )
    )
    HorizontalSpace(3.dp)
}