package com.interfun.buz.core.widget_record.ui

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.unit.dp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import com.interfun.buz.base.ktx.calculateValue
import com.interfun.buz.compose.ktx.*
import com.interfun.buz.compose.modifier.debouncedClickable
import com.interfun.buz.compose.utils.RecomposeCountHelper
import com.interfun.buz.core.widget_record.entity.LocalRecordUIConfig
import com.interfun.buz.core.widget_record.entity.RecordOperateStatus
import com.interfun.buz.core.widget_record.state.VoiceFilterListState
import com.interfun.buz.core.widget_record.state.VoiceFilterUiState
import com.interfun.buz.core.widget_record.state.isValidVoiceFilter
import kotlin.math.abs

/**
 * <AUTHOR>
 * @date 2025/6/18
 * @desc
 */
@Composable
internal fun VoiceFilterConstraintLayout(
    listState: VoiceFilterListState,
    voiceFilterUiStateLambda: () -> VoiceFilterUiState,
    recordOperateStatusLambda: () -> RecordOperateStatus,
    isInterceptGestureLambda: () -> Boolean,
    isVoiceFilterPreviewingLambda: () -> Boolean,
    isShowAIBgLambda: () -> Boolean,
    switchToVFProgressLambda: () -> Float,
    showBadgeMapLambda: () -> Map<Long, Boolean>,
    modifier: Modifier = Modifier,
) {
    SideEffect {
        RecomposeCountHelper.increment("VoiceFilterConstraintLayout")
    }
    val switchProgress = switchToVFProgressLambda()
    val isVoiceFilterHide by remember { derivedStateOf { switchToVFProgressLambda() == 0f } }
    val isSwitchFinished by remember { derivedStateOf { switchToVFProgressLambda() == 1f } }

    ConstraintLayout(modifier = modifier.fillMaxSize()) {
        val (refList, refGestureInterceptor, refControlBar) = createRefs()
        val config = LocalRecordUIConfig.current
        val recordPaddingTop = createGuidelineFromTop(fraction = config.recordTopPaddingPercent)
        val recordPaddingBottom =
            createGuidelineFromBottom(fraction = config.recordBottomPaddingPercent)
        val topMargin = if (voiceFilterUiStateLambda().isShowTabs) {
            config.recordTransitionYWhenShowTabs.pxToDp() * 2
        } else {
            0.dp
        }
        VoiceFilterList(
            listState = listState,
            voiceFilterUiStateLambda = voiceFilterUiStateLambda,
            recordOperateStatusLambda = recordOperateStatusLambda,
            isVoiceFilterHideLambda = { isVoiceFilterHide },
            isShowAIBgLambda = isShowAIBgLambda,
            showBadgeMapLambda = showBadgeMapLambda,
            isVoiceFilterPreviewingLambda = isVoiceFilterPreviewingLambda,
            modifier = Modifier
                .fillMaxWidth()
                .nestedScroll(listState.nestedScrollConnection)
                .constrainAs(refList) {
                    top.linkTo(recordPaddingTop, margin = topMargin)
                    bottom.linkTo(recordPaddingBottom)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    width = Dimension.fillToConstraints
                    height = Dimension.fillToConstraints
                }
        )
        // 当首页列表滑动中的时候，添加一个空click实现，拦截列表滑动
        // LazyRow 的 userScrollEnabled 也能实现这个效果，但两者分开监听不同的状态
        // 没有写在 LazyRow 的 userScrollEnabled 中的原因是：
        // 1. 避免列表滑动时List重组（虽然影响不大，item不会重组）
        // 2. 在VF列表滑动时，同时去滑动首页列表，如果此时将userScrollEnabled直接变为false
        //    那么VF列表滑动会被打断，并且会停止在当前最靠近的item，然后触发选中+预览逻辑，这会比较奇怪
        if (!isVoiceFilterHide) {
            VoiceFilterGestureInterceptor(
                isIntercept = {
                    isInterceptGestureLambda() || !isSwitchFinished
                },
                modifier = Modifier.constrainAs(refGestureInterceptor) {
                    fillToRef(refList)
                }
            )
        }

        val transitionYToBottom = config.voiceFilterControlBarTransitionY.dpToPx()
        VoiceFilterControlBar(
            currentScrollPosLambda = listState.currentScrollPosLambda,
            isClickEnable = { isSwitchFinished && !recordOperateStatusLambda().isPressingOrLocking },
            onClickClear = listState.onClickClear,
            onClickQuit = listState.onClickQuit,
            onClickCampaign = listState.onCampaignEntryClick,
            campaignConfig = { voiceFilterUiStateLambda().currentSelectVoiceFilter?.campaignConfig },
            modifier = Modifier
                .constrainAs(refControlBar) {
                    bottom.linkTo(parent.bottom)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                }
                .graphicsLayer {
                    translationY = calculateValue(transitionYToBottom, 0f, switchProgress)
                }
        )
    }
}

@Composable
private fun VoiceFilterList(
    modifier: Modifier = Modifier,
    listState: VoiceFilterListState,
    voiceFilterUiStateLambda: () -> VoiceFilterUiState,
    recordOperateStatusLambda: () -> RecordOperateStatus,
    isVoiceFilterHideLambda: () -> Boolean,
    isShowAIBgLambda: () -> Boolean,
    showBadgeMapLambda: () -> Map<Long, Boolean>,
    isVoiceFilterPreviewingLambda: () -> Boolean,
) {
    SideEffect {
        RecomposeCountHelper.increment("VoiceFilterList")
    }
    val recordStatus = recordOperateStatusLambda()
    val isRecordEnable = recordStatus.isEnable && !isVoiceFilterHideLambda()

    val list = voiceFilterUiStateLambda().voiceFilterList
    LazyRow(
        modifier = modifier,
        state = listState.lazyListStateLambda(),
        contentPadding = PaddingValues(horizontal = (getScreenWidth() - listState.itemWidthMax) / 2f),
        userScrollEnabled = isRecordEnable && !recordStatus.isPressingOrLocking,
        flingBehavior = rememberSpeedFlingBehavior(),
        overscrollEffect = null
    ) {
        items(
            key = { filter ->
                "${filter.filterId}-${filter.filterName}"
            },
            items = list
        ) { info ->
            val index = list.indexOf(info)
            val fraction = 1f - abs(listState.currentScrollPosLambda() - index).coerceAtMost(1f)
            val showBadge = showBadgeMapLambda()[info.filterId] == true
            VoiceFilterItem(
                isValidFilter = info.isValidVoiceFilter(),
                isShowAIBg = isShowAIBgLambda(),
                filterName = info.filterName,
                filterIcon = info.filterIconUrl,
                mainColor = info.backgroundColor,
                micColor = info.microphoneColor,
                showBadge = showBadge,
                fraction = fraction,
                itemWidthMax = listState.itemWidthMax,
                itemWidthMin = listState.itemWidthMin,
                isListScrollingLambda = listState.isListScrollingLambda,
                isVoiceFilterPreviewing = isVoiceFilterPreviewingLambda,
                onClickItem = { listState.onClickItem.invoke(index) },
                onClickName = { listState.onClickName.invoke(index) }
            )
        }
    }
}

@Composable
private fun VoiceFilterGestureInterceptor(
    isIntercept: () -> Boolean,
    modifier: Modifier = Modifier
) {
    if (isIntercept()) {
        Box(modifier = modifier.debouncedClickable(withIndication = false, onClick = {}))
    }
}