package com.interfun.buz.core.widget_record.ui

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import coil.compose.AsyncImage
import com.buz.idl.common.bean.ActionInfo
import com.buz.idl.common.bean.CampaignConfig
import com.interfun.buz.compose.modifier.debouncedClickable

@Composable
fun VoiceFilterCampaignEntry(
    modifier: Modifier = Modifier,
    config: () -> CampaignConfig?,
    isClickEnable: () -> <PERSON><PERSON><PERSON>,
    onClick: (ActionInfo?) -> Unit
) {
    val imageUrl = config()?.image
    val actionInfo = config()?.actionInfo
    AnimatedVisibility(
        modifier = modifier.fillMaxHeight(),
        visible = !imageUrl.isNullOrEmpty(),
        enter = fadeIn(),
        exit = fadeOut()
    ) {
        AsyncImage(
            modifier = Modifier
                .debouncedClickable(
                    enabled = isClickEnable(),
                    onClick = { onClick.invoke(actionInfo) }
                ),
            model = imageUrl,
            contentDescription = ""
        )
    }
}