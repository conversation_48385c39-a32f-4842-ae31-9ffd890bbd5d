package com.interfun.buz.core.widget_record.view

import android.content.Context
import android.util.AttributeSet
import com.airbnb.lottie.LottieDrawable
import com.interfun.buz.base.ktx.dp
import com.interfun.buz.base.ktx.gone
import com.interfun.buz.base.ktx.isUrl
import com.interfun.buz.base.ktx.visible
import com.interfun.buz.common.ktx.hideLayer
import com.interfun.buz.common.ktx.loadImageUrl
import com.interfun.buz.common.ktx.setDefaultFontAssetDelegate
import com.interfun.buz.common.ktx.setEmoji
import com.interfun.buz.common.utils.DeviceUtils
import com.interfun.buz.common.widget.view.BuzLottie

class VoiceFilterLottie @JvmOverloads constructor(
    context: Context,
    attributeSet: AttributeSet? = null,
) : BuzLottie(context, attributeSet) {

    var currentSerMsgId : String? = null

    companion object {
        const val TAG = "VoiceFilterAnimView"
        private const val PLAYING_1 = "voice_filter_playing_1.json"
        private const val RECORDING_1 = "voice_filter_recording_1.json"
        private const val RECORDING_2 = "voice_filter_recording_2.json"
    }

    init {
        setDefaultFontAssetDelegate()
        setRepeatCount(LottieDrawable.INFINITE)
    }

    suspend fun play(isRecordType: Boolean, animContent: String?, animType: Int = 1) {
        if (isAnimating) {
            return
        }
        if (animContent.isNullOrEmpty()) {
            cancelAnimation()
            gone()
            return
        }
        val animFile = if (isRecordType) {
            when (animType) {
                1 -> RECORDING_1
                2 -> RECORDING_2
                else -> null
            }
        } else {
            when (animType) {
                1 -> PLAYING_1
                else -> null
            }
        }

        if (animFile == null) return
        var newAnimContent = animContent
        if (DeviceUtils.isSamsung()) {
            if (animContent == "🧔‍♂️") {
                newAnimContent = animContent.replace("♂", "")
            }
        }

        setAnimation(animFile)
        if (newAnimContent.isUrl()) {
            hideLayer("Emoji text layer")
            loadImageUrl("Emoji image layer", newAnimContent, 200)
        } else {
            hideLayer("Emoji image layer")
            setEmoji("Emoji text layer", newAnimContent)
        }

        playAnimation()
        visible()
    }

    fun stop() {
        cancelAnimation()
        gone()
    }

}