package com.interfun.buz.core.widget_record.entity

import androidx.compose.runtime.staticCompositionLocalOf
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.interfun.buz.base.ktx.dpFloat
import com.interfun.buz.common.manager.ABTestManager
import com.interfun.buz.common.manager.AppConfigRequestManager
import com.interfun.buz.core.widget_record.R

/**
 * <AUTHOR>
 * @date 2025/5/12
 * @desc 录音组件配置类，用于在不同页面复用时配置不同的尺寸
 */
data class RecordUIConfig(
    // 是否展示滤镜入口
    val showInnerVoiceFilterEntry: Boolean = false,
    val showRecordCountDown: Boolean = false,
    val showLockInPopup: Boolean = false,

    // 通用配置
    // 录音按钮的高度百分比，相对于面板高度
    val bgColor: Int = R.color.color_background_2_default,
    val recordBtnHeightPercent: Float = 0.53f,
    // 录音按钮的上边距
    // 和下面的下边距一起搭配，最终整个面板高度减去这两个边距，居中的位置即为录音按钮的中心
    // 注意按钮的高度!=面板高度-上下边距，这两个间距只用来定位按钮中心位置，可以理解为bias的效果
    // 按钮的高度由recordBtnHeightPercent决定
    val recordTopPaddingPercent: Float = 0f,
    // 录音按钮的下边距（默认为录音按钮上边距比下边距为8/9），所以下边距多出的部分为除按钮外的高度的十七分之一
    val recordBottomPaddingPercent: Float = (1f - recordBtnHeightPercent) / (8f + 9f) * (9f - 8f),
    // 录音按钮按下时的缩放比例
    val recordBtnScaleWhenPress: Float = 160f / 180f,
    // 录音按钮的垂直偏移量（默认上边距比下边距为8比9）
    val recordBtnVerticalBias: Float = 8f / (8f + 9f),
    // 麦克风图标的大小
    val recordBtnMicIconSize: Dp = 40.dp,
    // 默认的录音按钮背景色(当在滤镜模式下划走时)
    val recordDefaultBgColorRes: Int = R.color.color_background_4_default,
    // 录音动画比例
    // 原先所有的录音中动画的尺寸都是根据录音后缩放系数为0.9来设计的，改为其他值后，需要再缩放一下
    val recordLottieScale: Float = 300f / 220f * (recordBtnScaleWhenPress / 0.9f),
    val recordLockingScale: Float = 104f / 160f,
    val recordTransitionYWhenShowTabs: Float = 15.dpFloat,

    // Cancel或Preview 按钮区域
    val btnCancelOrPreviewHorizontalMargin: Dp = 30.dp,
    val actionBtnBgColorRes: Int = R.color.color_background_4_default,
    val actionBtnIconColorRes: Int = R.color.color_text_white_secondary,
    val actionBtnHeightPercent: Float = 0.183f,
    val actionBtnScalePercent: Float = 1.14f,

    val enableVoiceFilterMode: () -> Boolean = {
        ABTestManager.showVoiceFilter && AppConfigRequestManager.openVoiceFilterFunction
    },

    // VoiceFilterScreen 模式下或切换过程中的相关缩放比例

    // 语音滤镜模式下非录音状态时按钮的缩放比例
    val voiceFilterUnRecordBtnSizeScale: Float = 124f / 220f,
    // 麦克风图标在语音滤镜模式下的缩放比例
    val voiceFilterMicIconScale: Float = 32f / 40f,
    // 语音滤镜录音按钮的大小比例，相对于普通录音按钮
    val voiceFilterItemSizeScaleMax: Float = 140f / 220f,
    // 非中心语音滤镜按钮的大小比例，相对于中心语音滤镜按钮
    val voiceFilterItemSizeScaleMin: Float = 60f / 140f,
    // 语音滤镜录音动画类型 1 的缩放比例
    val voiceFilterRecordLottie1Scale: Float = 500f / 180f,
    // 语音滤镜录音动画类型 2 的缩放比例
    val voiceFilterRecordLottie2Scale: Float = 200f / 180f,
    // 语音滤镜名称背景色
    val voiceFilterNameBackgroundColor: Int = R.color.color_background_4_default,

    val voiceFilterTabTopMargin: Dp = 16.dp,

    // 语音滤镜底部控制栏
    val voiceFilterControlBarHeightMin: Dp = 68.dp,
    val voiceFilterControlBarCenterGap: Dp = 8.dp,
    val voiceFilterControlBarTransitionY: Dp = 30.dp
)

val LocalRecordUIConfig = staticCompositionLocalOf<RecordUIConfig> { error("no provided") }