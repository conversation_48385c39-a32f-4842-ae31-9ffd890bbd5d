package com.interfun.buz.core.widget_record.state

import androidx.compose.ui.graphics.Color
import com.buz.idl.bot.bean.VoiceFilterInfo
import com.buz.idl.common.bean.CampaignConfig
import com.buz.idl.common.bean.VideoTemplate
import com.buz.idl.common.bean.VoiceFilterItemConfig
import com.interfun.buz.base.ktx.asColor
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.common.ktx.getIntDefault
import com.interfun.buz.compose.R
import com.interfun.buz.compose.ktx.parseColor
/**
 *@param type -1代表all
 */

data class VoiceFilterUiData(
    val filterId: Long,
    val filterName: String,
    val filterIconUrl: String,
    val backgroundColor: Color,
    val microphoneColor: Color,
    val recordingAnimationType: Int?,
    val recordingAnimationContent: String?,
    val playingAnimationType: Int?,
    val playingAnimationContent: String?,
    val filterSoundUrl: String?,
    val itemConfig: VoiceFilterItemConfig,
    val campaignConfig: CampaignConfig?,
    val videoTemplate: VideoTemplate?,
    val latestTimestamp: Int,
) {
    enum class PreviewType(val originDef: Int) {
        NORMAL(0),
        PREVIEW(1)
    }

    companion object {

        const val NO_FILTER_ID = -1L

        val NoFilter = VoiceFilterUiData(
            filterId = NO_FILTER_ID,
            filterName = R.string.no_filter.asString(),
            filterIconUrl = R.string.ic_mic_open.asString(),
            backgroundColor = Color(R.color.color_text_white_secondary.asColor()),
            microphoneColor = Color(R.color.color_foreground_neutral_important_default.asColor()),
            recordingAnimationType = null,
            recordingAnimationContent = null,
            playingAnimationType = null,
            playingAnimationContent = null,
            filterSoundUrl = null,
            latestTimestamp = 0,
            campaignConfig = null,
            videoTemplate = null,
            itemConfig = VoiceFilterItemConfig(0,0,0),
        )
    }
}

val VoiceFilterUiData.recordedBehavior get()= itemConfig.recordBehaviour
val VoiceFilterUiData.bubbleStyle get()= itemConfig.bubbleStyle

fun VoiceFilterUiData?.isValidVoiceFilter(): Boolean {
    this ?: return false
    return this.filterId != VoiceFilterUiData.NO_FILTER_ID
}

fun VoiceFilterUiData.hasUpdated(timestampList: List<Pair<Long, Int>>, latestUpdatedTimestamp: Int?): Boolean {
    if (timestampList.isEmpty() || this.isValidVoiceFilter().not()) return false
    val max = latestUpdatedTimestamp ?: return false
    val weekTimestamp = 7 * 24 * 60 * 60
    val min = latestUpdatedTimestamp.minus(weekTimestamp)
    return timestampList.firstOrNull { it.first == this.filterId }?.let {
        this.latestTimestamp in min..max && this.latestTimestamp > it.second
    } ?: true
}

// 不要使用filterRoleId，这个是服务端使用的。
fun VoiceFilterInfo.toVoiceFilterUiData(): VoiceFilterUiData? {
    return VoiceFilterUiData(
        filterId = this.filterId ?: return null,
        filterName = this.filterName ?: return null,
        filterIconUrl = this.filterIconUrl ?: return null,
        backgroundColor = this.backgroundColor?.let { parseColor(it) } ?: return null,
        microphoneColor = this.microphoneColor?.let { parseColor(it) } ?: return null,
        recordingAnimationType = this.recordingAnimationType,
        recordingAnimationContent = this.recordingAnimationContent,
        playingAnimationType = this.playingAnimationType,
        playingAnimationContent = this.playingAnimationContent,
        filterSoundUrl = this.filterSoundUrl,
        latestTimestamp = this.latestTimestamp.getIntDefault(),
        campaignConfig = this.campaignConfig,
        videoTemplate = this.videoTemplate,
        itemConfig = this.itemConfig?: VoiceFilterItemConfig(0,0,0),
    )
}

enum class VoiceFilterRecordedBehavior(val value: Int) {
    Normal(0),
    Preview(1)
}