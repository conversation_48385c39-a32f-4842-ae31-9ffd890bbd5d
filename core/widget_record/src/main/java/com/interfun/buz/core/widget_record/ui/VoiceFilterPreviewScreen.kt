package com.interfun.buz.core.widget_record.ui

import android.view.ViewGroup
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.LottieConstants
import com.airbnb.lottie.compose.rememberLottieComposition
import com.interfun.buz.base.ktx.asColor
import com.interfun.buz.base.ktx.voiceTimeStr
import com.interfun.buz.compose.components.CommonLoadingLottie
import com.interfun.buz.compose.components.IconFontText
import com.interfun.buz.compose.components.InitPreview
import com.interfun.buz.compose.components.SmartPositionContainer
import com.interfun.buz.compose.ktx.VerticalSpace
import com.interfun.buz.compose.ktx.asString
import com.interfun.buz.compose.modifier.debouncedClickable
import com.interfun.buz.compose.styles.TextStyles
import com.interfun.buz.core.widget_record.R
import com.interfun.buz.core.widget_record.entity.FilterState.FilterFail
import com.interfun.buz.core.widget_record.entity.FilterState.FilterSuccess
import com.interfun.buz.domain.im.social.entity.ShareItemBean
import com.interfun.buz.domain.im.social.entity.ShareType
import com.interfun.buz.domain.social.components.HorizontalShareListScreenNew
import com.interfun.buz.domain.social.components.VoiceFilterMessagePreviewType
import kotlinx.collections.immutable.PersistentList
import kotlinx.collections.immutable.persistentListOf
import com.interfun.buz.common.R as CommonR

/**
 * 语音滤镜预览界面状态基类
 * 包含所有子状态共有的属性
 *
 * @param isOnSendAnim 是否正在执行发送动画
 * @param sendAnimTranslationY 发送动画的Y轴偏移量
 * @param voiceFilter 语音滤镜数据
 * @param offsetY Y轴偏移量
 * @param notClickCancelOp 标记当前都没有点击的 cancel,如果点击了那么返回 false
 * @param isPlaying 是否正在播放预览（从VoicePreviewUiState迁移过来的属性）
 */
sealed class VoiceFilterPreviewState(
    open val isOnSendAnim: Boolean = false,
    open val sendAnimTranslationY: Int = 0,
    open val voiceFilter: com.interfun.buz.core.widget_record.state.VoiceFilterUiData? = null,
    open val offsetY: Int = 0,
    open val notClickCancelOp: Boolean = true,
    open val isPlaying: Boolean = false
) {
    /**
     * 加载状态 - 正在处理语音滤镜
     * 继承基类的所有共有属性，并包含语音时长信息
     *
     * @param duration 语音时长（毫秒），在loading状态下也需要显示正确的时间
     */
    data class Loading(
        // 加载状态特有属性
        val duration: Int = 0,

        // 继承的共有属性
        override val isOnSendAnim: Boolean = false,
        override val sendAnimTranslationY: Int = 0,
        override val voiceFilter: com.interfun.buz.core.widget_record.state.VoiceFilterUiData? = null,
        override val offsetY: Int = 0,
        override val notClickCancelOp: Boolean = true,
        override val isPlaying: Boolean = false
    ) : VoiceFilterPreviewState(
        isOnSendAnim = isOnSendAnim,
        sendAnimTranslationY = sendAnimTranslationY,
        voiceFilter = voiceFilter,
        offsetY = offsetY,
        notClickCancelOp = notClickCancelOp,
        isPlaying = isPlaying
    )

    /**
     * 完成状态 - 语音滤镜处理完成，可以预览和发送
     * 除了继承基类属性外，还包含完成状态特有的属性
     *
     * @param duration 语音时长（秒）
     * @param filterName 滤镜名称
     * @param asr ASR识别文本
     * @param backgroundColor 背景颜色
     * @param microphoneColor 麦克风颜色
     * @param filterState 滤镜状态
     * @param errorMessage 错误信息（仅在 FilterFail 状态时使用）
     */
    data class Completed(
        // 完成状态特有属性
        val duration: Int = 6,
        val filterName: String = "",
        val bubbleStyle: Int = 0,
        val asr: String = "",
        val backgroundColor: Int = R.color.brand_90.asColor(),
        val microphoneColor: Int = R.color.color_text_black_primary.asColor(),
        val filterState: Int = FilterSuccess,
        val errorMessage: String = "", // 新增错误信息字段

        // 继承的共有属性
        override val isOnSendAnim: Boolean = false,
        override val sendAnimTranslationY: Int = 0,
        override val voiceFilter: com.interfun.buz.core.widget_record.state.VoiceFilterUiData? = null,
        override val offsetY: Int = 0,
        override val notClickCancelOp: Boolean = true,
        override val isPlaying: Boolean = false
    ) : VoiceFilterPreviewState(
        isOnSendAnim = isOnSendAnim,
        sendAnimTranslationY = sendAnimTranslationY,
        voiceFilter = voiceFilter,
        offsetY = offsetY,
        notClickCancelOp = notClickCancelOp,
        isPlaying = isPlaying
    )
}

/**
 * 语音滤镜预览操作
 */
sealed class VoiceFilterPreviewAction {
    data object Cancel : VoiceFilterPreviewAction()
    data object Replay : VoiceFilterPreviewAction()
    data object Send : VoiceFilterPreviewAction()
    data object SendActionAnimFinish : VoiceFilterPreviewAction()
}


/**
 * 语音滤镜预览主界面（简化版）
 * 动画逻辑已移动到外部调用者中处理
 *
 * @param modifier 修饰符
 * @param state 当前状态
 * @param shareTypeList 分享类型列表
 * @param isShareLoading 是否正在分享加载中，用于控制分享列表的交互
 * @param playProgress 播放进度回调函数，用于最小化重组
 * @param onAction 操作回调
 * @param onShareAction 分享操作回调
 * @param videoPreviewTemplateUrl 背景动画URL，可选参数
 */
@Composable
fun VoiceFilterPreviewScreen(
    modifier: Modifier = Modifier,
    state: VoiceFilterPreviewState,
    shareTypeList: PersistentList<ShareType>,
    isShareLoading: Boolean = false,
    onAction: (VoiceFilterPreviewAction) -> Unit = {},
    onShareAction: (ShareItemBean) -> Unit = {},
    videoPreviewTemplateUrl: String? = null,
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Column(
            modifier = Modifier
                .background(
                    color = colorResource(CommonR.color.color_background_4_default),
                    shape = RoundedCornerShape(20.dp)
                )
                .padding(8.dp)
        ) {
            // 消息预览区域
            VoiceFilterMessagePreview(
                modifier = Modifier
                    .fillMaxWidth()
                    .aspectRatio(4f / 3f),
                state = state,
                videoPreviewTemplateUrl = videoPreviewTemplateUrl
            )

            VerticalSpace(20.dp)
            // 操作按钮区域
            VoiceFilterActionButtons(
                state = state,
                onAction = onAction
            )
            VerticalSpace(22.dp)
        }

        VerticalSpace(40.dp)

        // 根据分享状态控制透明度和交互
        val disableShare =
            isShareLoading || state is VoiceFilterPreviewState.Loading || (state is VoiceFilterPreviewState.Completed && state.filterState != FilterSuccess)
        val alpha = if (disableShare) 0.3f else 1.0f

        HorizontalShareListScreenNew(
            list = shareTypeList,
            modifier = Modifier.alpha(alpha),
            addBorder = false,
            onItemClick = { shareItem ->
                // 只有在非分享加载状态下才响应点击事件
                if (!disableShare) {
                    onShareAction(shareItem)
                }
            }
        )
    }
}


/**
 * 消息预览区域
 */
@Composable
private fun VoiceFilterMessagePreview(
    modifier: Modifier = Modifier,
    state: VoiceFilterPreviewState,
    videoPreviewTemplateUrl: String? = null
) {
    Box(
        modifier = modifier
            .background(
                color = colorResource(CommonR.color.overlay_grey_20),
                shape = RoundedCornerShape(14.dp)
            )
    ) {
        when (state) {
            is VoiceFilterPreviewState.Loading -> {
                LoadingPreview()
            }

            is VoiceFilterPreviewState.Completed -> {
                CompletedPreview(state, videoPreviewTemplateUrl)
            }
        }
    }
}

/**
 * 加载状态预览
 */
@Composable
private fun BoxScope.LoadingPreview() {
    // 背景渐变
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                brush = Brush.linearGradient(
                    colors = listOf(
                        Color.White.copy(alpha = 0.06f),
                        Color.White.copy(alpha = 0.14f),
                        Color.White.copy(alpha = 0.06f)
                    )
                ),
                shape = RoundedCornerShape(14.dp)
            )
    )

    // 加载文本
    Text(
        text = stringResource(R.string.loading),
        style = TextStyles.bodyMedium(),
        color = colorResource(CommonR.color.text_white_secondary),
        textAlign = TextAlign.Center,
        modifier = Modifier.align(Alignment.Center)
    )
}

/**
 * 完成状态预览
 */
@Composable
private fun BoxScope.CompletedPreview(
    state: VoiceFilterPreviewState.Completed,
    videoPreviewTemplateUrl: String? = null
) {
    // 根据滤镜状态显示不同内容
    if (state.filterState == FilterFail) {
        // 显示错误状态界面
        ErrorStatePreview(
            modifier = Modifier.align(Alignment.Center),
            errorMessage = state.errorMessage.ifEmpty {
                stringResource(R.string.unable_to_load_filter)
            }
        )
    } else {
        // 背景动画层 - 使用 Lottie 动画
        BackgroundAnimationLayer(videoPreviewTemplateUrl)

        // 消息内容层
        MessageContentLayer(
            modifier = Modifier.align(Alignment.BottomStart),
            state = state
        )
    }
}

/**
 * 错误状态预览
 * 根据 Figma 设计实现错误状态的 UI 界面
 */
@Composable
private fun ErrorStatePreview(
    modifier: Modifier = Modifier,
    errorMessage: String
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        // 错误图标和文本
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            // 警告图标
            IconFontText(
                iconText = stringResource(CommonR.string.ic_warning_solid),
                iconSize = 16.dp,
                iconColor = colorResource(CommonR.color.text_white_secondary)
            )

            // 错误文本
            Text(
                text = errorMessage,
                style = TextStyles.bodyMedium(),
                color = colorResource(CommonR.color.text_white_secondary),
                textAlign = TextAlign.Center
            )
        }
    }
}

/**
 * 背景动画层
 * 支持从网络URL加载Lottie动画
 */
@Composable
private fun BoxScope.BackgroundAnimationLayer(videoPreviewTemplateUrl: String? = null) {
    Box(
        modifier = Modifier
            .clip(RoundedCornerShape(14.dp))
            .fillMaxSize()
    ) {
        if (!videoPreviewTemplateUrl.isNullOrEmpty()) {
            // 从网络URL加载Lottie动画
            val compositionResult = rememberLottieComposition(
                LottieCompositionSpec.Url(
                    videoPreviewTemplateUrl
                )
            )
            LottieAnimation(
                composition = compositionResult.value,
                iterations = LottieConstants.IterateForever,
                isPlaying = true,
                modifier = Modifier.fillMaxSize()
            )
        } else {
            // 使用默认的本地Lottie动画作为后备
            val compositionResult =
                rememberLottieComposition(LottieCompositionSpec.Asset("voice_filter_recording_1.json"))
            LottieAnimation(
                composition = compositionResult.value,
                iterations = LottieConstants.IterateForever,
                isPlaying = true,
                modifier = Modifier.fillMaxSize()
            )
        }
    }
}

/**
 * 消息内容层
 */
@Composable
private fun MessageContentLayer(
    modifier: Modifier = Modifier,
    state: VoiceFilterPreviewState.Completed
) {
    SmartPositionContainer(
        modifier = modifier
            .padding(start = 22.dp, end = 7.dp)
            .fillMaxSize(),
        targetRatioX = 0.519f,
        targetRatioY = 0.5f
    ) {
        AndroidView(
            factory = { context ->
                val view =   com.interfun.buz.domain.social.components.VoiceFilterMessagePreview(context)
                view.apply {
                    val mLayoutParams = ViewGroup.LayoutParams(
                        ViewGroup.LayoutParams.WRAP_CONTENT,
                        ViewGroup.LayoutParams.WRAP_CONTENT
                    )
                    layoutParams = mLayoutParams
                }
                view
            }, modifier = Modifier.fillMaxSize(),
            update = { view ->
                view.initPreview(
                    VoiceFilterMessagePreviewType.PREVIEW_VIDEO,
                    state.duration.voiceTimeStr(),
                    state.filterName,
                    state.bubbleStyle,
                   state.asr,
                    state.backgroundColor,
                    state.microphoneColor
                )

                // 响应式动画控制：根据isPlaying状态自动控制动画
                if (state.isPlaying) {
                    view.startAnimation()
                } else {
                    view.stopAnimation()
                }
            })

    }
}


/**
 * 语音消息
 */
@Composable
private fun VoiceMessage(state: VoiceFilterPreviewState.Completed) {
    Row(
        modifier = Modifier
            .background(
                color = Color(0xFFC4F353),
                shape = RoundedCornerShape(17.dp, 17.dp, 4.dp, 17.dp)
            )
            .padding(horizontal = 12.dp, vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        // 滤镜标签
        FilterTag(filterName = state.filterName)

        // 时长
        Text(
            text = "0:0${state.duration}",
            style = TextStyles.bodyMedium().copy(fontSize = 14.sp),
            color = Color(0xFF0E4892),
            modifier = Modifier.weight(1f)
        )

        // 语音波形
        VoiceWaveform(isPlaying = state.isPlaying)
    }
}

/**
 * 滤镜标签
 */
@Composable
private fun FilterTag(filterName: String) {
    Row(
        modifier = Modifier
            .background(
                color = Color.Black.copy(alpha = 0.1f),
                shape = RoundedCornerShape(12.dp)
            )
            .padding(horizontal = 6.dp, vertical = 4.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(1.dp)
    ) {
        IconFontText(
            iconText = CommonR.string.ic_voice.asString(),
            iconSize = 12.dp,
            iconColor = Color(0xFF0E4892)
        )

        Text(
            text = filterName,
            style = TextStyles.labelSmall().copy(fontSize = 12.sp),
            color = Color(0xFF0E4892)
        )
    }
}

/**
 * 语音波形
 */
@Composable
private fun VoiceWaveform(isPlaying: Boolean) {
    Row(
        horizontalArrangement = Arrangement.spacedBy(2.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 左波形
        Box(
            modifier = Modifier
                .width(2.72.dp)
                .height(6.22.dp)
                .background(
                    color = Color(0xFF0E4892),
                    shape = RoundedCornerShape(14.dp)
                )
        )

        // 中波形
        Box(
            modifier = Modifier
                .width(2.72.dp)
                .height(11.28.dp)
                .background(
                    color = Color(0xFF0E4892),
                    shape = RoundedCornerShape(14.dp)
                )
        )

        // 右波形
        Box(
            modifier = Modifier
                .width(2.72.dp)
                .height(6.22.dp)
                .background(
                    color = Color(0xFF0E4892),
                    shape = RoundedCornerShape(14.dp)
                )
        )
    }
}

/**
 * 转录文本
 */
@Composable
private fun TranscriptionText() {
    Box(
        modifier = Modifier
            .background(
                color = Color.Black.copy(alpha = 0.1f),
                shape = RoundedCornerShape(13.dp, 13.dp, 2.dp, 13.dp)
            )
            .padding(horizontal = 10.dp, vertical = 6.dp)
    ) {
        Text(
            text = "うちは大阪の者や",
            style = TextStyles.titleMedium().copy(fontSize = 18.sp),
            color = Color.White
        )
    }
}


/**
 * 操作按钮区域
 */
@Composable
private fun VoiceFilterActionButtons(
    state: VoiceFilterPreviewState,
    onAction: (VoiceFilterPreviewAction) -> Unit
) {
    // 根据状态显示不同的按钮组合
    when (state) {
        is VoiceFilterPreviewState.Completed -> {
            if (state.filterState == FilterFail) {
                // 错误状态：显示 Cancel、Retry、Send 按钮
                ErrorStateActionButtons(onAction)
            } else {
                // 正常状态：显示原有的按钮
                NormalStateActionButtons(state, onAction)
            }
        }

        is VoiceFilterPreviewState.Loading -> {
            // 加载状态：显示原有的按钮
            NormalStateActionButtons(state, onAction)
        }
    }
}

/**
 * 错误状态的操作按钮
 * 根据 Figma 设计显示 Cancel、Retry、Send 三个按钮
 */
@Composable
private fun ErrorStateActionButtons(
    onAction: (VoiceFilterPreviewAction) -> Unit
) {
    Row(
        horizontalArrangement = Arrangement.spacedBy(0.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Cancel 按钮
        ActionButton(
            modifier = Modifier.weight(1f),
            icon = CommonR.string.ic_delete_solid.asString(),
            text = stringResource(CommonR.string.cancel),
            iconColor = colorResource(CommonR.color.text_white_important),
            textColor = colorResource(CommonR.color.text_white_secondary),
            onClick = { onAction(VoiceFilterPreviewAction.Cancel) }
        )

        // Retry 按钮
        ActionButton(
            modifier = Modifier.weight(1f),
            icon = CommonR.string.ic_refresh.asString(),
            text = stringResource(CommonR.string.retry),
            iconColor = colorResource(CommonR.color.text_white_important),
            textColor = colorResource(CommonR.color.text_white_secondary),
            onClick = { onAction(VoiceFilterPreviewAction.Replay) } // 复用 Replay 动作进行重试
        )

        // Send 按钮
        ActionButton(
            modifier = Modifier.weight(1f),
            icon = CommonR.string.ic_message.asString(),
            text = stringResource(CommonR.string.ve_start_send),
            iconColor = colorResource(CommonR.color.color_text_highlight_default),
            textColor = colorResource(CommonR.color.color_text_highlight_default),
            onClick = { onAction(VoiceFilterPreviewAction.Send) }
        )
    }
}

/**
 * 正常状态的操作按钮
 */
@Composable
private fun NormalStateActionButtons(
    state: VoiceFilterPreviewState,
    onAction: (VoiceFilterPreviewAction) -> Unit
) {
    Row(
        horizontalArrangement = Arrangement.spacedBy(0.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Cancel 按钮
        ActionButton(
            modifier = Modifier.weight(1f),
            icon = CommonR.string.ic_delete_solid.asString(),
            text = stringResource(CommonR.string.cancel),
            iconColor = colorResource(CommonR.color.text_white_important),
            textColor = colorResource(CommonR.color.text_white_secondary),
            onClick = { onAction(VoiceFilterPreviewAction.Cancel) }
        )

        // Replay 按钮
        ActionButton(
            modifier = Modifier.weight(1f),
            icon = when (state) {
                is VoiceFilterPreviewState.Loading -> null
                is VoiceFilterPreviewState.Completed -> {
                    if (state.isPlaying) {
                        CommonR.string.ic_square_solid.asString()
                    } else {
                        CommonR.string.ic_play_solid.asString()
                    }
                }
            },
            text = if (state.isPlaying) {
                stringResource(CommonR.string.stop)
            } else {
                stringResource(CommonR.string.play)
            },
            iconColor = colorResource(
                if (state is VoiceFilterPreviewState.Loading) {
                    R.color.color_foreground_neutral_important_disable
                } else {
                    R.color.text_white_important
                }
            ),
            textColor = colorResource(CommonR.color.text_white_secondary),
            showLoading = state is VoiceFilterPreviewState.Loading,
            onClick = { onAction(VoiceFilterPreviewAction.Replay) }
        )

        // Send 按钮
        ActionButton(
            modifier = Modifier.weight(1f),
            icon = CommonR.string.ic_message.asString(),
            text = stringResource(CommonR.string.ve_start_send),
            iconColor = colorResource(CommonR.color.color_text_highlight_default),
            textColor = colorResource(CommonR.color.color_text_highlight_default),
            onClick = { onAction(VoiceFilterPreviewAction.Send) }
        )
    }
}

/**
 * 操作按钮
 */
@Composable
private fun ActionButton(
    modifier: Modifier = Modifier,
    icon: String?,
    text: String,
    iconColor: Color,
    textColor: Color,
    showLoading: Boolean = false,
    onClick: () -> Unit
) {
    Column(
        modifier = modifier.debouncedClickable { onClick() },
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 图标或加载动画
        if (showLoading) {
            CommonLoadingLottie(
                modifier = Modifier.size(30.dp),
                color = R.color.color_foreground_neutral_important_disable

            )
        } else if (icon != null) {
            IconFontText(
                iconText = icon,
                iconSize = 30.dp,
                iconColor = iconColor
            )
        } else {
            // 占位空间
            Box(modifier = Modifier.size(30.dp))
        }

        VerticalSpace(6.dp)

        // 文本
        Text(
            text = text,
            style = TextStyles.bodyMedium().copy(fontSize = 14.sp),
            color = textColor,
            textAlign = TextAlign.Center
        )
    }
}

/**
 * 预览组件
 */
@Composable
@Preview
private fun PreviewVoiceFilterPreviewScreenLoading() {
    InitPreview()
    VoiceFilterPreviewScreen(
        modifier = Modifier
            .fillMaxWidth()
            .height(330.dp)
            .background(Color.Black)
            .padding(horizontal = 16.dp),
        state = VoiceFilterPreviewState.Loading(),
        shareTypeList = persistentListOf(),
        onAction = {}
    )
}

@Composable
@Preview
private fun PreviewVoiceFilterPreviewScreenCompleted() {
    InitPreview()
    VoiceFilterPreviewScreen(
        modifier = Modifier
            .width(330.dp)
            .background(Color.Black)
            .padding(16.dp),
        state = VoiceFilterPreviewState.Completed(isPlaying = false),
        shareTypeList = persistentListOf(),
        onAction = {}
    )
}

@Composable
@Preview
private fun PreviewVoiceFilterPreviewScreenPlaying() {
    InitPreview()
    VoiceFilterPreviewScreen(
        modifier = Modifier
            .width(330.dp)
            .background(Color.Black)
            .padding(16.dp),
        state = VoiceFilterPreviewState.Completed(isPlaying = true),
        shareTypeList = persistentListOf(
            ShareType.DOWN_LOAD,
            ShareType.SYSTEM_SHARE,
            ShareType.TELEGRAM
        ),
        onAction = {}
    )
}

@Composable
@Preview
private fun PreviewVoiceFilterPreviewScreenError() {
    InitPreview()
    VoiceFilterPreviewScreen(
        modifier = Modifier
            .width(330.dp)
            .background(Color.Black)
            .padding(16.dp),
        state = VoiceFilterPreviewState.Completed(
            filterState = FilterFail,
            errorMessage = "Filter loading failed"
        ),
        shareTypeList = persistentListOf(
            ShareType.DOWN_LOAD,
            ShareType.SYSTEM_SHARE,
            ShareType.TELEGRAM
        ),
        onAction = {}
    )
}
