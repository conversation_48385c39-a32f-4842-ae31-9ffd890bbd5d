package com.interfun.buz.core.widget_record.state

import com.interfun.buz.compose.components.TabOption

/**
 * <AUTHOR>
 * @date 2025/6/23
 * @desc VoiceFilter 每个Tab对应的所有数据
 */
data class VoiceFilterTabUiData(
    val tabType: Int,
    val tabName: String,
    val voiceFilterList: List<VoiceFilterUiData>
) {
    companion object {

        fun default(): VoiceFilterTabUiData {
            return VoiceFilterTabUiData(
                tabType = TabOption.allOptionTab.type,
                tabName = "",
                voiceFilterList = emptyList()
            )
        }
    }
}