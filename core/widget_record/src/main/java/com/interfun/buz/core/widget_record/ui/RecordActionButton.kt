package com.interfun.buz.core.widget_record.ui

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.unit.dp
import com.interfun.buz.compose.components.IconFontText
import com.interfun.buz.compose.ktx.asColor
import com.interfun.buz.compose.ktx.scale
import com.interfun.buz.core.widget_record.R
import com.interfun.buz.core.widget_record.entity.LocalRecordUIConfig

/**
 * <AUTHOR>
 * @date 2025/5/30
 * @desc
 */
@Composable
internal fun CancelButton(
    isVisibleLambda: () -> Boolean,
    isInCancelAreaLambda: () -> Boolean,
    modifier: Modifier,
) {
    val config = LocalRecordUIConfig.current
    RecordActionButton(
        isVisibleLambda = isVisibleLambda,
        isInAreaLambda = isInCancelAreaLambda,
        iconRes = R.string.ic_delete_solid,
        bgColorStart = R.color.color_text_consequential_default.asColor(),
        bgColorEnd = config.actionBtnBgColorRes.asColor(),
        iconColorStart = R.color.color_text_white_important.asColor(),
        iconColorEnd = config.actionBtnIconColorRes.asColor(),
        modifier = modifier
    )
}

@Composable
internal fun PreviewButton(
    isVisibleLambda: () -> Boolean,
    isInPreviewAreaLambda: () -> Boolean,
    modifier: Modifier,
) {
    val config = LocalRecordUIConfig.current
    RecordActionButton(
        isVisibleLambda = isVisibleLambda,
        isInAreaLambda = isInPreviewAreaLambda,
        iconRes = R.string.ic_preview,
        bgColorStart = R.color.color_background_light_default.asColor(),
        bgColorEnd = config.actionBtnBgColorRes.asColor(),
        iconColorStart = R.color.color_text_black_primary.asColor(),
        iconColorEnd = config.actionBtnIconColorRes.asColor(),
        modifier = modifier
    )
}

@Composable
private fun RecordActionButton(
    isVisibleLambda: () -> Boolean,
    isInAreaLambda: () -> Boolean,
    iconRes: Int,
    bgColorStart: Color,
    bgColorEnd: Color,
    iconColorStart: Color,
    iconColorEnd: Color,
    modifier: Modifier = Modifier,
) {
    val config = LocalRecordUIConfig.current
    val scale by animateFloatAsState(
        targetValue = if (isInAreaLambda()) config.actionBtnScalePercent else 1f
    )
    AnimatedVisibility(
        visible = isVisibleLambda(),
        modifier = modifier
            .graphicsLayer {
                scale(scale)
            }
    ) {
        val bgColor by animateColorAsState(
            targetValue = if (isInAreaLambda()) bgColorStart else bgColorEnd
        )
        val iconColor by animateColorAsState(
            targetValue = if (isInAreaLambda()) iconColorStart else iconColorEnd
        )
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape)
                .background(color = bgColor)
        ) {
            IconFontText(
                modifier = Modifier
                    .align(alignment = Alignment.Center)
                    .graphicsLayer {
                        scale(1f / scale)
                    },
                iconRes = iconRes,
                iconSize = 24.dp,
                iconColor = iconColor
            )
        }
    }
}
