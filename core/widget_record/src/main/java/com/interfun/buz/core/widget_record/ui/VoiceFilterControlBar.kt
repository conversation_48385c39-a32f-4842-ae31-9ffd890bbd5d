package com.interfun.buz.core.widget_record.ui

import androidx.compose.foundation.layout.*
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.buz.idl.common.bean.ActionInfo
import com.buz.idl.common.bean.CampaignConfig
import com.interfun.buz.base.ktx.calculateValue
import com.interfun.buz.base.ktx.minOf
import com.interfun.buz.compose.components.IconFontText
import com.interfun.buz.compose.ktx.VerticalSpace
import com.interfun.buz.compose.ktx.asColor
import com.interfun.buz.compose.ktx.asString
import com.interfun.buz.compose.modifier.debouncedClickable
import com.interfun.buz.compose.styles.TextStyles
import com.interfun.buz.compose.utils.RecomposeCountHelper
import com.interfun.buz.core.widget_record.R
import com.interfun.buz.core.widget_record.entity.LocalRecordUIConfig

/**
 * <AUTHOR>
 * @date 2024/12/2
 * @desc
 */
@Composable
fun VoiceFilterControlBar(
    currentScrollPosLambda: () -> Float,
    campaignConfig: ()-> CampaignConfig?,
    isClickEnable: () -> Boolean,
    onClickClear: () -> Unit,
    onClickQuit: () -> Unit,
    onClickCampaign: (ActionInfo?) -> Unit,
    modifier: Modifier = Modifier,
) {
    SideEffect {
        RecomposeCountHelper.increment("VoiceFilterControlBar")
    }

    val config = LocalRecordUIConfig.current
    Row(
        modifier = modifier
            .fillMaxWidth()
            .heightIn(min = config.voiceFilterControlBarHeightMin)
            .height(IntrinsicSize.Min),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        val alphaWhenScrolling by remember {
            derivedStateOf {
                calculateValue(0.4f, 1f, currentScrollPosLambda().minOf(1f))
            }
        }

        VoiceFilterBtnClear(
            modifier = Modifier
                .debouncedClickable(
                    enabled = isClickEnable(),
                    onClick = onClickClear
                )
                .graphicsLayer {
                    alpha = alphaWhenScrolling
                },
            centerGapHeight = config.voiceFilterControlBarCenterGap
        )

        Box(
            modifier = Modifier
                .fillMaxHeight()
                .weight(1f),
        ) {
            VoiceFilterCampaignEntry(
                modifier = Modifier.align(Alignment.Center),
                config = campaignConfig,
                isClickEnable = isClickEnable,
                onClick = onClickCampaign
            )
        }

        VoiceFilterBtnQuit(
            modifier = Modifier
                .debouncedClickable(
                    enabled = isClickEnable(),
                    onClick = onClickQuit
                ),
            centerGapHeight = config.voiceFilterControlBarCenterGap
        )
    }
}

@Composable
private fun VoiceFilterBtnClear(
    modifier: Modifier = Modifier,
    centerGapHeight: Dp = 8.dp
) {
    SideEffect {
        RecomposeCountHelper.increment("VoiceFilterBtnClear")
    }
    Column(
        modifier = modifier
            .widthIn(min = 80.dp)
            .padding(horizontal = 10.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        IconFontText(
            iconRes = R.string.ic_clear,
            iconColor = R.color.color_text_white_secondary.asColor(),
            iconSize = 24.dp,
            size = 24.dp
        )
        VerticalSpace(centerGapHeight)
        val config = LocalRecordUIConfig.current
        Text(
            text = R.string.clear.asString(),
            color = R.color.color_text_white_tertiary.asColor(),
            style = if (config.showInnerVoiceFilterEntry) {
                TextStyles.labelSmall()
            } else {
                TextStyles.labelMedium()
            },
        )
    }
}

@Composable
private fun VoiceFilterBtnQuit(
    modifier: Modifier = Modifier,
    centerGapHeight: Dp = 8.dp
) {
    SideEffect {
        RecomposeCountHelper.increment("VoiceFilterBtnQuit")
    }
    Column(
        modifier = modifier
            .widthIn(min = 80.dp)
            .padding(horizontal = 10.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        IconFontText(
            iconRes = R.string.ic_quit,
            iconColor = R.color.color_text_white_secondary.asColor(),
            iconSize = 24.dp,
            size = 24.dp
        )
        VerticalSpace(centerGapHeight)
        val config = LocalRecordUIConfig.current
        Text(
            text = R.string.exit.asString(),
            color = R.color.color_text_white_tertiary.asColor(),
            style = if (config.showInnerVoiceFilterEntry) {
                TextStyles.labelSmall()
            } else {
                TextStyles.labelMedium()
            },
        )
    }
}

@Composable
@Preview
private fun PreviewVoiceFilterControlBar() {
    VoiceFilterControlBar(
        currentScrollPosLambda = { 1f },
        isClickEnable = { true },
        onClickClear = {},
        onClickQuit = {},
        onClickCampaign = {},
        campaignConfig = { null }
    )
}