package com.interfun.buz.core.widget_record.ui

import androidx.compose.runtime.Composable
import androidx.compose.runtime.SideEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.viewinterop.AndroidView
import com.airbnb.lottie.LottieProperty
import com.airbnb.lottie.compose.*
import com.airbnb.lottie.compose.LottieCompositionSpec.Asset
import com.interfun.buz.compose.utils.RecomposeCountHelper
import com.interfun.buz.core.widget_record.entity.RecordOperateStatus
import com.interfun.buz.core.widget_record.state.VoiceFilterUiData
import com.interfun.buz.core.widget_record.view.VoiceFilterLottie
import kotlinx.coroutines.launch

/**
 * <AUTHOR>
 * @date 2025/5/30
 * @desc
 */
private const val recordLottieAnimPath = "voice_recording.json"
private const val recordCancelLottiePath = "voice_record_cancel.json"

@Composable
internal fun RecordLottie(
    isRecording: () -> Boolean,
    isInActionBtnArea: () -> Boolean,
    recordOperateStatusLambda: () -> RecordOperateStatus,
    voiceFilterBgColor: () -> Color?,
    modifier: Modifier = Modifier,
) {
    if (isRecording()) {
        SideEffect {
            RecomposeCountHelper.increment("RecordLottie")
        }
        //先 remember 两个，后面不会重组两次
        val compositionAction by rememberLottieComposition(Asset(recordCancelLottiePath))
        val compositionPressing by rememberLottieComposition(Asset(recordLottieAnimPath))
        val dynamicProperties = if (!isInActionBtnArea()) {
            voiceFilterBgColor()?.let {
                rememberLottieDynamicProperties(
                    rememberLottieDynamicProperty(
                        property = LottieProperty.STROKE_COLOR,
                        value = it.toArgb(),
                        keyPath = arrayOf("**")
                    ),
                )
            }
        } else {
            null
        }
        val isLocking = recordOperateStatusLambda().isLocking
        val composition = if (isInActionBtnArea() || dynamicProperties != null || isLocking) {
            compositionAction
        } else {
            compositionPressing
        }
        LottieAnimation(
            modifier = modifier,
            composition = composition,
            iterations = LottieConstants.IterateForever,
            dynamicProperties = dynamicProperties
        )
    }
}

@Composable
internal fun VoiceFilterRecordingLottie(
    modifierLambda: () -> Modifier,
    isRecording: () -> Boolean,
    isInCancelOrPreviewArea: () -> Boolean,
    isInVoiceFilterMode: () -> Boolean,
    currentVoiceFilter: () -> VoiceFilterUiData?,
) {
    if (!isInVoiceFilterMode() || !isRecording() || isInCancelOrPreviewArea()) return
    val voiceFilter = currentVoiceFilter() ?: return
    val animType = voiceFilter.recordingAnimationType ?: return
    val animContent = voiceFilter.recordingAnimationContent ?: return
    SideEffect {
        RecomposeCountHelper.increment("VoiceFilterRecordingLottie")
    }
    val scope = rememberCoroutineScope()
    AndroidView(
        modifier = modifierLambda(),
        factory = { VoiceFilterLottie(it) },
        update = {
            scope.launch {
                it.play(
                    isRecordType = true,
                    animContent = animContent,
                    animType = animType
                )
            }
        }
    )
}