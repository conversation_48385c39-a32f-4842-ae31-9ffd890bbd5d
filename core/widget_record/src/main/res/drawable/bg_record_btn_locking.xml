<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="104dp"
    android:height="104dp"
    android:viewportWidth="104"
    android:viewportHeight="104">
  <path
      android:pathData="M52,0L52,0A52,52 0,0 1,104 52L104,52A52,52 0,0 1,52 104L52,104A52,52 0,0 1,0 52L0,52A52,52 0,0 1,52 0z"
      android:fillColor="#141414"/>
  <path
      android:pathData="M52,0L52,0A52,52 0,0 1,104 52L104,52A52,52 0,0 1,52 104L52,104A52,52 0,0 1,0 52L0,52A52,52 0,0 1,52 0z"
      android:fillAlpha="0.75">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:centerX="18.77"
          android:centerY="11.92"
          android:gradientRadius="110.61"
          android:type="radial">
        <item android:offset="0" android:color="#66FFFFFF"/>
        <item android:offset="0.2" android:color="#51FFFFFF"/>
        <item android:offset="1" android:color="#28FFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M52,0.41L52,0.41A51.59,51.59 0,0 1,103.59 52L103.59,52A51.59,51.59 0,0 1,52 103.59L52,103.59A51.59,51.59 0,0 1,0.41 52L0.41,52A51.59,51.59 0,0 1,52 0.41z"
      android:strokeAlpha="0.2"
      android:strokeWidth="0.818182"
      android:fillColor="#00000000">
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:centerX="13.48"
          android:centerY="-0"
          android:gradientRadius="45.15"
          android:type="radial">
        <item android:offset="0" android:color="#C6FFFFFF"/>
        <item android:offset="1" android:color="#00FFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
