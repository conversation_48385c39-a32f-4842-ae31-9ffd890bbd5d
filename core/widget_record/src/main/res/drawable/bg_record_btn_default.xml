<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="220dp"
    android:height="220dp"
    android:viewportWidth="220"
    android:viewportHeight="220">
  <path
      android:pathData="M110,0L110,0A110,110 0,0 1,220 110L220,110A110,110 0,0 1,110 220L110,220A110,110 0,0 1,0 110L0,110A110,110 0,0 1,110 0z"
      android:fillColor="#141414"/>
  <path
      android:pathData="M110,0L110,0A110,110 0,0 1,220 110L220,110A110,110 0,0 1,110 220L110,220A110,110 0,0 1,0 110L0,110A110,110 0,0 1,110 0z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:centerX="39.71"
          android:centerY="25.22"
          android:gradientRadius="233.98"
          android:type="radial">
        <item android:offset="0" android:color="#66FFFFFF"/>
        <item android:offset="0.21" android:color="#51FFFFFF"/>
        <item android:offset="1" android:color="#28FFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:strokeWidth="1"
      android:pathData="M110,0.5L110,0.5A109.5,109.5 0,0 1,219.5 110L219.5,110A109.5,109.5 0,0 1,110 219.5L110,219.5A109.5,109.5 0,0 1,0.5 110L0.5,110A109.5,109.5 0,0 1,110 0.5z"
      android:strokeAlpha="0.2"
      android:fillColor="#00000000">
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:centerX="28.51"
          android:centerY="-0"
          android:gradientRadius="95.51"
          android:type="radial">
        <item android:offset="0" android:color="#C6FFFFFF"/>
        <item android:offset="1" android:color="#00FFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
