apply plugin: 'com.android.application'
apply from: "../base_config.gradle"
apply plugin: 'org.jetbrains.kotlin.android'
//apply from: "../build_config.gradle"

android {
    defaultConfig {
        manifestPlaceholders =[HW_APP_ID:"110599309"]
        applicationId "com.interfun.buz.demo"
        multiDexEnabled true
        ndk {
            abiFilters "armeabi-v7a"
            abiFilters "arm64-v8a"
        }
    }

    packagingOptions{
        exclude 'META-INF/idlkit_release.kotlin_module'
        pickFirst '**/liblog.so'
        pickFirst 'lib/*/libshadowhook.so'
        pickFirst '**/libshadowhook.so'
    }
    namespace 'com.interfun.buz.demo'
}

configurations.all {
    //全局移除腾讯X5内核相关依赖
    exclude group:'com.tencent.tbs.tbssdk', module:"sdk"
    resolutionStrategy {
        force("androidx.emoji2:emoji2-views-helper:1.3.0")
        force("androidx.emoji2:emoji2:1.3.0")
    }
}

dependencies {
    implementation project(':common')
    implementation project(':common-compose')
    implementation project(':core:widget_liveplace')
    implementation project(':component:download')
    implementation project(':biz-center:user')
}