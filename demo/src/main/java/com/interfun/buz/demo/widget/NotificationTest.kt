package com.interfun.buz.demo.widget

import android.app.Notification
import android.app.Notification.BigPictureStyle
import android.app.NotificationManager
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.os.Build
import android.widget.RemoteViews
import androidx.core.app.NotificationCompat
import androidx.core.app.Person
import androidx.core.content.FileProvider
import androidx.core.content.pm.ShortcutInfoCompat
import androidx.core.content.pm.ShortcutManagerCompat
import androidx.core.graphics.drawable.IconCompat
import androidx.core.graphics.drawable.toBitmapOrNull
import coil.Coil
import coil.imageLoader
import coil.request.ImageRequest
import coil.transform.CircleCropTransformation
import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.base.ktx.dp
import com.interfun.buz.common.constants.CommonConstant
import com.interfun.buz.common.utils.NotificationChannelUtils
import com.interfun.buz.common.utils.NotificationUtil
import com.interfun.buz.demo.R

object NotificationTest {
    suspend fun createCustomNotification(context: Context): Notification {
        val channelId = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannelUtils.getWalkieTalkieForegroundChannel()
            channel?.id ?: CommonConstant.NOTIFICATION_CHANNEL_ID_WT_FOREGROUND
        } else {
            CommonConstant.NOTIFICATION_CHANNEL_ID_WT_FOREGROUND
        }
        val builder = NotificationUtil.createBaseBuilder(
            context,
            channelId,
            null,
            R.mipmap.common_ic_notification_big
        )
        builder.setCustomContentView(
            RemoteViews(
                context.packageName,
                R.layout.demo_test_notification_normal
            )
        )
        builder.setCustomHeadsUpContentView(
            RemoteViews(
                context.packageName,
                R.layout.demo_test_notification_head_up
            )
        )
        builder.setCustomBigContentView(
            RemoteViews(
                context.packageName,
                R.layout.demo_test_notification_big
            )
        )
        builder.setLargeIcon(defaultBitmap())
//        builder.setContentTitle("contentTitle")
//        builder.setTicker("tickerText")
//        builder.setContentText("ContentText")
//        builder.setGroupSummary()
        builder.setSubText("subText")
        return builder.build()
    }

    fun createBigTextNotification(context: Context): Notification {
        val channelId = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannelUtils.getWalkieTalkieForegroundChannel()
            channel?.id ?: CommonConstant.NOTIFICATION_CHANNEL_ID_WT_FOREGROUND
        } else {
            CommonConstant.NOTIFICATION_CHANNEL_ID_WT_FOREGROUND
        }
        val builder = NotificationUtil.createBaseBuilder(
            context,
            channelId,
            null,
            R.mipmap.common_ic_notification_big
        )
//        builder.setCustomContentView(
//            RemoteViews(
//                context.packageName,
//                R.layout.common_test_notification_normal
//            )
//        )
//        builder.setCustomHeadsUpContentView(
//            RemoteViews(
//                context.packageName,
//                R.layout.common_test_notification_head_up
//            )
//        )
//        builder.setCustomBigContentView(
//            RemoteViews(
//                context.packageName,
//                R.layout.common_test_notification_big
//            )
//        )
        builder.setStyle(
            NotificationCompat.BigTextStyle()
                .bigText("This is big text.This is big text.This is big text.This is big text.This is big text.This is big text.This is big text.This is big text.This is big text.This is big text.This is big text.This is big text.This is big text.This is big text.This is big text.This is big text.This is big text.This is big text.This is big text.This is big text.This is big text.This is big text.This is big text.This is big text.This is big text.")
                .setSummaryText("this is summary")
                .setBigContentTitle("BigContentTitle")
        )
        return builder.build()
    }

    suspend fun createBigPictureNotification(context : Context) : Notification{
        val channelId = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannelUtils.getWalkieTalkieForegroundChannel()
            channel?.id ?: CommonConstant.NOTIFICATION_CHANNEL_ID_WT_FOREGROUND
        } else {
            CommonConstant.NOTIFICATION_CHANNEL_ID_WT_FOREGROUND
        }
        val builder = NotificationUtil.createBaseBuilder(
            context,
            channelId,
            null,
            R.mipmap.common_ic_notification_big
        )
//        builder.setLargeIcon(defaultBitmap())


        builder.setStyle(
            NotificationCompat.BigPictureStyle().bigPicture(bigBitmap())
                .setContentDescription("desc1\ndesc2\ndesc3")
                .showBigPictureWhenCollapsed(true)
                .setSummaryText("sum1\nsum2\nsum3")
                .setBigContentTitle("bigContentTitle")


        )
        return builder.build()
    }

    suspend fun createMessageNotification(context: Context) : Notification{
        val channelId = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannelUtils.getWalkieTalkieForegroundChannel()
            channel?.id ?: CommonConstant.NOTIFICATION_CHANNEL_ID_WT_FOREGROUND
        } else {
            CommonConstant.NOTIFICATION_CHANNEL_ID_WT_FOREGROUND
        }
        val builder = NotificationUtil.createBaseBuilder(
            context,
            channelId,
            null,
            R.mipmap.common_ic_notification_big
        )
        val user = Person.Builder().setName("userName").setIcon(IconCompat.createWithBitmap(
            defaultBitmap()!!
        )).build()
        builder.setContentText("contentText")
        ShortcutManagerCompat.pushDynamicShortcut(
            context,
            ShortcutInfoCompat.Builder(context, "6666")
                .setShortLabel(user.name?:"label")
                .setIntent(Intent(context,WidgetTestActivity::class.java).setAction("action"))
                .setLongLived(true)
                .setPerson(user)
                .build()
        )
        builder.setShortcutId("6666")
//        builder.setLargeIcon(defaultBitmap())
        val pic = bigBitmap("https://img.syt5.com/2021/0802/20210802052613520.jpg")
        val file = appContext.imageLoader.diskCache?.get("https://img.syt5.com/2021/0802/20210802052613520.jpg")?.data?.toFile()
        val path = FileProvider.getUriForFile(appContext,"com.interfun.buz.demo",file!!)
        builder.setStyle(
            NotificationCompat.MessagingStyle(user)
                .setConversationTitle(user.name)
                .addMessage(NotificationCompat.MessagingStyle.Message("picMessage1",System.currentTimeMillis(),user))
                .addMessage(NotificationCompat.MessagingStyle.Message("message2",System.currentTimeMillis(),user))
                .addMessage(NotificationCompat.MessagingStyle.Message("picMessage3",System.currentTimeMillis(),user).setData("image/*",path))
                .addMessage(NotificationCompat.MessagingStyle.Message("message4",System.currentTimeMillis(),user))
        )
        val notification = builder.build()
        return notification
    }

    suspend fun createGroupMessageNotification(context: Context) : Notification{
        val channelId = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannelUtils.getWalkieTalkieForegroundChannel()
            channel?.id ?: CommonConstant.NOTIFICATION_CHANNEL_ID_WT_FOREGROUND
        } else {
            CommonConstant.NOTIFICATION_CHANNEL_ID_WT_FOREGROUND
        }
        val builder = NotificationUtil.createBaseBuilder(
            context,
            channelId,
            null,
            R.mipmap.common_ic_notification_big
        )
        val group = Person.Builder().setName("groupName").setIcon(IconCompat.createWithBitmap(
            defaultBitmap(R.drawable.common_pic_portrait_group_default)!!
        )).build()
        val user1 = Person.Builder().setName("user1").setIcon(IconCompat.createWithBitmap(
            defaultBitmap()!!
        )).build()
        val user2 = Person.Builder().setName("user2").setIcon(IconCompat.createWithBitmap(
            defaultBitmap()!!
        )).build()
        val user3 = Person.Builder().setName("user3").setIcon(IconCompat.createWithBitmap(
            defaultBitmap()!!
        )).build()
        val pic = bigBitmap("https://img.syt5.com/2021/0802/20210802052613520.jpg")
        val file = appContext.imageLoader.diskCache?.get("https://img.syt5.com/2021/0802/20210802052613520.jpg")?.data?.toFile()
        val path = FileProvider.getUriForFile(appContext,"com.interfun.buz.demo",file!!)
        builder.setContentText("contentText")
        ShortcutManagerCompat.pushDynamicShortcut(
            context,
            ShortcutInfoCompat.Builder(context, "7777")
                .setShortLabel(group.name?:"label")
                .setIntent(Intent(context,WidgetTestActivity::class.java).setAction("action"))
                .setLongLived(true)
                .setPerson(group)
                .build()
        )
        builder.setShortcutId("7777")
//        builder.setLargeIcon(defaultBitmap())
        builder.setStyle(
            NotificationCompat.MessagingStyle(group)
                .setConversationTitle(group.name)
                .setGroupConversation(true)
                .addMessage(NotificationCompat.MessagingStyle.Message("message1FromUser1",System.currentTimeMillis(),user1))
                .addMessage(NotificationCompat.MessagingStyle.Message("picMessage2FromUser1",System.currentTimeMillis(),user1).setData("image/*",path))
                .addMessage(NotificationCompat.MessagingStyle.Message("picMessage1FromUser2",System.currentTimeMillis(),user2).setData("image/*",path))
                .addMessage(NotificationCompat.MessagingStyle.Message("picMessage1FromUser3",System.currentTimeMillis(),user3).setData("image/*",path))
        )
        val notification = builder.build()
        return notification
    }

    private suspend fun defaultBitmap(icon : Int? = null): Bitmap? {
        return Coil.imageLoader(appContext)
            .execute(
                ImageRequest.Builder(appContext)
                    .data(icon ?: com.interfun.buz.common.R.drawable.common_pic_portrait_user_default)
                    .transformations(CircleCropTransformation())
                    .size(64.dp,64.dp)
                    .build()
            )
            .drawable?.toBitmapOrNull()
    }

    private suspend fun bigBitmap(url: String? = null): Bitmap? {
        return Coil.imageLoader(appContext)
            .execute(
                ImageRequest.Builder(appContext)
//                    .data(R.drawable.demo_test_pic)
                    .data(url ?: "https://img.syt5.com/2021/0802/20210802052613520.jpg")
                    .size(300.dp, 200.dp)
                    .build()
            )
            .drawable?.toBitmapOrNull()
    }
}