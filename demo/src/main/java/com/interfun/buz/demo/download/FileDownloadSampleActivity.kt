package com.interfun.buz.demo.download

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.lifecycle.ViewModelProvider
import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.common.utils.StorageUtil.toReadableFileSize
import com.interfun.buz.compose.modifier.debouncedClickable
import com.interfun.buz.download.bean.DownloadStatus

/**
 * Author: ChenYouSheng
 * Date: 2025/5/30
 * Email: <EMAIL>
 * Desc: Sample Activity to demonstrate FileDownloadManager
 */
class FileDownloadSampleActivity : ComponentActivity() {

    val fileDownloadViewModel: FileDownloadViewModel by lazy {
        ViewModelProvider(this)[FileDownloadViewModel::class.java]
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        println("FileDownloadSampleActivity onCreate")
        logDebug("FileDownloadSampleActivity", "onCreate")
        setContent {
            DownloadSampleScreen {
                fileDownloadViewModel.openFileWithSystemApp(context = this, filePath = it)
            }
        }
        fileDownloadViewModel.initData()
    }


    @Composable
    fun DownloadSampleScreen(onClick: (filePath: String) -> Unit) {
        val tasks by fileDownloadViewModel.displayItems.collectAsState()
        logDebug("DownloadSampleScreen", "tasks size: ${tasks.size}")

        Column(
            modifier = Modifier
                .padding(16.dp)
                .fillMaxSize()
        ) {
            Text(
                "Downloadable Files:",
                style = MaterialTheme.typography.titleMedium,
                color = Color.White
            )
            Spacer(modifier = Modifier.height(8.dp))

            Button(
                onClick = {
                    fileDownloadViewModel.cancelAllDownloads()
                },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("CancelAll")
            }

            Spacer(modifier = Modifier.height(8.dp))

            LazyColumn(modifier = Modifier.fillMaxSize()) {
                items(tasks, key = { it.key+it.fileName }) { taskState ->
                    DownloadTaskItem(
                        taskState = taskState,
                        viewModel = fileDownloadViewModel,
                        onClick = onClick
                    )
                    HorizontalDivider(color = Color.White)
                }
            }
        }
    }
}

@Composable
fun DownloadTaskItem(
    taskState: DownloadUiState,
    viewModel: FileDownloadViewModel,
    onClick: (filePath: String) -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
            .debouncedClickable(onClick = {
                if (taskState.status == DownloadStatus.SUCCESS) {
                    onClick(taskState.filePath)
                }
            }),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Column(
            modifier = Modifier
                .weight(1f)
                .padding(end = 8.dp)
        ) {
            Text(
                text = "File: ${taskState.fileName}",
                fontWeight = FontWeight.Bold,
                color = Color.White
            )
            Text(text = "Status: ${taskState.status.javaClass.simpleName}", color = Color.White)
            Text(
                text = "total Size: ${taskState.fileSize.toReadableFileSize()}", color = Color.White
            )
            Text(
                text = "download Size: ${taskState.downloadedSize.toReadableFileSize()}",
                color = Color.White
            )

            if (taskState.status == DownloadStatus.STARTED && taskState.progress > 0 && taskState.progress < 100) {
                Text(text = "Progress: ${taskState.progress}%", color = Color.White)
                LinearProgressIndicator(
                    progress = { taskState.progress / 100f }, modifier = Modifier.fillMaxWidth()
                )
            }
        }

        val downloadableItem = remember(taskState.url, taskState.fileName) {
            DownloadableItem(url = taskState.url, fileName = taskState.fileName, key = taskState.key)
        }
        when (taskState.status) {
            DownloadStatus.IDLE, DownloadStatus.CANCEL -> {
                Button(onClick = { viewModel.startDownload(downloadableItem) }) {
                    Text("Start")
                }
            }

            DownloadStatus.PENDING -> {
                Text("Pending...")
            }

            DownloadStatus.STARTED -> {
                Button(onClick = { viewModel.pauseDownload(downloadableItem) }) {
                    Text("Pause")
                }
            }

            DownloadStatus.PAUSED -> {
                IconButton(onClick = {
                    viewModel.resumeDownload(downloadableItem)
                }) {
                    Icon(
                        Icons.Filled.PlayArrow,
                        contentDescription = "Resume Download",
                        tint = Color.White
                    )
                }
            }

            DownloadStatus.SUCCESS -> {
                Icon(
                    Icons.Filled.CheckCircle,
                    contentDescription = "Download Successful",
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(40.dp)
                )
            }

            is DownloadStatus.FAILURE -> {
                IconButton(onClick = {
                    viewModel.startDownload(downloadableItem)
                }) {
                    Icon(
                        Icons.Filled.Refresh,
                        contentDescription = "Retry Download",
                        tint = MaterialTheme.colorScheme.error
                    )
                }
            }
        }

        // delete
        if (taskState.status is DownloadStatus.SUCCESS || taskState.status is DownloadStatus.FAILURE || taskState.status is DownloadStatus.PAUSED || taskState.status is DownloadStatus.CANCEL) {
            Spacer(modifier = Modifier.width(8.dp))
            IconButton(onClick = { viewModel.deleteFile(downloadableItem) }) {
                Icon(
                    Icons.Filled.Delete,
                    contentDescription = "Delete File",
                    tint = MaterialTheme.colorScheme.primary,
                )
            }
        }

        // cancel
        if (taskState.status is DownloadStatus.PENDING
            || taskState.status is DownloadStatus.STARTED
        ) {
            Spacer(modifier = Modifier.width(8.dp))
            Button(onClick = { viewModel.cancelDownload(downloadableItem) }) {
                Text("Cancel")
            }
        }
    }
}