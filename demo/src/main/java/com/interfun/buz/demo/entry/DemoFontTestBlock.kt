package com.interfun.buz.demo.entry

import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModel
import androidx.recyclerview.widget.LinearLayoutManager
import com.interfun.buz.base.ktx.BindingViewHolder
import com.interfun.buz.base.ktx.ItemsLiveData
import com.interfun.buz.base.ktx.MultiTypeAdapter
import com.interfun.buz.base.ktx.getBinding
import com.interfun.buz.base.ktx.getItem
import com.interfun.buz.base.ktx.observeItemsChanged
import com.interfun.buz.base.ktx.onClick
import com.interfun.buz.base.ktx.onItemClick
import com.interfun.buz.base.ktx.toast
import com.interfun.buz.common.base.binding.BaseBindingBlock
import com.interfun.buz.common.base.binding.BaseBindingDelegate
import com.interfun.buz.common.ktx.setFontBold
import com.interfun.buz.common.ktx.setFontLight
import com.interfun.buz.common.ktx.setFontMedium
import com.interfun.buz.common.ktx.setFontRegular
import com.interfun.buz.demo.databinding.FragmentDemoEntryBinding
import com.interfun.buz.demo.databinding.ItemMessageBinding
import com.interfun.buz.demo.databinding.ItemTitleBinding

/**
 * <AUTHOR>
 *
 * @date 2022/6/21
 *
 * @desc
 */
class DemoFontTestBlock(fragment: DemoEntryFragment, binding: FragmentDemoEntryBinding) :
    BaseBindingBlock<FragmentDemoEntryBinding>(binding){

    private val viewModel: MessageListViewModel by fragment.viewModels()
    private val adapter = MultiTypeAdapter {
        register(TitleDelegate())
        register(MessageViewDelegate())
    }

    init {
        binding.rvTest.layoutManager = LinearLayoutManager(fragment.context)
        binding.rvTest.adapter = adapter
        adapter.observeItemsChanged(fragment.viewLifecycleOwner, viewModel.items) { oldItem, newItem ->
            (oldItem is String && newItem is String && oldItem == newItem) ||
                (oldItem is Message && newItem is Message && oldItem.id == newItem.id)
        }
        binding.cvSize.apply {
            text = "字号"
            count = 18f
            callback = { size ->
                repeat(adapter.items.size) { index ->
                    if (adapter.getItem(index) is Message) {
                        val holder = binding.rvTest.findViewHolderForLayoutPosition(index)
                        holder?.getBinding<ItemMessageBinding>()?.apply {
                            tvContent.textSize = size
                        }
                    }
                }
            }
        }
        binding.cvLetterSpacing.apply {
            text = "字间距"
            useFloat = true
            gap = 0.01f
            bigGap = 0.05f
            fractionDigits = 2
            count = 0.03f
            callback = { size ->
                repeat(adapter.items.size) { index ->
                    if (adapter.getItem(index) is Message) {
                        val holder = binding.rvTest.findViewHolderForLayoutPosition(index)
                        holder?.getBinding<ItemMessageBinding>()?.apply {
                            tvContent.letterSpacing = size
                        }
                    }
                }
            }
        }
        binding.cvLineSpacing.apply {
            text = "行间距系数"
            useFloat = true
            count = 1f
            callback = { size ->
                repeat(adapter.items.size) { index ->
                    if (adapter.getItem(index) is Message) {
                        val holder = binding.rvTest.findViewHolderForLayoutPosition(index)
                        holder?.getBinding<ItemMessageBinding>()?.apply {
                            tvContent.setLineSpacing(tvContent.lineSpacingExtra, size)
                        }
                    }
                }
            }
        }

        binding.cvLineSpacingExtra.apply {
            text = "行间距大小"
            count = 0f
            callback = { size ->
                repeat(adapter.items.size) { index ->
                    if (adapter.getItem(index) is Message) {
                        val holder = binding.rvTest.findViewHolderForLayoutPosition(index)
                        holder?.getBinding<ItemMessageBinding>()?.apply {
                            tvContent.setLineSpacing(size, tvContent.lineSpacingMultiplier)
                        }
                    }
                }
            }
        }
    }

    class TitleDelegate : BaseBindingDelegate<String, ItemTitleBinding>() {
        override fun onBindViewHolder(binding: ItemTitleBinding, item: String, position: Int) {
            binding.tvTitle.text = item
        }
    }

    data class Message(val id: Int, val name: String, val content: String)

    class MessageViewDelegate : BaseBindingDelegate<Message, ItemMessageBinding>() {
        override fun onBindViewHolder(binding: ItemMessageBinding, item: Message, position: Int) {
            when (item.id) {
                1 -> binding.tvContent.setFontLight()
                2 -> binding.tvContent.setFontRegular()
                3 -> binding.tvContent.setFontMedium()
                4 -> binding.tvContent.setFontBold()
            }
            binding.tvContent.text = "${item.name} :\n${item.content}"
        }

        override fun onViewHolderCreated(holder: BindingViewHolder<ItemMessageBinding>) {
            super.onViewHolderCreated(holder)
            holder.onClick(holder.binding.tvContent) { binding, pos ->
                val item = getItem(pos)
                toast("onClick: name = ${item.name}")
                // binding.tvName.text = item.name + " clicked"
            }
            holder.onItemClick(this) { binding, item, pos ->
                toast("onItemClick: content = ${item.content}")
                // binding.tvContent.text = item.content + " clicked"
            }
        }
    }

    class MessageListViewModel : ViewModel() {
        private val test = "Buz Font Test @123! 文本示例！： テキストの例 مثال نصي مثال نصي Ví dụ văn bản Παράδειγμα κειμένου"
        private val data = listOf(
            "字体演示",
            Message(1, "Light", test),
            Message(2, "Regular", test),
            Message(3, "Medium", test),
            Message(4, "Bold", test),
        )

        val items = ItemsLiveData(data)
    }

}