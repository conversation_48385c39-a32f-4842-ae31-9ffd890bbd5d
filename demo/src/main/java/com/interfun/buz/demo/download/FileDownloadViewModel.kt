package com.interfun.buz.demo.download

import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.os.Build
import android.provider.Settings
import androidx.compose.runtime.mutableStateListOf
import androidx.core.content.FileProvider
import androidx.core.net.toUri
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.download.BuzDownLoadManager
import com.interfun.buz.download.IDownloadManager
import com.interfun.buz.download.bean.DownloadProgressChange
import com.interfun.buz.download.bean.DownloadStatus
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import java.io.File


data class DownloadUiState(
    val url: String,
    val fileName: String,
    val status: DownloadStatus,
    val progress: Int, // 0-100
    val downloadedSize: Long,
    val fileSize: Long,
    val filePath: String,
    val key: String,
)

data class DownloadableItem(
    val key: String,
    val url: String,
    val fileName: String? = null
)

/**
 * Author: ChenYouSheng
 * Date: 2025/5/30
 * Email: <EMAIL>
 * Desc: ViewModel for FileDownloadSampleActivity
 */
class FileDownloadViewModel : ViewModel() {

    private val _downloadableItems = mutableStateListOf(
        // 1.3Gb
        DownloadableItem(
            url = "https://testfile.org/1.3GBiconpng",
            fileName = "1.3GBiconpng",
            key = "a"
        ),
        // 1Mb
        DownloadableItem(
            url = "http://speedtest.tele2.net/1MB.zip",
            fileName = "1MB.zip",
            key = "b"
        ),
        // 86.2Mb
        DownloadableItem(
            url = "https://capelinap.lzpsap1.com/Android/com.interfun.buz/test_video_release_6.2.2.4/release/1748505481354/1.65.0_20567_release.apk",
            fileName = "1.65.0_20567_release.apk",
            key = "c"

        ),
        // 86.2Mb
        DownloadableItem(
            url = "https://capelinap.lzpsap1.com/Android/com.interfun.buz/test_video_release_6.2.2.4/release/1748505481354/1.65.0_20567_release.apk",
            fileName = "1.65.0_20567_release.apk（2）",
            key = "d"
        ),
        // 1.1Gb
        DownloadableItem(
            url = "https://download.blender.org/durian/movies/Sintel.2010.1080p.mkv",
            fileName = "Sintel.2010.1080p.mkv",
            key = "e"
        ),
        // 425Mb
        DownloadableItem(
            url = "https://download.blender.org/ED/ED_1024.avi",
            fileName = "ED_1024.avi",
            key = "f"
        ),
        // 4kb
        DownloadableItem(
            url = "https://cdn.buymeacoffee.com/buttons/v2/default-yellow.png",
            fileName = "default-yellow.png",
            key = "g"
        )
    )

    // 保存每个URL对应的下载状态
    private val _downloadStates = mutableMapOf<String, DownloadUiState>() // key: item.key
    private val urlToKeys = mutableMapOf<String, MutableSet<String>>() // url -> set of keys

    private val _displayItems = MutableStateFlow<List<DownloadUiState>>(emptyList())
    val displayItems: StateFlow<List<DownloadUiState>> = _displayItems

    private val downloadScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    private val userDownloadManager: IDownloadManager by lazy {
        BuzDownLoadManager(
            appContext = appContext,
            downloadScope = downloadScope,
            storagePathName = "demo_download"
        )
    }

    fun initData() {
        // 初始化显示项
        viewModelScope.launch {
            initializeDisplayItems()
        }

        // 监听状态变化
        viewModelScope.launch {
            userDownloadManager.stateFlow.collect { stateChange ->
                handleStateChange(stateChange.url, stateChange.state)
            }
        }

        // 监听进度变化
        viewModelScope.launch {
            userDownloadManager.progressFlow.collect { progressChange ->
                handleProgressChange(progressChange)
            }
        }
    }

    private suspend fun initializeDisplayItems() {
        val initialStates = _downloadableItems.map { item ->
            val downloadedFile = try {
                userDownloadManager.getDownloadFile(item.url, item.fileName)
            } catch (e: Exception) {
                null
            }
            val downloadedSize = try {
                userDownloadManager.getDownloadFileSize(item.url, item.fileName)
            } catch (e: Exception) {
                0L
            }
            // 记录 url 到 key 的映射
            urlToKeys.getOrPut(item.url) { mutableSetOf() }.add(item.key)
            DownloadUiState(
                url = item.url,
                fileName = item.fileName ?: item.url.substringAfterLast('/'),
                status = DownloadStatus.IDLE,
                downloadedSize = downloadedSize,
                fileSize = -1, // 初始未知
                progress = 0,
                filePath = downloadedFile?.absolutePath ?: "",
                key = item.key
            )
        }
        initialStates.forEach { state ->
            _downloadStates[state.key] = state
        }
        updateDisplayItems()
    }

    override fun onCleared() {
        super.onCleared()
    }

    private fun handleStateChange(url: String, status: DownloadStatus) {
        viewModelScope.launch {
            val keys = urlToKeys[url] ?: return@launch
            keys.forEach { key ->
                val currentState = _downloadStates[key] ?: return@forEach
                val updatedState = currentState.copy(status = status)
                _downloadStates[key] = updatedState
            }
            updateDisplayItems()
        }
    }

    private fun handleProgressChange(progressChange: DownloadProgressChange) {
        viewModelScope.launch {
            val keys = urlToKeys[progressChange.url] ?: return@launch
            keys.forEach { key ->
                val currentState = _downloadStates[key] ?: return@forEach
                val updatedState = currentState.copy(
                    progress = progressChange.progress,
                    downloadedSize = progressChange.downloadedSize,
                    fileSize = progressChange.fileSize
                )
                _downloadStates[key] = updatedState
            }
            updateDisplayItems()
        }
    }

    // Helper function to map current data to DownloadUiState and update the StateFlow
    private fun updateDisplayItems() {
        val uiStates = _downloadableItems.map { initialItem ->
            _downloadStates[initialItem.key] ?: DownloadUiState(
                url = initialItem.url,
                fileName = initialItem.fileName ?: initialItem.url.substringAfterLast('/'),
                status = DownloadStatus.IDLE,
                downloadedSize = 0,
                fileSize = -1,
                progress = 0,
                filePath = "",
                key = initialItem.key
            )
        }
        _displayItems.value = uiStates
        logInfo(
            "FileDownloadViewModel", "[updateDisplayItems] Updated UI states. Items: ${
                uiStates.joinToString(", ") { "{${it.fileName}: ${it.status.javaClass.simpleName}, ${it.progress}%}" }
            }")
    }

    fun startDownload(item: DownloadableItem) {
        viewModelScope.launch {
            userDownloadManager.pendingDownload(
                url = item.url,
                fileName = item.fileName,
                serverFileSize = null,
                source = "demo",
            )
        }
    }

    fun resumeDownload(item: DownloadableItem) {
        viewModelScope.launch {
            userDownloadManager.resume(item.url, item.fileName)
        }
    }

    fun pauseDownload(task: DownloadableItem) {
        viewModelScope.launch {
            userDownloadManager.pause(task.url, task.fileName)
        }
    }

    fun cancelDownload(task: DownloadableItem) {
        viewModelScope.launch {
            userDownloadManager.cancel(task.url, task.fileName)
        }
    }

    fun deleteFile(task: DownloadableItem) {
        viewModelScope.launch {
            try {
                userDownloadManager.deleteFile(
                    task.url,
                    task.fileName,
                )
                // 重置所有同 url 的状态
                val keys = urlToKeys[task.url] ?: setOf(task.key)
                keys.forEach { key ->
                    val currentState = _downloadStates[key]
                    if (currentState != null) {
                        _downloadStates[key] = currentState.copy(
                            status = DownloadStatus.IDLE,
                            progress = 0,
                            downloadedSize = 0,
                            filePath = ""
                        )
                    }
                }
                updateDisplayItems()
            } catch (e: Exception) {
                logDebug("FileDownloadViewModel", "Delete file failed: ${e.message}")
            }
        }
    }

    fun cancelAllDownloads() {
        viewModelScope.launch {
            userDownloadManager.cancelAll()
        }
        // 重置所有状态
        _downloadStates.keys.forEach { key ->
            val currentState = _downloadStates[key]
            if (currentState != null) {
                _downloadStates[key] = currentState.copy(
                    status = DownloadStatus.CANCEL,
                    progress = 0
                )
            }
        }
        updateDisplayItems()
    }

    fun shutdownManager() {
        userDownloadManager.shutdown()
        // 清空所有状态
        _downloadStates.clear()
        updateDisplayItems()
    }

    // 根据文件名判断MimeType
    private fun getMimeTypeFromFileName(fileName: String): String {
        return when {
            fileName.endsWith(".pdf", true) -> "application/pdf"
            fileName.endsWith(".jpg", true) || fileName.endsWith(".jpeg", true) -> "image/jpeg"
            fileName.endsWith(".png", true) -> "image/png"
            fileName.endsWith(".mp4", true) -> "video/mp4"
            fileName.endsWith(".txt", true) -> "text/plain"
            fileName.endsWith(".doc", true) -> "application/msword"
            fileName.endsWith(
                ".docx",
                true
            ) -> "application/vnd.openxmlformats-officedocument.wordprocessingml.document"

            fileName.endsWith(".xls", true) -> "application/vnd.ms-excel"
            fileName.endsWith(
                ".xlsx",
                true
            ) -> "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"

            fileName.endsWith(".ppt", true) -> "application/vnd.ms-powerpoint"
            fileName.endsWith(
                ".pptx",
                true
            ) -> "application/vnd.openxmlformats-officedocument.presentationml.presentation"

            else -> "application/octet-stream"
        }
    }


    fun openFileWithSystemApp(context: Context, filePath: String) {
        try {
            val file = File(filePath)
            val uri = FileProvider.getUriForFile(
                context,
                context.applicationContext.packageName,
                file
            )

            // 优先用 contentResolver.getType(uri)
            val mimeType =
                context.contentResolver.getType(uri) ?: getMimeTypeFromFileName(file.name)

            if (mimeType == "application/vnd.android.package-archive") {
                // 处理APK文件安装需要特殊权限
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    if (!context.packageManager.canRequestPackageInstalls()) {
                        // 没有安装未知应用的权限，引导用户去设置
                        val intent = Intent(Settings.ACTION_MANAGE_UNKNOWN_APP_SOURCES).apply {
                            data = ("package:" + context.packageName).toUri()
                        }
                        context.startActivity(intent)
                        return // 退出，等待用户授权后再尝试安装
                    }
                }
                // 有权限或低于Android O，继续安装流程
                val intent = Intent(Intent.ACTION_VIEW).apply {
                    setDataAndType(uri, mimeType)
                    addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                    // 需要添加FLAG_ACTIVITY_NEW_TASK，否则可能报错
                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                }
                context.startActivity(intent)

            } else {
                // 处理其他文件类型
                val intent = Intent(Intent.ACTION_VIEW).apply {
                    setDataAndType(uri, mimeType)
                    addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                }
                context.startActivity(intent)
            }

        } catch (e: ActivityNotFoundException) {
            logDebug("openFileWithSystemApp", "没有可用的应用打开该文件: $filePath")
        } catch (e: Exception) {
            e.printStackTrace()
            logDebug("openFileWithSystemApp", "打开文件出错: ${e.message}")
        }
    }

}