package com.interfun.buz.demo.compose

import android.os.Bundle
import android.widget.TextView
import androidx.activity.compose.setContent
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.foundation.*
import androidx.compose.foundation.gestures.Orientation.Vertical
import androidx.compose.foundation.gestures.scrollable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.text.input.rememberTextFieldState
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.compose.ui.zIndex
import androidx.core.content.ContextCompat
import com.airbnb.lottie.LottieProperty
import com.airbnb.lottie.compose.*
import com.airbnb.lottie.compose.LottieCompositionSpec.Asset
import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.base.ktx.toast
import com.interfun.buz.common.ktx.*
import com.interfun.buz.compose.components.*
import com.interfun.buz.compose.ktx.VerticalSpace
import com.interfun.buz.compose.ktx.asColor
import com.interfun.buz.compose.ktx.rememberMutableBoolean
import com.interfun.buz.compose.modifier.overScrollVertical
import com.interfun.buz.compose.modifier.touchable
import com.interfun.buz.compose.styles.TextStyles
import com.interfun.buz.demo.R
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow

/**
 * <AUTHOR>
 * @date 2024/1/24
 * @desc
 */
class ComposeActivity : AppCompatActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            DemoPage()
        }
    }

    @Composable
    fun DemoPage() {
        val scrollState = rememberScrollState()
        Column(
            modifier = Modifier
                .fillMaxSize()
                .scrollable(rememberScrollState(), Vertical)
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(dimensionResource(id = R.dimen.title_bar_height))
                    .zIndex(1f)
            ) {
                IconFontBack(
                    modifier = Modifier.align(Alignment.CenterStart),
                    onClick = { onBackPressed() }
                )
                PortraitImage(
                    modifier = Modifier
                        .fillMaxHeight()
                        .align(Alignment.Center)
                        .touchable()
                        .background(R.color.black_90.asColor()),
                    urlList = urlList,
                    size = null
                )
                LottieDemo(
                    Modifier
                        .align(Alignment.CenterEnd)
                        .fillMaxHeight()
                        .aspectRatio(1f)
                )
            }
            FPSMonitor(Modifier.height(28.dp))
            // OnAirVolumeAnim()
            // CommonSlider(
            //     modifier = Modifier.fillMaxWidth(),
            //     useLabel = true,
            //     useDragEffect = true,
            // )
            CommonTextFiledDemo()
            VerticalSpace(20.dp)
            Box {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .aspectRatio(1f)
                        .background(R.color.text_white_disable.asColor())
                        .overScrollVertical()
                        .verticalScroll(scrollState)
                ) {
                    TextColumn(Modifier.weight(1f))
                    Spacer(Modifier.width(10.dp))
                    AndroidTextColumn(Modifier.weight(1f))
                }
            }
            Spacer(Modifier.height(10.dp))
            var showSheet by rememberMutableBoolean()
            if (showSheet) {
                BottomSheet {
                    showSheet = false
                }
            }
            CommonButton(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 20.dp),
                type = CommonButtonType.PRIMARY_LARGER,
                text = "Show BottomSheet",
                iconRes = R.string.ic_arrow_down_small
            ) {
                showSheet = true
            }
            Spacer(Modifier.height(10.dp))
            var commonButtonType by remember { mutableStateOf(CommonButtonType.PRIMARY_LARGER) }
            val paddingHorizontal = if (commonButtonType.size == CommonButtonSize.LARGER) {
                20.dp
            } else {
                80.dp
            }
            CommonButton(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = paddingHorizontal),
                type = commonButtonType,
                text = commonButtonType.name
            ) {
                commonButtonType = CommonButtonType.entries.random()
            }
        }
    }

    @Composable
    fun OnAirVolumeAnim(){
        Box(modifier = Modifier.padding(40f.dp)) {
            val volumeFlow = remember {
                MutableSharedFlow<Int>()
            }
            val isSilent = rememberMutableBoolean(false)
            LaunchedEffect(key1 = true) {
                while (true) {
                    val volume = if (isSilent.value) 0 else (0..100).random()
                    volumeFlow.emit(volume)
                    delay(5)
                    // flow {
                    //     emit(1)
                    //     delay(5)
                    //     emit(50)
                    //     delay(5)
                    //     emit(100)
                    //     delay(5)
                    // }.collect {
                    //     volumeFlow.emit(it)
                    // }
                }
            }
            OnAirVolumeBubble(
                color = colorResource(id = R.color.basic_primary),
                modifier = Modifier
                    .size(150f.dp, 150f.dp),
                volumeFlow = volumeFlow
            )
            Image(
                painter = painterResource(id = R.drawable.mingyi),
                contentDescription = "",
                contentScale = ContentScale.Crop,
                modifier = Modifier
                    .size(150f.dp)
                    .clip(CircleShape)
                    .clickable {
                        isSilent.value = !isSilent.value
                    }
            )
        }
    }

    @OptIn(ExperimentalMaterial3Api::class)
    @Composable
    fun BottomSheet(onDismiss: () -> Unit) {
        val modalBottomSheetState = rememberModalBottomSheetState()

        ModalBottomSheet(
            onDismissRequest = { onDismiss.invoke() },
            sheetState = modalBottomSheetState,
            dragHandle = { BottomSheetDefaults.DragHandle() },
        ) {
            CountryList()
        }
    }

    @Composable
    fun CountryList() {
        val countries = listOf(
            Pair("United States", "\uD83C\uDDFA\uD83C\uDDF8"),
            Pair("Canada", "\uD83C\uDDE8\uD83C\uDDE6"),
            Pair("India", "\uD83C\uDDEE\uD83C\uDDF3"),
            Pair("Germany", "\uD83C\uDDE9\uD83C\uDDEA"),
            Pair("France", "\uD83C\uDDEB\uD83C\uDDF7"),
            Pair("Japan", "\uD83C\uDDEF\uD83C\uDDF5"),
            Pair("China", "\uD83C\uDDE8\uD83C\uDDF3"),
            Pair("Brazil", "\uD83C\uDDE7\uD83C\uDDF7"),
            Pair("Australia", "\uD83C\uDDE6\uD83C\uDDFA"),
            Pair("Russia", "\uD83C\uDDF7\uD83C\uDDFA"),
            Pair("United Kingdom", "\uD83C\uDDEC\uD83C\uDDE7"),
        )

        LazyColumn {
            items(countries) { (country, flag) ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 10.dp, horizontal = 20.dp)
                ) {
                    Text(
                        text = flag,
                        modifier = Modifier.padding(end = 20.dp)
                    )
                    Text(text = country)
                }
            }
        }
    }

    @Composable
    fun LottieDemo(modifier: Modifier) {
        val composition by rememberLottieComposition(Asset("lottie/heart.json"))
        val dynamicProperties = rememberLottieDynamicProperties(
            rememberLottieDynamicProperty(
                property = LottieProperty.COLOR,
                value = R.color.basic_primary.asColor().toArgb(),
                keyPath = arrayOf("H2", "Shape 1", "Fill 1")
            ),
        )
        LottieAnimation(
            modifier = modifier,
            composition = composition,
            iterations = LottieConstants.IterateForever,
            dynamicProperties = dynamicProperties,
            // clipSpec = LottieClipSpec.Progress(0.5f, 0.75f)
        )
    }

    @Composable
    fun TextColumn(modifier: Modifier) {
        Column(modifier) {
            Text(TextStyles.titleLarge(), "Compose Text:", hasBorder = false)
            Text(TextStyles.titleLarge(), "TitleLarge\ntitleLarge\n中文\n中文")
            Text(TextStyles.titleMedium(), "TitleMedium\ntitleMedium\n中文\n中文")
            Text(TextStyles.titleSmall(), "TitleSmall\ntitleSmall\n中文\n中文")
            Text(TextStyles.labelLarge(), "LabelLarge\nlabelLarge\n中文\n中文")
            Text(TextStyles.labelMedium(), "LabelMedium\nlabelMedium\n中文\n中文")
            Text(TextStyles.labelSmall(), "LabelSmall\nlabelSmall\n中文\n中文")
            Text(TextStyles.bodyLarge(), "BodyLarge\nbodyLarge\n中文\n中文")
            Text(TextStyles.bodyLargeItalic(), "BodyLargeItalic\nbodyLargeItalic\n中文\n中文")
            Text(TextStyles.bodyMedium(), "BodyMedium\nbodyMedium\n中文\n中文")
            Text(TextStyles.bodySmall(), "BodySmall\nbodySmall\n中文\n中文")
        }
    }

    @Composable
    fun Text(style: TextStyle, t: String = text, hasBorder: Boolean = true) {
        Text(
            modifier = Modifier.takeIf { hasBorder }
                ?.border(1.dp, R.color.text_white_important.asColor()) ?: Modifier,
            text = t,
            style = style,
        )
        Spacer(Modifier.height(10.dp))
    }

    @Composable
    fun AndroidText(t: String = text, hasBorder: Boolean = true, block: (TextView) -> Unit) {
        AndroidView(
            modifier = Modifier.takeIf { hasBorder }
                ?.border(1.dp, R.color.text_white_important.asColor()) ?: Modifier,
            factory = { TextView(it) },
            update = {
                it.text = t
                it.setTextColor(ContextCompat.getColor(appContext, R.color.text_white_important))
                block.invoke(it)
            })
        Spacer(Modifier.height(10.dp))
    }

    @Composable
    fun AndroidTextColumn(modifier: Modifier) {
        Column(modifier) {
            AndroidText("TextView:", hasBorder = false) { it.setStyleTitleLarge() }
            AndroidText("TitleLarge\ntitleLarge\n中文\n中文") { it.setStyleTitleLarge() }
            AndroidText("TitleMedium\ntitleMedium\n中文\n中文") { it.setStyleTitleMedium() }
            AndroidText("TitleSmall\ntitleSmall\n中文\n中文") { it.setStyleTitleSmall() }
            AndroidText("LabelLarge\nlabelLarge\n中文\n中文") { it.setStyleLabelLarge() }
            AndroidText("LabelMedium\nlabelMedium\n中文\n中文") { it.setStyleLabelMedium() }
            AndroidText("LabelSmall\nlabelSmall\n中文\n中文") { it.setStyleLabelSmall() }
            AndroidText("BodyLarge\nbodyLarge\n中文\n中文") { it.setStyleBodyLarge() }
            AndroidText("BodyLargeItalic\nbodyLargeItalic\n中文\n中文") { it.setStyleBodyLargeItalic() }
            AndroidText("BodyMedium\nbodyMedium\n中文\n中文") { it.setStyleBodyMedium() }
            AndroidText("BodySmall\nbodySmall\n中文\n中文") { it.setStyleBodySmall() }
        }
    }

    private val urlList = listOf(
        "https://upload-images.jianshu.io/upload_images/5809200-a99419bb94924e6d.jpg?imageMogr2/auto-orient/strip%7CimageView2/2/w/1240",
        "https://upload-images.jianshu.io/upload_images/5809200-736bc3917fe92142.jpg?imageMogr2/auto-orient/strip%7CimageView2/2/w/1240",
        "https://upload-images.jianshu.io/upload_images/5809200-7fe8c323e533f656.jpg?imageMogr2/auto-orient/strip%7CimageView2/2/w/1240",
        "https://upload-images.jianshu.io/upload_images/5809200-c12521fbde6c705b.jpg?imageMogr2/auto-orient/strip%7CimageView2/2/w/1240",
        "https://upload-images.jianshu.io/upload_images/5809200-caf66b935fd00e18.jpg?imageMogr2/auto-orient/strip%7CimageView2/2/w/1240",
        "https://upload-images.jianshu.io/upload_images/5809200-48dd99da471ffa3f.jpg?imageMogr2/auto-orient/strip%7CimageView2/2/w/1240",
    )

    // private val text = "B@2示!يVíΠακε\n2示يVíΠ!ακεB@"
    private val text = "English Test\nEnglish Test"

    @Composable
    private fun CommonTextFiledDemo() {
        Column(
            Modifier
                .padding(horizontal = 40.dp)
                .fillMaxWidth()
        ) {
            //进入页面的时候自动弹出键盘
            val keyboardController = LocalSoftwareKeyboardController.current//软键盘控制器
            val focusRequester = remember { FocusRequester() }//焦点控制器
            // LaunchedEffect(key1 = Unit, block = {
            //     focusRequester.requestFocus()//使其获取焦点
            //     keyboardController?.show()//弹出软键盘
            // })
            val username = rememberTextFieldState()
            CommonTextField(
                state = username,
                hint = "请输入账号",
                enabledClearText = true,
                maxLength = 5,
                reachMaxLengthCallback = {
                    toast("输入过长")
                }
            )
        }
    }
}