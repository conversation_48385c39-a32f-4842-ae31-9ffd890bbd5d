/* package com.interfun.buz.demo.floatball.manager

import android.view.Gravity
import android.view.MotionEvent
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ProcessLifecycleOwner
import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.base.ktx.dp
import com.interfun.buz.base.ktx.log
import com.interfun.buz.base.ktx.navigationBarHeight
import com.interfun.buz.base.ktx.screenHeight
import com.interfun.buz.base.ktx.screenHeightReal
import com.interfun.buz.base.ktx.screenWidth
import com.interfun.buz.base.ktx.screenWidthReal
import com.interfun.buz.base.ktx.statusBarHeight
import com.interfun.buz.base.manager.ScreenRotateManager
import com.interfun.buz.basefloat.EasyFloat
import com.interfun.buz.basefloat.enums.ShowPattern
import com.interfun.buz.basefloat.enums.SidePattern
import com.interfun.buz.demo.R
import com.interfun.buz.demo.floatball.storage.FloatGuideMMKV
import com.interfun.buz.demo.floatball.view.WTFloatViewGroup

/**
 * <AUTHOR>
 * @date 2023/2/13
 * @desc
 */
object WTFloatTestManager {

    const val TAG = "WTFloat"

    private val floatTag = R.string.common_tag_wt_float_view.asString()
    private val floatView get() = EasyFloat.getFloatView(floatTag) as? WTFloatViewGroup
    private var screenStartX: Int = 0
    private var lastFloatY: Int = 0
    private val screenRotateManager = ScreenRotateManager {
        updateOrientation()
    }

    private val backgroundObserver = object : DefaultLifecycleObserver {
        override fun onCreate(owner: LifecycleOwner) {
            screenRotateManager.registerReceiver()
        }

        override fun onDestroy(owner: LifecycleOwner) {
            screenRotateManager.unRegisterReceiver()
        }
    }

    private var isShow = false
    fun show() {
        if (isShow) {
            ProcessLifecycleOwner.get().lifecycle.removeObserver(backgroundObserver)
            EasyFloat.dismiss(floatTag)
        } else {
            ProcessLifecycleOwner.get().lifecycle.addObserver(backgroundObserver)
            createAndShowTestFloat()
        }
        isShow = !isShow
    }

    private fun createAndShowTestFloat() {
        if (EasyFloat.getFloatView(floatTag) != null && EasyFloat.isShow(floatTag).not()) {
            EasyFloat.show(floatTag)
            return
        }
        val config = EasyFloat.with(appContext)
            .setTag(floatTag)
            .setShowPattern(ShowPattern.ALL_TIME)
            .setSidePattern(SidePattern.RESULT_HORIZONTAL)
            .setImmersionStatusBar(true)
            .setDisableDefaultWindowAnimation(true)
            .setSideWidth(50.dp)
            .setLayout(WTFloatViewGroup(appContext))

        initFloatPosition(config)
        initFloatBorder(config)
        config.registerCallback {
            // 在此处设置view也可以，建议在setLayout进行view操作
            createResult { isCreated, msg, _ ->
                floatView?.animDelegate?.updateInLeft(FloatGuideMMKV.isLastInLeft)
                log(TAG, "createResult isCreated: $isCreated msg: $msg")
            }

            onEnterAnimStart {
                floatView?.animDelegate?.isInitialized = false
            }

            onEnterAnimEnd {
                floatView?.animDelegate?.isInitialized = true
                EasyFloat.updateFloat()
            }

            onExitAnimStart {
                log(TAG, "onExitAnimStart lastY:$lastFloatY")
                floatView?.animDelegate?.isInitialized = false
                if (screenRotateManager.isVertical) {
                    FloatGuideMMKV.lastFloatVerticalY = lastFloatY
                } else {
                    FloatGuideMMKV.lastFloatHorizontalY = lastFloatY
                }
            }

            show { log(TAG, "show") }

            hide { log(TAG, "hide") }

            touchEvent { view, event ->
                event.apply {
                    log(TAG, "touchEvent action:${action} x:$x, y:$y, rawX:$rawX, rawY:$rawY")
                    if (action == MotionEvent.ACTION_OUTSIDE) {
                        floatView?.animDelegate?.minimize()
                    }
                }
            }

            drag { view, motionEvent ->
                // log(TAG, "drag x:${motionEvent.x} rawx:${motionEvent.rawX}")
            }

            updateWindow { windowView, params ->
                log(TAG, "updateWindow x:${params.x}, y:${params.y}")
                lastFloatY = params.y
                floatView?.animDelegate?.updateWindowView(windowView, params)
            }

            dragAnimStart { view, left, top, right, bottom ->
                FloatGuideMMKV.isLastInLeft = left < right
                floatView?.animDelegate?.onDragAnimStart()
            }

            dragAnimEnd {
                log(TAG, "dragEnd")
                floatView?.animDelegate?.onCollapsedStateChange()
            }

            dismiss {

            }
        }
        config.show()
    }

    private fun updateOrientation() {
        log(TAG, "updateOrientation orientation:${screenRotateManager.currentType}")
        initFloatBorder()
        if (screenRotateManager.isVertical) {
            FloatGuideMMKV.lastFloatHorizontalY = lastFloatY
        } else {
            FloatGuideMMKV.lastFloatVerticalY = lastFloatY
        }
        val lastFloatY = if (screenRotateManager.isVertical) {
            FloatGuideMMKV.lastFloatVerticalY
        } else {
            FloatGuideMMKV.lastFloatHorizontalY
        }
        initFloatPosition()
        val x = if (FloatGuideMMKV.isLastInLeft) screenStartX else -1
        val y = if (lastFloatY in -statusBarHeight..screenHeight) {
            lastFloatY
        } else {
            -1
        }
        if (x == -1) {
            EasyFloat.resetDefaultLocation(floatTag)
        }
        EasyFloat.updateFloat(floatTag, x = x, y = y)
    }

    private fun initFloatPosition(config: EasyFloat.Builder? = null){
        val offsetY: Int
        val gravity: Int
        val lastFloatY = if (screenRotateManager.isVertical) {
            FloatGuideMMKV.lastFloatVerticalY
        } else {
            FloatGuideMMKV.lastFloatHorizontalY
        }
        val isLastInLeft = FloatGuideMMKV.isLastInLeft
        val layoutChangeGravity = if (isLastInLeft) Gravity.START else Gravity.END
        val statusBarHeight = statusBarHeight
        if (lastFloatY in -statusBarHeight..screenHeight) {
            offsetY = lastFloatY + statusBarHeight
            gravity = Gravity.TOP or layoutChangeGravity
        } else {
            offsetY = 0
            gravity = Gravity.CENTER_VERTICAL or layoutChangeGravity
        }
        if (config != null) {
            config.setGravity(gravity, offsetY = offsetY)
            config.setLayoutChangedGravity(layoutChangeGravity)
        } else {
            EasyFloat.updateFloatGravity(floatTag, gravity, offsetY = offsetY)
            EasyFloat.setLayoutChangedGravity(floatTag, layoutChangeGravity)
        }
    }

    private fun initFloatBorder(config: EasyFloat.Builder? = null) {
        val w = screenWidth
        val wReal = screenWidthReal
        val h = screenHeight
        val hReal = screenHeightReal
        val topHeight = statusBarHeight
        val bottomHeight = hReal - h - topHeight
        val maxFloatWidth = 246.dp
        val floatPadding = 16.dp

        //在横屏过来后，坐标X的起点可能并不是从屏幕最左边开始的，而是从竖屏状态栏区域以下开始的（见于刘海屏水滴屏等）
        val screenStartX: Int
        val screenEndX: Int
        val horizontalPhysicalStatusBarWidth: Int
        val left: Int
        val right: Int
        val top: Int
        val bottom: Int
        if (w < h) {
            left = -maxFloatWidth
            right = w + maxFloatWidth
            top = topHeight - floatPadding
            bottom = hReal - bottomHeight + floatPadding
            screenStartX = 0
            screenEndX = wReal
        } else {
            val navigationBarWidth = navigationBarHeight
            //在横屏下，导航栏会放到右边，判断是否有导航栏
            val hasNavigationBar = hReal == h + topHeight
            horizontalPhysicalStatusBarWidth = if (hasNavigationBar) {
                wReal - w - navigationBarWidth
            } else {
                wReal - w
            }
            val isNavigationBarInLeft =
                screenRotateManager.currentType == ScreenRotateManager.ORIENTATION_TYPE_90
            screenStartX = if (isNavigationBarInLeft) {
                0
            } else {
                -horizontalPhysicalStatusBarWidth
            }
            screenEndX = if (isNavigationBarInLeft) {
                w + horizontalPhysicalStatusBarWidth
            } else {
                w
            }
            left = if (hasNavigationBar && isNavigationBarInLeft) {
                0
            } else {
                -maxFloatWidth - horizontalPhysicalStatusBarWidth
            }
            right = if (hasNavigationBar && !isNavigationBarInLeft) {
                screenEndX
            } else {
                w + maxFloatWidth
            }
            top = topHeight - floatPadding
            bottom = hReal - bottomHeight + floatPadding
        }
        if (config != null) {
            config.setBorder(left, top, right, bottom)
            config.setHorizontalSide(screenStartX, screenEndX)
        } else {
            EasyFloat.updateFloatBorder(
                floatTag,
                left,
                top,
                right,
                bottom,
                screenStartX,
                screenEndX
            )
        }
        floatView?.animDelegate?.updateOrientation(screenStartX, screenEndX)
        this.screenStartX = screenStartX
    }
}
*/