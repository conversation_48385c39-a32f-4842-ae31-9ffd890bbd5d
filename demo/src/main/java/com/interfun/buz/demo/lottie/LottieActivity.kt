package com.interfun.buz.demo.lottie

import androidx.constraintlayout.widget.ConstraintLayout.LayoutParams
import com.airbnb.lottie.TextDelegate
import com.interfun.buz.base.ktx.click
import com.interfun.buz.base.ktx.screenHeightReal
import com.interfun.buz.base.ktx.screenWidthReal
import com.interfun.buz.base.ktx.statusBarHeight
import com.interfun.buz.common.base.binding.BaseBindingActivity
import com.interfun.buz.demo.databinding.ActivityLottieBinding
import org.libpag.PAGFile

/**
 * <AUTHOR>
 * @date 2024/1/11
 * @desc
 */
class LottieActivity : BaseBindingActivity<ActivityLottieBinding>() {

    private val lottieUrl = "https://assets4.lottiefiles.com/packages/lf20_zyquagfl.json"
    private val lottieUrlPartial = "http://romefs.yfxn.lizhi.fm/public/admin/170537233992938fefe01237a40619c1190e8709c31ed.json"
    private val lottieUrlFull = "http://romefs.yfxn.lizhi.fm/public/admin/1705372431134dd07dc9a344d46fba586062140c945f5.json"
    private val pagPathFull1 = "pag/full_falling.pag"
    private val pagPathFull2 = "pag/full_rising.pag"
    private val pagPathPartial = "pag/half_exploding.pag"

    override fun initView() {
        val lpFull = LayoutParams(LayoutParams.MATCH_PARENT, 0).apply {
            width = LayoutParams.MATCH_PARENT
            height = 0
            dimensionRatio = "1:3"
            startToStart = LayoutParams.PARENT_ID
            endToEnd = LayoutParams.PARENT_ID
            topToTop = LayoutParams.PARENT_ID
            bottomToBottom = LayoutParams.PARENT_ID
        }
        val lpPartial = LayoutParams(LayoutParams.MATCH_PARENT, 0).apply {
            width = LayoutParams.MATCH_PARENT
            height = 0
            dimensionRatio = "1:1"
            verticalBias =
                (statusBarHeight + 0.3f * screenHeightReal - 0.5f * screenWidthReal) / (screenHeightReal - screenWidthReal)
            startToStart = LayoutParams.PARENT_ID
            endToEnd = LayoutParams.PARENT_ID
            topToTop = LayoutParams.PARENT_ID
            bottomToBottom = LayoutParams.PARENT_ID
        }
        binding.btnPlay1.click {
            play(lottieUrlFull, "\uD83D\uDE0A", lpFull)
        }

        binding.btnPlay2.click {
            play(lottieUrlPartial, "\uD83D\uDE0A", lpPartial)
        }

        binding.btnPlay3.click {
            play(pagPathFull2, lp = lpFull)
        }

        binding.btnPlay4.click {
            play(pagPathPartial, lp = lpPartial)
        }
    }

    private fun play(data: String, emojiUnicode: String? = null, lp: LayoutParams? = null) {
        binding.animContainerView.apply {
            stopAnim()
            loadAnim(data, lp) {
                if (emojiUnicode != null) {
                    if (data.endsWith(".json")) {
                        lottieView?.let { lottieView ->
                            val textDelegate = TextDelegate(lottieView)
                            textDelegate.setText("\uD83D\uDC36", "   $emojiUnicode")
                            lottieView.setTextDelegate(textDelegate)
                        }
                    } else {
                        pagView?.let { pagView ->
                            (pagView.composition as? PAGFile)?.let { pagFile ->
                                val textData = pagFile.getTextData(0)
                                textData?.text = emojiUnicode
                                pagFile.replaceText(0, textData)
                            }
                        }
                    }
                }
                playAnim()
            }
        }
    }
}

