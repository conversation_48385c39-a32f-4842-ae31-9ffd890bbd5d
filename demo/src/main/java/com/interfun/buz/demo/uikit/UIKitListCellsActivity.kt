package com.interfun.buz.demo.uikit

import androidx.recyclerview.widget.LinearLayoutManager
import com.drakeet.multitype.MultiTypeAdapter
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.common.base.binding.BaseBindingActivity
import com.interfun.buz.common.databinding.CommonItemListPrimaryActionBinding
import com.interfun.buz.demo.R
import com.interfun.buz.demo.databinding.ActivityUikitListCellsTokenBinding
import com.interfun.buz.demo.uikit.itemview.ContactItemView
import com.interfun.buz.demo.uikit.itemview.ImageItemView
import com.interfun.buz.demo.uikit.itemview.PrimaryActionItemView

class UIKitListCellsActivity: BaseBindingActivity<ActivityUikitListCellsTokenBinding>() {

    private lateinit var mAdapter: MultiTypeAdapter
    private val list = mutableListOf<ItemBean>().apply {
        add(ItemBean(1, "https://cdn.buz-app.com/public/default/3a4dfe6be84b0c433347017c70e30155/dba1c39fcc2774778c27856b3939888f.jpg", "User Name", "Description"))
        add(ItemBean(2, R.string.ic_contact_add.asString(), "Action", "Label"))
        add(ItemBean(5, "https://cdn.buz-app.com/public/default/3a4dfe6be84b0c433347017c70e30155/dba1c39fcc2774778c27856b3939888f.jpg", "Action", "Description"))

    }

    override fun initView() {
        super.initView()
        mAdapter = MultiTypeAdapter(list).apply {
            register(ItemBean::class.java).to(
                ContactItemView(),
                PrimaryActionItemView(),
                ImageItemView()
            ).withKotlinClassLinker { _, item ->
                when(item.type){
                    1 -> ContactItemView::class
                    2 -> PrimaryActionItemView::class
                    5 -> ImageItemView::class
                    //todo table item view
                    else -> ContactItemView::class
                }
            }
        }
        binding.list.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
            adapter = mAdapter
        }
    }

}

data class ItemBean(
    val type: Int,
    val portrait: String,
    val content: String,
    val description: String? = null
)