package com.interfun.buz.demo.timing

import android.annotation.SuppressLint
import android.app.AlarmManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import com.interfun.buz.user.entity.TimeScheduledEvent
import com.interfun.buz.user.repository.AlarmType
import com.interfun.buz.user.repository.TimeTriggerRepository
import com.interfun.buz.user.repository.TimeTriggerType
import com.interfun.buz.user.utils.ExactAlarmPermissionUtil
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import java.util.Calendar

class TimeTriggerRepositoryDemo(
    private val userScope: CoroutineScope,
    private val appContext: Context
) : TimeTriggerRepository {


    private val _timeTriggerShareFlow = MutableSharedFlow<TimeScheduledEvent>()

    override val timeTriggerShareFlow: SharedFlow<TimeScheduledEvent>
        get() = _timeTriggerShareFlow.asSharedFlow()


    @SuppressLint("ScheduleExactAlarm")
    override fun scheduleAlarms(
        hour: Int,
        minute: Int,
        alarmType: AlarmType,
        weekDay: Boolean
    ): TimeTriggerType {
        val hasPermission = ExactAlarmPermissionUtil.hasExactAlarmPermission(appContext)

        val (action, requestCode) = when (alarmType) {
            AlarmType.START -> TimeTriggerRepository.ACTION_ENTER_SCHEDULE_TIME to TimeTriggerRepository.START_CODE
            AlarmType.END -> TimeTriggerRepository.ACTION_EXIT_SCHEDULE_TIME to TimeTriggerRepository.END_CODE
        }
        val triggerTime = calculateTriggerTimeMillis(hour, minute)

        val alarmManager = appContext.getSystemService(Context.ALARM_SERVICE) as AlarmManager

        val pending = createPendingIntent(
            action = action,
            requestCode = requestCode,
            includeUpdateFlag = true
        )

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (hasPermission) {
                alarmManager.setExactAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, triggerTime, pending)
            } else {
                alarmManager.setAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, triggerTime, pending)
            }
        } else {
            if (hasPermission) {
                alarmManager.setExact(AlarmManager.RTC_WAKEUP, triggerTime, pending)
            } else {
                alarmManager.set(AlarmManager.RTC_WAKEUP, triggerTime, pending)
            }
        }

        return if (hasPermission) TimeTriggerType.Exact else TimeTriggerType.NonExact
    }

    override fun cancelScheduleAlarms() {
        val alarmManager = appContext.getSystemService(Context.ALARM_SERVICE) as AlarmManager

        val startPending = createPendingIntent(
            action = TimeTriggerRepository.ACTION_ENTER_SCHEDULE_TIME,
            requestCode = TimeTriggerRepository.START_CODE,
            includeUpdateFlag = false
        )
        val endPending = createPendingIntent(
            action = TimeTriggerRepository.ACTION_EXIT_SCHEDULE_TIME,
            requestCode = TimeTriggerRepository.END_CODE,
            includeUpdateFlag = false
        )

        alarmManager.cancel(startPending)
        alarmManager.cancel(endPending)
    }

    override fun calculateTriggerTimeMillis(hour: Int, minute: Int): Long {
        val now = Calendar.getInstance()
        val trigger = Calendar.getInstance().apply {
            set(Calendar.HOUR_OF_DAY, hour)
            set(Calendar.MINUTE, minute)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
            if (before(now)) {
                // 如果已经过去了，就设为明天
                add(Calendar.DAY_OF_YEAR, 1)
            }
        }
        return trigger.timeInMillis
    }

    private fun createPendingIntent(
        action: String,
        requestCode: Int,
        includeUpdateFlag: Boolean
    ): PendingIntent {
        val intent = Intent(appContext, TimeTriggerReceiverDemo::class.java).apply {
            this.action = action
        }
        val flags = PendingIntent.FLAG_IMMUTABLE or
                if (includeUpdateFlag) PendingIntent.FLAG_UPDATE_CURRENT else 0

        return PendingIntent.getBroadcast(appContext, requestCode, intent, flags)
    }

    override fun notifyTimeTriggerEvent(event: TimeScheduledEvent) {
        userScope.launch {
            _timeTriggerShareFlow.emit(event)
        }
    }



}