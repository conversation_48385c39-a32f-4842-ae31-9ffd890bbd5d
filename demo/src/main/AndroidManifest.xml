<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />

    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.READ_CONTACTS" />
    <uses-permission android:name="android.permission.WRITE_CONTACTS" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
    <application
        android:name=".app.BuzDemoApp"
        android:allowBackup="false"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:requestLegacyExternalStorage="true"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true"
        tools:replace="android:allowBackup">

        <activity android:name="com.interfun.buz.demo.widget.ShowComposeWidgetsActivity"/>
        <activity
            android:name=".timing.TimeTriggerTestActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity android:name=".circleseekbar.CircleSeekBarActivity" />
        <activity android:name=".widget.WidgetTestActivity" />
        <activity android:name=".wtlist.DemoWTListActivity" />
        <activity android:name=".pag.PagActivity" />
        <activity android:name=".lottie.LottieActivity" />
        <activity android:name=".audio.AudioFocusTestActivity" />

        <activity
            android:name=".tekiplayer.TekiPlayerDemoActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar" />

        <service
            android:name=".tekiplayer.TekiPlayerDemoService"
            android:process=":player" />
        <service android:name=".service.TestForegroundService" />
        <!--
            targetSdkVersion超过30后，需要为设置了intent-filter的四大组件添加android:exported
            由于部分依赖三方库中的组件无法修改，如果报错所以需要在下面覆写并添加android:exported。⬇️⬇️
        -->
        <activity
            android:name="com.lightning.appboot.sdk.push.NotifyDispatchActivity"
            android:configChanges="keyboard|keyboardHidden|screenSize|orientation"
            android:exported="true"
            android:launchMode="singleTask"
            android:screenOrientation="portrait">
            <intent-filter tools:ignore="AppLinkUrlError">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>
        </activity>

        <activity
            android:name=".palette.PaletteColorActivity"
            android:theme="@style/AppNoTitleTheme.transparent" />
       <!-- <activity android:name=".audio.AudioFocusTestActivity"/>
        <activity android:name=".dbtest.GroupBotInfoDaoTestActivity" />
        <activity android:name=".compose.ComposeActivity"
            android:exported="true">
            &lt;!&ndash;<intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>&ndash;&gt;
        </activity>-->

       <!-- <activity android:name=".palette.PaletteColorActivity"
            android:theme="@style/AppNoTitleTheme.transparent"/>-->

        <activity android:name=".compose.ComposeActivity" android:exported="false" />
        <activity android:name=".uikit.UIKitHomeActivity" android:exported="false" />

        <activity android:name=".uikit.UIKitFontTokenActivity" android:exported="false" />

        <activity android:name=".uikit.UIKitButtonActivity" android:exported="false" />

        <activity android:name=".uikit.UIKitRoundButtonActivity" android:exported="false" />

        <activity android:name=".uikit.UIKitTextButtonActivity" android:exported="false" />

        <activity android:name=".uikit.UIKitListCellsActivity" android:exported="false" />

        <activity android:name=".uikit.UIKitTableCellsActivity" android:exported="false" />

        <activity android:name="com.interfun.buz.demo.widget.ToolTipsTestActivity" />

        <activity android:name=".videocall.FlexBoxLayoutDemoActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"/>
        <activity
            android:name=".videocall.PipDemoActivity"
            android:theme="@style/RouterHostTheme"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:supportsPictureInPicture="true" />

        <provider
            android:name=".provider.DemoProvider"
            android:authorities="com.interfun.buz.demo"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <receiver android:name=".timing.TimeTriggerReceiverDemo"
            android:exported="true"/>
    </application>

</manifest>