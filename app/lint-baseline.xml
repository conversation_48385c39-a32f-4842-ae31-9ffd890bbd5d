<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.8.2" type="baseline" client="gradle" dependencies="true" name="AGP (8.8.2)" variant="all" version="8.8.2">

    <issue
        id="NewApi"
        message="Call requires API level 23 (current min is 21): `cancelAll`"
        errorLine1="                NotificationUtil.cancelAll(appContext)"
        errorLine2="                                 ~~~~~~~~~">
        <location
            file="src/main/java/com/interfun/buz/startup/task/main/BaseInitTask.kt"
            line="52"
            column="34"/>
    </issue>

    <issue
        id="NewApi"
        message="Call requires API level 35 (current min is 21): `java.util.List#removeLast` (Prior to API level 35, this call would resolve to a Kotlin stdlib extension function. You can use `remove(`*index*`)` instead.)"
        errorLine1="            mediaList.removeLast()"
        errorLine2="                      ~~~~~~~~~~">
        <location
            file="src/main/java/com/interfun/buz/album/ui/block/PreviewListBlock.kt"
            line="432"
            column="23"/>
    </issue>

    <issue
        id="RtlCompat"
        message="Inconsistent alignment specification between `textAlignment` and `gravity` attributes: was `center_vertical|left`, expected `start`"
        errorLine1="        android:textAlignment=&quot;viewStart&quot;"
        errorLine2="                               ~~~~~~~~~">
        <location
            file="src/main/res/layout/login_fragment_account_input.xml"
            line="210"
            column="32"/>
        <location
            file="src/main/res/layout/login_fragment_account_input.xml"
            line="206"
            column="26"
            message="Incompatible direction here"/>
    </issue>

</issues>
