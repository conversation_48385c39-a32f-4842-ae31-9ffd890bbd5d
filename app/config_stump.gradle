apply plugin: 'stump.v8'//Stump 插件:TEKI-APM配套插件，与TEKI-APM配合提供堆栈记录功能和MAPPING上传功能；

stump {
    appId '87075309' // 闪电上面的 appid，必须！！
    //ignoreRulesFile "ignore.txt"  // 全局黑名单，选填
    globalWhiteIgnoreRules 'whiteListIgnore.txt'
}
if (USE_DETECT_MONITOR_FOR_TEST.toBoolean()) {
    apply plugin: 'bitmapMonitor'
    apply plugin: 'viewMonitor'
    bitmapMonitor {
        setEnableMonitor(OPEN_BITMAP_CHECK.toBoolean())
    }
    viewMonitor {
        setEnableMonitor(OPEN_BITMAP_CHECK.toBoolean())
    }
}

if (QA_JENKINS_BUILD.toBoolean()) {
    apply plugin: 'teki.apm'

// 这里描述暂不准确，其实只代表卡顿的插桩
    tekiApm {
        // 卡顿插桩的黑名单规则
        apmIgnoreRules 'apm_block_ignore.txt'
    }
    tekiCobraApm {
        // 是否启用点击事件监控
        // FIXME: TekiAPM 现在的点击事件插桩暂时不支持 Kotlin 1.6，目前怀疑跟SAM有关系，还在优化，先关闭点击事件插桩
        enableClickEvent false
        // 是否启用 Fragment 启动监控
        enableFragmentTrack true
        // 点击事件和Fragment插桩忽略规则
        ignoreRules 'apm_cobra_ignore.txt'
    }

    tekiWebApm {
        // 是否启用 http 监控
        enableHttp true
        // 是否启用 webview 监控
        enableWebView true
        // Web监控忽略规则
        // webIgnoreRules 'webRules.txt'
    }
}



