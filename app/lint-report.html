<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<title>Lint Report</title>
<link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
 <link rel="stylesheet" href="https://code.getmdl.io/1.2.1/material.blue-indigo.min.css" />
<link rel="stylesheet" href="http://fonts.googleapis.com/css?family=Roboto:300,400,500,700" type="text/css">
<script defer src="https://code.getmdl.io/1.2.0/material.min.js"></script>
<style>
section.section--center {
    max-width: 860px;
}
.mdl-card__supporting-text + .mdl-card__actions {
    border-top: 1px solid rgba(0, 0, 0, 0.12);
}
main > .mdl-layout__tab-panel {
  padding: 8px;
  padding-top: 48px;
}

.mdl-card__actions {
    margin: 0;
    padding: 4px 40px;
    color: inherit;
}
.mdl-card > * {
    height: auto;
}
.mdl-card__actions a {
    color: #00BCD4;
    margin: 0;
}
.error-icon {
    color: #bb7777;
    vertical-align: bottom;
}
.warning-icon {
    vertical-align: bottom;
}
.mdl-layout__content section:not(:last-of-type) {
  position: relative;
  margin-bottom: 48px;
}

.mdl-card .mdl-card__supporting-text {
  margin: 40px;
  -webkit-flex-grow: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
  padding: 0;
  color: inherit;
  width: calc(100% - 80px);
}
div.mdl-layout__drawer-button .material-icons {
    line-height: 48px;
}
.mdl-card .mdl-card__supporting-text {
    margin-top: 0px;
}
.chips {
    float: right;
    vertical-align: middle;
}

pre.errorlines {
    background-color: white;
    font-family: monospace;
    border: 1px solid #e0e0e0;
    line-height: 0.9rem;
    font-size: 0.9rem;    padding: 1px 0px 1px; 1px;
    overflow: scroll;
}
.prefix {
    color: #660e7a;
    font-weight: bold;
}
.attribute {
    color: #0000ff;
    font-weight: bold;
}
.value {
    color: #008000;
    font-weight: bold;
}
.tag {
    color: #000080;
    font-weight: bold;
}
.comment {
    color: #808080;
    font-style: italic;
}
.javadoc {
    color: #808080;
    font-style: italic;
}
.annotation {
    color: #808000;
}
.string {
    color: #008000;
    font-weight: bold;
}
.number {
    color: #0000ff;
}
.keyword {
    color: #000080;
    font-weight: bold;
}
.caretline {
    background-color: #fffae3;
}
.lineno {
    color: #999999;
    background-color: #f0f0f0;
}
.error {
    display: inline-block;
    position:relative;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAECAYAAACp8Z5+AAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH4AwCFR4T/3uLMgAAADxJREFUCNdNyLERQEAABMCjL4lQwIzcjErpguAL+C9AvgKJDbeD/PRpLdm35Hm+MU+cB+tCKaJW4L4YBy+CAiLJrFs9mgAAAABJRU5ErkJggg==) bottom repeat-x;
}
.warning {
    text-decoration: none;
    background-color: #f6ebbc;
}
.overview {
    padding: 10pt;
    width: 100%;
    overflow: auto;
    border-collapse:collapse;
}
.overview tr {
    border-bottom: solid 1px #eeeeee;
}
.categoryColumn a {
     text-decoration: none;
     color: inherit;
}
.countColumn {
    text-align: right;
    padding-right: 20px;
    width: 50px;
}
.issueColumn {
   padding-left: 16px;
}
.categoryColumn {
   position: relative;
   left: -50px;
   padding-top: 20px;
   padding-bottom: 5px;
}
.options {
   padding-left: 16px;
}
</style>
<script language="javascript" type="text/javascript">
<!--
function reveal(id) {
if (document.getElementById) {
document.getElementById(id).style.display = 'block';
document.getElementById(id+'Link').style.display = 'none';
}
}
function hideid(id) {
if (document.getElementById) {
document.getElementById(id).style.display = 'none';
}
}
//-->
</script>
</head>
<body class="mdl-color--grey-100 mdl-color-text--grey-700 mdl-base">
<div class="mdl-layout mdl-js-layout mdl-layout--fixed-header">
  <header class="mdl-layout__header">
    <div class="mdl-layout__header-row">
      <span class="mdl-layout-title">Lint Report: 1 error and 40 warnings</span>
      <div class="mdl-layout-spacer"></div>
      <nav class="mdl-navigation mdl-layout--large-screen-only">Check performed at Tue Jul 15 14:36:13 MYT 2025 by AGP (8.8.2)</nav>
    </div>
  </header>
  <div class="mdl-layout__drawer">
    <span class="mdl-layout-title">Issue Types</span>
    <nav class="mdl-navigation">
      <a class="mdl-navigation__link" href="#overview"><i class="material-icons">dashboard</i>Overview</a>
      <a class="mdl-navigation__link" href="#UnknownIssueId"><i class="material-icons warning-icon">warning</i>Unknown Lint Issue Id (4)</a>
      <a class="mdl-navigation__link" href="#InlinedApi"><i class="material-icons warning-icon">warning</i>Using inlined constants on older versions (6)</a>
      <a class="mdl-navigation__link" href="#RtlCompat"><i class="material-icons error-icon">error</i>Right-to-left text compatibility issues (1)</a>
      <a class="mdl-navigation__link" href="#RtlHardcoded"><i class="material-icons warning-icon">warning</i>Using left/right instead of start/end attributes (22)</a>
      <a class="mdl-navigation__link" href="#RtlEnabled"><i class="material-icons warning-icon">warning</i>Using RTL attributes without enabling RTL support (8)</a>
    </nav>
  </div>
  <main class="mdl-layout__content">
    <div class="mdl-layout__tab-panel is-active">
<a name="overview"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="OverviewCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Overview</h2>
  </div>
              <div class="mdl-card__supporting-text">
<table class="overview">
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Lint">Lint</a>
</td></tr>
<tr>
<td class="countColumn">4</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#UnknownIssueId">UnknownIssueId</a>: Unknown Lint Issue Id</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Correctness">Correctness</a>
</td></tr>
<tr>
<td class="countColumn">6</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#InlinedApi">InlinedApi</a>: Using inlined constants on older versions</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Internationalization:Bidirectional Text">Internationalization:Bidirectional Text</a>
</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons error-icon">error</i>
<a href="#RtlCompat">RtlCompat</a>: Right-to-left text compatibility issues</td></tr>
<tr>
<td class="countColumn">22</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#RtlHardcoded">RtlHardcoded</a>: Using left/right instead of start/end attributes</td></tr>
<tr>
<td class="countColumn">8</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#RtlEnabled">RtlEnabled</a>: Using RTL attributes without enabling RTL support</td></tr>
</table>
<br/>              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="OverviewCardLink" onclick="hideid('OverviewCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Lint"></a>
<a name="UnknownIssueId"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="UnknownIssueIdCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Unknown Lint Issue Id</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../build.gradle">../../build.gradle</a></span>: <span class="message">Unknown issue id "LZLint_HashMapForJDK7"</span><br />
<span class="location"><a href="../../build.gradle">../../build.gradle</a></span>: <span class="message">Unknown issue id "LZLint_ImageFileSizeInvalid"</span><br />
<span class="location"><a href="../../build.gradle">../../build.gradle</a></span>: <span class="message">Unknown issue id "LZLint_MessageObtainUseError"</span><br />
<span class="location"><a href="../../build.gradle">../../build.gradle</a></span>: <span class="message">Unknown issue id "LZLint_ParseHandleID"</span><br />
</div>
<div class="metadata"><div class="explanation" id="explanationUnknownIssueId" style="display: none;">
Lint will report this issue if it is configured with an issue id it does not recognize in for example Gradle files or <code>lint.xml</code> configuration files.<br/>To suppress this error, use the issue id "UnknownIssueId" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">UnknownIssueId</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 1/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationUnknownIssueIdLink" onclick="reveal('explanationUnknownIssueId');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="UnknownIssueIdCardLink" onclick="hideid('UnknownIssueIdCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Correctness"></a>
<a name="InlinedApi"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="InlinedApiCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Using inlined constants on older versions</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../../media/src/main/java/com/interfun/buz/media/video/compressor/encoder/DefaultEncoderFactory.java">../../../media/src/main/java/com/interfun/buz/media/video/compressor/encoder/DefaultEncoderFactory.java</a>:293</span>: <span class="message">Field requires API level 33 (current min is 31): <code>android.media.MediaCodecInfo.CodecCapabilities#COLOR_Format32bitABGR2101010</code></span><br /><pre class="errorlines">
<span class="lineno"> 290 </span>          .contains(MediaCodecInfo.CodecCapabilities.COLOR_Format32bitABGR2101010)) {
<span class="lineno"> 291 </span>      mediaFormat.setInteger(
<span class="lineno"> 292 </span>              MediaFormat.KEY_COLOR_FORMAT,
<span class="caretline"><span class="lineno"> 293 </span>              <span class="warning">MediaCodecInfo.CodecCapabilities.COLOR_Format32bitABGR2101010</span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 294 </span>  } <span class="keyword">else</span> {
<span class="lineno"> 295 </span>      <span class="keyword">throw</span> createExportException(
<span class="lineno"> 296 </span>              format, <span class="comment">/* errorString= */</span> <span class="string">"Encoding HDR is not supported on this device."</span>);
</pre>

<span class="location"><a href="../../../base/src/main/java/com/interfun/buz/base/manager/FrameMetricsListener.kt">../../../base/src/main/java/com/interfun/buz/base/manager/FrameMetricsListener.kt</a>:65</span>: <span class="message">Field requires API level 31 (current min is 24): <code>android.view.FrameMetrics#GPU_DURATION</code></span><br /><pre class="errorlines">
<span class="lineno">  62 </span>           <span class="keyword">val</span> drawDurationMs =
<span class="lineno">  63 </span>               (<span class="number">0.000001</span> * f.getMetric(FrameMetrics.DRAW_DURATION)).toFloat()
<span class="lineno">  64 </span>           <span class="keyword">val</span> gpu =
<span class="caretline"><span class="lineno">  65 </span>               (<span class="number">0.000001</span> * f.getMetric(<span class="warning">FrameMetrics.GPU_DURATION</span>)).toFloat()&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  66 </span>           <span class="keyword">val</span> commandIssue =
<span class="lineno">  67 </span>               (<span class="number">0.000001</span> * f.getMetric(FrameMetrics.COMMAND_ISSUE_DURATION)).toFloat()
<span class="lineno">  68 </span>           <span class="keyword">val</span> input =
</pre>

<span class="location"><a href="../../../base/src/main/java/com/interfun/buz/base/ktx/Network.kt">../../../base/src/main/java/com/interfun/buz/base/ktx/Network.kt</a>:243</span>: <span class="message">Field requires API level 23 (current min is 21): <code>android.net.NetworkCapabilities#NET_CAPABILITY_VALIDATED</code></span><br /><pre class="errorlines">
<span class="lineno"> 240 </span>  <span class="keyword">val</span> isCellular = networkCapabilities.hasTransport(TRANSPORT_CELLULAR)
<span class="lineno"> 241 </span>  <span class="keyword">val</span> connected =
<span class="lineno"> 242 </span>      networkCapabilities.hasCapability(NET_CAPABILITY_INTERNET) &amp;&amp; networkCapabilities.hasCapability(
<span class="caretline"><span class="lineno"> 243 </span>          <span class="warning">NET_CAPABILITY_VALIDATED</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 244 </span>      )
<span class="lineno"> 245 </span>  <span class="keyword">val</span> currentTransportType = <span class="keyword">when</span> {
<span class="lineno"> 246 </span>      isWifi -> TRANSPORT_WIFI
</pre>

<span class="location"><a href="../../../common/src/main/java/com/interfun/buz/common/utils/NotificationChannelUtils.kt">../../../common/src/main/java/com/interfun/buz/common/utils/NotificationChannelUtils.kt</a>:271</span>: <span class="message">Field requires API level 24 (current min is 21): <code>android.app.NotificationManager#IMPORTANCE_HIGH</code></span><br /><pre class="errorlines">
<span class="lineno"> 268 </span>        <span class="keyword">return</span> getNotificationChannel(
<span class="lineno"> 269 </span>            CommonConstant.NOTIFICATION_CHANNEL_ID_LIVE_PLACE_MSG,
<span class="lineno"> 270 </span>            R.string.lp_notification_channel_name.asString(),
<span class="caretline"><span class="lineno"> 271 </span>            <span class="warning">NotificationManager.IMPORTANCE_HIGH</span>,&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 272 </span>            getNormalNotificationSoundUri(),
<span class="lineno"> 273 </span>            showBadge = <span class="keyword">false</span>,
<span class="lineno"> 274 </span>            lockscreenVisibility = Notification.VISIBILITY_PUBLIC
</pre>

<span class="location"><a href="../../../common/src/main/java/com/interfun/buz/common/utils/NotificationChannelUtils.kt">../../../common/src/main/java/com/interfun/buz/common/utils/NotificationChannelUtils.kt</a>:283</span>: <span class="message">Field requires API level 24 (current min is 21): <code>android.app.NotificationManager#IMPORTANCE_HIGH</code></span><br /><pre class="errorlines">
<span class="lineno"> 280 </span>        <span class="keyword">val</span> channel = getNotificationChannel(
<span class="lineno"> 281 </span>            CommonConstant.NOTIFICATION_CHANNEL_ID_LIVE_PLACE_OPEN,
<span class="lineno"> 282 </span>            R.string.liveplace_notification_channel_name_open.asString(),
<span class="caretline"><span class="lineno"> 283 </span>            <span class="warning">NotificationManager.IMPORTANCE_HIGH</span>,&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 284 </span>            lpSoundUri,
<span class="lineno"> 285 </span>            showBadge = <span class="keyword">false</span>,
<span class="lineno"> 286 </span>            lockscreenVisibility = Notification.VISIBILITY_PUBLIC
</pre>

<span class="location"><a href="../../../sharedmedia/src/main/java/com/interfun/buz/shared/SharedBroadcastReceiver.kt">../../../sharedmedia/src/main/java/com/interfun/buz/shared/SharedBroadcastReceiver.kt</a>:18</span>: <span class="message">Field requires API level 22 (current min is 21): <code>android.content.Intent#EXTRA_CHOSEN_COMPONENT</code></span><br /><pre class="errorlines">
<span class="lineno"> 15 </span>  <span class="keyword">val</span> TAG = <span class="string">"SharedBroadcastReceiver"</span>
<span class="lineno"> 16 </span>  override <span class="keyword">fun</span> onReceive(context: Context?, intent: Intent?) {
<span class="lineno"> 17 </span>      <span class="keyword">val</span> componentName =
<span class="caretline"><span class="lineno"> 18 </span>          intent?.getParcelableExtra&lt;ComponentName>(<span class="warning">android.content.Intent.EXTRA_CHOSEN_COMPONENT</span>)</span>
<span class="lineno"> 19 </span>      logInfo(TAG, <span class="string">"onReceive app >> ${</span>componentName?.packageName<span class="string">}"</span>)
<span class="lineno"> 20 </span>      SharedTracker.resultBackRB2024062503(componentName?.packageName,intent?.getStringExtra(KEY_SHARED_TYPE)?:<span class="string">""</span>)
<span class="lineno"> 21 </span>  }
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationInlinedApi" style="display: none;">
This check scans through all the Android API field references in the application and flags certain constants, such as static final integers and Strings, which were introduced in later versions. These will actually be copied into the class files rather than being referenced, which means that the value is available even when running on older devices. In some cases that's fine, and in other cases it can result in a runtime crash or incorrect behavior. It depends on the context, so consider the code carefully and decide whether it's safe and can be suppressed or whether the code needs to be guarded.<br/>
<br/>
If you really want to use this API and don't need to support older devices just set the <code>minSdkVersion</code> in your <code>build.gradle</code> or <code>AndroidManifest.xml</code> files.<br/>
<br/>
If your code is <b>deliberately</b> accessing newer APIs, and you have ensured (e.g. with conditional execution) that this code will only ever be called on a supported platform, then you can annotate your class or method with the <code>@TargetApi</code> annotation specifying the local minimum SDK to apply, such as <code>@TargetApi(11)</code>, such that this check considers 11 rather than your manifest file's minimum SDK as the required API level.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "InlinedApi" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">InlinedApi</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationInlinedApiLink" onclick="reveal('explanationInlinedApi');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="InlinedApiCardLink" onclick="hideid('InlinedApiCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Internationalization:Bidirectional Text"></a>
<a name="RtlCompat"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="RtlCompatCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Right-to-left text compatibility issues</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../../login/src/main/res/layout/login_fragment_account_input.xml">../../../login/src/main/res/layout/login_fragment_account_input.xml</a>:210</span>: <span class="message">Inconsistent alignment specification between <code>textAlignment</code> and <code>gravity</code> attributes: was <code>center_vertical|left</code>, expected <code>start</code></span><br /><pre class="errorlines">
<span class="lineno"> 207 </span>        <span class="prefix">android:</span><span class="attribute">imeOptions</span>=<span class="value">"actionDone"</span>
<span class="lineno"> 208 </span>        <span class="prefix">android:</span><span class="attribute">layoutDirection</span>=<span class="value">"ltr"</span>
<span class="lineno"> 209 </span>        <span class="prefix">android:</span><span class="attribute">paddingEnd</span>=<span class="value">"10dp"</span>
<span class="caretline"><span class="lineno"> 210 </span>        <span class="prefix">android:</span><span class="attribute">textAlignment</span>=<span class="value">"</span><span class="error"><span class="value">viewStart</span></span><span class="value">"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 211 </span>        <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/text_white_main"</span>
<span class="lineno"> 212 </span>        <span class="prefix">android:</span><span class="attribute">textColorHint</span>=<span class="value">"@color/text_white_disable"</span>
<span class="lineno"> 213 </span>        <span class="prefix">android:</span><span class="attribute">textCursorDrawable</span>=<span class="value">"@drawable/common_edittext_cursor"</span></pre>

<ul><span class="location"><a href="../../../login/src/main/res/layout/login_fragment_account_input.xml">../../../login/src/main/res/layout/login_fragment_account_input.xml</a>:206</span>: <span class="message">Incompatible direction here</span><br /><pre class="errorlines">
<span class="lineno"> 203 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"0dp"</span>
<span class="lineno"> 204 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginEnd</span>=<span class="value">"40dp"</span>
<span class="lineno"> 205 </span>        <span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@color/transparent"</span>
<span class="caretline"><span class="lineno"> 206 </span>        <span class="prefix">android:</span><span class="attribute">gravity</span>=<span class="value">"</span><span class="error"><span class="value">center_vertical|left</span></span><span class="value">"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 207 </span>        <span class="prefix">android:</span><span class="attribute">imeOptions</span>=<span class="value">"actionDone"</span>
<span class="lineno"> 208 </span>        <span class="prefix">android:</span><span class="attribute">layoutDirection</span>=<span class="value">"ltr"</span>
<span class="lineno"> 209 </span>        <span class="prefix">android:</span><span class="attribute">paddingEnd</span>=<span class="value">"10dp"</span></pre>
</ul></div>
<div class="metadata"><div class="explanation" id="explanationRtlCompat" style="display: none;">
API 17 adds a <code>textAlignment</code> attribute to specify text alignment. However, if you are supporting older versions than API 17, you must <b>also</b> specify a gravity or layout_gravity attribute, since older platforms will ignore the <code>textAlignment</code> attribute.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "RtlCompat" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">RtlCompat</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Bidirectional Text</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Internationalization</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Error</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationRtlCompatLink" onclick="reveal('explanationRtlCompat');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="RtlCompatCardLink" onclick="hideid('RtlCompatCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="RtlHardcoded"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="RtlHardcodedCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Using left/right instead of start/end attributes</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../../chat/src/main/java/com/interfun/buz/chat/common/view/widget/GettingStartedGuideView.kt">../../../chat/src/main/java/com/interfun/buz/chat/common/view/widget/GettingStartedGuideView.kt</a>:61</span>: <span class="message">Use "<code>Gravity.START</code>" instead of "<code>Gravity.LEFT</code>" to ensure correct behavior in right-to-left locales</span><br /><pre class="errorlines">
<span class="lineno">  58 </span>        ConstraintSet().apply {
<span class="lineno">  59 </span>            clone(<span class="keyword">this</span><span class="annotation">@GettingStartedGuideView</span>)
<span class="lineno">  60 </span>            <span class="keyword">when</span> (pointGravity) {
<span class="caretline"><span class="lineno">  61 </span>                Gravity.START, Gravity.<span class="warning">LEFT</span> -> {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  62 </span>                    connect(
<span class="lineno">  63 </span>                        pointView.id,
<span class="lineno">  64 </span>                        ConstraintSet.START,
</pre>

<span class="location"><a href="../../../chat/src/main/java/com/interfun/buz/chat/common/view/widget/GettingStartedGuideView.kt">../../../chat/src/main/java/com/interfun/buz/chat/common/view/widget/GettingStartedGuideView.kt</a>:86</span>: <span class="message">Use "<code>Gravity.END</code>" instead of "<code>Gravity.RIGHT</code>" to ensure correct behavior in right-to-left locales</span><br /><pre class="errorlines">
<span class="lineno">  83 </span>                    )
<span class="lineno">  84 </span>                }
<span class="lineno">  85 </span>
<span class="caretline"><span class="lineno">  86 </span>                Gravity.END, Gravity.<span class="warning">RIGHT</span> -> {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  87 </span>                    connect(
<span class="lineno">  88 </span>                        pointView.id,
<span class="lineno">  89 </span>                        ConstraintSet.END,
</pre>

<span class="location"><a href="../../../common/src/main/java/com/interfun/buz/common/transition/SidePropagation.java">../../../common/src/main/java/com/interfun/buz/common/transition/SidePropagation.java</a>:127</span>: <span class="message">Use "<code>Gravity.END</code>" instead of "<code>Gravity.RIGHT</code>" to ensure correct behavior in right-to-left locales</span><br /><pre class="errorlines">
<span class="lineno"> 124 </span>        <span class="keyword">if</span> (mSide == Gravity.START) {
<span class="lineno"> 125 </span>            <span class="keyword">final</span> <span class="keyword">boolean</span> isRtl = ViewCompat.getLayoutDirection(sceneRoot)
<span class="lineno"> 126 </span>                    == ViewCompat.LAYOUT_DIRECTION_RTL;
<span class="caretline"><span class="lineno"> 127 </span>            side = isRtl ? Gravity.<span class="warning">RIGHT</span> : Gravity.LEFT;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 128 </span>        } <span class="keyword">else</span> <span class="keyword">if</span> (mSide == Gravity.END) {
<span class="lineno"> 129 </span>            <span class="keyword">final</span> <span class="keyword">boolean</span> isRtl = ViewCompat.getLayoutDirection(sceneRoot)
<span class="lineno"> 130 </span>                    == ViewCompat.LAYOUT_DIRECTION_RTL;
</pre>

<span class="location"><a href="../../../common/src/main/java/com/interfun/buz/common/transition/SidePropagation.java">../../../common/src/main/java/com/interfun/buz/common/transition/SidePropagation.java</a>:127</span>: <span class="message">Use "<code>Gravity.START</code>" instead of "<code>Gravity.LEFT</code>" to ensure correct behavior in right-to-left locales</span><br /><pre class="errorlines">
<span class="lineno"> 124 </span>        <span class="keyword">if</span> (mSide == Gravity.START) {
<span class="lineno"> 125 </span>            <span class="keyword">final</span> <span class="keyword">boolean</span> isRtl = ViewCompat.getLayoutDirection(sceneRoot)
<span class="lineno"> 126 </span>                    == ViewCompat.LAYOUT_DIRECTION_RTL;
<span class="caretline"><span class="lineno"> 127 </span>            side = isRtl ? Gravity.RIGHT : Gravity.<span class="warning">LEFT</span>;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 128 </span>        } <span class="keyword">else</span> <span class="keyword">if</span> (mSide == Gravity.END) {
<span class="lineno"> 129 </span>            <span class="keyword">final</span> <span class="keyword">boolean</span> isRtl = ViewCompat.getLayoutDirection(sceneRoot)
<span class="lineno"> 130 </span>                    == ViewCompat.LAYOUT_DIRECTION_RTL;
</pre>

<span class="location"><a href="../../../common/src/main/java/com/interfun/buz/common/transition/SidePropagation.java">../../../common/src/main/java/com/interfun/buz/common/transition/SidePropagation.java</a>:131</span>: <span class="message">Use "<code>Gravity.END</code>" instead of "<code>Gravity.RIGHT</code>" to ensure correct behavior in right-to-left locales</span><br /><pre class="errorlines">
<span class="lineno"> 128 </span>        } <span class="keyword">else</span> <span class="keyword">if</span> (mSide == Gravity.END) {
<span class="lineno"> 129 </span>            <span class="keyword">final</span> <span class="keyword">boolean</span> isRtl = ViewCompat.getLayoutDirection(sceneRoot)
<span class="lineno"> 130 </span>                    == ViewCompat.LAYOUT_DIRECTION_RTL;
<span class="caretline"><span class="lineno"> 131 </span>            side = isRtl ? Gravity.LEFT : Gravity.<span class="warning">RIGHT</span>;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 132 </span>        } <span class="keyword">else</span> {
<span class="lineno"> 133 </span>            side = mSide;
<span class="lineno"> 134 </span>        }
</pre>

<button class="mdl-button mdl-js-button mdl-button--primary" id="RtlHardcodedDivLink" onclick="reveal('RtlHardcodedDiv');" />+ 17 More Occurrences...</button>
<div id="RtlHardcodedDiv" style="display: none">
<span class="location"><a href="../../../common/src/main/java/com/interfun/buz/common/transition/SidePropagation.java">../../../common/src/main/java/com/interfun/buz/common/transition/SidePropagation.java</a>:131</span>: <span class="message">Use "<code>Gravity.START</code>" instead of "<code>Gravity.LEFT</code>" to ensure correct behavior in right-to-left locales</span><br /><pre class="errorlines">
<span class="lineno"> 128 </span>        } <span class="keyword">else</span> <span class="keyword">if</span> (mSide == Gravity.END) {
<span class="lineno"> 129 </span>            <span class="keyword">final</span> <span class="keyword">boolean</span> isRtl = ViewCompat.getLayoutDirection(sceneRoot)
<span class="lineno"> 130 </span>                    == ViewCompat.LAYOUT_DIRECTION_RTL;
<span class="caretline"><span class="lineno"> 131 </span>            side = isRtl ? Gravity.<span class="warning">LEFT</span> : Gravity.RIGHT;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 132 </span>        } <span class="keyword">else</span> {
<span class="lineno"> 133 </span>            side = mSide;
<span class="lineno"> 134 </span>        }
</pre>

<span class="location"><a href="../../../common/src/main/java/com/interfun/buz/common/transition/SidePropagation.java">../../../common/src/main/java/com/interfun/buz/common/transition/SidePropagation.java</a>:137</span>: <span class="message">Use "<code>Gravity.START</code>" instead of "<code>Gravity.LEFT</code>" to ensure correct behavior in right-to-left locales</span><br /><pre class="errorlines">
<span class="lineno"> 134 </span>        }
<span class="lineno"> 135 </span>        <span class="keyword">int</span> distance = <span class="number">0</span>;
<span class="lineno"> 136 </span>        <span class="keyword">switch</span> (side) {
<span class="caretline"><span class="lineno"> 137 </span>            <span class="keyword">case</span> Gravity.<span class="warning">LEFT</span>:&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 138 </span>                distance = right - viewX + Math.abs(epicenterY - viewY);
<span class="lineno"> 139 </span>                <span class="keyword">break</span>;
<span class="lineno"> 140 </span>            <span class="keyword">case</span> Gravity.TOP:
</pre>

<span class="location"><a href="../../../common/src/main/java/com/interfun/buz/common/transition/SidePropagation.java">../../../common/src/main/java/com/interfun/buz/common/transition/SidePropagation.java</a>:143</span>: <span class="message">Use "<code>Gravity.END</code>" instead of "<code>Gravity.RIGHT</code>" to ensure correct behavior in right-to-left locales</span><br /><pre class="errorlines">
<span class="lineno"> 140 </span>            <span class="keyword">case</span> Gravity.TOP:
<span class="lineno"> 141 </span>                distance = bottom - viewY + Math.abs(epicenterX - viewX);
<span class="lineno"> 142 </span>                <span class="keyword">break</span>;
<span class="caretline"><span class="lineno"> 143 </span>            <span class="keyword">case</span> Gravity.<span class="warning">RIGHT</span>:&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 144 </span>                distance = viewX - left + Math.abs(epicenterY - viewY);
<span class="lineno"> 145 </span>                <span class="keyword">break</span>;
<span class="lineno"> 146 </span>            <span class="keyword">case</span> Gravity.BOTTOM:
</pre>

<span class="location"><a href="../../../common/src/main/java/com/interfun/buz/common/transition/SidePropagation.java">../../../common/src/main/java/com/interfun/buz/common/transition/SidePropagation.java</a>:155</span>: <span class="message">Use "<code>Gravity.START</code>" instead of "<code>Gravity.LEFT</code>" to ensure correct behavior in right-to-left locales</span><br /><pre class="errorlines">
<span class="lineno"> 152 </span>
<span class="lineno"> 153 </span>    <span class="keyword">private</span> <span class="keyword">int</span> getMaxDistance(ViewGroup sceneRoot) {
<span class="lineno"> 154 </span>        <span class="keyword">switch</span> (mSide) {
<span class="caretline"><span class="lineno"> 155 </span>            <span class="keyword">case</span> Gravity.<span class="warning">LEFT</span>:&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 156 </span>            <span class="keyword">case</span> Gravity.RIGHT:
<span class="lineno"> 157 </span>            <span class="keyword">case</span> Gravity.START:
<span class="lineno"> 158 </span>            <span class="keyword">case</span> Gravity.END:
</pre>

<span class="location"><a href="../../../common/src/main/java/com/interfun/buz/common/transition/SidePropagation.java">../../../common/src/main/java/com/interfun/buz/common/transition/SidePropagation.java</a>:156</span>: <span class="message">Use "<code>Gravity.END</code>" instead of "<code>Gravity.RIGHT</code>" to ensure correct behavior in right-to-left locales</span><br /><pre class="errorlines">
<span class="lineno"> 153 </span>    <span class="keyword">private</span> <span class="keyword">int</span> getMaxDistance(ViewGroup sceneRoot) {
<span class="lineno"> 154 </span>        <span class="keyword">switch</span> (mSide) {
<span class="lineno"> 155 </span>            <span class="keyword">case</span> Gravity.LEFT:
<span class="caretline"><span class="lineno"> 156 </span>            <span class="keyword">case</span> Gravity.<span class="warning">RIGHT</span>:&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 157 </span>            <span class="keyword">case</span> Gravity.START:
<span class="lineno"> 158 </span>            <span class="keyword">case</span> Gravity.END:
<span class="lineno"> 159 </span>                <span class="keyword">return</span> sceneRoot.getWidth();
</pre>

<span class="location"><a href="../../../common/src/main/java/com/interfun/buz/common/transition/Slide.java">../../../common/src/main/java/com/interfun/buz/common/transition/Slide.java</a>:62</span>: <span class="message">Use "<code>Gravity.END</code>" instead of "<code>Gravity.RIGHT</code>" to ensure correct behavior in right-to-left locales</span><br /><pre class="errorlines">
<span class="lineno">  59 </span>  <span class="javadoc">/** @hide */</span>
<span class="lineno">  60 </span>  <span class="annotation">@RestrictTo</span>(LIBRARY_GROUP_PREFIX)
<span class="lineno">  61 </span>  <span class="annotation">@Retention</span>(RetentionPolicy.SOURCE)
<span class="caretline"><span class="lineno">  62 </span>  <span class="annotation">@IntDef</span>({Gravity.LEFT, Gravity.TOP, Gravity.<span class="warning">RIGHT</span>, Gravity.BOTTOM, Gravity.START, Gravity.END})&nbsp;</span>
<span class="lineno">  63 </span>  <span class="keyword">public</span> @<span class="keyword">interface</span> GravityFlag {
<span class="lineno">  64 </span>  }
</pre>

<span class="location"><a href="../../../common/src/main/java/com/interfun/buz/common/transition/Slide.java">../../../common/src/main/java/com/interfun/buz/common/transition/Slide.java</a>:62</span>: <span class="message">Use "<code>Gravity.START</code>" instead of "<code>Gravity.LEFT</code>" to ensure correct behavior in right-to-left locales</span><br /><pre class="errorlines">
<span class="lineno">  59 </span>  <span class="javadoc">/** @hide */</span>
<span class="lineno">  60 </span>  <span class="annotation">@RestrictTo</span>(LIBRARY_GROUP_PREFIX)
<span class="lineno">  61 </span>  <span class="annotation">@Retention</span>(RetentionPolicy.SOURCE)
<span class="caretline"><span class="lineno">  62 </span>  <span class="annotation">@IntDef</span>({Gravity.<span class="warning">LEFT</span>, Gravity.TOP, Gravity.RIGHT, Gravity.BOTTOM, Gravity.START, Gravity.END})&nbsp;</span>
<span class="lineno">  63 </span>  <span class="keyword">public</span> @<span class="keyword">interface</span> GravityFlag {
<span class="lineno">  64 </span>  }
</pre>

<span class="location"><a href="../../../common/src/main/java/com/interfun/buz/common/transition/Slide.java">../../../common/src/main/java/com/interfun/buz/common/transition/Slide.java</a>:205</span>: <span class="message">Use "<code>Gravity.START</code>" instead of "<code>Gravity.LEFT</code>" to ensure correct behavior in right-to-left locales</span><br /><pre class="errorlines">
<span class="lineno"> 202 </span><span class="javadoc">     */</span>
<span class="lineno"> 203 </span>    <span class="keyword">public</span> <span class="keyword">void</span> setSlideEdge(<span class="annotation">@GravityFlag</span> <span class="keyword">int</span> slideEdge) {
<span class="lineno"> 204 </span>        <span class="keyword">switch</span> (slideEdge) {
<span class="caretline"><span class="lineno"> 205 </span>            <span class="keyword">case</span> Gravity.<span class="warning">LEFT</span>:&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 206 </span>                mSlideCalculator = sCalculateLeft;
<span class="lineno"> 207 </span>                <span class="keyword">break</span>;
<span class="lineno"> 208 </span>            <span class="keyword">case</span> Gravity.TOP:
</pre>

<span class="location"><a href="../../../common/src/main/java/com/interfun/buz/common/transition/Slide.java">../../../common/src/main/java/com/interfun/buz/common/transition/Slide.java</a>:211</span>: <span class="message">Use "<code>Gravity.END</code>" instead of "<code>Gravity.RIGHT</code>" to ensure correct behavior in right-to-left locales</span><br /><pre class="errorlines">
<span class="lineno"> 208 </span>            <span class="keyword">case</span> Gravity.TOP:
<span class="lineno"> 209 </span>                mSlideCalculator = sCalculateTop;
<span class="lineno"> 210 </span>                <span class="keyword">break</span>;
<span class="caretline"><span class="lineno"> 211 </span>            <span class="keyword">case</span> Gravity.<span class="warning">RIGHT</span>:&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 212 </span>                mSlideCalculator = sCalculateRight;
<span class="lineno"> 213 </span>                <span class="keyword">break</span>;
<span class="lineno"> 214 </span>            <span class="keyword">case</span> Gravity.BOTTOM:
</pre>

<span class="location"><a href="../../../chat/src/main/res/layout/chat_layout_quiet_gude_tips.xml">../../../chat/src/main/res/layout/chat_layout_quiet_gude_tips.xml</a>:69</span>: <span class="message">Consider replacing <code>android:paddingLeft</code> with <code>android:paddingStart="12dp"</code> to better support right-to-left layouts</span><br /><pre class="errorlines">
<span class="lineno"> 66 </span>        <span class="prefix">android:</span><span class="attribute">ellipsize</span>=<span class="value">"end"</span>
<span class="lineno"> 67 </span>        <span class="prefix">android:</span><span class="attribute">maxLines</span>=<span class="value">"5"</span>
<span class="lineno"> 68 </span>        <span class="prefix">android:</span><span class="attribute">paddingHorizontal</span>=<span class="value">"16dp"</span>
<span class="caretline"><span class="lineno"> 69 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">paddingLeft</span></span>=<span class="value">"12dp"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 70 </span>        <span class="prefix">android:</span><span class="attribute">paddingTop</span>=<span class="value">"5dp"</span>
<span class="lineno"> 71 </span>        <span class="prefix">android:</span><span class="attribute">paddingRight</span>=<span class="value">"16dp"</span>
<span class="lineno"> 72 </span>        <span class="prefix">android:</span><span class="attribute">paddingBottom</span>=<span class="value">"12dp"</span></pre>

<span class="location"><a href="../../../chat/src/main/res/layout/chat_layout_quiet_gude_tips.xml">../../../chat/src/main/res/layout/chat_layout_quiet_gude_tips.xml</a>:71</span>: <span class="message">Consider replacing <code>android:paddingRight</code> with <code>android:paddingEnd="16dp"</code> to better support right-to-left layouts</span><br /><pre class="errorlines">
<span class="lineno"> 68 </span>        <span class="prefix">android:</span><span class="attribute">paddingHorizontal</span>=<span class="value">"16dp"</span>
<span class="lineno"> 69 </span>        <span class="prefix">android:</span><span class="attribute">paddingLeft</span>=<span class="value">"12dp"</span>
<span class="lineno"> 70 </span>        <span class="prefix">android:</span><span class="attribute">paddingTop</span>=<span class="value">"5dp"</span>
<span class="caretline"><span class="lineno"> 71 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">paddingRight</span></span>=<span class="value">"16dp"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 72 </span>        <span class="prefix">android:</span><span class="attribute">paddingBottom</span>=<span class="value">"12dp"</span>
<span class="lineno"> 73 </span>        <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/text_black_main"</span>
<span class="lineno"> 74 </span>        <span class="prefix">app:</span><span class="attribute">layout_constrainedWidth</span>=<span class="value">"true"</span></pre>

<span class="location"><a href="../../../login/src/main/res/layout/login_fragment_account_input.xml">../../../login/src/main/res/layout/login_fragment_account_input.xml</a>:117</span>: <span class="message">Consider replacing <code>android:layout_marginLeft</code> with <code>android:layout_marginStart="20dp"</code> to better support right-to-left layouts</span><br /><pre class="errorlines">
<span class="lineno"> 114 </span>        <span class="attribute">style</span>=<span class="value">"@style/text_body_large"</span>
<span class="lineno"> 115 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 116 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno"> 117 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_marginLeft</span></span>=<span class="value">"20dp"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 118 </span>        <span class="prefix">android:</span><span class="attribute">paddingVertical</span>=<span class="value">"10dp"</span>
<span class="lineno"> 119 </span>        <span class="prefix">android:</span><span class="attribute">paddingEnd</span>=<span class="value">"17dp"</span>
<span class="lineno"> 120 </span>        <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/text_white_main"</span></pre>

<span class="location"><a href="../../../login/src/main/res/layout/login_fragment_account_input.xml">../../../login/src/main/res/layout/login_fragment_account_input.xml</a>:206</span>: <span class="message">Use "<code>start</code>" instead of "<code>left</code>" to ensure correct behavior in right-to-left locales</span><br /><pre class="errorlines">
<span class="lineno"> 203 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"0dp"</span>
<span class="lineno"> 204 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginEnd</span>=<span class="value">"40dp"</span>
<span class="lineno"> 205 </span>        <span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@color/transparent"</span>
<span class="caretline"><span class="lineno"> 206 </span>        <span class="prefix">android:</span><span class="attribute">gravity</span>=<span class="value">"</span><span class="warning"><span class="value">center_vertical|left</span></span><span class="value">"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 207 </span>        <span class="prefix">android:</span><span class="attribute">imeOptions</span>=<span class="value">"actionDone"</span>
<span class="lineno"> 208 </span>        <span class="prefix">android:</span><span class="attribute">layoutDirection</span>=<span class="value">"ltr"</span>
<span class="lineno"> 209 </span>        <span class="prefix">android:</span><span class="attribute">paddingEnd</span>=<span class="value">"10dp"</span></pre>

<span class="location"><a href="../../../pictureselector/src/main/res/layout/ps_item_grid_audio.xml">../../../pictureselector/src/main/res/layout/ps_item_grid_audio.xml</a>:36</span>: <span class="message">Consider replacing <code>android:drawableLeft</code> with <code>android:drawableStart="@drawable/ps_ic_audio"</code> to better support right-to-left layouts</span><br /><pre class="errorlines">
<span class="lineno"> 33 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno"> 34 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 35 </span>        <span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@drawable/ps_ic_shadow_bg"</span>
<span class="caretline"><span class="lineno"> 36 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">drawableLeft</span></span>=<span class="value">"@drawable/ps_ic_audio"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 37 </span>        <span class="prefix">android:</span><span class="attribute">drawablePadding</span>=<span class="value">"5dp"</span>
<span class="lineno"> 38 </span>        <span class="prefix">android:</span><span class="attribute">gravity</span>=<span class="value">"center_vertical"</span>
<span class="lineno"> 39 </span>        <span class="prefix">android:</span><span class="attribute">paddingLeft</span>=<span class="value">"5dp"</span></pre>

<span class="location"><a href="../../../pictureselector/src/main/res/layout/ps_item_grid_audio.xml">../../../pictureselector/src/main/res/layout/ps_item_grid_audio.xml</a>:39</span>: <span class="message">Consider replacing <code>android:paddingLeft</code> with <code>android:paddingStart="5dp"</code> to better support right-to-left layouts</span><br /><pre class="errorlines">
<span class="lineno"> 36 </span>        <span class="prefix">android:</span><span class="attribute">drawableLeft</span>=<span class="value">"@drawable/ps_ic_audio"</span>
<span class="lineno"> 37 </span>        <span class="prefix">android:</span><span class="attribute">drawablePadding</span>=<span class="value">"5dp"</span>
<span class="lineno"> 38 </span>        <span class="prefix">android:</span><span class="attribute">gravity</span>=<span class="value">"center_vertical"</span>
<span class="caretline"><span class="lineno"> 39 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">paddingLeft</span></span>=<span class="value">"5dp"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 40 </span>        <span class="prefix">android:</span><span class="attribute">layout_alignParentBottom</span>=<span class="value">"true"</span>
<span class="lineno"> 41 </span>        <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"00:00"</span>
<span class="lineno"> 42 </span>        <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/ps_color_white"</span></pre>

<span class="location"><a href="../../../pictureselector/src/main/res/layout/ps_item_grid_video.xml">../../../pictureselector/src/main/res/layout/ps_item_grid_video.xml</a>:36</span>: <span class="message">Consider replacing <code>android:drawableLeft</code> with <code>android:drawableStart="@drawable/ps_ic_video"</code> to better support right-to-left layouts</span><br /><pre class="errorlines">
<span class="lineno"> 33 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno"> 34 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 35 </span>        <span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@drawable/ps_ic_shadow_bg"</span>
<span class="caretline"><span class="lineno"> 36 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">drawableLeft</span></span>=<span class="value">"@drawable/ps_ic_video"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 37 </span>        <span class="prefix">android:</span><span class="attribute">drawablePadding</span>=<span class="value">"5dp"</span>
<span class="lineno"> 38 </span>        <span class="prefix">android:</span><span class="attribute">gravity</span>=<span class="value">"center_vertical"</span>
<span class="lineno"> 39 </span>        <span class="prefix">android:</span><span class="attribute">layout_alignParentBottom</span>=<span class="value">"true"</span></pre>

<span class="location"><a href="../../../pictureselector/src/main/res/layout/ps_item_grid_video.xml">../../../pictureselector/src/main/res/layout/ps_item_grid_video.xml</a>:40</span>: <span class="message">Consider replacing <code>android:paddingLeft</code> with <code>android:paddingStart="5dp"</code> to better support right-to-left layouts</span><br /><pre class="errorlines">
<span class="lineno"> 37 </span>        <span class="prefix">android:</span><span class="attribute">drawablePadding</span>=<span class="value">"5dp"</span>
<span class="lineno"> 38 </span>        <span class="prefix">android:</span><span class="attribute">gravity</span>=<span class="value">"center_vertical"</span>
<span class="lineno"> 39 </span>        <span class="prefix">android:</span><span class="attribute">layout_alignParentBottom</span>=<span class="value">"true"</span>
<span class="caretline"><span class="lineno"> 40 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">paddingLeft</span></span>=<span class="value">"5dp"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 41 </span>        <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"00:00"</span>
<span class="lineno"> 42 </span>        <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/ps_color_white"</span>
<span class="lineno"> 43 </span>        <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"11sp"</span> />
</pre>

</div>
</div>
<div class="metadata"><div class="explanation" id="explanationRtlHardcoded" style="display: none;">
Using <code>Gravity#LEFT</code> and <code>Gravity#RIGHT</code> can lead to problems when a layout is rendered in locales where text flows from right to left. Use <code>Gravity#START</code> and <code>Gravity#END</code> instead. Similarly, in XML <code>gravity</code> and <code>layout_gravity</code> attributes, use <code>start</code> rather than <code>left</code>.<br/>
<br/>
For XML attributes such as paddingLeft and <code>layout_marginLeft</code>, use <code>paddingStart</code> and <code>layout_marginStart</code>. <b>NOTE</b>: If your <code>minSdkVersion</code> is less than 17, you should add <b>both</b> the older left/right attributes <b>as well as</b> the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.<br/>
<br/>
(Note: For <code>Gravity#LEFT</code> and <code>Gravity#START</code>, you can use these constants even when targeting older platforms, because the <code>start</code> bitmask is a superset of the <code>left</code> bitmask. Therefore, you can use <code>gravity="start"</code> rather than <code>gravity="left|start"</code>.)<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "RtlHardcoded" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">RtlHardcoded</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Bidirectional Text</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Internationalization</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationRtlHardcodedLink" onclick="reveal('explanationRtlHardcoded');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="RtlHardcodedCardLink" onclick="hideid('RtlHardcodedCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="RtlEnabled"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="RtlEnabledCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Using RTL attributes without enabling RTL support</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../../common/src/main/AndroidManifest.xml">../../../common/src/main/AndroidManifest.xml</a></span>: <span class="message">The project references RTL attributes, but does not explicitly enable or disable RTL support with <code>android:supportsRtl</code> in the manifest</span><br />
<span class="location"><a href="../../../contacts/src/main/AndroidManifest.xml">../../../contacts/src/main/AndroidManifest.xml</a></span>: <span class="message">The project references RTL attributes, but does not explicitly enable or disable RTL support with <code>android:supportsRtl</code> in the manifest</span><br />
<span class="location"><a href="../../../domain-ui/social/src/main/AndroidManifest.xml">../../../domain-ui/social/src/main/AndroidManifest.xml</a></span>: <span class="message">The project references RTL attributes, but does not explicitly enable or disable RTL support with <code>android:supportsRtl</code> in the manifest</span><br />
<span class="location"><a href="../../../home/<USER>/main/AndroidManifest.xml">../../../home/<USER>/main/AndroidManifest.xml</a></span>: <span class="message">The project references RTL attributes, but does not explicitly enable or disable RTL support with <code>android:supportsRtl</code> in the manifest</span><br />
<span class="location"><a href="../../../login/src/main/AndroidManifest.xml">../../../login/src/main/AndroidManifest.xml</a></span>: <span class="message">The project references RTL attributes, but does not explicitly enable or disable RTL support with <code>android:supportsRtl</code> in the manifest</span><br />
<span class="location"><a href="../../../onair/src/main/AndroidManifest.xml">../../../onair/src/main/AndroidManifest.xml</a></span>: <span class="message">The project references RTL attributes, but does not explicitly enable or disable RTL support with <code>android:supportsRtl</code> in the manifest</span><br />
<span class="location"><a href="../../../user/src/main/AndroidManifest.xml">../../../user/src/main/AndroidManifest.xml</a></span>: <span class="message">The project references RTL attributes, but does not explicitly enable or disable RTL support with <code>android:supportsRtl</code> in the manifest</span><br />
<span class="location"><a href="../../../voicecall/src/main/AndroidManifest.xml">../../../voicecall/src/main/AndroidManifest.xml</a></span>: <span class="message">The project references RTL attributes, but does not explicitly enable or disable RTL support with <code>android:supportsRtl</code> in the manifest</span><br />
</div>
<div class="metadata"><div class="explanation" id="explanationRtlEnabled" style="display: none;">
To enable right-to-left support, when running on API 17 and higher, you must set the <code>android:supportsRtl</code> attribute in the manifest <code>&lt;application></code> element.<br/>
<br/>
If you have started adding RTL attributes, but have not yet finished the migration, you can set the attribute to false to satisfy this lint check.<br/>To suppress this error, use the issue id "RtlEnabled" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">RtlEnabled</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Bidirectional Text</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Internationalization</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 3/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationRtlEnabledLink" onclick="reveal('explanationRtlEnabled');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="RtlEnabledCardLink" onclick="hideid('RtlEnabledCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="SuppressInfo"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="SuppressCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Suppressing Warnings and Errors</h2>
  </div>
              <div class="mdl-card__supporting-text">
Lint errors can be suppressed in a variety of ways:<br/>
<br/>
1. With a <code>@SuppressLint</code> annotation in the Java code<br/>
2. With a <code>tools:ignore</code> attribute in the XML file<br/>
3. With a //noinspection comment in the source code<br/>
4. With ignore flags specified in the <code>build.gradle</code> file, as explained below<br/>
5. With a <code>lint.xml</code> configuration file in the project<br/>
6. With a <code>lint.xml</code> configuration file passed to lint via the --config flag<br/>
7. With the --ignore flag passed to lint.<br/>
<br/>
To suppress a lint warning with an annotation, add a <code>@SuppressLint("id")</code> annotation on the class, method or variable declaration closest to the warning instance you want to disable. The id can be one or more issue id's, such as <code>"UnusedResources"</code> or <code>{"UnusedResources","UnusedIds"}</code>, or it can be <code>"all"</code> to suppress all lint warnings in the given scope.<br/>
<br/>
To suppress a lint warning with a comment, add a <code>//noinspection id</code> comment on the line before the statement with the error.<br/>
<br/>
To suppress a lint warning in an XML file, add a <code>tools:ignore="id"</code> attribute on the element containing the error, or one of its surrounding elements. You also need to define the namespace for the tools prefix on the root element in your document, next to the <code>xmlns:android</code> declaration:<br/>
<code>xmlns:tools="http://schemas.android.com/tools"</code><br/>
<br/>
To suppress a lint warning in a <code>build.gradle</code> file, add a section like this:<br/>

<pre>
android {
    lintOptions {
        disable 'TypographyFractions','TypographyQuotes'
    }
}
</pre>
<br/>
Here we specify a comma separated list of issue id's after the disable command. You can also use <code>warning</code> or <code>error</code> instead of <code>disable</code> to change the severity of issues.<br/>
<br/>
To suppress lint warnings with a configuration XML file, create a file named <code>lint.xml</code> and place it at the root directory of the module in which it applies.<br/>
<br/>
The format of the <code>lint.xml</code> file is something like the following:<br/>

<pre>
&lt;?xml version="1.0" encoding="UTF-8"?>
&lt;lint>
    &lt;!-- Ignore everything in the test source set -->
    &lt;issue id="all">
        &lt;ignore path="\*/test/\*" />
    &lt;/issue>

    &lt;!-- Disable this given check in this project -->
    &lt;issue id="IconMissingDensityFolder" severity="ignore" />

    &lt;!-- Ignore the ObsoleteLayoutParam issue in the given files -->
    &lt;issue id="ObsoleteLayoutParam">
        &lt;ignore path="res/layout/activation.xml" />
        &lt;ignore path="res/layout-xlarge/activation.xml" />
        &lt;ignore regexp="(foo|bar)\.java" />
    &lt;/issue>

    &lt;!-- Ignore the UselessLeaf issue in the given file -->
    &lt;issue id="UselessLeaf">
        &lt;ignore path="res/layout/main.xml" />
    &lt;/issue>

    &lt;!-- Change the severity of hardcoded strings to "error" -->
    &lt;issue id="HardcodedText" severity="error" />
&lt;/lint>
</pre>
<br/>
To suppress lint checks from the command line, pass the --ignore flag with a comma separated list of ids to be suppressed, such as:<br/>
<code>$ lint --ignore UnusedResources,UselessLeaf /my/project/path</code><br/>
<br/>
For more information, see <a href="https://developer.android.com/studio/write/lint.html#config">https://developer.android.com/studio/write/lint.html#config</a><br/>

            </div>
            </div>
          </section>    </div>
  </main>
</div>
</body>
</html>