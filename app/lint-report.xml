<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.8.2">

    <issue
        id="UnknownIssueId"
        severity="Warning"
        message="Unknown issue id &quot;LZLint_HashMapForJDK7&quot;"
        category="Lint"
        priority="1"
        summary="Unknown Lint Issue Id"
        explanation="<PERSON><PERSON> will report this issue if it is configured with an issue id it does not recognize in for example Gradle files or `lint.xml` configuration files.">
        <location
            file="/Users/<USER>/workspace/work/buz_android/app/build.gradle"/>
    </issue>

    <issue
        id="UnknownIssueId"
        severity="Warning"
        message="Unknown issue id &quot;LZLint_ImageFileSizeInvalid&quot;"
        category="Lint"
        priority="1"
        summary="Unknown Lint Issue Id"
        explanation="Lin<PERSON> will report this issue if it is configured with an issue id it does not recognize in for example Gradle files or `lint.xml` configuration files.">
        <location
            file="/Users/<USER>/workspace/work/buz_android/app/build.gradle"/>
    </issue>

    <issue
        id="UnknownIssueId"
        severity="Warning"
        message="Unknown issue id &quot;LZLint_MessageObtainUseError&quot;"
        category="Lint"
        priority="1"
        summary="Unknown Lint Issue Id"
        explanation="Lint will report this issue if it is configured with an issue id it does not recognize in for example Gradle files or `lint.xml` configuration files.">
        <location
            file="/Users/<USER>/workspace/work/buz_android/app/build.gradle"/>
    </issue>

    <issue
        id="UnknownIssueId"
        severity="Warning"
        message="Unknown issue id &quot;LZLint_ParseHandleID&quot;"
        category="Lint"
        priority="1"
        summary="Unknown Lint Issue Id"
        explanation="Lint will report this issue if it is configured with an issue id it does not recognize in for example Gradle files or `lint.xml` configuration files.">
        <location
            file="/Users/<USER>/workspace/work/buz_android/app/build.gradle"/>
    </issue>

    <issue
        id="InlinedApi"
        severity="Warning"
        message="Field requires API level 33 (current min is 31): `android.media.MediaCodecInfo.CodecCapabilities#COLOR_Format32bitABGR2101010`"
        category="Correctness"
        priority="6"
        summary="Using inlined constants on older versions"
        explanation="This check scans through all the Android API field references in the application and flags certain constants, such as static final integers and Strings, which were introduced in later versions. These will actually be copied into the class files rather than being referenced, which means that the value is available even when running on older devices. In some cases that&apos;s fine, and in other cases it can result in a runtime crash or incorrect behavior. It depends on the context, so consider the code carefully and decide whether it&apos;s safe and can be suppressed or whether the code needs to be guarded.&#xA;&#xA;If you really want to use this API and don&apos;t need to support older devices just set the `minSdkVersion` in your `build.gradle` or `AndroidManifest.xml` files.&#xA;&#xA;If your code is **deliberately** accessing newer APIs, and you have ensured (e.g. with conditional execution) that this code will only ever be called on a supported platform, then you can annotate your class or method with the `@TargetApi` annotation specifying the local minimum SDK to apply, such as `@TargetApi(11)`, such that this check considers 11 rather than your manifest file&apos;s minimum SDK as the required API level."
        errorLine1="                        MediaCodecInfo.CodecCapabilities.COLOR_Format32bitABGR2101010);"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/Users/<USER>/workspace/work/buz_android/media/src/main/java/com/interfun/buz/media/video/compressor/encoder/DefaultEncoderFactory.java"
            line="293"
            column="25"/>
    </issue>

    <issue
        id="InlinedApi"
        severity="Warning"
        message="Field requires API level 31 (current min is 24): `android.view.FrameMetrics#GPU_DURATION`"
        category="Correctness"
        priority="6"
        summary="Using inlined constants on older versions"
        explanation="This check scans through all the Android API field references in the application and flags certain constants, such as static final integers and Strings, which were introduced in later versions. These will actually be copied into the class files rather than being referenced, which means that the value is available even when running on older devices. In some cases that&apos;s fine, and in other cases it can result in a runtime crash or incorrect behavior. It depends on the context, so consider the code carefully and decide whether it&apos;s safe and can be suppressed or whether the code needs to be guarded.&#xA;&#xA;If you really want to use this API and don&apos;t need to support older devices just set the `minSdkVersion` in your `build.gradle` or `AndroidManifest.xml` files.&#xA;&#xA;If your code is **deliberately** accessing newer APIs, and you have ensured (e.g. with conditional execution) that this code will only ever be called on a supported platform, then you can annotate your class or method with the `@TargetApi` annotation specifying the local minimum SDK to apply, such as `@TargetApi(11)`, such that this check considers 11 rather than your manifest file&apos;s minimum SDK as the required API level."
        errorLine1="                (0.000001 * f.getMetric(FrameMetrics.GPU_DURATION)).toFloat()"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/Users/<USER>/workspace/work/buz_android/base/src/main/java/com/interfun/buz/base/manager/FrameMetricsListener.kt"
            line="65"
            column="41"/>
    </issue>

    <issue
        id="InlinedApi"
        severity="Warning"
        message="Field requires API level 23 (current min is 21): `android.net.NetworkCapabilities#NET_CAPABILITY_VALIDATED`"
        category="Correctness"
        priority="6"
        summary="Using inlined constants on older versions"
        explanation="This check scans through all the Android API field references in the application and flags certain constants, such as static final integers and Strings, which were introduced in later versions. These will actually be copied into the class files rather than being referenced, which means that the value is available even when running on older devices. In some cases that&apos;s fine, and in other cases it can result in a runtime crash or incorrect behavior. It depends on the context, so consider the code carefully and decide whether it&apos;s safe and can be suppressed or whether the code needs to be guarded.&#xA;&#xA;If you really want to use this API and don&apos;t need to support older devices just set the `minSdkVersion` in your `build.gradle` or `AndroidManifest.xml` files.&#xA;&#xA;If your code is **deliberately** accessing newer APIs, and you have ensured (e.g. with conditional execution) that this code will only ever be called on a supported platform, then you can annotate your class or method with the `@TargetApi` annotation specifying the local minimum SDK to apply, such as `@TargetApi(11)`, such that this check considers 11 rather than your manifest file&apos;s minimum SDK as the required API level."
        errorLine1="                    NET_CAPABILITY_VALIDATED"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/Users/<USER>/workspace/work/buz_android/base/src/main/java/com/interfun/buz/base/ktx/Network.kt"
            line="243"
            column="21"/>
    </issue>

    <issue
        id="InlinedApi"
        severity="Warning"
        message="Field requires API level 24 (current min is 21): `android.app.NotificationManager#IMPORTANCE_HIGH`"
        category="Correctness"
        priority="6"
        summary="Using inlined constants on older versions"
        explanation="This check scans through all the Android API field references in the application and flags certain constants, such as static final integers and Strings, which were introduced in later versions. These will actually be copied into the class files rather than being referenced, which means that the value is available even when running on older devices. In some cases that&apos;s fine, and in other cases it can result in a runtime crash or incorrect behavior. It depends on the context, so consider the code carefully and decide whether it&apos;s safe and can be suppressed or whether the code needs to be guarded.&#xA;&#xA;If you really want to use this API and don&apos;t need to support older devices just set the `minSdkVersion` in your `build.gradle` or `AndroidManifest.xml` files.&#xA;&#xA;If your code is **deliberately** accessing newer APIs, and you have ensured (e.g. with conditional execution) that this code will only ever be called on a supported platform, then you can annotate your class or method with the `@TargetApi` annotation specifying the local minimum SDK to apply, such as `@TargetApi(11)`, such that this check considers 11 rather than your manifest file&apos;s minimum SDK as the required API level."
        errorLine1="            NotificationManager.IMPORTANCE_HIGH,"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/Users/<USER>/workspace/work/buz_android/common/src/main/java/com/interfun/buz/common/utils/NotificationChannelUtils.kt"
            line="271"
            column="13"/>
    </issue>

    <issue
        id="InlinedApi"
        severity="Warning"
        message="Field requires API level 24 (current min is 21): `android.app.NotificationManager#IMPORTANCE_HIGH`"
        category="Correctness"
        priority="6"
        summary="Using inlined constants on older versions"
        explanation="This check scans through all the Android API field references in the application and flags certain constants, such as static final integers and Strings, which were introduced in later versions. These will actually be copied into the class files rather than being referenced, which means that the value is available even when running on older devices. In some cases that&apos;s fine, and in other cases it can result in a runtime crash or incorrect behavior. It depends on the context, so consider the code carefully and decide whether it&apos;s safe and can be suppressed or whether the code needs to be guarded.&#xA;&#xA;If you really want to use this API and don&apos;t need to support older devices just set the `minSdkVersion` in your `build.gradle` or `AndroidManifest.xml` files.&#xA;&#xA;If your code is **deliberately** accessing newer APIs, and you have ensured (e.g. with conditional execution) that this code will only ever be called on a supported platform, then you can annotate your class or method with the `@TargetApi` annotation specifying the local minimum SDK to apply, such as `@TargetApi(11)`, such that this check considers 11 rather than your manifest file&apos;s minimum SDK as the required API level."
        errorLine1="            NotificationManager.IMPORTANCE_HIGH,"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/Users/<USER>/workspace/work/buz_android/common/src/main/java/com/interfun/buz/common/utils/NotificationChannelUtils.kt"
            line="283"
            column="13"/>
    </issue>

    <issue
        id="InlinedApi"
        severity="Warning"
        message="Field requires API level 22 (current min is 21): `android.content.Intent#EXTRA_CHOSEN_COMPONENT`"
        category="Correctness"
        priority="6"
        summary="Using inlined constants on older versions"
        explanation="This check scans through all the Android API field references in the application and flags certain constants, such as static final integers and Strings, which were introduced in later versions. These will actually be copied into the class files rather than being referenced, which means that the value is available even when running on older devices. In some cases that&apos;s fine, and in other cases it can result in a runtime crash or incorrect behavior. It depends on the context, so consider the code carefully and decide whether it&apos;s safe and can be suppressed or whether the code needs to be guarded.&#xA;&#xA;If you really want to use this API and don&apos;t need to support older devices just set the `minSdkVersion` in your `build.gradle` or `AndroidManifest.xml` files.&#xA;&#xA;If your code is **deliberately** accessing newer APIs, and you have ensured (e.g. with conditional execution) that this code will only ever be called on a supported platform, then you can annotate your class or method with the `@TargetApi` annotation specifying the local minimum SDK to apply, such as `@TargetApi(11)`, such that this check considers 11 rather than your manifest file&apos;s minimum SDK as the required API level."
        errorLine1="            intent?.getParcelableExtra&lt;ComponentName>(android.content.Intent.EXTRA_CHOSEN_COMPONENT)"
        errorLine2="                                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/Users/<USER>/workspace/work/buz_android/sharedmedia/src/main/java/com/interfun/buz/shared/SharedBroadcastReceiver.kt"
            line="18"
            column="55"/>
    </issue>

    <issue
        id="RtlCompat"
        severity="Error"
        message="Inconsistent alignment specification between `textAlignment` and `gravity` attributes: was `center_vertical|left`, expected `start`"
        category="Internationalization:Bidirectional Text"
        priority="6"
        summary="Right-to-left text compatibility issues"
        explanation="API 17 adds a `textAlignment` attribute to specify text alignment. However, if you are supporting older versions than API 17, you must **also** specify a gravity or layout_gravity attribute, since older platforms will ignore the `textAlignment` attribute."
        errorLine1="        android:textAlignment=&quot;viewStart&quot;"
        errorLine2="                               ~~~~~~~~~">
        <location
            file="/Users/<USER>/workspace/work/buz_android/login/src/main/res/layout/login_fragment_account_input.xml"
            line="210"
            column="32"/>
        <location
            file="/Users/<USER>/workspace/work/buz_android/login/src/main/res/layout/login_fragment_account_input.xml"
            line="206"
            column="26"
            message="Incompatible direction here"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Use &quot;`Gravity.START`&quot; instead of &quot;`Gravity.LEFT`&quot; to ensure correct behavior in right-to-left locales"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="                Gravity.START, Gravity.LEFT -> {"
        errorLine2="                                       ~~~~">
        <location
            file="/Users/<USER>/workspace/work/buz_android/chat/src/main/java/com/interfun/buz/chat/common/view/widget/GettingStartedGuideView.kt"
            line="61"
            column="40"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Use &quot;`Gravity.END`&quot; instead of &quot;`Gravity.RIGHT`&quot; to ensure correct behavior in right-to-left locales"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="                Gravity.END, Gravity.RIGHT -> {"
        errorLine2="                                     ~~~~~">
        <location
            file="/Users/<USER>/workspace/work/buz_android/chat/src/main/java/com/interfun/buz/chat/common/view/widget/GettingStartedGuideView.kt"
            line="86"
            column="38"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Use &quot;`Gravity.END`&quot; instead of &quot;`Gravity.RIGHT`&quot; to ensure correct behavior in right-to-left locales"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="            side = isRtl ? Gravity.RIGHT : Gravity.LEFT;"
        errorLine2="                                   ~~~~~">
        <location
            file="/Users/<USER>/workspace/work/buz_android/common/src/main/java/com/interfun/buz/common/transition/SidePropagation.java"
            line="127"
            column="36"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Use &quot;`Gravity.START`&quot; instead of &quot;`Gravity.LEFT`&quot; to ensure correct behavior in right-to-left locales"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="            side = isRtl ? Gravity.RIGHT : Gravity.LEFT;"
        errorLine2="                                                   ~~~~">
        <location
            file="/Users/<USER>/workspace/work/buz_android/common/src/main/java/com/interfun/buz/common/transition/SidePropagation.java"
            line="127"
            column="52"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Use &quot;`Gravity.END`&quot; instead of &quot;`Gravity.RIGHT`&quot; to ensure correct behavior in right-to-left locales"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="            side = isRtl ? Gravity.LEFT : Gravity.RIGHT;"
        errorLine2="                                                  ~~~~~">
        <location
            file="/Users/<USER>/workspace/work/buz_android/common/src/main/java/com/interfun/buz/common/transition/SidePropagation.java"
            line="131"
            column="51"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Use &quot;`Gravity.START`&quot; instead of &quot;`Gravity.LEFT`&quot; to ensure correct behavior in right-to-left locales"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="            side = isRtl ? Gravity.LEFT : Gravity.RIGHT;"
        errorLine2="                                   ~~~~">
        <location
            file="/Users/<USER>/workspace/work/buz_android/common/src/main/java/com/interfun/buz/common/transition/SidePropagation.java"
            line="131"
            column="36"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Use &quot;`Gravity.START`&quot; instead of &quot;`Gravity.LEFT`&quot; to ensure correct behavior in right-to-left locales"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="            case Gravity.LEFT:"
        errorLine2="                         ~~~~">
        <location
            file="/Users/<USER>/workspace/work/buz_android/common/src/main/java/com/interfun/buz/common/transition/SidePropagation.java"
            line="137"
            column="26"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Use &quot;`Gravity.END`&quot; instead of &quot;`Gravity.RIGHT`&quot; to ensure correct behavior in right-to-left locales"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="            case Gravity.RIGHT:"
        errorLine2="                         ~~~~~">
        <location
            file="/Users/<USER>/workspace/work/buz_android/common/src/main/java/com/interfun/buz/common/transition/SidePropagation.java"
            line="143"
            column="26"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Use &quot;`Gravity.START`&quot; instead of &quot;`Gravity.LEFT`&quot; to ensure correct behavior in right-to-left locales"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="            case Gravity.LEFT:"
        errorLine2="                         ~~~~">
        <location
            file="/Users/<USER>/workspace/work/buz_android/common/src/main/java/com/interfun/buz/common/transition/SidePropagation.java"
            line="155"
            column="26"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Use &quot;`Gravity.END`&quot; instead of &quot;`Gravity.RIGHT`&quot; to ensure correct behavior in right-to-left locales"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="            case Gravity.RIGHT:"
        errorLine2="                         ~~~~~">
        <location
            file="/Users/<USER>/workspace/work/buz_android/common/src/main/java/com/interfun/buz/common/transition/SidePropagation.java"
            line="156"
            column="26"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Use &quot;`Gravity.END`&quot; instead of &quot;`Gravity.RIGHT`&quot; to ensure correct behavior in right-to-left locales"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="    @IntDef({Gravity.LEFT, Gravity.TOP, Gravity.RIGHT, Gravity.BOTTOM, Gravity.START, Gravity.END})"
        errorLine2="                                                ~~~~~">
        <location
            file="/Users/<USER>/workspace/work/buz_android/common/src/main/java/com/interfun/buz/common/transition/Slide.java"
            line="62"
            column="49"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Use &quot;`Gravity.START`&quot; instead of &quot;`Gravity.LEFT`&quot; to ensure correct behavior in right-to-left locales"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="    @IntDef({Gravity.LEFT, Gravity.TOP, Gravity.RIGHT, Gravity.BOTTOM, Gravity.START, Gravity.END})"
        errorLine2="                     ~~~~">
        <location
            file="/Users/<USER>/workspace/work/buz_android/common/src/main/java/com/interfun/buz/common/transition/Slide.java"
            line="62"
            column="22"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Use &quot;`Gravity.START`&quot; instead of &quot;`Gravity.LEFT`&quot; to ensure correct behavior in right-to-left locales"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="            case Gravity.LEFT:"
        errorLine2="                         ~~~~">
        <location
            file="/Users/<USER>/workspace/work/buz_android/common/src/main/java/com/interfun/buz/common/transition/Slide.java"
            line="205"
            column="26"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Use &quot;`Gravity.END`&quot; instead of &quot;`Gravity.RIGHT`&quot; to ensure correct behavior in right-to-left locales"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="            case Gravity.RIGHT:"
        errorLine2="                         ~~~~~">
        <location
            file="/Users/<USER>/workspace/work/buz_android/common/src/main/java/com/interfun/buz/common/transition/Slide.java"
            line="211"
            column="26"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Consider replacing `android:paddingLeft` with `android:paddingStart=&quot;12dp&quot;` to better support right-to-left layouts"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="        android:paddingLeft=&quot;12dp&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~">
        <location
            file="/Users/<USER>/workspace/work/buz_android/chat/src/main/res/layout/chat_layout_quiet_gude_tips.xml"
            line="69"
            column="9"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Consider replacing `android:paddingRight` with `android:paddingEnd=&quot;16dp&quot;` to better support right-to-left layouts"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="        android:paddingRight=&quot;16dp&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/Users/<USER>/workspace/work/buz_android/chat/src/main/res/layout/chat_layout_quiet_gude_tips.xml"
            line="71"
            column="9"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Consider replacing `android:layout_marginLeft` with `android:layout_marginStart=&quot;20dp&quot;` to better support right-to-left layouts"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="        android:layout_marginLeft=&quot;20dp&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/Users/<USER>/workspace/work/buz_android/login/src/main/res/layout/login_fragment_account_input.xml"
            line="117"
            column="9"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Use &quot;`start`&quot; instead of &quot;`left`&quot; to ensure correct behavior in right-to-left locales"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="        android:gravity=&quot;center_vertical|left&quot;"
        errorLine2="                         ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/Users/<USER>/workspace/work/buz_android/login/src/main/res/layout/login_fragment_account_input.xml"
            line="206"
            column="26"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Consider replacing `android:drawableLeft` with `android:drawableStart=&quot;@drawable/ps_ic_audio&quot;` to better support right-to-left layouts"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="        android:drawableLeft=&quot;@drawable/ps_ic_audio&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/Users/<USER>/workspace/work/buz_android/pictureselector/src/main/res/layout/ps_item_grid_audio.xml"
            line="36"
            column="9"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Consider replacing `android:paddingLeft` with `android:paddingStart=&quot;5dp&quot;` to better support right-to-left layouts"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="        android:paddingLeft=&quot;5dp&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~">
        <location
            file="/Users/<USER>/workspace/work/buz_android/pictureselector/src/main/res/layout/ps_item_grid_audio.xml"
            line="39"
            column="9"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Consider replacing `android:drawableLeft` with `android:drawableStart=&quot;@drawable/ps_ic_video&quot;` to better support right-to-left layouts"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="        android:drawableLeft=&quot;@drawable/ps_ic_video&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/Users/<USER>/workspace/work/buz_android/pictureselector/src/main/res/layout/ps_item_grid_video.xml"
            line="36"
            column="9"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Consider replacing `android:paddingLeft` with `android:paddingStart=&quot;5dp&quot;` to better support right-to-left layouts"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="        android:paddingLeft=&quot;5dp&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~">
        <location
            file="/Users/<USER>/workspace/work/buz_android/pictureselector/src/main/res/layout/ps_item_grid_video.xml"
            line="40"
            column="9"/>
    </issue>

    <issue
        id="RtlEnabled"
        severity="Warning"
        message="The project references RTL attributes, but does not explicitly enable or disable RTL support with `android:supportsRtl` in the manifest"
        category="Internationalization:Bidirectional Text"
        priority="3"
        summary="Using RTL attributes without enabling RTL support"
        explanation="To enable right-to-left support, when running on API 17 and higher, you must set the `android:supportsRtl` attribute in the manifest `&lt;application>` element.&#xA;&#xA;If you have started adding RTL attributes, but have not yet finished the migration, you can set the attribute to false to satisfy this lint check.">
        <location
            file="/Users/<USER>/workspace/work/buz_android/common/src/main/AndroidManifest.xml"/>
    </issue>

    <issue
        id="RtlEnabled"
        severity="Warning"
        message="The project references RTL attributes, but does not explicitly enable or disable RTL support with `android:supportsRtl` in the manifest"
        category="Internationalization:Bidirectional Text"
        priority="3"
        summary="Using RTL attributes without enabling RTL support"
        explanation="To enable right-to-left support, when running on API 17 and higher, you must set the `android:supportsRtl` attribute in the manifest `&lt;application>` element.&#xA;&#xA;If you have started adding RTL attributes, but have not yet finished the migration, you can set the attribute to false to satisfy this lint check.">
        <location
            file="/Users/<USER>/workspace/work/buz_android/contacts/src/main/AndroidManifest.xml"/>
    </issue>

    <issue
        id="RtlEnabled"
        severity="Warning"
        message="The project references RTL attributes, but does not explicitly enable or disable RTL support with `android:supportsRtl` in the manifest"
        category="Internationalization:Bidirectional Text"
        priority="3"
        summary="Using RTL attributes without enabling RTL support"
        explanation="To enable right-to-left support, when running on API 17 and higher, you must set the `android:supportsRtl` attribute in the manifest `&lt;application>` element.&#xA;&#xA;If you have started adding RTL attributes, but have not yet finished the migration, you can set the attribute to false to satisfy this lint check.">
        <location
            file="/Users/<USER>/workspace/work/buz_android/domain-ui/social/src/main/AndroidManifest.xml"/>
    </issue>

    <issue
        id="RtlEnabled"
        severity="Warning"
        message="The project references RTL attributes, but does not explicitly enable or disable RTL support with `android:supportsRtl` in the manifest"
        category="Internationalization:Bidirectional Text"
        priority="3"
        summary="Using RTL attributes without enabling RTL support"
        explanation="To enable right-to-left support, when running on API 17 and higher, you must set the `android:supportsRtl` attribute in the manifest `&lt;application>` element.&#xA;&#xA;If you have started adding RTL attributes, but have not yet finished the migration, you can set the attribute to false to satisfy this lint check.">
        <location
            file="/Users/<USER>/workspace/work/buz_android/home/<USER>/main/AndroidManifest.xml"/>
    </issue>

    <issue
        id="RtlEnabled"
        severity="Warning"
        message="The project references RTL attributes, but does not explicitly enable or disable RTL support with `android:supportsRtl` in the manifest"
        category="Internationalization:Bidirectional Text"
        priority="3"
        summary="Using RTL attributes without enabling RTL support"
        explanation="To enable right-to-left support, when running on API 17 and higher, you must set the `android:supportsRtl` attribute in the manifest `&lt;application>` element.&#xA;&#xA;If you have started adding RTL attributes, but have not yet finished the migration, you can set the attribute to false to satisfy this lint check.">
        <location
            file="/Users/<USER>/workspace/work/buz_android/login/src/main/AndroidManifest.xml"/>
    </issue>

    <issue
        id="RtlEnabled"
        severity="Warning"
        message="The project references RTL attributes, but does not explicitly enable or disable RTL support with `android:supportsRtl` in the manifest"
        category="Internationalization:Bidirectional Text"
        priority="3"
        summary="Using RTL attributes without enabling RTL support"
        explanation="To enable right-to-left support, when running on API 17 and higher, you must set the `android:supportsRtl` attribute in the manifest `&lt;application>` element.&#xA;&#xA;If you have started adding RTL attributes, but have not yet finished the migration, you can set the attribute to false to satisfy this lint check.">
        <location
            file="/Users/<USER>/workspace/work/buz_android/onair/src/main/AndroidManifest.xml"/>
    </issue>

    <issue
        id="RtlEnabled"
        severity="Warning"
        message="The project references RTL attributes, but does not explicitly enable or disable RTL support with `android:supportsRtl` in the manifest"
        category="Internationalization:Bidirectional Text"
        priority="3"
        summary="Using RTL attributes without enabling RTL support"
        explanation="To enable right-to-left support, when running on API 17 and higher, you must set the `android:supportsRtl` attribute in the manifest `&lt;application>` element.&#xA;&#xA;If you have started adding RTL attributes, but have not yet finished the migration, you can set the attribute to false to satisfy this lint check.">
        <location
            file="/Users/<USER>/workspace/work/buz_android/user/src/main/AndroidManifest.xml"/>
    </issue>

    <issue
        id="RtlEnabled"
        severity="Warning"
        message="The project references RTL attributes, but does not explicitly enable or disable RTL support with `android:supportsRtl` in the manifest"
        category="Internationalization:Bidirectional Text"
        priority="3"
        summary="Using RTL attributes without enabling RTL support"
        explanation="To enable right-to-left support, when running on API 17 and higher, you must set the `android:supportsRtl` attribute in the manifest `&lt;application>` element.&#xA;&#xA;If you have started adding RTL attributes, but have not yet finished the migration, you can set the attribute to false to satisfy this lint check.">
        <location
            file="/Users/<USER>/workspace/work/buz_android/voicecall/src/main/AndroidManifest.xml"/>
    </issue>

</issues>
