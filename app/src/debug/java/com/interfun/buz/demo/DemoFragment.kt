package com.interfun.buz.demo

import android.Manifest
import androidx.recyclerview.widget.GridLayoutManager
import com.interfun.buz.base.ktx.*
import com.interfun.buz.chat.common.constants.ChatMMKV
import com.interfun.buz.common.base.binding.BaseBindingDelegate
import com.interfun.buz.common.base.binding.BaseBindingFragment
import com.interfun.buz.common.database.entity.SessionKey
import com.interfun.buz.common.ktx.uid
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.qrcode.QRCodeScanActivity
import com.interfun.buz.common.utils.PermissionHelper
import com.interfun.buz.common.web.WebViewActivity
import com.interfun.buz.common.widget.dialog.CommonAlertDialog
import com.interfun.buz.constants.AppMMKV
import com.interfun.buz.databinding.FragmentDemoBinding
import com.interfun.buz.databinding.ItemButtonBinding
import com.interfun.buz.demo.giphy.GiphyAPIDemoActivity
import com.interfun.buz.demo.homepage.HomeDemoActivity
import com.interfun.buz.startup.helper.AppSignatureHashHelper
import kotlin.system.exitProcess

/**
 * 示例模块
 */
class DemoFragment : BaseBindingFragment<FragmentDemoBinding>() {

    private val requestPermissionsLauncher = requestMultiplePermissionsLauncher(onAllGranted = {
        // 已全部同意
        toast("已全部同意")
    }, onDenied = { deniedList ->
        // 部分权限已拒绝且不再询问，可弹框引导用户到设置里授权该权限
        // 弹框提示后可调用 launchAppSettings() 方法跳到设置页
        toast("部分权限已拒绝且不再询问: $deniedList")
        CommonAlertDialog(<EMAIL>(), title = "标题", tips = "描述", negativeText = "取消", negativeCallback = {
            it.dismiss()
        }, positiveText = "确定", positiveCallback = {
            launchAppSettings()
            it.dismiss()
        }).show()
    }, onShowRequestRationale = { deniedList ->
        // 部分权限拒绝了一次，可弹框解释为什么要获取该权限
        // 弹框提示后可调用 requestDeniedPermissions() 方法请求拒绝的权限
        toast("部分权限拒绝了一次: $deniedList")
    })

    private val permissionHelper = PermissionHelper(this)

    override fun initView() {
        binding.spaceStatusBar.initStatusBarHeight()
        val btnListAdapter = MultiTypeAdapter { register(ButtonDelegate()) }
        binding.rvBtn.apply {
            layoutManager = GridLayoutManager(requireContext(), 4)
            adapter = btnListAdapter
        }
        binding.btnWebView.click {
            val url = binding.etUrl.textString
            if (url.isEmpty()) {
                toast("url is empty")
            } else {
                WebViewActivity.start(requireContext, url)
            }
        }
        btnListAdapter.items = listOf(
            ButtonInfo("申请权限") {
                requestPermissionsLauncher.launch(
                    arrayOf(
                        Manifest.permission.READ_CONTACTS, Manifest.permission.CAMERA
                    )
                )
            },
            ButtonInfo("MMKV") {
                // 常规使用方式
                val bool = mmkv.decodeBool(AppMMKV.APP_KEY_DEMO_TEST_BOOL)
                mmkv.encode(AppMMKV.APP_KEY_DEMO_TEST_BOOL, !bool)
                // 属性委托使用方式
                AppMMKV.appDemoTestBool = !AppMMKV.appDemoTestBool
                "MMKV:${AppMMKV.appDemoTestBool}".toast()
            },
            ButtonInfo("获取Hash") {
                AppSignatureHashHelper(context).appSignatures.toString().toastLong()
            },
            ButtonInfo("IM模拟Opus音频") {
                startActivity<IMTestActivity>()
            },
            ButtonInfo("修改token") {
                UserSessionManager.setSessionValue(UserSessionManager.uid, SessionKey.KEY_SESSION_KEY, "dshfiwehf")
            },
            ButtonInfo("Home WT Activity") {
                startActivity<HomeDemoActivity>()
            },
            ButtonInfo("新手引导显示标记清除") {
                ChatMMKV.hadShownRegisterWTGuideStepOne = false
                ChatMMKV.hadShownRegisterWTGuideStepTwo = false
                ChatMMKV.hadShownRegisterWTGuideStepThird = false
            },
            ButtonInfo("打开扫码") {
                startActivity<QRCodeScanActivity>()
            },
            ButtonInfo("Camera Button") {
                startActivity<TakePhotoDemoActivity>()
            },
            ButtonInfo("Media Upload Animation") {
                startActivity<MediaUploadDemoActivity>()
            },
            ButtonInfo("Media download Animation") {
                startActivity<DownloadMediaButtonDemoActivity>()
            },
            ButtonInfo("BuzPlayerView") {
                startActivity<BuzPlayerViewDemoActivity>()
            },
            ButtonInfo("SinglePagePlayer") {
                startActivity<SinglePagePlayerDemoActivity>()
            },  ButtonInfo("ExoPlayer") {
                startActivity<PureExoplayerDemoActivity>()
            }, ButtonInfo("BuzMediaCache") {
                startActivity<BuzMediaCacheDemoActivity>()
            }, ButtonInfo("FromHtml") {
                startActivity<FromHtmlDemoActivity>()
            }, ButtonInfo("VoiceEmojiTextView") {
                startActivity<VoiceEmojiTextViewDemoActivity>()
            },
            ButtonInfo("Hyperlink") {
                startActivity<HyperlinkDemoActivity>()
            },
            ButtonInfo("Empty Data View") {
                startActivity<EmptyDataDemoActivity>()
            },
            ButtonInfo("ExitProcess") {
                finishAllActivities()
                exitProcess(0)
            },
            ButtonInfo("注册优化v2ABtest") {
                startActivity<RegisterV2Activity>()
            },
            ButtonInfo("弹窗") {
                startActivity<DialogDemoActivity>()
            },
            ButtonInfo("BottomDialog") {
                startActivity<InteractiveDialogDemoActivity>()
            },
            ButtonInfo("Font Test") {
                startActivity<FontTestDemoActivity>()
            },
            ButtonInfo("振动 测试") {
                startActivity<DemoVibratorActivity>()
            },
            ButtonInfo("Giphy API") {
                startActivity<GiphyAPIDemoActivity>()
            },
            ButtonInfo("闹钟 test") {
                startActivity<AlarmScheduleDemoActivity>()
            },
        )
    }

    data class ButtonInfo(val text: String, val action: DefaultCallback)

    class ButtonDelegate : BaseBindingDelegate<ButtonInfo, ItemButtonBinding>() {
        override fun onBindViewHolder(binding: ItemButtonBinding, item: ButtonInfo, position: Int) {
            binding.btn.text = item.text
            binding.btn.click { item.action.invoke() }
        }
    }
}

