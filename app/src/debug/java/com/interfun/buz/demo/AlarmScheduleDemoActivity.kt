package com.interfun.buz.demo

import android.os.Bundle
import android.util.Log
import android.widget.Toast
import androidx.activity.compose.setContent
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.lifecycleScope
import com.interfun.buz.compose.components.TimePicker
import com.interfun.buz.user.entity.TimeScheduledEvent
import com.interfun.buz.user.repository.AlarmType
import com.interfun.buz.user.repository.TimeTriggerRepository
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Author: ChenYouSheng
 * Date: 2025/7/30
 * Email: <EMAIL>
 * Desc:
 */
@AndroidEntryPoint
class AlarmScheduleDemoActivity : AppCompatActivity() {

    companion object{
        const val TAG = "AlarmScheduleDemoActivity"
    }

    @Inject
    lateinit var timeTriggerRepository: TimeTriggerRepository

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            var selectedHour by remember { mutableIntStateOf(16) }
            var selectedMinute by remember { mutableIntStateOf(30) }

            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color(0xFFF5F5F5)),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    text = "选择时间：${selectedHour.toString().padStart(2, '0')}:${
                        selectedMinute.toString().padStart(2, '0')
                    }",
                    fontSize = 20.sp,
                    modifier = Modifier.padding(bottom = 16.dp)
                )

                TimePicker(
                    modifier = Modifier
                        .background(Color(0xFF292929))
                        .padding(horizontal = 20.dp),
                    selectedHour = selectedHour,
                    selectedMinute = selectedMinute,
                    onTimeSelected = { hour, minute ->
                        selectedHour = hour
                        selectedMinute = minute

                    }
                )

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceAround
                ) {
                    Button(onClick = {
                        scheduleTestAlarms(selectedHour, selectedMinute,AlarmType.START)
                    }) {
                        Text("开始闹钟")
                    }

                    Button(onClick = {
                        scheduleTestAlarms(selectedHour, selectedMinute,AlarmType.END)
                    }) {
                        Text("结束闹钟")
                    }

                    Button(onClick = {
                        timeTriggerRepository.cancelScheduleAlarms()
                    }) {
                        Text("取消所有闹钟")
                    }

                }

            }
        }


        // 收集触发结果
        GlobalScope.launch {
            timeTriggerRepository.timeTriggerShareFlow.collect { event ->
                when (event) {
                    TimeScheduledEvent.Start -> Log.d(TAG, "闹钟触发：进入时间段")
                    TimeScheduledEvent.End -> Log.d(TAG, "闹钟触发：退出时间段")
                }
            }
        }
    }

    private fun scheduleTestAlarms(selectedHour: Int, selectedMinute: Int,alarmType: AlarmType) {
        timeTriggerRepository.scheduleAlarms(
            hour = selectedHour,
            minute = selectedMinute,
            alarmType = alarmType,
        )
        val content = if(alarmType == AlarmType.START){
            "开始闹钟"
        }else{
            "结束闹钟"
        }
        Toast.makeText(this, "${selectedHour}:${selectedMinute} $content", Toast.LENGTH_SHORT).show()
    }
}