<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.READ_CONTACTS" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.DEVICE_POWER" />
    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
    <uses-sdk tools:overrideLibrary="fm.lizhi.testing.leakcanary"/>

    <application
        android:name=".BuzAppShell"
        android:allowBackup="false"
        android:icon="@mipmap/buz_launcher"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:requestLegacyExternalStorage="true"
        android:roundIcon="@mipmap/buz_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true"
        tools:replace="android:allowBackup">

        <meta-data
            android:name="HW_APP_ID"
            android:value="${HW_APP_ID}" /> <!-- android:networkSecurityConfig="@xml/network_security_config" -->
        <activity
            android:name=".demo.DemoActivity"
            android:exported="false" />
        <activity android:name=".demo.IMTestActivity" />
        <activity android:name=".demo.TakePhotoDemoActivity" />
        <activity android:name=".demo.MediaUploadDemoActivity" />
        <activity android:name=".demo.BuzPlayerViewDemoActivity" />
        <activity android:name=".demo.SinglePagePlayerDemoActivity" />
        <activity android:name=".demo.PureExoplayerDemoActivity" />
        <activity android:name=".demo.BuzMediaCacheDemoActivity" />
        <activity android:name=".demo.HyperlinkDemoActivity" />
        <activity android:name=".demo.RegisterV2Activity" /> <!-- activity for test -->
        <activity
            android:name=".demo.DownloadMediaButtonDemoActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".demo.EmptyDataDemoActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity android:name=".demo.FromHtmlDemoActivity" />
        <activity android:name=".demo.VoiceEmojiTextViewDemoActivity" />
        <activity android:name=".demo.DialogDemoActivity" />
        <activity android:name=".demo.InteractiveDialogDemoActivity" />
        <activity android:name=".demo.homepage.HomeDemoActivity" />
        <activity android:name=".demo.FontTestDemoActivity" />
        <activity android:name=".demo.DemoVibratorActivity" />
        <activity android:name=".demo.giphy.GiphyAPIDemoActivity" />
        <activity android:name=".demo.VEFlowTestActivity" />
        <activity android:name=".demo.AlarmScheduleDemoActivity" />
        <activity android:name=".demo.videocall.FlexBoxLayoutDemoActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"/>
        <activity
            android:name=".demo.videocall.PipDemoActivity"
            android:theme="@style/RouterHostTheme"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:supportsPictureInPicture="true" />

    </application>

</manifest>