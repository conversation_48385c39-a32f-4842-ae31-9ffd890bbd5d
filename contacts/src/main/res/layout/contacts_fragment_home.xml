<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_0A0A0A"
    tools:ignore="SpUsage">

    <androidx.legacy.widget.Space
        android:id="@+id/spaceStatusBar"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_height="@dimen/status_bar_height" />

    <androidx.legacy.widget.Space
        android:id="@+id/spaceTitleBar"
        android:layout_width="0dp"
        android:layout_height="@dimen/title_bar_height"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/spaceStatusBar" />

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftvBack"
        style="@style/iconfont_24"
        app:autoRTL="true"
        android:layout_marginStart="4dp"
        android:text="@string/ic_back"
        android:textColor="@color/text_white_main"
        app:layout_constraintBottom_toBottomOf="@+id/spaceTitleBar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/spaceTitleBar" />

    <TextView
        android:id="@+id/tvTitle"
        style="@style/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/contacts"
        android:textColor="@color/text_white_main"
        app:layout_constraintBottom_toBottomOf="@+id/spaceTitleBar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/spaceTitleBar" />

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftvScan"
        style="@style/iconfont_24"
        android:layout_marginEnd="4dp"
        android:text="@string/ic_scan"
        android:textColor="@color/text_white_main"
        app:layout_constraintBottom_toBottomOf="@+id/spaceTitleBar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/spaceTitleBar" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvContent"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/spaceTitleBar"
        tools:itemCount="10"
        tools:listitem="@layout/contacts_item_common" />

    <View
        android:id="@+id/viewSearchBg"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/overlay_page_mask"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/spaceTitleBar"
        tools:visibility="visible" />

    <FrameLayout
        android:id="@+id/flSearch"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/spaceTitleBar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clSearch"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingStart="20dp"
            android:paddingEnd="10dp">

            <EditText
                android:id="@+id/etSearch"
                style="@style/body"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_marginTop="10dp"
                android:layout_gravity="center_vertical"
                android:background="@drawable/common_rect_overlay_white_6_radius_8"
                android:gravity="start|center_vertical"
                android:hint="@string/search_your_friends_and_group"
                android:imeOptions="actionDone"
                android:inputType="text"
                android:maxLength="50"
                android:maxLines="1"
                android:paddingStart="48dp"
                android:paddingEnd="10dp"
                android:textColor="@color/text_white_main"
                android:textColorHint="@color/text_white_disable"
                android:textCursorDrawable="@drawable/common_edittext_cursor"
                app:layout_constraintEnd_toStartOf="@+id/tvCancel"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_goneMarginEnd="10dp" />

            <com.interfun.buz.common.widget.view.IconFontTextView
                android:id="@+id/iftvSearch"
                style="@style/iconfont_base"
                android:layout_width="18dp"
                android:layout_height="18dp"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="20dp"
                android:text="@string/ic_search_input"
                android:textColor="@color/text_white_secondary"
                android:textSize="18dp"
                app:pressEffect="false"
                app:layout_constraintBottom_toBottomOf="@+id/etSearch"
                app:layout_constraintStart_toStartOf="@+id/etSearch"
                app:layout_constraintTop_toTopOf="@+id/etSearch" />

            <com.interfun.buz.common.widget.view.IconFontTextView
                android:id="@+id/iftvClear"
                style="@style/iconfont_base"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:gravity="center"
                android:text="@string/ic_clear_input_solid"
                android:textColor="@color/text_white_secondary"
                android:textSize="18dp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@+id/etSearch"
                app:layout_constraintEnd_toEndOf="@+id/etSearch"
                app:layout_constraintTop_toTopOf="@+id/etSearch"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tvCancel"
                style="@style/body"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:gravity="center"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                android:text="@string/cancel"
                android:textColor="@color/text_white_default"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/etSearch"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/etSearch"
                tools:visibility="visible" />

            <com.interfun.buz.common.widget.view.BuzBannerTips
                android:id="@+id/bannerTips"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:layout_marginEnd="10dp"
                android:paddingVertical="8dp"
                android:visibility="gone"
                app:text="@string/ftue_v3_searchBuzId"
                app:bannerTips_icon="@string/ic_directory_solid"
                app:layout_constraintTop_toBottomOf="@id/etSearch"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                tools:visibility="visible"/>

            <com.interfun.buz.base.widget.round.RoundConstraintLayout
                android:id="@+id/clBindPhone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingEnd="12dp"
                android:paddingStart="16dp"
                android:paddingVertical="12dp"
                android:layout_marginTop="12dp"
                android:layout_marginEnd="10dp"
                android:visibility="gone"
                tools:visibility="visible"
                app:layout_goneMarginTop="20dp"
                app:round_radius="@dimen/guide_layout_radius_min"
                android:background="@color/overlay_grey_10"
                app:layout_constraintTop_toBottomOf="@id/bannerTips">

                <TextView
                    android:id="@+id/tvBindPhoneTips"
                    style="@style/body"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="12dp"
                    android:text="@string/verifyPhoneNumberToMakeItEasierForYourFriendsToFindYou"
                    android:textColor="@color/text_white_default"
                    app:layout_constraintEnd_toStartOf="@id/tvCloseTips"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <com.interfun.buz.common.widget.view.IconFontTextView
                    android:id="@+id/tvCloseTips"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/ic_exit"
                    android:textColor="@color/text_white_default"
                    android:textSize="16sp"
                    app:layout_constraintBottom_toBottomOf="@id/tvBindPhoneTips"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/tvBindPhoneTips"
                    app:layout_constraintTop_toTopOf="@id/tvBindPhoneTips" />

            </com.interfun.buz.base.widget.round.RoundConstraintLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>