<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/clRoot"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="70dp"
    android:paddingVertical="10dp"
    tools:background="@color/color_0A0A0A">

    <com.interfun.buz.common.widget.view.PortraitImageView
        android:id="@+id/ivPortrait"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:layout_marginStart="20dp"
        android:src="@drawable/common_user_default_portrait_round"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/roundOnlineBg"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_marginEnd="-2dp"
        android:layout_marginBottom="-2dp"
        android:background="@drawable/common_oval_0a0a0a_background"
        app:layout_constraintBottom_toBottomOf="@id/ivPortrait"
        app:layout_constraintEnd_toEndOf="@id/ivPortrait" />

    <View
        android:id="@+id/roundOnline"
        android:layout_width="8dp"
        android:layout_height="8dp"
        android:background="@drawable/common_oval_basic_primary"
        app:layout_constraintBottom_toBottomOf="@id/roundOnlineBg"
        app:layout_constraintEnd_toEndOf="@id/roundOnlineBg"
        app:layout_constraintStart_toStartOf="@+id/roundOnlineBg"
        app:layout_constraintTop_toTopOf="@+id/roundOnlineBg" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/gOnline"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        tools:visibility="visible"
        app:constraint_referenced_ids="roundOnlineBg,roundOnline"/>


    <TextView
        android:id="@+id/tvContent"
        style="@style/main_body"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="12dp"
        app:layout_goneMarginEnd="80dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textDirection="locale"
        android:textColor="@color/text_white_main"
        app:layout_constraintBottom_toTopOf="@+id/tvDesc"
        app:layout_constraintEnd_toStartOf="@+id/btnCommon"
        app:layout_constraintStart_toEndOf="@+id/ivPortrait"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Main Content" />

    <TextView
        android:id="@+id/tvDesc"
        style="@style/text_body_small"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="2dp"
        android:layout_marginEnd="12dp"
        app:layout_goneMarginEnd="80dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/text_white_secondary"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/btnCommon"
        app:layout_constraintStart_toEndOf="@+id/ivPortrait"
        app:layout_constraintTop_toBottomOf="@+id/tvContent"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="description"
        tools:visibility="visible" />

    <com.interfun.buz.common.widget.button.CommonButton
        android:id="@+id/btnCommon"
        android:layout_width="wrap_content"
        android:layout_height="30dp"
        android:layout_marginTop="10dp"
        android:paddingHorizontal="10dp"
        android:visibility="invisible"
        app:iconSize="16dp"
        app:layout_constraintEnd_toStartOf="@+id/iftvDelete"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_goneMarginEnd="20dp"
        app:textSize="14dp"
        app:type="primary_small"
        tools:background="@drawable/common_r8_basic_primary"
        tools:ignore="SpUsage"
        tools:layout_width="80dp"
        tools:visibility="visible" />

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftvDelete"
        style="@style/iconfont_base"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginTop="5dp"
        android:layout_marginEnd="8dp"
        android:gravity="center"
        android:text="@string/ic_exit"
        android:textColor="@color/text_white_main"
        android:textSize="14dp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="SpUsage"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>