package com.interfun.buz.contacts.viewmodel

import android.util.Log
import com.buz.idl.liveplace.request.RequestShareLivePlaceToIM
import com.buz.idl.liveplace.response.ResponseShareLivePlaceToIM
import com.buz.idl.liveplace.service.BuzNetLivePlaceServiceClient
import com.interfun.buz.base.ktx.launchIO
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.common.constants.AddFriendPageSource
import com.interfun.buz.common.ktx.isOfficial
import com.interfun.buz.common.ktx.isRobot
import com.interfun.buz.common.net.withConfig
import com.interfun.buz.contacts.entity.ContactsItemBean
import com.interfun.buz.contacts.entity.ContactsItemType
import com.interfun.buz.domain.im.social.usecase.BuzConversationListUseCase
import com.interfun.buz.social.repo.BotRepository
import com.interfun.buz.social.repo.GroupRepository
import com.interfun.buz.social.repo.OfficialAccountRepository
import com.interfun.buz.social.repo.UserOnlineStatusRepository
import com.interfun.buz.social.repo.UserRepository
import com.lizhi.itnet.lthrift.service.ITResponse
import com.lizhi.itnet.lthrift.service.MethodCallback
import dagger.hilt.android.AndroidEntryPoint
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class ShareLinkViewModel  @Inject constructor(
    private val userRepository: UserRepository,
    private val groupRepository: GroupRepository,
    private val botRepository: BotRepository,
    private val officialRepository: OfficialAccountRepository,
    private val conversationListUseCase: BuzConversationListUseCase,
    private val userOnlineStatusRepository: UserOnlineStatusRepository,
): ContactsHomeViewModel(userRepository, groupRepository, botRepository, officialRepository, conversationListUseCase,userOnlineStatusRepository){

    companion object {
        private const val TAG = "ShareLinkViewModel"
    }

    private val itemHorizontalShareList by lazy { ContactsItemBean(type = ContactsItemType.HorizontalShareList) }
    var source = AddFriendPageSource.RegisterInviteDialog.value

    override fun requireOriginData(isFirstRequest: Boolean) {
        fullList.setList(buildFullList())

        if (isListHaveData()) {
            Log.i("requireOriginData2",fullList.list.size.toString())
            notifyListChange(fullList.list)
        }

        launchIO {
            refreshAllCommonData(isFirstRequest, TAG)
            fullList.clear()
            fullList.setList(buildFullList())
            notifyListChange(fullList.list)
        }
    }

    private fun isListHaveData():Boolean{
        return !(friendList.isEmpty()) || !(groupList.isEmpty())
    }

    override fun buildFullList(): MutableList<ContactsItemBean> = mutableListOf<ContactsItemBean>().apply {
        add(itemSpaceDP10)

        add(itemHorizontalShareList.apply { extra.source = source })

        if (chatList.notEmpty()) {

            val filteredChatList = chatList.list.filter { item ->
                item.userInfo?.isOfficial != true && item.userInfo?.isRobot != true
            }

            add(itemSpaceDP10)
            add(itemTitleRecentChats)
            addAll(filteredChatList)
        }
    }


    private val client by lazy { BuzNetLivePlaceServiceClient().withConfig() }
    fun sendShareReq(item: ContactsItemBean, channelId: Long) {
        // Initialize target lists as null by default
        val targetUserIdList: List<Long>? = item.userInfo?.userId?.let { listOf(it) }
        val targetGroupIdList: List<Long>? = item.groupInfo?.groupId?.let { listOf(it) }

        // Create a request object with the initialized lists
        val request = RequestShareLivePlaceToIM(
            channelId = channelId,
            targetUserIds = targetUserIdList,
            targetGroupIds = targetGroupIdList
        )

        client.shareLivePlaceToIM(
            request = request,
            callback = object : MethodCallback<ITResponse<ResponseShareLivePlaceToIM>> {
                override fun onSuccess(result: ITResponse<ResponseShareLivePlaceToIM>?) {
                    logInfo(
                        "ShareLinkDialogFragment",
                        "Share succeeded with result: %s",
                        result?.msg
                    )
                }

                override fun onError(e: Exception?) {
                    logInfo("ShareLinkDialogFragment", "Share failed with error: %s", e?.message)
                }
            }
        )

    }

}