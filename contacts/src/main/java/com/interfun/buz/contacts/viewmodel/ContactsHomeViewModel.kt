package com.interfun.buz.contacts.viewmodel

import androidx.core.text.isDigitsOnly
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.buz.idl.group.bean.GroupInfo
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.bean.Resp.Success
import com.interfun.buz.common.bean.user.FriendApplySource
import com.interfun.buz.common.constants.AddFriendSource
import com.interfun.buz.common.constants.ProfileSource
import com.interfun.buz.common.database.UserDatabase
import com.interfun.buz.common.database.entity.UserRelationInfo
import com.interfun.buz.common.ktx.*
import com.interfun.buz.common.manager.ABTestManager
import com.interfun.buz.common.manager.ChannelStatusManager
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.manager.cache.user.UserRelationCacheManager
import com.interfun.buz.common.manager.user.FriendRequestCount
import com.interfun.buz.common.manager.user.FriendRequestCountManager
import com.interfun.buz.common.service.ContactsService
import com.interfun.buz.common.utils.parse
import com.interfun.buz.common.widget.dialog.EnableNotificationPromptDialog
import com.interfun.buz.contacts.R
import com.interfun.buz.contacts.entity.*
import com.interfun.buz.contacts.utils.ContactsTracker
import com.interfun.buz.domain.im.social.usecase.BuzConversationListUseCase
import com.interfun.buz.onair.bean.isLivePlaceOpen
import com.interfun.buz.social.repo.BotRepository
import com.interfun.buz.social.repo.GroupRepository
import com.interfun.buz.social.repo.OfficialAccountRepository
import com.interfun.buz.social.repo.UserOnlineStatusRepository
import com.interfun.buz.social.repo.UserRepository
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import com.yibasan.lizhifm.sdk.platformtools.ResUtil
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import javax.inject.Inject

/**
 * <AUTHOR>
 * @date 2023/5/6
 * @desc
 */
@HiltViewModel
open class ContactsHomeViewModel  @Inject constructor(
    private val userRepository: UserRepository,
    private val groupRepository: GroupRepository,
    private val botRepository: BotRepository,
    private val officialRepository: OfficialAccountRepository,
    private val conversationListUseCase: BuzConversationListUseCase,
    private val userOnlineStatusRepository: UserOnlineStatusRepository,
): ContactCommonDataViewModel(userRepository, groupRepository, botRepository, officialRepository,conversationListUseCase, userOnlineStatusRepository) {

    companion object {
        const val TAG = "ContactsHomeViewModel"
    }

    private val mutex = Mutex()

    protected val itemSpaceDP10 = ContactsItemBean.generateSpace(10.dp)
    protected val itemSpaceDP20 = ContactsItemBean.generateSpace(20.dp)
    private val itemEntryAddFriend = ContactsItemBean(type = ContactsItemType.EntryAddFriends)
    private val itemEntryCreateGroup = ContactsItemBean(type = ContactsItemType.EntryCreateGroup)
    private val itemEntryRequest = ContactsItemBean(type = ContactsItemType.EntryRequests)
    protected val itemSearchHint = ContactsItemBean(type = ContactsItemType.SearchHint)
    private val itemTitleRecommend = ContactsItemBean.generateTitle(R.string.contacts_add_your_contacts)
    open val itemTitleFriends = ContactsItemBean.generateTitle(R.string.friends)
    open val itemTitleRecentChats = ContactsItemBean.generateTitle(R.string.home_search_recent_chats)
    protected val itemTitleGroup = ContactsItemBean.generateTitle(R.string.group_v2)
    private val itemTitleRegistered = ContactsItemBean.generateTitle(R.string.contacts_contacts_on_buz)
    private val itemTitleUnRegistered = ContactsItemBean.generateTitle(R.string.contacts_invite_from_contacts)
    private val itemTitleOfficialAccount = ContactsItemBean.generateTitle(R.string.contacts_buz_official_account)
    protected val fullList = ContactsListContainer()
    var searchKeyWord:String? = null

    val keySearchFinishedLiveData = MutableLiveData<Boolean>()
    val isSearchingStateFlow = MutableStateFlow(false)
    val scrollToFirstLiveData = MutableLiveData<Boolean>()
    val itemChangeLiveData = MutableLiveData<UpdateItemInfo>()
    val listLiveData = MutableLiveData<List<ContactsItemBean>>()
    val livePlaceList = ChannelStatusManager.convChannelInfoFlow.map { infoMap ->
        infoMap.mapNotNull { (targetId, channelInfo) ->
            if (channelInfo.isLivePlaceOpen()) {
                if (channelInfo.convType == IM5ConversationType.PRIVATE.value) {
                    val userComposite =
                        userRepository.getUserCompositeFromCache(channelInfo.convTargetId)
                    OnairContactItem(
                        channelInfo.convTargetId,
                        false,
                        userComposite?.user?.userName ?: "${channelInfo.convTargetId}",
                        userComposite?.user?.toUserPortrait()
                    )
                } else {
                    val groupInfo = groupRepository.getGroupFromCache(channelInfo.convTargetId)
                    OnairContactItem(
                        channelInfo.convTargetId,
                        true,
                        groupInfo?.groupName ?: "${channelInfo.convTargetId}",
                        groupInfo?.toGroupPortrait()
                    )
                }
            } else {
                null
            }
        }
    }
    private val listContainer = ContactsListContainer()
    private var requireOriginDataJob: Job? = null
    private val hasNewAiFlow = botRepository.hasNewAiInMarket()
        .stateIn(viewModelScope, SharingStarted.Eagerly, false)

    fun requestAddFriend(
        source: Int,
        eventSource: Int = AddFriendSource.AddFriendPage.value,
        userId: Long,
        isFromBlindBox: Boolean = false
    ){
        requestAddFriend(userRepository, source, eventSource, userId, isFromBlindBox)
    }


    open fun requireOriginData(isFirstRequest: Boolean) {
        if (requireOriginDataJob?.isActive == true){
            requireOriginDataJob?.cancel()
        }
        requireOriginDataJob = launch(Dispatchers.IO) {
            refreshAllCommonData(isFirstRequest, TAG)
            fullList.setList(buildFullList())
            if (isSearchingStateFlow.value.not()) {
                logInfo(TAG,"getHomeList notifyListChange,size:${fullList.list.size}")
                notifyListChange(fullList.list)
            } else {
                filterByKeyWord(searchKeyWord, false)
            }
            if (isFirstRequest) {
                ContactsTracker.onRecommendListView(getRecommendListSize())
            }
        }
    }

    open fun buildFullList() = mutableListOf<ContactsItemBean>().apply {
        add(itemSpaceDP10)
        add(itemEntryAddFriend)
        add(itemEntryCreateGroup)
        if (ABTestManager.aiMarketplaceInContact) {
            add(
                ContactsItemBean(
                    type = ContactsItemType.EntryAIMarketsPlace,
                    extra = ContactsExtra(hasNewAi = hasNewAiFlow.value)
                )
            )
        }

        add(itemEntryRequest.apply {
            extra.count = FriendRequestCountManager.countFlow.value
        })
        if (recommendList.notEmpty()) {
            add(itemSpaceDP10)
            add(itemTitleRecommend)
            val itemRecommendList = ContactsItemBean(type = ContactsItemType.RecommendList).apply {
                val list = recommendList.list.take(10).filter { it.isNotNull() }.toMutableList().onEach {
                    it.type = ContactsItemType.RecommendHome
                }
                list.add(ContactsItemBean(type = ContactsItemType.RecommendEntry))
                extra.recommendList = list
            }
            add(itemRecommendList)
        }

        if (friendList.notEmpty()) {
            add(itemSpaceDP10)
            add(itemTitleFriends)
            addAll(friendList.list.filter { it.userInfo?.isRobot!=true })
        }

        if (groupList.notEmpty()) {
            add(itemSpaceDP10)
            add(itemTitleGroup)
            addAll(groupList.list)
        }

        if (officialAccountList.notEmpty()) {
            add(itemSpaceDP10)
            add(itemTitleOfficialAccount)
            addAll(officialAccountList.list)
        }

        if (unRegisteredList.notEmpty()) {
            add(itemSpaceDP10)
            add(itemTitleUnRegistered)
            addAll(unRegisteredList.list)
        }

    }

    open fun filterByKeyWord(keyWord: String?,includeAI:Boolean = true) {
        searchKeyWord = keyWord ?: ""
        viewModelScope.launch(Dispatchers.IO) {
            if (keyWord.isNullOrEmpty()) {
                isSearchingStateFlow.emitInScopeIfDifferent(viewModelScope, false)
                logInfo(TAG,"filterByKeyWord,notifyListChange,keyword:$keyWord")
                notifyListChange(fullList.list, true)
            } else {
                if (keyWord.isBlank()) return@launch
                isSearchingStateFlow.emitInScopeIfDifferent(viewModelScope, true)
                val searchList = buildFilterList(keyWord,includeAI)
                logInfo(TAG,"filterByKeyWord,notifyListChange,keyword:$keyWord")
                notifyListChange(searchList, true)
            }
        }
    }

    private fun buildFilterList(keyWord: String,includeAI: Boolean = true) = mutableListOf<ContactsItemBean>().apply {
        var  filterFriendList = friendList.filterUserInfo(keyWord)
        if (includeAI.not()){
            filterFriendList = filterFriendList.filter { it.userInfo?.isRobot!=true }
        }
        val filterGroupList = groupList.filterGroupByKeyWord(keyWord)
        val filterRegisteredList = registeredList.filterUserInfo(keyWord)
        val filterUnRegisteredList = unRegisteredList.filterByKeyWord(keyWord)
        val filterOfficialAccountList = officialAccountList.filterByKeyWord(keyWord)
        add(itemSpaceDP20)
        add(itemSearchHint)
        if (filterFriendList.notEmpty()) {
            add(itemSpaceDP10)
            add(itemTitleFriends)
            addAll(filterFriendList)
        }
        if (filterGroupList.notEmpty()) {
            add(itemSpaceDP10)
            add(itemTitleGroup)
            addAll(filterGroupList)
        }
        if (filterRegisteredList.notEmpty()) {
            add(itemSpaceDP10)
            add(itemTitleRegistered)
            addAll(filterRegisteredList)
        }
        if (filterOfficialAccountList.notEmpty()) {
            add(itemSpaceDP10)
            add(itemTitleOfficialAccount)
            addAll(filterOfficialAccountList)
        }
        if (filterUnRegisteredList.notEmpty()) {
            add(itemSpaceDP10)
            add(itemTitleUnRegistered)
            addAll(filterUnRegisteredList)
        }
    }

    fun logClickInviteUnRegisterFriend() {
        viewModelScope.launch {
            ContactsTracker.onClickInviteUnregisterFriend(userRepository.getFriendsSizeFromCache())
        }
    }

    fun searchByKeyWord(keyWord: String,activity:FragmentActivity,includeAI: Boolean = true) = doInMutex(mutex) {
        val searchList = getSearchList(keyWord)
        val firstResult = searchList?.firstOrNull()

        filterByKeyWord(keyWord,includeAI)
        if (firstResult.isNull() || firstResult?.userInfo?.userId.isNull()) {
            showNoSearchResultDialog(activity)
            keySearchFinishedLiveData.postValue(true)
            return@doInMutex
        }

        if (firstResult?.userInfo?.userId == UserSessionManager.uid){
            toast(R.string.the_username_belong_to_yourself.asString())
            keySearchFinishedLiveData.postValue(true)
            return@doInMutex
        }

        if (activity.lifecycle.currentState.isAtLeast(Lifecycle.State.STARTED)){
            var contactName: String? = null
            if (keyWord.isDigitsOnly()) {
                //如果用户输入的都是数字，就按默认是手机号规则去查询通讯录是否是通讯录好友
                val contactsDao = UserDatabase.currInstance?.getContactsDao()
                val contactsBean = contactsDao?.queryContactsByPhone(keyWord)
                if (contactsBean != null) {
                    contactName = contactsBean.displayName
                }
            }
            routerServices<ContactsService>().value?.getProfileDialog(
                userId = firstResult!!.userInfo.userId!!,
                source = FriendApplySource.search,
                contactName = contactName,
                businessId = null,
                trackerSource = ProfileSource.SEARCH_USER.source
            )?.showDialog(activity)
        }
        keySearchFinishedLiveData.postValue(true)
    }

    fun updateFriendInfo(userId: Long) = doInMutex(mutex) {
        val userInfo = UserRelationCacheManager.getUserRelationInfoByUid(userId)
        if (userInfo.isNull()) return@doInMutex
        val newContactsItemBean = userInfo!!.transform2ContactsItemBean()
        listContainer.update(
            predicate = { it.type == ContactsItemType.Normal && it.userInfo?.userId == userInfo.userId },
            action = { pos, item ->
                item.update(newContactsItemBean)
                itemChangeLiveData.postValue(UpdateItemInfo(item.targetId,pos,ContactsPayloadType.UpdateAll))
            })

        friendList.update(
            userId,
            action = { _, item ->
                item.update(newContactsItemBean)
            })
    }

    private fun updateRecommendRelation(userId: Long, userInfo: UserRelationInfo?) = doInMutex(mutex) {
        if (userInfo?.isFriend == true) {
            recommendList.removeIf { it.targetId == userId }
            listContainer.update(
                predicate = { it.type == ContactsItemType.RecommendList },
                action = { pos, item ->
                    item.extra.recommendList?.removeIf { it.targetId == userId }
                    itemChangeLiveData.postValue(UpdateItemInfo(item.targetId,pos,ContactsPayloadType.UpdateAll))
                })
            return@doInMutex
        }
        recommendList.update(
            userId,
            action = { _, item ->
                item.extra.isLoading = false
                if (userInfo != null) {
                    item.userInfo = userInfo
                }
            })
        listContainer.update(
            predicate = {
                it.targetId == userId || it.type == ContactsItemType.RecommendList
            },
            action = { pos, item ->
                if (item.type == ContactsItemType.RecommendList) {
                    val recommendItem =
                        item.extra.recommendList?.find { it.targetId == userId } ?: return@update
                    recommendItem.extra.isLoading = false
                    if (userInfo != null) {
                        item.userInfo = userInfo
                    }
                    itemChangeLiveData.postValue(UpdateItemInfo(item.targetId,pos , ContactsPayloadType.UpdateAll))
                } else {
                    item.extra.isLoading = false
                    if (userInfo != null) {
                        item.userInfo = userInfo
                    }
                    itemChangeLiveData.postValue(UpdateItemInfo(item.targetId,pos , ContactsPayloadType.UpdateUserRelation))
                }
            })
    }

    fun updateRelation(userId: Long) = doInMutex(mutex) {
        val userInfo = UserRelationCacheManager.getUserRelationInfoByUid(userId)
        if (UserRelationCacheManager.isUserOfficial(userId)) {
            listContainer.update(
                predicate = { it.targetId == userId },
                action = { pos, item ->
                    item.extra.isLoading = false
                    item.userInfo = userInfo
                    itemChangeLiveData.postValue(UpdateItemInfo(item.targetId,pos, ContactsPayloadType.UpdateUserRelation))
                }
            )
        } else {
            updateRecommendRelation(userId, userInfo)
        }
    }

    fun updateGroup(groupInfo: GroupInfo) = doInMutex(mutex) {
        listContainer.update(
            groupInfo.groupBaseInfo.groupId,
            action = { pos, item ->
                val info = groupInfo.convertGroupInfoBean()
                item.content = info.groupName
                item.desc = ResUtil.getString(R.string.group_members, info.memberNum)
                item.groupInfo = info
                itemChangeLiveData.postValue(UpdateItemInfo(item.targetId,pos, ContactsPayloadType.UpdateAll))
            })
        groupList.update(
            groupInfo.groupBaseInfo.groupId,
            action = { _, item ->
                val info = groupInfo.convertGroupInfoBean()
                item.content = info.groupName
                item.desc = ResUtil.getString(R.string.group_members, info.memberNum)
                item.groupInfo = info
            })
    }

    fun updateRequestsCount(count: FriendRequestCount) = doInMutex(mutex) {
        listContainer.update(
            predicate = {
                it.type == ContactsItemType.EntryRequests
            }, action = { pos, item ->
                item.extra.count = count
                itemChangeLiveData.postValue(UpdateItemInfo(item.targetId,pos,ContactsPayloadType.UpdateAll))
            })
    }

    fun removeGroup(groupId: Long) = doInMutex(mutex) {
        groupList.removeIf { it.targetId == groupId }
        listContainer.removeIf { it.targetId == groupId }
        log(TAG,"removeGroup,size:${listContainer.list.size}")
        listLiveData.postValue(listContainer.list)
    }

    fun requestIgnoreRecommend(userId: Long) = doInMutex(mutex) {
        removeRecommendByUserId(userId)
        requestIgnoreRecommendFriends(userId)
    }

    open fun requestAcceptRecommend(userId: Long) {
        viewModelScope.launch(Dispatchers.IO) {
            val resp = userRepository.agreeFriendApply(userId)
            resp.prompt?.parse()
            if (resp is Success) {
                doInMutex(mutex) {
                    removeRecommendByUserId(userId)
                }
                toastRegularCorrect(R.string.friend_request_add_success)
                //旧代码迁移，旧代码是这样搞的，理论上不应该这样处理，viewmodel里面做ui操作，但是这期改动太大了，先保留
                delay(500)
                EnableNotificationPromptDialog.showIfValid()
            }else {
                if (resp.prompt == null){
                    toastNetworkErrorTips()
                }
            }
        }
    }

    private suspend fun removeRecommendByUserId(userId: Long){
        recommendList.removeIf { it.targetId == userId }
        listContainer.update({
            it.type == ContactsItemType.RecommendList
        }, { pos, item ->
            item.extra.recommendList?.removeIf { it.targetId == userId }
            itemChangeLiveData.postValue(UpdateItemInfo(item.targetId,pos,ContactsPayloadType.UpdateAll))
        })
        if (recommendList.isEmpty()) {
            fullList.setList(buildFullList())
            logInfo(TAG,"removeRecommendByUserId,notifyListChange,size:${fullList.list.size}")
            notifyListChange(fullList.list)
        }
    }

    private fun getRecommendListSize() = recommendList.size.minOf(10)

    protected fun notifyListChange(list: List<ContactsItemBean>, needScrollToFirst: Boolean = false) =
        doInMutex(mutex) {
            listContainer.setList(list)
            logInfo(TAG,"notifyListChange,listSize:${list.size}")
            listLiveData.postValue(listContainer.list)
            if (needScrollToFirst) {
                scrollToFirstLiveData.postValue(true)
            }
        }
}

data class UpdateItemInfo(
    val targetId:Long,
    val pos:Int,
    val updateType:ContactsPayloadType
)