package com.interfun.buz.contacts.viewmodel

import android.Manifest.permission
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.base.ktx.doInMutex
import com.interfun.buz.base.ktx.dp
import com.interfun.buz.base.ktx.emitInScopeIfDifferent
import com.interfun.buz.base.ktx.isNull
import com.interfun.buz.base.ktx.isPermissionGranted
import com.interfun.buz.base.ktx.launch
import com.interfun.buz.base.ktx.launchIO
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.base.ktx.notEmpty
import com.interfun.buz.base.ktx.toast
import com.interfun.buz.base.utils.PinyinUtils
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.bean.Resp.Success
import com.interfun.buz.common.bean.user.BuzUserRelationValue
import com.interfun.buz.common.bean.user.FriendApplySource
import com.interfun.buz.common.constants.AddFriendPageSource
import com.interfun.buz.common.constants.AddFriendSource
import com.interfun.buz.common.constants.ProfileSource
import com.interfun.buz.common.database.UserDatabase
import com.interfun.buz.common.ktx.isFriend
import com.interfun.buz.common.ktx.toastNetworkErrorTips
import com.interfun.buz.common.ktx.toastRegularCorrect
import com.interfun.buz.common.ktx.uid
import com.interfun.buz.common.manager.ABTestManager
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.manager.cache.user.UserRelationCacheManager
import com.interfun.buz.common.service.ContactsService
import com.interfun.buz.common.utils.parse
import com.interfun.buz.common.widget.dialog.EnableNotificationPromptDialog
import com.interfun.buz.contacts.R
import com.interfun.buz.contacts.entity.ContactsItemBean
import com.interfun.buz.contacts.entity.ContactsItemType
import com.interfun.buz.contacts.entity.ContactsListContainer
import com.interfun.buz.contacts.entity.ContactsPayloadType
import com.interfun.buz.contacts.utils.ContactsManager
import com.interfun.buz.domain.im.social.usecase.BuzConversationListUseCase
import com.interfun.buz.social.repo.BotRepository
import com.interfun.buz.social.repo.GroupRepository
import com.interfun.buz.social.repo.OfficialAccountRepository
import com.interfun.buz.social.repo.UserOnlineStatusRepository
import com.interfun.buz.social.repo.UserRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import java.util.concurrent.CopyOnWriteArrayList
import javax.inject.Inject
import kotlin.collections.plus

/**
 * <AUTHOR>
 * @date 2023/5/6
 * @desc Host:
 * [com.interfun.buz.contacts.view.fragment.ContactsAddFriendsFragment]
 */
@HiltViewModel
class ContactsAddFriendViewModel @Inject constructor(
    private val userRepository: UserRepository,
    private val groupRepository: GroupRepository,
    private val botRepository: BotRepository,
    private val officialRepository: OfficialAccountRepository,
    private val conversationUseCase : BuzConversationListUseCase,
    private val userOnlineStatusRepository: UserOnlineStatusRepository,
) : ContactCommonDataViewModel(userRepository, groupRepository, botRepository, officialRepository, conversationUseCase, userOnlineStatusRepository) {

    companion object {
        const val TAG = "ContactsAddFriendViewModel"
    }

    private val mutex = Mutex()

    private val itemSpaceDP10 by lazy { ContactsItemBean.generateSpace(10.dp) }
    private val itemSpaceDP20 by lazy { ContactsItemBean.generateSpace(20.dp) }
    private val itemSpaceDP40 by lazy { ContactsItemBean.generateSpace(40.dp) }
    private val itemSpaceDP60 by lazy { ContactsItemBean.generateSpace(60.dp) }
    private val itemHorizontalShareList by lazy { ContactsItemBean(type = ContactsItemType.NewHorizontalShareList) }
    private val itemVerticalShareList by lazy { ContactsItemBean(type = ContactsItemType.NewVerticalShareList) }
    private val itemNoPermission by lazy { ContactsItemBean(type = ContactsItemType.NoPermission) }
    private val itemSearchHint by lazy { ContactsItemBean(type = ContactsItemType.SearchHint) }
    private val itemTitleContactsOnBuz by lazy { ContactsItemBean.generateTitle(R.string.contacts_contacts_on_buz) }
    private val itemTitleMightKnowUsers by lazy { ContactsItemBean.generateTitleWithIcon(R.string.might_know_title) }
    private val itemTitleInviteFromContacts by lazy { ContactsItemBean.generateTitle(R.string.contacts_invite_from_contacts) }
    private val itemTitleBuzOfficialAccount by lazy { ContactsItemBean.generateTitle(R.string.contacts_buz_official_account) }

    private val fullList = ContactsListContainer()
    private var requireDataJob: Job? = null
    private var waitingContactsSyncJob: Job? = null
    private var keyword = ""

    val letterListLiveData = MutableLiveData<List<String>>()
    val isSearchingStateFlow = MutableStateFlow(false)
    val showLoadingLiveData = MutableLiveData<Boolean>()
    val scrollToFirstLiveData = MutableLiveData<Boolean>()
    val itemChangeLiveData = MutableLiveData<Pair<Int, ContactsPayloadType>>()
    val listLiveData = MutableLiveData<List<ContactsItemBean>>()
    val listContainer = ContactsListContainer()
    var source = AddFriendPageSource.Contacts.value
    val isSourceFromInvite get() = source == AddFriendPageSource.RegisterInviteDialog.value

    fun requestAddFriend(
        source: Int,
        eventSource: Int = AddFriendSource.AddFriendPage.value,
        userId: Long,
        isFromBlindBox: Boolean = false
    ){
        requestAddFriend(userRepository, source, eventSource, userId, isFromBlindBox)
    }


    fun requestAddFriendList(needRefreshData: Boolean) = launchIO {
        // Waiting for the ContactsSyncCompleteEvent
        if (isPermissionGranted(permission.READ_CONTACTS) && !ContactsManager.isContactsSyncComplete) {
            if (waitingContactsSyncJob?.isActive == true) {
                waitingContactsSyncJob?.cancel()
            }
            waitingContactsSyncJob = launch(Dispatchers.IO) {
                delay(12000)
                getAddFriendList(needRefreshData)
            }
            return@launchIO
        }
        if (waitingContactsSyncJob?.isActive == true) {
            waitingContactsSyncJob?.cancel()
        }
        getAddFriendList(needRefreshData)
    }

    private suspend fun getAddFriendList(needRefreshData: Boolean) {
        val needRefresh = if (needRefreshData) {
            true
        } else {
            val contactsSize = UserDatabase.currInstance?.getContactsDao()?.getContactsCount() ?: 0
            contactsSize > 0 && registeredList.size + unRegisteredList.size == 0
        }
        if (needRefresh) {
            if (requireDataJob?.isActive == true) {
                requireDataJob?.cancel()
            }
            requireDataJob = launch(Dispatchers.IO) {
                refreshSpecificData(RefreshType.REGISTERED, TAG)
                refreshSpecificData(RefreshType.UNREGISTERED, TAG)
                refreshMightKnowUsers(TAG)
                refreshSpecificData(RefreshType.OFFICIAL_ACCOUNT, TAG)
                updateAddFriendList()
            }
        } else {
            updateAddFriendList()
        }
    }

    private fun updateAddFriendList(){
        val needScrollToFirst = listContainer.list.any { it.type == ContactsItemType.VerticalShareList }
        fullList.setList(buildFullList())
        if (isSearchingStateFlow.value.not()) {
            notifyListChange(fullList.list, needScrollToFirst)
        } else {
            notifyListChange(buildFilterList(keyword), needScrollToFirst)
        }
    }

    /**
     * AB Plan plz check in this doc:
     * https://vocalbeats.sg.larksuite.com/wiki/Qhgsw3RPkiMI2SkN70tlm5fjgO1
     * */
    private fun buildFullList() = mutableListOf<ContactsItemBean>().apply {
        add(itemSpaceDP10)
        val contentList = when (source) {
            AddFriendPageSource.RegisterInviteDialog.value -> buildContentListWhenInvite()
            else -> buildContentList()
        }

        if (contentList.notEmpty()) {
            add(itemHorizontalShareList.apply { extra.source = source })
            addAll(contentList)
        } else {
            add(itemVerticalShareList.apply { extra.source = source })
        }
    }

    private fun buildContentList() = mutableListOf<ContactsItemBean>().apply {
        if (!isPermissionGranted(permission.READ_CONTACTS)) {
            add(itemSpaceDP40)
            add(itemNoPermission)
        } else {
            if (registeredList.notEmpty()) {
                add(itemSpaceDP10)
                add(itemTitleContactsOnBuz)
                val sortRegisteredList = registeredList.list.sortedBy {
                    if (it.userInfo?.serverRelation == BuzUserRelationValue.BEING_FRIEND_REQUEST.value) {
                        0
                    } else {
                        1
                    }
                }
                addAll(sortRegisteredList)
            }
            if (mightKnowUserList.notEmpty()) {
                add(itemSpaceDP10)
                add(itemTitleMightKnowUsers)
                addAll(mightKnowUserList.list)
            }
            val officialAccountInfoList = officialAccountList.list.filter { it.userInfo?.isFriend == false }
            if (officialAccountInfoList.notEmpty()) {
                add(itemSpaceDP10)
                add(itemTitleBuzOfficialAccount)
                addAll(officialAccountInfoList)
            }
            if (unRegisteredList.notEmpty()) {
                add(itemSpaceDP10)
                add(itemTitleInviteFromContacts)
                addAll(unRegisteredList.list)
            }
            updateUnRegisterLetter()
        }
    }

    private fun buildContentListWhenInvite() = mutableListOf<ContactsItemBean>().apply {
        if (!isPermissionGranted(permission.READ_CONTACTS)) {
            return@apply
        }
        val registeredListCopy = registeredList.list.toList()

        val contactList = if (ABTestManager.isAddFriendDialogJapanExperimental) {
            registeredListCopy.sortedBy { PinyinUtils.getNameFirstLetter(it.content) }
        } else {
            (registeredListCopy + unRegisteredList.list).onEach { bean ->
                bean.userInfo?.let { _ ->
                    bean.extra.firstLetter = PinyinUtils.getNameFirstLetter(bean.content)
                }
            }.sortedBy {
                it.extra.firstLetter
            }
        }

        if (contactList.notEmpty()) {
            add(itemSpaceDP10)
            if (ABTestManager.isAddFriendDialogJapanExperimental) {
                add(itemTitleContactsOnBuz)
            }else{
                add(itemTitleInviteFromContacts)
            }
            addAll(contactList)
            if (!ABTestManager.isAddFriendDialogJapanExperimental) {
                contactList.map { it.extra.firstLetter }.distinct().let { letterList ->
                    letterListLiveData.postValue(letterList)
                }
            }
        }

        logInfo(TAG,"buildContentListWhenInvite:${mightKnowUserList.size} ")
        if (ABTestManager.isAddFriendDialogJapanExperimental && mightKnowUserList.notEmpty()) {
            add(itemSpaceDP10)
            add(itemTitleMightKnowUsers)
            addAll(mightKnowUserList.list)
        }

        if (ABTestManager.isAddFriendDialogJapanExperimental && unRegisteredList.notEmpty()) {
            add(itemSpaceDP10)
            add(itemTitleInviteFromContacts)
            addAll(unRegisteredList.list)
            updateUnRegisterLetter()
        }


    }

    private fun updateUnRegisterLetter() {
        val copyOnWriteArrayList = CopyOnWriteArrayList(unRegisteredList.list)
        copyOnWriteArrayList.filterNotNull().map {
            it.extra.firstLetter
        }.distinct().let { letterList ->
            if (unRegisteredList.list.isEmpty()) {
                letterListLiveData.postValue(mutableListOf())
            } else {
                letterListLiveData.postValue(letterList)
            }
        }
    }

    fun getHeaderList() = listOf(
        itemSpaceDP10,
        itemHorizontalShareList.apply { extra.source = source }
    )

    fun filterByKeyWord(keyWord: String?) {
        viewModelScope.launch(Dispatchers.IO) {
            if (keyWord.isNullOrEmpty()) {
                isSearchingStateFlow.emitInScopeIfDifferent(viewModelScope, false)
                notifyListChange(fullList.list, true)
            } else {
                if (keyWord.isBlank()) return@launch
                isSearchingStateFlow.emitInScopeIfDifferent(viewModelScope, true)
                val searchList = buildFilterList(keyWord)
                notifyListChange(searchList, true)
            }
        }
    }

    private fun buildFilterList(keyWord: String) = mutableListOf<ContactsItemBean>().apply {
        keyword = keyWord
        add(itemSpaceDP20)
        add(itemSearchHint)
        when (source) {
            AddFriendPageSource.RegisterInviteDialog.value -> {
                val filterList = fullList.filterUserInfo(keyWord)
                if (filterList.notEmpty()) {
                    add(itemSpaceDP10)
                    add(itemTitleInviteFromContacts)
                    addAll(filterList)
                }
            }

            else -> {
                val filterRegisteredList = registeredList.filterUserInfo(keyWord)
                val filterUnRegisteredList = unRegisteredList.filterByKeyWord(keyWord)
                val filterOfficialAccountList = officialAccountList.filterByKeyWord(keyWord).filter { it.userInfo?.isFriend != true }
                if (filterRegisteredList.notEmpty()) {
                    add(itemSpaceDP10)
                    add(itemTitleContactsOnBuz)
                    addAll(filterRegisteredList)
                }
                if (filterOfficialAccountList.notEmpty()) {
                    add(itemSpaceDP10)
                    add(itemTitleBuzOfficialAccount)
                    addAll(filterOfficialAccountList)
                }
                if (filterUnRegisteredList.notEmpty()) {
                    add(itemSpaceDP10)
                    add(itemTitleInviteFromContacts)
                    addAll(filterUnRegisteredList)
                }
            }
        }
    }

    fun searchByKeyWord(keyWord: String,activity:FragmentActivity) = doInMutex(mutex) {
        val searchList = getSearchList(keyWord)
        val firstResult = searchList?.firstOrNull()
        filterByKeyWord(keyWord)

        if (firstResult.isNull() || firstResult?.userInfo?.userId.isNull()) {
            showNoSearchResultDialog(activity)
            return@doInMutex
        }

        if (firstResult?.userInfo?.userId == UserSessionManager.uid){
            toast(R.string.the_username_belong_to_yourself.asString())
            return@doInMutex
        }

        if (activity.lifecycle.currentState.isAtLeast(Lifecycle.State.STARTED)){
            routerServices<ContactsService>().value?.getProfileDialog(
                userId = firstResult!!.userInfo.userId!!,
                source = FriendApplySource.search,
                businessId = null,
                trackerSource = ProfileSource.OTHER.source
            )?.showDialog(activity)
        }
    }

    fun updateRelation(userId: Long) = doInMutex(mutex) {
        val userInfo = UserRelationCacheManager.getUserRelationInfoByUid(userId)
        if (userInfo?.isFriend == true) {
            registeredList.removeIf { it.targetId == userId }
            listContainer.removeIf { it.targetId == userId }
            return@doInMutex
        }
        listContainer.update(
            userId,
            action = { pos, item ->
                item.extra.isLoading = false
                if (userInfo != null) {
                    item.userInfo = userInfo
                }
                itemChangeLiveData.postValue(pos to ContactsPayloadType.UpdateUserRelation)
            })
        registeredList.update(
            userId,
            action = { pos, item ->
                item.extra.isLoading = false
                if (userInfo != null) {
                    item.userInfo = userInfo
                }
            })
    }

    fun accept(userId: Long) = launchIO {
        val resp = userRepository.agreeFriendApply(userId)
        resp.prompt?.parse()
        if (resp is Success) {
            toastRegularCorrect(R.string.friend_request_add_success)
            //旧代码迁移，旧代码是这样搞的，理论上不应该这样处理，viewmodel里面做ui操作，但是这期改动太大了，先保留
            delay(500)
            EnableNotificationPromptDialog.showIfValid()
        }else {
            if (resp.prompt == null){
                toastNetworkErrorTips()
            }
        }
    }

    fun onPermissionRequest() = doInMutex(mutex) {
        listContainer.removeIf { it.type == ContactsItemType.NoPermission }
        listLiveData.postValue(listContainer.list)
    }

    private fun notifyListChange(list: List<ContactsItemBean>, needScrollToFirst: Boolean = false) =
        doInMutex(mutex) {
            listContainer.setList(list)
            listLiveData.postValue(listContainer.list)
            if (needScrollToFirst) {
                scrollToFirstLiveData.postValue(true)
            }
            showLoadingLiveData.postValue(false)
        }

    fun getUnRegisteredListSize(): Int {
        return unRegisteredList.size
    }
}