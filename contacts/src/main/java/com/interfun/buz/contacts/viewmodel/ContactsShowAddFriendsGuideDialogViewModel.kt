package com.interfun.buz.contacts.viewmodel

import android.Manifest.permission
import androidx.lifecycle.viewModelScope
import com.interfun.buz.base.ktx.isPermissionGranted
import com.interfun.buz.base.ktx.launchIO
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.common.constants.CommonMMKV
import com.interfun.buz.common.manager.af.AFManager
import com.interfun.buz.contacts.data.repository.AddFriendGuideRepository
import com.interfun.buz.contacts.utils.ContactsManager
import com.interfun.buz.contacts.viewmodel.ContactCommonDataViewModel.RefreshType.REGISTERED
import com.interfun.buz.domain.im.social.usecase.BuzConversationListUseCase
import com.interfun.buz.social.repo.BotRepository
import com.interfun.buz.social.repo.GroupRepository
import com.interfun.buz.social.repo.OfficialAccountRepository
import com.interfun.buz.social.repo.UserOnlineStatusRepository
import com.interfun.buz.social.repo.UserRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancelAndJoin
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * <AUTHOR>
 * @date 2024/7/7
 * @desc
 * 在收到推送或通讯录上传完成后，去请求已注册接口列表
 * 收到推送，立刻请求
 * 通讯录上传完成后，5s 后请求
 * 如果返回数量>0 或者 hasContacts 为 ture（新增），则直接展示弹窗
 * 如果不符合上面的条件，则在一段时间后重试。间隔依次为5s、5s、10s、10s、15s、15s
 * 全部6次重试完成后，不管结果如何，都直接展示弹窗（展示 Invite 页面）
 */
@HiltViewModel
class ContactsShowAddFriendsGuideDialogViewModel @Inject constructor(
    private val userRepository: UserRepository,
    private val groupRepository: GroupRepository,
    private val botRepository: BotRepository,
    private val officialRepository: OfficialAccountRepository,
    private val conversationListUseCase: BuzConversationListUseCase,
    private val userOnlineStatusRepository: UserOnlineStatusRepository
) : ContactCommonDataViewModel(userRepository, groupRepository, botRepository, officialRepository,conversationListUseCase, userOnlineStatusRepository) {

    companion object {
        const val TAG = "ContactsShowAddFriendsGuideDialogViewModel"
    }

    private val addFriendGuideRepository = AddFriendGuideRepository.instance
    val showAddFriendsGuideDialogFlow = MutableStateFlow(false)
    private var currentJob: Job? = null
    private val retryIntervals = listOf(5000L, 5000L, 10000L, 10000L, 15000L, 15000L)
    private val canStartCheckAddFriendGuideDialogWithoutPermission = MutableStateFlow(false)
    val showAddFriendsGuideWithoutContactPermission =
        combine(
            canStartCheckAddFriendGuideDialogWithoutPermission,
            addFriendGuideRepository.recommendAiList
        ) { canStart, list ->
            //等待可以开始展示，并且没有权限，且有ai列表请求回来
            canStart && list != null && !isPermissionGranted(permission.READ_CONTACTS)
        }

    fun startCheckAddFriendGuideDialogWithoutPermission() {
        canStartCheckAddFriendGuideDialogWithoutPermission.value = true
    }

    fun checkCanShow(): Boolean {
        return !CommonMMKV.hasShownAddFriendsGuideDialog
            && CommonMMKV.isUserRegister
            && !CommonMMKV.NotificationProblemDialogIsShow
            && ContactsManager.isContactsSyncComplete
            && isPermissionGranted(permission.READ_CONTACTS)
    }

    fun showAddFriendGuideDialog(immediatelyRequest: Boolean) = launchIO {
        currentJob?.cancelAndJoin()
        currentJob = launch {
            if (immediatelyRequest.not()) {
                delay(5000L)
            }
            tryShowAddFriendGuideDialog()
        }
    }

    private suspend fun tryShowAddFriendGuideDialog() {
        val canShow = checkCanShow()
        logInfo(TAG, "tryShowAddFriendGuideDialog, isIntercept: ${canShow.not()}")
        if (!canShow) return
        var time = 1
        for (interval in retryIntervals) {
            refreshSpecificData(REGISTERED, TAG)
            refreshMightKnowUsers(TAG)
            logInfo(
                TAG, "tryShowAddFriendGuideDialog time: $time " +
                    "size: ${registeredList.size} isServerHasContacts: $isServerHasContacts"
            )
            if (registeredList.size > 0 || isServerHasContacts) {
                showAddFriendsGuideDialogFlow.emit(true)
                return
            }
            delay(interval)
            time +=1
        }
        // 6次重试结束，不管结果如何，都直接showDialogFlow.emit(true)
        showAddFriendsGuideDialogFlow.emit(true)
    }

    /**
     * @param sharedAccountUid 点击某个用户分享页面从而注册而来，传递af承接时拿到的用户卡片的用户id
     */
    fun requestRecommendAiList(sharedAccountUid: Long?,afParams: AFManager.AFParamsCache?) = viewModelScope.launchIO {
        addFriendGuideRepository.requestRecommendAiList(sharedAccountUid,afParams)
    }

}