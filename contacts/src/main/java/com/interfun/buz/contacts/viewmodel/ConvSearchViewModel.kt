package com.interfun.buz.contacts.viewmodel

import androidx.lifecycle.viewModelScope
import com.interfun.buz.base.ktx.emitInScopeIfDifferent
import com.interfun.buz.base.ktx.launchIO
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.base.ktx.notEmpty
import com.interfun.buz.common.bean.Resp.Success
import com.interfun.buz.common.constants.AddFriendSource
import com.interfun.buz.common.ktx.isRobot
import com.interfun.buz.common.ktx.toastNetworkErrorTips
import com.interfun.buz.common.ktx.toastRegularCorrect
import com.interfun.buz.common.manager.login.LoginMainABTestManager
import com.interfun.buz.common.utils.parse
import com.interfun.buz.common.widget.dialog.EnableNotificationPromptDialog
import com.interfun.buz.contacts.R
import com.interfun.buz.contacts.entity.ContactsItemBean
import com.interfun.buz.contacts.entity.ContactsListContainer
import com.interfun.buz.contacts.utils.ContactsTracker
import com.interfun.buz.domain.im.social.usecase.BuzConversationListUseCase
import com.interfun.buz.social.repo.BotRepository
import com.interfun.buz.social.repo.GroupRepository
import com.interfun.buz.social.repo.OfficialAccountRepository
import com.interfun.buz.social.repo.UserOnlineStatusRepository
import com.interfun.buz.social.repo.UserRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class ConvSearchViewModel  @Inject constructor(
    private val userRepository: UserRepository,
    private val groupRepository: GroupRepository,
    private val botRepository: BotRepository,
    private val officialRepository: OfficialAccountRepository,
    private val conversationListUseCase: BuzConversationListUseCase,
    private val userOnlineStatusRepository: UserOnlineStatusRepository
): ContactsHomeViewModel(userRepository, groupRepository, botRepository, officialRepository,conversationListUseCase,userOnlineStatusRepository) {

    companion object{
        const val TAG = "ConvSearchViewModel"
    }

    private val isShowNewHomePagePlanB by lazy { LoginMainABTestManager.isShowNewHomePagePlanB }
    override val itemTitleFriends = ContactsItemBean.generateTitle(R.string.chats)
    override val itemTitleRecentChats = ContactsItemBean.generateTitle(R.string.home_search_recent_chats)
    private val itemTitleRegistered = ContactsItemBean.generateTitle(R.string.contacts_contacts_on_buz)
    private val itemTitleOfficialAccount = ContactsItemBean.generateTitle(R.string.contacts_buz_official_account)
    private val itemTitleUnRegistered = ContactsItemBean.generateTitle(R.string.contacts_invite_from_contacts)

    override fun requireOriginData(isFirstRequest: Boolean) {
        fullList.setList(buildFullList())
        if (isListHaveData()){
            if (isSearchingStateFlow.value.not()) {
                notifyListChange(fullList.list)
            } else {
                filterByKeyWord(searchKeyWord, false)
            }
        }
        launchIO {
            refreshAllCommonData(isFirstRequest, TAG)
            fullList.clear()
            fullList.setList(buildFullList())
            if (isSearchingStateFlow.value.not()) {
                notifyListChange(fullList.list)
            } else {
                filterByKeyWord(searchKeyWord, false)
            }
        }
    }

    private fun isListHaveData():Boolean{
        return (isShowNewHomePagePlanB && chatList.notEmpty())
                || !(friendList.isEmpty()
                && groupList.isEmpty()
                && registeredList.isEmpty()
                && unRegisteredList.isEmpty())
    }

    override fun requestAcceptRecommend(userId: Long) {
        viewModelScope.launch(Dispatchers.IO) {
            val resp = userRepository.agreeFriendApply(userId)
            resp.prompt?.parse()
            if (resp is Success) {
                toastRegularCorrect(R.string.friend_request_add_success)
                refreshSpecificData(RefreshType.REGISTERED, TAG)
                refreshSpecificData(RefreshType.FRIEND, TAG)
                fullList.clear()
                fullList.setList(buildFullList())
                notifyListChange(fullList.list)
                //旧代码迁移，旧代码是这样搞的，理论上不应该这样处理，viewmodel里面做ui操作，但是这期改动太大了，先保留
                delay(500)
                EnableNotificationPromptDialog.showIfValid()
            }else {
                if (resp.prompt == null){
                    toastNetworkErrorTips()
                }
            }
        }
    }

    override fun buildFullList() = mutableListOf<ContactsItemBean>().apply {
        if (isShowNewHomePagePlanB) {
            if (chatList.notEmpty()) {
                add(itemSpaceDP10)
                add(itemTitleRecentChats)
                addAll(chatList.list)
            }
        } else {
            if (friendList.notEmpty()) {
                add(itemSpaceDP10)
                add(itemTitleFriends)
                addAll(friendList.list)
            }

            if (groupList.notEmpty()) {
                add(itemSpaceDP10)
                add(itemTitleGroup)
                addAll(groupList.list)
            }

            if (officialAccountList.notEmpty()) {
                add(itemSpaceDP10)
                add(itemTitleOfficialAccount)
                addAll(officialAccountList.list)
            }
        }

        if (registeredList.notEmpty()) {
            add(itemSpaceDP10)
            add(itemTitleRegistered)
            addAll(registeredList.list)
        }

        if (unRegisteredList.notEmpty()) {
            add(itemSpaceDP10)
            add(itemTitleUnRegistered)
            addAll(unRegisteredList.list)
        }
    }

    override fun filterByKeyWord(keyWord: String?,includeAI:Boolean) {
        searchKeyWord = keyWord ?: ""
        viewModelScope.launch(Dispatchers.IO) {
            if (keyWord.isNullOrEmpty()) {
                isSearchingStateFlow.emitInScopeIfDifferent(viewModelScope, false)
                logInfo(ContactsHomeViewModel.TAG,"filterByKeyWord,notifyListChange,keyword:$keyWord")
                notifyListChange(fullList.list, true)
            } else {
                if (keyWord.isBlank()) return@launch
                isSearchingStateFlow.emitInScopeIfDifferent(viewModelScope, true)
                val searchList = buildFilterList(keyWord,includeAI)
                logInfo(ContactsHomeViewModel.TAG,"filterByKeyWord,notifyListChange,keyword:$keyWord")
                notifyListChange(searchList, true)
            }
        }
    }

    private fun buildFilterList(keyWord: String,includeAI: Boolean = true) = mutableListOf<ContactsItemBean>().apply {
        val filterRegisteredList = registeredList.filterUserInfo(keyWord)
        val filterUnRegisteredList = unRegisteredList.filterByKeyWord(keyWord)
        add(itemSpaceDP20)
        add(itemSearchHint)
        if (isShowNewHomePagePlanB) {
           val filterChatList = ContactsListContainer().apply {
               addAll(chatList.list)
           }.filterChatByKeyWord(keyWord)
            if (filterChatList.notEmpty()) {
                add(itemSpaceDP10)
                add(itemTitleFriends)
                addAll(filterChatList)
            }
        } else {
            var  filterFriendList = friendList.filterUserInfo(keyWord)
            if (includeAI.not()){
                filterFriendList = filterFriendList.filter { it.userInfo?.isRobot!=true }
            }
            val filterGroupList = groupList.filterGroupByKeyWord(keyWord)
            val filterOfficialAccountList = officialAccountList.filterByKeyWord(keyWord)
            if (filterFriendList.notEmpty()) {
                add(itemSpaceDP10)
                add(itemTitleFriends)
                addAll(filterFriendList)
            }
            if (filterGroupList.notEmpty()) {
                add(itemSpaceDP10)
                add(itemTitleGroup)
                addAll(filterGroupList)
            }
            if (filterOfficialAccountList.notEmpty()) {
                add(itemSpaceDP10)
                add(itemTitleOfficialAccount)
                addAll(filterOfficialAccountList)
            }
        }
        if (filterRegisteredList.notEmpty()) {
            add(itemSpaceDP10)
            add(itemTitleRegistered)
            addAll(filterRegisteredList)
        }
        if (filterUnRegisteredList.notEmpty()) {
            add(itemSpaceDP10)
            add(itemTitleUnRegistered)
            addAll(filterUnRegisteredList)
        }
    }
}