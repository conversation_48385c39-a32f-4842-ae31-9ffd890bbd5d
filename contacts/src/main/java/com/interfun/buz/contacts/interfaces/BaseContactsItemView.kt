package com.interfun.buz.contacts.interfaces

import android.view.View
import android.widget.TextView
import androidx.viewbinding.ViewBinding
import com.interfun.buz.base.ktx.BindingViewHolder
import com.interfun.buz.base.ktx.asColor
import com.interfun.buz.base.ktx.asDrawable
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.base.ktx.click
import com.interfun.buz.base.ktx.gone
import com.interfun.buz.base.ktx.notEmpty
import com.interfun.buz.base.ktx.onClick
import com.interfun.buz.base.ktx.setTextWithAutoGoneIfEmpty
import com.interfun.buz.base.ktx.visible
import com.interfun.buz.base.ktx.visibleIf
import com.interfun.buz.common.bean.user.BuzUserRelationValue
import com.interfun.buz.common.ktx.getHighLightWord
import com.interfun.buz.common.ktx.isRobot
import com.interfun.buz.common.manager.ABTestManager
import com.interfun.buz.common.widget.button.CommonButton
import com.interfun.buz.common.widget.view.PortraitImageView
import com.interfun.buz.contacts.R
import com.interfun.buz.contacts.entity.ContactsItemBean
import com.interfun.buz.contacts.entity.ContactsItemInPage
import com.interfun.buz.contacts.entity.ContactsItemType
import com.interfun.buz.contacts.entity.ContactsPayloadType
import com.interfun.buz.contacts.entity.FriendAppliesStatus
import com.interfun.buz.contacts.utils.ContactsTracker
import com.interfun.buz.domain.chat.ktx.updateUserState
import com.interfun.buz.social.entity.OnlineState
import com.interfun.buz.social.entity.UserStateInfo

/**
 * <AUTHOR>
 * @date 2023/5/9
 * @desc General contacts list item view, including recommend, friends, groups, etc.
 */
open class BaseContactsItemView<VB : ViewBinding>(callback: ContactsItemCallback) :
    BaseContactsDelegate<VB>(callback) {

    private val VB.ivPortrait get() = root.findViewById<PortraitImageView>(R.id.ivPortrait)
    private val VB.tvContent get() = root.findViewById<TextView>(R.id.tvContent)
    private val VB.tvDesc get() = root.findViewById<TextView>(R.id.tvDesc)
    private val VB.iftvDelete get() = root.findViewById<View>(R.id.iftvDelete)
    private val VB.btnCommon get() = root.findViewById<CommonButton>(R.id.btnCommon)
    private val VB.gOnline get() = root.findViewById<View>(R.id.gOnline)
    private val VB.roundOnline get() = root.findViewById<View>(R.id.roundOnline)
    private val isContactHomePage get() = callback.getCurrentPage() == ContactsItemInPage.PAGE_CONTACT_HOME

    open fun update(binding: VB, item: ContactsItemBean) {
        resetViews(binding)
        if (item.type == ContactsItemType.Loading) return
        updatePortrait(binding, item)
        updateContent(binding, item)
        updateRelation(binding, item)
    }

    private fun resetViews(binding: VB) {
        binding.ivPortrait?.setImageDrawable(null)
        binding.tvContent?.text = ""
        binding.tvDesc?.apply {
            text = ""
            gone()
        }
        binding.iftvDelete?.gone()
        binding.btnCommon?.gone()
    }

    open fun updatePortrait(binding: VB, item: ContactsItemBean) {
        binding.ivPortrait?.apply {
            setImageDrawable(null)
            item.dispatch(
                group = {
                    setGroupInfoBean(it)
                },
                user = {
                    setPortrait(it.portrait)
                }
            )
        }
    }

    open fun updateContent(binding: VB, item: ContactsItemBean) {
        val content = item.content.orEmpty()
        val username = item.userInfo?.userName.orEmpty()
        binding.tvContent?.apply {
            text = content.ifEmpty { username }.getSearchText() ?: ""
        }

        val searchText = callback.getSearchText().orEmpty()
        val isSearching = searchText.isNotEmpty() &&
                content.lowercase().contains(searchText.lowercase()).not() &&
                username.lowercase().contains(searchText.lowercase())

        binding.tvDesc.setTextColor(R.color.text_white_secondary.asColor())
        binding.gOnline?.gone()

        if (item.groupInfo != null) {
            binding.tvDesc?.setTextWithAutoGoneIfEmpty(item.desc)
        } else if (isSearching) {
            binding.tvDesc?.setTextWithAutoGoneIfEmpty(
                String.format(
                    R.string.im_reply_name.asString(),
                    username
                )
            )
        } else if (item.isNormalUser()) {
            if (ABTestManager.isUserStatePlanA && isContactHomePage) {
                item.extra.userStateInfo?.updateUserState(binding.tvDesc, item.desc.orEmpty())
            } else {
                binding.tvDesc?.setTextWithAutoGoneIfEmpty(item.desc.orEmpty())
            }
            updateUserStateDot(item.extra.userStateInfo, binding)
        } else {
            binding.tvDesc?.setTextWithAutoGoneIfEmpty(item.desc.orEmpty())
        }
    }

    private fun updateUserStateDot(userStateInfo: UserStateInfo?, binding: VB) {
        if (userStateInfo is OnlineState) {
            binding.gOnline?.visible()
            if (userStateInfo is OnlineState.Quiet) {
                binding.roundOnline.setBackgroundResource(com.interfun.buz.chat.R.drawable.common_oval_secondary_purple)
            } else {
                binding.roundOnline.setBackgroundResource(com.interfun.buz.chat.R.drawable.common_oval_basic_primary)
            }
        } else {
            binding.gOnline?.gone()
        }
    }

    open fun updateRelation(binding: VB, item: ContactsItemBean) {
        val relation = item.userInfo?.serverRelation
        //only show when Add or Accept button visible, and in Requests or Recommend list
        binding.iftvDelete?.visibleIf(
            (relation == BuzUserRelationValue.BEING_FRIEND_REQUEST.value
                || relation == BuzUserRelationValue.NO_RELATION.value)
                &&
                (item.type == ContactsItemType.Requests
                    || item.type == ContactsItemType.Recommend
                    || item.type == ContactsItemType.RecommendHome)
        )
        binding.btnCommon?.apply {
            gone()
            click { }
            if (item.extra.isLoading) {
                showLoading(true)
            } else {
                hideLoading(true)
            }
            item.dispatch(
                contact = {
                    visible()
                    setText(R.string.invite.asString())
                    setIconFontText(R.string.ic_add.asString())
                    setType(CommonButton.TYPE_SECONDARY_SMALL)
                    click { callback.onInviteClick(item) }
                },
                user = {
                    when (relation) {
                        BuzUserRelationValue.FRIEND_REQUEST.value -> {
                            visible()
                            setText(R.string.pending.asString())
                            setIconFontText(R.string.ic_check.asString())
                            setType(CommonButton.TYPE_TERTIARY_SMALL)
                            click { }
                        }

                        BuzUserRelationValue.BEING_FRIEND_REQUEST.value -> {
                            visible()
                            setText(R.string.accept.asString())
                            if (item.type != ContactsItemType.Requests
                                && item.type != ContactsItemType.Recommend
                            ) {
                                setIconFontText(R.string.ic_check.asString())
                            } else {
                                setIconFontText("")
                            }
                            setType(CommonButton.TYPE_PRIMARY_SMALL)
                            click { callback.onAcceptClick(item) }
                        }

                        BuzUserRelationValue.NO_RELATION.value -> {
                            visible()
                            if (item.extra.friendAppliesStatus == FriendAppliesStatus.EXPIRED.value) {
                                setText(R.string.expired.asString())
                                setIconFontText("")
                                setTextColor(R.color.text_white_secondary.asColor())
                                background = R.drawable.chat_bg_transparent.asDrawable()
                                click { }
                                return@dispatch
                            }
                            setText(R.string.add.asString())
                            setIconFontText(R.string.ic_contact_add.asString())
                            setType(CommonButton.TYPE_PRIMARY_SMALL)
                            click {
                                if (it.userId.isRobot()) {
                                    ContactsTracker.onClickAddAiRoot()
                                }
                                item.extra.isLoading = true
                                showLoading(true)
                                callback.onAddFriendClick(item)
                            }
                        }

                        BuzUserRelationValue.FRIEND.value -> {
                            if (item.extra.showAddedWhenIsFriend) {
                                visible()
                                setText(R.string.added.asString())
                                setIconFontText("")
                                setType(CommonButton.TYPE_TERTIARY_SMALL)
                            } else {
                                gone()
                            }
                            click { }
                        }

                        BuzUserRelationValue.MYSELF.value -> {
                            gone()
                            click { }
                        }
                    }
                }
            )
        }
    }

    override fun onBindViewHolder(
        holder: BindingViewHolder<VB>,
        item: ContactsItemBean,
        payloads: List<Any>
    ) {
        val binding = holder.binding
        if (payloads.isEmpty()) {
            update(binding, item)
        } else {
            payloads.forEach {
                when (it) {
                    ContactsPayloadType.UpdateContent,
                    ContactsPayloadType.UpdateSearchKeyWord -> updateContent(binding, item)
                    ContactsPayloadType.UpdateUserRelation -> updateRelation(binding, item)
                    ContactsPayloadType.UpdatePortrait -> updatePortrait(binding, item)
                    else -> update(binding, item)
                }
            }
        }
    }

    override fun onBindViewHolder(
        binding: VB,
        item: ContactsItemBean,
        position: Int
    ) {
        update(binding, item)
    }

    override fun onViewHolderCreated(holder: BindingViewHolder<VB>) {
        super.onViewHolderCreated(holder)
        holder.binding.iftvDelete?.let{
            holder.onClick(this, it) { binding, item, pos ->
                callback.onDeleteClick(item)
            }
        }
    }

    override fun onViewRecycled(holder: BindingViewHolder<VB>) {
        super.onViewRecycled(holder)
        holder.binding.btnCommon?.hideLoading(true)
    }

    protected fun String?.getSearchText() = if (notEmpty()) {
        if (callback.getSearchText().notEmpty()) {
            getHighLightWord(
                this,
                callback.getSearchText(),
                R.color.basic_primary,
                ignoreCase = true
            )
        } else {
            this
        }
    } else {
        ""
    }

}