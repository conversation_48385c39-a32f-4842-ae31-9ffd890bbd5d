package com.interfun.buz.contacts.viewmodel

import androidx.lifecycle.viewModelScope
import com.interfun.buz.base.ktx.launchIO
import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.common.bean.Resp.Success
import com.interfun.buz.common.constants.AddFriendSource
import com.interfun.buz.common.constants.CommonMMKV
import com.interfun.buz.common.database.entity.UserRelationInfo
import com.interfun.buz.domain.im.social.usecase.BuzConversationListUseCase
import com.interfun.buz.social.repo.BotRepository
import com.interfun.buz.social.repo.GroupRepository
import com.interfun.buz.social.repo.OfficialAccountRepository
import com.interfun.buz.social.repo.UserOnlineStatusRepository
import com.interfun.buz.social.repo.UserRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import org.json.JSONObject
import javax.inject.Inject

/**
 * @Desc
 * @Author:LumJunYean
 * @Date: 2024/8/2
 */
@HiltViewModel
class AddOfficialAccountDialogViewModel @Inject constructor(
    private val userRepository: UserRepository,
    private val groupRepository: GroupRepository,
    private val botRepository: BotRepository,
    private val officialRepository: OfficialAccountRepository,
    private val conversationUseCase : BuzConversationListUseCase,
    private val userOnlineStatusRepository : UserOnlineStatusRepository,
): ContactCommonDataViewModel(userRepository, groupRepository, botRepository, officialRepository, conversationUseCase,userOnlineStatusRepository) {

    companion object {
        const val TAG = "AddOfficialAccountDialogViewModel"
    }
    val addFriendState by lazy { MutableStateFlow(false) }

    var userRelationInfo: UserRelationInfo? = null

    fun addOfficialAccountFriend(extraData: String?, popDialogId: Long){
        logDebug(TAG, "extraData: $extraData")
        viewModelScope.launchIO {
            extraData?.let { data ->
                val extraDataJSON = JSONObject(data)
                val userId = extraDataJSON.getLong("userId")
                val resp = userRepository.addFriend(
                    userId = userId,
                    source = AddFriendSource.FromOfficialAccountPopWindow.value,
                    businessId = null
                )
                if (resp is Success) {
                    savePopHistory(popDialogId)
                    officialRepository.syncAllOAAccounts()
                    userRelationInfo = resp.data.toOldUserRelationInfo()
                    addFriendState.value = true
                }
            }
        }
    }

    private fun savePopHistory(popDialogId: Long) {
        val cacheIds = CommonMMKV.officialAccountDialogPopIds?.toMutableSet() ?: mutableSetOf()
        cacheIds.add("$popDialogId")
        CommonMMKV.officialAccountDialogPopIds = cacheIds
    }
}
