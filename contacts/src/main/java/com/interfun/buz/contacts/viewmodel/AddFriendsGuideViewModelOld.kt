package com.interfun.buz.contacts.viewmodel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.buz.idl.user.request.RequestAddFriendBatch
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.base.ktx.launchIO
import com.interfun.buz.common.bean.share.AFBusinessType
import com.interfun.buz.common.ktx.isSuccess
import com.interfun.buz.common.manager.af.AFManager
import com.interfun.buz.common.manager.cache.user.UserRelationCacheManager
import com.interfun.buz.common.net.newInstanceBuzNetUserServiceClient
import com.interfun.buz.common.utils.parse
import com.interfun.buz.contacts.R
import com.interfun.buz.contacts.data.repository.AddFriendGuideRepository
import com.interfun.buz.contacts.entity.ContactsItemBean
import com.interfun.buz.contacts.entity.ContactsItemType
import com.interfun.buz.contacts.view.itemdelegate.RegisteredContactsItemBean
import com.interfun.buz.domain.im.social.usecase.BuzConversationListUseCase
import com.interfun.buz.social.repo.*
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * @Desc
 * @Author:<EMAIL>
 * @Date: 2022/12/29
 */
@HiltViewModel
class AddFriendsGuideViewModelOld @Inject constructor(
    private val userRepository: UserRepository,
    private val groupRepository: GroupRepository,
    private val botRepository: BotRepository,
    private val officialRepository: OfficialAccountRepository,
    private val conversationListUseCase: BuzConversationListUseCase,
    private val userOnlineStatusRepository: UserOnlineStatusRepository
) : ContactCommonDataViewModel(
    userRepository, groupRepository, botRepository, officialRepository,
    conversationListUseCase,
    userOnlineStatusRepository
) {

    private val userIDLService by lazy { newInstanceBuzNetUserServiceClient() }
    val contactsRegisterLiveData = MutableLiveData<List<ContactsItemBean>>()
    val addFriendBatchLiveData = MutableLiveData<Boolean>()
    private val addFriendGuideRepository = AddFriendGuideRepository.instance
    val recommendAiList = addFriendGuideRepository.recommendAiList.value
    val recommendAiListOrEmpty = recommendAiList ?: emptyList()

    fun getRegisteredContacts() {
        launchIO {
            val registeredContactsList = mutableListOf<ContactsItemBean>()
            registeredContactsList.clear()
            val afType = AFManager.afParams.value?.type
            val inviterUserId =
                if (afType == AFBusinessType.SHARE_TO_INVITE.type || afType == AFBusinessType.SHARE_GROUP.type) AFManager.afParams.value?.afParams?.inviteUid?.toLongOrNull() else null
            if (inviterUserId != null) {
                UserRelationCacheManager.getUserRelationInfoByUidSync(inviterUserId)?.let {
                    registeredContactsList.add(
                        index = 0,
                        element = RegisteredContactsItemBean(ContactsItemType.Recommend,it, checked = true, isInviter = true)
                    )
                }
            }
            registeredList.list.forEach {
                it.dispatch { info ->
                    if (info.userId != inviterUserId) {
                        registeredContactsList.add(RegisteredContactsItemBean(ContactsItemType.Contact,info, true))
                    }
                }
            }
            if (registeredContactsList.isNotEmpty()) {
                registeredContactsList.add(
                    0,
                    ContactsItemBean.generateTitle(R.string.add_to_buz_contacts)
                )
            }

//            if (registeredContactsList.size <= MIN_BUZ_FRIENDS) { // 注登v2需求去掉该限制
                val mightKnowUserList = mightKnowUserList.list.toMutableList()
                if (mightKnowUserList.isNotEmpty()) {
                    registeredContactsList.add(
                        ContactsItemBean(
                            ContactsItemType.Title,
                            content = R.string.might_know_tips.asString()
                        )
                    )
                }
                mightKnowUserList.forEach {
                    it.userInfo?.let {
                        registeredContactsList.add(
                            RegisteredContactsItemBean(
                                ContactsItemType.MightKnow,
                                it,
                                true
                            )
                        )
                    }
//                }
            }

            contactsRegisterLiveData.postValue(registeredContactsList)
        }
    }

    fun sendFriendBatchRequest(userIdList: List<Long>){
        viewModelScope.launch {
            val addFriendBatch =
                userIDLService.addFriendBatch(RequestAddFriendBatch(userIdList, 2, null))
            addFriendBatch.data?.prompt?.parse()
            addFriendBatchLiveData.postValue(addFriendBatch.isSuccess)
        }
    }

    fun refreshContactsData() {
        launchIO {
            refreshSpecificData(RefreshType.REGISTERED)
            refreshSpecificData(RefreshType.UNREGISTERED)
        }
    }
}
