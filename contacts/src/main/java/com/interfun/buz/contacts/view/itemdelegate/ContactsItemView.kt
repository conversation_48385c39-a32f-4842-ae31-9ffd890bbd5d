package com.interfun.buz.contacts.view.itemdelegate

import androidx.core.text.buildSpannedString
import com.interfun.buz.base.ktx.appendCenterImage
import com.interfun.buz.base.ktx.appendSpace
import com.interfun.buz.base.ktx.asColor
import com.interfun.buz.base.ktx.asDrawable
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.base.ktx.dp
import com.interfun.buz.base.ktx.iconFontAlign
import com.interfun.buz.base.ktx.log
import com.interfun.buz.base.ktx.size
import com.interfun.buz.base.ktx.typeface
import com.interfun.buz.common.ktx.FontUtil
import com.interfun.buz.common.ktx.isOfficial
import com.interfun.buz.common.R
import com.interfun.buz.common.ktx.getBooleanDefault
import com.interfun.buz.common.ktx.isRobot
import com.interfun.buz.contacts.databinding.ContactsItemCommonBinding
import com.interfun.buz.contacts.entity.ContactsItemBean
import com.interfun.buz.contacts.entity.ContactsItemType
import com.interfun.buz.contacts.interfaces.BaseContactsItemView
import com.interfun.buz.contacts.interfaces.ContactsItemCallback

/**
 * <AUTHOR>
 * @date 2023/5/9
 * @desc 通讯录通用类型，包括用户\群\通讯录好\搜索搜索列表
 * General type of address book, including user\group\address book friends
 */
class ContactsItemView(callback: ContactsItemCallback) :
    BaseContactsItemView<ContactsItemCommonBinding>(callback) {
    private val TAG = "ContactsItemView"

    override fun updateContent(binding: ContactsItemCommonBinding, item: ContactsItemBean) {
        super.updateContent(binding, item)
        val isOfficial = item.userInfo?.isOfficial == true
        val isRobot = item.userInfo?.isRobot.getBooleanDefault()

        log(TAG, "updateOfficialTag: isOfficial = $isOfficial,,isRobot=${isRobot}")
        if (isOfficial || isRobot || item.type == ContactsItemType.Contact) {
            val originText = binding.tvContent.text.toString().getSearchText()

            val text = buildSpannedString {
                append(originText)
                if (isOfficial) {
                    appendSpace(4.dp)
                    iconFontAlign(color = R.color.basic_primary.asColor()) {
                        size(18.dp) {
                            typeface(FontUtil.fontIcon!!) {
                                append(R.string.ic_official.asString())
                            }
                        }
                    }
                }
                if (item.userInfo?.isRobot.getBooleanDefault()) {
                    appendSpace(4.dp)
                    com.interfun.buz.contacts.R.drawable.common_icon_ai_flag.asDrawable()?.let { appendCenterImage(it, 18.dp, 18.dp) }
                }

                if (item.type == ContactsItemType.Contact) {
                    appendSpace(4.dp)
                    iconFontAlign(color = R.color.text_white_main.asColor()) {
                        size(18.dp) {
                            typeface(FontUtil.fontIcon!!) {
                                append(R.string.ic_contacts_solid.asString())
                            }
                        }
                    }
                }
            }
            binding.tvContent.text = text
        }


    }

}