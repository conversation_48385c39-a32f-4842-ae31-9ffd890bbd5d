package com.interfun.buz.contacts.viewmodel

import android.Manifest
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.base.ktx.isPermissionGranted
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.common.manager.login.LoginMainABTestManager
import com.interfun.buz.contacts.R
import com.interfun.buz.contacts.entity.ContactsExtra
import com.interfun.buz.contacts.entity.ContactsItemBean
import com.interfun.buz.contacts.entity.ContactsItemType
import com.interfun.buz.contacts.entity.ContactsListContainer
import com.interfun.buz.domain.im.social.entity.GroupConversation
import com.interfun.buz.domain.im.social.entity.UserConversation
import com.interfun.buz.domain.im.social.usecase.BuzConversationListUseCase
import com.interfun.buz.social.entity.convertStatusToState
import com.interfun.buz.social.repo.*
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.withContext
import javax.inject.Inject
import com.interfun.buz.social.db.entity.BuzUserComposite
import com.interfun.buz.social.entity.UserOnlineStatus
import com.interfun.buz.social.entity.UserStateInfo

@Deprecated("不要再用这个了，耦合太严重，什么业务需要的话自己建viewmodel使用对应的repository加载即可")
@HiltViewModel
open class ContactCommonDataViewModel @Inject constructor(
    private val userRepository: UserRepository,
    private val groupRepository: GroupRepository,
    private val botRepository: BotRepository,
    private val officialRepository: OfficialAccountRepository,
    private val conversationUseCase : BuzConversationListUseCase,
    private val userOnlineStatusRepository : UserOnlineStatusRepository,
) :ViewModel() {
    private val TAG = "ContactCommonDataViewModel"

    companion object{
        val recommendList = ContactsListContainer()
        val chatList = ContactsListContainer()
        val friendList = ContactsListContainer()
        val groupList = ContactsListContainer()
        val registeredList = ContactsListContainer()
        val unRegisteredList = ContactsListContainer()
        val mightKnowUserList = ContactsListContainer()
        val officialAccountList = ContactsListContainer()
        // getRegisteredContacts 中返回，标明是否已上传通讯录到服务端
        // 如果为 false 则表明当前 getRegisteredContacts 接口返回列表不可用
        var isServerHasContacts = false

        fun clearAllData(){
            recommendList.clear()
            chatList.clear()
            friendList.clear()
            groupList.clear()
            registeredList.clear()
            unRegisteredList.clear()
            officialAccountList.clear()
            isServerHasContacts = false
        }
    }

    // 首页会话搜索的列表
    val convList =
        conversationUseCase.invoke()
            .shareIn(this.viewModelScope, SharingStarted.Lazily, 1)

    @OptIn(ExperimentalCoroutinesApi::class)
    val convListFlow: Flow<List<ContactsItemBean>> = convList
        .flatMapLatest { conversationList ->
            val userIds = conversationList
                .filterIsInstance<UserConversation>()
                .map { it.convId }

            userOnlineStatusRepository.getUserStateInfoFlow(userIds)
                .map { statusMap ->
                    conversationList.mapNotNull { conv ->
                        when (conv) {
                            is GroupConversation -> {
                                conv.groupComposite
                                    ?.toOldGroupInfoBean()
                                    ?.transform2ContactsItemBean()
                            }
                            is UserConversation -> {
                                conv.userComposite
                                    ?.toOldUserRelationInfo()
                                    ?.transform2ContactsItemBean()
                                    ?.apply {
                                        extra = ContactsExtra(
                                            userStateInfo = statusMap[conv.convId]
                                        )
                                    }
                            }
                        }
                    }
                }
        }.stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )

    @OptIn(ExperimentalCoroutinesApi::class)
    private val sortedFriendListWithStateFlow: Flow<List<Pair<BuzUserComposite, UserStateInfo?>>> =
        userRepository.getAllFriendsFlow()
            .map { buzUserComposites ->
                // 仅正常用户
                buzUserComposites.filter { it.user.isNormalUser }
            }
            .flatMapLatest { userMembers ->
                val userIds = userMembers.map { it.user.userId }
                // 获取在线状态并与用户列表一起排序
                userOnlineStatusRepository.getStatusInfoFlow(userIds)
                    .map { statusMap ->
                        sortUserMembersByStatus(userMembers, statusMap)
                    }
            }


    private fun sortUserMembersByStatus(
        userMembers: List<BuzUserComposite>,
        statusMap: Map<Long, UserOnlineStatus>
    ): List<Pair<BuzUserComposite, UserStateInfo?>> {
        return userMembers.map { userComposite ->
            val status = statusMap[userComposite.user.userId]
            Triple(
                userComposite, status, when {
                    status?.isOnline == true && !status.isInQuietMode -> 1
                    status?.isOnline == true && status.isInQuietMode -> 2
                    else -> 3
                }
            )
        }
            /**
             * {
             *   1 -> [...自动播放的用户...],
             *   2 -> [...静音的用户...],
             *   3 -> [...离线的用户...]
             * }
             */
            .groupBy { it.third }
            .toSortedMap()
            .flatMap { (_, group) ->
                group.sortedWith(compareByDescending { (_, status, _) ->
                    if (status?.isOnline == true) status.onlineTime else status?.offlineTime ?: 0L
                }).map { it.first to it.second?.convertStatusToState() }
            }
    }


    // 通讯录的好友列表
    @OptIn(ExperimentalCoroutinesApi::class)
    private val myFriendListFlow = sortedFriendListWithStateFlow
        .map { sortedUserMembersWithState ->
            sortedUserMembersWithState.map { (userComposite,userState) ->
                val prefix =
                    if (userComposite.user.buzId.isNullOrEmpty()) "" else R.string.common_symbol_at.asString()
                ContactsItemBean(
                    type = ContactsItemType.Normal,
                    content = userComposite.fullNickName,
                    desc = prefix + (userComposite.user.buzId ?: ""),
                    targetId = userComposite.user.userId,
                    userInfo = userComposite.toOldUserRelationInfo(),
                    extra = ContactsExtra(
                        userStateInfo = userState
                    )
                )
            }
        }


    suspend fun refreshAllCommonData(
        isFirstRequest: Boolean = false,
        from: String = ""
    ) = withContext(Dispatchers.IO) {
        logInfo(TAG, "from: $from, refreshAllCommonData isFirstRequest: $isFirstRequest")

        val friendListDeferred = async { myFriendListFlow.first() }
        val groupListDeferred = async { groupRepository.getAllJoinedGroups().map { it.toOldGroupInfoBean().transform2ContactsItemBean() } }
        val officialAccountListDeferred = async {
            officialRepository.getAllMyOfficialAccountsFromCache().map {
                ContactsItemBean(
                    type = ContactsItemType.Official,
                    content = it.user.userName,
                    targetId = it.user.userId,
                    userInfo = it.toOldUserRelationInfo()
                )
            }
        }

        if (LoginMainABTestManager.isShowNewHomePagePlanB) {
            val convListDeffered = async { convListFlow.filter { it.isNotEmpty() }.first() }
            chatList.setList(convListDeffered.await())
        }

        friendList.setList(friendListDeferred.await())
        groupList.setList(groupListDeferred.await())
        officialAccountList.setList(officialAccountListDeferred.await())

        if (isPermissionGranted(Manifest.permission.READ_CONTACTS)) {
            val recommendListDeferred = async { getRecommendFriendsList(isFirstRequest) }
            val registeredListDeferred = async { getRegisteredContactsList() }
            val unRegisteredListDeferred = async { getUnRegisterContactSortList() }

            recommendList.setList(recommendListDeferred.await())
            registeredListDeferred.await().let {
                registeredList.setList(it.first)
                isServerHasContacts = it.second
            }
            unRegisteredList.setList(unRegisteredListDeferred.await())
        } else {
            recommendList.clear()
            registeredList.clear()
            unRegisteredList.clear()
        }
    }

    suspend fun refreshSpecificData(
        refreshType: RefreshType,
        from: String = ""
    ) = withContext(Dispatchers.IO) {
        logInfo(TAG, "from $from, refreshSpecificData type: $refreshType")
        when(refreshType){
            RefreshType.RECOMMEND ->{
                if (isPermissionGranted(Manifest.permission.READ_CONTACTS)) {
                    val recommendListDeferred = async {
                        getRecommendFriendsList(false)
                    }
                    recommendList.setList(recommendListDeferred.await())
                }else{
                    recommendList.clear()
                }
            }

            RefreshType.FRIEND ->{
                val friendListDeferred = async { myFriendListFlow.first()  }
                friendList.setList(friendListDeferred.await())
            }

            RefreshType.GROUP ->{
                val groupListDeferred = async {
                    groupRepository.getAllJoinedGroupsFromServer()
                        .map { it.toOldGroupInfoBean().transform2ContactsItemBean() }
                }
                groupList.setList(groupListDeferred.await())
            }

            RefreshType.REGISTERED -> {
                if (isPermissionGranted(Manifest.permission.READ_CONTACTS)) {
                    val registeredListDeferred = async { getRegisteredContactsList() }
                    registeredListDeferred.await().let {
                        registeredList.setList(it.first)
                        isServerHasContacts = it.second
                    }
                } else {
                    registeredList.clear()
                }
            }

            RefreshType.UNREGISTERED ->{
                if (isPermissionGranted(Manifest.permission.READ_CONTACTS)) {
                    val unRegisteredListDeferred = async { getUnRegisterContactSortList() }
                    unRegisteredList.setList(unRegisteredListDeferred.await())
                } else {
                    unRegisteredList.clear()
                }
            }

            RefreshType.OFFICIAL_ACCOUNT ->{
                val officialAccountListDeferred = async {
                    officialRepository.getAllMyOfficialAccountsFromCache().map {
                        ContactsItemBean(
                            type = ContactsItemType.Official,
                            content = it.user.userName,
                            targetId = it.user.userId,
                            userInfo = it.toOldUserRelationInfo()
                        )
                    }
                }
                officialAccountList.setList(officialAccountListDeferred.await())
            }

            RefreshType.CHAT ->{
                val chatListDeffered = async { convListFlow.filter { it.isNotEmpty() }.first() }
                chatList.setList(chatListDeffered.await())
            }
        }
    }

    private suspend fun getConversationList(): List<ContactsItemBean> {
        val conversationList = convList.first()
        return conversationList.mapNotNull { conv ->
            when(conv){
                is GroupConversation -> {
                    conv.groupComposite?.toOldGroupInfoBean()?.transform2ContactsItemBean()
                }
                is UserConversation -> {
                    conv.userComposite?.toOldUserRelationInfo()?.transform2ContactsItemBean()
                }
            }
        }
    }

    suspend fun refreshMightKnowUsers(
        from: String = ""
    ) = withContext(Dispatchers.IO) {
        logInfo(TAG, "from $from, getMightKnowUsers")
        val mightKnowUserListDeferred = async { getMightKnowUsers() }
        mightKnowUserList.setList(mightKnowUserListDeferred.await())
    }

    enum class RefreshType {
        RECOMMEND,
        FRIEND,
        GROUP,
        REGISTERED,
        UNREGISTERED,
        OFFICIAL_ACCOUNT,
        CHAT
    }
}