package com.interfun.buz.contacts.view.fragment

import android.os.Bundle
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.base.ktx.bind
import com.interfun.buz.base.ktx.click
import com.interfun.buz.base.ktx.finishActivity
import com.interfun.buz.base.ktx.initStatusBarHeight
import com.interfun.buz.common.base.binding.BaseBindingFragment
import com.interfun.buz.common.constants.JUMP_INFO
import com.interfun.buz.common.constants.RouterParamKey
import com.interfun.buz.contacts.R
import com.interfun.buz.contacts.databinding.ContactsFragmentListBinding
import com.interfun.buz.contacts.view.block.ContactsRecommendListBlock
import com.interfun.buz.contacts.view.block.ContactsRequestsListBlock
import dagger.hilt.android.AndroidEntryPoint

/**
 * <AUTHOR>
 * @date 2023/5/9
 * @desc Common Contacts List Fragment, Host:
 * [com.interfun.buz.contacts.view.activity.ContactsRequestsActivity] 通讯录好友请求列表
 * [com.interfun.buz.contacts.view.activity.ContactsRecommendActivity] 通讯录推荐列表View all
 */
@AndroidEntryPoint
class ContactsListFragment : BaseBindingFragment<ContactsFragmentListBinding>() {

    companion object {
        fun newInstance(pageType: Int): ContactsListFragment {
            return ContactsListFragment().apply {
                arguments = Bundle().apply { putInt(RouterParamKey.Common.JUMP_INFO, pageType) }
            }
        }
        const val TAG = "ContactsListFragment"
        const val TYPE_REQUESTS = 1
        const val TYPE_RECOMMEND = 2
    }

    private var pageType = 0

    override fun initArguments() {
        pageType = arguments?.getInt(RouterParamKey.Common.JUMP_INFO) ?: 0
    }

    override fun initView() {
        showPageLoading()
        binding.spaceStatusBar.initStatusBarHeight()
        binding.iftvBack.click { finishActivity() }
        binding.tvTitle.text = when (pageType) {
            TYPE_REQUESTS -> R.string.requests.asString()
            TYPE_RECOMMEND -> R.string.contacts_add_your_contacts.asString()
            else -> ""
        }
    }

    override fun initBlock() {
        when (pageType) {
            TYPE_REQUESTS -> ContactsRequestsListBlock(this, binding).bind(viewLifecycleOwner)
            TYPE_RECOMMEND -> ContactsRecommendListBlock(this, binding.rvContent, true).bind(viewLifecycleOwner)
        }
    }
}