apply plugin: 'com.android.library'
apply from: "../base_config.gradle"

android {
    namespace 'com.interfun.buz.contacts'
}

dependencies {
    implementation project(':common')
    implementation project(':common-compose')
    implementation(project(':im'))
    implementation project(':chat')
    implementation project(':push')
    implementation project(':core:widget_liveplace')
    implementation project(':biz-center:liveplace')
    implementation project(':biz-center:social')
    implementation project(':biz-center:translator')
    implementation project(':domain-ui:social')
    implementation project(':domain-ui:chat')
    implementation project(':domain:im-social')

}