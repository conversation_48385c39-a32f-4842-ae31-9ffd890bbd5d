import sys
import os
import requests
import json

def send_feishu_message(project_platform, branch, job_url):
    title = "Detect snapshot words"
    header = {
        "Content-type": "application/json",
        "charset": "utf-8"
    }
    url = "https://open.larksuite.com/open-apis/bot/v2/hook/bc752f88-1aec-466d-9018-e9f72702944d"
    data = {"msg_type": "post", "content": {"post": {"zh_cn": {"title": title, "content": [
            [{"tag": "text", "text": "project_platform: " + project_platform}],
            [{"tag": "text", "text": "branch: " + branch}],
            [{"tag": "text", "text": "Report details:"},
             {"tag": "a", "text": "click here to check👉🏻", "href": job_url}]]}}}}
    json_data = json.dumps(data)         
    response = requests.post(url, headers=header, data=json_data.encode("UTF-8"))


def detect_snapshot_keyword(project_platform, branch, file_name, keyword):
    keyword_detect = False
    # 打开文件
    with open(file_name, "r") as f:
        # 读取文件内容
        content = f.readlines()
        # 检查关键词是否存在
        for i,line in enumerate(content):
            if keyword in line.lower():
                # 如果关键词存在，打印错误信息: 文件名、行号和关键词
                print(f"{project_platform} {branch},detect fail! {file_name}:{i+1} contains {keyword}")
                # 打印包含关键词的行的源代码
                print("具体的源代码内容：",line)
                keyword_detect = True
    
    if not keyword_detect:
        print("detect success")
    else:
        # 飞书群通知
        send_feishu_message(project_platform, branch, job_url)


if __name__ == "__main__":
    project_platform = sys.argv[1]
    branch = sys.argv[2]
    job_url= sys.argv[3]

    if project_platform == "iOS":
        file_name = "Podfile"
    else:
        file_name = "config.gradle"

    print("project_platform={}".format(project_platform))
    print("branch={}".format(branch))
    print("file_name={}".format(file_name))

    # 要查找的关键词
    keyword = "snapshot"
    print("=============检测结果====================")
    detect_snapshot_keyword(project_platform, branch, file_name, keyword)