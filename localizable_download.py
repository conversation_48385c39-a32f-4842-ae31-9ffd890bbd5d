import oss2
import os
import sys
import shutil
import json
import subprocess
from lxml import etree
import xml.dom.minidom

print(len(sys.argv))
if len(sys.argv) != 3:
    print("arguments count not equal two, error")
    os.exit(1)

specialString = "~!@#$%^&*()+-*/<>,.[]\/"
tmpDir = sys.argv[2]
# 阿里云账号AccessKey拥有所有API的访问权限，风险很高。强烈建议您创建并使用RAM用户进行API访问或日常运维，请登录RAM控制台创建RAM用户。
auth = oss2.Auth('LTAI5tHCsEaN1yZWmwJGTMfb', '******************************')
# Endpoint以杭州为例，其它Region请按实际情况填写。
bucket = oss2.Bucket(auth, 'http://oss-cn-guangzhou.aliyuncs.com', 'i18n-text-china-gz')

languageFp = open("./localizable_config.json")
languageEntity = json.load(languageFp)
languageFp.close()

#下载OSS文件到本地文件。
# <yourObjectName>由包含文件后缀，不包含Bucket名称组成的Object完整路径，例如abc/efg/123.jpg。
# <yourLocalFile>由本地文件路径加文件名包括后缀组成，例如/users/local/myfile.txt。
if os.path.exists("./" + tmpDir):
    shutil.rmtree("./" + tmpDir)

os.makedirs("./" + tmpDir)

removeDefaultLanguage = languageEntity["removeDefaultLanguage"]
business = languageEntity["business"]
languageDic = languageEntity["languages"]
languageResourceModuleName = languageEntity["languageResourceModuleName"]
languageFileName = languageEntity["fileName"]
defaultLanguage = languageEntity["default"]
defaultTargetPath = "./" + languageResourceModuleName + "/src/main/res/values"
defaultXmlPath = "./" + tmpDir + "/default.xml"
businessCode = sys.argv[1]

def makeDefaultXml():
    try:
        fp = open(defaultXmlPath, "w")
        fp.write("<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n"
             "<resources xmlns:tools=\"http://schemas.android.com/tools\" tools:ignore=\"MissingTranslation,TypographyEllipsis\">\n"
             "</resources>")
        fp.close()
    except Exception  as err:
        print("\033[31makeDefaultXml error + " + str(err) + "\033[0m")
        os.exit(1)

def downloadData(language, business, businessCode):
    print("\033[32mstart load language:" + language + " address:" + "i18n/" + business + "/PRO/" + businessCode + "/" + language + ".json\033[0m")
    filepath = "./" + tmpDir + "/" + language + "-language.json"
    bucket.get_object_to_file("i18n/" + business + "/PRO/" + businessCode + "/" + language + ".json",  filepath)

    fp = open(filepath)

    json_data = json.load(fp)

    fp.close()
    print("\033[32mend load language:" + language + "\033[0m \033[34mSuccess\033[0m")
    return json_data

def readXml(language, androidModule, fileName, defaultLanguage):
    if language == defaultLanguage:
        filePath = "./" + androidModule + "/src/main/res/values/" + fileName
    else:
        filePath = "./" + androidModule + "/src/main/res/values-" + language + "/" + fileName
    
    print("\033[32mstart load language.xml:" + language + " address:" + filePath + "\033[0m")
    
    if os.path.exists(filePath):
        tree = etree.parse(filePath)
    else:
        tree = etree.parse(defaultXmlPath)
        # tree.setroot(tree)
    
    print("\033[32mend load language.xml:" + language + " address:" + filePath + "\033[0m \033[34mSuccess\033[0m")
    return tree

def xmlKeys(tree):
    keyValues = {}
    parent = tree.xpath("//resources")[0]
    contents = parent.xpath("//string")
    
    for content in contents:
        if content.attrib.has_key("version") and content.attrib["version"] == businessCode:
            parent.remove(content)
            continue
        
        keyValues[content.attrib["name"]] = ""

    return keyValues

def replace_first_special_letter(string):
    if len(string) > 0:
        first_letter = string[0]
        if first_letter == '@':
            new_string = '\\@' + string[1:]
            return new_string
        return string
    else:
        return string
def handleLanguage(language, json_data, tree, filepath):
    parent = tree.xpath("//resources")[0]
    contents = parent.xpath("//string")
    etree.indent(parent, space="    ")
    
    for content in contents:
        if content.attrib.has_key("version") and content.attrib["version"] == businessCode:
            parent.remove(content)
        content.tail = None
    
    for key in json_data:
        node = etree.SubElement(parent, "string")
        print(json_data[key])
        value = json_data[key].replace("\\\'", "\'")
        value = value.replace("\'", "\\\'")
        value = replace_first_special_letter(value)
        node.text = value.replace("\"", "\\\"")
        node.attrib["name"] = key
        node.attrib["version"] = businessCode
    
    result = etree.tostring(tree.getroot(), 
                        pretty_print=True,
                        xml_declaration=True,
                        encoding='UTF-8')
    
    result = xml.dom.minidom.parseString(result).toprettyxml()
    fp = open(filepath, "w")
    fp.write(result)
    fp.close()
    
def getJsonKeyValue(lan):
    filepath = "./" + tmpDir + "/" + lan + "-language.xml"
    json_data = downloadData(lan, business, businessCode)
    
    return json_data
    
makeDefaultXml()
englistJson = getJsonKeyValue(defaultLanguage)

for language in languageDic.keys():
    try:
        filepath = "./" + tmpDir + "/" + language + "-language.xml"
        json_data = downloadData(language, business, businessCode)
        tree = readXml(languageDic[language], languageResourceModuleName, languageFileName, defaultLanguage)
        treeKeys = xmlKeys(tree)
        
        for key in json_data:
            for i in specialString:
                if i in key:
                    raise Exception("contain invalidate key : " + key) 
            
            if key in treeKeys:
                raise Exception("duplicate key : " + key) 
            
            cnt = englistJson[key].count("%s") + englistJson[key].count("%d")
            checkCnt = json_data[key].count("%s") + json_data[key].count("%d")
            
            if cnt != checkCnt:
                raise Exception("different format(/d/s) count : " + key) 
        
        handleLanguage(language, json_data, tree, filepath)
    except Exception  as err:
        print("\033[31mdeal language： " + language + ", error + " + str(err) + "\033[0m")
        os.exit(1)

if os.path.exists(defaultTargetPath) == False:
    os.makedirs(defaultTargetPath)

for language in languageDic.keys():
    try:
        filepath = "./" + tmpDir + "/" + language + "-language.xml"
        targetPath = "./" + languageResourceModuleName + "/src/main/res/values-" + languageDic[language]
        
        if language == defaultLanguage:
            subprocess.Popen("cp " + filepath + " " + defaultTargetPath + "/" + languageFileName, shell=True)

        if removeDefaultLanguage == "true" and language == defaultLanguage:
            continue

        if os.path.exists(targetPath) == False:
            os.makedirs(targetPath)

        subprocess.Popen("mv " + filepath + " " + targetPath + "/" + languageFileName, shell=True)
    except:
        print("\033[31mdeal language： " + language + ", error\033[0m")
        os.exit(1)