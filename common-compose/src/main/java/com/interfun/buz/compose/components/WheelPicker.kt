package com.interfun.buz.compose.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.pager.PageSize
import androidx.compose.foundation.pager.VerticalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.interfun.buz.compose.styles.TextStyles
import kotlinx.coroutines.launch
import com.interfun.buz.common.R
import com.interfun.buz.compose.modifier.debouncedClickable
import kotlin.collections.List

/**
 * 滚轮选择器配置
 * @param visibleCount 可见的项目数量
 * @param itemHeight 每个项目的高度
 * @param textWidth 每个项目文本的宽度，如果是0则根据文本内容自适应宽度
 * @param textHeight 每个项目文本的高度
 * @param textStyle 每个项目的文本样式
 * @param textAlign 每个项目的文本对齐方式
 * @param contentAlignment 每个项目的内容对齐方式
 * @param indicatorShape 中间项目的指示器形状
 * @param selectedTextColor 选中项目的文本颜色
 * @param unselectedTextColor 未选中项目的文本颜色
 * @param indicatorBackgroundColor 中间项目的指示器背景颜色
 * @param gradientTopColors 每个项目的顶部渐变颜色
 * @param gradientBottomColors 每个项目的底部渐变颜色
 * @param gradientTopHeight 顶部渐变高度
 * @param gradientBottomHeight 底部渐变高度
 * @param enableTopGradient 是否启用顶部渐变
 * @param enableBottomGradient 是否启用底部渐变
 * @param enableHapticFeedback 是否启用点击和滚动的震动反馈
 */
data class WheelPickerConfig(
    val visibleCount: Int = 7,
    val itemHeight: Dp = 30.dp,
    val textWidth: Dp = 0.dp,
    val textHeight: Dp = 21.dp,
    val textStyle: TextStyle,
    val textAlign: TextAlign = TextAlign.Center,
    val contentAlignment: Alignment = Alignment.Center,
    val indicatorShape: Shape = RoundedCornerShape(8.dp),
    val selectedTextColor: Color = Color.White,
    val unselectedTextColor: Color = Color(0xFF808080),
    val indicatorBackgroundColor: Color = Color(0x1AFFFFFF),
    val gradientTopColors: List<Color> = listOf(Color(0xFF292929), Color(0x00292929)),
    val gradientBottomColors: List<Color> = listOf(Color(0x00292929), Color(0xFF292929)),
    val gradientTopHeight: Dp = itemHeight * 3,
    val gradientBottomHeight: Dp = itemHeight * 3,
    val enableTopGradient: Boolean = true,
    val enableBottomGradient: Boolean = true,
    val enableHapticFeedback: Boolean = true
)


@Composable
fun WheelPicker(
    items: List<String>,
    modifier: Modifier = Modifier,
    config: WheelPickerConfig,
    initialSelectedIndex: Int = 0,
    onItemSelected: (index: Int) -> Unit = {}
) {
    val coroutineScope = rememberCoroutineScope()
    val pagerState =
        rememberPagerState(initialPage = initialSelectedIndex, pageCount = { items.size })
    val verticalPadding = config.itemHeight * (config.visibleCount / 2)

    val haptic = LocalHapticFeedback.current
    val userScrollStarted = remember { mutableStateOf(false) }
    LaunchedEffect(pagerState) {
        snapshotFlow { pagerState.isScrollInProgress }
            .collect { inProgress ->
                userScrollStarted.value = inProgress
            }
    }
    LaunchedEffect(pagerState.currentPage) {
        if (userScrollStarted.value && config.enableHapticFeedback) {
            haptic.performHapticFeedback(HapticFeedbackType.TextHandleMove)
        }
        onItemSelected(pagerState.currentPage)
    }

    Box(
        modifier = modifier
            .height(config.itemHeight * config.visibleCount)
            .fillMaxWidth()
    ) {
        // 滚轮内容
        VerticalPager(
            state = pagerState,
            pageSize = PageSize.Fixed(config.itemHeight),
            beyondViewportPageCount = config.visibleCount / 2,
            contentPadding = PaddingValues(
                top = verticalPadding,
                bottom = verticalPadding
            ),
            key = { index -> items[index] },
            modifier = Modifier.fillMaxSize()
        ) { page ->
            val pageOffset = (pagerState.currentPage - page) + pagerState.currentPageOffsetFraction
            val distance = kotlin.math.abs(pageOffset)
            val textColor = if (distance < 0.5f) {
                config.selectedTextColor
            } else {
                config.unselectedTextColor
            }
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .debouncedClickable {
                        coroutineScope.launch {
                            pagerState.animateScrollToPage(page)
                        }
                    },
                contentAlignment = config.contentAlignment
            ) {
                Text(
                    text = items[page],
                    style = config.textStyle,
                    color = textColor,
                    textAlign = config.textAlign,
                    modifier = Modifier
                        .then(
                            if (config.textWidth == 0.dp) Modifier.wrapContentWidth()
                            else Modifier.width(config.textWidth)
                        )
                        .height(config.textHeight)
                )
            }
        }

        // 顶部渐变遮罩
        if (config.gradientTopColors.isNotEmpty() && config.enableTopGradient) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(config.gradientTopHeight)
                    .align(Alignment.TopCenter)
                    .background(
                        Brush.verticalGradient(colors = config.gradientTopColors)
                    )
            )
        }

        // 中间高亮指示器
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(config.itemHeight)
                .align(Alignment.Center)
                .clip(config.indicatorShape)
                .background(config.indicatorBackgroundColor)
        )

        // 底部渐变遮罩
        if (config.gradientBottomColors.isNotEmpty() && config.enableBottomGradient) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(config.gradientBottomHeight)
                    .align(Alignment.BottomCenter)
                    .background(
                        Brush.verticalGradient(colors = config.gradientBottomColors)
                    )
            )
        }

    }
}

/**
 * 24小时制时间选滚轮
 * @param selectedHour 选中的小时
 * @param selectedMinute 选中的分钟
 * @param onTimeSelected 时间选择回调
 * @param hourItems 小时滚轮数据
 * @param minuteItems 分钟滚轮数据
 * @param hourConfig 小时滚轮配置
 * @param minuteConfig 分钟滚轮配置
 * @param separatorContent 时间和分钟之间分隔符背景
 */
@Composable
fun TimePicker(
    modifier: Modifier = Modifier,
    selectedHour: Int = 0,
    selectedMinute: Int = 0,
    onTimeSelected: (hour: Int, minute: Int) -> Unit,
    hourItems: List<String> = (0..23).map { it.toString() },
    minuteItems: List<String> = (0..59).map { it.toString().padStart(2, '0') },
    hourConfig: WheelPickerConfig = WheelPickerConfig(
        textAlign = TextAlign.End,
        contentAlignment = Alignment.CenterEnd,
        textStyle = TextStyles.labelLarge(),
        indicatorShape = RoundedCornerShape(topStart = 8.dp, bottomStart = 8.dp),
        selectedTextColor = colorResource(R.color.color_text_white_important),
        unselectedTextColor = colorResource(R.color.color_text_black_tertiary),
    ),
    minuteConfig: WheelPickerConfig = WheelPickerConfig(
        textAlign = TextAlign.Start,
        contentAlignment = Alignment.CenterStart,
        textStyle = TextStyles.labelLarge(),
        indicatorShape = RoundedCornerShape(topEnd = 8.dp, bottomEnd = 8.dp),
        selectedTextColor = colorResource(R.color.color_text_white_important),
        unselectedTextColor = colorResource(R.color.color_text_black_tertiary),
    ),
    separatorContent: @Composable () -> Unit = {
        Box(
            modifier = Modifier
                .size(39.dp, hourConfig.itemHeight)
                .background(hourConfig.indicatorBackgroundColor)
        )
    }
) {
    var currentHour by remember { mutableIntStateOf(selectedHour) }
    var currentMinute by remember { mutableIntStateOf(selectedMinute) }

    Row(
        modifier = modifier.wrapContentHeight(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        WheelPicker(
            items = hourItems,
            modifier = Modifier.weight(1f),
            config = hourConfig,
            initialSelectedIndex = selectedHour,
            onItemSelected = {
                currentHour = it
                onTimeSelected(currentHour, currentMinute)
            }
        )

        separatorContent()

        WheelPicker(
            items = minuteItems,
            modifier = Modifier.weight(1f),
            config = minuteConfig,
            initialSelectedIndex = selectedMinute,
            onItemSelected = {
                currentMinute = it
                onTimeSelected(currentHour, currentMinute)
            }
        )
    }
}


@Preview(showBackground = true)
@Composable
fun TimePickerPreview() {
    var selectedHour by remember { mutableIntStateOf(12) }
    var selectedMinute by remember { mutableIntStateOf(30) }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFFF5F5F5)),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = "选择时间：${selectedHour.toString().padStart(2, '0')}:${
                selectedMinute.toString().padStart(2, '0')
            }",
            fontSize = 20.sp,
            modifier = Modifier.padding(bottom = 16.dp)
        )

        TimePicker(
            modifier = Modifier
                .background(Color(0xFF292929))
                .padding(horizontal = 20.dp),
            selectedHour = selectedHour,
            selectedMinute = selectedMinute,
            onTimeSelected = { hour, minute ->
                selectedHour = hour
                selectedMinute = minute
            }
        )
    }
}


@Preview(showBackground = true)
@Composable
fun SingleWheelPickerPreview() {
    val items = List(10) { "Item $it" }
    var selectedIndex by remember { mutableIntStateOf(0) }

    val config = WheelPickerConfig(
        visibleCount = 3,
        itemHeight = 30.dp,
        textStyle = TextStyles.labelLarge(),
        unselectedTextColor = Color.Gray,
        indicatorShape = RoundedCornerShape(8.dp),
        enableTopGradient = false,
        enableBottomGradient = false
    )

    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .height(300.dp)
                .padding(horizontal = 30.dp)
                .background(color = Color(0xFF292929))
                .padding(horizontal = 20.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Text(
                text = "当前选中：${items[selectedIndex]}",
                color = Color.White,
                fontSize = 18.sp,
                modifier = Modifier.padding(bottom = 30.dp)
            )

            WheelPicker(
                items = items,
                config = config,
                onItemSelected = { selectedIndex = it }
            )
        }
    }
}


