@file:Suppress("unused")

package com.interfun.buz.compose.ktx

import androidx.compose.runtime.Composable
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.rememberTextMeasurer
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.IntSize

@Composable
fun measureTextSize(text: String, style: TextStyle): IntSize {
    val textMeasurer = rememberTextMeasurer()
    return textMeasurer.measure(text = text, style = style).size
}

@Composable
fun measureTextWidth(text: String, style: TextStyle): Int {
    val textMeasurer = rememberTextMeasurer()
    return textMeasurer.measure(text = text, style = style).size.width
}

@Composable
fun measureTextHeight(text: String, style: TextStyle): Int {
    val textMeasurer = rememberTextMeasurer()
    return textMeasurer.measure(text = text, style = style).size.height
}

@Composable
fun measureTextWidthDp(text: String, style: TextStyle): Dp {
    val textMeasurer = rememberTextMeasurer()
    return textMeasurer.measure(text = text, style = style).size.width.pxToDp()
}

@Composable
fun measureTextHeightDp(text: String, style: TextStyle): Dp {
    val textMeasurer = rememberTextMeasurer()
    return textMeasurer.measure(text = text, style = style).size.height.pxToDp()
}