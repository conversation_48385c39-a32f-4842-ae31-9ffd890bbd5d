package com.interfun.buz.compose.components

import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.os.Build
import android.view.Gravity
import android.view.ViewGroup
import android.view.WindowManager
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color as ComposeColor
import androidx.compose.ui.input.pointer.pointerInteropFilter
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.LifecycleOwner
import androidx.compose.ui.platform.LocalSavedStateRegistryOwner
import androidx.lifecycle.setViewTreeLifecycleOwner
import androidx.savedstate.SavedStateRegistryOwner
import androidx.savedstate.setViewTreeSavedStateRegistryOwner
import com.interfun.buz.compose.R
import com.interfun.buz.compose.styles.TextStyles

/**
 * 自定义加载对话框类，支持触摸事件透传
 *
 * 基于 android.app.Dialog 实现，内嵌 ComposeView 来渲染 Compose UI
 * 通过正确配置 WindowManager.LayoutParams 实现真正的事件透传功能
 */
private class CustomLoadingDialog(
    context: Context,
    private val text: String,
    private val backgroundColor: ComposeColor,
    private val textColor: ComposeColor,
    private val loadingIconColor: Int,
    private val dimAmount: Float,
    private val allowTouchPassThrough: Boolean,
    private val lifecycleOwner: LifecycleOwner,
    private val savedStateRegistryOwner: SavedStateRegistryOwner,
    private val onDismissRequest: () -> Unit
) : Dialog(context, android.R.style.Theme_Translucent_NoTitleBar) {

    override fun onCreate(savedInstanceState: android.os.Bundle?) {
        super.onCreate(savedInstanceState)

        // 配置窗口属性
        window?.apply {
            // 设置背景透明
            setBackgroundDrawableResource(android.R.color.transparent)

            // 设置窗口属性
            attributes?.apply {
                width = WindowManager.LayoutParams.MATCH_PARENT
                height = WindowManager.LayoutParams.MATCH_PARENT
                gravity = Gravity.CENTER
            }

            // 移除焦点，防止拦截事件
            addFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE)

            // 根据是否允许透传设置窗口标志
            if (allowTouchPassThrough) {
                // 允许触摸事件透传到下层界面
                addFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE)
            } else {
                // 默认行为，拦截触摸事件
                clearFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE)
            }

            // 设置背景变暗程度
            setDimAmount(dimAmount)

            // 修复导航栏白色条带问题
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                // 设置系统栏背景绘制标志
                addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
                // 设置导航栏透明
                navigationBarColor = Color.TRANSPARENT
                // 设置状态栏透明
                statusBarColor = Color.TRANSPARENT
            }

            // Android Q+ 禁用导航栏对比度强制
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                isNavigationBarContrastEnforced = false
            }
        }

        // 创建 ComposeView 并设置内容
        val composeView = ComposeView(context).apply {
            // 设置 LifecycleOwner 以避免 ViewTreeLifecycleOwner not found 错误
            setViewTreeLifecycleOwner(lifecycleOwner)
            // 设置 SavedStateRegistryOwner 以修复 Composed into the View which doesn't propagateViewTreeSavedStateRegistryOwner! 错误
            setViewTreeSavedStateRegistryOwner(savedStateRegistryOwner)
            setViewCompositionStrategy(ViewCompositionStrategy.DisposeOnDetachedFromWindow)
            setContent {
                LoadingDialogContent(
                    text = text,
                    backgroundColor = backgroundColor,
                    textColor = textColor,
                    loadingIconColor = loadingIconColor,
                    allowTouchPassThrough = allowTouchPassThrough
                )
            }
        }

        setContentView(
            composeView,
            ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
        )

        // 设置取消监听器
        setOnCancelListener { onDismissRequest() }
        setOnDismissListener { onDismissRequest() }
    }
}

/**
 * 加载对话框的 Compose 内容
 *
 * @param text 显示的文本内容
 * @param backgroundColor 背景颜色
 * @param textColor 文本颜色
 * @param loadingIconColor 加载图标颜色
 * @param allowTouchPassThrough 是否允许触摸事件透传
 */
@Composable
private fun LoadingDialogContent(
    text: String,
    backgroundColor: ComposeColor,
    textColor: ComposeColor,
    loadingIconColor: Int,
    allowTouchPassThrough: Boolean
) {
    // 外层容器，根据是否允许透传来决定事件处理方式
    Box(
        modifier = Modifier
            .let { modifier ->
                if (allowTouchPassThrough) {
                    // 允许透传时，不拦截触摸事件
                    modifier
                } else {
                    // 不允许透传时，拦截所有触摸事件
                    modifier.pointerInteropFilter { true }
                }
            }
            .fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        // 根据 Figma 设计稿实现加载对话框UI
        // 使用纯色背景，不使用高斯模糊效果
        Box(
            modifier = Modifier
                .background(
                    color = backgroundColor,
                    shape = RoundedCornerShape(24.dp)
                )
                .padding(20.dp),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(6.dp)
            ) {
                // 使用 CommonLoadingLottie 组件显示加载动画
                CommonLoadingLottie(
                    modifier = Modifier.size(50.dp),
                    color = loadingIconColor
                )

                // 显示加载文本
                Text(
                    text = text,
                    style = TextStyles.bodyMedium(),
                    color = textColor,
                    textAlign = TextAlign.Center
                )
            }
        }
    }
}

/**
 * 通用加载对话框组件
 *
 * 基于 Figma 设计稿实现的加载对话框，使用纯色背景和 Lottie 动画。
 * 适用于需要显示加载状态的场景，如文件处理、网络请求等。
 *
 * @param text 显示的文本内容，如 "Processing..."、"Loading..." 等
 * @param backgroundColor 背景颜色，默认为半透明灰色 rgba(51, 51, 51, 0.5)
 * @param textColor 文本颜色，默认使用项目的主要白色文本颜色
 * @param loadingIconColor 加载图标颜色，默认为白色
 * @param dismissOnBackPress 是否允许按返回键关闭对话框，默认为 false（不允许）
 * @param dismissOnClickOutside 是否允许点击对话框外部区域关闭，默认为 false（不允许）
 * @param dimAmount 对话框背景变暗程度，范围 0.0f-1.0f，0.0f 为不变暗，1.0f 为完全变暗，默认为 0.6f
 * @param allowTouchPassThrough 是否允许触摸事件透传到下层界面，默认为 false（不透传）
 * @param onDismissRequest 对话框关闭回调，当用户点击对话框外部或按返回键时触发
 *
 * <AUTHOR> Assistant
 * @since 2025-06-18
 * @updated 2025-06-20 - 添加了 dismissOnBackPress、dismissOnClickOutside、dimAmount 参数
 * @updated 2025-06-20 - 添加了 allowTouchPassThrough 参数，支持触摸事件透传
 *
 * 使用示例：
 * ```
 * // 基础用法 - 不可关闭的加载对话框
 * CommonLoadingDialog(
 *     text = "Processing...",
 *     onDismissRequest = { /* 处理关闭逻辑 */ }
 * )
 *
 * // 可关闭的加载对话框
 * CommonLoadingDialog(
 *     text = "Loading...",
 *     dismissOnBackPress = true,
 *     dismissOnClickOutside = true,
 *     onDismissRequest = { /* 处理关闭逻辑 */ }
 * )
 *
 * // 无背景变暗效果的对话框
 * CommonLoadingDialog(
 *     text = "Processing...",
 *     dimAmount = 0.0f,
 *     onDismissRequest = { /* 处理关闭逻辑 */ }
 * )
 *
 * // 支持触摸事件透传的对话框
 * CommonLoadingDialog(
 *     text = "Loading...",
 *     allowTouchPassThrough = true,
 *     onDismissRequest = { /* 处理关闭逻辑 */ }
 * )
 * ```
 */
@Composable
fun CommonLoadingDialog(
    text: String,
    backgroundColor: ComposeColor = colorResource(R.color.color_background_4_default),
    textColor: ComposeColor = colorResource(R.color.color_text_white_primary),
    loadingIconColor: Int = R.color.white,
    dismissOnBackPress: Boolean = false,
    dismissOnClickOutside: Boolean = false,
    dimAmount: Float = 0.6f,
    allowTouchPassThrough: Boolean = false,
    onDismissRequest: () -> Unit = {}
) {
    val context = LocalContext.current
    val lifecycleOwner = androidx.lifecycle.compose.LocalLifecycleOwner.current
    val savedStateRegistryOwner = LocalSavedStateRegistryOwner.current

    // 使用自定义 Dialog 实现真正的事件透传
    val dialog = remember(
        text, backgroundColor, textColor, loadingIconColor,
        dimAmount, allowTouchPassThrough, onDismissRequest, savedStateRegistryOwner
    ) {
        CustomLoadingDialog(
            context = context,
            text = text,
            backgroundColor = backgroundColor,
            textColor = textColor,
            loadingIconColor = loadingIconColor,
            dimAmount = dimAmount,
            allowTouchPassThrough = allowTouchPassThrough,
            lifecycleOwner = lifecycleOwner,
            savedStateRegistryOwner = savedStateRegistryOwner,
            onDismissRequest = onDismissRequest
        ).apply {
            // 根据参数设置对话框行为
            setCancelable(dismissOnBackPress)
            setCanceledOnTouchOutside(dismissOnClickOutside && !allowTouchPassThrough)
        }
    }

    // 管理对话框的显示和隐藏
    DisposableEffect(dialog) {
        dialog.show()
        onDispose {
            if (dialog.isShowing) {
                dialog.dismiss()
            }
        }
    }
}




/**
 * CommonLoadingDialog 预览函数
 * 展示默认样式的加载对话框
 */
@Composable
@Preview
fun CommonLoadingDialogPreview() {
    InitPreview()
    CommonLoadingDialog(
        text = "Processing...",
        onDismissRequest = {}
    )
}

/**
 * CommonLoadingDialog 预览函数 - 自定义样式
 * 展示自定义颜色的加载对话框
 */
@Composable
@Preview
fun CommonLoadingDialogCustomPreview() {
    InitPreview()
    CommonLoadingDialog(
        text = "Loading data...",
        backgroundColor = ComposeColor(0x80000000), // 半透明黑色
        textColor = ComposeColor.White,
        loadingIconColor = R.color.color_text_highlight_default,
        onDismissRequest = {}
    )
}

/**
 * CommonLoadingDialog 预览函数 - 可关闭样式
 * 展示可通过返回键和点击外部关闭的加载对话框
 */
@Composable
@Preview
fun CommonLoadingDialogDismissiblePreview() {
    InitPreview()
    CommonLoadingDialog(
        text = "Loading...",
        dismissOnBackPress = true,
        dismissOnClickOutside = true,
        onDismissRequest = {}
    )
}

/**
 * CommonLoadingDialog 预览函数 - 无背景变暗
 * 展示无背景遮罩效果的加载对话框
 */
@Composable
@Preview
fun CommonLoadingDialogNoDimPreview() {
    InitPreview()
    CommonLoadingDialog(
        text = "Processing...",
        dimAmount = 0.0f,
        onDismissRequest = {}
    )
}

/**
 * CommonLoadingDialog 预览函数 - 触摸事件透传
 * 展示支持触摸事件透传到下层界面的加载对话框
 */
@Composable
@Preview
fun CommonLoadingDialogPassThroughPreview() {
    InitPreview()
    CommonLoadingDialog(
        text = "Loading...",
        allowTouchPassThrough = true,
        dimAmount = 0.3f, // 降低背景变暗程度，便于看到下层界面
        onDismissRequest = {}
    )
}
