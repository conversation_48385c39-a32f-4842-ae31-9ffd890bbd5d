package com.interfun.buz.compose.components

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.common.R
import com.interfun.buz.compose.ktx.asColor
import com.interfun.buz.compose.ktx.asDimension
import com.interfun.buz.compose.ktx.dpToPx
import com.interfun.buz.compose.ktx.rememberMutableBoolean
import com.interfun.buz.compose.modifier.debouncedClickable
import com.interfun.buz.compose.styles.TextStyles

/**
 * 选项卡数据类
 */
data class TabOption(
    val type: Int,
    val name: String
){
    companion object {
        val allOptionTab = TabOption(-1,R.string.ugc_share_all.asString())
    }
}

/**
 * 高性能、可扩展的选项卡组件
 *
 * @param options 选项列表
 * @param currentSelectTabTypeLambda 当前选中的索引
 * @param onSelectionChange 选择变化回调
 * @param modifier 修饰符
 * @param containerColor 容器背景色
 * @param selectorColor 选中项背景色
 * @param selectedTextColor 选中项文字颜色
 * @param unselectedTextColor 未选中项文字颜色
 * @param cornerRadius 圆角大小
 * @param containerHorizontalPadding 水平内边距
 * @param containerVerticalPadding 垂直内边距
 * @param spaceBetweenTabs 选项间距
 */
@Composable
fun OptionTabs(
    modifier: Modifier = Modifier,
    options: List<TabOption>,
    currentSelectTabTypeLambda: () -> Int,
    onSelectionChange: (TabOption) -> Unit,
    isEnableLambda: () -> Boolean = { true },
    containerColor: Color = R.color.neutral_5.asColor(),
    selectorColor: Color = R.color.color_background_6_default.asColor(),
    selectedTextColor: Color = R.color.text_white_important.asColor(),
    unselectedTextColor: Color = R.color.color_text_white_secondary.asColor(),
    cornerRadius: Dp = R.dimen.radius_full.asDimension(),
    spaceBetweenTabs: Dp = 0.dp,
    containerHorizontalPadding: Dp = 2.dp,
    containerVerticalPadding: Dp = 2.dp,
    itemHeight: Dp = 28.dp,
    animationDurationMs: Int = 300,
) {
    // 是否是首次加载, 不执行动画
    var isFirstInit by rememberMutableBoolean(true)
    val options = options.take(4)
    // 这里写死的宽度可能导致某些语言下长文案超出宽度
    val itemWidth = when(options.size){
        3 -> 87.33.dp
        4 -> 82.75.dp
        else -> 100.dp
    }
    
    Box(
        modifier = modifier
            .wrapContentSize()
            .background(
                color = containerColor,
                shape = RoundedCornerShape(cornerRadius)
            )
            .padding(horizontal = containerHorizontalPadding, vertical = containerVerticalPadding)

    ) {
        val animatedOffset = remember { Animatable(0f) }
        val itemWidthPx = itemWidth.dpToPx()
        val spaceBetweenTabsPx = spaceBetweenTabs.dpToPx()

        LaunchedEffect(currentSelectTabTypeLambda.invoke()) {
            val selectedIndex = options.indexOfFirst { it.type == currentSelectTabTypeLambda() }
            if (selectedIndex >= 0) {
                val targetOffset = selectedIndex * itemWidthPx + (selectedIndex - 1) * spaceBetweenTabsPx
                
                if (isFirstInit) {
                    // 首次加载时直接设置位置，不触发动画
                    animatedOffset.snapTo(targetOffset)
                    isFirstInit = false
                } else {
                    // 用户手动点击后，触发动画
                    animatedOffset.animateTo(
                        targetValue = targetOffset,
                        animationSpec = tween(durationMillis = animationDurationMs)
                    )
                }
            }
        }

        Box(
            modifier = Modifier
                .offset { IntOffset(animatedOffset.value.toInt(), 0) }
                .width(itemWidth)
                .height(itemHeight)
                .background(
                    color = selectorColor,
                    shape = RoundedCornerShape(cornerRadius)
                )
        )

        TabsContainers(
            modifier = Modifier,
            options = options,
            selectedType = currentSelectTabTypeLambda,
            onSelectionChange = onSelectionChange,
            isEnableLambda = isEnableLambda,
            selectedTextColor = selectedTextColor,
            unselectedTextColor = unselectedTextColor,
            spaceBetweenTabs = spaceBetweenTabs,
            itemWidth = itemWidth,
            itemHeight = itemHeight,
            animationDurationMs = animationDurationMs
        )
    }
}

@Composable
private fun TabsContainers(
    modifier: Modifier = Modifier,
    options: List<TabOption>,
    selectedType: () -> Int,
    onSelectionChange: (TabOption) -> Unit,
    isEnableLambda: () -> Boolean,
    selectedTextColor: Color = R.color.text_white_important.asColor(),
    unselectedTextColor: Color = R.color.color_text_white_secondary.asColor(),
    spaceBetweenTabs: Dp,
    itemWidth: Dp,
    itemHeight: Dp,
    animationDurationMs: Int,
){
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(spaceBetweenTabs),
        verticalAlignment = Alignment.CenterVertically
    ) {
        options.forEachIndexed {index, option ->
            val onClickCallback = remember(option) {
                { onSelectionChange(option) }
            }
            TabItem(
                option = option,
                textColor = if (option.type == selectedType()) selectedTextColor else unselectedTextColor,
                onClick = onClickCallback,
                isEnableLambda = isEnableLambda,
                itemWidth = itemWidth,
                itemHeight = itemHeight,
                colorAnimateTime = animationDurationMs
            )
        }
    }
}

/**
 * 单个选项卡项组件
 */
@Composable
private fun TabItem(
    option: TabOption,
    onClick: () -> Unit,
    isEnableLambda: () -> Boolean,
    textColor: Color,
    itemWidth: Dp,
    itemHeight: Dp,
    colorAnimateTime: Int
) {
    val transitionColor = animateColorAsState(
        targetValue = textColor,
        animationSpec = tween(
            durationMillis = colorAnimateTime,
            easing = FastOutSlowInEasing
        ), label = ""
    )

    Box(
        modifier = Modifier
            .debouncedClickable(enabled = isEnableLambda(), onClick = onClick)
            .width(itemWidth)
            .height(itemHeight),
        contentAlignment = Alignment.Center
    ) {
        Text(
            modifier = Modifier.wrapContentSize(),
            text = option.name,
            color = transitionColor.value,
            style = TextStyles.labelMedium(),
        )
    }
}

/**
 * 简化版本的选项卡组件，用于简单场景
 */
@Composable
fun SimpleOptionTabs(
    options: List<String>,
    selectedIndex: Int,
    onSelectionChange: (TabOption) -> Unit,
    modifier: Modifier = Modifier
) {
    val tabOptions = remember(options) {
        options.mapIndexed { _, title ->
            TabOption( 1,name = title)
        }
    }

    OptionTabs(
        options = tabOptions,
        currentSelectTabTypeLambda = {selectedIndex},
        onSelectionChange = onSelectionChange,
        modifier = modifier
    )
}

/**
 * 预览组件
 */
@Preview(showBackground = true, backgroundColor = 0xFF000000)
@Composable
private fun OptionTabsPreview() {
    InitPreview()
    val options = listOf(
        TabOption(0,"Option 1"),
        TabOption(1,"Option 2"),
        TabOption(2,"Option 3"),
        TabOption(3,"Option 4"),

    )

    var selectedType by remember { mutableIntStateOf(0) }

    Column(
        modifier = Modifier.padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 完整版本
        OptionTabs(
            options = options,
            currentSelectTabTypeLambda = {selectedType},
            onSelectionChange = { }
        )

        // 简化版本
        SimpleOptionTabs(
            options = listOf("Simple 1", "Simple 2", "Simple 3", "Simple 4"),
            selectedIndex = 1,
            onSelectionChange = { }
        )

        // 自定义样式
        OptionTabs(
            options = options.take(4),
            currentSelectTabTypeLambda = {2},
            onSelectionChange = { },
            containerColor = Color(0xFF1A1A1A),
            selectorColor = Color(0xFF007AFF),
            selectedTextColor = Color.White,
            unselectedTextColor = Color(0xFF8E8E93),
            cornerRadius = 12.dp
        )
    }
}