package com.interfun.buz.compose.components

import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.unit.coerceAtLeast
import androidx.compose.ui.unit.dp
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import com.interfun.buz.base.ktx.buzDp
import com.interfun.buz.compose.ktx.pxToDp


/**
 * A Modifier that applies dynamic bottom padding based on whether the device has a full-screen display
 * (no navigation bar, typically a notch screen). Applies 64dp padding for full-screen devices and
 * 34dp for devices with a navigation bar.
 */
@Composable
fun Modifier.notchAwareBottomPadding(miniSizePadding:Int): Modifier {
    val view = LocalView.current
    // Calculate bottom padding based on whether the device has a navigation bar
    val bottomPadding = remember {
        val windowInsets = ViewCompat.getRootWindowInsets(view)
        // A full-screen (notch) device typically has no navigation bar height
        val navigationBarHeight = windowInsets?.getInsets(WindowInsetsCompat.Type.navigationBars())?.bottom ?: 0
        val paddingBottom = (miniSizePadding - navigationBarHeight).coerceAtLeast(0.buzDp)
        paddingBottom
    }

    return this.then(
        Modifier.padding(bottom = bottomPadding.pxToDp())
    )
}