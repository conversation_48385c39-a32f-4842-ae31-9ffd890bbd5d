package com.interfun.buz.compose.components

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.viewinterop.AndroidView
import androidx.constraintlayout.compose.ConstraintLayout
import com.interfun.buz.base.ktx.DefaultCallback
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.common.R
import com.interfun.buz.common.widget.view.EmptyDataType
import com.interfun.buz.common.widget.view.EmptyDataView
import com.interfun.buz.common.widget.view.EmptyDataViewListener

@Composable
fun EmptyView(
    modifier: Modifier = Modifier,
    emptyType: EmptyDataType = EmptyDataType.NO_DATA,
    desc: String = R.string.no_results_found.asString(),
    buttonText: String? = null,
    buttonIconFont: String? = null,
    buttonType: Int? = null, // @see CommonButton#setType
    lottieSize: Int? = null,
    onButtonClicked: DefaultCallback? = null
) {
    ConstraintLayout(modifier = modifier.fillMaxSize()) {
        val (box) = createRefs()
        Box(
            modifier = Modifier
                .wrapContentSize()
                .constrainAs(box) {
                    top.linkTo(parent.top)
                    bottom.linkTo(parent.bottom)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    verticalBias = 0.3f
                }
        ) {
            AndroidView(
                modifier = Modifier.fillMaxWidth(),
                factory = { context ->
                    EmptyDataView(context)
                },
                update = {
                    it.setType(emptyType)
                    it.setText(desc)
                    if (buttonText != null) {
                        it.setButton(buttonText, buttonIconFont, buttonType)
                    }
                    if (null != onButtonClicked) {
                        it.setListener(object : EmptyDataViewListener {
                            override fun onButtonClicked() {
                                onButtonClicked()
                            }
                        })
                    }
                    if (null != lottieSize) {
                        it.setLottieSize(lottieSize)
                    }
                }
            )
        }
    }
}
