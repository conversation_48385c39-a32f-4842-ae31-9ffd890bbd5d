<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:background="@color/color_background_4_default"
    tools:ignore="SpUsage"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <!--标题栏区域-->
    <androidx.legacy.widget.Space
        android:id="@+id/spaceTitleBar"
        android:layout_width="0dp"
        android:layout_height="@dimen/title_bar_height"
        android:tag="@string/common_tag_drag_view"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.interfun.buz.chat.wt.view.QuiteModeLightBgView
        android:id="@+id/ivQuiteLight"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@+id/spaceTitleBar"
        app:layout_constraintStart_toStartOf="@+id/spaceTitleBar"
        app:layout_constraintTop_toTopOf="@+id/spaceTitleBar"
        app:round_top_end_radius="20dp"
        app:round_top_start_radius="20dp" />

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftvHeaderHandle"
        style="@style/iconfont_24"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:padding="8dp"
        android:text="@string/ic_arrow_down_small"
        android:textColor="@color/color_text_white_important"
        android:textSize="@dimen/general_font_size_24"
        app:layout_constraintBottom_toBottomOf="@+id/spaceTitleBar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/spaceTitleBar" />

    <androidx.legacy.widget.Space
        android:id="@+id/spaceTitleContent"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginEnd="20dp"
        app:layout_constraintBottom_toBottomOf="@id/spaceTitleBar"
        app:layout_constraintEnd_toStartOf="@+id/composeRightEntry"
        app:layout_constraintStart_toEndOf="@id/iftvHeaderHandle"
        app:layout_constraintTop_toTopOf="@id/spaceTitleBar" />

    <View
        android:id="@+id/vTitleContentBg"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@+id/ivPortrait"
        app:layout_constraintEnd_toEndOf="@+id/iftvMuteNotification"
        app:layout_constraintStart_toStartOf="@+id/spaceTitleContent"
        app:layout_constraintTop_toTopOf="@+id/ivPortrait" />

    <View
        android:id="@+id/viewLivePlaceCircle"
        android:layout_width="34dp"
        android:layout_height="34dp"
        android:background="@drawable/chat_circle_live_place"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/ivPortrait"
        app:layout_constraintEnd_toEndOf="@+id/ivPortrait"
        app:layout_constraintStart_toStartOf="@+id/ivPortrait"
        app:layout_constraintTop_toTopOf="@+id/ivPortrait" />

    <com.interfun.buz.common.widget.view.PortraitImageView
        android:id="@+id/ivPortrait"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="10dp"
        android:src="@drawable/album_draw_icon"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/iftvOfficialTag"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toEndOf="@id/iftvHeaderHandle"
        app:layout_constraintTop_toTopOf="parent" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/llTitle"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="2dp"
        android:gravity="center_vertical"
        android:orientation="vertical"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="@id/spaceTitleContent"
        app:layout_constraintEnd_toStartOf="@id/iftvMuteAutoPlay"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toEndOf="@id/ivPortrait"
        app:layout_constraintTop_toTopOf="@id/spaceTitleContent"
        app:layout_goneMarginEnd="2dp">

        <com.interfun.buz.common.widget.view.IconFontTextView
            android:id="@+id/iftvOfficialTag"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:layout_marginEnd="2dp"
            android:gravity="center"
            android:text="@string/ic_official"
            android:textColor="@color/basic_primary"
            android:textSize="18sp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/tvName"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tvName"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tvName"
            style="@style/text_title_medium"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:ellipsize="end"
            android:letterSpacing="0.0"
            android:maxLines="1"
            android:textColor="@color/text_white_important"
            app:layout_constraintBottom_toTopOf="@+id/tvUserState"
            app:layout_constraintStart_toEndOf="@+id/iftvOfficialTag"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed"
            app:layout_goneMarginStart="0dp"
            tools:text="Hubert Blaine Wolfeschlegelsteinhausenbergerdorff Sr" />


        <TextView
            android:id="@+id/tvUserState"
            style="@style/text_body_small"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:singleLine="true"
            android:textColor="@color/text_white_secondary"
            android:visibility="gone"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvName"
            tools:text="Active 5 Minutes agoxxxxxxxxx"
            tools:visibility="visible" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupGreenDotBg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="viewGreenDotBg, viewGreenDot"
        tools:visibility="visible" />

    <View
        android:id="@+id/viewGreenDotBg"
        android:layout_width="14dp"
        android:layout_height="14dp"
        android:background="@drawable/common_oval_background_3_default"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/viewGreenDot"
        app:layout_constraintEnd_toEndOf="@+id/viewGreenDot"
        app:layout_constraintStart_toStartOf="@+id/viewGreenDot"
        app:layout_constraintTop_toTopOf="@+id/viewGreenDot"
        tools:visibility="visible" />

    <View
        android:id="@+id/viewGreenDot"
        android:layout_width="6dp"
        android:layout_height="6dp"
        android:background="@drawable/common_oval_basic_primary"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/ivPortrait"
        app:layout_constraintEnd_toEndOf="@+id/ivPortrait"
        app:layout_constraintHorizontal_chainStyle="packed"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/iftvMuteAutoPlay"
        style="@style/iconfont_base"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:background="@drawable/ic_profile_mute_message"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/iftvMuteNotification"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toEndOf="@+id/llTitle"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_goneMarginEnd="10dp"
        tools:ignore="SpUsage"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/iftvMuteNotification"
        style="@style/iconfont_base"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginStart="2dp"
        android:layout_marginEnd="10dp"
        android:background="@drawable/ic_profile_mute_notification"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/groupPagLoading"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toEndOf="@+id/iftvMuteAutoPlay"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_goneMarginEnd="10dp"
        tools:ignore="SpUsage"
        tools:visibility="visible" />

    <com.interfun.buz.common.widget.view.loading.CircleLoadingView
        android:id="@+id/groupPagLoading"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:background="@drawable/common_oval_overlay_grey_20"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/ivPortrait"
        app:layout_constraintEnd_toEndOf="@id/spaceTitleContent"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintTop_toTopOf="@+id/ivPortrait"
        tools:visibility="visible" />

    <androidx.compose.ui.platform.ComposeView
        android:id="@+id/composeRightEntry"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@id/spaceTitleBar"
        app:layout_constraintEnd_toEndOf="@id/spaceTitleBar"
        app:layout_constraintTop_toTopOf="@id/spaceTitleBar"
        tools:layout_width="20dp" />

</merge>