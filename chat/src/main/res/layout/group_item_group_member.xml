<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/clRoot"
    android:layout_width="match_parent"
    android:layout_height="64dp"
    android:layout_marginHorizontal="20dp"
    tools:background="@color/black_100">

    <View
        android:id="@+id/vSplitLine"
        android:layout_width="0dp"
        android:layout_height="0.25dp"
        android:background="@color/color_foreground_neutral_important_disable"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@+id/ivPortrait"
        app:layout_constraintTop_toTopOf="parent" />


    <com.interfun.buz.common.widget.view.PortraitImageView
        android:id="@+id/ivPortrait"
        style="@style/iconfont_base"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginStart="20dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/common_portrait_placeholder" />

    <View
        android:id="@+id/roundOnlineBg"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:background="@drawable/group_dialog_item_online_bg"
        app:layout_constraintBottom_toBottomOf="@id/roundOnline"
        app:layout_constraintEnd_toEndOf="@id/roundOnline"
        app:layout_constraintStart_toStartOf="@id/roundOnline"
        app:layout_constraintTop_toTopOf="@id/roundOnline" />
    <!--191919-->
    <View
        android:id="@+id/roundOnline"
        android:layout_width="8dp"
        android:layout_height="8dp"
        android:background="@drawable/common_oval_basic_primary"
        app:layout_constraintBottom_toBottomOf="@id/ivPortrait"
        app:layout_constraintEnd_toEndOf="@id/ivPortrait" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/gOnline"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="roundOnlineBg,roundOnline" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="20dp"
        android:gravity="center_vertical"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/iftArrow"
        app:layout_constraintStart_toEndOf="@id/ivPortrait"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tvUserName"
            style="@style/text_label_large"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="start|center_vertical"
            android:maxLines="1"
            android:textAlignment="viewStart"
            android:textColor="@color/color_text_white_primary"
            tools:text="namexxxxxxxxxxxxxx" />

        <TextView
            android:id="@+id/tvDesc"
            style="@style/text_body_small"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/text_white_secondary"
            android:visibility="gone"
            tools:text="descriptiondescriptiondescriptiondescriptiondescription"
            tools:visibility="visible" />
    </LinearLayout>

    <TextView
        android:id="@+id/iftArrow"
        style="@style/text_body_medium"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_marginEnd="8dp"
        android:gravity="center"
        android:textColor="@color/color_text_white_secondary"
        android:textSize="16sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/iftvOnAir"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_goneMarginEnd="20dp"
        tools:text="Owner" />

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftvOnAir"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_marginEnd="20dp"
        android:text="@string/ic_on_air"
        android:textColor="@color/basic_primary"
        android:textSize="20dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>