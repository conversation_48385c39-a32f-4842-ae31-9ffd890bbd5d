<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/secondary_bg"
    tools:ignore="SpUsage">

    <View
        android:id="@+id/vDialog"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <!-- Title Bar -->
    <Space
        android:id="@+id/titleBar"
        android:layout_width="0dp"
        android:layout_height="@dimen/title_bar_new_height"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftClose"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:gravity="center"
        android:text="@string/ic_exit"
        android:textColor="@color/color_foreground_neutral_important_default"
        android:textSize="24dp"
        app:layout_constraintBottom_toBottomOf="@id/titleBar"
        app:layout_constraintDimensionRatio="1"
        app:layout_constraintStart_toStartOf="@id/titleBar"
        app:layout_constraintTop_toTopOf="@id/titleBar" />

    <TextView
        android:id="@+id/tvTitle"
        style="@style/text_title_medium"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="20dp"
        android:text="@string/export_voice"
        android:textColor="@color/color_foreground_neutral_important_default"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="@id/titleBar"
        app:layout_constraintEnd_toEndOf="@id/titleBar"
        app:layout_constraintStart_toStartOf="@id/titleBar"
        app:layout_constraintTop_toTopOf="@id/titleBar" />

    <!-- EmptyDataView -->
    <com.interfun.buz.common.widget.view.EmptyDataView
        android:id="@+id/emptyDataView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="40dp"
        android:visibility="gone"
        app:button_text="@string/retry"
        app:button_type="secondary_small"
        app:empty_type="no_network"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:text="@string/network_error_try_again" />

    <!-- Loading -->
    <LinearLayout
        android:id="@+id/loadingGroup"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.interfun.buz.common.widget.button.CommonButtonLoadingView
            android:id="@+id/loadingView"
            android:layout_width="64dp"
            android:layout_height="64dp"/>


        <TextView
            android:id="@+id/tvProcessing"
            style="@style/text_body_medium"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:text="@string/processing"
            android:textColor="@color/color_text_white_primary"/>

    </LinearLayout>

    <com.interfun.buz.media.player.view.BuzPlayerView
        android:id="@+id/videoContainer"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="30dp"
        android:layout_marginHorizontal="50dp"
        android:visibility="gone"
        app:round_radius="30dp"
        app:layout_constraintDimensionRatio="275:490"
        app:layout_constraintBottom_toTopOf="@id/shareItemList"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginBottom="40dp"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- Share Item List -->
    <androidx.compose.ui.platform.ComposeView
        android:id="@+id/shareItemList"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>


</androidx.constraintlayout.widget.ConstraintLayout>