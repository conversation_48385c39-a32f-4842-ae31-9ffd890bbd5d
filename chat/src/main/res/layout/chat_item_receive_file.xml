<?xml version="1.0" encoding="utf-8"?>
<com.interfun.buz.chat.common.view.widget.ChatMsgConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:background="@color/overlay_grey_10"
    tools:paddingHorizontal="@dimen/chat_list_horizontal_padding"
    tools:paddingVertical="@dimen/chat_item_between_same_padding_top">

    <TextView
        android:id="@+id/tvGroupMemberName"
        style="@style/caption"
        android:layout_width="wrap_content"
        android:layout_height="18dp"
        android:background="@drawable/common_r10_overlay_white_6"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxWidth="200dp"
        android:maxLines="1"
        android:paddingHorizontal="6dp"
        android:textColor="@color/text_white_secondary"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/replyView"
        app:layout_constraintStart_toStartOf="@id/clFileContentLayout"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_marginTop="@dimen/chat_item_between_different_padding_top"
        tools:text="Kathryn"
        tools:visibility="visible" />

    <androidx.legacy.widget.Space
        android:id="@+id/spaceContent"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@+id/clFileContentLayout"
        app:layout_constraintEnd_toEndOf="@+id/clFileContentLayout"
        app:layout_constraintStart_toStartOf="@+id/clFileContentLayout"
        app:layout_constraintTop_toTopOf="@+id/clFileContentLayout" />

    <com.interfun.buz.common.widget.view.PortraitImageView
        android:id="@+id/ivPortrait"
        android:layout_width="@dimen/chat_item_portrait_size"
        android:layout_height="@dimen/chat_item_portrait_size"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:src="@drawable/common_user_default_portrait_round"
        tools:visibility="visible" />

    <com.interfun.buz.chat.common.view.widget.ReplyItemView
        android:id="@+id/replyView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/chat_item_between_same_padding_top"
        android:layout_marginEnd="@dimen/chat_item_receive_margin_end_with_portrait"
        android:background="@color/color_background_1_default"
        android:paddingTop="@dimen/chat_dimen_reply_height_at_history_top"
        android:paddingBottom="@dimen/chat_dimen_reply_height_at_history_Bottom"
        android:visibility="gone"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toStartOf="@id/spaceContent"
        app:layout_constraintTop_toBottomOf="@id/tvGroupMemberName"
        app:layout_constraintWidth_min="@dimen/chat_dimen_mim_width_reply"
        app:layout_goneMarginTop="0dp"
        app:round_bottom_right_radius="@dimen/chat_item_bg_radius"
        app:round_top_left_radius="@dimen/chat_item_bg_radius"
        app:round_top_right_radius="@dimen/chat_item_bg_radius"
        tools:visibility="visible" />

    <com.interfun.buz.base.widget.round.RoundConstraintLayout
        android:id="@+id/clFileContentLayout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/chat_item_receive_margin_start_with_portrait"
        android:layout_marginTop="@dimen/chat_dimen_reply_height_at_history_diff"
        android:layout_marginEnd="@dimen/chat_item_receive_margin_end_with_portrait"
        android:background="@color/color_background_5_default"
        android:gravity="center_vertical"
        android:minWidth="@dimen/chat_item_bg_min_height"
        android:minHeight="@dimen/chat_item_bg_min_height"
        android:paddingHorizontal="@dimen/chat_message_item_horizontal_padding"
        android:paddingVertical="@dimen/chat_message_item_vertical_padding"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toEndOf="@+id/ivPortrait"
        app:layout_constraintTop_toBottomOf="@id/replyView"
        app:layout_goneMarginStart="0dp"
        app:layout_goneMarginTop="0dp"
        app:round_bottom_left_radius="@dimen/chat_item_bg_radius_min"
        app:round_bottom_right_radius="@dimen/chat_item_bg_radius"
        app:round_top_left_radius="@dimen/chat_item_bg_radius_min"
        app:round_top_right_radius="@dimen/chat_item_bg_radius">

        <ImageView
            android:id="@+id/ivFile"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:adjustViewBounds="true"
            android:scaleType="fitCenter"
            android:src="@drawable/file_receive_image_drawable"
            app:layout_constraintBottom_toBottomOf="@id/llTextContainer"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/llTextContainer"
            app:layout_constraintVertical_bias="0" />

        <TextView
            android:id="@+id/tvReceiveFileExtName"
            style="@style/text_body_medium"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="4dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textAlignment="center"
            android:textColor="@color/color_text_white_primary"
            android:textSize="9sp"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="@id/ivFile"
            app:layout_constraintEnd_toEndOf="@id/ivFile"
            app:layout_constraintStart_toStartOf="@id/ivFile"
            app:layout_constraintTop_toTopOf="@id/ivFile"
            tools:text="PDF" />

        <com.interfun.buz.common.widget.media.DownloadFileButton
            android:id="@+id/downloadFileButton"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:visibility="gone"
            app:bz_downloadBgSize="24dp"
            app:bz_downloadIconSize="12sp"
            app:bz_downloadProgressCircular="24dp"
            app:bz_downloadTrackThickness="2dp"
            app:layout_constraintBottom_toBottomOf="@id/ivFile"
            app:layout_constraintEnd_toEndOf="@id/ivFile"
            app:layout_constraintStart_toStartOf="@id/ivFile"
            app:layout_constraintTop_toTopOf="@id/ivFile" />

        <LinearLayout
            android:id="@+id/llTextContainer"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:orientation="vertical"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/ivFile"
            app:layout_constraintTop_toTopOf="parent">

            <com.interfun.buz.common.widget.view.MiddleEllipsizeTextView
                android:id="@+id/tvFileName"
                style="@style/text_body_large"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:maxLines="2"
                android:textColor="@color/color_text_white_important"
                tools:text="file long file file long file file long file file long file.pdf" />

            <TextView
                android:id="@+id/tvFileSize"
                style="@style/text_body_medium"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:maxLines="1"
                android:textColor="@color/color_text_white_secondary"
                android:textSize="14sp"
                tools:text="100KB" />
        </LinearLayout>
    </com.interfun.buz.base.widget.round.RoundConstraintLayout>

    <View
        android:id="@+id/vDownloadClickArea"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/clFileContentLayout"
        app:layout_constraintEnd_toEndOf="@id/clFileContentLayout"
        app:layout_constraintStart_toStartOf="@id/clFileContentLayout"
        app:layout_constraintTop_toTopOf="@id/clFileContentLayout"
        tools:visibility="visible" />
</com.interfun.buz.chat.common.view.widget.ChatMsgConstraintLayout>