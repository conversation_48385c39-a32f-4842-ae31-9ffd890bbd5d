package com.interfun.buz.chat.wt.utils

import com.interfun.buz.base.ktx.toSafeLong
import com.interfun.buz.chat.DND.DNDManager
import com.interfun.buz.chat.wt.manager.WTQuietModeManager
import com.interfun.buz.common.constants.TrackConstant
import com.interfun.buz.common.ktx.trackString
import com.interfun.buz.common.tracker.trackerIMString
import com.interfun.buz.common.utils.BuzTracker
import com.interfun.buz.common.utils.CommonTracker
import com.lizhi.component.basetool.ntp.NtpTime
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch

object WTTracker {

    /** 结果反馈-群聊/私聊开始播放对讲机语音，获取结果时上报
     **/
    fun onWTMsgPlay(
        targetId: String,
        convType: IM5ConversationType,
        isVoiceFilterMsg:Boolean,
        source: String? = null,
        contentName: String? = null,
        isRobot: Boolean,
        traceId: String? = null,
        duration:Int,
        filterID:Long = 0
    ) {
        GlobalScope.launch{
            val pageBusinessType = trackerIMString(targetId.toSafeLong(),convType, diffOfficial = false, diffSelf = true)

            if (convType != IM5ConversationType.PRIVATE && convType != IM5ConversationType.GROUP) {
                return@launch
            }
            val isGroup = convType == IM5ConversationType.GROUP
            BuzTracker.onResult {
                put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2022102805")
                put(TrackConstant.KEY_RESULT_TYPE, "play_walkie_talkie_voice")
                put(TrackConstant.KEY_PAGE_TYPE, "chat")
                put(TrackConstant.KEY_PAGE_BUSINESS_ID, targetId)
                put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, pageBusinessType)
                put(TrackConstant.KEY_IS_SUCCESS, true.trackString)
                put(TrackConstant.KEY_SOURCE, source ?: "")
                put(TrackConstant.KEY_CONTENT_NAME, contentName ?: "")
                put(TrackConstant.KEY_LOG_TIME, NtpTime.nowForce().toString())
                put(TrackConstant.KEY_BUSINESS_ID,if (isVoiceFilterMsg) "Y" else "N")
                put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT,duration.toString())
                put(TrackConstant.KEY_ELEMENT_BUSINESS_ID,filterID)
                traceId?.let {
                    put(TrackConstant.KEY_CONTENT_ID, it)
                }
            }
        }

    }

    /**
     * 元素点击-好友展示弹窗，点击好友/群（不区分是否在线)时上报
     */
    fun postOnlineMembersClick(businessType: String, businessId: String){
        CommonTracker.postClickEvent(
            "AC2022112501",
            "在线好友展示弹窗",
            "好友",
            "chat",
            element_business_type = businessType,
            element_business_id = businessId
        )
    }

    /**
     * 元素点击-对讲机首页，点击头像上方的未读按钮时上报
     */
    fun postUnreadMsgClick(businessType: String, businessId: String){
        CommonTracker.postClickEvent(
            "AC2022112502",
            "对讲机首页",
            "未读消息",
            "chat",
            element_business_type = businessType,
            element_business_id = businessId
        )
    }

    /**
     * 元素点击-对讲机首页，点击右下角的回听按钮时上报
     */
    fun postLeaveMsgEntryClick(businessType: String, businessId: String, businessNum: Int){
        CommonTracker.postClickEvent(
            "AC2022112503",
            "对讲机首页",
            "回听",
            "chat",
            element_business_type = businessType,
            element_business_id = businessId,
            business_num = businessNum
        )
    }

    /**
     * 元素点击-语音消息列表，手动点击语音消息时上报
     */
    fun postLeaveMsgPlayClick(
        pageBusinessType: String,
        pageBusinessId: String,
        businessNum: Int,
        elementBusinessType: String?
    ) {
        CommonTracker.postClickEvent(
            "AC2022112505",
            "语音消息列表",
            "播放语音消息",
            "chat",
            page_business_type = pageBusinessType,
            page_business_id = pageBusinessId,
            business_num = businessNum,
            element_business_type = elementBusinessType
        )
    }

    fun postReportQuietModeResult(isEnableQuiet: Boolean, isSuccess: Boolean, isStartUp: Boolean,isInDND: Boolean) {
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2023041001")
            put(TrackConstant.KEY_RESULT_TYPE, "receive_quiet_mode")
            put(TrackConstant.KEY_PAGE_TYPE, "home_setting")
            val content = if (isEnableQuiet.not()) "available" else "quiet"
            put(TrackConstant.KEY_CONTENT_NAME, content)
            put(TrackConstant.KEY_SOURCE, "1")
            put(TrackConstant.KEY_IS_SUCCESS, if (isSuccess) "success" else "fail")
            put(TrackConstant.KEY_BUSINESS_ID, "sync_dnd") // android不需要权限授权，这里上报sync_dnd
            val dndMode = if (isInDND) {
                "detected_DND"
            } else {
                "not_detected_DND"
            }
            put(TrackConstant.KEY_CONTENT_ID, dndMode)
            val elementBusinessType = if (isStartUp) {
                "start-up"
            } else {
                "not-start-up"
            }
            put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, elementBusinessType)

        }
    }

    fun postOnQuietModeClick(fromShowMuteGuideTip: Boolean, isInDND: Boolean) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2023062901")
            put(TrackConstant.KEY_TITLE, "首页")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "模式切换")
            put(
                TrackConstant.KEY_ELEMENT_BUSINESS_TYPE,
                if (WTQuietModeManager.isQuietModeEnable.not()) "available" else "quiet"
            )
            put(TrackConstant.KEY_SOURCE, "1")
            put(
                TrackConstant.KEY_BUSINESS_NUM,
                if (fromShowMuteGuideTip) "come_from_official_account" else "not_come_from_official_account"
            )
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, "sync_dnd") // android不需要权限授权，这里上报sync_dnd
            val dndMode = if (isInDND) {
                "open_DND"
            } else {
                "not_open_DND"
            }
            put(TrackConstant.KEY_PAGE_STATUS, dndMode)
        }
    }

    fun postOpenAlbumInHomeClick(isGroup: Boolean, hasAlbumPermission: Boolean){
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024032501")
            put(TrackConstant.KEY_TITLE, "首页")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "home_send_media")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, if (isGroup) "group" else "private")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, if (hasAlbumPermission) "yes" else "no")
        }
    }

    /**
     * When user click the add friends button in the home page
     */
    fun onClickAddFriendWTPage() {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024081201")
            put(TrackConstant.KEY_TITLE, "home_page")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "home_add_friend")
            put(TrackConstant.KEY_PAGE_TYPE, "home")
        }
    }

    /**
     * When user change the chat that in the middle by scroll or clicking
     */
    fun onScrollOrClickWTItem(type: String, targetId: Long?) {
        BuzTracker.onAppViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AVS2024081202")
            put(TrackConstant.KEY_TITLE, "home_page")
            put(TrackConstant.KEY_PAGE_TYPE, "home")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, type)
            put(TrackConstant.KEY_PAGE_CONTENT, targetId.toString())
        }
    }
}