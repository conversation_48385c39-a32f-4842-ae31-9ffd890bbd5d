package com.interfun.buz.chat.privy.view.block

import androidx.core.text.buildSpannedString
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import com.interfun.buz.base.ktx.*
import com.interfun.buz.base.utils.BusUtil
import com.interfun.buz.chat.R
import com.interfun.buz.chat.databinding.ChatFragmentMsgListBinding
import com.interfun.buz.chat.privy.viewmodel.PrivateChatInfoViewModel
import com.interfun.buz.chat.privy.viewmodel.PrivateChatMsgViewModelNew
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.base.BaseFragment
import com.interfun.buz.common.base.binding.BaseBindingBlock
import com.interfun.buz.common.bean.user.FriendApplySource
import com.interfun.buz.common.constants.ProfileSource
import com.interfun.buz.common.database.entity.UserRelationInfo
import com.interfun.buz.common.eventbus.MuteStatusUpdateEvent
import com.interfun.buz.common.ktx.*
import com.interfun.buz.common.manager.cache.user.UserRelationCacheManager
import com.interfun.buz.common.manager.user.FriendStatusManager
import com.interfun.buz.common.service.ContactsService
import com.interfun.buz.domain.record.entity.RecordBgType
import com.interfun.buz.domain.record.viewmodel.RecordVoiceViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * <AUTHOR>
 * @date 2022/11/23
 * @desc
 */
class PrivateChatUserInfoBlock(
    val fragment: BaseFragment,
    val userId: Long,
    val contacts: UserRelationInfo? = null,
    binding: ChatFragmentMsgListBinding
) : BaseBindingBlock<ChatFragmentMsgListBinding>(binding) {

    private val infoViewModel by fragment.fragmentViewModels<PrivateChatInfoViewModel>()
    private val recordViewModel by fragment.viewModels<RecordVoiceViewModel>()
    val chatMsgViewModel by lazy { ViewModelProvider(fragment)[PrivateChatMsgViewModelNew::class.java] }

    override fun initData() {
        super.initData()
        updateWTOnlineStatusAndOfficialTag()
        FriendStatusManager.friendStatusChangeFlow.collectIn(fragment.viewLifecycleOwner) {
            if (it?.updatedUserId?.contains(userId) == true) {
                updateWTOnlineStatusAndOfficialTag()
            }
        }
        BusUtil.observe<MuteStatusUpdateEvent>(fragment.viewLifecycleOwner) {
            if (it.targetId == userId) {
                infoViewModel.requestMuteInfo(userId)
            }
        }
        infoViewModel.getUserStateInfoFlow(userId)
            .collectIn(fragment.viewLifecycleOwner) { userStateInfo ->
                binding.clChatListHeader.updateUserState(userStateInfo)
            }
        observeMuteInfoChange()
        observeUserInfoChange()
        infoViewModel.requestUserInfo(userId)
        infoViewModel.requestMuteInfo(userId)
    }

    override fun initView() {
        super.initView()
        binding.clChatListHeader.onClickTitleContent  {
            openPrivateChatProfileDialog(userId)
        }
    }

    private fun observeUserInfoChange() {
        infoViewModel.userInfoFlow.collectIn(fragment.viewLifecycleOwner) { userInfo ->
            updateUserName(userInfo)
            updateUserPortrait(userInfo)
            if (userInfo?.isRobot == true) {
                recordViewModel.updateRecordBgType(RecordBgType.Robot)
            } else {
                recordViewModel.updateRecordBgType(RecordBgType.Normal)
            }
        }
    }

    private fun observeMuteInfoChange() {
        infoViewModel.isMuteMsgFlow.collectIn(fragment.viewLifecycleOwner) { isMute ->
            binding.clChatListHeader.updateHeaderMuteStatus(isMute, null)
        }

        infoViewModel.isMuteNotificationFlow.collectIn(fragment.viewLifecycleOwner) { isMute ->
            binding.clChatListHeader.updateHeaderMuteStatus(null, isMute)
        }
    }

    private fun updateUserName(userInfo: UserRelationInfo?) {
        val tvName = buildSpannedString {
            val name = contacts?.getDisplayName()
                ?: userInfo?.getContactFirstName() ?: return
            if (userInfo?.isRobot == true) {
                R.drawable.common_icon_ai_flag.asDrawable()
                    ?.let { appendCenterImage(it, 18.dp, 18.dp) }
                appendSpace(4.dp)
            }
            append(name)
        }
        binding.clChatListHeader.setHeaderName(tvName)
    }

    private fun updateUserPortrait(userInfo: UserRelationInfo?) {
        binding.clChatListHeader.setUserPortrait(userInfo?.portrait)
    }


    private fun updateWTOnlineStatusAndOfficialTag() {
        fragment.viewLifecycleScope.launch(Dispatchers.IO) {
            val userStatusInfo = FriendStatusManager.getStatusInfo(userId)
            val allInfo = UserRelationCacheManager.getUserRelationInfoByUidSync(userId)
            val isOfficial = allInfo?.isOfficial == true
            val isRobot = allInfo?.isRobot == true
            withContext(Dispatchers.Main) {
                if (isOfficial) {
                    binding.clChatListHeader.updateOfficialTagVisibility(true)
                    binding.clChatListHeader.updateGreenDotVisibility(false)
                }
                binding.clChatListHeader.updateGreenDotStatus(userStatusInfo, isRobot, userId)
            }
        }
    }

    private fun openPrivateChatProfileDialog(userId: Long) {
        if (userId.isMe()) {
            return
        }
        routerServices<ContactsService>().value!!.getProfileDialog(
            userId,
            source = FriendApplySource.CHAT_HISTORY_NAME, /* username at top of the chat list source */
            businessId = null,
            trackerSource = ProfileSource.CHAT_HISTORY_NAME.source
        ).showDialog(fragment.activity)
    }
}