@file:OptIn(DelicateCoroutinesApi::class)

package com.interfun.buz.chat.wt.manager

import android.annotation.SuppressLint
import android.media.AudioAttributes
import android.media.AudioManager
import androidx.media3.common.util.UnstableApi
import com.interfun.buz.base.coroutine.CloseableCoroutineScope
import com.interfun.buz.base.coroutine.withReentrantLock
import com.interfun.buz.base.ktx.*
import com.interfun.buz.base.utils.VibratorUtil
import com.interfun.buz.base.widget.view.animContainer.AnimContainerView
import com.interfun.buz.chat.R
import com.interfun.buz.chat.common.manager.ChatQuickReactPlayManager
import com.interfun.buz.chat.common.manager.LastReadMsgManager
import com.interfun.buz.chat.common.manager.VideoRecordStateManager
import com.interfun.buz.chat.common.utils.AsrTracker
import com.interfun.buz.chat.common.utils.ChatTracker
import com.interfun.buz.chat.common.utils.DNDTracker
import com.interfun.buz.chat.voicemoji.manager.*
import com.interfun.buz.chat.wt.entity.*
import com.interfun.buz.chat.wt.manager.MessageState.PLAYING
import com.interfun.buz.chat.wt.service.EarphonesNotificationManager
import com.interfun.buz.chat.wt.utils.WTTracker
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.constants.CommonConstant.MAX_RECORD_DURATION
import com.interfun.buz.common.constants.CommonMMKV
import com.interfun.buz.common.constants.TrackConstant
import com.interfun.buz.common.database.entity.MediaCacheType
import com.interfun.buz.common.ktx.*
import com.interfun.buz.common.manager.*
import com.interfun.buz.common.manager.cache.user.UserRelationCacheManager
import com.interfun.buz.common.manager.chat.CallPendStatus
import com.interfun.buz.common.manager.chat.ChannelPendStatusManager
import com.interfun.buz.common.manager.chat.VoiceConflictType
import com.interfun.buz.common.manager.chat.VoiceConflictTypeManager
import com.interfun.buz.common.manager.storage.BuzMediaRecordManager
import com.interfun.buz.common.manager.update.UpdateVersionManager
import com.interfun.buz.common.service.FloatModuleService
import com.interfun.buz.common.service.HomeService
import com.interfun.buz.common.service.RealTimeCallService
import com.interfun.buz.common.utils.BuzTracker
import com.interfun.buz.common.utils.NotificationUtil
import com.interfun.buz.common.utils.RingtonePlayer
import com.interfun.buz.common.voiceemoji.IVoiceEmojiSupport
import com.interfun.buz.common.widget.view.VoiceGifVideoCache
import com.interfun.buz.domain.record.helper.RecordStatusHelper
import com.interfun.buz.domain.voiceemoji_im.ktx.convert
import com.interfun.buz.domain.voiceemoji_im.manager.VoiceGifAgent
import com.interfun.buz.im.IMAgent
import com.interfun.buz.im.entity.BuzNotifyType
import com.interfun.buz.im.entity.IMSendStateEvent
import com.interfun.buz.im.entity.isAsrEditMsg
import com.interfun.buz.im.ktx.*
import com.interfun.buz.im.message.BuzVoiceGifMsg
import com.interfun.buz.im.message.BuzVoiceMsg
import com.interfun.buz.im.message.WTVoiceEmojiMsg
import com.interfun.buz.im.track.IMTracker
import com.interfun.buz.im.util.IMMsgIdentity
import com.interfun.buz.im.util.toMsgIdentity
import com.interfun.buz.onair.standard.CallNotificationOnAirManager
import com.interfun.buz.onair.standard.IGlobalOnAirController
import com.interfun.buz.onair.standard.OnAirInvitingDlgStatus
import com.lizhi.component.basetool.common.AppStateWatcher
import com.lizhi.component.basetool.ntp.NtpTime
import com.lizhi.component.tekiplayer.MediaItem
import com.lizhi.component.tekiplayer.PlayEventListener
import com.lizhi.component.tekiplayer.controller.PlayerState
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import com.lizhi.im5.sdk.conversation.IM5ConversationType.PRIVATE
import com.lizhi.im5.sdk.message.IM5CheckPerferredUrl
import com.lizhi.im5.sdk.message.IMessage
import com.lizhi.im5.sdk.message.MsgDirection
import com.lizhi.im5.sdk.message.model.IM5RecallMessage
import com.lizhi.im5.sdk.message.model.IM5VoiceMessage
import com.lizhi.im5.sdk.utils.IM5MsgUtils
import com.yibasan.lizhifm.lzlogan.Logz
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import okhttp3.internal.toLongOrDefault
import java.util.Collections
import java.util.LinkedList
import kotlin.coroutines.CoroutineContext
import kotlin.coroutines.EmptyCoroutineContext
import kotlin.math.abs

enum class VoiceGifPlayState {
    START,
    END
}

class VoiceGifState(
    val serMsgId:String,
    val state:VoiceGifPlayState, // 1:Play, 2:End
)

enum class VoicePlayState {
    IDLE,
    BUFFERING,
    PLAYING,
    PAUSE,
}

enum class MessageState {
    IDLE,
    PLAYING,
    PAUSE
}

object WTMessageManager {

    private val TAG = "WTMessageManager"
    private val msgList = Collections.synchronizedList(LinkedList<IMessage>())
    private val mutex = Mutex()
    private var nextChecking = false//防止同时进行next检查
    private val floatService by lazy { routerServices<FloatModuleService>().value }
    val messageFlow by lazy {
        MutableStateFlow<Pair<RealTimeMessage?, MessageState>>(
            Pair(
                null,
                MessageState.IDLE
            )
        )
    }

    // 将消息真正播放的时机发送出去。
    val messagePlayStateFlow by lazy { MutableSharedFlow<VoiceGifState>()}

    val voicePlayStateFlow by lazy {
        MutableStateFlow<Pair<RealTimeMessage?, VoicePlayState>>(
            Pair(
                null,
                VoicePlayState.IDLE
            )
        )
    }

    val playingMsgFlow = messageFlow.map {
        val (msg, state) = it
        if (state == MessageState.PLAYING && msg is IMPushMessage) {
            msg.message
        } else {
            null
        }
    }

    //todo 目前这个消息Flow的播放有 mute/quietMode下收到消息 和 voice call通话中收到的消息 popup展示，但最好建议两者场景分开
    val quietModeReceiveMsg by lazy { MutableSharedFlow<Pair<BuzNotifyType, IMessage>>() }
    // 专门给VoiceGif中没有音频的消息使用的。
    val receiveNoVoiceMsg by lazy { MutableSharedFlow<Pair<BuzNotifyType, IMessage>>() }
    val muteMessageReceiveMsg by lazy { MutableSharedFlow<Pair<BuzNotifyType, IMessage>>() }
    val takingVideoModeReceiveMsg by lazy { MutableSharedFlow<Any?>() }

    val isPlaying: Boolean
        get() {
            val (msg, state) = messageFlow.value
            return msg != null && state == PLAYING
        }

    val isBuzVoiceMsgPlaying: Boolean
        get() {
            val (msg, state) = messageFlow.value
            return msg is IMPushMessage && state == PLAYING && (msg.message.content is BuzVoiceMsg)
        }

    val isIMPlaying: Boolean
        get() {
            val (msg, state) = messageFlow.value
            return msg is IMPushMessage && state == PLAYING
        }
    val currentPlayingConvId: String?
        get() {
            val (msg, state) = messageFlow.value
            return if (state == PLAYING) {
                msg?.getConversationId()
            } else {
                null
            }
        }

    /**
     * 记录最新的已发送的voicegif，播放的时候只会播放最新的
     */
    private var newestVoiceGifMsgId = 0L

    private fun isTargetPlaying(msgId: Long): Boolean {
        val (msg, state) = messageFlow.value
        return msg != null && msg is IMPushMessage && msg.message.msgId == msgId && state == PLAYING
    }

    private fun isTargetPlayingBySerMsgId(serMsgId: String): Boolean {
        val (msg, state) = messageFlow.value
        return msg != null && msg is IMPushMessage && msg.message.serMsgId == serMsgId && state == PLAYING
    }


    var currentPlayingMsg: RealTimeMessage? = null

    var currentSendPlayingMsg: RealTimeMessage? = null

    private var playNextScope =
        CloseableCoroutineScope(SupervisorJob() + Dispatchers.Main.immediate)

    //记录当前是否 enterConversation进入某个会话中，leaveConversation时清空记录
    private var currentEnterConversationIdList = mutableListOf<String>()
    var currentEnterConversation: Pair<IM5ConversationType, String>? = null

    @SuppressLint("StaticFieldLeak")
    private val audioFocusManager = BuzAudioFocusManager(
        application, TAG, FocusRequestConfig(
            focusGain = AudioManager.AUDIOFOCUS_GAIN_TRANSIENT_MAY_DUCK,
            contentType = AudioAttributes.CONTENT_TYPE_MUSIC,
            usage = AudioAttributes.USAGE_MEDIA,
            willPauseWhenDucked = true
        ) { focus ->
            handleAudioFocusChange(focus)
        }
    )
    private var clearAllDelayJob: Job? = null
    private var hadShownVolumeMutedToast = false
    private const val playCutTime = 500L
    fun enterConversation(convType: IM5ConversationType, conversationId: String) {
        currentEnterConversation = convType to conversationId
        if (currentEnterConversationIdList.contains(conversationId).not()) {
            currentEnterConversationIdList.add(conversationId)
        }
    }

    fun leaveConversation(conversationId: String) {
        currentEnterConversation = null
        if (currentEnterConversationIdList.contains(conversationId)) {
            currentEnterConversationIdList.remove(conversationId)
        }
    }

    /**
     * the result 表示当前正在指定的conversationId会话中
     */
    fun findEnterConversationId(conversationId: String): Boolean {
        return currentEnterConversationIdList.contains(conversationId)
    }

    init {
        observeMessagePush()
        observeMessageSend()
        observeWalkieTalkieState()
        observeCameraState()
        observeMessageFlow()
        observePlayerState()
        observeOtherVoiceMessagePlayerConflict()
        observerVoiceMojiPreviewState()
        observerDriveModelManager()
    }

    fun login() {
        val ntpTime = NtpTime.now()
        Logz.tag(TAG).d("login ntpTime $ntpTime")
        try {
            if (ntpTime != null) {
                val cutTime = System.currentTimeMillis()
                val diffValue = ntpTime - cutTime
                BuzTracker.onTechTrack {
                    put(TrackConstant.KEY_EXCLUSIVE_ID,"TT2024121601")
                    put(TrackConstant.KEY_EVENT_NAME,"NtpDifferenceCurtime")
                    put(TrackConstant.KEY_NUMBER_1,diffValue)
                }
            }
        } catch (e: Exception) {
            Logz.tag(TAG).e(e)
        }
    }

    fun logout() {
        clearAll()
    }

    private fun observeOtherVoiceMessagePlayerConflict() {
        launch {
            //the Play priority conflicts with WTLeaveMsgPlayerManager
            WTLeaveMsgPlayerManager.playStateFlow.collect { (state, msg) ->
                when (state) {
                    LMPlayerState.PLAY_LM_MSG, LMPlayerState.PLAY_WT_MSG -> {
                        logInfo(TAG, "observeLeaveMessagePlayerState: ${state}")
                        VoiceConflictTypeManager.addConflictType(VoiceConflictType.ChatVoiceType)
                        //- 如果用户正在播放实时语音消息，此时主动点击回听语音留言时，
                        // 则当前正在播放的实时语音停止播放（后续也不再自动播放），等语音留言播放完成后，顺序播放其他的实时语音
                        if (messageFlow.value.second == PLAYING || messageFlow.value.second == MessageState.PAUSE) {
                            messageFlow.value.first?.let { realTimeMessage ->
                                realTimeMessage.dispatchSuspend(
                                    imPushMessage = { imPushMessage ->
                                        remove(imPushMessage.message)
                                    }
                                )
                            }
                        }
                        msg?.let { iMessage ->
                            remove(iMessage)
                        }
                        stop()
                    }

                    LMPlayerState.IDLE -> {
                        logInfo(TAG, "observeLeaveMessagePlayerState: IDLE")
                        nextAsync("LMPlayerState IDLE")
                        VoiceConflictTypeManager.removeConflictType(VoiceConflictType.ChatVoiceType)
                    }
                }
            }
        }

        //首页点击预览播放按钮播放时
        launch {
            routerServices<HomeService>().value?.getHomePlayingPreviewFlow()?.collect { msg ->
                if (msg != null) {
                    //- 如果用户正在播放实时语音消息，此时主动点击回听语音留言时，
                    // 则当前正在播放的实时语音停止播放（后续也不再自动播放），等语音留言播放完成后，顺序播放其他的实时语音
                    if (messageFlow.value.second == PLAYING || messageFlow.value.second == MessageState.PAUSE) {
                        messageFlow.value.first?.let { realTimeMessage ->
                            realTimeMessage.dispatchSuspend(
                                imPushMessage = { imPushMessage ->
                                    remove(imPushMessage.message)
                                }
                            )
                        }
                    }
                    msg.let { iMessage ->
                        remove(iMessage)
                    }
                    stop()
                } else {
                    resume()
                }
            }
        }

        launch {
            WTLeaveMsgPlayerManager.itemRemoveFlow.collect {
                it.forEach { iMessage ->
                    logInfo(
                        TAG, "itemRemoveFlow: msgId=${iMessage.msgId}," +
                                " serMsgId=${iMessage.serMsgId}," +
                                " remove size=${it.size},msgList size=${msgList.size}, currPlayItem=${messageFlow.value.first}"
                    )
                    remove(iMessage)
                    if (isTargetPlaying(iMessage.msgId)) {
                        stop()
                    }
                }
            }
        }

        launch {
            routerServices<HomeService>().value?.getIsPlayingPreviewVoiceMsg()?.collect {
                if (it) {
                    if (messageFlow.value.second == PLAYING || messageFlow.value.second == MessageState.PAUSE) {
                        messageFlow.value.first?.let { realTimeMessage ->
                            realTimeMessage.dispatchSuspend(
                                imPushMessage = { imPushMessage ->
                                    remove(imPushMessage.message)
                                }
                            )
                        }
                    }
                    stop()
                } else {
                    resume()
                }
            }
        }

        launch {
            //正在播放语音消息时，接听到通话呼叫时场景处理
            ChannelPendStatusManager.isPendingFlow.collect {
                logInfo(TAG, "observe VoiceCallPendStatusManager isPendingFlow: $it")
                if (it.first && it.second == CallPendStatus.BEING_INVITED) {
                    if (messageFlow.value.second == PLAYING) {
                        stop()
                        messageFlow.value.first?.let { realTimeMessage ->
                            realTimeMessage.dispatchSuspend(imPushMessage = { imPushMessage ->
                                remove(imPushMessage.message)
                            })
                        }
                    }
                } else {
                    if (it.second == CallPendStatus.ANSWER) {
                        //清空播放列表里的所有数据，消息转为未读
                        clearAll()
                    } else {
                        nextAsync("VoiceCallPendStatusManager Pend_end")
                    }
                }
            }
        }
        //监听OnAir房间的开启/加入
        //开启和加入都会回调onNewlyOnAirRoom因此可以结合判断

        launch {
            routerServices<IGlobalOnAirController>().value?.getOnAirRoomFlow()?.collect { newRoom ->
                if (newRoom != null) {
                    clearAll()
                }
            }
        }

        launch {
            //正在播放实时语音消息时，发起voice call呼叫时场景处理
            ChannelPendStatusManager.isCallWaitingFlow.collect {
                logInfo(TAG, "observe VoiceCallPendStatusManager isCallWaitingFlow: $it")
                if (it.first) {
                    if (messageFlow.value.second == PLAYING) {
                        stop()
                        messageFlow.value.first?.let { realTimeMessage ->
                            realTimeMessage.dispatchSuspend(imPushMessage = { imPushMessage ->
                                remove(imPushMessage.message)
                            })
                        }
                    }
                } else {
                    if (it.second) {
                        //清空播放列表里的所有数据，消息转为未读
                        clearAll()
                    } else {
                        nextAsync("VoiceCallPendStatusManager call_waiting_end")
                    }
                }
            }
        }
    }

    /**
     * Check and add the messages received offline,
     * and take the voice messages in the last 2 minutes for automatic playback after starting the APP
     * [检查和添加离线时收到的消息，在启动APP后取最近2分钟内的语音消息进行自动播放]
     * 改成监听WTStatusManager.isOn值来处理即可
     */
    private fun checkAndAddValidOfflineMsgPush() {
        launchIO {
            val validCacheMsgList = offlineVoiceMessageCacheManager.getValidCacheMsgList()
            logInfo(
                TAG,
                "checkAndAddValidOfflineMsgPush validCacheMsgList size is ${validCacheMsgList.size}"
            )
            if (validCacheMsgList.isNotEmpty()) {
                offlineVoiceMessageCacheManager.clear()
                mutex.withReentrantLock {
                    validCacheMsgList.forEach {
                        if (!filterMessage(BuzNotifyType.NewMsg, it)) {
                            insert(BuzNotifyType.NewMsg, it)
                        }
                    }
                }
                delay(2000)
                emitIfIdle()
            }
        }
    }
    private  val offlineVoiceMessageCacheManager = OfflineVoiceMessageCacheManager()
    private fun observeMessagePush() {
        launch {
            IMAgent.msgReceivedFlow.collect { (type, msgList) ->
                logInfo(TAG, "observeMessagePush :type ${type} size ${msgList.size} ${msgList}")

                offlineVoiceMessageCacheManager.cacheMessage(msgList, type)

                // 用于上报音转文的结果
                if (type.isAsrEditMsg) {
                    msgList.forEach { msg ->
                        AsrTracker.onAsrResult(
                            isPrivate = !msg.isGroup,
                            targetId = msg.targetId.toLongOrDefault(0L),
                            isAuto = true,
                            traceId = msg.msgTraceId ?: "",
                            AsrTracker.AsrResultType.SUCCESS
                        )
                    }
                }

                if (type == BuzNotifyType.NewMsg || type == BuzNotifyType.RoamMsg) {
                    IMTracker.trackNewMessageReceived(msgList)
                }

                if (type == BuzNotifyType.PrepareRecallMsg) {
                    return@collect
                }

                if (type == BuzNotifyType.RecallMsg) {
                    removeMessageInQueueFromRecall(msgList)
                    return@collect
                }

                logInfo(
                    TAG,
                    "observeMessagePush isOn:${WTStatusManager.isOn}  type:${type.name}"
                )
                if (!WTStatusManager.isOn || needFilter(type)) {
                    return@collect
                }

                val isInVoiceCall =
                    routerServices<RealTimeCallService>().value?.isOnRealTimeCall() == true
                val isInOnAir = routerServices<IGlobalOnAirController>().value?.isInOnAir() == true
                if (isInVoiceCall || isInOnAir) {
                    val isCallWaitingState = ChannelPendStatusManager.isCallWaitingState()
                    val isCalledPendState = ChannelPendStatusManager.isCalledPendState()
                    logInfo(
                        TAG,
                        "observeMessagePush: isOnRealTimeCall = true, isCallWaitingState = $isCallWaitingState, isCalledPendState = $isCalledPendState"
                    )
                    //发起呼叫等待/收到呼叫邀请pend状态/通话中下，接收实时消息插入到播放队列，但不执行next检查，插入到队列的消息是否恢复自动播放，依赖是否进入通话中状态
                    launchIO {
                        mutex.withReentrantLock {
                            insert(type, msgList)
                        }
                    }
                    return@collect
                }


                //业务需求请参阅CallNotificationOnAirManager注释
                if (CallNotificationOnAirManager.showJoinOnairDlgFLow.value == OnAirInvitingDlgStatus.SHOW) {
                    launchIO {
                        mutex.withReentrantLock {
                            insert(type, msgList)
                        }
                    }
                    return@collect
                }

                launchIO {
                    mutex.withReentrantLock {
                        insert(type, msgList)
                        emitIfIdle()
                    }
                }
            }
        }
    }

    fun isOfflineCacheEmpty(): Boolean {
        return offlineVoiceMessageCacheManager.getValidCacheMsgList().isEmpty()
    }

    private fun needFilter(type: BuzNotifyType): Boolean {
        return type != BuzNotifyType.NewMsg && type != BuzNotifyType.AsrEditMSG && type != BuzNotifyType.RoamMsg
    }

    private fun observeMessageSend() {
        launch {
            IMAgent.msgSendStateFlow.collect { sendStateEvent ->
                when (sendStateEvent) {
                    is IMSendStateEvent.OnAttach -> {
                        logInfo(TAG, "observeMessageSend :type ${sendStateEvent.msg.msgType}")
                        val isOnRealTimeCall =
                            routerServices<RealTimeCallService>().value?.isOnRealTimeCall() == true
                        val isInOnAir =
                            routerServices<IGlobalOnAirController>().value?.isInOnAir() == true
                        if (sendStateEvent.msg.isVoiceMojiMessage
                            && WTQuietModeManager.isQuietModeEnable.not()
                            && isOnRealTimeCall.not()
                            && isInOnAir.not()
                        ) {
                            launchIO {
                                mutex.withReentrantLock {
                                    insertSendVEMsg(sendStateEvent.msg)
                                    emitIfIdle()
                                }
                            }
                        }
                        if (sendStateEvent.msg.isVoiceGifMessage) {
                            newestVoiceGifMsgId = sendStateEvent.msg.msgId
                        }
                    }
                    is IMSendStateEvent.OnSuccess -> {
                        val conversationType = sendStateEvent.msg.conversationType
                        val targetId = sendStateEvent.msg.targetId
                        checkUserStatusAfterSendMsg(targetId, conversationType)
                        logInfo(TAG, "observeMessageSend onSuccess :type ${sendStateEvent.msg.msgType}")
                        val isOnRealTimeCall =
                            routerServices<RealTimeCallService>().value?.isOnRealTimeCall() == true
                        val isInOnAir =
                            routerServices<IGlobalOnAirController>().value?.isInOnAir() == true
                        if (sendStateEvent.msg.isVoiceGifMessage
                            && WTQuietModeManager.isQuietModeEnable.not()
                            && isOnRealTimeCall.not()
                            && isInOnAir.not()
                        ) {
                            launchIO {
                                mutex.withReentrantLock {
                                    insertSendVGMsg(sendStateEvent.msg)
                                    emitIfIdle()
                                }
                            }
                        }
                        VoiceGifAgent.updateVoiceGifRemoteUrl(sendStateEvent.msg)
                    }
                    else -> {}
                }

            }
        }
    }

    private fun observeWalkieTalkieState() {
        launch {
            WTStatusManager.switchFlow.collect {
                if (it.not()) {
                    clearAll()
                } else {
                    // 通过监听这里的值，使得全局一致，不用在去通过界面展示来控制
                    logInfo(TAG, "observeWalkieTalkieState is on")
                    checkAndAddValidOfflineMsgPush()
                }
            }
        }
        launch(Dispatchers.Main) {
            RecordStatusHelper.isRecordingFlow.collect {
                logInfo(TAG, "当前录音状态更新 isSpeaking result is $it")
                if (it) {
                    pause()
                    WTVoiceEmojiManager.stopEffect()
                    OnlineChatRingtoneManager.stopIncomingSoundEffect()
                } else {
                    resume()
                }
            }
        }
        launch {
            LeaveMsgStatusManager.isLeaveMsgSpeakingFlow.collect {
                if (it) {
                    pause()
                } else {
                    resume()
                }
            }
        }
    }

    private fun observeCameraState() {
        launch {
            VideoRecordStateManager.isRecording_.collect {
                if (it) {
                    pause()
                } else {
                    resume()
                }
            }
        }
    }

    private fun observeMessageFlow() {
        launch(Dispatchers.Main) {
            messageFlow.collect { (msg, state) ->
                if (msg != null && state == PLAYING) {
                    currentSendPlayingMsg = msg
                }
                if (msg != null && state == PLAYING && !msg.isDirectionSend()) {
                    //notice wt unRead count update
                    currentPlayingMsg = msg
                    if (AppStateWatcher.isForeground == false) {
                        val targetId = msg.getConversationId()?.toLong()
                        logInfo(
                            TAG,
                            "App is Background, GlobalEventManager.homesRouterTarget post targetId: $targetId"
                        )
                        GlobalEventManager.homesRouterTarget.postIfDifferent(targetId to null)
                    }
                    logWTMsgPlay(msg)
                }

                logInfo(TAG, "messageFlow changed,msg:${msg},state:$state")
                when (state) {
                    PLAYING -> {
                        VoiceConflictTypeManager.addConflictType(VoiceConflictType.HomeVoiceType)
                        msg?.dispatch(
                            imPushMessage = {
                                logInfo(
                                    TAG,
                                    "start WTAudioPlayer playing im-message msgId = ${it.message.msgId} and ConversationId = ${it.message.getConversationId()}"
                                )

                                audioFocusManager.enableSpeakerphoneBasedOnHeadset()
                                val content = it.message.content
                                WTVoiceEmojiManager.stopEffect()
                                ChatQuickReactPlayManager.stop()

                                WTAudioPlayer.resumeOrPlay()
                                if (content is WTVoiceEmojiMsg) {
                                    handleVoiceMojiMsgCutLogic(it, content)
                                }

                                logInfo(
                                    TAG,
                                    "messageFlow.collect  invoke WTAudioPlayer.resumeOrPlay() on MessageState.PLAYING status"
                                )
                                if (!it.isDirectionSend()) {
                                    it.message.apply {
                                        launchIO {
                                            makeVoiceMsgListened()
                                        }
                                        LastReadMsgManager.get().onMsgPlayed(it.message)
                                        NotificationUtil.cancelNotification(
                                            appContext,
                                            serMsgId?.toLongOrNull()?.toInt() ?: 0
                                        )
                                    }
                                }
                                cancelClearAllDelayJob()
                            }
                        )
                    }

                    MessageState.PAUSE -> {
                        currentPlayingMsg = null
                        currentSendPlayingMsg = null
                        WTVoiceEmojiManager.stopEffect()
                        ChatQuickReactPlayManager.stop()
                        WTAudioPlayer.pauseAudio()
                        msg?.dispatch(
                            imPushMessage = {
                            }
                        )
                    }

                    MessageState.IDLE -> {
                        VoiceConflictTypeManager.removeConflictType(VoiceConflictType.HomeVoiceType)
                        currentPlayingMsg = null
                        currentSendPlayingMsg = null
                        //It is possible that the stream ends and enters the idle state. In this case, there is no need to call tekiPlayer.stop().
                        //[有可能是拉流结束进入idle状态，这种case不需要调用tekiPlayer.stop()]
                        if (WTAudioPlayer.isPlaying) {
                            WTAudioPlayer.stop()
                        }

                        audioFocusManager.abandon()
                    }

                    else -> {}
                }
            }
        }
    }

    private fun handleVoiceMojiMsgCutLogic(imPushMessage: IMPushMessage, content: WTVoiceEmojiMsg) {
        if (msgList.size == 1) {
            playNextScope.launch {
                delay(playCutTime)
                if (needCutMsg(imPushMessage)) {
                    if (WTAudioPlayer.playerCurrItem?.tag == getMediaItemTag(imPushMessage.message) && WTAudioPlayer.isPlaying) {
                        WTVoiceEmojiManager.stopEffect()
                        playNext("WTVoiceEmojiManager need to cut current VoiceMoji", imPushMessage)
                    }
                }
            }
            playVEAni(imPushMessage, content)
        } else {
            if (needCutMsg(imPushMessage)) {
                playNextScope.launch {
                    delay(playCutTime)
                    if (WTAudioPlayer.playerCurrItem?.tag == getMediaItemTag(imPushMessage.message) && WTAudioPlayer.isPlaying) {
                        WTVoiceEmojiManager.stopEffect()
                        playNext("WTVoiceEmojiManager need to cut current VoiceMoji", imPushMessage)
                    }
                }
            } else {
                playVEAni(imPushMessage, content)
            }
        }
    }

    private fun checkUserStatusAfterSendMsg(targetId: String, convType: IM5ConversationType) {
        launchIO {
            val userInfo = if (convType == PRIVATE) {
                UserRelationCacheManager.getUserRelationInfoByUidSync(targetId.toLong())
            } else null
            if (userInfo.isNull()) return@launchIO
            if (!userInfo!!.isAccountStatusNormal) {
                toastSolidWarning(R.string.account_suspension_toast)
                ChatTracker.onToastUserBanned(userInfo.userId.toString())
            }
        }
    }


    private fun needCutMsg(imPushMessage: IMPushMessage): Boolean {
        val nextMsg = msgList.getOrNull(1)
        val nextVoiceMojiMsg = nextMsg?.content as? WTVoiceEmojiMsg
        nextVoiceMojiMsg?.let {
            if (nextMsg.getConversationId() == imPushMessage.message.getConversationId()) {
                return true
            }
        }
        return false
    }

    private fun playVEAni(
        message: IMPushMessage,
        content: WTVoiceEmojiMsg
    ) {
        val curTopA = topActivity
        //仅有在首页和历史聊天界面且选中用户id和发送人相同才能播放动画
        val curPlayFlag =
            curTopA is IVoiceEmojiSupport && curTopA.obtainTargetID()
                .toString() == IM5MsgUtils.getConvTargetId(message.message) && curTopA.attachCondition()
        if (isAppInForeground) {
            if (curPlayFlag) {
                ChatTracker.onVoiceEmojiPlayResult("1")
            } else {
                ChatTracker.onVoiceEmojiPlayResult("2")
            }
        } else {
            ChatTracker.onVoiceEmojiPlayResult("3")
        }
        if (curPlayFlag) {
            val playVoiceEmojiWrapper = PlayVoiceEmojiWrapper(
                content.convert(),
                type = PlayType.GLOBAL_PLAY
            )
            WTVoiceEmojiManager.startPlayAniEffect(effect = playVoiceEmojiWrapper,
                object :
                    AbsVoicePlayListener() {
                    override fun onTouch() {
                        WTVoiceEmojiManager.stopEffect()
                        Logz.tag("WTMessageManager")
                            .e("WTVoiceEmojiManager ontouch")
                        playNext("WTVoiceEmojiManager ontouch", message)
                    }
                })
        }
    }

    private fun logWTMsgPlay(msg: RealTimeMessage) {
        GlobalScope.launch {
            val targetId = msg.getConversationId()?.toLongOrNull() ?: 0L
            val floatViewStateStr = floatService?.getOverlayFloatWindowStateLogString()
            val source = if (floatViewStateStr == null) "" else "overlay"
            val contentName = floatViewStateStr ?: ""
            val isRobot =
                UserRelationCacheManager.getUserRelationInfoFromCache(targetId)?.isRobot ?: false
            msg.dispatch(imPushMessage = {
                WTTracker.onWTMsgPlay(
                    msg.getConversationId() ?: "0",
                    it.message.conversationType,
                    it.message.hasVoiceFilter,
                    source,
                    contentName,
                    isRobot,
                    traceId = it.message.msgTraceId,
                    duration = it.message.getVoiceDuration(),
                    filterID = it.message.getVoiceFilterId ?: 0L
                )
            })
        }
    }

    private fun observePlayerState() {
        WTAudioPlayer.playerCallback = object : PlayEventListener {
            override fun onBufferedPositionUpdate(
                index: Int,
                item: MediaItem?,
                bufferPosition: Long
            ) {

            }

            override fun onError(errCode: Int, message: String) {
                logInfo(TAG, "PlayEventListener onError errcode $errCode message $message")
                nextAsync("playerCallback error")
            }

            override fun onPlayListFinished() {
//                next()
            }

            override fun onPlayListUpdate() {
                //https://project.feishu.cn/businesscenter/issue/detail/16572135#detail
                //处理additem后，由于tekiplayer内部是异步添加，所以可能会导致添加后，但是播放停止了，
                //这个时候才真正添加到Tekiplayer播放队列，触发了onPlayListUpdate，这时WTMessageManager.isPlaying是true，
                //但是播放器里面已经停止了，所以需要再播一次
                launch {
                    mutex.withLock {
                        if (isIMPlaying && WTAudioPlayer.isPlaying.not() && WTAudioPlayer.getSize() > 0) {
                            logInfo(TAG, "resume from onPlayListUpdate")
                            WTAudioPlayer.resumeOrPlay()
                        }
                    }
                }
            }

            override fun onPlayZeroItem(item: MediaItem?) {
                //上一层回调已经处理埋点上报
                logInfo(TAG, "PlayEventListener onPlayZeroItem ${item}")
            }

            override fun onPlaybackChange(
                index: Int,
                item: MediaItem?,
                lastPosition: Long,
                reason: Int
            ) {
                messageFlow.value.first?.dispatch(imPushMessage = {
                    val content = it.message.content as? IM5VoiceMessage
                    val currentUrl = item?.uri?.toString()
                    if (it.message.isVoiceMojiMessage.not()) {
                        BuzMediaRecordManager.insertOrUpdateMediaCacheRecord(
                            userId = UserSessionManager.uid,
                            mediaUrl = currentUrl.getStringDefault(),
                            mediaType = MediaCacheType.Audio,
                            msg = it.message
                        )
                    }
                    logInfo(
                        TAG,
                        "onPlaybackChange currentUrl:$currentUrl,content?.url:${content?.url}"
                    )
                })
            }

            override fun onPlaybackRemoveOnList() {
            }

            override fun onPlaybackStateChange(status: Int) {
                if ((status == PlayerState.STATE_ENDED || status == PlayerState.STATE_PAUSED)
                    && needAutoPause().not() && WTQuietModeManager.isQuietModeEnable.not()
                ) {
                    val isVEMsg =
                        (messageFlow.value.first?.msg as? IMessage)?.isVoiceMojiMessage == true
                    if (!isVEMsg) {
                        if (CommonMMKV.settingAPMSoundsOpen) {
                            RingtonePlayer.getInstance()
                                .play(RingtonePlayer.TYPE_CHAT_PLAY_WT_MESSAGE_END)
                        }
                    }
                }
                if (status == PlayerState.STATE_ENDED || status == PlayerState.STATE_PAUSED) {
                    //有一种case是已添加到tekiPlayer的mediaItem进入到STATE_READY状态，如果这时候又从tekiPlayer中removeItem，会触发PlayerState.STATE_ENDED回调,
                    //如果在RTP拉流播放的过程中触发了上述的STATE_ENDED回调，会异常触发next检查，
                    //例如next检查进入IDLE状态（实际还在拉流中）会出现首页正在拉流播放用户头像动效消失
                    //fixme 判断如果这个时候是正在拉流中，则不进行next检查
                    val realTimeMessage = messageFlow.value.first
                    if (realTimeMessage is IMPushMessage) {
                        nextAsync("onPlaybackStateChange STATE_ENDED")
                    } else {
                        logWarn(
                            TAG,
                            "onPlaybackStateChange is STATE_ENDED or STATE_PAUSED, " +
                                    "but current messageFlow realTimeMessage is ${realTimeMessage}, " +
                                    "don't need to invoking next() check"
                        )
                    }
                }

                // 当一条Voice消息播放的时候我们就需要将这个消息的开始播放的通知发送出去，VoiceGif根据这个状态将Gif动画重置到开始那一刻。
                if (status == PlayerState.STATE_PLAYING || status == PlayerState.STATE_ENDED) {
                    val serMsgId =
                        WTAudioPlayer.playerCurrItem?.extraData?.getString(ZeroDataAudioHelper.KEY_SER_MSG_ID)
                    userLifecycleScope?.launch {
                        serMsgId?.let {
                            logDebug("TestMessagePlayStateFlow","messageStartFlow emit $status, $serMsgId")
                            messagePlayStateFlow.emit(
                                VoiceGifState(
                                    serMsgId,
                                    if (status == PlayerState.STATE_PLAYING)
                                        VoiceGifPlayState.START
                                    else VoiceGifPlayState.END
                                )
                            )
                        }
                    }
                }
                if (status == PlayerState.STATE_PLAYING) {
                    launchIO {
                        mutex.withReentrantLock {
                            emitIfDifferentVoicePlay(currentSendPlayingMsg to VoicePlayState.PLAYING)
                        }
                    }
                }
                if (status == PlayerState.STATE_BUFFERING) {
                    launchIO {
                        mutex.withReentrantLock {
                            emitIfDifferentVoicePlay(currentSendPlayingMsg to VoicePlayState.BUFFERING)
                        }
                    }
                }
            }

            override fun onPlayedPositionUpdate(index: Int, item: MediaItem?, position: Long) {
            }
        }
    }

    private fun observerVoiceMojiPreviewState() {
        VoiceMojiManager.setPreviewStateCallback(object : IVoiceEmojiStateCallback {
                override fun onFinish() {
                    nextAsync("Preview Finish")
                }
            })
    }

    private fun observerDriveModelManager() {
        launch {
            DriveModelManager.headsetUpdateFlow.collect { headsetData ->
                if (headsetData != null) {
                    EarphonesNotificationManager.cancelNotification()
                } else { // 移除耳机
                    // 在这里调用，而不再unplugEarphonesWhenPlayingFlow调用，是因为后者需要再有音频播放（包括系统在播放音乐）的时候拔掉耳机才能响应
                    VibratorUtil.vibrator()
                }
            }
        }

        launch(Dispatchers.Main) {
            /**
             *  这里用这个方法监听是否有耳机移除，是因为播放器WTAudioPlayer(TekiPlayer)监听播放状态用的是同样的方式，
             *  即unplugEarphonesWhenPlayingFlow（ACTION_AUDIO_BECOMING_NOISY），
             *  其调用时机比headsetFlow（AudioDeviceCallback）要早，如果用headsetFlow（AudioDeviceCallback）拔掉耳机时，来不及清除播放状态
             */
            DriveModelManager.unplugEarphonesWhenPlayingFlow.collect { hasUnplug ->
                if (hasUnplug) {
                    if (isPlaying) { // 这里添加判断有播放auto-play 消息，排除系统播放音乐时拔掉耳机的情况
                        clearAll()
                        EarphonesNotificationManager.showNotification()
                        DNDTracker.onResultRB2024081502()
                    }
                }
            }
        }
    }

    private fun needAutoPause(): Boolean {
        return (RecordStatusHelper.isRecording
                || WTLeaveMsgPlayerManager.playStateFlow.value.first != LMPlayerState.IDLE
                || LeaveMsgStatusManager.isLeaveMsgSpeakingFlow.value
                || VoiceMojiManager.isPlayingPreview()
                || routerServices<RealTimeCallService>().value?.isOnRealTimeCall() == true
                || routerServices<IGlobalOnAirController>().value?.isInOnAir() == true
                || VideoRecordStateManager.isRecording
                || VideoRecordStateManager.isPreviewing
                || ChannelPendStatusManager.isCalledPendState())
                //如果这个不为空那么证明用户进入了OnAir业务房，不应该播放音效
                || routerServices<IGlobalOnAirController>().value?.isInOnAir() == true
                || routerServices<HomeService>().value?.getHomePlayingPreviewFlow()?.value != null
                || routerServices<HomeService>().value?.getIsPlayingPreviewVoiceMsg()?.value == true
    }

    private fun needStop(): Boolean {
        if (WTStatusManager.isOn.not()) {
            return true
        }
        return false
    }

    fun playNext(from: String, expectMsg: IMPushMessage? = null) {
        playNextMessage(from, expectMsg?.message?.toMsgIdentity())
    }

    fun playNextMessage(
        from: String,
        msgIdentity: IMMsgIdentity? = null
    ) {
        launchWithLock {
            logInfo(TAG, "playNext(),from:$from")
            val playingState = messageFlow.value.second
            val curPlayValue = messageFlow.value.first as? IMPushMessage
            val curPlayingMsg = curPlayValue?.message ?: return@launchWithLock
            if (msgIdentity != null && msgIdentity != curPlayingMsg.toMsgIdentity()) {
                return@launchWithLock
            }
            if (playingState == PLAYING) {
                WTAudioPlayer.remove(getMediaItemTag(curPlayingMsg))
            } else {
                resume()
            }
        }
    }

    private fun nextAsync(from: String) {
        launchIO {
            nextSync(from)
        }
    }

    private suspend fun nextSync(from: String) {
        mutex.withReentrantLock {
            logInfo(TAG, "next(),from:${from}")
            if (nextChecking) {
                logInfo(TAG, "nextChecking and return")
                return@withReentrantLock
            }
            if (needStop()) {
                logInfo(TAG, "needStop clearAll")
                clearAll()
                return@withReentrantLock
            }
            if (needAutoPause()) {
                logInfo(
                    TAG,
                    "next return.isRecording:${RecordStatusHelper.isRecording}" +
                            ",leaveMsgState:${WTLeaveMsgPlayerManager.playStateFlow.value}"
                )
                pause()
                return@withReentrantLock
            }
            try {//先检查播放队列中Next元素是否有内容
                nextChecking = true
                var nextRealTimeMessage: RealTimeMessage? = null
                val next = getNext()
                if (next == null) {
                    logInfo(TAG, "next() getNext为空")
                } else {
                    logInfo(TAG, "next() next=${next}")
                    nextRealTimeMessage = IMPushMessage(next)
                }

                if (nextRealTimeMessage != null) {
                    //请求音频焦点成功 触发resume 又触发了一次next(), 导致出问题
                    if (audioFocusManager.request().not()) {
                        logInfo(TAG, "focus request failed")
                        clearAll()
                        return@withReentrantLock
                    }
                    if (WTQuietModeManager.isQuietModeEnable) {
                        logInfo(TAG, "next() return, quiet mode enable, clear all msg")
                        clearAll()
                        return@withReentrantLock
                    }
                }

                logInfo(
                    TAG,
                    "current: RealTimeMessage = ${messageFlow.value.first}, moveTo() nextRealTimeMessage = $nextRealTimeMessage , from: ${from}"
                )
                messageFlow.value.first?.let {
                    it.dispatchSuspend(imPushMessage = { imMessage ->
                        removeWithoutLock(imMessage.message)
                    })
                }
                val state = if (nextRealTimeMessage == null) MessageState.IDLE else PLAYING
                val voicePlayState = if (nextRealTimeMessage == null) VoicePlayState.IDLE else VoicePlayState.BUFFERING
                if (nextRealTimeMessage.isDirectionSend()) {
                    emitIfDifferent(nextRealTimeMessage to state)
                    emitIfDifferentVoicePlay(nextRealTimeMessage to voicePlayState)
                    return@withReentrantLock
                }
                nextRealTimeMessage?.let { realTimeMessage ->
                    if (realTimeMessage.isMessageMuted(TAG)) {
                        realTimeMessage.dispatchSuspend(imPushMessage = { imPushMsg ->
                            removeWithoutLock(imPushMsg.message)
                        })
                        emitIfDifferent(null to MessageState.IDLE)
                        emitIfDifferentVoicePlay(null to VoicePlayState.IDLE)
                        emitIfIdle()
                        return@withReentrantLock
                    }
                }
                val nextRealTimeMessageBackup = nextRealTimeMessage

                if (state == PLAYING) {
                    val voiceEmojiMsg =
                        nextRealTimeMessageBackup is IMPushMessage && nextRealTimeMessageBackup.message.isVoiceMojiMessage
                    if (CommonMMKV.settingAPMSoundsOpen && !voiceEmojiMsg) {
                        RingtonePlayer.getInstance()
                            .play(RingtonePlayer.TYPE_CHAT_PLAY_WT_MESSAGE_START)
                    }

                    val currentSelectConversation =
                        WTStatusManager.wtTargetId.toString()

                    //voiceEmojiMsg 在对准当前对话时由这里震动，不对准时由popup控制
                    if (CommonMMKV.vibration && isAppInForeground) {
                        if (!voiceEmojiMsg || currentSelectConversation == nextRealTimeMessageBackup?.getConversationId()) {
                            VibratorUtil.vibrateOnReceiveMsg(appContext)
                        }
                    }
                }
                emitIfDifferent(nextRealTimeMessage to state)
                emitIfDifferentVoicePlay(nextRealTimeMessage to voicePlayState)
            } finally {
                nextChecking = false
            }
        }
    }

    private fun stop() {
        launch {
            mutex.withReentrantLock {
                logInfo(TAG, "stop()")
                if (messageFlow.value.second != MessageState.IDLE) {
                    emitIfDifferent(null to MessageState.IDLE)
                }
                if (voicePlayStateFlow.value.second != VoicePlayState.IDLE) {
                    emitIfDifferentVoicePlay(null to VoicePlayState.IDLE)
                }
            }
        }
    }

    fun pause() {
        logInfo(TAG, "pause()")
        launch {
            mutex.withReentrantLock {
                if (messageFlow.value.second == PLAYING) {
                    emitIfDifferent(messageFlow.value.first to MessageState.PAUSE)
                }
                if (voicePlayStateFlow.value.second == VoicePlayState.PLAYING) {
                    emitIfDifferentVoicePlay(voicePlayStateFlow.value.first to VoicePlayState.PAUSE)
                }
            }
        }
    }

    private suspend fun emitIfDifferent(newData: Pair<RealTimeMessage?, MessageState>) {
        mutex.withReentrantLock {
            logInfo(
                TAG,
                "emitIfDifferent newData=${newData},,,old messageFlow=${messageFlow.value}"
            )
            val (newMsg, newState) = newData
            val (oldMsg, oldState) = messageFlow.value
            if (newMsg == oldMsg && newState == oldState) {
                return@withReentrantLock
            }
            messageFlow.emit(newData)
        }
    }

    private suspend fun emitIfDifferentVoicePlay(newData: Pair<RealTimeMessage?, VoicePlayState>) {
        mutex.withReentrantLock {
            logInfo(
                TAG,
                "emitIfDifferentVoicePlay newData=${newData},,,old messageFlow=${voicePlayStateFlow.value}"
            )
            val (newMsg, newState) = newData
            val (oldMsg, oldState) = voicePlayStateFlow.value
            if (newMsg == oldMsg && newState == oldState) {
                return@withReentrantLock
            }
            voicePlayStateFlow.emit(newData)
        }
    }

    private fun resume() {
        launchWithLock {
            val current = messageFlow.value.first
            logInfo(TAG, "resume(), current = $current")
            if (current == null) {
                nextSync("resume")
            } else {
                emitIfDifferent(current to PLAYING)
                val current = voicePlayStateFlow.value.first
                if (current != null) {
                    emitIfDifferentVoicePlay(current to VoicePlayState.PLAYING)
                }
            }
        }
    }

    private suspend fun insert(type: BuzNotifyType, msgList: List<IMessage>) {
        logInfo(TAG, "insert msgListSize:${msgList.size}")

        for (item in msgList) {
            if (item.isDecryptFail) {
                logInfo(TAG, "insert message decryptFail:${item.msgId}")
                continue
            }

            if (filterMessage(type, item)) {
                continue
            }

            insert(type, item)
        }
    }

    private fun filterMessage(type: BuzNotifyType, message: IMessage): Boolean {

        if (message.isNeedAutoPlay().not()) {
            logInfo(TAG, "insert filter,is not needAutoPlay")
            return true
        }

        if (type == BuzNotifyType.RoamMsg) {
            logInfo(TAG, "insert filter, is roma msg(${message.msgId})")
            return true
        }
        if (message.isGroupOfflineMessage()) {
            logInfo(TAG, "insert filter, group:gap > 5min")
            return true
        }
        return false
    }

    private fun emitIfIdle() {
        launchWithLock {
            if (messageFlow.value.first == null) {
                nextSync("emitIfIdle")
            }
        }
    }

    @androidx.annotation.OptIn(UnstableApi::class)
    private suspend fun insert(type: BuzNotifyType, message: IMessage) {
        if (message.messageDirection == MsgDirection.SEND) {
            return
        }
        val content = message.content
        if (content is IM5VoiceMessage) {
            if (WTQuietModeManager.isQuietModeEnable ||
                MuteInfoManager.isMessageMuted(message, TAG) ||
                UpdateVersionManager.hasShowUpdateVersion() // 有更新窗口时，不显示 dropdown
            ) {
                if (WTQuietModeManager.isQuietModeEnable) {
                    quietModeReceiveMsg.emit(Pair(type, message))
                    if (isVolumeMuted && hadShownVolumeMutedToast.not()) {
                        toastIconFontMsg(
                            message = R.string.chat_low_audio_volume.asString(),
                            textColor = R.color.text_white_default.asColor(),
                            iconFont = R.string.ic_sound_close.asString(),
                            iconFontColor = R.color.text_white_important.asColor(),
                            style = IconToastStyle.ICON_TOP_TEXT_BOTTOM
                        )
                        hadShownVolumeMutedToast = true
                    }
                    if (!MuteInfoManager.isMessageMuted(
                            message,
                            TAG
                        ) && CommonMMKV.vibration && isAppInForeground && !type.isAsrEditMsg
                    ) {
                        VibratorUtil.vibrateOnReceiveMsg(appContext)
                    }
                } else if (MuteInfoManager.isMessageMuted(message, TAG)) {
                    muteMessageReceiveMsg.emit(Pair(type, message))
                }
                return
            }

            //voice call通话中，收到语音消息不自动播放，但需要展示popup弹窗,效果同设置了quiet模式
            if ((routerServices<RealTimeCallService>().value?.isOnRealTimeCall() == true
                        || routerServices<IGlobalOnAirController>().value?.isInOnAir() == true)
                && ChannelPendStatusManager.isCallWaitingState().not()
            ) {
                logInfo(
                    TAG,
                    "current is in voice call state, new voice message popup display, but does not automatically play"
                )
                quietModeReceiveMsg.emit(Pair(type, message))
                return
            }
            //OnAir通话中，收到语音消息不自动播放，但需要展示popup弹窗,效果同设置了quiet模式
            if (routerServices<IGlobalOnAirController>().value?.isInOnAir() == true) {
                logInfo(
                    TAG,
                    "current is in voice call state, new voice message popup display, but does not automatically play"
                )
                quietModeReceiveMsg.emit(Pair(type, message))
                return
            }

            //拍摄视屏中或从相机或相册预览媒体时，收到语音消息不自动播放，但需要展示popup弹窗
            if (VideoRecordStateManager.isRecording || VideoRecordStateManager.isPreviewing) {
                logInfo(
                    TAG,
                    "currently taking video, new voice message popup display, but does not automatically play"
                )
                takingVideoModeReceiveMsg.emit(message)
                return
            }

            var highPriority = false
            val audioUrl = content.url
            if (audioUrl.isEmpty()) {
                logInfo(TAG, "audioUrl is empty, not need insert to playlist")
                if (content is BuzVoiceGifMsg) {
                    receiveNoVoiceMsg.emit(Pair(type, message))
                }
                return
            }
            val audioUri = message.getAudioUri()?.first
            if (audioUri == null) {
                logInfo(TAG, "audioUri is empty, not need insert to playlist")
                return
            }
            if (content is WTVoiceEmojiMsg) {
                //预下载
                val url = content.getAnimUrl()
                AnimContainerView.preLoad(url)
                highPriority = true
            }
            if (content is BuzVoiceGifMsg) {
                //预下载
                val url = content.animationUrl
                VoiceGifVideoCache.preloadCache(url)
                highPriority = true
            }
            logInfo(TAG, "insert:${message.msgId}, msgType = ${message.msgType}")

            var needCut = false
            if (msgList.size == 1) {
                val currentMsg = msgList.getOrNull(0)
                if (message.isVoiceMojiMessage && currentMsg != null && currentMsg.isVoiceMojiMessage) {
                    if (currentMsg.getConversationId() == message.getConversationId()) {
                        needCut = WTAudioPlayer.getCurrentPosition() > playCutTime
                        logDebug(
                            TAG,
                            "AA currentPosition = ${WTAudioPlayer.getCurrentPosition()}, needCut: $needCut"
                        )
                    }
                }
            }

            logDebug(
                TAG,
                "currentPosition = ${WTAudioPlayer.getCurrentPosition()}, needCut: $needCut"
            )

            if (addMsgToList(type, message)) {
                return
            }

            // add key and iv
            val aesComponent =
                if (content.checkPerferredUrl() == IM5CheckPerferredUrl.Remote && content !is WTVoiceEmojiMsg) message.aesComponent else null

            WTAudioPlayer.add(
                getMediaItemTag(message),
                audioUri,
                content.duration.toLong(),
                message.msgTraceId,
                aesComponent,
                highPriority, serMsgId = message?.serMsgId ?: ""
            )
            if (needCut) {
                (currentPlayingMsg as? IMPushMessage)?.let {
                    MainScope().launch {
                        WTVoiceEmojiManager.stopEffect()
                        playNext("WTVoiceEmojiManager add new VoiceMoji", it)
                    }
                }
            }
        }
    }

    /**
     * @param type 消息通知类型，主要识别是不是ASR编辑消息
     * @param message 消息
     * @return 若为AsrEditMSG则返回true，不需要再处理接来下的播放流程
     * 若非AsrEditMSG则就是NewMsg，应添加到msgList中。
     * 若是AsrEditMSG，并刚好此消息为正在播放的消息，则无需替换，否则从msgList中找出原消息替换。
     */
    private fun addMsgToList(type: BuzNotifyType, message: IMessage): Boolean {
        if (type == BuzNotifyType.AsrEditMSG) {
            if (currentPlayingMsg?.getSerMessageId() != message.serMsgId) {
                val index = msgList.indexOfFirst { it.msgId == message.msgId }
                if (index != -1) {
                    logDebug(TAG, "replace Message $index")
                    try {
                        msgList[index] = message
                    } catch (e: IndexOutOfBoundsException) {
                        logError(TAG, "addMsgToList IndexOutOfBoundsException $e")
                    }
                }
            }
            return true
        } else {
            msgList.add(message)
        }
        return false
    }

    private fun insertSendVGMsg(message: IMessage): Boolean {
        val content = message.content as? BuzVoiceGifMsg ?: return false
        val audioUrl = content.remoteUrl
        if (audioUrl.isNullOrEmpty()) {
            R.string.sound_unavailable.toast()
            logInfo(TAG, "audioUrl is empty, not need insert to playlist")
            return false
        }
        val audioUri = message.getAudioUri()?.first
        if (audioUri == null) {
            logInfo(TAG, "audioUri is empty, not need insert to playlist")
            return false
        }
        if (msgList.isNotEmpty()) {
            val currentMsg = msgList.getOrNull(0)
            if (currentMsg != null && currentMsg.messageDirection == MsgDirection.RECEIVE) {
                logInfo(TAG, "current receive msg is not empty, not need insert to playlist")
                return false
            }
        }
        logInfo(TAG, "compare createTime :${message.msgId} - ${message.createTime}")

        if (message.msgId != newestVoiceGifMsgId) {
            logInfo(TAG, "not newestVoiceGifMsg not need to play")
            return false
        }
        logInfo(TAG, "insertSendVGMsg:${message.msgId}")
        msgList.add(message)
        WTAudioPlayer.add(
            getMediaItemTag(message),
            audioUri,
            content.duration.toLong(),
            message.msgTraceId,
            aesComponent = null,
            highPriority = true,
            serMsgId = message?.serMsgId ?: ""
        )
        // 假如当前播放的列表是大于两条的，那么如果当前播放的是VoiceGif则会因为有新的消息要播放而被打断。
        playNextScope.launch {
            if (msgList.size > 1) {
                msgList.getOrNull(0)?.let {
                    if (it.isVoiceGifMessage) {
                        playNextMessage("add new VoiceGif", it.toMsgIdentity())
                    }
                }
            }
        }
        return true
    }

    private fun insertSendVEMsg(message: IMessage) {
        val content = message.content as? WTVoiceEmojiMsg ?: return
        val audioUrl = content.url
        if (audioUrl.isEmpty()) {
            logInfo(TAG, "audioUrl is empty, not need insert to playlist")
            return
        }
        val audioUri = message.getAudioUri()?.first
        if (audioUri == null) {
            logInfo(TAG, "audioUri is empty, not need insert to playlist")
            return
        }
        if (msgList.isNotEmpty()) {
            val currentMsg = msgList.getOrNull(0)
            if (currentMsg != null && currentMsg.messageDirection == MsgDirection.RECEIVE) {
                logInfo(TAG, "current receive msg is not empty, not need insert to playlist")
                return
            }
        }
        logInfo(TAG, "insertSendVEMsg:${message.msgId}")
        msgList.add(message)
        WTAudioPlayer.add(
            getMediaItemTag(message),
            audioUri,
            content.duration.toLong(),
            message.msgTraceId,
            aesComponent = null,
            highPriority = true,
            serMsgId = message?.serMsgId ?: ""
        )
    }

    private fun removeWithoutLock(
        message: IMessage,
        removeFilter: ((IMessage) -> Boolean)? = null,
    ) {
        logInfo(
            TAG,
            "invoke removeWithoutLock msgId=${message.msgId} serMsgId=${message.serMsgId}"
        )
        val content = message.content
        if (content is IM5VoiceMessage
            || content is IM5RecallMessage && message.isRecallWTAutoPlayMessage(content.orgType.toInt())
        ) {
            val success = msgList.removeIf {
                removeFilter?.invoke(it) ?: (it.msgId == message.msgId)
            }
            logInfo(
                TAG,
                "remove success=${success}, msgId=${message.msgId} serMsgId=${message.serMsgId}"
            )
            WTAudioPlayer.remove(getMediaItemTag(message))
        }
    }

    private fun remove(message: IMessage) {
        launch {
            mutex.withReentrantLock {
                removeWithoutLock(message)
            }
        }
    }


    fun removeByConvIdAndPlayNext(conversationId: String) {
        logInfo(TAG, "removeByConversationId:conversationId = $conversationId")
        launchWithLock {
            // playNext will skip the message which is being played
            val shouldRemoveMessages =
                msgList.filter { it.getConversationId() == conversationId && it != messageFlow.value.first?.msg }
            shouldRemoveMessages.forEach {
                removeWithoutLock(it)
            }
        }

        playNext("removeByConvIdAndPlayNext")
    }

    private fun getNext(): IMessage? {
        if (messageFlow.value.first != null) {
            messageFlow.value.first.let {
                return if (messageFlow.value.first is IMPushMessage) {
                    logInfo("getNext from 1, current RealTimeMessage is IMPushMessage")
                    get(1)
                } else {
                    logInfo("getNext from 0, current RealTimeMessage is SignalMessage")
                    get(0)
                }
            }
        } else {
            logInfo("getNext from 0")
            return get(0)
        }
    }

    private fun get(index: Int): IMessage? {
        return if (index >= msgList.size) {
            null
        } else {
            msgList.elementAt(index)
        }
    }

    fun getMessageListFirst(): IMessage? {
        return get(0)
    }

    fun getMessageListSize(): Int {
        return msgList.size
    }

    fun muteMessagesByTargetId(targetId: Long) {
        launchWithLock {
            if (currentPlayingMsg?.getConversationId()?.toLongOrNull() == targetId) {
                nextSync("cut current msg playing, because the user/group is muted")
            }
        }
    }

    fun clearAll() {
        launch {
            logInfo(TAG, "clearAll")
            msgList.clear()
            emitIfDifferent(null to MessageState.IDLE)
            emitIfDifferentVoicePlay(null to VoicePlayState.IDLE)
            WTAudioPlayer.clear()
        }
    }

    private fun clearAllDelayIfValid() {
        if (clearAllDelayJob?.isActive == true) {
            return
        }
        clearAllDelayJob = launch {
            delay(MAX_RECORD_DURATION * 1000L)
            if (isActive) {
                clearAll()
                clearAllDelayJob = null
            }
        }
    }

    private fun cancelClearAllDelayJob() {
        clearAllDelayJob?.cancel()
        clearAllDelayJob = null
    }

    private fun handleAudioFocusChange(focus: Int) {
        when (focus) {
            AudioManager.AUDIOFOCUS_LOSS -> {
                if (isPlaying && abs(audioFocusManager.lastRequestTime - System.currentTimeMillis()) < 500) {
                    //在背景播放音乐过程中，app开始录音，录音过程中如果收到对讲机消息，那么在录音结束后，释放焦点
                    //对讲机消息会去请求焦点，请求成功，开始播放，但是系统这个时候才将焦点给了音乐的app，导致我们刚请求完就失效
                    //所以加个500ms的判断，如果低于这个时间，则认为是这种情况，重新请求焦点，如果不能成功说明在通话，则清除队列
                    if (audioFocusManager.request().not()) {
                        clearAll()
                    }
                } else {
                    clearAll()
                }
            }

            AudioManager.AUDIOFOCUS_GAIN -> {
                cancelClearAllDelayJob()
                resume()
            }

            AudioManager.AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK -> {

            }

            AudioManager.AUDIOFOCUS_LOSS_TRANSIENT -> {
                pause()
                clearAllDelayIfValid()
            }
        }
    }

    private fun launch(
        context: CoroutineContext = EmptyCoroutineContext,
        start: CoroutineStart = CoroutineStart.DEFAULT,
        block: suspend CoroutineScope.() -> Unit
    ): Job {
        return GlobalScope.launch(context, start, block = block)
    }

    private fun launchIO(block: suspend CoroutineScope.() -> Unit): Job {
        return GlobalScope.launch(Dispatchers.IO, block = block)
    }

    private fun getMediaItemTag(msg: IMessage): String {
        return "${msg.getConversationId()}-${msg.msgId}"
    }

    private suspend fun <T> reentrantLock(block: suspend () -> T): T {
        return mutex.withReentrantLock(null, block)
    }

    private fun launchWithLock(block: suspend () -> Unit): Job {
        return launchIO {
            reentrantLock(block)
        }
    }

    /**
     * 撤回消息后，从队列移除撤回的消息,如果当前正在播放，立即停止
     */
    private fun removeMessageInQueueFromRecall(recallMsgList: List<IMessage>) {
        launchWithLock {
            recallMsgList.forEach { recallMsg ->
                if (isTargetPlayingBySerMsgId(recallMsg.serMsgId)) {
                    if (!recallMsg.isVoiceMojiMessage) {
                        playNext("removeMessageInQueueFromRecall")
                    }
                } else {
                    removeWithoutLock(
                        message = recallMsg,
                        removeFilter = { it.serMsgId == recallMsg.serMsgId }
                    )
                    offlineVoiceMessageCacheManager.removeMsgFromRecallMsg(recallMsg)
                }
            }
        }

    }
}