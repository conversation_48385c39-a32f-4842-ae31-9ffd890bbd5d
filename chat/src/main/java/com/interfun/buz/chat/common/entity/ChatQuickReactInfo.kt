package com.interfun.buz.chat.common.entity

import androidx.lifecycle.LifecycleOwner
import com.buz.idl.user.bean.UserInfo
import com.interfun.buz.base.ktx.TwoParamCallback
import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.base.ktx.appStringContext
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.base.ktx.toSafeLong
import com.interfun.buz.base.ktx.toast
import com.interfun.buz.biz.center.voicemoji.model.voiceemoji.VoiceEmojiEntity
import com.interfun.buz.biz.center.voicemoji.repository.voiceemoji.VoiceEmojiRepository
import com.interfun.buz.chat.R
import com.interfun.buz.common.bean.push.PushPayload
import com.interfun.buz.common.bean.push.extra.BasePushExtra
import com.interfun.buz.common.bean.push.extra.BuzPushReactionType
import com.interfun.buz.common.bean.push.extra.BuzReactionOperateType
import com.interfun.buz.common.bean.push.extra.BuzReactionOperateType.ADD
import com.interfun.buz.common.bean.push.extra.BuzReactionOperateType.REMOVE
import com.interfun.buz.common.bean.push.extra.BuzReactionOperateType.REPLACE
import com.interfun.buz.common.bean.push.extra.BuzReactionOperateType.UNKNOWN
import com.interfun.buz.common.bean.push.extra.IMReactionType
import com.interfun.buz.common.database.entity.UserRelationInfo
import com.interfun.buz.common.database.entity.UserStatus
import com.interfun.buz.common.ktx.getContactFirstName
import com.interfun.buz.common.ktx.isFriend
import com.interfun.buz.common.ktx.uid
import com.interfun.buz.common.manager.AppConfigRequestManager
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.manager.cache.user.UserRelationCacheManager
import com.interfun.buz.common.manager.router.RouterCreator
import com.interfun.buz.common.utils.language.LanguageManager
import com.interfun.buz.domain.voiceemoji_im.ktx.isBlindBox
import com.interfun.buz.im.IMAgent
import com.interfun.buz.im.ktx.isPrivate
import com.interfun.buz.im.ktx.isSend
import com.interfun.buz.im.util.IMUtils
import com.lizhi.component.basetool.ntp.NtpTime
import com.lizhi.im5.sdk.base.Reaction
import com.lizhi.im5.sdk.base.ReactionInfo
import com.lizhi.im5.sdk.base.ReactionOperation
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import com.lizhi.im5.sdk.message.IMessage
import com.yibasan.lizhifm.sdk.platformtools.ResUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.withContext
import org.json.JSONObject

/**
 * <AUTHOR>
 * @date 2024/4/2
 * @desc
 */
data class ChatQuickReactInfo(
    val voicemoji: VoiceEmojiEntity,
    var userList: List<Long>?,
    var userCount: Int,
    val createVer: Long,
    val updateVer:Long
)

data class ChatQRHistoryItemBean(
    val msg: IMessage,
    val userId: Long,
    val voicemoji: VoiceEmojiEntity
)

data class ChatQRHistoryTabInfo(
    val title: String,
    val content: List<ChatQRHistoryItemBean>
)

data class ChatQRPayloadInfo(
    val operateType: BuzReactionOperateType,
    val operator: Long,
    val voicemojiId: Long
)

private const val TAG = "ChatQuickReactInfo"

private val localQROperations = mutableMapOf<String, ReactionOperation>()

private fun ReactionOperation?.equal(other: ReactionOperation?): Boolean {
    if (this == null || other == null) return false
    return type == other.type && operator == other.operator && reactionId == other.reactionId
}

suspend fun IMessage.getMsgQuickReactInfoList(): List<ChatQuickReactInfo>? {
    val localOp = if (serMsgId != null) {
        localQROperations[serMsgId]
    } else {
        null
    }
    val infoList = if (localOp != null) {
        if (reactionOperation.equal(localOp)) {
            logInfo(TAG, "getMsgQuickReactInfoList localOp:$localOp")
            reactionInfos
        } else {
            when (localOp.type) {
                ADD.value -> addQuickReactInNewList(
                    reactionInfos,
                    localOp.operator,
                    localOp.reactionId
                )

                REPLACE.value -> replaceQuickReactInNewList(
                    reactionInfos,
                    localOp.operator,
                    localOp.oldReactionId ?: "",
                    localOp.reactionId
                )

                REMOVE.value -> removeQuickReactInNewList(
                    reactionInfos,
                    localOp.operator,
                    localOp.reactionId
                )

                else -> reactionInfos
            }
        }
    } else {
        reactionInfos
    }
    if (infoList.isNullOrEmpty()) return null
    return infoList.mapNotNull { info ->
        logInfo(TAG, "getMsgQuickReactInfoList info:$info")
        if (info.reaction.type != IMReactionType.QUICK_REACT.value) return@mapNotNull null
        val voicemoji = VoiceEmojiRepository.getVoiceEmojiById(info.reaction.id.toLongOrNull()).firstOrNull()
        voicemoji?.let {
            ChatQuickReactInfo(
                it,
                info.userList?.mapNotNull { id -> id.toLongOrNull() },
                info.userCount,
                info.createVer,
                info.updateVer
            )
        }
    }
}

fun ReactionOperation?.convertToChatQRPayloadInfo(): ChatQRPayloadInfo? {
    this ?: return null
    val operateType = when (type) {
        ADD.value -> ADD
        REMOVE.value -> REMOVE
        REPLACE.value -> REPLACE
        else -> UNKNOWN
    }
    val operator = operator.toLongOrNull() ?: return null
    val voicemojiId = reactionId.toLongOrNull() ?: return null
    return ChatQRPayloadInfo(operateType, operator, voicemojiId)
}

fun VoiceEmojiEntity.convertToIMReaction(
    msg: IMessage? = null,
    pushExtra: BasePushExtra? = null,
    reactionOperateType: BuzReactionOperateType = UNKNOWN
): Reaction {
    val reaction = Reaction(
        type = IMReactionType.QUICK_REACT.value,
        id = id.toString()
    )
    if (AppConfigRequestManager.enableNewOfflinePushFormat){
        reaction.pushPayload = IMUtils.createPayloadV2()
        reaction.pushContent = ""
    }else {
        msg?.let { message ->
            val pushContent = if (reactionOperateType == ADD
                || reactionOperateType == REPLACE
            ) {
                appStringContext.getString(R.string.qr_notify_tip, emojiIcon)
            } else {
                ""
            }
            val myUserInfo = UserSessionManager.userProfile
            val pushPayload = PushPayload(
                title = myUserInfo?.userName,
                pushContent = pushContent,
                router = when (message.conversationType) {
                    IM5ConversationType.PRIVATE -> {
                        RouterCreator.createPrivateChatRouter(
                            fromId = UserSessionManager.uid.toString(),
                            targetId = if (msg.isSend) msg.targetId else msg.fromId,
                            serMsgId = msg.serMsgId,
                            reactionOpUserId = UserSessionManager.uid.toString()
                        )
                    }
                    IM5ConversationType.GROUP -> {
                        RouterCreator.createGroupChatRouter(
                            groupId = msg.targetId.toLong(),
                            serMsgId = msg.serMsgId,
                            reactionOpUserId = UserSessionManager.uid.toString()
                        )
                    }
                    else -> {
                        JSONObject()
                    }
                },
                senderUserInfo = UserInfo(
                    myUserInfo?.userId,
                    myUserInfo?.userName,
                    myUserInfo?.firstName,
                    myUserInfo?.lastName,
                    myUserInfo?.portrait,
                    myUserInfo?.phone,
                    0L,
                    null,
                    1,
                    UserRelationInfo.USER_TYPE_NORMAL_USER,
                    LanguageManager.getLocaleLanguageName(),
                    myUserInfo?.buzId,
                    myUserInfo?.email,
                    UserStatus.STATUS_NORMAL,
                    myUserInfo?.imUin,
                    null
                ),
                type = PushPayload.TYPE_QUICK_REACT,
                pushExtra = pushExtra?.apply {
                    reactionType = BuzPushReactionType.QR_VE.value
                    reactionOpUserId = UserSessionManager.uid.toString()
                    reactionOpType = reactionOperateType.value
                    reactionEmoji = <EMAIL>
                    ntpTime = NtpTime.nowForce()
                }?.toJsonObject()
            )
            reaction.pushPayload = pushPayload.toJson()
            reaction.pushContent = pushContent
        }
    }
    return reaction
}

suspend fun IMessage.operateQuickReact(
    lifecycleOwner: LifecycleOwner?,
    type: BuzReactionOperateType,
    pushExtra: BasePushExtra,
    voicemoji: VoiceEmojiEntity,
    old: VoiceEmojiEntity? = null,
    onResult: TwoParamCallback<Boolean, IMessage?>){
    val serMsgId = this.serMsgId
    if (serMsgId.isNullOrEmpty()) {
        logInfo(TAG, "onOperation serMsgId is null")
        withContext(Dispatchers.Main) {
            toast(R.string.network_error)
        }
        return
    }
    if (voicemoji.isBlindBox()) {
        return
    }
    val qrOperation = ReactionOperation(
        type = type.value,
        operator = UserSessionManager.uid.toString(),
        reactionType = IMReactionType.QUICK_REACT.value,
        reactionId = voicemoji.id.toString()
    )
    old?.let {
        qrOperation.oldReactionId = it.id.toString()
        qrOperation.oldReactionType = IMReactionType.QUICK_REACT.value
    }
    localQROperations[serMsgId] = qrOperation
    onResult.invoke(false, null)
    logInfo(TAG, "onOperation type: $type serMsgId: $serMsgId")
    val reaction = voicemoji.convertToIMReaction(this, pushExtra, type)
    val result = when (type) {
        ADD -> IMAgent.addReactionSync(lifecycleOwner, msgId, conversationType, reaction)
        REMOVE -> IMAgent.removeReactionSync(lifecycleOwner, msgId, conversationType, reaction)
        REPLACE -> IMAgent.updateReactionSync(
            lifecycleOwner,
            msgId,
            conversationType,
            old!!.convertToIMReaction(),
            reaction
        )
        else -> return
    }
    val message = result.first
    val errorCode = result.second
    localQROperations.remove(serMsgId)
    if (message != null) {
        logInfo(TAG, "onSuccess type: $type serMsgId: $serMsgId message: $message")
        onResult.invoke(true, message)
    } else {
        logInfo(TAG, "onError type: $type serMsgId: $serMsgId errorCode: $errorCode")
        val fetchMsg = IMAgent.getMessageSync(lifecycleOwner, conversationType, msgId)
        onResult.invoke(true, fetchMsg)
        if (errorCode == 3 && isPrivate) {
            val userId = fromId.toSafeLong()
            UserRelationCacheManager.getUserFromServer(userId)?.let { userInfo ->
                withContext(Dispatchers.Main){
                    if (userInfo.isFriend.not()) {
                        toast(R.string.chat_add_friend_first)
                    } else {
                        toast(
                            ResUtil.getString(
                                R.string.rejects_receive_your_msg,
                                userInfo.getContactFirstName()
                            )
                        )
                    }
                }
            }
        }
    }
}

private fun addQuickReactInNewList(
    list: List<ReactionInfo>?,
    uid: String,
    veId: String
): List<ReactionInfo> {
    val infoList = list?.toMutableList()?: mutableListOf()
    val reactInfoIndex = infoList.indexOfFirst { it.reaction.id == veId }
    val reaction = Reaction(type = IMReactionType.QUICK_REACT.value, id = veId)
    if (reactInfoIndex != -1) {
        // 如果reactInfo存在，将其移动到列表首位，并更新userList
        val reactInfo = infoList.removeAt(reactInfoIndex)
        val userList = reactInfo.userList?.toMutableList() ?: mutableListOf()
        userList.remove(uid) // 移除已存在的uid（如果有）
        userList.add(0, uid) // 将uid添加到列表首位
        val info = ReactionInfo(reaction, userList, userList.size, 0, 0)
        infoList.add(0, info) // 将更新后的reactInfo添加到列表首位
    } else {
        // 如果reactInfo不存在，创建一个新的reactInfo并添加到列表首位
        infoList.add(0, ReactionInfo(reaction, listOf(uid), 1, 0, 0))
    }
    return infoList
}

private fun removeQuickReactInNewList(list: List<ReactionInfo>?, uid: String, veId: String): List<ReactionInfo> {
    val infoList = list?.toMutableList() ?: mutableListOf()
    val reactInfo = infoList.find { it.reaction.id == veId }
    reactInfo?.let {
        val userList = it.userList?.filterNot { userId -> userId == uid }
        if (userList.isNullOrEmpty()) {
            infoList.remove(it)
        } else {
            val index = infoList.indexOf(reactInfo)
            infoList[index] = ReactionInfo(it.reaction, userList, it.userList?.size ?: 0, 0, 0)
        }
    }
    return infoList
}

private fun replaceQuickReactInNewList(
    list: List<ReactionInfo>?,
    uid: String,
    oldVeId: String,
    newVeId: String
): List<ReactionInfo> {
    val stepList = removeQuickReactInNewList(list, uid, oldVeId)
    return addQuickReactInNewList(stepList, uid, newVeId)
}
