package com.interfun.buz.chat.wt.service

import android.app.Service
import android.content.Intent
import android.os.IBinder
import com.interfun.buz.base.ktx.OneParamCallback
import com.interfun.buz.base.ktx.isNotNull
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.chat.wt.manager.WTQuietModeManager
import com.interfun.buz.chat.wt.manager.WTStatusManager
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.constants.RouterParamKey
import com.interfun.buz.common.di.RealtimeCallCanonicalName
import com.interfun.buz.common.manager.*
import com.interfun.buz.common.manager.cache.voicemoji.VoiceEmojiAnimationRecycler
import com.interfun.buz.common.manager.chat.CallPendStatus
import com.interfun.buz.common.manager.chat.ChannelInviteManager
import com.interfun.buz.common.manager.chat.ChannelPendStatusManager
import com.interfun.buz.common.manager.voicecall.VoiceCallNotificationConflictManager
import com.interfun.buz.common.service.RealTimeCallService
import com.interfun.buz.common.utils.NotificationUtil
import com.interfun.buz.im.signal.PushAgentManager
import com.interfun.buz.onair.standard.IGlobalOnAirController
import com.interfun.buz.user.repository.UserSettingRepository
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import javax.inject.Inject

@AndroidEntryPoint
class WalkieTalkieService : Service() {

    @Inject
    lateinit var userSettingRepository: UserSettingRepository

    private val notificationManager by lazy {
        ForegroundDNDNotificationManager(this).apply {
            WTStatusManager.updateDndInQuitTipState(false)
            onStartForegroundCallback = { isStarted ->
                isServiceStartForeground = isStarted
            }
        }
    }

    private val flowManager = CommonFlowManager

    private val TAG = "BuzApp"
    override fun onCreate() {
        super.onCreate()
        isServiceStartForeground = false
        launchWalkietalkieFlag = true
        logInfo(TAG, "WalkieTalkieService onCreate")
        notificationManager.onCreate()
        GlobalEventManager.onCreate()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        logInfo(TAG, "WalkieTalkieService onStartCommand")
        val isFromOverlay =
            intent?.getBooleanExtra(RouterParamKey.Common.KEY_IS_FROM_OVERLAY, false) ?: false
        notificationManager.checkAndShowNotification(isFromOverlay)
        return super.onStartCommand(intent, flags, startId)
    }

    override fun onDestroy() {
        super.onDestroy()
        launchWalkietalkieFlag = false
        logInfo(TAG, "WalkieTalkieService onDestroy")
        notificationManager.onDestroy()

    }
    @RealtimeCallCanonicalName
    @Inject
    lateinit var realtimeActivityName: String

    override fun onTaskRemoved(rootIntent: Intent?) {
        super.onTaskRemoved(rootIntent)
        logInfo(TAG, "WalkieTalkieService onTaskRemoved ${rootIntent?.component?.className}")

        /**
         * realtimeActivity 是一个双栈结构 activity,如果这个被关闭那么此时也会回调onTaskRemoved
         */
        if (rootIntent?.component?.className==realtimeActivityName){
            return
        }
        if (rootIntent?.component?.className=="com.interfun.buz.startup.GatewayActivity"){
            return
        }
        PushAgentManager.disConnect()
        val voiceCallService = routerServices<RealTimeCallService>().value
        voiceCallService?.hangUp()
        //如果当前正在发起voicecall呼叫中，呼叫等待音效需要停止
        val isCallWaitingState = ChannelPendStatusManager.isCallWaitingState()
        if (isCallWaitingState){
            ChannelPendStatusManager.changeCallWaitingStatus(isWaiting = false, isCallConnected = false)
        }
        if (ChannelPendStatusManager.isCalledPendState()){
            logInfo(TAG, "changeStatus IDLE: WalkieTalkieService")
            val callPendState = ChannelPendStatusManager.getCallPendState()
            val channelId = callPendState.second?.channelId
            val channelType = callPendState.second?.channelType
            if (channelId != null && channelId > 0 && channelType.isNotNull()){
                val voiceCallNotifiId =
                    CallNotificationCache.getVoiceCallNotifiIdByChannelId(channelId)
                if (voiceCallNotifiId != null){
                    NotificationUtil.cancelVoiceCallNotificationAndUpdatePendStatus(voiceCallNotifiId,channelType!!)
                }
                ChannelInviteManager.removeInviteMapByChannelId(channelId.toString())
            }
            ChannelPendStatusManager.changeStatus(CallPendStatus.IDLE)
        }
        ChannelInviteManager.onServiceDestroy()
        VoiceCallNotificationConflictManager.onServiceDestroy()
        GlobalEventManager.onServiceDestroy()
        VoiceEmojiAnimationRecycler.onServiceDestroy()
        routerServices<IGlobalOnAirController>().value?.killApp()
        RegisterStatusManager.onServiceDestroy()
        GlobalScope.launch{
            flowManager.resetKnockCache()
        }


        if (!ABTestManager.closeNotificationAfterKill || notificationManager.isQuietModeEnable){
            launchWalkietalkieFlag = false
            WTStatusManager.requestChangeSwitchStatus(false)
            stopSelf()
        }

    }

    override fun onBind(intent: Intent?): IBinder? {
        return null
    }

    companion object {
        var launchWalkietalkieFlag = false
            private set(value) {
                field = value
                onStartedChangeCallback?.invoke(isStartedForeground())
            }
        var isServiceStartForeground: Boolean = false
            private set(value) {
                field = value
                onStartedChangeCallback?.invoke(isStartedForeground())
            }

        var onStartedChangeCallback: OneParamCallback<Boolean>? = null

        fun isStartedForeground(): Boolean {
            return launchWalkietalkieFlag && isServiceStartForeground
        }
    }

}