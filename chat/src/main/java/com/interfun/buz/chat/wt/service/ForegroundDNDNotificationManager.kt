package com.interfun.buz.chat.wt.service

import android.app.Notification
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.Color
import android.graphics.drawable.BitmapDrawable
import android.os.Build
import android.util.TypedValue
import android.view.View
import android.widget.RemoteViews
import androidx.core.app.NotificationCompat
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.ViewModelStore
import androidx.lifecycle.ViewModelStoreOwner
import coil.imageLoader
import coil.request.ImageRequest
import coil.request.SuccessResult
import coil.size.Precision
import coil.transform.CircleCropTransformation
import com.interfun.buz.base.coroutine.CloseableCoroutineScope
import com.interfun.buz.base.ktx.*
import com.interfun.buz.chat.R
import com.interfun.buz.chat.wt.broadcast.NotificationActionReceiver
import com.interfun.buz.chat.wt.broadcast.NotificationActionReceiver.Companion.ACTION_CLOSE_NOTIFICATION
import com.interfun.buz.chat.wt.broadcast.NotificationActionReceiver.Companion.ACTION_QUIT_STATE_CHANGE
import com.interfun.buz.chat.wt.broadcast.NotificationActionReceiver.Companion.ACTION_SWITCH_CHANGED
import com.interfun.buz.chat.wt.entity.isDirectionSend
import com.interfun.buz.chat.wt.manager.WTMessageManager
import com.interfun.buz.chat.wt.manager.WTStatusManager
import com.interfun.buz.chat.wt.viewmodel.ForegroundServiceViewModel
import com.interfun.buz.common.constants.CommonConstant
import com.interfun.buz.common.ktx.getContactFirstName
import com.interfun.buz.common.manager.ABTestManager
import com.interfun.buz.common.manager.UserManager
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.manager.router.RouterCreator
import com.interfun.buz.common.manager.router.RouterManager
import com.interfun.buz.common.manager.userLifecycleScope
import com.interfun.buz.common.utils.BuzTracker
import com.interfun.buz.common.utils.NotificationChannelUtils
import com.interfun.buz.common.utils.NotificationUtil
import com.interfun.buz.common.widget.portrait.PortraitUtil
import com.interfun.buz.common.widget.portrait.group.fetcher.GroupPortraitFetcherInfo
import com.interfun.buz.user.repository.UserSettingRepository
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.collectLatest
import java.util.LinkedList
import java.util.Random
import java.util.concurrent.ConcurrentHashMap

class NotificationWidgetDND(
    val notification: Notification,
    val collapsedView: RemoteViews,
)

/**
 * 如果允许关联DND则使用该前台样式
 */
class ForegroundDNDNotificationManager(val service: WalkieTalkieService) :
    BaseForegroundNotificationManager, ViewModelStoreOwner {
    companion object {
        private const val TAG = "ForegroundDNDNotificationManager"
    }
    private val userSettingRepository get() = service.userSettingRepository

    class ForegroundServiceViewModelFactory(
        private val userSettingRepository: UserSettingRepository
    ) : ViewModelProvider.Factory {
        override fun <T : ViewModel> create(modelClass: Class<T>): T {
            return ForegroundServiceViewModel(userSettingRepository) as T
        }
    }
    private val id = Random().nextInt(Int.MAX_VALUE) + 1
    private val notificationManager by lazy {
        appContext.getSystemService(Context.NOTIFICATION_SERVICE) as android.app.NotificationManager
    }
    private var isCreated = true
    override val viewModelStore = ViewModelStore()
    private var scope = CloseableCoroutineScope(SupervisorJob() + Dispatchers.Main.immediate)
    private val factory = ForegroundServiceViewModelFactory(userSettingRepository)
    private val viewModel = ViewModelProvider(this, factory)[ForegroundServiceViewModel::class.java]
    private val bitmapMap = ConcurrentHashMap<BitmapKey, Bitmap>()
    private var isStartedForeground = false
    private val isSmallDevice = Build.VERSION.SDK_INT <= Build.VERSION_CODES.P
    private var defaultNotificationWidgetDND: NotificationWidgetDND? = null

    var onStartForegroundCallback: OneParamCallback<Boolean>? = null
    val isQuietModeEnable get() = viewModel.isQuietModeEnable

    private fun isLogin() : Boolean {
        return UserSessionManager.hasSession()
    }

    override fun onCreate() {
        logDebug(TAG, "foregroundNotification->: oncreate ${this}")
        isCreated = true
        initData()
    }

    override fun onDestroy() {
        logDebug(TAG, "foregroundNotification->: onDestroy ${this}")
        defaultNotificationWidgetDND = null
        viewModelStore.clear()
        destroyNotification()
        isCreated = false
        scope.close()
    }

    override fun checkAndShowNotification(isFromOverlay: Boolean) {
        val args = viewModel.argsValue
        if (isLogin()) {
            if (args != null) {
                updateNotification(args, isFromOverlay)
            } else {
                showDefaultNotification(isFromOverlay)
            }
        }
    }

    private fun destroyNotification() {
        service.stopForeground(true)
        isStartedForeground = false
        onStartForegroundCallback?.invoke(isStartedForeground)
    }

    private fun showDefaultNotification(isFromOverlay: Boolean) {
        if (canStartForeground(isFromOverlay).not()) {
            return
        }
        if (isStartedForeground) {
            return
        }
        val widget = defaultNotificationWidgetDND ?: createDefaultNotification()

        startForeground(widget, isFromOverlay)
    }

    private fun updateDefaultNotification(isFromOverlay: Boolean) {
        val widget =  defaultNotificationWidgetDND ?: createDefaultNotification()
        widget.let {
            updateDefaultNotificationData(it.collapsedView)
            it.collapsedView.setViewVisibility(R.id.flInfo, View.GONE)
            it.collapsedView.setViewVisibility(R.id.llDefault, View.VISIBLE)
            startForeground(it, isFromOverlay)
        }
    }

    private fun updateDefaultNotificationData(collapsedView: RemoteViews) {
        val imgId: Int
        val descId: Int
        logInfo(TAG, "isQuietModeEnable=${viewModel.isQuietModeEnable}")
        if (viewModel.isQuietModeEnable) {
            imgId = R.drawable.ic_notification_quite_mode_on
            descId = R.string.foreground_auto_play_off
        } else {
            imgId = R.drawable.ic_notification_quite_model_off
            descId = R.string.foreground_auto_play_on
        }

        collapsedView.setImageViewResource(R.id.ivQuietModeSwitch, imgId)
        collapsedView.setTextViewText(R.id.tvContent, descId.asString())
    }

    private fun startForeground(widget: NotificationWidgetDND, isFromOverlay: Boolean) {
        if (canStartForeground(isFromOverlay).not()) {
            logInfo(
                TAG,
                "startForeground return,isFromOverlay:$isFromOverlay,isAppInForeground:$isAppInForeground"
            )
            return
        }
        try {
            service.startForeground(id, widget.notification)
            isStartedForeground = true
            onStartForegroundCallback?.invoke(isStartedForeground)
            logInfo(TAG, "startForeground succeed")
        } catch (t: Throwable) {
            logError(TAG, t, "startForeground failed")
            BuzTracker.onForegroundServiceStartFailed(isAppInForeground, t, "2")
        }
    }

    private fun canStartForeground(isFromOverlay: Boolean): Boolean {
        val cannotStart =
            isFromOverlay.not() && isAppInForeground.not() && Build.VERSION.SDK_INT >= Build.VERSION_CODES.O
        return cannotStart.not()
    }


    private fun initObserver() {
        scope.launch {
            UserManager.isUserLogin.collectLatest{ islogin ->
               if (islogin) {
                   log(TAG, "islogin showDefaultNotification}")
                   showDefaultNotification(false)
               }
            }
        }

        scope.launch {
            viewModel.notificationArgsFlow.collect { args ->
                log(TAG, "notificationArgsFlow update:$args， isLogin= ${isLogin()}")
                if (isLogin()) {
                    if (args != null) {
                        defaultNotificationWidgetDND = null
                        updateNotification(args, false)
                    } else {
//                        showDefaultNotification(false)
                        log(TAG, "notificationArgsFlow ignore----------->")
                    }
                }
            }
        }
        scope.launch {
            WTMessageManager.messageFlow.collect {
                if (it.first.isDirectionSend()) return@collect
                viewModel.updateSpeakingInfo(it)
            }
        }
        scope.launch {
            viewModel.quietModeFlow.collect {
                if (it) {
                    viewModel.clearSpeakingInfo()
                }
                defaultNotificationWidgetDND = createDefaultNotification()
                defaultNotificationWidgetDND?.let {
                    updateDefaultNotificationData(it.collapsedView)
                    it.collapsedView.setViewVisibility(R.id.flInfo, View.GONE)
                    it.collapsedView.setViewVisibility(R.id.llDefault, View.VISIBLE)
                    notifyNotification(it, false)
                }
            }
        }

        scope.launch {
            WTStatusManager.dndInQuitTipState.collect{ isInQuitTip ->
                logInfo(TAG,"initObserver: isInQuitTip:$isInQuitTip")
                defaultNotificationWidgetDND = createDefaultNotification()
                if (isInQuitTip){
                    defaultNotificationWidgetDND?.collapsedView?.setViewVisibility(R.id.rlNotification,View.GONE)
                    defaultNotificationWidgetDND?.collapsedView?.setViewVisibility(R.id.rlQuitView,View.VISIBLE)
                }else{
                    defaultNotificationWidgetDND?.collapsedView?.setViewVisibility(R.id.rlNotification,View.VISIBLE)
                    defaultNotificationWidgetDND?.collapsedView?.setViewVisibility(R.id.rlQuitView,View.GONE)
                }
                defaultNotificationWidgetDND?.let { notifyNotification(it,false) }
            }
        }
    }

    private fun initData() {
        initObserver()
        viewModel.startIntervalUpdate()
    }

    private fun updateNotification(args: ForegroundNotificationArgs, isFromOverlay: Boolean) {
        val widget = defaultNotificationWidgetDND ?: createDefaultNotification()
        updateBitmapCache(args)
        updateUiAndNotify(widget, args, isFromOverlay)
    }

    private fun updateUiAndNotify(
        widget: NotificationWidgetDND,
        args: ForegroundNotificationArgs,
        isFromOverlay: Boolean
    ) {
        val (currentChat, uiRecentChats) = getUiChats(args)
        if (currentChat == null) {
            updateDefaultNotification(isFromOverlay)
            return
        }
        updateUi(widget, currentChat)
        notifyNotification(widget, isFromOverlay)
    }

    private fun notifyNotification(widget: NotificationWidgetDND, isFromOverlay: Boolean) {
        if (isStartedForeground.not()) {
            if (isAppInForeground.not() && Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                return
            }
            logInfo(TAG, "startForeground in notifyNotification ${this}")
            startForeground(widget, isFromOverlay)
        } else {
            // 这里可能会出现binder 调用时间过长的问题，所以放到后台线程
            if (defaultNotificationWidgetDND != null) {
                logInfo(TAG, "notify notification ${this}")
                (userLifecycleScope ?: GlobalScope).launch(Dispatchers.IO) {
                    notificationManager.notify(id, widget.notification)
                }
            }
        }
    }

    private fun getUiChats(args: ForegroundNotificationArgs): Pair<NotificationBaseInfo?, List<NotificationBaseInfo>> {
        val recentChats = args.recentChats
        val size = recentChats?.size ?: 0
        val currentChat = recentChats?.firstOrNull()
        val uiRecentChats =
            if (size > 1) recentChats?.subList(1, size) ?: emptyList() else emptyList()
        return currentChat to uiRecentChats
    }

    private fun updateBitmapCache(args: ForegroundNotificationArgs) {
        val keys = LinkedList<BitmapKey>()
        args.recentChats?.forEach {
            convertToBitmapKey(it)?.let { key -> keys.add(key) }
        }
        val tempMap = hashMapOf<BitmapKey, Bitmap>()
        keys.forEach {
            val bitmap = bitmapMap[it]
            if (bitmap != null) {
                tempMap[it] = bitmap
            }
        }
        bitmapMap.clear()
        bitmapMap.putAll(tempMap)
        keys.forEach {
            if (!bitmapMap.containsKey(it)) {
                requestBitmap(it)
            }
        }
    }

    private fun requestBitmap(bitmapKey: BitmapKey) {
        log(TAG, "requestBitmap , bitmapKey:${bitmapKey.targetId}")
        scope.launch(Dispatchers.IO) {
            val builder = ImageRequest.Builder(appContext)
                .size(PortraitUtil.defaultPortraitSize, PortraitUtil.defaultPortraitSize)
            val request = when (bitmapKey) {
                is UserBitmapKey -> {
                    builder.data(bitmapKey.url).transformations(CircleCropTransformation()).build()
                }

                is GroupBitmapKey -> {
                    val groupInfoBean = bitmapKey.groupInfo
                    val resizeUrlList = groupInfoBean.firstFewPortraits?.map { url ->
                        PortraitUtil.resizePortraitUrl(
                            url,
                            (PortraitUtil.defaultPortraitSize * 0.35f).toInt()
                        )
                    }
                    val fetcherInfo = GroupPortraitFetcherInfo(
                        groupId = groupInfoBean.groupId,
                        portraitList = resizeUrlList,
                        legacyPortrait = groupInfoBean.portraitUrl
                    )
                    val cacheKey = resizeUrlList?.toHashString()
                    builder.data(fetcherInfo)
                        .error(PortraitUtil.defaultPortraitRes)
                        .memoryCacheKey(cacheKey)
                        .diskCacheKey(cacheKey)
                        .precision(Precision.INEXACT)
                        .build()
                }

                else -> {
                    return@launch
                }
            }
            val result = appContext.imageLoader.execute(request) as? SuccessResult
            val drawable = result?.drawable
            val bitmap = (drawable as? BitmapDrawable)?.bitmap
            if (bitmap != null && isActive) {
                val widget = defaultNotificationWidgetDND
                val args = viewModel.notificationArgsFlow.value
                if (widget != null && args != null) {
                    withContext(Dispatchers.Main) {
                        bitmapMap[bitmapKey] = bitmap
                        log(TAG, "updateUiAndNotify in requestBitmap")
                        updateUiAndNotify(widget, args, false)
                    }
                }
            }
        }
    }

    private fun convertToBitmapKey(info: NotificationBaseInfo?): BitmapKey? {
        if (info is NotificationUserInfo) {
            val url = info.user.portrait
            if (url.notEmpty()) {
                val resizeUrl = PortraitUtil.resizePortraitUrl(url)
                return UserBitmapKey(info.targetId, resizeUrl)
            }
        } else if (info is NotificationGroupInfo) {
            return GroupBitmapKey(info.targetId, info.groupInfo)
        }
        return null
    }

    private fun updateUi(
        widget: NotificationWidgetDND,
        currentChat: NotificationBaseInfo,
    ) {
        // update currentUser
        handleChatInfo(widget, currentChat, true)
    }

    private fun handleChatInfo(
        widget: NotificationWidgetDND,
        info: NotificationBaseInfo?,
        isCurrent: Boolean,
        index: Int = -1
    ) {
        val c = widget.collapsedView
        val remoteViews = listOf(widget.collapsedView)
        val flInfo = R.id.flInfo
        val ivPortraitId =  R.id.ivPortraitCurrent
        val groupNameId = R.id.tvGroupName
        val userNameId = R.id.tvUserName
        if (info == null) {
            c.setImageViewResource(ivPortraitId, R.drawable.chat_notification_placeholder)
            return
        }

        if (isCurrent) {
            if (info.isSpeaking) {
                val padding = if (isSmallDevice) 0.dp else 6.dp
                remoteViews.forEach {
                    it.setViewVisibility(R.id.flInfo, View.VISIBLE)
                    it.setViewVisibility(R.id.llDefault, View.GONE)
                    it.setViewPadding(R.id.flPortrait, padding, padding, padding, padding)
                    it.setViewVisibility(R.id.ivSpeaking, View.VISIBLE)
                    it.setViewVisibility(R.id.tvSpeaking, View.VISIBLE)
                    it.setTextColor(R.id.tvUserName, R.color.notification_green.asColor())
                    if (info.voiceMoji != null) {
                        it.setViewVisibility(R.id.ivPortraitCurrent, View.GONE)
                        it.setViewVisibility(R.id.ivPortraitBg, View.GONE)
                        it.setViewVisibility(R.id.tvVoiceMoji, View.VISIBLE)
                        it.setTextViewText(R.id.tvVoiceMoji, info.voiceMoji)
                        it.setViewVisibility(R.id.tvSpeaking, View.GONE)
                        it.setViewVisibility(R.id.tvVoiceMojiView, View.VISIBLE)
                        it.setTextViewText(
                            R.id.tvVoiceMojiView,
                            String.format(
                                R.string.ve_voiceemoji_placehold_updated.asString(),
                                info.voiceMoji
                            )
                        )
                        val nameTextSize = if (isSmallDevice) 12f else 15f
                        it.setTextViewTextSize(R.id.tvUserName, TypedValue.COMPLEX_UNIT_SP, nameTextSize)

                    } else {
                        it.setViewVisibility(R.id.tvVoiceMoji, View.GONE)
                        it.setViewVisibility(R.id.ivPortraitCurrent, View.VISIBLE)
                        it.setViewVisibility(R.id.ivPortraitBg, View.VISIBLE)
                        it.setViewVisibility(R.id.tvVoiceMojiView, View.GONE)
                    }
                }

            } else {
                remoteViews.forEach {
                    it.setViewVisibility(R.id.flInfo, View.GONE)
                    it.setViewVisibility(R.id.llDefault, View.VISIBLE)
                    it.setViewPadding(R.id.flPortrait, 0, 0, 0, 0)
                    it.setViewVisibility(R.id.ivSpeaking, View.GONE)
                    it.setViewVisibility(R.id.tvSpeaking, View.GONE)
                    it.setTextColor(R.id.tvUserName, R.color.notification_80.asColor())
                    if (info.voiceMoji != null) {
                        it.setViewVisibility(R.id.tvVoiceMoji, View.GONE)
                        it.setViewVisibility(R.id.tvSpeaking, View.GONE)
                        it.setViewVisibility(R.id.tvVoiceMojiView, View.GONE)
                    } else {
                        it.setViewVisibility(R.id.tvVoiceMoji, View.GONE)
                        it.setViewVisibility(R.id.tvVoiceMojiView, View.GONE)
                    }
                }
            }
        }

        info.dispatch(
            group = { groupInfo ->
                val userName = groupInfo.lastMsgUser?.getContactFirstName() ?: ""
                val intent = generateChatPendingIntent(flInfo, groupInfo.targetId.toString(), 2, 1)
                remoteViews.forEach {
                    getBitmapByInfo(info)?.let { bitmap ->
                        it.setImageViewBitmap(ivPortraitId, bitmap)
                    } ?: run {
                        it.setImageViewResource(
                            ivPortraitId,
                            R.drawable.common_user_default_portrait_round
                        )
                    }
                    it.setTextViewText(groupNameId, groupInfo.groupInfo.groupName)
                    it.setViewVisibility(groupNameId, View.VISIBLE)
                    it.setTextViewText(userNameId, userName)
                    if (!isCurrent) {
                        if (groupInfo.lastMsgUser == null) {
                            it.setViewVisibility(userNameId, View.GONE)
                        } else {
                            it.setViewVisibility(userNameId, View.VISIBLE)
                        }
                    } else {
                        it.setViewVisibility(R.id.ivSpacing, View.GONE)
                    }
                    it.setOnClickPendingIntent(flInfo, intent)
                }
            },
            user = { userInfo ->
                val intent = generateChatPendingIntent(flInfo, userInfo.targetId.toString(), 1, 1)
                remoteViews.forEach {
                    getBitmapByInfo(info)?.let { bitmap ->
                        it.setImageViewBitmap(ivPortraitId, bitmap)
                    } ?: run {
                        it.setImageViewResource(
                            ivPortraitId,
                            R.drawable.common_user_default_portrait_round
                        )
                    }
                    it.setTextViewText(groupNameId, "")
                    it.setViewVisibility(groupNameId, View.GONE)
                    it.setTextViewText(userNameId, userInfo.user.getContactFirstName())
                    if (isCurrent) {
                        it.setViewVisibility(R.id.ivSpacing, View.VISIBLE)
                    }
                    it.setOnClickPendingIntent(flInfo, intent)
                }
            }
        )
    }

    private fun generateChatPendingIntent(
        id: Int,
        targetId: String,
        type: Int = 1,
        clickNotificationSpot: Int
    ): PendingIntent {
        val router = RouterCreator.createHomeListRouter(targetId, type, clickNotificationSpot)
        return RouterManager.getPendingIntentByRouter(id, router.toString())
    }

    private fun getBitmapByInfo(info: NotificationBaseInfo): Bitmap? {
        return convertToBitmapKey(info)?.let { bitmapMap[it] }
    }


    private fun createDefaultNotification(): NotificationWidgetDND {
        val builder = getBaseNotificationBuilder()
        // 部分机型通知栏折叠高度只有42或40，大部分是48，找不到哪些是小的，先通过是否超过Android9来区分
        val res = if (isSmallDevice) {
            R.layout.notification_dnd_layout_collapsed_small
        } else {
            R.layout.notification_dnd_layout_collapsed
        }
        val collapsedView = RemoteViews(appContext.packageName, res)
        updateDefaultNotificationData(collapsedView)
        val pendingIntent = generateChatPendingIntent(R.id.rlRoot, "", clickNotificationSpot = 2)
        collapsedView.setOnClickPendingIntent(R.id.rlRoot, pendingIntent)
        collapsedView.setOnClickPendingIntent(
            R.id.ivQuietModeSwitch,
            getNotificationActionReceiverPendingIntent()
        )
        if (ABTestManager.closeNotificationAfterKill){
            collapsedView.setViewVisibility(R.id.ivCloseNotification,View.VISIBLE)
        }
        collapsedView.setOnClickPendingIntent(
            R.id.ivCloseNotification,
            getNotificationActionChangeQuitStateIntent(true)
        )
        collapsedView.setOnClickPendingIntent(
            R.id.tvStay,
            getNotificationActionChangeQuitStateIntent(false)
        )
        collapsedView.setOnClickPendingIntent(
            R.id.tvQuit,
            getNotificationActionCloseNotificationIntent()
        )

        builder.setCustomContentView(collapsedView)
        builder.setStyle(NotificationCompat.DecoratedCustomViewStyle())
        builder.setOnlyAlertOnce(true)
        builder.setGroup("wtForeground")
        builder.setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
        builder.setShowWhen(false)
        builder.setSubText(R.string.activity.asString())
        defaultNotificationWidgetDND = NotificationWidgetDND(builder.build(), collapsedView)
        return defaultNotificationWidgetDND!!
    }

    private fun getNotificationActionReceiverPendingIntent(): PendingIntent {
        val context = appContext
        val intent = Intent(context, NotificationActionReceiver::class.java)
        intent.setAction(ACTION_SWITCH_CHANGED)
        intent.putExtra(
            NotificationActionReceiver.FROM_SROUCE_TYPE,
            NotificationActionReceiver.TYPE_FOREGROUND_NOTIFICATION_AUTO_PLAY
        )
        val flag = if (Build.VERSION.SDK_INT >= 23) {
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        } else {
            PendingIntent.FLAG_UPDATE_CURRENT
        }
        return PendingIntent.getBroadcast(context, 0, intent, flag)
    }

    private fun getNotificationActionCloseNotificationIntent(): PendingIntent {
        val context = appContext
        val intent = Intent(context, NotificationActionReceiver::class.java)
        intent.setAction(ACTION_CLOSE_NOTIFICATION)
        val flag = if (Build.VERSION.SDK_INT >= 23) {
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        } else {
            PendingIntent.FLAG_UPDATE_CURRENT
        }
        return PendingIntent.getBroadcast(context, 0, intent, flag)
    }

    private fun getNotificationActionChangeQuitStateIntent(state:Boolean): PendingIntent {
        val context = appContext
        val intent = Intent(context, NotificationActionReceiver::class.java)
        intent.setAction(ACTION_QUIT_STATE_CHANGE)
        intent.putExtra(
            NotificationActionReceiver.QUIT_STATE,
            state
        )
        val flag = if (Build.VERSION.SDK_INT >= 23) {
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        } else {
            PendingIntent.FLAG_UPDATE_CURRENT
        }
        return PendingIntent.getBroadcast(context, if (!state) 1 else 2, intent, flag)
    }


    private fun getBaseNotificationBuilder(): NotificationCompat.Builder {
        val pendingIntent = generateChatPendingIntent(R.id.rlRoot, "", clickNotificationSpot = 2)
        val channelId = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannelUtils.getWalkieTalkieForegroundChannel()
            channel?.id ?: CommonConstant.NOTIFICATION_CHANNEL_ID_WT_FOREGROUND
        } else {
            CommonConstant.NOTIFICATION_CHANNEL_ID_WT_FOREGROUND
        }
        val builder = NotificationUtil.createBaseBuilder(
            appContext,
            channelId,
            pendingIntent,
            R.mipmap.common_ic_notification_big
        )
        builder.color = Color.BLACK
        return builder
    }

}