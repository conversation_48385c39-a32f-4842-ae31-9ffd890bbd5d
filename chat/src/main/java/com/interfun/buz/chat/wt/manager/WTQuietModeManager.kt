@file:OptIn(DelicateCoroutinesApi::class)

package com.interfun.buz.chat.wt.manager

import androidx.annotation.IntDef
import com.interfun.buz.base.ktx.*
import com.interfun.buz.chat.wt.utils.WTTracker
import com.interfun.buz.common.constants.CommonMMKV
import com.interfun.buz.common.ktx.CODE_NET_ERROR
import com.interfun.buz.common.ktx.CODE_NET_UNAVAILABLE
import com.interfun.buz.common.ktx.isSuccess
import com.interfun.buz.common.manager.UserInfoReportManager
import com.interfun.buz.common.manager.userLifecycleScope
import com.interfun.buz.user.di.BizUserSingletonEntryPoint
import com.interfun.buz.user.entity.QuiteModeFromType
import com.interfun.buz.user.entity.TimingCycle
import com.lizhi.component.basetool.common.NetStateWatcher
import dagger.hilt.android.EntryPointAccessors
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.runBlocking
import kotlin.annotation.AnnotationRetention.SOURCE

/**
 * <AUTHOR>
 * @date 2023/4/4
 * @desc
 */
@Deprecated(message = "Use UserSettingRepository instead")
object WTQuietModeManager {

    const val TAG = "QuietModeManager"
    private const val MAX_FAIL_COUNT = 10

    const val SET_QUIET_MODE_FROM_DEFAULT = 0
    const val SET_QUIET_MODE_FROM_HOME_SWITCH = 1 // 首页切换静音按钮
    const val SET_QUIET_MODE_FROM_TIMING = 2 // 定时静音模式
    const val SET_QUIET_MODE_FROM_SETTING_PAGE = 3 // 设置页面切换静音按钮

    @IntDef(
        SET_QUIET_MODE_FROM_DEFAULT,
        SET_QUIET_MODE_FROM_HOME_SWITCH,
        SET_QUIET_MODE_FROM_TIMING,
        SET_QUIET_MODE_FROM_SETTING_PAGE
    )
    @Retention(SOURCE)
    annotation class SetQuietModeFromDef

    val userSettingRepository
        get() = EntryPointAccessors.fromApplication<BizUserSingletonEntryPoint>(
            appContext
        ).userSettingRepository()

    val isQuietModeEnable get() = runBlocking {
        userSettingRepository.getQuiteMode().firstOrNull()?.enable ?: false
    }

    val isInDND get() = runBlocking {
        userSettingRepository.inSystemDndFlow.firstOrNull() == true
    }

    val quietModeFlow get() = userSettingRepository.getQuiteMode().map { it.enable }

    val quietModeFlowFrom get() = userSettingRepository.getQuiteMode()

    private var failCount = 0


    private val netWatcher: OneParamCallback<Boolean> = { isConnected ->
        if (isConnected) {
            log(TAG, "network isConnected")
            // In case network is not really connected when clicking to turn on the network.
            delayInMainThread(1000) {
                requestReport()
            }
        }
    }

    fun setQuietModeEnable(
        isEnable: Boolean,
        @SetQuietModeFromDef from: Int = SET_QUIET_MODE_FROM_DEFAULT,
        startHour: Int = 0,
        startMinute: Int = 0,
        endHour: Int = 0,
        endMinute: Int = 0,
        timingCycle: TimingCycle = TimingCycle.Daily
    ) {
        logInfo(TAG, "isQuietModeEnable=${isEnable}")
        //CommonMMKV.isQuietModeEnable = isEnable
        //CommonMMKV.hadChangedQuietMode = true
        //WTQuietModeChangeEvent.post()
//        GlobalScope.launch {
//            //quietModeFlow.emit(isEnable)
//            quietModeFlowFrom.emit(Pair(isEnable, from))
//        }
        if (isEnable) {
            WTMessageManager.clearAll()
        }
        reportUserQuietMode()

        when (from) {
            SET_QUIET_MODE_FROM_DEFAULT,
            SET_QUIET_MODE_FROM_HOME_SWITCH,
            SET_QUIET_MODE_FROM_SETTING_PAGE -> {
                userSettingRepository.enableQuiteMode(isEnable, QuiteModeFromType.Manual)
            }
            SET_QUIET_MODE_FROM_TIMING -> {
                userSettingRepository.enableTimingQuiteMode(
                    enable = isEnable,
                    startHour = startHour,
                    startMinute = startMinute,
                    endHour = endHour,
                    endMinute = endMinute,
                    timingCycle = timingCycle
                )
            }
        }
    }

    fun reportUserQuietMode(isStartUp: Boolean = false) {
        if (!NetStateWatcher.isConnected) {
            log(TAG, "reportUserQuietMode isConnected = false")
            failCount = 0
            NetStateWatcher.removeNetStateWatcher(netWatcher)
            NetStateWatcher.addNetStateWatcher(netWatcher)
        } else {
            requestReport(isStartUp)
        }
    }

    private fun requestReport(isStartUp: Boolean = false) {
        NetStateWatcher.removeNetStateWatcher(netWatcher)
        UserInfoReportManager.reportUserInfo { rCode ->
            WTTracker.postReportQuietModeResult(
                isQuietModeEnable, rCode.isSuccess, isStartUp = isStartUp, isInDND = isInDND
            )
            if (!rCode.isSuccess) {
                handleRequestFailed(rCode)
            }
        }
    }

    private fun handleRequestFailed(rCode: Int) {
        if (failCount >= MAX_FAIL_COUNT) return
        userLifecycleScope?.launchIO {
            if (rCode == CODE_NET_ERROR) {
                // 弱网失败, 3秒后触发重试
                delay(3 * 1000)
                log(TAG, "retry when low quality network, failCount: $failCount")
                requestReport()
            } else if (rCode == CODE_NET_UNAVAILABLE) {
                withMainContext {
                    failCount = 0
                    NetStateWatcher.removeNetStateWatcher(netWatcher)
                    NetStateWatcher.addNetStateWatcher(netWatcher)
                }
            }
        }
        failCount++
    }

    fun login() {
        logInfo(TAG, "login")
//        (userLifecycleScope ?: GlobalScope).launchIO {
//            quietModeFlow.emit(isQuietModeEnable)
//        }
//
//        userLifecycleScope?.launch {
//            DNDManager.isInDNDFlow.collect {
//                setQuietModelStateFromDND()
//            }
//        }


    }

    fun logout() {
        logInfo(TAG, "logout")
    }

    // 是否绑定DND
//    private fun hasBindDND(): Boolean {
//        return CommonMMKV.settingSyncDND
//    }

    // 获取用户主动操作保存的QuietModel状态
    private fun getLatestCacheQuietModeState() : Boolean {
        return CommonMMKV.cacheLatestQuietModeByUserSwitch
    }

    fun cacheLatestQuietModeByUserSwitch(state: Boolean) {
        logInfo(TAG,"cacheLatestQuietModeByUserSwitch: $state")
        CommonMMKV.cacheLatestQuietModeByUserSwitch = state
    }
    fun clearCacheLatestQuietModeByUserSwitch(state: Boolean) {
        logInfo(TAG,"clearLatestQuietModeByUserSwitch: $state")
        CommonMMKV.cacheLatestQuietModeByUserSwitch = false
    }

    // 跟进绑定DND开关（我的设置页面->消息设置->DND开关），根据DND设置静音模式
//    fun setQuietModelStateFromDND() {
//        if (hasBindDND()) {
//            logInfo(TAG, "hasBindDND: true")
//            if (DNDManager.isInDND) {
//                if (!isQuietModeEnable) {
//                    setQuietModeEnable(true, SET_QUIET_MODE_FROM_DEFAULT)
//                }
//            } else {
//                val cacheLatestQuietMode = getLatestCacheQuietModeState()
//                if (cacheLatestQuietMode != isQuietModeEnable) {
//                    setQuietModeEnable(cacheLatestQuietMode,SET_QUIET_MODE_FROM_DEFAULT)
//                }
//            }
//        } else {
//            logInfo(TAG, "hasBindDND: false")
//        }
//    }

    // 定时静音模式
    fun setQuiteModeInTime(
        isEnable: Boolean, startHour: Int,
        startMinute: Int,
        endHour: Int,
        endMinute: Int, timingCycle: TimingCycle
    ) {
        setQuietModeEnable(
            isEnable = isEnable,
            from = SET_QUIET_MODE_FROM_TIMING,
            startHour = startHour,
            startMinute = startMinute,
            endHour = endHour,
            endMinute = endMinute,
            timingCycle = timingCycle
        )
    }

}