package com.interfun.buz.chat.wt.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.interfun.buz.base.ktx.emitInScope
import com.interfun.buz.base.ktx.notEmpty
import com.interfun.buz.chat.wt.entity.*
import com.interfun.buz.chat.wt.manager.MessageState
import com.interfun.buz.chat.wt.service.ForegroundNotificationArgs
import com.interfun.buz.chat.wt.service.NotificationBaseInfo
import com.interfun.buz.chat.wt.service.NotificationGroupInfo
import com.interfun.buz.chat.wt.service.NotificationUserInfo
import com.interfun.buz.common.manager.MuteInfoManager
import com.interfun.buz.common.manager.cache.group.GroupInfoCacheManager
import com.interfun.buz.common.manager.cache.user.UserRelationCacheManager
import com.interfun.buz.im.ktx.userId
import com.interfun.buz.im.message.WTVoiceEmojiMsg
import com.interfun.buz.user.repository.UserSettingRepository
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import com.lizhi.im5.sdk.message.IMessage
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import okhttp3.internal.toLongOrDefault
import kotlin.math.max

class ForegroundServiceViewModel(private val userSettingRepository: UserSettingRepository) : ViewModel() {
    companion object {
        const val MAX_RECENT_SIZE = 4
    }

    val notificationArgsFlow = MutableStateFlow<ForegroundNotificationArgs?>(null)
    val argsValue get() = notificationArgsFlow.value
    private val argsMutex = Mutex()

    val quietModeFlow = userSettingRepository.getQuiteMode().map { it.enable }
        .stateIn(viewModelScope, SharingStarted.WhileSubscribed(5000L),false)

    val isQuietModeEnable
        get() = runBlocking {
            userSettingRepository.getQuiteMode().map { it.enable }.firstOrNull() == true
        }

    val isInDND
        get() = runBlocking {
            userSettingRepository.inSystemDndFlow.firstOrNull() == true
        }

    // !WTQuietModeManager.isQuietModeEnable && DNDManager.isInDND
    val canUpdateSpeakingInfoFlow get() = !isQuietModeEnable && isInDND


    fun updateSpeakingInfo(speakingInfo: Pair<RealTimeMessage?, MessageState>) {
        viewModelScope.launch(Dispatchers.IO) {
            if (canUpdateSpeakingInfoFlow) {
                val playingChat = playingInfoToNotificationInfo(speakingInfo)
                if (isActive) {
                    updateArgs(playingChat)
                }
            }
        }
    }

    fun updateMuteStatus() {
        viewModelScope.launch(Dispatchers.IO) {
            argsMutex.withLock {
                notificationArgsFlow.value?.recentChats?.let { list ->
                    if (list.notEmpty()) {
                        val newList = list.filterNot {
                            if (it.isUser) {
                                MuteInfoManager.getUserMuteInfo(
                                    it.targetId,
                                    "LiveActivity"
                                ).isMuteMessages
                            } else if (it.isGroup) {
                                MuteInfoManager.getGroupMuteInfo(
                                    it.targetId,
                                    "LiveActivity"
                                ).isMuteMessages
                            } else {
                                false
                            }
                        }
                        if (newList.size != list.size) {
                            notificationArgsFlow.emit(ForegroundNotificationArgs(newList))
                        }
                    }
                }
            }
        }
    }

    fun clearSpeakingInfo(){
        notificationArgsFlow.emitInScope(viewModelScope, null)
    }

    fun startIntervalUpdate() {
        viewModelScope.launch(Dispatchers.IO) {
            while (isActive) {
                updateArgs(null)
                delay(60 * 1000)
            }
        }
    }

    private fun updateArgs(updateInfo: NotificationBaseInfo? = null) {
        viewModelScope.launch {
            argsMutex.withLock {
                val originArgs = notificationArgsFlow.value
                val originRecentChats = originArgs?.recentChats
                val size = max(MAX_RECENT_SIZE, (originRecentChats?.size ?: 0) + 1)
                val finalRecentChats = ArrayList<NotificationBaseInfo>(size)
                updateInfo?.let { finalRecentChats.add(updateInfo) }
                originRecentChats?.forEach {
                    if (it.targetId != updateInfo?.targetId){
                        it.isSpeaking = false
                        finalRecentChats.add(it)
                    }
                }
                notificationArgsFlow.emit(
                    ForegroundNotificationArgs(finalRecentChats)
                )
            }
        }
    }

    private suspend fun playingInfoToNotificationInfo(playingInfo: Pair<RealTimeMessage?, MessageState>): NotificationBaseInfo? {
        val (msg, state) = playingInfo
        if (state == MessageState.PLAYING && msg != null) {
            return when(msg.conversationType()){
                IM5ConversationType.GROUP -> convToGroupInfo(msg)?.apply { isSpeaking = true }
                IM5ConversationType.PRIVATE -> convToUserInfo(msg)?.apply { isSpeaking = true }
                else -> null
            }
        }
        return null
    }

    private suspend fun convToUserInfo(msg: RealTimeMessage): NotificationUserInfo? {
        val targetId = msg.getConversationId()?.toLongOrDefault(0) ?: return null
        if (targetId == 0L) {
            return null
        }
        val userInfo = UserRelationCacheManager.getUserRelationInfoByUidSync(targetId)
        val voiceMoji: String? = when (msg) {
            is IMPushMessage -> {
                ((msg.msg as? IMessage)?.content as? WTVoiceEmojiMsg)?.emojiIcon
            }
            else -> {
                null
            }
        }
        val lastConvTime = if (msg is IMPushMessage){
            msg.message.createTime
        } else{
            null
        }
        return if (userInfo == null) null else NotificationUserInfo(
            userInfo,
            voiceMoji,
            lastConvTime
        )
    }

    private suspend fun convToGroupInfo(msg: RealTimeMessage): NotificationGroupInfo? {
        val targetId = msg.getConversationId()?.toLongOrDefault(0) ?: return null
        if (targetId == 0L) {
            return null
        }
        return convToGroupInfo(targetId, msg)
    }

    private suspend fun convToGroupInfo(convId: Long, msg: RealTimeMessage?): NotificationGroupInfo? {
        val groupInfoDeferred = coroutineScope {
            async { GroupInfoCacheManager.getGroupInfoBeanByIdSync(convId) }
        }
        var lastMsgUserId: Long? = null
        var lastConvTime: Long? = null
        var voiceMoji:String? = null
        if (msg is IMPushMessage) {
            lastMsgUserId = msg.message.userId
            lastConvTime = msg.message.createTime
            voiceMoji = ((msg.msg as? IMessage)?.content as? WTVoiceEmojiMsg)?.emojiIcon
        }
        val lastMsgUserDeferred = coroutineScope {
            async {
                if (lastMsgUserId == null)
                    null
                else
                    UserRelationCacheManager.getUserRelationInfoByUidSync(lastMsgUserId)
            }
        }
        val groupInfo = groupInfoDeferred.await()
        return if (groupInfo == null) {
            return null
        } else {
            NotificationGroupInfo(groupInfo, lastMsgUserDeferred.await(),voiceMoji, lastConvTime)
        }
    }
}