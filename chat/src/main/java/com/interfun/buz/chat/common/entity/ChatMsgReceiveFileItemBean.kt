package com.interfun.buz.chat.common.entity

import com.lizhi.im5.sdk.message.IMessage

class ChatMsgReceiveFileItemBean(
    override var msg: IMessage,
    override val msgType: ChatMsgType = ChatMsgType.File,
    private var remainingBytes: Long = 0L
) : ChatMsgReceiveMediaItemBean() {

    companion object {
        private const val TAG = "ChatMsgReceiveFileItemBean"
    }

    override fun getRemainingBytes(): Long {
        return remainingBytes
    }

    override fun setRemainingBytes(bytes: Long) {
        remainingBytes = bytes
    }
}