package com.interfun.buz.chat.common.view.block

import android.view.ViewGroup
import androidx.core.view.children
import androidx.core.view.isEmpty
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import com.interfun.buz.base.ktx.collectIn
import com.interfun.buz.base.ktx.collectLatestIn
import com.interfun.buz.chat.R
import com.interfun.buz.chat.common.entity.BottomPanelContentType
import com.interfun.buz.chat.common.viewmodel.ChatBottomMenuPanelViewModel
import com.interfun.buz.chat.common.viewmodel.ChatInputMentionViewModel
import com.interfun.buz.chat.common.viewmodel.ChatMsgViewModelNew
import com.interfun.buz.chat.databinding.ChatFragmentMsgListBinding
import com.interfun.buz.chat.wt.entity.IMPushMessage
import com.interfun.buz.chat.wt.manager.LMPlayerState.PLAY_LM_MSG
import com.interfun.buz.chat.wt.manager.LMPlayerState.PLAY_WT_MSG
import com.interfun.buz.chat.wt.manager.MessageState.PLAYING
import com.interfun.buz.chat.wt.manager.WTLeaveMsgPlayerManager
import com.interfun.buz.chat.wt.manager.WTMessageManager
import com.interfun.buz.common.bean.HomeWTItemType
import com.interfun.buz.common.bean.chat.MentionedUser
import com.interfun.buz.common.ktx.toastSolidWarning
import com.interfun.buz.core.widget_record.ui.VoicePreviewAction.CancelAction
import com.interfun.buz.core.widget_record.ui.VoicePreviewAction.SendAction
import com.interfun.buz.domain.record.block.BaseRecordBlock
import com.interfun.buz.domain.record.ui.VoiceFilterIntroduceDialog
import com.interfun.buz.domain.record.viewmodel.VadRecordSegment
import com.interfun.buz.domain.record.viewmodel.VoicePreviewViewModel
import com.interfun.buz.im.entity.IMSendFrom
import com.interfun.buz.im.entity.IMSendFrom.CHAT_LIST
import com.interfun.buz.im.ktx.isVoiceMojiMessage
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map

/**
 * <AUTHOR>
 * @date 2025/5/14
 * @desc
 */
class ChatRecordBlock(
    private val fragment: Fragment,
    private val targetId: Long,
    private val convType: IM5ConversationType,
    binding: ChatFragmentMsgListBinding,
    chatMsgViewModelClass: Class<out ChatMsgViewModelNew>,
    override val pageType: IMSendFrom = CHAT_LIST
) : BaseRecordBlock<ChatFragmentMsgListBinding>("ChatRecordBlock", fragment, binding) {

    private val chatMsgViewModel by lazy { ViewModelProvider(fragment)[chatMsgViewModelClass] }
    private val inputMentionViewModel by fragment.viewModels<ChatInputMentionViewModel>()
    private val chatBottomMenuPanelViewModel by fragment.viewModels<ChatBottomMenuPanelViewModel>()
    override val replyId get() = chatMsgViewModel.replyMsgStateFlow.value?.msgId

    override fun getTargetId() = targetId

    override fun getConvType() = convType

    override fun initData() {
        super.initData()
        initTarget()
        combine(
            voiceFilterViewModel.isVoiceFilterModeStateFlow,
            chatBottomMenuPanelViewModel.panelState.map { it == BottomPanelContentType.VOICE_RECORD_PANEL }
        ) { isInVoiceFilterMode, isInRecordPanel ->
            return@combine isInVoiceFilterMode && isInRecordPanel
        }.collectLatestIn(fragment.viewLifecycleOwner) {
            if (it) {
                VoiceFilterIntroduceDialog.tryToShowDialog(fragment.childFragmentManager)
                return@collectLatestIn
            }
        }
        recordViewModel.isRecordingFlow.collectIn(fragment.viewLifecycleOwner) { isRecording ->
            binding.replyView.animateAlphaWhenRecording(isRecording)
            binding.llWaitingAIResponse.animateAlphaWhenRecording(isRecording)
            binding.clLinkPreviewView.animateAlphaWhenRecording(isRecording)
            binding.rvHorizontalAiList.animateAlphaWhenRecording(isRecording) {
                !binding.rvHorizontalAiList.isEmpty()
            }
        }
    }

    private fun initTarget(){
        val itemType = if (convType == IM5ConversationType.GROUP) HomeWTItemType.Group else HomeWTItemType.User
        recordViewModel.updateCurrentTarget(targetId to itemType)
    }

    override fun onStartRecord(recordingTargetId: Long?) {
        super.onStartRecord(recordingTargetId)
        inputMentionViewModel.dismissMentionPanel()
    }

    override fun checkRecordEnable(): Boolean {
        // 开始录音前的一系列判断，注意这里并不是全部
        // 更多判断已经在RecordVoiceViewModel.checkEnableAndToastWhenDisable中检查过了
        val (msg, state) = WTMessageManager.messageFlow.value
        if (msg != null && state == PLAYING) {
            if (msg is IMPushMessage && msg.message.isVoiceMojiMessage.not()) {
                toastRecordDisable()
                return false
            } else if (msg is IMPushMessage) {
                //voice emoji,skip
                WTMessageManager.pause()
            }
        }

        val chatListPlayingState = WTLeaveMsgPlayerManager.playStateFlow.value.first
        val chatListPlayingMsg = WTLeaveMsgPlayerManager.playStateFlow.value.second
        if (chatListPlayingState == PLAY_WT_MSG) {
            if (chatListPlayingMsg?.isVoiceMojiMessage != true) {
                toastRecordDisable()
                return false
            } else {
                //voice emoji,skip
                WTLeaveMsgPlayerManager.stop()
            }
        } else if (chatListPlayingState == PLAY_LM_MSG) {
            WTLeaveMsgPlayerManager.stop()
        }

        return true
    }

    override fun onSegmentReceived(
        segment: VadRecordSegment,
        isPreview: Boolean,
        isPreviewNotAutoPlay: Boolean
    ) {
        super.onSegmentReceived(segment, isPreview, isPreviewNotAutoPlay)
        chatMsgViewModel.cancelReplyForce()
    }

    override fun getMentionedUsers(): List<MentionedUser>? {
        val mentionedUser = inputMentionViewModel.currentMentioningBotMemberFlow.value
        return if (mentionedUser == null || mentionedUser.isSelected.not()) {
            null
        } else {
            arrayListOf(
                MentionedUser(
                    mentionedUser.mentionedMemberInfo.userId,
                    mentionedUser.mentionedMemberInfo.userName ?: ""
                )
            )
        }
    }

    private fun toastRecordDisable() {
        toastSolidWarning(R.string.message_auto_laying_try_again_later)
    }

    private fun ViewGroup.animateAlphaWhenRecording(
        isRecording: Boolean,
        needHandleLambda: () -> Boolean = { this.isVisible }
    ) {
        if (!needHandleLambda()) return
        if (isRecording) {
            this.children.forEach {
                it.animate().alpha(0.3f).start()
            }
        } else {
            this.children.forEach {
                it.animate().alpha(1f).start()
            }
        }
    }
}