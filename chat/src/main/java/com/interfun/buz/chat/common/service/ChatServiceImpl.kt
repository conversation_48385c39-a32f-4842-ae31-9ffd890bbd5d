package com.interfun.buz.chat.common.service

import android.content.Context
import android.content.Intent
import android.os.Parcelable
import androidx.fragment.app.Fragment
import androidx.lifecycle.LiveData
import com.alibaba.android.arouter.facade.annotation.Route
import com.buz.idl.common.bean.PopWindow
import com.interfun.buz.base.ktx.*
import com.interfun.buz.biz.center.voicemoji.manager.VoiceMojiFrequencyManager
import com.interfun.buz.chat.DND.DNDManager
import com.interfun.buz.chat.R
import com.interfun.buz.chat.ai.invite.InviteBotToGroupDialog
import com.interfun.buz.chat.common.manager.*
import com.interfun.buz.chat.common.utils.ChatTracker
import com.interfun.buz.chat.common.utils.ReceiveSharedHelper
import com.interfun.buz.chat.common.view.activity.ChatHomeActivity
import com.interfun.buz.chat.group.view.activity.GroupChatActivity
import com.interfun.buz.chat.group.view.dialog.GroupInfoDialog
import com.interfun.buz.chat.media.view.block.ChatMediaPlayConflictBlock
import com.interfun.buz.chat.privy.view.activity.PrivateChatActivity
import com.interfun.buz.chat.voicepanel.view.dialog.VoicemojiCollectBlindBoxIntroDialog
import com.interfun.buz.chat.wt.block.WTPopMessageBlock
import com.interfun.buz.chat.wt.entity.IMPushMessage
import com.interfun.buz.chat.wt.manager.*
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.base.BaseActivity
import com.interfun.buz.common.base.BaseFragment
import com.interfun.buz.common.base.BaseManualBlock
import com.interfun.buz.common.bean.chat.ChatJumpType
import com.interfun.buz.common.bean.chat.GroupChatJumpInfo
import com.interfun.buz.common.bean.chat.PrivateChatJumpInfo
import com.interfun.buz.common.constants.PATH_CHAT_SERVICE
import com.interfun.buz.common.constants.RouterParamValues
import com.interfun.buz.common.constants.SOURCE_PUSH
import com.interfun.buz.common.constants.SOURCE_WIDGET
import com.interfun.buz.common.database.entity.UserRelationInfo
import com.interfun.buz.common.manager.AlertSoundManager
import com.interfun.buz.common.manager.OnlineChatRingtoneManager
import com.interfun.buz.common.service.ChatService
import com.interfun.buz.common.service.GlobalPlayingMsgState
import com.interfun.buz.common.service.GlobalPlayingState
import com.interfun.buz.common.service.IWTFloatingViewManager
import com.interfun.buz.common.widget.dialog.BaseBottomSheetDialogFragment
import com.interfun.buz.common.widget.dialog.BasePriorityBottomSheetDialogFragment
import com.interfun.buz.domain.record.helper.RecordStatusHelper
import com.interfun.buz.onair.standard.IGlobalOnAirController
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.map

@Route(path = PATH_CHAT_SERVICE)
class ChatServiceImpl : ChatService {
    val chatPreviewVF = MutableStateFlow(false)
    override fun chatPreviewVF(): StateFlow<Boolean> {
        return chatPreviewVF
    }

    override fun setChatPreviewVF(isPreview: Boolean) {
        chatPreviewVF.value = isPreview
    }

    override fun createHomeIntent(
        context: Context,
        router: String?
    ): Intent {
        return ChatHomeActivity.createIntent(context, router)
    }

    override fun getPrivateChatJumpInfo(
        userId: Long,
        source: String,
        serMsgId: String?,
        reactionOpUserId: String?,
        msgId: Long?,
        openVoiceFilter:Boolean,
        voiceFilterId:Long?
    ): Parcelable {
        val jumpType = when (source) {
            RouterParamValues.Chat.SOURCE_PUSH -> ChatJumpType.Push
            RouterParamValues.Chat.SOURCE_WIDGET -> ChatJumpType.Widget
            else -> ChatJumpType.Unknown
        }
        return PrivateChatJumpInfo(
            userId,
            jumpType,
            serMsgId = serMsgId,
            reactionOpUserId = reactionOpUserId,
            msgId = msgId,
            openVoiceFilter = openVoiceFilter,
            voiceFilterId = voiceFilterId
        )
    }

    override fun getGroupChatJumpInfo(
        groupId: Long,
        source: String,
        serMsgId: String?,
        addressUserInfo: UserRelationInfo?,
        reactionOpUserId: String?,
        msgId: Long?,
    ): Parcelable {
        val jumpType = when (source) {
            RouterParamValues.Chat.SOURCE_PUSH -> ChatJumpType.Push
            RouterParamValues.Chat.SOURCE_WIDGET -> ChatJumpType.Widget
            else -> ChatJumpType.Unknown
        }
        return GroupChatJumpInfo(
            groupId,
            jumpType,
            null,
            null,
            serMsgId = serMsgId,
            addressUserInfo = addressUserInfo,
            reactionOpUserId = reactionOpUserId,
            msgId = msgId
        )
    }

    override fun getConversationAutoTranslateSwitch(conversationId: Long): Boolean {
        return TranslationMessageManager.getConversationAutoTranslateSwitch(conversationId)
    }

    override fun enableTranslationFun(): Boolean {
        return TranslationMessageManager.openTranslationFunctionality()
    }

    // Close group chat
    override fun closeGroupChat(groupChatFragment: Fragment?) {
        groupChatFragment?.finishActivity()
    }

    override fun getHomeAddressUserInfo(groupId: Long): UserRelationInfo? {
        return WTStatusManager.getHomeAddressUserInfo(groupId)
    }

    override fun isChatHomeActivity(obj: Any?): Boolean {
        return obj is ChatHomeActivity
    }

    override fun isChatActivity(obj: Any?): Boolean {
        return obj is PrivateChatActivity || obj is GroupChatActivity
    }

    override fun setIsPlayOnlineChatRingtone(play: Boolean, targetId: Long?, isGroup: Boolean?) {
        if (play) {
            OnlineChatRingtoneManager.playIncomingSoundEffect(targetId ?: 0L, isGroup ?: false)
        } else {
            OnlineChatRingtoneManager.stopIncomingSoundEffect()
        }
    }

    override fun onLogin() {
        IMEventTrackManager.login()
        WTMessageManager.login()
        LastReadMsgManager.get().login()
        VoiceMojiFrequencyManager.login()
        SmartTransManager.login()
        RecallMessageManager.login()
        WTQuietModeManager.login()
        DriveModelManager.login()
        TranslationMessageManager.login()
        AlertSoundManager.login()
    }

    override fun onLoginBeforeIM() {
    }

    override fun onLogout() {
        IMEventTrackManager.logout()
        WTMessageManager.logout()
        VoiceMojiFrequencyManager.logout()
        SmartTransManager.logout()
        RecallMessageManager.logout()
        WTQuietModeManager.logout()
        routerServices<IGlobalOnAirController>().value?.logout()
        DriveModelManager.logout()
        TranslationMessageManager.logout()
        AlertSoundManager.logout()
    }

    override fun createWTMessageBlock(activity: BaseActivity): BaseManualBlock {
        return WTPopMessageBlock(activity)
//        return WTMessageBlock(activity)
    }

    override fun isWTOnlineStatus(): Boolean {
        return WTStatusManager.isOn
    }

    override fun getWtSwitchFlow(): StateFlow<Boolean> {
        return WTStatusManager.switchFlow
    }

    override fun checkIsWTOnlineStatus(
        doWhenWTOnline: DefaultCallback,
        doWhenWTOffline: DefaultCallback
    ) {
        if (WTStatusManager.isOn) {
            doWhenWTOnline.invoke()
        } else {
            doWhenWTOffline.invoke()
        }
    }

    override fun requestChangeSwitchStatus(turnOn: Boolean) {
        WTStatusManager.requestChangeSwitchStatus(turnOn)
    }

    override fun isWTRecording(): Boolean {
        return WTStatusManager.isOn && RecordStatusHelper.isRecording
    }

    override fun isRecordBtnPressing() = RecordStatusHelper.isPressing

    override fun getGroupInfoDialog(groupId: Long, traceSource: Int): BaseBottomSheetDialogFragment {
        return GroupInfoDialog.newInstance(groupId,traceSource)
    }

    override fun getInviteBotToGroupDialog(botUserId: Long): BaseBottomSheetDialogFragment {
        return InviteBotToGroupDialog.newInstance(botUserId)
    }

    override fun createFlowViewManager(): IWTFloatingViewManager {
        return WTFloatingViewManager()
    }

    override fun setQuietModeEnable(isEnable: Boolean) {
        WTQuietModeManager.setQuietModeEnable(isEnable)
    }

    override fun isQuietModeEnable(): Boolean {
        return WTQuietModeManager.isQuietModeEnable
    }

    override fun muteMessagesByTargetId(targetId: Long) {
        WTMessageManager.muteMessagesByTargetId(targetId)
    }

    @Deprecated("为了兼容旧逻辑，请不要使用")
    override fun getHomeListSize(): Int {
        return WTStatusManager.homeListSize
    }

    override fun getInviteToGroupFragment(userId: Long): BaseBottomSheetDialogFragment {
        return InviteBotToGroupDialog.newInstance(userId)
    }

    override fun getIsRecordingFlow(): StateFlow<Boolean> {
        return RecordStatusHelper.isRecordingFlow
    }

    override fun isPlayingVoiceMsg(): Boolean {
        return WTMessageManager.isPlaying
    }

    override fun stopVoiceMessagePlayback() {
        //这种实现方式不好，新方案已在WTMessageManager监听呼叫和挂断状态
        //WTMessageManager.pause()
        WTLeaveMsgPlayerManager.stop()
    }

    override fun findEnterConversationId(conversationId: String): Boolean {
        return WTMessageManager.findEnterConversationId(conversationId)
    }

    override fun topActivityIsChatOngoing(
        conversationId: String,
        conversationType: IM5ConversationType
    ): Boolean {
        val topIsChatActivity = if (conversationType == IM5ConversationType.GROUP) {
            topActivity is GroupChatActivity
        } else {
            topActivity is PrivateChatActivity
        }
        return topIsChatActivity && findEnterConversationId(conversationId)
    }

    override fun init(context: Context?) {
    }

    override fun getMediaPlayerConflictBlock(fragment: BaseFragment): BaseManualBlock {
        return ChatMediaPlayConflictBlock(fragment)
    }

    override fun isTakingVideo(): Boolean {
        return VideoRecordStateManager.isRecording
    }

    /**
     * 从相册或相机打开的预览页
     **/
    override fun isAlbumPreviewing(): Boolean {
        return VideoRecordStateManager.isPreviewing
    }

    override fun setTakingVideoStatus(isTakingVideo: Boolean) {
        VideoRecordStateManager.isRecording_.value = isTakingVideo
    }

    /**
     * 从相册或相机打开的预览页
     **/
    override fun setAlbumPreviewStatus(isPreview: Boolean) {
        VideoRecordStateManager.isPreviewing_.value = isPreview
    }

    override fun stopRecording() {
        return VideoRecordStateManager.stopRecording()
    }

    override fun getChatVoiceCallMinimizeHeight(context: Context): Int {
        return context.statusBarHeight + R.dimen.real_time_call_minimize_height.asDimensionInt()
    }

    override suspend fun forwardShareIntent(activity: BaseActivity, intent: Intent) {
        ReceiveSharedHelper().forwardShareIntent(activity, intent)
    }

    override fun getCollectVoicemojiBlindBoxDialog(priority: Int): BasePriorityBottomSheetDialogFragment {
        return VoicemojiCollectBlindBoxIntroDialog.newInstance(priority)
    }

    override fun setCollectVoicemojiBlindBoxDialogData(
        dialog: BasePriorityBottomSheetDialogFragment,
        popWindow: PopWindow
    ) {
        if (dialog is VoicemojiCollectBlindBoxIntroDialog) {
            dialog.setBlindBoxData(popWindow)
        }
    }

    override fun onChaHomeContactClick() {
        ChatTracker.onChaHomeContactClick()
    }

    override fun onChaHomeMinePortraitClick() {
        ChatTracker.onChaHomeMinePortraitClick()
    }

    override fun isPlayingChatListMsg(): Boolean {
        return  WTLeaveMsgPlayerManager.playStateFlow.value.first == LMPlayerState.PLAY_LM_MSG
                || WTLeaveMsgPlayerManager.playStateFlow.value.first == LMPlayerState.PLAY_WT_MSG
    }

    override fun getGlobalPlayedMsgFlow(): Flow<GlobalPlayingMsgState> {
        return WTMessageManager.messageFlow.map { (msg, state) ->
            if (msg is IMPushMessage) {
                val imMessage = msg.message
                val newState = when (state) {
                    MessageState.IDLE -> GlobalPlayingState.IDLE
                    MessageState.PLAYING -> GlobalPlayingState.PLAYING
                    MessageState.PAUSE -> GlobalPlayingState.PAUSE
                }
                GlobalPlayingMsgState(imMessage, newState)
            } else {
                GlobalPlayingMsgState(null, GlobalPlayingState.IDLE)
            }
        }
    }

    override fun nowChatListTargetId(): LiveData<Long?> {
        return ChatGlobalInfoRecorder.nowChatListTargetId
    }

    override val isPlayingFlow: Flow<Boolean>
        get() = WTStatusManager.isPlayingFlow

}
