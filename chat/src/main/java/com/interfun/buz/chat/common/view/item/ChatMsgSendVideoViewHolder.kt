package com.interfun.buz.chat.common.view.item

import android.annotation.SuppressLint
import android.view.View
import androidx.media3.common.util.UnstableApi
import androidx.media3.datasource.DataSpec
import com.interfun.buz.base.ktx.gone
import com.interfun.buz.base.ktx.isNotNull
import com.interfun.buz.chat.R
import com.interfun.buz.chat.common.entity.ChatMsgSendMediaItemBean
import com.interfun.buz.chat.common.interfaces.ChatItemCallback
import com.interfun.buz.chat.common.ktx.showImage
import com.interfun.buz.chat.databinding.ChatItemSendVideoBinding
import com.interfun.buz.common.base.BaseActivity
import com.interfun.buz.common.media.MediaLoadingEvent
import com.interfun.buz.media.player.manager.MediaDownloadManager
import com.interfun.buz.media.player.manager.MediaLoadingListener
import com.interfun.buz.media.player.manager.key
import com.lizhi.component.basetool.ntp.NtpTime
import com.lizhi.im5.sdk.message.model.IM5VideoMessage
import com.yibasan.lizhifm.lzlogan.Logz

class ChatMsgSendVideoViewHolder(
    vb: ChatItemSendVideoBinding,
    itemCallback: ChatItemCallback
) : ChatMsgSendMediaViewHolder<ChatItemSendVideoBinding>(vb, itemCallback) {
    private val TAG = "ChatMsgSendVideoViewHolder"
    private var videoLoadingListener: MediaLoadingListener? = null

    @SuppressLint("DefaultLocale")
    override fun initView (item: ChatMsgSendMediaItemBean, binding: ChatItemSendVideoBinding) {
        updateStateUI(item, binding)
    }

    private fun updateStateUI(
        item: ChatMsgSendMediaItemBean,
        binding: ChatItemSendVideoBinding
    ) {
        binding.cpiDownloadProgress.gone()
        binding.tvLeftSize.gone()
        binding.iftvDownload.gone()

        binding.ivImage.transitionName = "share_img_${item.msg.msgId}"
        item.msg.showImage(
            imageView = binding.ivImage,
            placeHolder = R.drawable.chat_item_send_image_default_bg
        )
        val videoMessage = item.msg.content as IM5VideoMessage
        var durationSecond = videoMessage.duration / 1000
        if (durationSecond * 1000 < videoMessage.duration){
            durationSecond += 1
        }
        //at least 1 second
        val useDuration = durationSecond.coerceAtLeast(1L)
        binding.tvDuration.text = String.format("%02d:%02d", useDuration / 60, useDuration % 60)
        updateDownloadState(item, binding)
    }

    override fun getResendIcon(binding: ChatItemSendVideoBinding) = binding.iftvSendFailed

    override fun getSendingLoading(binding: ChatItemSendVideoBinding) = binding.lottieLoading

    override fun getUploadMediaButton(binding: ChatItemSendVideoBinding) = binding.uploadMediaButton

    override fun getAnchorView(binding: ChatItemSendVideoBinding) = binding.clContent

    override fun getUploadMediaStateView(binding: ChatItemSendVideoBinding) = binding.tvProcessing

    override fun getProcessingBackground(binding: ChatItemSendVideoBinding) = binding.vBottomBg

    override fun getUploadClickAreaView(binding: ChatItemSendVideoBinding): View = binding.vUploadClickArea

    override fun checkCache(item: ChatMsgSendMediaItemBean) {}

    override fun getCpiDownloadProgressView(binding: ChatItemSendVideoBinding) =
        binding.cpiDownloadProgress

    override fun getTvLeftSizeView(binding: ChatItemSendVideoBinding) = binding.tvLeftSize

    override fun getIftvDownloadView(binding: ChatItemSendVideoBinding) = binding.iftvDownload

    override fun getVBottomBgView(binding: ChatItemSendVideoBinding) = binding.vDownloadBottomBg


    override fun unregisterLoadingListener() {
        videoLoadingListener?.let {
            MediaDownloadManager.removeMediaLoadingListener(it)
        }
    }


    override fun registerLoadingListener(item: ChatMsgSendMediaItemBean) {
        registerVideoLoadingListener(item, binding)
    }

    private fun registerVideoLoadingListener(
        item: ChatMsgSendMediaItemBean,
        binding: ChatItemSendVideoBinding
    ) {
        // Reset Download State
        updateDownloadState(item, binding)
        val videoMessage = item.msg.content as IM5VideoMessage
        Logz.tag(TAG).d("addMediaLoadingListener msgId: ${item.msg.msgId}")
        videoLoadingListener = @UnstableApi object : MediaLoadingListener {
            private val sampleVideoTime = 200
            private val mediaLastUpdateTime = HashMap<String, Long>()

            fun isSameVideo(url: String): Boolean {
                return url == videoMessage.remoteUrl
            }

            fun notifyReceiveMediaItemDownloadStatusChange(
                dataSpec: DataSpec,
                event: MediaLoadingEvent.Key,
                leftSize: Long? = null
            ) {
                if (!isSameVideo(dataSpec.key())) {
                    return
                }
                item.downloading = when (event) {
                    MediaLoadingEvent.Key.ON_ERROR,
                    MediaLoadingEvent.Key.ON_CANCEL,
                    MediaLoadingEvent.Key.ON_SUCCESS -> false

                    MediaLoadingEvent.Key.ON_START,
                    MediaLoadingEvent.Key.ON_PROGRESS -> true
                }

                if (leftSize.isNotNull()) {
                    item.setRemainingBytes(item.getRemainingBytes().coerceAtMost(leftSize!!))
                }
                if (event == MediaLoadingEvent.Key.ON_SUCCESS) {
                    item.setRemainingBytes(0)
                }

                if (event == MediaLoadingEvent.Key.ON_PROGRESS) {
                    updateDownloadState(item, binding)
                } else if (event == MediaLoadingEvent.Key.ON_SUCCESS && !item.forceHD) {
                    item.forceHD = true
                    updateStateUI(item, binding)
                } else {
                    updateDownloadState(item, binding)
                }
            }

            @UnstableApi
            override fun onInit(dataSpec: DataSpec) {
            }

            @UnstableApi
            override fun onStart(dataSpec: DataSpec) {
                if (!isSameVideo(dataSpec.key())) {
                    return
                }
                Logz.tag(TAG).d("onStart ${dataSpec.key()}")
                notifyReceiveMediaItemDownloadStatusChange(dataSpec, MediaLoadingEvent.Key.ON_START)
            }

            @UnstableApi
            override fun onProgress(dataSpec: DataSpec, cachedSize: Long) {
                if (!isSameVideo(dataSpec.key())) {
                    return
                }
                val now = NtpTime.nowForce()
                val lastUpdateTime = mediaLastUpdateTime[dataSpec.key()] ?: 0L
                if (now - lastUpdateTime <= sampleVideoTime) {
                    return
                }
                mediaLastUpdateTime[dataSpec.key()] = now
                Logz.tag(TAG).d("onProgress $cachedSize ${dataSpec.key()}")

                notifyReceiveMediaItemDownloadStatusChange(
                    dataSpec,
                    MediaLoadingEvent.Key.ON_PROGRESS,
                    videoMessage.totalBytes - cachedSize
                )
            }

            @UnstableApi
            override fun onEnd(dataSpec: DataSpec, cachedSize: Long) {
                if (!isSameVideo(dataSpec.key())) {
                    return
                }
                Logz.tag(TAG).d("onEnd ${dataSpec.key()}")
                val realDownloadSize =
                    MediaDownloadManager.getVideoDownloadSize(videoMessage.remoteUrl)
                if (cachedSize >= videoMessage.totalBytes || realDownloadSize >= videoMessage.totalBytes) {
                    notifyReceiveMediaItemDownloadStatusChange(
                        dataSpec,
                        MediaLoadingEvent.Key.ON_SUCCESS,
                    )
                } else {
                    notifyReceiveMediaItemDownloadStatusChange(
                        dataSpec,
                        MediaLoadingEvent.Key.ON_CANCEL,
                    )
                }

            }
        }
        videoLoadingListener?.let {
            MediaDownloadManager.addMediaLoadingListener(
                (binding.root.context as? BaseActivity),
                it
            )
        }
    }




}