package com.interfun.buz.chat.common.view.block.guidance

import android.view.View
import android.widget.FrameLayout
import androidx.fragment.app.Fragment
import com.interfun.buz.base.ktx.collect
import com.interfun.buz.base.ktx.contentView
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.chat.databinding.ChatFragmentMsgListBinding
import com.interfun.buz.chat.wt.manager.WTQuietModeManager
import com.interfun.buz.common.R
import com.interfun.buz.common.base.binding.BaseBindingBlock
import com.interfun.buz.common.manager.chat.WTGuidanceManager
import com.interfun.buz.common.manager.chat.isStepAutoPlay
import com.interfun.buz.common.utils.CommonTracker
import com.interfun.buz.user.entity.QuiteModeFromType

class ChatGuidanceBlock (
    private val fragment: Fragment,
    binding: ChatFragmentMsgListBinding
) : BaseBindingBlock<ChatFragmentMsgListBinding>(binding) {
    private val TAG = "ChatGuidanceBlock"
    private val autoPlayTooltip by lazy {
        AutoPlayTooltip()
    }

    override fun initData() {
        WTGuidanceManager.guideStepStateFlow.collect(fragment) {step ->
            if (null == step) return@collect
            when{
                step.isStepAutoPlay() -> {
                    showVoiceAutoPlayToolTips()
                }
            }
        }

        WTQuietModeManager.quietModeFlowFrom.collect(fragment.viewLifecycleOwner) { (quietMode, from) ->
            if (from == QuiteModeFromType.Manual && WTGuidanceManager.isStepAutoPlay()) {
                if (autoPlayTooltip.isShowing()) {
                    autoPlayTooltip.dismiss()
                    val businessType = if (quietMode) "turn_on" else "turn_off"
                    CommonTracker.onClickAC2024102102(businessType = businessType)
                }
                WTGuidanceManager.gotoStepReportWillComplete()
            }
        }
    }

    fun onBackPress(): Boolean {
        return AutoPlayTooltip.tooltipsLocation.value == AutoPlayTooltip.LOCATION_CHAT_LIST
    }


    private fun showVoiceAutoPlayToolTips(){
        val parent = fragment.activity?.contentView?.findViewById<View>(R.id.common_container)
        logInfo(TAG,"showFifthStepToolTips: $parent,  isFrameLayout = ${parent is FrameLayout}")

        if (parent !is FrameLayout ) return
        fragment.activity?.let {
            autoPlayTooltip.showToolTipsInChatPage(
                it,
                parent = parent,
                binding.dragLayout
            )
        }
    }
}