package com.interfun.buz.chat.group.view.itemdelegate

import com.interfun.buz.base.ktx.*
import com.interfun.buz.chat.R
import com.interfun.buz.chat.databinding.GroupItemGroupMemberBinding
import com.interfun.buz.chat.group.viewmodel.GroupInfoViewModel.GroupProfileItem.CustomGroupMember
import com.interfun.buz.chat.wt.manager.WTQuietModeManager
import com.interfun.buz.common.base.binding.BaseBindingDelegate
import com.interfun.buz.common.database.entity.chat.GroupUserRole
import com.interfun.buz.common.manager.ABTestManager
import com.interfun.buz.domain.chat.ktx.updateUserState
import com.interfun.buz.social.entity.OnlineState
import com.yibasan.lizhifm.sdk.platformtools.ResUtil

/**
 * @Desc
 * @Author:<EMAIL>
 * @Date: 2022/7/26
 */
class GroupProfileMemberItemView : BaseBindingDelegate<CustomGroupMember, GroupItemGroupMemberBinding>() {
    private val TAG = "GroupMemberItemView"

    override fun onBindViewHolder(
        binding: GroupItemGroupMemberBinding,
        item: CustomGroupMember,
        position: Int
    ) {
        binding.ivPortrait.setPortrait(item.portrait)
        if (item.isSelf) {
            binding.gOnline.visible()
            if (WTQuietModeManager.isQuietModeEnable.not()) {
                binding.roundOnline.setBackgroundResource(R.drawable.common_oval_basic_primary)
            } else {
                binding.roundOnline.setBackgroundResource(R.drawable.common_oval_secondary_purple)
            }
            binding.tvUserName.text = item.userName
        } else {
            if (item.isNormalUser()) {
                updateUserStatus(binding, item)
            }
            updateName(binding, item.userName)
        }

        when (item.role) {
            GroupUserRole.Manager.value -> {
                binding.iftArrow.visible()
                binding.iftArrow.textSize = 14.0f
                binding.iftArrow.text = ResUtil.getString(R.string.manager)
            }

            GroupUserRole.Owner.value -> {
                binding.iftArrow.visible()
                binding.iftArrow.textSize = 14.0f
                binding.iftArrow.text = ResUtil.getString(R.string.owner)
            }

            else -> {
                binding.iftArrow.gone()
            }
        }

        binding.clRoot.setBackgroundResource(item.backgroundRes)
    }

    private fun updateUserStatus(
        binding: GroupItemGroupMemberBinding,
        item: CustomGroupMember
    ) {
        val userStateInfo = item.userStateInfo
        if (ABTestManager.isUserStatePlanA && item.isBigGroup.not()) {
            userStateInfo.updateUserState(binding.tvDesc, item.buzId)
        } else {
            binding.tvDesc.setTextWithAutoGoneIfEmpty(item.buzId)
        }
        if (userStateInfo is OnlineState) {
            binding.gOnline.visible()
            if (userStateInfo is OnlineState.Quiet) {
                binding.roundOnline.setBackgroundResource(R.drawable.common_oval_secondary_purple)
            } else {
                binding.roundOnline.setBackgroundResource(R.drawable.common_oval_basic_primary)
            }
        } else {
            binding.gOnline.gone()
        }

    }

    private fun updateName(binding: GroupItemGroupMemberBinding, name : String) {
        binding.tvUserName.text = name
    }

    override fun onBindViewHolder(
        holder: BindingViewHolder<GroupItemGroupMemberBinding>,
        item: CustomGroupMember,
        payloads: List<Any>
    ) {
        if (payloads.isEmpty()) {
            onBindViewHolder(holder, item)
        } else {
            payloads.forEach {

            }
        }
    }

    override fun useItemClickListener(item: CustomGroupMember): Boolean {
        return true
    }
}