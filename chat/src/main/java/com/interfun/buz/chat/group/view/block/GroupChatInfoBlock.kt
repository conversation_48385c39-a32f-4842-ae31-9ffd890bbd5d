package com.interfun.buz.chat.group.view.block

import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.withResumed
import com.interfun.buz.base.ktx.*
import com.interfun.buz.base.utils.BusUtil
import com.interfun.buz.chat.R
import com.interfun.buz.chat.databinding.ChatFragmentMsgListBinding
import com.interfun.buz.chat.group.view.dialog.GroupInfoDialog
import com.interfun.buz.chat.group.viewmodel.GroupChatViewModel
import com.interfun.buz.common.base.BaseFragment
import com.interfun.buz.common.base.binding.BaseBindingBlock
import com.interfun.buz.common.database.entity.chat.GroupInfoBean
import com.interfun.buz.common.database.entity.chat.GroupUserStatus
import com.interfun.buz.common.eventbus.CreateLpSuccessEvent
import com.interfun.buz.common.eventbus.MuteStatusUpdateEvent
import com.interfun.buz.common.eventbus.group.GroupInfoDidUpdateEvent
import com.interfun.buz.common.ktx.convertGroupInfoBean
import com.interfun.buz.common.ktx.isInGroup
import com.interfun.buz.common.manager.MuteInfoManager
import com.interfun.buz.common.widget.dialog.CommonAlertDialog
import com.interfun.buz.common.widget.toast.BuzToast
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.mapNotNull
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * <AUTHOR>
 * @date 2022/11/27
 * @desc
 */
class GroupChatInfoBlock(
    val fragment: BaseFragment,
    val groupId: Long,
    binding: ChatFragmentMsgListBinding
) : BaseBindingBlock<ChatFragmentMsgListBinding>(binding) {
    companion object {
        val TAG = "GroupChatInfoBlock"
    }

    private val chatViewModel by fragment.fragmentViewModels<GroupChatViewModel>()
    private var groupInfo: GroupInfoBean? = null

    override fun initData() {
        super.initData()
        chatViewModel.groupInfoFlow.collectInScope(fragment.viewLifecycleScope){
            if (it != null){
                val oldGroupInfo = it.toOldGroupInfoBean()
                groupInfo = oldGroupInfo
                updateGroupInfo(oldGroupInfo)
            }
        }
        bindKickOutDialog()

        BusUtil.observe<MuteStatusUpdateEvent>(fragment.viewLifecycleOwner) {
            if (it.targetId == groupInfo?.groupId) {
                fragment.viewLifecycleScope.launch(Dispatchers.IO) {
                    updateMuteStatus(it.targetId)
                }
            }
        }

        BusUtil.observe<CreateLpSuccessEvent>(fragment.viewLifecycleOwner) {data->
            if (data.isGroup && data.target == groupId) {
                fragment.viewLifecycleScope.launch {
                    fragment.lifecycle.withResumed {
                        BuzToast.showTwoLineImgToast(
                            context = fragment.requireContext,
                            imgRes = R.drawable.compose_firworks,
                            title = R.string.live_place_created.asString(),
                            subTitle = R.string.setup_your_live_place_done.asString()
                        )
                    }
                }

            }
        }
        chatViewModel.isGroupOnlineFlow.collectIn(fragment.viewLifecycleOwner) {
            updateGreenDot(it)
        }
        chatViewModel.onlineMemberCountFlow.collectIn(fragment.viewLifecycleOwner){
            updateMemberCount(it)
        }
    }

    private fun bindKickOutDialog(){
        fragment.lifecycleScope.launch {
            chatViewModel.groupInfoFlow.mapNotNull { groupInfo?.userStatus }
                .distinctUntilChanged()
                .collect { status ->
                    if (status == GroupUserStatus.Kicked.value) {
                        val activity = fragment.activity
                        if (activity != null) {
                            CommonAlertDialog(
                                activity,
                                title = R.string.air_are_not_group_member.asString(),
                                positiveText = R.string.ok.asString(),
                                positiveCallback = {
                                    it.dismiss()
                                }
                            ).show()
                        }
                    }
                }
        }
    }

    override fun initView() {
        super.initView()
        binding.clChatListHeader.onClickTitleContent {
            if (groupInfo?.isInGroup() == true){
                groupInfo?.let { openGroupChatProfileDialog(it) }
            } else{
                toast(R.string.chat_be_removed_tip.asString())
            }
        }
    }

    private fun updateGroupInfo(groupInfo: GroupInfoBean?) {
        groupInfo?.apply {
            fragment.viewLifecycleScope.launch(Dispatchers.IO) {
                withContext(Dispatchers.Main) {
                    binding.clChatListHeader.setHeaderName(groupName)
                    binding.clChatListHeader.setGroupPortrait(groupInfo, allowHardware = false)
                    logInfo(TAG, "initObserve: groupInfo = $groupInfo  isInGroup = ${groupInfo.isInGroup()}")
                }
                updateMuteStatus(groupId)
            }
        }
    }

    private suspend fun updateMuteStatus(groupId: Long) {
        MuteInfoManager.getGroupMuteInfo(groupId, "GroupChatFragment").let {
            logDebug(TAG,"updateMuteStatus: isMuteMessages=${it.isMuteMessages}, isMuteNotification=${it.isMuteNotification}" )
            withContext(Dispatchers.Main) {
                binding.clChatListHeader.updateHeaderMuteStatus(it.isMuteMessages, it.isMuteNotification)
            }
        }
    }

    private fun updateGreenDot(isOnline : Boolean) {
        binding.clChatListHeader.updateGreenDotVisibility(isOnline)
    }

    private fun updateMemberCount(count:Int){
        binding.clChatListHeader.updateMemberCount(count)
    }

    private fun openGroupChatProfileDialog(groupInfo: GroupInfoBean){
        val dialog = GroupInfoDialog.newInstance(groupInfo.groupId)
        dialog.setFromChatHistory(true, fragment)
        dialog.showDialog(fragment.activity)
    }
}