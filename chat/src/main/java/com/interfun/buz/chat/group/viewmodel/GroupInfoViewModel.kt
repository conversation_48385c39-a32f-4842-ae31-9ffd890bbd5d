package com.interfun.buz.chat.group.viewmodel

import androidx.annotation.StringRes
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.DiffUtil.ItemCallback
import com.interfun.buz.base.ktx.*
import com.interfun.buz.chat.R
import com.interfun.buz.chat.common.constants.ChatMMKV
import com.interfun.buz.chat.common.manager.TranslationMessageManager
import com.interfun.buz.chat.common.utils.ChatTracker
import com.interfun.buz.chat.group.viewmodel.GroupInfoViewModel.BotListItem.GroupBotMember
import com.interfun.buz.chat.group.viewmodel.GroupInfoViewModel.GroupProfileItem.*
import com.interfun.buz.chat.group.viewmodel.GroupInfoViewModel.GroupProfileItem.TextDataItem.TextDataItemType
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.bean.DataState
import com.interfun.buz.common.bean.Resp.Error
import com.interfun.buz.common.bean.Resp.Success
import com.interfun.buz.common.bean.chat.MuteType
import com.interfun.buz.common.constants.RouterParamKey
import com.interfun.buz.common.database.entity.ConvType
import com.interfun.buz.common.database.entity.ExistPlaceType
import com.interfun.buz.common.database.entity.chat.GroupInfoBean
import com.interfun.buz.common.eventbus.chat.ChatListClearAllHistoryEvent
import com.interfun.buz.common.eventbus.wt.WTListScrollEvent
import com.interfun.buz.common.interfaces.DeleteCacheType
import com.interfun.buz.common.ktx.*
import com.interfun.buz.common.manager.*
import com.interfun.buz.common.service.ChatService
import com.interfun.buz.common.service.StorageService
import com.interfun.buz.common.utils.PromptUtil
import com.interfun.buz.common.utils.language.TranslateLanguageManager
import com.interfun.buz.common.widget.portrait.group.GroupPortrait
import com.interfun.buz.im.IMAgent
import com.interfun.buz.liveplace.manager.LivePlaceCacheHelper
import com.interfun.buz.social.db.entity.BuzGroupComposite
import com.interfun.buz.social.entity.OfflineState
import com.interfun.buz.social.entity.UserStateInfo
import com.interfun.buz.social.entity.convertStatusToState
import com.interfun.buz.social.repo.GroupMembersRepository
import com.interfun.buz.social.repo.GroupRepository
import com.interfun.buz.social.repo.UserOnlineStatusRepository
import com.interfun.buz.social.repo.UserRepository
import com.interfun.buz.translator.repo.AutoTranslationSettingRepository
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import com.lizhi.im5.sdk.conversation.IM5ConversationType.GROUP
import com.yibasan.lizhifm.sdk.platformtools.ResUtil
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject
import kotlin.math.min

@HiltViewModel
class GroupInfoViewModel @Inject constructor(
    private val groupMembersRepository: GroupMembersRepository,
    private val groupRepository: GroupRepository,
    private val userRepository: UserRepository,
    private val userOnlineStatusRepository: UserOnlineStatusRepository,
    private val autoTranslationSettingRepository: AutoTranslationSettingRepository,
    private val savedStateHandle: SavedStateHandle,
) : ViewModel() {

    companion object {
        const val TAG = "GroupInfoViewModel"
    }

    private val imRepository: IMAgent = IMAgent
    private val groupId =
        savedStateHandle.get<Long>(RouterParamKey.Group.KEY_GROUP_ID_LOWER_CASE) ?: throw IllegalArgumentException("groupId is null")
    private val inviterId =
        savedStateHandle.get<String>(RouterParamKey.Group.KEY_INVITER_ID_LOWER_CASE)?.toLongOrNull()

    private val DEFAULT_SHOW_MAX_MEMUBER_NUM = 10 // 默认最大显示群成员数量
    private val enableAIGroupEntrancePinToTop =
        AppConfigRequestManager.AppConfigWithLogin?.commonConfig?.enableAIGroupEntrancePinToTop.getBooleanDefault()

    //todo UserRepository 待处理群更新信息时更新用户在线状态
    private val userMembersFlow = channelFlow<List<CustomGroupMember>> {
        groupMembersRepository.getGroupUserMemberFromCacheFlow(
            groupId,
            DEFAULT_SHOW_MAX_MEMUBER_NUM + 1, //取多一条，用于判断是否超过了最大显示数量
        ).collectLatest { userMembers ->
            userOnlineStatusRepository.getStatusInfoFlow(userMembers.map { it.userId })
                .collect { statusMap ->
                    val size = userMembers.size
                    send(userMembers.mapIndexed { index, member ->
                        val userComposite = member.buzUserComposite
                        val backgroundRes = if (index == size - 1) {
                            //如果超过了最大显示数量，这条就不会展示，被view all members收尾
                            R.drawable.group_dialog_group_member_item_bottom_radius_bg
                        } else {
                            R.drawable.group_dialog_group_member_item_nomal_bg
                        }
                        val status = statusMap[member.userId]
                        CustomGroupMember(
                            member.userId,
                            member.buzUserComposite?.fullNickName ?: "${member.userId}",
                            portrait = userComposite?.user?.portrait ?: "",
                            isSelf = member.userId == UserSessionManager.getSessionUid(),
                            isInQuietMode = status?.isInQuietMode ?: false,
                            isOnline = status?.isOnline ?: false,
                            backgroundRes = backgroundRes,
                            role = member.userRole,
                            userStateInfo = status?.convertStatusToState()?: OfflineState.MoreThan48Hour(member.userId)
                        )
                    })
                }
        }
    }.flowOn(Dispatchers.Default)
        .stateIn(
            viewModelScope, SharingStarted.WhileSubscribed(5000L), emptyList()
        )

    private val removingBotsFlow = MutableStateFlow(emptySet<Long>())

    val botMembersFlow = combine(
        groupMembersRepository.getGroupBotMemberFromCacheFlow(groupId),
        removingBotsFlow
    ) { botMembers, removingBots ->
        botMembers.map { member ->
            GroupBotMember(
                member.userId,
                member.buzUserComposite?.fullNickName ?: "${member.userId}",
                member.buzUserComposite?.user?.portrait ?: "",
                isRemoving = removingBots.contains(member.userId)
            )
        }
    }.flowOn(Dispatchers.Default)
        .stateIn(
            viewModelScope, SharingStarted.WhileSubscribed(5000L), emptyList()
        )

    val groupInfoFlow : StateFlow<DataState<BuzGroupComposite>> = groupRepository.getGroupCompositeFlow(groupId,true).map {
        DataState.Data(it)
    }.stateIn(viewModelScope, SharingStarted.Eagerly, DataState.Loading)

    val groupInfo : BuzGroupComposite?
        get() = (groupInfoFlow.value as? DataState.Data)?.data

    private val inviterNameFlow = flow {
        if (inviterId == null) {
            emit(null)
        } else {
            emitAll(userRepository.getUserCompositeFlow(inviterId).map { it.fullNickName })
        }
    }.stateIn(viewModelScope, SharingStarted.Eagerly, null)

    private val isJoiningGroup = MutableStateFlow(false)

    val groupProfileDataFlow: StateFlow<DataState<List<GroupProfileItem>>> = combine7(
        groupInfoFlow,
        userMembersFlow,
        botMembersFlow,
        inviterNameFlow,
        isJoiningGroup,
        autoTranslationSettingRepository.observeAutoTranslationSelectedLanguage(),
        TranslationMessageManager.getAutoTranslationSettingFlow(groupId, true)
    ) { groupInfoState, userMembers, botMembers, inviterName, isJoiningGroup, selectedLanguage, isAutoTranslated ->
        if (groupInfoState !is DataState.Data) {
            return@combine7 DataState.Loading
        }
        val groupInfo = groupInfoState.data
        val result = arrayListOf<GroupProfileItem>()
        val buzGroup = groupInfo.buzGroup
        val groupExtra = groupInfo.buzGroupExtra
        val isInGroup = groupExtra?.isInGroup ?: false
        val isMuteNotification = groupExtra?.isMuteNotification ?: false
        val isMuteMessages = groupExtra?.isMuteMessages ?: false
        val profileItem = GroupProfileInfoItem(
            buzGroup.groupId,
            buzGroup.groupName,
            buzGroup.toGroupPortrait(),
            isMuteNotification,
            isMuteMessages,
            inviterName,
            buzGroup.memberNum,
            groupExtra?.canEdit ?: false,
            buzGroup.isBigGroup,
            isJoiningGroup,
            isInGroup
        )
        result.add(profileItem)
        if (isInGroup) {
            if (enableAIGroupEntrancePinToTop) {
                result.addBotMembers(buzGroup.isBigGroup, botMembers)
            }
            result.addNotificationSetting(isMuteMessages, isMuteNotification)
            result.addTranslateSetting(isAutoTranslated, selectedLanguage.displayText)
            result.add(Space(14))
            result.add(GroupMemberCountBean(buzGroup.memberNum))
            if (groupExtra?.canInvite == true) {
                result.add(AddMemberItem)
            }
            //添加member
            result.addUserMembers(userMembers, buzGroup.memberNum)
            if (!enableAIGroupEntrancePinToTop) {
                result.add(Space(20))
                result.addBotMembers(buzGroup.isBigGroup, botMembers)
            }
            result.addClearHistoryItem(buzGroup.isBigGroup)
            result.addReport()
            result.addLeaveGroup()
        }
        result.add(Space(64))
        DataState.Data(result)
    }.flowOn(Dispatchers.Default)
        .stateIn(viewModelScope, SharingStarted.WhileSubscribed(5000L), DataState.Loading)

    val eventToUIFlow = MutableSharedFlow<EventToUI>()

    private val chatRouterService by lazy { routerServices<ChatService>().value }

    init {
        viewModelScope.launch {
            groupMembersRepository.syncGroupMemberList(groupId)
        }
    }

    fun requestAutoTranslationLanguageList() {
        launchIO {
            autoTranslationSettingRepository.requestAutoTranslationLanguageList()
        }
    }

    fun clickToAddMembersPage() {
        viewModelScope.launch {
            val memberIds =
                groupMembersRepository.getGroupUserMemberEntityFromCache(groupId).mapNotNull {
                    if (it.userId == UserSessionManager.uid) {
                        null
                    } else {
                        it.userId
                    }
                }
            eventToUIFlow.emit(EventToUI.ToAddMembersPage(memberIds))
        }
    }

    fun getGroupMemberCount(): Int {
        if (groupInfoFlow.value !is DataState.Data) return 0
        val groupInfo = (groupInfoFlow.value as DataState.Data).data
        return groupInfo.buzGroup.memberNum
    }

    fun requestJoinGroup() {
        log(TAG, "requestJoinGroup: inviterId = ${inviterId}}")
        if (inviterId == null) return
        viewModelScope.launch(Dispatchers.IO) {
            isJoiningGroup.update { true }
            val resp = groupRepository.joinGroup(groupId,inviterId)
            when(resp){
                is Error -> {
                    //250 means inviter has left group
                    if (resp.code == 250) {
                        val name = inviterNameFlow.value ?: "$inviterId"
                        val tip = ResUtil.getString(R.string.common_inviter_has_left_group, name)
                        toast(tip)
                    } else {
                        PromptUtil.parse(resp.prompt)
                    }
                }
                is Success -> {
                    log(TAG, "requestJoinGroup: onSuccess")
                    WTListScrollEvent.post(groupId)
                }
            }
            isJoiningGroup.update { false }
        }
    }

    fun confirmClearHistory() {
        viewModelScope.launch {
            val result = imRepository.clearMessagesWaitLocal(GROUP, groupId.toString(), true)
            if (result) {
                ChatListClearAllHistoryEvent.post()
                routerServices<StorageService>().value?.deleteBatchMsgCache(
                    userId = UserSessionManager.uid,
                    targetId = groupId,
                    deleteFrom = DeleteCacheType.ClearChat,
                    convType = ConvType.GroupChat.value
                )
                toastRegularCorrect(R.string.profile_item_clear_history_success)
            } else {
                toastNetworkErrorTips()
            }
        }
    }

    fun reportGroup(groupId: Long) {
        viewModelScope.launch(Dispatchers.IO) {
            val resp = groupRepository.reportGroup(groupId)
            PromptUtil.parse(resp.prompt)
            when (resp) {
                is Error -> {
                    if (resp.prompt == null) {
                        toastNetworkErrorTips()
                    }
                }

                is Success -> {
                    toastRegularCorrect(R.string.report_success)
                }
            }
        }
    }

    fun leaveGroup(groupId: Long) {
        viewModelScope.launch(Dispatchers.IO) {
            val resp = groupRepository.quitGroup(groupId)
            PromptUtil.parse(resp.prompt)
            when(resp){
                is Error -> {
                    if (resp.prompt == null) {
                        toastNetworkErrorTips()
                    }
                }
                is Success -> {
                    IMAgent.deleteConversationSync(IM5ConversationType.GROUP, groupId.toString())
                    routerServices<StorageService>().value?.deleteBatchMsgCache(
                        userId = UserSessionManager.uid,
                        deleteFrom = DeleteCacheType.ExitGroup,
                        targetId = groupId,
                        convType = ConvType.GroupChat.value
                    )
                    ChatMMKV.putGroupInviteExposureFlag(groupId.toString(), false)
                    eventToUIFlow.emit(EventToUI.QuitGroupSuccess(groupId))
                }
            }
        }
    }

    fun kickOutGroupMember(member: Long) {
        viewModelScope.launch(Dispatchers.IO) {
            val resp = groupMembersRepository.kickOutGroupUserMembers(groupId, listOf(member))
            PromptUtil.parse(resp.prompt)
            if (resp !is Success && resp.prompt == null) {
                toastNetworkErrorTips()
            }
        }
    }

    fun kickOutGroupBot(botId: Long) {
        viewModelScope.launch {
            removingBotsFlow.update { hashSetOf(botId) }
            val resp = groupMembersRepository.kickOutGroupBotMembers(groupId, listOf(botId))
            PromptUtil.parse(resp.prompt)
            ChatTracker.onRemoveBotFromGroupResult(resp is Success, arrayListOf(botId))
            if (resp !is Success && resp.prompt == null) {
                toastNetworkErrorTips()
            }
            removingBotsFlow.update { emptySet() }
        }
    }

    private fun MutableList<GroupProfileItem>.addNotificationSetting(
        isMuteMessages: Boolean,
        isMuteNotification: Boolean
    ) {
        add(SubTitleItem(R.string.notification))
        add(NotificationSettingItem(isMuteNotification, isMuteMessages))
    }

    private fun MutableList<GroupProfileItem>.addTranslateSetting(isAutoTranslate: Boolean, targetLanguage: String) {
        if (!ABTestManager.showTranslateFunction) {
            return
        }
        add(Space(20))
        add(SubTitleItem(R.string.trans_msg_translation))
        add(AutoTranslationSetting(isAutoTranslate, targetLanguage))
        add(Space(2))
        add(SubTitleItem(R.string.trans_translation_desc,false))
    }

    private fun MutableList<GroupProfileItem>.addUserMembers(
        userMembers: List<CustomGroupMember>,
        groupMemberCount: Int
    ) {
        val maxSize = min(DEFAULT_SHOW_MAX_MEMUBER_NUM, userMembers.size)
        for (i in 0 until maxSize) {
            add(userMembers[i])
        }
        if (groupMemberCount > DEFAULT_SHOW_MAX_MEMUBER_NUM) {
            add(GroupViewAllMemberBean(groupMemberCount))
        }
    }

    private fun MutableList<GroupProfileItem>.addBotMembers(
        isBigGroup: Boolean,
        botMemberList: List<GroupBotMember>
    ) {
        val canShowBotBlock = isBigGroup.not()
                && (AppConfigRequestManager.maxGroupBotLimit > 0 || botMemberList.isNotEmpty())
        if (canShowBotBlock) {
            add(SubTitleItem(R.string.ai_buz_ai_character))
            val botItemList: MutableList<BotListItem> = ArrayList(botMemberList)
            if (AppConfigRequestManager.maxGroupBotLimit > 0) {
                botItemList.add(
                    BotListItem.AddBotMemberItem(
                        isBigStyle = botMemberList.isEmpty(),
                        canInvite = botMemberList.size < AppConfigRequestManager.maxGroupBotLimit
                    )
                )
            }
            add(GroupBotListBean(botItemList))
        }
    }

    fun onAutoPlayVoiceChecked(isChecked: Boolean, groupId: Long) {
        viewModelScope.launch(Dispatchers.IO) {
            groupRepository.updateGroupSetting(
                groupId,
                muteMessages = if (isChecked) MuteType.MUTE.value else MuteType.UN_MUTE.value
            )
            chatRouterService?.muteMessagesByTargetId(groupId)
            MuteInfoManager.trackMuteUser(false, isMuteMessages = isChecked)
        }
    }

    fun onShowNotificationChecked(isChecked: Boolean, groupId: Long) {
        viewModelScope.launch {
            groupRepository.updateGroupSetting(
                groupId,
                muteNotification = if (isChecked) MuteType.MUTE.value else MuteType.UN_MUTE.value
            )
            MuteInfoManager.trackMuteUser(false, isMuteNotification = isChecked)
        }
    }

    fun MutableList<GroupProfileItem>.addClearHistoryItem(isBigGroup: Boolean) {
        if (isBigGroup) {
            add(Space(10))
        }
        add(
            TextDataItem(
                TextDataItemType.clearChatHistory,
                R.string.chat_clear_chat_history.asString()
            )
        )
    }

    fun MutableList<GroupProfileItem>.addReport() {
        add(Space(10))
        add(
            TextDataItem(
                TextDataItemType.report,
                R.string.report.asString(),
                backgroundRes = R.drawable.group_dialog_group_member_item_top_radius_bg
            )
        )
    }

    fun MutableList<GroupProfileItem>.addLeaveGroup() {
        add(
            TextDataItem(
                TextDataItemType.leaveGroup,
                R.string.chat_leave_group.asString(),
                textColor = R.color.secondary_error.asColor(),
                backgroundRes = R.drawable.group_dialog_group_member_item_bottom_radius_bg,
                isShowTopLine = true
            )
        )
    }

    fun trackOnPageView(
        groupInfo: GroupInfoBean,
        isNewUser: Boolean,
        fromChatHistory: Boolean,
        fromChatHeader: Boolean,
    ) {
        val content = when (LivePlaceCacheHelper.getExistsInfoFromMem(groupInfo.groupId)) {
            ExistPlaceType.EXIST -> "created"
            ExistPlaceType.NO_EXIST -> "not_created"
            ExistPlaceType.PENDING -> "pending"
        }
        ChatTracker.onGroupInfoDialogView(
            isNewUser,
            groupInfo.groupId.toString(),
            groupInfo.isInGroup(),
            fromChatHistory,
            fromChatHeader,
            content = content,
            isLive = ChannelStatusManager.isOpenLivePlace(groupInfo.groupId)
        )
    }

    inner class LoadItem(status: Boolean = false) {
        var fail = status
    }

    sealed interface BotListItem {

        fun areItemsTheSame(newItem: BotListItem): Boolean

        fun areContentsTheSame(newItem: BotListItem): Boolean

        data class GroupBotMember(
            val userId: Long,
            val userName: String,
            val portrait: String,
            val isRemoving: Boolean
        ) : BotListItem {
            override fun areItemsTheSame(newItem: BotListItem): Boolean {
                return newItem is GroupBotMember && newItem.userId == userId
            }

            override fun areContentsTheSame(newItem: BotListItem): Boolean {
                return newItem == this
            }
        }

        data class AddBotMemberItem(val isBigStyle: Boolean, val canInvite: Boolean) : BotListItem {
            override fun areItemsTheSame(newItem: BotListItem): Boolean {
                return newItem is AddBotMemberItem
            }

            override fun areContentsTheSame(newItem: BotListItem): Boolean {
                return newItem == this
            }
        }
    }

    class BotListDiffCallback : DiffUtil.ItemCallback<BotListItem>() {
        override fun areItemsTheSame(oldItem: BotListItem, newItem: BotListItem): Boolean {
            return oldItem.areItemsTheSame(newItem)
        }
        override fun areContentsTheSame(oldItem: BotListItem, newItem: BotListItem): Boolean {
            return oldItem.areContentsTheSame(newItem)
        }
    }

    sealed interface GroupProfileItem {

        fun areItemsTheSame(newItem : GroupProfileItem) : Boolean

        fun areContentsTheSame(newItem: GroupProfileItem) : Boolean

        data class GroupProfileInfoItem(
            val groupId: Long,
            val groupName: String,
            val groupPortrait: GroupPortrait,
            val isMuteNotification: Boolean,
            val isMuteMessages: Boolean,
            val inviterName: String?,
            val memberNum : Int,
            val canEdit : Boolean,
            val isBigGroup: Boolean,
            val isJoinButtonLoading : Boolean,
            val isInGroup : Boolean,
        ) : GroupProfileItem {
            override fun areItemsTheSame(newItem: GroupProfileItem): Boolean {
                return newItem is GroupProfileInfoItem
            }

            override fun areContentsTheSame(newItem: GroupProfileItem): Boolean {
                return newItem == this
            }
        }

        data object AddMemberItem : GroupProfileItem {
            override fun areItemsTheSame(newItem: GroupProfileItem): Boolean {
                return true
            }

            override fun areContentsTheSame(newItem: GroupProfileItem): Boolean {
                return true
            }
        }

        data class GroupMemberCountBean(
            val totalCount: Int
        ) : GroupProfileItem {
            override fun areItemsTheSame(newItem: GroupProfileItem): Boolean {
                return newItem is GroupMemberCountBean
            }

            override fun areContentsTheSame(newItem: GroupProfileItem): Boolean {
                return newItem == this
            }
        }

        data class NotificationSettingItem(
            val isMuteNotification: Boolean,
            val isMuteMessages: Boolean
        ) : GroupProfileItem {
            override fun areItemsTheSame(newItem: GroupProfileItem): Boolean {
                return newItem === this || newItem is NotificationSettingItem
            }

            override fun areContentsTheSame(newItem: GroupProfileItem): Boolean {
                return newItem == this
            }
        }

        data class AutoTranslationSetting(
            val isAutoTranslate: Boolean,
            val translateLanguage: String,
        ) : GroupProfileItem {
            override fun areItemsTheSame(newItem: GroupProfileItem): Boolean {
                return newItem === this || newItem is AutoTranslationSetting
            }

            override fun areContentsTheSame(newItem: GroupProfileItem): Boolean {
                return newItem == this
            }
        }

        data class SubTitleItem(@StringRes val title: Int, val isAllCaps: Boolean = true) : GroupProfileItem {
            override fun areItemsTheSame(newItem: GroupProfileItem): Boolean {
                return newItem === this || newItem is SubTitleItem && newItem.title == title
            }

            override fun areContentsTheSame(newItem: GroupProfileItem): Boolean {
                return newItem == this
            }
        }

        data class GroupBotListBean(val botMemberList: List<BotListItem>) : GroupProfileItem {
            override fun areItemsTheSame(newItem: GroupProfileItem): Boolean {
                return newItem is GroupBotListBean
            }

            override fun areContentsTheSame(newItem: GroupProfileItem): Boolean {
                return newItem is GroupBotListBean && newItem.botMemberList === botMemberList
            }
        }

        data class CustomGroupMember(
            val userId: Long,
            val userName: String,
            val portrait: String,
            val isSelf: Boolean,
            val isInQuietMode: Boolean,
            val isOnline: Boolean,
            val backgroundRes: Int,
            val role: Int,
            val userStateInfo: UserStateInfo
        ) : GroupProfileItem {
            override fun areItemsTheSame(newItem: GroupProfileItem): Boolean {
                return newItem === this || newItem is CustomGroupMember && newItem.userId == userId
            }

            override fun areContentsTheSame(newItem: GroupProfileItem): Boolean {
                return newItem == this
            }
        }


        data class TextDataItem(
            val type: TextDataItemType,
            val text: String,
            val textColor: Int = R.color.text_white_main.asColor(),
            val backgroundRes: Int = R.drawable.group_dialog_group_member_item_radius_bg,
            val isShowTopLine: Boolean = false
        ) : GroupProfileItem {

            enum class TextDataItemType {
                clearChatHistory, report, leaveGroup
            }

            override fun areItemsTheSame(newItem: GroupProfileItem): Boolean {
                return newItem === this || newItem is TextDataItem && newItem.type == type
            }

            override fun areContentsTheSame(newItem: GroupProfileItem): Boolean {
                return newItem == this
            }
        }

        data class GroupViewAllMemberBean(
            val totalCount: Int,
        ) : GroupProfileItem {
            override fun areItemsTheSame(newItem: GroupProfileItem): Boolean {
                return newItem is GroupViewAllMemberBean
            }

            override fun areContentsTheSame(newItem: GroupProfileItem): Boolean {
                return newItem is GroupViewAllMemberBean && newItem.totalCount == totalCount
            }
        }

        data class Space(val dp: Int) : GroupProfileItem {
            override fun areItemsTheSame(newItem: GroupProfileItem): Boolean {
                return newItem === this || newItem is Space
            }

            override fun areContentsTheSame(newItem: GroupProfileItem): Boolean {
                return newItem == this
            }
        }
    }

    class DiffCallback : ItemCallback<GroupProfileItem>() {
        override fun areItemsTheSame(
            oldItem: GroupProfileItem,
            newItem: GroupProfileItem
        ): Boolean {
            return oldItem.areItemsTheSame(newItem)
        }

        override fun areContentsTheSame(
            oldItem: GroupProfileItem,
            newItem: GroupProfileItem
        ): Boolean {
            return oldItem.areContentsTheSame(newItem)
        }
    }

    sealed interface EventToUI {
        data class QuitGroupSuccess(val groupId: Long) : EventToUI
        data class ToAddMembersPage(val memberIds : List<Long>) : EventToUI
    }
}