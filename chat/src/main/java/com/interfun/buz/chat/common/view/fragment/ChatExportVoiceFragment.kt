package com.interfun.buz.chat.common.view.fragment

import android.Manifest
import android.app.Dialog
import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.fragment.app.viewModels
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.interfun.buz.base.ktx.*
import com.interfun.buz.biz.center.voicefilter.compat.VoiceFilterHelper
import com.interfun.buz.chat.common.utils.ChatTracker
import com.interfun.buz.chat.databinding.ChatLayoutExportVoiceBinding
import com.interfun.buz.chat.group.viewmodel.ExportVoiceViewModel
import com.interfun.buz.common.R
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.ktx.getIntDefault
import com.interfun.buz.common.ktx.getLongDefault
import com.interfun.buz.common.manager.AppConfigRequestManager
import com.interfun.buz.common.manager.voicecall.VoiceCallPortal
import com.interfun.buz.common.widget.view.EmptyDataViewListener
import com.interfun.buz.compose.components.notchAwareBottomPadding
import com.interfun.buz.compose.ktx.parseColorToInt
import com.interfun.buz.domain.social.components.HorizontalShareListScreenNew
import com.interfun.buz.domain.social.components.VoiceFilterMessagePreview
import com.interfun.buz.domain.social.components.VoiceFilterMessagePreviewType
import com.interfun.buz.domain.im.social.entity.ShareType
import com.interfun.buz.im.ktx.asrText
import com.interfun.buz.im.ktx.getAudioUri
import com.interfun.buz.im.ktx.isGroup
import com.interfun.buz.im.message.BuzVoiceMsg
import com.interfun.buz.im.message.VoiceTextMsg
import com.interfun.buz.media.bean.BuzMediaItem
import com.interfun.buz.media.bean.MediaType
import com.interfun.buz.media.constants.CHAT_EXPORT_VIDEO_PREVIEW_PLAYER
import com.interfun.buz.media.player.view.OnActiveIntercept
import com.interfun.buz.media.player.view.PlayerConfig
import com.interfun.buz.onair.standard.IGlobalOnAirController
import com.lizhi.im5.sdk.message.IMessage
import com.lizhi.im5.sdk.message.model.IM5VoiceMessage
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.collections.immutable.persistentListOf


/**
 *
 *
 * <AUTHOR>
 * @date 2024/3/27
 * @desc
 */
@AndroidEntryPoint
class ChatExportVoiceFragment : BottomSheetDialogFragment() {

    companion object {
        const val TAG = "ChatExportVoiceFragment"

        private const val KEY_MSG_ID = "msgId"
         const val KEY_IS_GROUP = "isGroup"
        private const val KEY_ASR_TEXT = "asrText"
        private const val KEY_DURATION = "duration"
        private const val KEY_AUDIO_URL = "audioUrl"
        const val KEY_FILTER_ID = "filterId"
        const val KEY_TARGET_ID = "targetId"
        private const val KEY_FILTER_NAME = "filterName"
        private const val KEY_BG_COLOR = "backgroundColor"
        private const val KEY_MIC_COLOR = "microphoneColor"
        private const val KEY_VOICE_FILTER_TYPE = "voiceFilterType"
        private const val KEY_VIDEO_TEMPLATE_URL = "videoTemplateUrl"
        private const val KEY_VIDEO_TEMPLATE_MD5 = "videoTemplateMd5"

        fun newInstance(msg: IMessage): ChatExportVoiceFragment {
            val msgContent = msg.content as IM5VoiceMessage
            val audioUrl = msg.getAudioUri()?.first.toString()

            val filterId = when (msgContent) {
                is BuzVoiceMsg -> msgContent.voiceFilterInfo?.filterId
                is VoiceTextMsg -> msgContent.voiceFilterInfo?.filterId
                else -> null
            }
            val voiceFilterInfo = when (msgContent) {
                is BuzVoiceMsg -> msgContent.voiceFilterInfo
                is VoiceTextMsg -> msgContent.voiceFilterInfo
                else -> null
            }
            val voiceFilterData = filterId?.let { VoiceFilterHelper.getCachedVoiceFilterById(it) }
            val filterName = voiceFilterData?.filterName
            val voiceFilterType = voiceFilterInfo?.bubbleStyle
                ?: voiceFilterData?.bubbleType?.value.getIntDefault()

            val backgroundColor = voiceFilterInfo?.suitColor1?.parseColorToInt()
                ?: voiceFilterData?.backgroundColor?.parseColorToInt()

            val microphoneColor = voiceFilterInfo?.suitColor2?.parseColorToInt()
                ?: voiceFilterData?.microphoneColor?.parseColorToInt()

            val videoTemplateUrl = voiceFilterInfo?.videoTemplateUrl
                ?: voiceFilterData?.videoTemplateUrl
                ?: AppConfigRequestManager.voiceFilterVideoTemplateUrl

            val videoTemplateMd5 = voiceFilterInfo?.videoTemplateMd5
                ?: voiceFilterData?.videoTemplateMd5
                ?: AppConfigRequestManager.voiceFilterVideoTemplateMd5

            return ChatExportVoiceFragment().apply {
                arguments = Bundle().apply {
                    putLong(KEY_MSG_ID, msg.msgId)
                    putString(KEY_AUDIO_URL, audioUrl)
                    putBoolean(KEY_IS_GROUP, msg.isGroup)
                    putString(KEY_TARGET_ID, msg.targetId)
                    putString(KEY_FILTER_NAME, filterName)
                    putInt(KEY_DURATION, msgContent.duration)
                    putString(KEY_ASR_TEXT, msgContent.asrText)
                    putInt(KEY_VOICE_FILTER_TYPE, voiceFilterType)
                    putLong(KEY_FILTER_ID, filterId.getLongDefault())
                    putString(KEY_VIDEO_TEMPLATE_MD5, videoTemplateMd5)
                    putString(KEY_VIDEO_TEMPLATE_URL, videoTemplateUrl)
                    putInt(KEY_BG_COLOR, backgroundColor.getIntDefault())
                    putInt(KEY_MIC_COLOR, microphoneColor.getIntDefault())
                }
            }
        }
    }

    private var actionAfterRequestPermission:(()-> Unit)? = null
    private val permissionLauncher = fragment.requestPermissionLauncher{
        if (it.not()){
            toastIconFontMsg(
                iconFont = com.interfun.buz.domain.record.R.string.ic_warning_solid.asString(),
                message = com.interfun.buz.domain.record.R.string.failed_to_save.asString(),
                iconFontColor = com.interfun.buz.domain.record.R.color.text_white_important.asColor(),
                textColor = com.interfun.buz.domain.record.R.color.color_text_white_primary.asColor(),
                style = IconToastStyle.ICON_TOP_TEXT_BOTTOM
            )
        }else{
            actionAfterRequestPermission?.invoke()
            actionAfterRequestPermission = null
        }
    }

    val exportVoiceViewModel by viewModels<ExportVoiceViewModel>()


    private var _binding: ChatLayoutExportVoiceBinding? = null
    private val binding get() = _binding!!
    private val contentView: View get() = binding.root

    private val msgId by lazy { arguments?.getLong(KEY_MSG_ID) ?: 0L }
    private val asrText by lazy { arguments?.getString(KEY_ASR_TEXT) }
    private val duration by lazy { arguments?.getInt(KEY_DURATION) ?: 0 }
    private val filterId by lazy { arguments?.getLong(KEY_FILTER_ID) ?: 0L }
    private val filterName by lazy { arguments?.getString(KEY_FILTER_NAME) }
    private val targetId by lazy { arguments?.getString(KEY_TARGET_ID) ?: "" }
    private val audioUrl by lazy { arguments?.getString(KEY_AUDIO_URL) ?: "" }
    private val isGroup by lazy { arguments?.getBoolean(KEY_IS_GROUP) == true }
    private val voiceFilterType by lazy { arguments?.getInt(KEY_VOICE_FILTER_TYPE) ?: 0 }
    private val backgroundColor by lazy { arguments?.getInt(KEY_BG_COLOR).takeIf { it != 0 } }
    private val microphoneColor by lazy { arguments?.getInt(KEY_MIC_COLOR).takeIf { it != 0 } }
    private val videoTemplateUrl by lazy { arguments?.getString(KEY_VIDEO_TEMPLATE_URL) ?: "" }
    private val videoTemplateMd5 by lazy { arguments?.getString(KEY_VIDEO_TEMPLATE_MD5) ?: "" }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        return super.onCreateDialog(savedInstanceState).also { dialog ->
            dialog.window?.attributes?.windowAnimations = 0
            dialog.setOnShowListener { dlg ->
                val bottomSheet =
                    dialog.findViewById<FrameLayout>(R.id.design_bottom_sheet)
                bottomSheet?.let {
                    BottomSheetBehavior.from(it).apply {
                        state = BottomSheetBehavior.STATE_EXPANDED
                        isDraggable = false // make it non-draggable
                    }
                }
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = ChatLayoutExportVoiceBinding.inflate(inflater, container, false)
        initContentFragment()
        return contentView
    }

    private fun initContentFragment() {
        initData()
        initView()
    }

    private fun initView() {
        binding.vDialog.layoutSize(deviceWidth, deviceHeight)
        binding.root.background = R.color.alpha_black_70.asDrawable()
        binding.loadingView.setFixedColor(R.color.color_foreground_neutral_important_default.asColor())
        binding.iftClose.click {
            reportExportActionTracker("cancel")
            dismiss()
        }
        binding.emptyDataView.setListener(object : EmptyDataViewListener {
            override fun onButtonClicked() {
                reportExportActionTracker("retry")
                generateVideo()
            }
        })
    }

    private fun initData() {
        generateVideo()
    }

    private fun generateVideo() {
        startLoading()
        val voiceFilterMessagePreview = VoiceFilterMessagePreview(fragment.requireContext()).apply {
            initPreview(
                previewType = VoiceFilterMessagePreviewType.EXPORT_VIDEO,
                recordDuration = duration.voiceTimeStr(),
                filterName = filterName,
                bubbleStyle = voiceFilterType,
                backgroundColor = backgroundColor,
                microphoneColor = microphoneColor,
                asrText = asrText
            )
            autoMeasure()
        }
        val videoTemplateUrlFinal= videoTemplateUrl.ifEmpty {
            AppConfigRequestManager.voiceFilterVideoTemplateUrl
        }
        val videoTemplateMd5Final = videoTemplateMd5.ifEmpty {
            AppConfigRequestManager.voiceFilterVideoTemplateMd5
        }
        exportVoiceViewModel.generateVideo(
            templateUrl = videoTemplateUrlFinal,
            templateMd5 = videoTemplateMd5Final,
            duration = duration,
            audioUrl = audioUrl,
            placeHolderView = voiceFilterMessagePreview,
            onSuccess = { file ->
                updateVideoContent(file.absolutePath)
                logInfo(TAG, "generate video success: ${file.absolutePath}")
            },
            onFailed = { errorMessage ->
                stopLoading()
                binding.emptyDataView.visible()
                logError(TAG, "generate video failed: $errorMessage")
            }
        )
    }

    private fun updateVideoContent(filePath: String) {
        val width = deviceWidth - 50.dp * 2
        val height = width * (490 / 275)
        val mediaItem = BuzMediaItem(
            mediaId = msgId,
            mediaUri = filePath,
            width = width,
            height = height,
            type = MediaType.Video,
            duration = duration.toLong()
        )
        val playerConfig = PlayerConfig().apply {
            pauseResString = R.string.ic_play_solid.asString()
            onActiveIntercept = object : OnActiveIntercept {
                override fun onIntercept(item: BuzMediaItem?): Boolean {
                    val isInVoiceCall = VoiceCallPortal.isOnRealTimeCall()
                    if (isInVoiceCall) {
                        R.string.calling_unable_to_play_vid.toast()
                        return true
                    }

                    val isInOnAir =
                        routerServices<IGlobalOnAirController>().value?.isInOnAir() == true
                    if (isInOnAir) {
                        R.string.exit_current_call_and_try.asString().toast()
                        return true
                    }
                    return false
                }
            }
        }
        val playerName = CHAT_EXPORT_VIDEO_PREVIEW_PLAYER
        binding.videoContainer.apply {
            invisible()
            bindData(
                mediaItem,
                playerConfig,
                playerName
            ) {
                stopLoading()
                visible()
                updateShareList(filePath)
                click { if (isPlaying()) inActive() }
            }
        }
    }

    private fun startLoading() {
        binding.tvProcessing.text = R.string.processing.asString()
        binding.emptyDataView.gone()
        binding.loadingView.playAnimation()
        binding.loadingGroup.visible()
    }

    private fun stopLoading() {
        binding.loadingGroup.gone()
        binding.loadingView.cancelAnimation()
    }

    private fun updateShareList(absolutePath: String) {
        binding.shareItemList.apply {
            setViewCompositionStrategy(ViewCompositionStrategy.DisposeOnViewTreeLifecycleDestroyed)
            setContent {
                val shareTypeList = persistentListOf(
                    ShareType.DOWN_LOAD,
                    ShareType.SYSTEM_SHARE,
                ).addAll(
                    ShareType.Companion.getShareTypeList(AppConfigRequestManager.sharePlatformList)
                )
                HorizontalShareListScreenNew(
                    list = shareTypeList,
                    modifier = Modifier
                        .notchAwareBottomPadding(64.buzDp)
                        .wrapContentWidth(Alignment.CenterHorizontally),
                    addBorder = false
                ) { shareItemBean ->
                    val action = {
                        exportVoiceViewModel.shareVideoByFile(
                            context = context,
                            shareItemBean = shareItemBean,
                            filePath = absolutePath
                        )
                    }
                    if (shareItemBean.type == ShareType.DOWN_LOAD && checkWritePermission().not()) {
                        actionAfterRequestPermission = { action.invoke() }
                        return@HorizontalShareListScreenNew
                    }
                    action.invoke()
                }
            }
        }
    }

    private fun checkWritePermission(): Boolean {
        if (Build.VERSION.SDK_INT < 29 && isPermissionGranted(Manifest.permission.WRITE_EXTERNAL_STORAGE).not()) {
            permissionLauncher.launch(Manifest.permission.WRITE_EXTERNAL_STORAGE)
            return false
        }
        return true
    }

    private fun reportExportActionTracker(exportAction: String) {
        ChatTracker.onClickExportAction(
            isGroup.not(),
            targetId,
            filterId.toString(),
            exportAction
        )
    }

    override fun getTheme(): Int {
        return com.interfun.buz.chat.R.style.BottomSheetDialogBg2NavBarDarkColor
    }

    override fun onDestroyView() {
        stopLoading()
        super.onDestroyView()
    }
}