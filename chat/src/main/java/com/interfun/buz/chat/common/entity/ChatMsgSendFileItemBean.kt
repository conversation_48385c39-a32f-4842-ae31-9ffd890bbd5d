package com.interfun.buz.chat.common.entity

import com.interfun.buz.common.constants.CommonMMKV
import com.lizhi.component.basetool.ntp.NtpTime
import com.lizhi.im5.sdk.message.IMessage

class ChatMsgSendFileItemBean(
    override var msg: IMessage,
    override val msgType: ChatMsgType = ChatMsgType.File,
    private var remainingBytes: Long = 0L,
) : ChatMsgSendMediaItemBean() {
    override fun getRemainingBytes(): Long {
        return remainingBytes
    }

    override fun setRemainingBytes(bytes: Long) {
        remainingBytes = bytes
    }
}