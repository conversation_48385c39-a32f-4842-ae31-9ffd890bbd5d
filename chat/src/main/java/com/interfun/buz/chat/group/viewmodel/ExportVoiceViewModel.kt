package com.interfun.buz.chat.group.viewmodel

import android.content.Context
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.interfun.buz.base.ktx.IconToastStyle
import com.interfun.buz.base.ktx.OneParamCallback
import com.interfun.buz.base.ktx.TwoParamCallback
import com.interfun.buz.base.ktx.asColor
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.base.ktx.launchIO
import com.interfun.buz.base.ktx.toastIconFontMsg
import com.interfun.buz.base.ktx.withMainContext
import com.interfun.buz.biz.center.voicefilter.repository.VoiceFilterDataRepository
import com.interfun.buz.chat.common.view.fragment.ChatExportVoiceFragment
import com.interfun.buz.common.di.standard.IVFVideoGeneration
import com.interfun.buz.common.service.Result
import com.interfun.buz.common.service.STATUS
import com.interfun.buz.common.tracker.trackerString
import com.interfun.buz.common.utils.saveVideoInGallery
import com.interfun.buz.domain.im.social.entity.ShareItemBean
import com.interfun.buz.domain.im.social.entity.ShareType
import com.interfun.buz.domain.record.R
import com.interfun.buz.domain.record.trace.TraceUtils
import com.interfun.buz.domain.social.components.VoiceFilterMessagePreview
import com.interfun.buz.domain.social.helper.ShareHelper
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.GlobalScope
import java.io.File
import javax.inject.Inject
import kotlin.coroutines.cancellation.CancellationException

/**
 * @Desc export voice view model
 * @Author: jasonLum
 * @Date: 2025/6/18
 */
@HiltViewModel
class ExportVoiceViewModel @Inject constructor(
    private val vfVideoGeneration: IVFVideoGeneration,
    private var voiceFilterDataRepository: VoiceFilterDataRepository,
    private val saveHandler: SavedStateHandle,
) : ViewModel() {
    val targetId: String = saveHandler.get<String>(ChatExportVoiceFragment.KEY_TARGET_ID) ?: ""
    val conType: IM5ConversationType by lazy {
        val isGroup =saveHandler.get<Boolean>(ChatExportVoiceFragment.KEY_IS_GROUP)
        if (isGroup == true) {
            IM5ConversationType.GROUP
        }else{
            IM5ConversationType.PRIVATE
        }
    }
    val filterId: Long = saveHandler.get<Long>(ChatExportVoiceFragment.KEY_FILTER_ID) ?: 0L

    companion object {
        private const val TAG = "ExportVoiceViewModel"
    }

    private fun reportShareActionTracker(
        shareType: ShareType,
        isSuccess: Boolean,
        errorMessage: String?,
    ) {
        viewModelScope.launchIO {
            TraceUtils.onResultShareVoiceFilterVideo(
                conType == IM5ConversationType.PRIVATE,
                targetId,
                "export_voice",
                isSuccess,
                shareType,
                filterId,
                errorMessage,
                "${voiceFilterDataRepository.getCachedVoiceFilterInfoById(filterId)?.campaignConfig?.id ?: ""}"
            )
        }
    }

    fun shareVideoByFile(
        context: Context,
        shareItemBean: ShareItemBean,
        filePath: String
    ) {
        launchIO {
            if (shareItemBean.type == ShareType.DOWN_LOAD) {
                saveVideoInGallery(
                    context,
                    File(filePath),
                    onSuccess = {
                        toastIconFontMsg(
                            iconFont = R.string.ic_correct_solid.asString(),
                            message = R.string.chat_media_save_to_photos.asString(),
                            iconFontColor = R.color.text_white_important.asColor(),
                            textColor = R.color.color_text_white_primary.asColor(),
                            style = IconToastStyle.ICON_TOP_TEXT_BOTTOM
                        )
                        reportShareActionTracker(ShareType.DOWN_LOAD, true, null)
                    },
                    onFail = {
                        toastIconFontMsg(
                            iconFont = R.string.ic_warning_solid.asString(),
                            message = R.string.failed_to_save.asString(),
                            iconFontColor = R.color.text_white_important.asColor(),
                            textColor = R.color.color_text_white_primary.asColor(),
                            style = IconToastStyle.ICON_TOP_TEXT_BOTTOM
                        )
                        reportShareActionTracker(ShareType.DOWN_LOAD, false, null)
                    }
                )
            } else {
                val result = ShareHelper.shareVideoByFile(
                    context,
                    shareItemBean.type,
                    filePath,false
                )
                val isSuccess = result == Result(STATUS.NORMAL)
                reportShareActionTracker(shareItemBean.type, isSuccess, null)
            }
        }
    }

    private fun reportVideoResult(isSuccess: Boolean, rCode: String?, stopReason: String?) {
        GlobalScope.launchIO {
            com.interfun.buz.domain.record.helper.TraceUtils.resultBackRB2025061901(
                chatType = conType.trackerString,
                chatId = targetId.toString(),
                source = "export_voice",
                eventId = "${voiceFilterDataRepository.getCachedVoiceFilterInfoById(filterId)?.campaignConfig?.id ?: ""}",
                voiceFilterId = "$filterId",
                isSuccess = isSuccess,
                stopReason = stopReason,
                failReason = rCode
            )
        }
    }

    fun generateVideo(
        templateUrl: String,
        templateMd5: String,
        duration: Int,
        audioUrl: String,
        placeHolderView: VoiceFilterMessagePreview,
        onSuccess: OneParamCallback<File>,
        onFailed: OneParamCallback<String?>
    ) {
        launchIO {
            try {
                placeHolderView.waitForImageLoad()
                val file = vfVideoGeneration.generateVideo(
                    templateUrl,
                    templateMd5,
                    duration,
                    audioUrl,
                    placeHolderView
                )

                withMainContext {
                    if (!file.exists() || file.length() <= 0) {
                        onFailed.invoke("File not exist!")
                    } else {
                        onSuccess.invoke(file)
                    }
                }
                reportVideoResult(true, null, null)
            } catch (e: Exception) {
                if (e is CancellationException) {
                    reportVideoResult(false, e.message, "cancel")
                }else{
                    reportVideoResult(false, e.message, "others")
                }
                withMainContext {
                    onFailed.invoke(e.message)
                    e.printStackTrace()
                }

            }
        }
    }
}