package com.interfun.buz.chat.common.view.item

import com.interfun.buz.base.ktx.BindingViewHolder
import com.interfun.buz.chat.common.entity.ChatMsgItemPayloadType
import com.interfun.buz.chat.common.entity.ChatTimeItemBean
import com.interfun.buz.chat.databinding.ChatItemMsgTimeBinding
import com.interfun.buz.common.base.binding.BaseBindingDelegate
import com.interfun.buz.common.utils.TimeUtils

/**
 * <AUTHOR>
 * @date 2022/11/26
 * @desc
 */
class ChatMsgTimeItemView : BaseBindingDelegate<ChatTimeItemBean, ChatItemMsgTimeBinding>() {
    override fun onBindViewHolder(binding: ChatItemMsgTimeBinding, item: ChatTimeItemBean, position: Int) {
        updateMsgTime(binding, item.time)
    }

    override fun onBindViewHolder(
        holder: BindingViewHolder<ChatItemMsgTimeBinding>,
        item: ChatTimeItemBean,
        payloads: List<Any>
    ) {
        val binding = holder.binding
        if (payloads.isEmpty()) {
            onBindViewHolder(binding, item, holder.absoluteAdapterPosition)
        } else {
            payloads.forEach {
                if (it == ChatMsgItemPayloadType.UpdateMsgTime) {
                    updateMsgTime(binding, item.time)
                }
            }
        }
    }

    private fun updateMsgTime(binding: ChatItemMsgTimeBinding, time: Long) {
        binding.tvMsgTime.text = TimeUtils.formatToMsgTime(time,false)
    }
}