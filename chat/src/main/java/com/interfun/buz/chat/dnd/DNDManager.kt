package com.interfun.buz.chat.DND

import android.app.NotificationManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import com.interfun.buz.base.ktx.log
import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.common.manager.userLifecycleOwner
import com.interfun.buz.common.manager.userLifecycleScope
import com.lizhi.component.itnet.base.getAppContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch

/**
 * <AUTHOR>
 * @date 2024/7/30
 * @desc
 */
@Deprecated(message = "use UserSettingRepository#inSystemDndFlow instead")
object DNDManager {
    val TAG = "DNDManager"
    
    private var mReceiver: DNDReceiver? = null
    private var _isInDND: Boolean? = null
    val isInDND: Boolean
        get() {
            return if (_isInDND == null) {
                obtainDNDStateFromSystemService()
            } else {
                _isInDND == true
            }
        }

    @Deprecated(message = "use UserSettingRepository#inSystemDndFlow instead")
    val isInDNDFlow by lazy { MutableStateFlow(isInDND) }

    fun login() {
        registerReceiver()
    }

    private fun registerReceiver(){
        if (mReceiver != null){
            unRegisterListener()
        }
        mReceiver = DNDReceiver()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val filter = IntentFilter()
            filter.addAction(NotificationManager.ACTION_INTERRUPTION_FILTER_CHANGED)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                getAppContext().registerReceiver(mReceiver, filter, Context.RECEIVER_NOT_EXPORTED)
            } else {
                getAppContext().registerReceiver(mReceiver, filter)
            }
        }
        log(TAG, "DNDManager#registerReceiver")
    }


    private fun unRegisterListener(){
        try {
            mReceiver?.let {
                getAppContext().unregisterReceiver(it)
                log(TAG, "DNDManager#unRegisterListener")
            }
            mReceiver = null
        } catch (e: Exception) {
            log(TAG, "DNDManager#unRegisterListener Exception")
        }
    }


    fun logout(){
        unRegisterListener()
    }

    private fun obtainDNDStateFromSystemService() : Boolean {
        val notificationManager = getAppContext().getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) { // android 7.0后才正式有勿扰模式，
            val interruptionFilter: Int = notificationManager.getCurrentInterruptionFilter()
            _isInDND = interruptionFilter != NotificationManager.INTERRUPTION_FILTER_ALL
            logInfo(TAG, "is in DND?  $_isInDND, the interruptionFilter= ${interruptionFilter}}")
            return _isInDND == true
        }
        return false
    }

    
    class DNDReceiver: BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            obtainDNDStateFromSystemService()
            userLifecycleScope?.launch {
                logDebug(TAG, "emit the isInDND $isInDND， userLifecycleOwner currentState${userLifecycleOwner.lifecycle.currentState}")
                isInDNDFlow.emit(isInDND)
            }
        }
    }
}