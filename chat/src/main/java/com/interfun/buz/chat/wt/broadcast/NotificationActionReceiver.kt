package com.interfun.buz.chat.wt.broadcast

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.chat.common.utils.DNDTracker
import com.interfun.buz.chat.wt.manager.WTQuietModeManager
import com.interfun.buz.chat.wt.manager.WTStatusManager
import com.interfun.buz.chat.wt.utils.ForegroundNotificationTracker

/**
 * <AUTHOR> <PERSON>
 * @date 2024/8/8
 * @desc 通知栏DND开关
 */
class NotificationActionReceiver  : BroadcastReceiver() {
    companion object {
        val TAG = "NotificationActionReceiver"
        val ACTION_SWITCH_CHANGED = "com.interfun.buz.notification.NotificationActionReceiver"
        val ACTION_CLOSE_NOTIFICATION = "com.interfun.buz.notification.NotificationActionReceiver.CloseNotification"
        val ACTION_QUIT_STATE_CHANGE = "com.interfun.buz.notification.NotificationActionReceiver.ChangeQuitState"
        val FROM_SROUCE_TYPE: String = "from_source_type"
        val QUIT_STATE = "quit_state"
        val TYPE_FOREGROUND_NOTIFICATION_AUTO_PLAY = 0
    }


    override fun onReceive(context: Context?, intent: Intent?) {
        if (intent != null) {
            val action = intent.action
            logDebug(TAG,"the action: $action")
            if (ACTION_SWITCH_CHANGED.equals(action)) {
                // 处理Switch状态变化的逻辑
                val fromType = intent.getIntExtra(FROM_SROUCE_TYPE, -1)
                when (fromType) {
                    TYPE_FOREGROUND_NOTIFICATION_AUTO_PLAY -> {
                        val newQuietModeEnable = !WTQuietModeManager.isQuietModeEnable
                        //WTQuietModeManager.cacheLatestQuietModeByUserSwitch(newQuietModeEnable)
                        WTQuietModeManager.setQuietModeEnable(newQuietModeEnable)
                        DNDTracker.onClickAC2024081501(newQuietModeEnable)
                    }
                }
            }

            if (ACTION_CLOSE_NOTIFICATION == action){
                ForegroundNotificationTracker.onClickNotificationCancel("quit")
                WTStatusManager.requestChangeSwitchStatus(false)
                WTStatusManager.stopForegroundService()
            }

            if (ACTION_QUIT_STATE_CHANGE == action){
                val state = intent.getBooleanExtra(QUIT_STATE, false)
                logInfo(TAG,"onReceive: intent state = ${intent.getBooleanExtra(QUIT_STATE,true)}, state = $state")
                if (state) {
                    ForegroundNotificationTracker.onClickNotificationCancel("cancel")
                } else {
                    ForegroundNotificationTracker.onClickNotificationCancel("stay")
                }
                WTStatusManager.updateDndInQuitTipState(state)
            }
        }
    }
}