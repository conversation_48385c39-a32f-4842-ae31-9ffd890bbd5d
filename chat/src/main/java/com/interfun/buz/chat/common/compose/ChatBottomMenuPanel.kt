package com.interfun.buz.chat.common.compose

import android.view.View
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.SideEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import com.interfun.buz.chat.R
import com.interfun.buz.chat.common.entity.BottomPanelContentType
import com.interfun.buz.chat.common.entity.BottomPanelContentType.*
import com.interfun.buz.compose.ktx.pxToDp
import com.interfun.buz.compose.ktx.rememberMutableInt
import com.interfun.buz.compose.modifier.applyIf
import com.interfun.buz.compose.modifier.debouncedClickable
import com.interfun.buz.compose.utils.RecomposeCountHelper
import com.interfun.buz.core.widget_record.ui.action.RecordScreenAction
import com.interfun.buz.domain.chat.repo.ChatMorePanelAction
import com.interfun.buz.domain.chat.ui.MorePanelConfig
import com.interfun.buz.domain.chat.viewmodel.MorePanelOptionUiState

@Composable
fun ColumnScope.ChatBottomMenuPanel(
    inputTextView: () -> View,
    optionList: () -> List<MorePanelOptionUiState>,
    vePanelHeight: () -> Int,
    isRecordingLambda: () -> Boolean,
    showVEButtonRedDot: () -> Boolean,
    showMoreButtonRedDot: () -> Boolean,
    showVoiceButtonRedDot: () -> Boolean,
    showVFEntryRedDot: () -> Boolean,
    isInVoiceFilterMode: () -> Boolean,
    selectedPanelLambda: () -> BottomPanelContentType,
    onChatPanelAction: (ChatMorePanelAction) -> Unit,
    onRecordScreenAction: (RecordScreenAction) -> Unit,
    onSelectPanel: (BottomPanelContentType) -> Unit,
) {
    SideEffect {
        RecomposeCountHelper.increment("ChatBottomMenuPanel")
    }
    ChatBottomMenuBar(
        inputTextView = inputTextView,
        showMoreButtonEntry = { optionList().isNotEmpty() },
        isRecordingLambda = isRecordingLambda,
        showVEButtonRedDot = showVEButtonRedDot,
        showMoreButtonRedDot = showMoreButtonRedDot,
        showVoiceButtonRedDot = showVoiceButtonRedDot,
        isInVoiceFilterMode = isInVoiceFilterMode,
        selectedPanelLambda = selectedPanelLambda,
        onSelectPanel = onSelectPanel
    )

    ChatBottomPanelSwitcher(
        selectedPanelLambda = selectedPanelLambda,
        vePanelHeight = vePanelHeight,
        optionList = optionList,
        showVFEntryRedDot = showVFEntryRedDot,
        onChatPanelAction = onChatPanelAction,
        onRecordScreenAction = onRecordScreenAction,
    )
}

@Composable
private fun ChatBottomMenuBar(
    modifier: Modifier = Modifier,
    inputTextView: () -> View,
    isRecordingLambda: () -> Boolean,
    showMoreButtonEntry: () -> Boolean,
    showVEButtonRedDot: () -> Boolean,
    showMoreButtonRedDot: () -> Boolean,
    showVoiceButtonRedDot: () -> Boolean,
    isInVoiceFilterMode: () -> Boolean,
    selectedPanelLambda: () -> BottomPanelContentType,
    onSelectPanel: (BottomPanelContentType) -> Unit
) {
    SideEffect {
        RecomposeCountHelper.increment("ChatBottomMenuBar")
    }
    Box(
        modifier = modifier
            .fillMaxWidth()
            .wrapContentHeight()
    ) {
        val alphaAnim by animateFloatAsState(
            targetValue = if (isRecordingLambda()) 0f else 1f
        )
        var contentHeight by rememberMutableInt()
        Row(
            modifier = modifier
                .background(colorResource(R.color.color_background_4_default))
                .fillMaxWidth()
                .wrapContentHeight()
                .padding(horizontal = 7.dp)
                .graphicsLayer {
                    alpha = alphaAnim
                }
                .onSizeChanged {
                    contentHeight = it.height
                },
            verticalAlignment = Alignment.Bottom,
        ) {
            ChatBottomMenuButton(
                modifier = Modifier.debouncedClickable {
                    onSelectPanel(VE_PANEL)
                },
                iconResLambda = { R.string.ic_voice_moji },
                showRedDot = showVEButtonRedDot,
                isSelectedLambda = { selectedPanelLambda() == VE_PANEL },
            )

            AndroidView(
                modifier = Modifier
                    .weight(1f)
                    .wrapContentHeight()
                    .padding(horizontal = 5.dp, vertical = 6.dp),
                factory = { inputTextView() }
            )

            ChatBottomMenuButton(
                modifier = Modifier.debouncedClickable {
                    onSelectPanel(MORE_PANEL)
                },
                iconResLambda = { R.string.ic_add },
                showRedDot = showMoreButtonRedDot,
                isSelectedLambda = { selectedPanelLambda() == MORE_PANEL },
                visibleLambda = { showMoreButtonEntry() },
            )

            ChatBottomMenuButton(
                modifier = Modifier.pointerInput(Unit) {
                    detectTapGestures(
                        onLongPress = {
                            if (selectedPanelLambda() != VOICE_RECORD_PANEL) {
                                onSelectPanel(VOICE_RECORD_PANEL)
                            }
                        },
                        onTap = {
                            onSelectPanel(VOICE_RECORD_PANEL)
                        }
                    )
                },
                iconResLambda = {
                    if (isInVoiceFilterMode()) R.string.ic_wt_speaking else R.string.ic_mic_open
                },
                showRedDot = showVoiceButtonRedDot,
                isSelectedLambda = { selectedPanelLambda() == VOICE_RECORD_PANEL },
            )
        }
        MenuBarMask(
            menuBarHeightLambda = { contentHeight },
            isRecordingLambda = isRecordingLambda
        )
    }
}

@Composable
fun ChatBottomPanelSwitcher(
    selectedPanelLambda: () -> BottomPanelContentType,
    vePanelHeight: () -> Int,
    showVFEntryRedDot: () -> Boolean,
    optionList: () -> List<MorePanelOptionUiState>,
    onChatPanelAction: (ChatMorePanelAction) -> Unit,
    onRecordScreenAction: (RecordScreenAction) -> Unit,
) {
    SideEffect {
        RecomposeCountHelper.increment("ChatBottomPanelSwitcher")
    }
    when (selectedPanelLambda()) {
        VE_PANEL -> {
            // 设置VE面板的高度，高度的变化随着拖拽时改变，此方法为了达到以下效果：
            // 切换不同面板之间，ChatBottomMenuBar 会根据面板的不同高度，有从高度 a -> b 的动画
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(vePanelHeight().pxToDp())
            )
        }

        MORE_PANEL -> {
            val rowGap = 4.dp
            val morePanelConfig = MorePanelConfig(
                column = 4,
                rowGap = rowGap,
                bgColor = R.color.color_background_4_default,
                minPanelHeight = 86.dp * 2 + rowGap
            )
            MorePanelInChat(
                optionList = optionList,
                showVFEntryRedDot = showVFEntryRedDot,
                morePanelConfig = morePanelConfig,
                onChatPanelAction = onChatPanelAction
            )
        }

        VOICE_RECORD_PANEL -> {
            ChatVoiceRecordPanel(
                showVFEntryRedDot = showVFEntryRedDot,
                onRecordScreenAction = onRecordScreenAction,
            )
        }

        NO_PANEL -> {}
    }
}

@Composable
private fun MenuBarMask(
    modifier: Modifier = Modifier,
    menuBarHeightLambda: () -> Int,
    isRecordingLambda: () -> Boolean,
) {
    val heightDp = menuBarHeightLambda().pxToDp()
    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(heightDp)
            .applyIf(isRecordingLambda()) {
                this.clickable {}
            }
    )
}
