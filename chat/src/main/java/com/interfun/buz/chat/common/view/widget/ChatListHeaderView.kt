package com.interfun.buz.chat.common.view.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import com.interfun.buz.base.ktx.asColor
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.base.ktx.click
import com.interfun.buz.base.ktx.gone
import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.base.ktx.visible
import com.interfun.buz.base.ktx.visibleIf
import com.interfun.buz.chat.R
import com.interfun.buz.chat.databinding.ChatListHeaderViewBinding
import com.interfun.buz.common.database.entity.chat.GroupInfoBean
import com.interfun.buz.common.ktx.isMe
import com.interfun.buz.common.manager.user.UserOnlineStatusCompat
import com.interfun.buz.common.widget.button.CommonButton
import com.interfun.buz.social.entity.OfflineState
import com.interfun.buz.social.entity.OnlineState
import com.interfun.buz.social.entity.UserStateInfo

class ChatListHeaderView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    private val binding = ChatListHeaderViewBinding.inflate(LayoutInflater.from(context), this)
    val quiteLightView = binding.ivQuiteLight
    val composeRightEntry = binding.composeRightEntry

    init {
        binding.groupPagLoading.gone()
    }

    fun onClickHeaderHandle(action: () -> Unit) {
        binding.iftvHeaderHandle.click {
            action.invoke()
        }
    }

    fun onClickTitleContent(action: () -> Unit) {
        binding.vTitleContentBg.click {
            action.invoke()
        }
    }

    fun setUserPortrait(portraitUrl: String?) {
        binding.ivPortrait.setPortrait(portraitUrl)
    }

    fun setGroupPortrait(groupInfoBean: GroupInfoBean, allowHardware: Boolean = false) {
        binding.ivPortrait.setGroupInfoBean(groupInfoBean, allowHardware = allowHardware)
    }

    fun updateGreenDotStatus(userStatusInfo: UserOnlineStatusCompat?, isRobot: Boolean, userId: Long) {
        val isQuietModeEnable = userStatusInfo?.isInQuiteMode ?: false
        val showDot = (userStatusInfo?.isOnline?:false) && isRobot.not() && userId.isMe().not()
        if (!showDot) return
        updateGreenDotVisibility(true)
        binding.viewGreenDot.setBackgroundResource(
            if (isQuietModeEnable) R.drawable.common_oval_secondary_purple
            else R.drawable.common_oval_basic_primary
        )
    }

    // 设置用户状态
    fun updateUserState(userStateInfo: UserStateInfo?) {
        val tvUserState = binding.tvUserState
        logDebug("updateUserState", "userStateInfo=$userStateInfo")
        when (userStateInfo) {
            null,
            is OfflineState.MoreThan48Hour -> {
                tvUserState.gone()
            }

            is OnlineState.AutoPlay -> {
                tvUserState.visible()
                tvUserState.setTextColor(R.color.color_text_highlight_default.asColor())
                tvUserState.text = R.string.user_state_online_auto_play.asString()
            }

            is OnlineState.Quiet -> {
                tvUserState.visible()
                tvUserState.setTextColor(R.color.color_foreground_dnd_default.asColor())
                tvUserState.text = R.string.user_state_online_quiet.asString()
            }

            is OfflineState.WithinADay -> {
                tvUserState.visible()
                tvUserState.setTextColor(R.color.text_white_secondary.asColor())
                tvUserState.text = R.string.user_state_offline_within_a_day.asString()
            }

            is OfflineState.LessThan60Minute -> {
                tvUserState.visible()
                tvUserState.setTextColor(R.color.text_white_secondary.asColor())
                tvUserState.text = if (userStateInfo.minutes == 1)
                    R.string.user_state_offline_within_one_minute.asString()
                else
                    String.format(
                        R.string.user_state_offline_within_60_minute.asString(),
                        userStateInfo.minutes
                    )
            }

            is OfflineState.LessThan8Hour -> {
                tvUserState.visible()
                tvUserState.setTextColor(R.color.text_white_secondary.asColor())
                tvUserState.text = if (userStateInfo.hours == 1)
                    R.string.user_state_offline_within_one_hour.asString()
                else
                    String.format(
                        R.string.user_state_offline_within_8_hour.asString(),
                        userStateInfo.hours
                    )
            }

            is OfflineState.Yesterday -> {
                tvUserState.visible()
                tvUserState.setTextColor(R.color.text_white_secondary.asColor())
                tvUserState.text = R.string.user_state_offline_yesterday.asString()
            }

        }
    }

    fun updateMemberCount(count: Int) {
        if (count > 0) {
            binding.tvUserState.visible()
            binding.tvUserState.setTextColor(R.color.color_text_highlight_default.asColor())
            if (count == 1) {
                binding.tvUserState.text = R.string.group_online_member_count_one.asString()
            } else {
                binding.tvUserState.text = String.format(
                    R.string.group_online_member_count_more_than_one.asString(),
                    count
                )
            }
        } else {
            binding.tvUserState.gone()
        }
    }


    fun updateGreenDotVisibility(show: Boolean) {
        binding.groupGreenDotBg.visibleIf(show)
    }

    fun updateOfficialTagVisibility(show: Boolean) {
        binding.iftvOfficialTag.visibleIf(show)
    }

    fun setHeaderName(name: CharSequence?) {
        binding.tvName.text = name
    }

    fun updateHeaderMuteStatus(muteAutoPlay: Boolean?, muteNotification: Boolean?) {
        if (muteAutoPlay != null) {
            binding.iftvMuteAutoPlay.visibleIf(muteAutoPlay)
        }
        if (muteNotification != null) {
            binding.iftvMuteNotification.visibleIf(muteNotification)
        }
    }

    fun handleRemoteLoading(isPrivate: Boolean, showLoading: Boolean) {
        if (isPrivate) {
            binding.groupPagLoading.gone()
            return
        }
        if (showLoading) {
            binding.groupPagLoading.setColorType(CommonButton.COLOR_TERTIARY)
            binding.groupPagLoading.visible()
            binding.groupPagLoading.startLoading()
        } else {
            binding.groupPagLoading.gone()
            binding.groupPagLoading.stopLoading()
        }
    }

    fun setLiveStatus(show: Boolean) {
        binding.viewLivePlaceCircle.visibleIf(show)
    }


}