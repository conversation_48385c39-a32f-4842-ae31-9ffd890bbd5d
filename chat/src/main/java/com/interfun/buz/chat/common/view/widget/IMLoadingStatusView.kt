package com.interfun.buz.chat.common.view.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.LifecycleCoroutineScope
import com.interfun.buz.base.ktx.asColor
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.base.ktx.gone
import com.interfun.buz.base.ktx.invisible
import com.interfun.buz.base.ktx.visible
import com.interfun.buz.chat.R
import com.interfun.buz.chat.databinding.ChatImLoadingStatusLayoutBinding
import com.interfun.buz.common.widget.button.CommonButton
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.LazyThreadSafetyMode.NONE

/**
 * Author: ChenYouSheng
 * Date: 2023/6/30
 * Email: <EMAIL>
 * Desc:
 */
class IMLoadingStatusView @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null) :
    ConstraintLayout(context, attrs) {

    private val binding by lazy(NONE) {
        ChatImLoadingStatusLayoutBinding.inflate(LayoutInflater.from(context), this, true).apply {
            loadingView.alpha = 0.6f
        }
    }
    private var jobShowLoadingDelay: Job? = null
    private var isShown = false

    fun showLoading(scope: LifecycleCoroutineScope) {
        isShown = true
        jobShowLoadingDelay = scope.launch(Dispatchers.Main) {
            delay(CommonButton.SHOW_LOADING_DELAY)
            showLoadingView(R.string.common_loading.asString())
        }
    }

    fun showConnecting(scope: LifecycleCoroutineScope) {
        isShown = true
        jobShowLoadingDelay = scope.launch(Dispatchers.Main) {
            delay(CommonButton.SHOW_LOADING_DELAY)
            showLoadingView(R.string.common_connecting.asString())
        }
    }

    fun hide() {
        isShown = false
        jobShowLoadingDelay?.cancel()
        binding.loadingView.invisible()
        binding.loadingView.pauseAnimation()
        gone()
    }

    private fun showLoadingView(text: String) {
        if (isShown.not()) return
        binding.loadingView.visible()
        if (!binding.loadingView.isAnimating) {
            binding.loadingView.playAnimation()
        }
        visible()
    }
}