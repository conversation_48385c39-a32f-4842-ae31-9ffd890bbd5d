package com.interfun.buz.chat.wt.manager

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.media.AudioDeviceCallback
import android.media.AudioDeviceInfo
import android.media.AudioManager
import android.os.Build
import androidx.annotation.RequiresApi
import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.common.constants.TrackConstant
import com.interfun.buz.common.manager.userLifecycleScope
import com.interfun.buz.common.utils.BuzTracker
import com.lizhi.component.itnet.base.getAppContext
import com.yibasan.lizhifm.lzlogan.Logz
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.onSubscription
import kotlinx.coroutines.launch

enum class HeadsetType{
    Wired, Bluetooth
}
data class HeadsetData(val type: HeadsetType) {

    override fun equals(other: Any?): Boolean {
        if (other === this) {
            return true
        }
        if (other !is HeadsetData) {
            return false
        }

        return this.type == (other as? HeadsetData)?.type
    }

    override fun hashCode(): Int {
        return type.hashCode()
    }
}

fun HeadsetData?.isWired(): Boolean {
    return this?.type == HeadsetType.Wired
}

fun HeadsetData?.isBluetooth(): Boolean {
    return this?.type == HeadsetType.Bluetooth
}

object DriveModelManager {
    val TAG = "DriveModelManager"
    private var audioDeviceCallback: AudioDeviceCallback? = null
    private var _headsetData: HeadsetData? = null
    val headsetData: HeadsetData?
        get() {
            if (_headsetData == null) {
                refreshHeadsetData()
            }
            return _headsetData
        }

    val headsetUpdateFlow by lazy { MutableSharedFlow<HeadsetData?>() }
    val headsetFlow = headsetUpdateFlow.onSubscription { emit(headsetData) }
    // 当正在播放音频时拔出耳机响应
    val unplugEarphonesWhenPlayingFlow by lazy { MutableSharedFlow<Boolean>() }

    var hasRegister = false

    fun login() {

         if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
             val audioManager = getAppContext().getSystemService(Context.AUDIO_SERVICE) as? AudioManager
             if (audioDeviceCallback != null) {
                 audioManager?.unregisterAudioDeviceCallback(audioDeviceCallback)
             }
             val devices = audioManager?.getDevices(AudioManager.GET_DEVICES_OUTPUTS)
             report(devices?.toList() ?: emptyList())
             audioDeviceCallback = object : AudioDeviceCallback() {
                 override fun onAudioDevicesAdded(addedDevices: Array<out AudioDeviceInfo>) {
                     super.onAudioDevicesAdded(addedDevices)
                     report(addedDevices.toList())
                     refreshHeadsetData()
                     emitHeadsetFlowData()
                 }

                 override fun onAudioDevicesRemoved(removedDevices: Array<out AudioDeviceInfo>) {
                     super.onAudioDevicesRemoved(removedDevices)
                     logDebug("onAudioDevicesRemoved")
                     refreshHeadsetData()
                     emitHeadsetFlowData()
                 }
             }
             audioManager?.registerAudioDeviceCallback(audioDeviceCallback, null)
         }
         registerReceiver()
    }

    @RequiresApi(Build.VERSION_CODES.M)
    fun report(devices: List<AudioDeviceInfo>) {
        devices
            .distinctBy { it.productName }
            .forEach { info ->
                Logz.tag("DriveModelBlock").d("AudioDeviceInfo productName ${info.productName} type ${info.type}")
                BuzTracker.onResult {
                    put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2023110701")
                    put(TrackConstant.KEY_RESULT_TYPE, "connect_bluetooth")
                    put(TrackConstant.KEY_CONTENT_ID, "${info.type} ")
                    put(TrackConstant.KEY_CONTENT_NAME, info.productName.toString())
                    put(TrackConstant.KEY_IS_SUCCESS, "success")
                }
            }
    }

     fun logout() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (audioDeviceCallback != null ) {
                val audioManager = getAppContext().getSystemService(Context.AUDIO_SERVICE) as? AudioManager
                audioManager?.unregisterAudioDeviceCallback(audioDeviceCallback)
                audioDeviceCallback = null
            }
        }
         unregisterReceiver()
    }


    private fun refreshHeadsetData() {
        _headsetData = null
        val audioManager = getAppContext().getSystemService(Context.AUDIO_SERVICE) as? AudioManager
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val devices = audioManager?.getDevices(AudioManager.GET_DEVICES_OUTPUTS)
            devices?.let {
                for (device in devices) {
                    if (device.type == AudioDeviceInfo.TYPE_BLUETOOTH_SCO || device.type == AudioDeviceInfo.TYPE_BLUETOOTH_A2DP) {
                        _headsetData = HeadsetData(HeadsetType.Bluetooth)
                        return
                    } else if ( device.type == AudioDeviceInfo.TYPE_WIRED_HEADSET || device.type == AudioDeviceInfo.TYPE_WIRED_HEADPHONES || device.type == AudioDeviceInfo.TYPE_USB_HEADSET) {
                        _headsetData = HeadsetData(HeadsetType.Wired)
                        return
                    }
                }
            }

        } else {
            if (audioManager?.isBluetoothScoOn == true || audioManager?.isBluetoothA2dpOn == true) {
                _headsetData = HeadsetData(HeadsetType.Bluetooth)
            } else if (audioManager?.isWiredHeadsetOn == true) {
                _headsetData = HeadsetData(HeadsetType.Wired)
            }
        }
    }

    private var mReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (AudioManager.ACTION_AUDIO_BECOMING_NOISY == intent?.action) {
                logDebug(TAG," 有线耳机拔出")
                emitUnplugEarphonesFlowData(true)
            }
        }
    }

    private fun registerReceiver() {
        if (!hasRegister) {
            val filter = IntentFilter()
            //监听有线耳机的两种方式
            filter.addAction(AudioManager.ACTION_AUDIO_BECOMING_NOISY)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                appContext.registerReceiver(mReceiver, filter, Context.RECEIVER_NOT_EXPORTED)
            } else {
                appContext.registerReceiver(mReceiver, filter)
            }

            hasRegister = true
        }
    }

    private fun unregisterReceiver() {
        if (hasRegister) {
            hasRegister = false
            try {
                appContext.unregisterReceiver(mReceiver)
            } catch (e: Exception) {
                e.printStackTrace()
                logInfo(TAG, "unregisterReceiver exception")
            }

        }
    }

    private fun emitHeadsetFlowData() {
        userLifecycleScope?.launch {
            headsetUpdateFlow.emit(_headsetData)
        }
    }

    private fun emitUnplugEarphonesFlowData(value: Boolean) {
        userLifecycleScope?.launch {
            unplugEarphonesWhenPlayingFlow.emit(value)
        }
    }

}