package com.interfun.buz.chat.group.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import com.buz.idl.group.bean.GroupInfo
import com.interfun.buz.base.ktx.launchIO
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.chat.common.repository.ChatGlobalRepository
import com.interfun.buz.chat.common.viewmodel.ChatViewModel
import com.interfun.buz.common.bean.chat.ChatJumpType
import com.interfun.buz.common.bean.chat.GroupChatJumpInfo
import com.interfun.buz.common.constants.JUMP_INFO
import com.interfun.buz.common.constants.RouterParamKey
import com.interfun.buz.common.database.entity.UserRelationInfo
import com.interfun.buz.common.manager.cache.ai.GroupBotDataHelper
import com.interfun.buz.social.entity.SimpleBuzUser
import com.interfun.buz.social.repo.GroupMemberInfo
import com.interfun.buz.social.repo.GroupMembersRepository
import com.interfun.buz.social.repo.GroupOnlineMembersRepository
import com.interfun.buz.social.repo.GroupRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * <AUTHOR>
 * @date 2022/8/12
 * @desc
 */
@HiltViewModel
class GroupChatViewModel @Inject constructor(
    private val groupMembersRepository: GroupMembersRepository,
    private val groupRepository: GroupRepository,
    private val savedStateHandle: SavedStateHandle,
    private val groupOnlineMembersRepository: GroupOnlineMembersRepository,
    private val chatGlobalRepository: ChatGlobalRepository,
): ChatViewModel() {

    private val jumpInfo = savedStateHandle.get<Any>(RouterParamKey.Chat.JUMP_INFO) as? GroupChatJumpInfo
    val groupId = jumpInfo?.groupId

    val groupInfoFlow = if (groupId == null) {
        MutableStateFlow(null)
    } else groupRepository.getGroupCompositeFlow(groupId)
        .stateIn(viewModelScope, SharingStarted.Eagerly, null)

    private val _serverGroupInfoLiveData = MutableLiveData<GroupInfo>()
    val serverGroupInfoLiveData: LiveData<GroupInfo> = _serverGroupInfoLiveData

    private val _botMemberStateFlow: MutableStateFlow<List<UserRelationInfo>?> = MutableStateFlow(null)
    val botMemberStateFlow = _botMemberStateFlow.asStateFlow()

    private val _groupUserMemberRefreshFlow = MutableStateFlow(0)
    val groupUserMemberRefreshFlow: StateFlow<Int> = _groupUserMemberRefreshFlow
    val isGroupOnlineFlow =
        groupOnlineMembersRepository.getOnlineGroupMembersFromCache(groupId ?: 0L)
            .map { (it?.onlineMemberCount ?: 0) > 1 }
            .stateIn(viewModelScope, SharingStarted.Lazily, false)

    val onlineMemberCountFlow =
        groupOnlineMembersRepository.getOnlineGroupMembersFromCache(groupId ?: 0L)
            .map { it?.onlineMemberCount ?: 0 }
            .stateIn(viewModelScope, SharingStarted.Lazily, 0)

    fun getConvTargetId(): Long = jumpInfo?.groupId ?: 0L
    fun getGroupJumpType(): ChatJumpType? = jumpInfo?.type
    fun getGroupSerMsgId(): String? = jumpInfo?.serMsgId
    fun getGroupAddressUserInfo(): UserRelationInfo? = jumpInfo?.addressUserInfo
    fun getGroupReactionOpUserId(): String? = jumpInfo?.reactionOpUserId
    fun getGroupMsgId(): Long? = jumpInfo?.msgId

    /** Sync group member list from server, & save to database */
    fun syncGroupUserMemberList(groupId: Long) {
        viewModelScope.launch {
            groupMembersRepository.syncGroupMemberList(
                groupId = groupId
            )
        }
    }

    fun postChatHistoryPageAddressBot(groupId: Long, bot: UserRelationInfo?) {
        viewModelScope.launch {
            val buzUser = if (bot == null) null else SimpleBuzUser(
                bot.userId,
                bot.userName ?: "${bot.userId}",
                bot.portrait
            )
            chatGlobalRepository.postChatHistoryPageAddressBot(groupId, buzUser)
        }
    }

    /** Get group member list from database */
    @OptIn(ExperimentalCoroutinesApi::class)
    fun getGroupUserMemberStateFlow(groupId: Long): StateFlow<List<GroupMemberInfo>> {
        return _groupUserMemberRefreshFlow.flatMapLatest {
            groupMembersRepository.getGroupUserMemberFromCacheFlow(groupId)
        }.stateIn(viewModelScope, SharingStarted.Eagerly, emptyList())
    }

    /** Get group bot list from database */
    fun getGroupBotMemberFromCache(groupId: Long) {
        viewModelScope.launch(Dispatchers.IO) {
            val botList = GroupBotDataHelper.getFromCache(groupId)
            _botMemberStateFlow.emit(botList)
        }
    }

    /** Emit signal to refresh cached data from group user member database */
    fun updateGroupMemberListCache() {
        _groupUserMemberRefreshFlow.value += 1
    }
}
