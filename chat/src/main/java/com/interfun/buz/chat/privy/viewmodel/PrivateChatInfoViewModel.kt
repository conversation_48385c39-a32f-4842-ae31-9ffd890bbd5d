package com.interfun.buz.chat.privy.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.interfun.buz.base.ktx.launchIO
import com.interfun.buz.common.database.entity.UserRelationInfo
import com.interfun.buz.common.ktx.isRobot
import com.interfun.buz.common.manager.MuteInfoManager
import com.interfun.buz.common.manager.cache.user.UserRelationCacheManager
import com.interfun.buz.social.entity.UserStateInfo
import com.interfun.buz.social.repo.BotRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import javax.inject.Inject
import com.interfun.buz.social.repo.UserOnlineStatusRepository
import com.interfun.buz.social.repo.UserRepository

@HiltViewModel
class PrivateChatInfoViewModel @Inject constructor(
    val botRepository: BotRepository,
    val userRepository: UserRepository,
    val userStateRepository: UserOnlineStatusRepository
) : ViewModel() {
    val userInfoFlow = MutableStateFlow<UserRelationInfo?>(null)
    val isMuteMsgFlow = MutableStateFlow(false)
    val isMuteNotificationFlow = MutableStateFlow(false)
    val botInfoFlow = channelFlow {
        userInfoFlow.map { if (it?.isRobot != true) null else it.userId }
            .distinctUntilChanged().collectLatest {
                if (it == null) {
                    send(null)
                } else {
                    botRepository.getBotExtraFlow(it).collect { botExtra ->
                        send(botExtra)
                    }
                }
            }
    }.stateIn(viewModelScope, SharingStarted.WhileSubscribed(5000L), null)

    fun requestUserInfo(userId: Long) {
        launchIO {
            val info = UserRelationCacheManager.getUserRelationInfoByUidSync(userId)
            userInfoFlow.emit(info)
        }
    }

    fun requestMuteInfo(userId: Long){
        launchIO {
            val isMute = MuteInfoManager.getUserMuteInfo(userId, "PrivateChatFragment").isMuteMessages
            isMuteMsgFlow.emit(isMute)

            val isMuteNotification = MuteInfoManager.getUserMuteInfo(userId, "PrivateChatFragment").isMuteNotification
            isMuteNotificationFlow.emit(isMuteNotification)
        }
    }

    fun getUserStateInfoFlow(userId: Long): Flow<UserStateInfo?> {
        return combine(
            userRepository.getUserCompositeFromCacheFlow(userId),
            userStateRepository.getUserStateInfoFlow(userId)
        ) { userInfo, userStatus ->
            userStatus.takeIf { userInfo?.user?.isNormalUser == true }
        }
    }

}