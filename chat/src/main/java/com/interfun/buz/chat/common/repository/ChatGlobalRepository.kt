package com.interfun.buz.chat.common.repository

import com.interfun.buz.social.db.entity.BuzUser
import com.interfun.buz.social.entity.SimpleBuzUser
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class ChatGlobalRepository @Inject constructor() {
    //GroupAddressUserInfoChangeEvent
    private val _chatHistoryPageAddressBotFlow = MutableSharedFlow<Pair<Long, SimpleBuzUser?>>()

    val chatHistoryPageAddressBotFlow = _chatHistoryPageAddressBotFlow.asSharedFlow()

    suspend fun postChatHistoryPageAddressBot(groupId: Long, bot: SimpleBuzUser?) {
        _chatHistoryPageAddressBotFlow.emit(groupId to bot)
    }
}