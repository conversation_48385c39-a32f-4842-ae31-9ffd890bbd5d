package com.interfun.buz.chat.wt.viewmodel

import androidx.lifecycle.ViewModel
import com.buz.idl.user.request.RequestCheckAccountStatus
import com.interfun.buz.base.ktx.launchIO
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.base.manager.retry.IntervalStrategy
import com.interfun.buz.base.manager.retry.RetryAction
import com.interfun.buz.base.manager.retry.RetryManager
import com.interfun.buz.common.bean.push.PushGoodBye.LogoutReason
import com.interfun.buz.common.constants.CommonMMKV
import com.interfun.buz.common.ktx.CODE_ACCOUNT_BANNED
import com.interfun.buz.common.ktx.isRequestSuccess
import com.interfun.buz.common.manager.UserManager
import com.interfun.buz.common.net.newInstanceBuzNetUserServiceClient
import com.interfun.buz.common.net.withConfig
import com.interfun.buz.common.utils.PromptUtil
import com.interfun.buz.common.utils.parse
import com.interfun.buz.user.repository.UserSettingRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import javax.inject.Inject

@HiltViewModel
class HomeActivityViewModel @Inject constructor(
    private val userSettingRepository: UserSettingRepository
) : ViewModel() {
    @Volatile
    private var haveCheckUserStatus = false
    private val userService by lazy { newInstanceBuzNetUserServiceClient().withConfig() }
    private val _viewPanelMaskAlphaFlow = MutableStateFlow(1f)
    val viewPanelMaskAlphaFlow: Flow<Float> = _viewPanelMaskAlphaFlow

    fun updatePanelAlpha(alpha: Float) {
        _viewPanelMaskAlphaFlow.value = alpha
    }

    //copy from wtviewmodel
    fun checkAccountStatus() {
        if (haveCheckUserStatus) return
        haveCheckUserStatus = true
        launchIO {
            val request = RequestCheckAccountStatus()
            val retryStrategy = object : IntervalStrategy {
                override fun interval(time: Int): Long {
                    return 10 * 1000L
                }

                override fun reset() {}
            }

            val retryAction = RetryAction {
                val response = userService.checkAccountStatus(request)
                logInfo("HomeActivityViewModel", "checkAccountStatus: ${response.code}")
                if (!response.isRequestSuccess) return@RetryAction false
                val prompt = response.data?.prompt

                if (response.code == CODE_ACCOUNT_BANNED) {
                    UserManager.logout(LogoutReason.REASON_ACCOUNT_BANNED)
                    CommonMMKV.loginOutPrompt = prompt?.let { PromptUtil.prompt2JSONObject(it).toString() }
                } else {
                    prompt?.parse()
                }
                return@RetryAction true
            }
            val retryManager = RetryManager()
            retryManager.retryImmediately(retryStrategy, retryAction)
        }
    }

    fun enableDND() {
        userSettingRepository.enableSyncDND(true)
    }
}