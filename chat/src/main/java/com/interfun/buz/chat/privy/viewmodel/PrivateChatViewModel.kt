package com.interfun.buz.chat.privy.viewmodel

import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ViewModel
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.im.IMAgent
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.withTimeoutOrNull
import javax.inject.Inject

@HiltViewModel
class PrivateChatViewModel @Inject constructor(): ViewModel() {
    private val TAG = "PrivateChatViewModel"
    private val maxTime = 3000L

    suspend fun getFirstPrivateConversationId(lifecycleOwner: LifecycleOwner): Long? {
        return withTimeoutOrNull(maxTime) {
            return@withTimeoutOrNull IMAgent.getConversationListSync(lifecycleOwner, waitLogin = false).first?.find {
                val targetId = it.targetId.toLongOrNull() ?: 0L
                it.convType == IM5ConversationType.PRIVATE.value && targetId > 0 && targetId != (UserSessionManager.userProfile?.userId
                    ?: 0L)
            }?.targetId?.toLongOrNull()
        }
    }
}