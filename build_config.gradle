//即时更新SNAPSHOT
configurations.all {
    resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
}
//引入local.properties
Properties myProperties = new Properties()

android {
    aaptOptions {
        cruncherEnabled false
    }

    lintOptions {
        //设置为 true时lint将不报告分析的进度
        quiet true
        //如果为 true，则当lint发现错误时停止 gradle构建
        abortOnError false
        // 如果为 true，则只报告错误
        ignoreWarnings false
        // 如果为 true，则当有错误时会显示文件的全路径或绝对路径 (默认情况下为true)
        //absolutePaths true
        // 如果为 true，则检查所有的问题，包括默认不检查问题
        checkAllWarnings true
        // 如果为 true，则将所有警告视为错误
        warningsAsErrors false
        // 不检查给定的问题id
        disable 'TypographyFractions', 'TypographyQuotes'
        // 检查给定的问题 id
        enable 'RtlHardcoded', 'RtlCompat', 'RtlEnabled'
        // * 仅 * 检查给定的问题 id
        checkOnly 'NewApi', 'InlinedApi', 'LZLint_ParseHandleID', 'LZLint_MessageObtainUseError',
            'LZLint_HashMapForJDK7', 'LZLint_ImageFileSizeInvalid'
        // 如果为true，则在错误报告的输出中不包括源代码行
        noLines false
        // 如果为 true，则对一个错误的问题显示它所在的所有地方，而不会截短列表，等等。
        showAll true
        // 重置 lint 配置（使用默认的严重性等设置）。
        lintConfig file("default-lint.xml")
        // 如果为 true，生成一个问题的纯文本报告（默认为false）
        textReport true
        // 配置写入输出结果的位置；它可以是一个文件或 “stdout”（标准输出）
        textOutput 'stdout'
        // 如果为true，会生成一个XML报告，以给Jenkins之类的使用
        xmlReport true
        // 用于写入报告的文件（如果不指定，默认为lint-results.xml）
        xmlOutput file("lint-report.xml")
        // 如果为真，会生成一个HTML报告（包括问题的解释，存在此问题的源码，等等）
        htmlReport true
        // 写入报告的路径，它是可选的（默认为构建目录下的 lint-results.html ）
        htmlOutput file("lint-report.html")
        // 设置为 true， 将使所有release 构建都以issus的严重性级别为fatal（severity=false）的设置来运行lint
        // 并且，如果发现了致命（fatal）的问题，将会中止构建（由上面提到的 abortOnError 控制）
        checkReleaseBuilds false
        // 设置给定问题的严重级别（severity）为fatal （这意味着他们将会
        // 在release构建的期间检查 （即使 lint 要检查的问题没有包含在代码中)
        fatal 'NewApi'
        // 设置给定问题的严重级别为error
        error 'Wakelock', 'TextViewEdits'
        // 设置给定问题的严重级别为warning
        warning 'ResourceAsColor'
        // 设置给定问题的严重级别（severity）为ignore （和不检查这个问题一样）
        ignore 'TypographyQuotes'
        //忽略(ignore)ValidFragment检查
        disable 'ValidFragment'
    }

    signingConfigs {
        debug {
            myProperties.load(project.rootProject.file('keystore.properties').newDataInputStream())
            // No debug config
            storeFile file('debug.keystore')
            keyAlias 'androiddebugkey'
            storePassword 'android'
            keyPassword 'android'
            v1SigningEnabled true

            v2SigningEnabled true
        }

        release {
            myProperties.load(project.rootProject.file('local.properties').newDataInputStream())

            storeFile file(myProperties.getProperty('key.store'))
            storePassword myProperties.getProperty('key.store.password')
            keyAlias myProperties.getProperty('key.alias')
            keyPassword myProperties.getProperty('key.alias.password')
            v1SigningEnabled true

            v2SigningEnabled true
        }
    }

    buildTypes {

        debug {
            buildConfigField "String", "GIT_COMMIT_SHA", String.valueOf(getGitCommitSha())
            buildConfigField "String", "GIT_BRANCH", String.valueOf(getGitBranchName())
            buildConfigField "boolean", "LOG_FILE", "true"
            buildConfigField "boolean", "BUILD_WITH_JENKINS", QA_JENKINS_BUILD
            buildConfigField "boolean", "RELEASE_BUGLY", "false"
            //打包环境是否默认灯塔
            buildConfigField "boolean", "IS_DEFAULT_TOWER_ENV", QA_DEFAULT_TOWER_ENV
            //打包环境是否默认预发
            buildConfigField "boolean", "IS_DEFAULT_PRE_ENV", QA_DEFAULT_PRE_ENV
            buildConfigField "String", "BUILD_QA_BIND_IP_TAG", QA_BIND_IP_TAG

            buildConfigField "boolean", "BUILD_OPEN_BITMAP_CHECK", OPEN_BITMAP_CHECK
            buildConfigField "boolean", "IS_BENCHMARK", "false"

            minifyEnabled false
            shrinkResources false
            debuggable true
            signingConfig signingConfigs.debug
        }

        release {
            buildConfigField "String", "GIT_COMMIT_SHA", String.valueOf(getGitCommitSha())
            buildConfigField "String", "GIT_BRANCH", String.valueOf(getGitBranchName())
            // 不显示Log 调用的类 ReleaseDebugUtils
            buildConfigField "boolean", "LOG_FILE", "false"
            buildConfigField "boolean", "BUILD_WITH_JENKINS", QA_JENKINS_BUILD
            buildConfigField "boolean", "RELEASE_BUGLY", QA_RELEASE_BUGLY
            buildConfigField "boolean", "IS_DEFAULT_TOWER_ENV", QA_DEFAULT_TOWER_ENV
            //打包环境是否默认预发
            buildConfigField "boolean", "IS_DEFAULT_PRE_ENV", QA_DEFAULT_PRE_ENV
            buildConfigField "String", "BUILD_QA_BIND_IP_TAG", QA_BIND_IP_TAG

            buildConfigField "boolean", "BUILD_OPEN_BITMAP_CHECK", OPEN_BITMAP_CHECK
            buildConfigField "boolean", "IS_BENCHMARK", "false"
            minifyEnabled true
            // 移除无用的resource文件
            shrinkResources false
            debuggable false
            signingConfig signingConfigs.release
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'),
                'proguard-rules.pro'
        }

        //输出log的release
        releaseLog {
            buildConfigField "String", "GIT_COMMIT_SHA", String.valueOf(getGitCommitSha())
            buildConfigField "String", "GIT_BRANCH", String.valueOf(getGitBranchName())
            buildConfigField "boolean", "LOG_FILE", "true"
            buildConfigField "boolean", "BUILD_WITH_JENKINS", QA_JENKINS_BUILD
            buildConfigField "boolean", "RELEASE_BUGLY", QA_RELEASE_BUGLY
            buildConfigField "boolean", "IS_DEFAULT_TOWER_ENV", QA_DEFAULT_TOWER_ENV
            //打包环境是否默认预发
            buildConfigField "boolean", "IS_DEFAULT_PRE_ENV", QA_DEFAULT_PRE_ENV
            buildConfigField "String", "BUILD_QA_BIND_IP_TAG", QA_BIND_IP_TAG

            buildConfigField "boolean", "BUILD_OPEN_BITMAP_CHECK", OPEN_BITMAP_CHECK
            buildConfigField "boolean", "IS_BENCHMARK", "false"

            minifyEnabled true
            // 移除无用的resource文件
            shrinkResources false
            debuggable false
            signingConfig signingConfigs.release
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'),
                'proguard-rules.pro'
            matchingFallbacks = ['release']
        }

        benchmark {
            initWith buildTypes.release
            minifyEnabled false
            buildConfigField "boolean", "IS_BENCHMARK", "true"
            buildConfigField "String", "BUILD_QA_BIND_IP_TAG", "\"common\""
            signingConfig signingConfigs.debug
            proguardFiles 'benchmark-rules.pro'
            matchingFallbacks = ['release']
        }
    }

    packagingOptions {
        exclude 'META-INF/kotlin*'
        exclude 'META-INF/proguard/*'
        exclude 'kotlin*/**'
        exclude 'META-INF/ktor-io.kotlin_module'
        exclude 'lib/mips*/*.so'
        exclude 'lib/x86*/*.so'
        exclude 'lib/**/lizhisecret.so'
    }
}

/**
 * 当前git commit的sha值
 */

def getGitCommitSha() {
    def sha = 'git rev-parse --short HEAD'.execute([], project.rootDir).text.trim()
    return "\"" + sha + "\""
}

/**
 * 获取git分支名字
 * @return
 */
static def getGitBranchName() {
    def branch = 'git symbolic-ref --short -q HEAD'.execute().text.trim()
    if (branch == "") {
        // Jenkins 环境下, 第一个获取到的会是空, 所以得通过第二个来获取
        branch = 'git name-rev --name-only HEAD'.execute().text.trim()
        branch = branch.replace("remotes/origin/", "")
    }
    println("Got git branch name: " + branch)
    return "\"" + branch + "\""
}