<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <application>

        <activity
            android:name=".view.activity.LivePlaceActivity"
            android:configChanges="keyboard|keyboardHidden|screenSize|orientation"
            android:screenOrientation="portrait"
            android:launchMode="singleTop"
            android:exported="false"
            android:theme="@style/DimTransparent" />

        <activity
            android:name=".view.activity.OnAirTestActivity"
            android:configChanges="keyboard|keyboardHidden|screenSize|orientation"
            android:screenOrientation="portrait"
            android:launchMode="singleTop"
            android:exported="false"
            android:theme="@style/AppTheme"
            />

        <activity android:name=".view.activity.LivePlaceVisitActivity"
            android:configChanges="keyboard|keyboardHidden|screenSize|orientation"
            android:screenOrientation="portrait"
            android:launchMode="singleTop"
            android:exported="false"
            android:theme="@style/AppTheme"
            />
    </application>


</manifest>