package com.interfun.buz.create

import android.net.Uri
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.PickVisualMediaRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.interfun.buz.base.ktx.launchIO
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.base.ktx.toast
import com.interfun.buz.base.ktx.withMainContext
import com.interfun.buz.common.utils.ImageUtil
import com.interfun.buz.compose.components.IconFontText
import com.interfun.buz.compose.ktx.HorizontalSpace
import com.interfun.buz.compose.ktx.toPxInt
import com.interfun.buz.compose.modifier.applyIf
import com.interfun.buz.compose.modifier.debouncedClickable
import com.interfun.buz.liveplace.bean.update.BgImgInfoWrapper
import com.interfun.buz.liveplace.repository.BgImgResource
import com.interfun.buz.onair.R
import kotlinx.collections.immutable.PersistentList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toPersistentList
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@Composable
fun LivePlaceSelectBg(
    bgItems: PersistentList<BgImgInfoWrapper>,
    onItemClick: ((BgImgInfoWrapper) -> Unit),
    onUploadImgClick: (Uri) -> Unit,
    onTrackCustomBgLick: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val selectPickContracts =
        rememberLauncherForActivityResult(contract = ActivityResultContracts.PickVisualMedia()) { retUri ->
            if (retUri != null) {
//                try {
                //懒得处理异常 直接拷贝
//                    val flag = Intent.FLAG_GRANT_READ_URI_PERMISSION
//                    context.contentResolver.takePersistableUriPermission(retUri, flag)
//                } catch (e: Exception) {
//
//                }
                val type = context.contentResolver.getType(retUri)
                coroutineScope.launchIO {
                    val userSelectImgCopyFile = ImageUtil.tempCopyToDir("lp_bg_temp_copy", retUri)
                    if (userSelectImgCopyFile == null) {
                        logInfo(TAG, "userSelectImgCopyFile is null")
                        return@launchIO
                    }
                    val copyRetUri: Uri = Uri.fromFile(userSelectImgCopyFile)
                    if ((type != null && type == "image/gif") || ImageUtil.isGifImage(copyRetUri)) {
                        toast(R.string.live_place_not_support)
                    } else {
                        withMainContext {
                            onUploadImgClick.invoke(copyRetUri)
                        }
                    }
                }
            }
        }
    
    // 调整背景项目顺序：非自定义背景先显示，自定义背景放在末尾
    val orderedBgItems = remember(bgItems) {
        val nonCustomBgs = bgItems.filter { !it.isUserUpdate }
        val customBgs = bgItems.filter { it.isUserUpdate }
        (nonCustomBgs + customBgs).toPersistentList()
    }

    Row(
        modifier = modifier
            .wrapContentWidth()
            .height(56.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center
    ) {
        HorizontalSpace(16.dp)
        // 上传图片按钮 - 始终保持在第一个位置
        Box(
            modifier = Modifier
                .clip(CircleShape)
                .size(44.dp)
                .debouncedClickable {
                    onTrackCustomBgLick.invoke()
                    selectPickContracts.launch(
                        PickVisualMediaRequest(
                            ActivityResultContracts.PickVisualMedia.ImageOnly
                        )
                    )
                },
            contentAlignment = Alignment.Center
        ) {
            Box(
                modifier = Modifier
                    .size(37.dp)
                    .background(colorResource(R.color.white_20), CircleShape)
            ) {
                IconFontText(
                    iconText = stringResource(R.string.ic_album_solid),
                    iconSize = 20.dp,
                    iconColor = colorResource(R.color.color_foreground_neutral_important_default),
                    modifier = Modifier.align(Alignment.Center)
                )
            }
        }
        
        Spacer(modifier = Modifier.size(6.dp))
        
        // 背景列表
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(6.dp),
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.weight(1f, false),
            contentPadding = PaddingValues(end = 30.dp)
        ) {
            items(orderedBgItems) { item ->
                Box(
                    modifier = Modifier
                        .size(44.dp)
                        .debouncedClickable {
                            onItemClick.invoke(item)
                        }
                        .applyIf(item.isSelect) {
                            border(
                                BorderStroke(4.dp, Color.White), CircleShape
                            )
                        },
                    contentAlignment = Alignment.Center
                ) {
                    val retryFlag = remember { mutableIntStateOf(0) }
                    val retryJobState = remember { mutableStateOf<Job?>(null) }
                    val retryScope = rememberCoroutineScope()

                    AsyncImage(
                        modifier = Modifier
                            .clip(CircleShape)
                            .size(37.dp),
                        onError = { error ->
                            retryJobState.value?.cancel()
                            retryJobState.value = retryScope.launch {
                                delay(1000)
                                retryFlag.value += 1
                            }
                        },
                        model = ImageRequest.Builder(LocalContext.current)
                            .data(item.baseInfo.imgUrl)
                            .size(37.dp.toPxInt())
                            .placeholder(R.drawable.common_contacts_name_image_bg)
                            .error(R.drawable.common_contacts_name_image_bg)
                            .setParameter("ref", "livePlaceSelectBg ${retryFlag}")
                            .crossfade(true)
                            .build(),
                        contentScale = ContentScale.Crop,
                        contentDescription = null,
                    )
                }
            }
        }
    }
}


@Composable
@Preview
fun LivePlaceSelectBgPreview() {
    val dataListState = remember {
        mutableStateOf<PersistentList<BgImgInfoWrapper>>(
            persistentListOf(
                BgImgInfoWrapper(
                    BgImgResource(
                        Uri.parse("https://upload.wikimedia.org/wikipedia/zh/7/7f/Hatsune_Miku_NT.jpg"),
                        null,
                        false, id = 0
                    )
                ),
//                BgImgInfoWrapper(
//                    BgImgResource(
//                        2,
//                        Uri.parse("https://upload.wikimedia.org/wikipedia/zh/7/7f/Hatsune_Miku_NT.jpg"),
//                        1,
//                        false
//                    )
//                ), BgImgInfoWrapper(
//                    BgImgResource(
//                        3,
//                        Uri.parse("https://upload.wikimedia.org/wikipedia/zh/7/7f/Hatsune_Miku_NT.jpg"),
//                        1,
//                        false
//                    )
//                ), BgImgInfoWrapper(
//                    BgImgResource(
//                        4,
//                        Uri.parse("https://upload.wikimedia.org/wikipedia/zh/7/7f/Hatsune_Miku_NT.jpg"),
//                        1,
//                        false
//                    )
//                ), BgImgInfoWrapper(
//                    BgImgResource(
//                        5,
//                        Uri.parse("https://upload.wikimedia.org/wikipedia/zh/7/7f/Hatsune_Miku_NT.jpg"),
//                        1,
//                        false
//                    )
//                ), BgImgInfoWrapper(
//                    BgImgResource(
//                        6,
//                        Uri.parse("https://upload.wikimedia.org/wikipedia/zh/7/7f/Hatsune_Miku_NT.jpg"),
//                        1,
//                        false
//                    )
//                ), BgImgInfoWrapper(
//                    BgImgResource(
//                        7,
//                        Uri.parse("https://upload.wikimedia.org/wikipedia/zh/7/7f/Hatsune_Miku_NT.jpg"),
//                        1,
//                        false
//                    )
//                ), BgImgInfoWrapper(
//                    BgImgResource(
//                        8,
//                        Uri.parse("https://upload.wikimedia.org/wikipedia/zh/7/7f/Hatsune_Miku_NT.jpg"),
//                        1,
//                        false
//                    )
//                ),
//                BgImgInfoWrapper(
//                    BgImgResource(
//                        9,
//                        Uri.parse("https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTW7bQIfsFiz-oBzeXKAgxEkR3FO6o2ckU9bw&s"),
//                        1,
//                        false
//                    ), true
//                )
            )
        )
    }

    Box(Modifier.background(colorResource(R.color.secondary_bg))) {
        LivePlaceSelectBg(dataListState.value, { item ->
            val dataList = dataListState.value
            dataListState.value = dataList.map { origin ->
                origin.copy(isSelect = origin == item)
            }.toPersistentList()

        }, onUploadImgClick = { uri ->
            val dataList = dataListState.value
            dataList.filter { !it.isUserUpdate }.map { it.copy(isSelect = false) }
                .toPersistentList()
                .add(
                    BgImgInfoWrapper(
                        BgImgResource(uri, null, true, id = 0),
                        isSelect = true,
                        isUserUpdate = true
                    )
                ).apply {
                    dataListState.value = this
                }
        })
    }
}