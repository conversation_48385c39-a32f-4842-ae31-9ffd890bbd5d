package com.interfun.buz.create

import android.net.Uri
import androidx.activity.compose.BackHandler
import androidx.compose.animation.AnimatedContentScope
import androidx.compose.animation.ExperimentalSharedTransitionApi
import androidx.compose.animation.SharedTransitionScope
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.geometry.toRect
import androidx.compose.ui.graphics.*
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import androidx.media3.common.util.UnstableApi
import coil.compose.AsyncImage
import com.interfun.buz.compose.components.CommonButton
import com.interfun.buz.compose.components.CommonButtonType
import com.interfun.buz.compose.components.IconFontBack
import com.interfun.buz.compose.components.MusicItemStatus
import com.interfun.buz.compose.ktx.VerticalSpace
import com.interfun.buz.compose.ktx.asColor
import com.interfun.buz.compose.ktx.asString
import com.interfun.buz.compose.styles.TextStyles
import com.interfun.buz.core.widget_liveplace.ui.CommonPlayStatusPopup
import com.interfun.buz.core.widget_liveplace.ui.LivePlaceTag
import com.interfun.buz.liveplace.bean.update.BgImgInfoWrapper
import com.interfun.buz.liveplace.bean.update.SongInfoWrapper
import com.interfun.buz.liveplace.repository.BgImgResource
import com.interfun.buz.liveplace.viewmodel.ICreateLivePlaceInfo
import com.interfun.buz.onair.R
import com.interfun.buz.onair.eventrack.OnAirTracking
import com.interfun.buz.onair.viewmodel.LivePlaceBackgroundViewmodel
import kotlinx.collections.immutable.PersistentList
import kotlinx.coroutines.launch

val TAG = "LIVE_PLACE_MAINSCREEN"

@androidx.annotation.OptIn(UnstableApi::class)
@OptIn(ExperimentalSharedTransitionApi::class)
@Composable
fun CreateSongAndBgSetting(
    backgroundViewmodel: LivePlaceBackgroundViewmodel,
    viewmodel: ICreateLivePlaceInfo,
    onNextClick: () -> Unit,
    onReIconClick: () -> Unit,
    sharedTransitionScope: SharedTransitionScope,
    animatedContentScope: AnimatedContentScope,
    backPop: () -> Unit,
    modifier: Modifier = Modifier
) {

    val bgOptionsState = viewmodel.obtainBgImgFlow().collectAsState()
    val songsOptionState = viewmodel.obtainSongListFlow().collectAsState()
    val volumeState = viewmodel.obtainVolumeFlow().collectAsState()
    val musicPopupState = remember { mutableStateOf(false) }
    val localCoroutineScope = rememberCoroutineScope()
    // 使用 LaunchedEffect 监听 count 的变化
    LaunchedEffect(Unit) {
        viewmodel.requestInfo()
    }
    UpdateBgInCreate(backgroundViewmodel, viewmodel)
    BackHandler { backPop.invoke() }
    ReportTrackCreateSongAndBgSetting({ songsOptionState.value })

    with(sharedTransitionScope) {
        Column(
            modifier = modifier
                .fillMaxSize()
                .statusBarsPadding()
                .navigationBarsPadding(),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            IconFontBack(
                onClick = onReIconClick,
                modifier = Modifier
                    .align(Alignment.Start)
                    .sharedElement(
                        rememberSharedContentState("reIcon"),
                        animatedVisibilityScope = animatedContentScope
                    )
            )

            VerticalSpace(20.dp)

            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(min = 50.dp)
                    .padding(horizontal = 20.dp),
                contentAlignment = Alignment.Center

            ) {
                Text(
                    text = R.string.customize_live_place.asString(),
                    style = TextStyles.titleLarge(),
                    color = R.color.color_foreground_neutral_important_default.asColor()
                )
            }

            Text(
                text = R.string.live_place_choose_photo_tip.asString(),
                maxLines = 10,
                textAlign = TextAlign.Center,
                style = TextStyles.bodyMedium(),
                color = R.color.alpha_white_60.asColor(),
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 50.dp)
                    .sharedElement(
                        rememberSharedContentState("subTitle"),
                        animatedVisibilityScope = animatedContentScope
                    )
            )
            VerticalSpace(20.dp)
            LivePlaceSelectBg(
                bgItems = bgOptionsState.value,
                onItemClick = { item ->
                    viewmodel.userUpdateBgInfo(item)
                },
                onTrackCustomBgLick = {
                    OnAirTracking.trackAC2025010202()
                },
                onUploadImgClick = { uri ->
                    viewmodel.userUpdateBgInfo(
                        BgImgInfoWrapper(
                            BgImgResource(uri, null, false,id = 0),
                            isSelect = true,
                            isUserUpdate = true
                        )
                    )
                }
            )
            Spacer(modifier = Modifier.height(42.dp))
            // 使用权重让PreviewBg占据中间剩余空间
            PreviewBg(
                bgUrl = bgOptionsState.value.firstOrNull { it.isSelect }?.baseInfo?.imgUrl,
                isUserUpdate = bgOptionsState.value.firstOrNull { it.isSelect }?.isUserUpdate ?: false,
                songName = songsOptionState.value.firstOrNull { it.isSelect }?.baseInfo?.songName
                    ?: "",
                musicPopup = {
                    localCoroutineScope.launch {
                        musicPopupState.value = true
                        OnAirTracking.trackVS2025010204()
                    }
                },
                showLoading = songsOptionState.value.firstOrNull { it.isSelect }?.status == MusicItemStatus.LOADING,
                showSilent = songsOptionState.value.firstOrNull { it.isSelect }?.baseInfo?.uri == Uri.EMPTY,
                modifier = Modifier
                    .fillMaxWidth(fraction = 240f / 375f)
                    .weight(1f)
            )
            VerticalSpace(45.dp)
            CommonButton(
                type = CommonButtonType.MAIN_LARGER,
                text = R.string.next.asString(),
                onClick = {
                    onNextClick.invoke()
                },
                showLoading = false,
                modifier = Modifier
                    .fillMaxWidth()
                    .height(56.dp)
                    .padding(horizontal = 20.dp)
            )
            VerticalSpace(20.dp)
        }
    }

    if (musicPopupState.value) {
        LivePlaceMusicSelectPopup(
            onDismissRequest = {
                musicPopupState.value = false
            },
            onSelectSong = { selectItem ->
                viewmodel.userUpdateSong(selectItem)
            },
            onVolumeChange = { volume ->
                viewmodel.userUpdateVolume(volume)
            },
            onCurVolume = {
                volumeState.value
            },
            songsOptionState = { songsOptionState.value },
        )
    }

}

@Composable
private fun ReportTrackCreateSongAndBgSetting(obtainSongs: () -> PersistentList<SongInfoWrapper>) {
    val isReported = rememberSaveable {
        mutableStateOf(false)
    }
    if (!isReported.value) {
        val selectSong = obtainSongs.invoke().firstOrNull { it.isSelect }
        if (selectSong != null && selectSong.baseInfo.uri != Uri.EMPTY) {
            isReported.value = true
            OnAirTracking.trackAvs2025010201(ambientId = "${selectSong.originData.id}")
        }
    }
}


@Composable
fun PreviewBg(
    bgUrl: Uri?,
    isUserUpdate: Boolean,
    musicPopup: () -> Unit,
    songName: String,
    showSilent: Boolean = false,
    showLoading: Boolean = false,
    modifier: Modifier = Modifier,
) {
    Outline.Generic(Path())
    val radius = 40.dp

    //shadow
    Box(modifier = modifier) {
        Box(modifier = Modifier
            .fillMaxSize()
            .clip(RoundedCornerShape(topStart = radius, topEnd = radius))
            .drawWithContent {
                drawContext.canvas.withSaveLayer(this.size.toRect(), Paint().apply {}) {
                    drawContent()
                    drawRect(
                        Brush.verticalGradient(
                            0f to Color.Black,
                            0.5f to Color.Black,
                            1f to Color.Transparent,
                        ), blendMode = BlendMode.DstIn
                    )
                }
            }
            .border(
                BorderStroke(4.dp, R.color.alpha_white_10.asColor()),
                RoundedCornerShape(topStart = radius, topEnd = radius)
            )) {
            AsyncImage(
                modifier = Modifier.fillMaxSize(),
                model = bgUrl,
                contentScale = ContentScale.Crop,
                contentDescription = null,
            )
            if (isUserUpdate) {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(color = R.color.alpha_black_50.asColor())
                )
            }
        }

        ConstraintLayout(
            modifier = Modifier.fillMaxSize()
        ) {
            val livePlaceCon = createRef()
            val previewTitleCon = createRef()
            val previewSubtitleCon = createRef()
            val songPopupCon = createRef()
            LivePlaceTag(true, modifier = Modifier
                .constrainAs(livePlaceCon) {
                    start.linkTo(parent.start, margin = 20.dp)
                    top.linkTo(parent.top, margin = 40.dp)
                })
            Text(modifier = Modifier
                .constrainAs(previewTitleCon) {
                    start.linkTo(livePlaceCon.start)
                    end.linkTo(parent.end, margin = 20.dp)
                    top.linkTo(livePlaceCon.bottom, margin = 12.dp)
                    horizontalBias = 0f
                }
                .wrapContentSize(align = Alignment.Center),
                text = R.string.your_live_place.asString(),
                maxLines = 2,
                textAlign = TextAlign.Center,
                overflow = TextOverflow.Ellipsis,
                style = TextStyles.titleSmall(),
                color = R.color.color_foreground_neutral_important_default.asColor())

            Text(modifier = Modifier
                .constrainAs(previewSubtitleCon) {
                    start.linkTo(parent.start, margin = 20.dp)
                    end.linkTo(parent.end, margin = 20.dp)
                    bottom.linkTo(songPopupCon.top, margin = 20.dp)
                    width = Dimension.fillToConstraints
                    horizontalBias = 0f
                }
                .wrapContentSize(align = Alignment.Center),
                text = R.string.live_place_choose_sound_tip.asString(),
                maxLines = 2,
                textAlign = TextAlign.Center,
                overflow = TextOverflow.Ellipsis,
                style = TextStyles.bodyMedium(),
                color = R.color.alpha_white_60.asColor())

            CommonPlayStatusPopup(modifier = Modifier
                .constrainAs(songPopupCon) {
                    start.linkTo(parent.start, margin = 20.dp)
                    end.linkTo(parent.end, margin = 20.dp)
                    bottom.linkTo(parent.bottom)
                    width = Dimension.wrapContent
                },
                title = songName,
                showSilent = showSilent,
                showLoading = showLoading,
                onclick = {
                    musicPopup.invoke()
                })
        }
    }
}
