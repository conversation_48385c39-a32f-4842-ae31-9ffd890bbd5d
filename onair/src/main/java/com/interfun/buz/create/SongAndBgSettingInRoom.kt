package com.interfun.buz.create

import android.net.Uri
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.input.TextFieldState
import androidx.compose.foundation.text.input.rememberTextFieldState
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.media3.common.Player
import com.interfun.buz.base.ktx.toast
import com.interfun.buz.compose.components.CommonTextField
import com.interfun.buz.compose.components.IconFontBack
import com.interfun.buz.compose.components.haze.hazeEffect
import com.interfun.buz.compose.components.updateText
import com.interfun.buz.compose.ktx.*
import com.interfun.buz.compose.modifier.debouncedClickable
import com.interfun.buz.compose.styles.TextStyles
import com.interfun.buz.core.widget_liveplace.ui.CommonPlayStatusPopup
import com.interfun.buz.liveplace.bean.update.BgImgInfoWrapper
import com.interfun.buz.liveplace.bean.update.SongInfoWrapper
import com.interfun.buz.liveplace.repository.BgImgResource
import com.interfun.buz.liveplace.track.LivePlaceTracker
import com.interfun.buz.liveplace.viewmodel.IUpdateLivePlaceInfo
import com.interfun.buz.liveplace.viewmodel.UpdateLivePlaceStatus
import com.interfun.buz.onair.R
import com.interfun.buz.onair.standard.LivePlaceVisibleTypeEnum
import com.interfun.buz.onair.standard.RoomParam
import com.interfun.buz.onair.ui.composable.ChannelSettingVisibleType
import com.interfun.buz.onair.ui.screen.TopicInviteUserSetting
import com.interfun.buz.onair.ui.state.LivePlaceNavType
import com.interfun.buz.onair.viewmodel.LivePlaceBackgroundViewmodel
import com.interfun.buz.onair.viewmodel.RouteBackground
import kotlinx.collections.immutable.PersistentList
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.mapNotNull

@Composable
fun SongAndBgSettingInRoom(
    bgmVm: LivePlaceBackgroundViewmodel,
    updateInfoViewmodel: IUpdateLivePlaceInfo,
    modifier: Modifier = Modifier,
    onClickReturn: () -> Unit,
    finishUpdate: () -> Unit,
    showJoinOpt: Boolean = true,
    jumpInfo: RoomParam
) {
    LaunchedEffect(updateInfoViewmodel) {
        updateInfoViewmodel.requestInfo()
    }
    LaunchedEffect(updateInfoViewmodel) {
        updateInfoViewmodel.obtainUpdateRetFlow().collect { state ->
            if (state == UpdateLivePlaceStatus.SUCCESS) {
                finishUpdate.invoke()
            }
        }
    }
    val trackVolumeState = remember { mutableStateOf<TrackVolume?>(null) }
    val visibleTypeState = updateInfoViewmodel.obtainVisibleFlow().collectAsState()
    val songsState = updateInfoViewmodel.obtainSongListFlow().collectAsState()
    val bgImgState = updateInfoViewmodel.obtainBgImgFlow().collectAsState()
    val playerStatusState = bgmVm.bgmPlayerStatusFlow.collectAsState()
    val updateLpState = updateInfoViewmodel.obtainUpdateRetFlow().collectAsState()
    val topicState by updateInfoViewmodel.obtainTopicFlow().collectAsState()
    val musicPopupState = remember { mutableStateOf(false) }
    val nameTextFieldState = rememberTextFieldState(topicState)
    val volumeState = updateInfoViewmodel.obtainVolumeFlow().collectAsState()

    LaunchedEffect(topicState) {
        nameTextFieldState.updateText(topicState)
    }
    LaunchedEffect(updateInfoViewmodel) {
        updateInfoViewmodel.obtainBgImgFlow().mapNotNull { dataList ->
            dataList.firstOrNull { it.isSelect }
        }.distinctUntilChanged().collect { bgInfo ->
            bgmVm.requestUpdateBg(
                RouteBackground(
                    LivePlaceNavType.HomeSettingBg.route,
                    bgInfo.baseInfo.imgUrl,bgInfo.isUserUpdate || bgInfo.baseInfo.customizeImg
                )
            )
        }
    }
    BackHandler {
        updateInfoViewmodel.resetVolume()
        onClickReturn.invoke()
    }
    val openWhoCanJoinPageState = remember { mutableStateOf(false) }

    Box(modifier = modifier) {
        if (openWhoCanJoinPageState.value) {
            updateInfoViewmodel.pauseMusic()

            TopicInviteUserSetting(
                selectItemLambda = {
                    visibleTypeState.value
                },
                onSaveClick = { item ->
                    updateInfoViewmodel.userUpdateVisible(item)
                    openWhoCanJoinPageState.value = false
                    updateInfoViewmodel.resumeMusic()
                    LivePlaceTracker.onAC2025010204(if (item== LivePlaceVisibleTypeEnum.EVERYONE)"all" else "invite")


                },
                onCloseClick = {
                    openWhoCanJoinPageState.value = false
                    updateInfoViewmodel.resumeMusic()
                }
            )
        } else {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .navigationBarsPadding()
            ) {
                Spacer(Modifier.statusBarsPadding())
                TitleArea(
                    showLoading = updateLpState.value == UpdateLivePlaceStatus.LOADING,
                    modifier =
                    Modifier
                        .fillMaxWidth()
                        .height(64.dp),
                    onClickReturn = {
                        //如果没有保存过那么你需要还原音量
                        updateInfoViewmodel.resetVolume()
                        onClickReturn.invoke()
                    },
                    onClickSave = {
                        val topic = nameTextFieldState.text.trim().toString()
                        if (updateLpState.value == UpdateLivePlaceStatus.LOADING) {
                            return@TitleArea
                        }
                        val trackVolume = trackVolumeState.value
                        if (trackVolume != null && trackVolume.finalVolume != trackVolume.initVolume) {
                            LivePlaceTracker.onRB2025010207(trackVolume.finalVolume)
                        }
                        if (topic.isEmpty()) {
                            toast(R.string.live_place_topic_empty_tips)
                        } else {
                            updateInfoViewmodel.userUpdateTopic(topic)
                            updateInfoViewmodel.updateInfoToServer()
                        }
                    }
                )
                VerticalSpace(50.dp)
                LivePlaceNameField(
                    nameTextFieldState = nameTextFieldState,
                    modifier = Modifier
                        .fillMaxWidth()
                        .wrapContentHeight()
                )
                VerticalSpace(40.dp)
                if (jumpInfo.isMineLp() && showJoinOpt) {
                    ChannelSettingVisibleType(
                        title = stringResource(R.string.who_can_join),
                        visibleType = visibleTypeState.value,
                        isInInitSettingPage = false,
                        needNotify = false,
                        showNotifyUI = false,
                        modifier = Modifier
                            .padding(horizontal = 20.dp)
                            .fillMaxWidth()
                            .wrapContentHeight(),
                        onCheckedChange = {
                        },
                        onClickChangeWhoCanJoin = {
                            LivePlaceTracker.onAC2025010203()
                            openWhoCanJoinPageState.value = true
                        }
                    )
                    VerticalSpace(40.dp)
                }
                LivePlaceBgSelect(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(84.dp),
                    bgItems = bgImgState.value,
                    onItemClick = { bgImgInfoWrapper ->
                        LivePlaceTracker.onAC2025010206("background")
                        updateInfoViewmodel.userUpdateBgInfo(bgImgInfoWrapper)
                    },
                    onUploadImgClick = { uri ->
                        val bgImgResource =
                            BgImgResource(uri, null, customizeImg = true, id = 0)
                        updateInfoViewmodel.userUpdateBgInfo(
                            BgImgInfoWrapper(
                                bgImgResource,
                                isSelect = false,
                                isUserUpdate = true,
                                upToServerPath = null
                            )
                        )
                    }
                )
                VerticalSpace(40.dp)
                SongSelect(
                    showLoading = playerStatusState.value == Player.STATE_BUFFERING,
                    selectSongState = songsState.value.firstOrNull { it.isSelect },
                    modifier = Modifier
                        .fillMaxWidth()
                        .wrapContentHeight(),
                    onClick = {
                        musicPopupState.value = true
                        LivePlaceTracker.onAC2025010206("sound")
                    },
                    showSilent = songsState.value.firstOrNull { it.isSelect }?.baseInfo?.uri == Uri.EMPTY

                )
                WeightSpace(1f)
            }

        }

    }
    if (musicPopupState.value) {

        LivePlaceMusicSelectPopup(
            onDismissRequest = {
                musicPopupState.value = false
            },
            onSelectSong = { selectItem ->
                updateInfoViewmodel.userUpdateSong(selectItem)
            },
            onVolumeChange = { change ->
                val trackVolume = trackVolumeState.value
                if (trackVolume != null) {
                    trackVolumeState.value = trackVolume.copy(finalVolume = change)
                } else {
                    trackVolumeState.value = TrackVolume(volumeState.value, change)
                }
                updateInfoViewmodel.userUpdateVolume(change)
            },
            onCurVolume = {
                volumeState.value
            },
            songsOptionState = {
                songsState.value
            }
        )
    }

}

@Composable
fun SongSelect(
    showLoading: Boolean = false,
    showSilent: Boolean = false,
    selectSongState: SongInfoWrapper?,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier.padding(start = 20.dp)) {
        Text(
            modifier = Modifier,
            text = R.string.ambient_sound_volume_settings.asString(),
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            style = TextStyles.bodySmall(),
            color = R.color.alpha_white_60.asColor()
        )
        VerticalSpace(12.dp)
        CommonPlayStatusPopup(
            showSilent = showSilent,
            showLoading = showLoading,
            title = selectSongState?.baseInfo?.songName ?: "",
            onclick = onClick
        )
    }
}

@Composable
private fun LivePlaceBgSelect(
    modifier: Modifier = Modifier,
    bgItems: PersistentList<BgImgInfoWrapper>,
    onItemClick: ((BgImgInfoWrapper) -> Unit),
    onUploadImgClick: (Uri) -> Unit
) {
    Column(modifier = modifier) {
        Text(
            modifier = Modifier.padding(start = 20.dp),
            text = R.string.environment_background.asString(),
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            style = TextStyles.bodySmall(),
            color = R.color.alpha_white_60.asColor()
        )
        Spacer(Modifier.height(12.dp))
        LivePlaceSelectBg(
            bgItems = bgItems,
            onItemClick = onItemClick,
            onTrackCustomBgLick = {
                LivePlaceTracker.onAC2025010206("background")
            },
            onUploadImgClick = onUploadImgClick,
        )
    }
}

@Composable
private fun LivePlaceNameField(
    nameTextFieldState: TextFieldState,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        Text(
            modifier = Modifier.padding(start = 20.dp),
            text = R.string.live_place_topic.asString(),
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            style = TextStyles.bodySmall(),
            color = R.color.alpha_white_60.asColor()
        )
        VerticalSpace(12.dp)

        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(48.dp)
                .padding(horizontal = 20.dp)
                .clip(shape = RoundedCornerShape(16.dp))
                .hazeEffect(state = LocalHazeState.current)
        ) {
            val reachLimitsToast =
                stringResource(R.string.live_place_topic_limit, LivePlaceTopicMaxChar)
            CommonTextField(
                state = nameTextFieldState,
                hint = R.string.live_place_topic_setting.asString(),
                textStyle = TextStyles.titleLarge(),
                maxLength = LivePlaceTopicMaxChar,
                reachMaxLengthCallback = {
                    toast(reachLimitsToast)
                },
                enabledClearText = true,
                backgroundColor = Color.Transparent,
                backgroundShape = RoundedCornerShape(16.dp),
                modifier = Modifier
                    .fillMaxSize()
                    .onFocusChanged { focusState ->
                        if (focusState.isFocused) {
                            LivePlaceTracker.onAC2025010206("place_name")
                        }
                    }

            )
        }
    }
}

@Composable
private fun TitleArea(
    modifier: Modifier,
    showLoading: Boolean,
    onClickReturn: () -> Unit,
    onClickSave: () -> Unit
) {
    Box(modifier = modifier) {
        IconFontBack(
            modifier = Modifier.align(Alignment.TopStart),
            onClick = { onClickReturn.invoke() })
        Text(
            modifier = Modifier.align(Alignment.Center),
            text = R.string.live_place_settings.asString(),
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            style = TextStyles.titleSmall(),
            color = R.color.color_foreground_neutral_important_default.asColor()
        )
        if (showLoading) {
            CircularProgressIndicator(
                modifier = Modifier
                    .padding(end = 20.dp)
                    .size(20.dp)
                    .align(Alignment.CenterEnd),
                color = com.interfun.buz.compose.R.color.color_foreground_neutral_important_default.asColor(),
                strokeWidth = 3.dp,
                trackColor = Color.Transparent,
            )
        } else {
            Text(
                modifier = Modifier
                    .debouncedClickable { onClickSave.invoke() }
                    .align(Alignment.CenterEnd)
                    .padding(end = 20.dp),
                text = R.string.save.asString(),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                style = TextStyles.labelLarge(),
                color = R.color.color_foreground_highlight_default.asColor()
            )
        }
    }
}

@Composable
@Preview
fun SongAndBgSettingInRoomPreview() {
}