package com.interfun.buz.biz.center.voicemoji.model.collection

import androidx.annotation.Keep
import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import com.google.gson.JsonPrimitive
import com.google.gson.JsonSerializationContext
import com.google.gson.JsonSerializer
import com.interfun.buz.biz.center.voicemoji.model.collection.CollectionState.Normal.toState
import com.interfun.buz.biz.center.voicemoji.model.collection.CollectionType.Unknown.toType
import java.lang.reflect.Type

/**
 * Author: ChenYouSheng
 * Date: 2024/12/19
 * Email: <EMAIL>
 * Desc: 收藏夹数据
 */
@Keep
data class VECollectionData(
    /**收藏唯一id*/
    val id: Long,

    /**收藏类型 1: VE ,2: VG, 3: sticker*/
    val type: CollectionType,

    /**json字符串，type=1：[VoiceEmojiEntity]; type=2 or 3：[VoiceGifEntity]*/
    val data: String,

    /**表情id，type=1：[VoiceEmojiEntity.id]; type=2 or 3：[VoiceGifEntity.id]*/
    val objectId: String,

    /**0 正常 1 已下线*/
    val status: CollectionState
)

/**
 * 收藏状态
 */
@Keep
sealed class CollectionState(val state: Int) {
    data object Normal : CollectionState(0)
    data object Offline : CollectionState(1)

    fun Int?.toState(): CollectionState {
        return when (this) {
            0 -> Normal
            else -> Offline
        }
    }
}

/**
 * 收藏类型
 */
@Keep
sealed class CollectionType(val type: Int) {
    data object VoiceEmoji : CollectionType(1)
    data object VoiceGif : CollectionType(2)
    data object VoiceSticker : CollectionType(3)
    data object Unknown : CollectionType(-1)

    fun Int?.toType(): CollectionType {
        return when (this) {
            1 -> VoiceEmoji
            2 -> VoiceGif
            3 -> VoiceSticker
            else -> Unknown
        }
    }

    fun CollectionType.toValue(): Int {
        return when (this) {
            VoiceEmoji -> 1
            VoiceGif -> 2
            else -> 3
        }
    }
}


@Keep
// gson 序列化/反序列化针对CollectionType处理
class CollectionTypeAdapter : JsonSerializer<CollectionType>,
    JsonDeserializer<CollectionType> {
    override fun serialize(
        src: CollectionType?,
        typeOfSrc: Type?,
        context: JsonSerializationContext?
    ): JsonElement {
        return JsonPrimitive(src?.type)
    }

    override fun deserialize(
        json: JsonElement?,
        typeOfT: Type?,
        context: JsonDeserializationContext?
    ): CollectionType {
        val typeValue = json?.asInt ?: Int.MIN_VALUE
        return typeValue.toType()
    }
}

@Keep
// gson 序列化/反序列化针对CollectionState处理
class CollectionStateTypeAdapter : JsonSerializer<CollectionState>,
    JsonDeserializer<CollectionState> {
    override fun serialize(
        src: CollectionState?,
        typeOfSrc: Type?,
        context: JsonSerializationContext?
    ): JsonElement {
        return JsonPrimitive(src?.state)
    }

    override fun deserialize(
        json: JsonElement?,
        typeOfT: Type?,
        context: JsonDeserializationContext?
    ): CollectionState {
        val typeValue = json?.asInt ?: Int.MIN_VALUE
        return typeValue.toState()
    }
}



