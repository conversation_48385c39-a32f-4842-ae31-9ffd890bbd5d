package com.interfun.buz.biz.center.voicemoji.model.voiceemoji

import com.buz.idl.voicemoji.bean.Voicemoji
import com.buz.idl.voicemoji.bean.VoicemojiCategory
import com.interfun.buz.common.ktx.getIntDefault
import com.interfun.buz.common.ktx.getStringDefault

data class VoiceEmojiData(val emojiList: List<VoiceEmojiEntity>, val category: VoiceEmojiCategory)


fun VoicemojiCategory.convert(): VoiceEmojiData {
    val voiceEmojiCategory = VoiceEmojiCategory(
        this.category,
        this.categoryType,
        this.categoryIconUrl,
        this.style,
        this.latestTimestamp.getIntDefault()
    )
    val voiceEmojiEntityList = this.voicemojis
        ?.asSequence()
        ?.map { it.convert(voiceEmojiCategory) }
        ?.toList()
        ?: emptyList()

    return VoiceEmojiData(voiceEmojiEntityList, voiceEmojiCategory)
}

fun Voicemoji.convert(category: VoiceEmojiCategory): VoiceEmojiEntity {
    return VoiceEmojiEntity(
        id = this.id,
        superscript = this.superscript ?: "",
        emojiIcon = this.icon,
        oldAnimUrl = this.animationUrl ?: "",
        oldAudioUrl = this.voiceUrl ?: "",
        newAnimUrl = this.newAnimationUrl ?: "",
        newAudioUrl = this.newVoiceUrl ?: "",
        animType = this.animationType ?: 1,
        emojiType = this.emojiType ?: 0,
        category = category,
        emojiDescription = description.getStringDefault()
    )
}