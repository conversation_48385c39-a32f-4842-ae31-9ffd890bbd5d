package com.interfun.buz.biz.center.voicemoji.manager

import android.os.SystemClock
import android.widget.Toast
import com.interfun.buz.base.ktx.*
import com.interfun.buz.base.utils.BusUtil
import com.interfun.buz.biz.center.voicemoji.R
import com.interfun.buz.biz.center.voicemoji.model.voiceemoji.VoiceEmojiEntity
import com.interfun.buz.common.eventbus.BaseEvent
import com.interfun.buz.common.ktx.uid
import com.interfun.buz.common.manager.AppConfigRequestManager
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.manager.launchAsync
import com.interfun.buz.common.manager.userLifecycleScope
import com.tencent.mmkv.MMKV
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicInteger
import java.util.concurrent.atomic.AtomicLong

/**
 * 普通表情和盲盒的使用管理
 */
object VoiceMojiFrequencyManager {

    const val TAG = "VoiceMojiFrequencyManager"
    private val maxSendNumber get() = AppConfigRequestManager.voicemojiSendNumber
    private val intervalTime get() = AppConfigRequestManager.voicemojiTimeInterval * 1000L
    private val forbidTime get() = AppConfigRequestManager.voicemojiForbidTime * 1000L
    private val requestCount = AtomicInteger(0)
    private val lastRequestTime = AtomicLong(SystemClock.uptimeMillis())
    private val blockUntilTime = AtomicLong(0)
    private var showOverLimitToastJob: Job? = null
    private val overLimitToastContents by lazy {
        listOf(
            R.string.ve_over_limit_tips_1.asString(),
            R.string.ve_over_limit_tips_2.asString(),
            R.string.ve_over_limit_tips_3.asString(),
        )
    }

    val voiceMojiUsageMap = ConcurrentHashMap<String, VoiceMojiUsage>()
    private val voiceMojiUsageMMKV: MMKV
        get() = MMKV.mmkvWithID("VOICE_MOJI_USAGE_${UserSessionManager.uid}")

    fun login() {
//        logInfo(TAG, "login")
        userLifecycleScope?.launchAsync(Dispatchers.IO) {
            loadFromMMKV()
        }
    }

    fun logout() {
//        logInfo(TAG, "logout")
        voiceMojiUsageMap.clear()
    }

    fun clear() {
        voiceMojiUsageMap.clear()
        voiceMojiUsageMMKV.clearAll()
    }

    fun isVoiceMojiUsageEmpty(): Boolean {
        if (voiceMojiUsageMap.isEmpty()) {
            loadFromMMKV()
        }
        return voiceMojiUsageMap.isEmpty()
    }

    private fun loadFromMMKV() {
        val keys = voiceMojiUsageMMKV.allKeys() ?: return
        keys.forEach { key ->
            val data = voiceMojiUsageMMKV.getString(key, null)
            data?.let {
                val parts = it.split("|")
                if (parts.size == 2) {
                    val frequency = parts[0].toIntOrNull() ?: 0
                    val timestamp = parts[1].toLongOrNull() ?: 0L
                    voiceMojiUsageMap[key] = VoiceMojiUsage(frequency, timestamp)
                }
            }
        }
        logInfo(TAG, "loadFromMMKV voiceMojiUsageMap:$voiceMojiUsageMap")
    }

    fun increaseVoiceMojiUsage(data: VoiceEmojiEntity) {
        userLifecycleScope?.launchIO {
            val currentTime = System.currentTimeMillis()
            val voiceMojiKey = data.getUsageKey()
            val usage = voiceMojiUsageMap.compute(voiceMojiKey) { _, oldValue ->
                val updatedUsage = oldValue ?: VoiceMojiUsage(0, currentTime)
                updatedUsage.apply {
                    frequency++
                    lastUsedTimestamp = currentTime
                    logInfo(TAG, "increaseVoiceMojiUsage: $this")
                }
            }
            usage?.let {
                voiceMojiUsageMMKV.putString(voiceMojiKey, it.transAsString())
                HasVoiceMojiUsageIncreasedEvent.post()
            }
        }
    }

    fun incrementAndCheckOverLimit(): Boolean {
        val currentTime = SystemClock.uptimeMillis()
        if (currentTime < blockUntilTime.get()) {
            showOverLimitToast()
            logInfo(TAG, "incrementAndCheckOverLimit: blockUntilTime: ${blockUntilTime.get()}")
            return true
        }
        if (currentTime - lastRequestTime.get() >= intervalTime) {
            requestCount.set(0)
            lastRequestTime.set(currentTime)
        }
        val newCount = requestCount.incrementAndGet()
        if (newCount > maxSendNumber) {
            blockUntilTime.set(currentTime + forbidTime)
            showOverLimitToast()
            logInfo(TAG, "incrementAndCheckOverLimit: blockUntilTime: ${blockUntilTime.get()}")
            return true
        } else if (newCount == maxSendNumber) {
            blockUntilTime.set(currentTime + forbidTime)
            return false
        }
        return false
    }

    private fun showOverLimitToast() {
        userLifecycleScope?.apply {
            if (showOverLimitToastJob == null) {
                showOverLimitToastJob = launch(Dispatchers.Main) {
                    toastIconFontMsg(
                        message = overLimitToastContents.random(),
                        textColor = R.color.text_white_default.asColor(),
                        iconFont = R.string.ic_clear_input_solid.asString(),
                        iconFontColor = R.color.text_white_important.asColor(),
                        style = IconToastStyle.ICON_LEFT_TEXT_RIGHT,
                        duration = Toast.LENGTH_SHORT
                    )
                    delay(2800)
                    showOverLimitToastJob = null
                }
            }
        }
    }
}

class HasVoiceMojiUsageIncreasedEvent : BaseEvent() {
    companion object {
        fun post() {
            BusUtil.post(HasVoiceMojiUsageIncreasedEvent())
        }
    }
}

data class VoiceMojiUsage(var frequency: Int, var lastUsedTimestamp: Long) {
    fun transAsString() = "$frequency|$lastUsedTimestamp"
}

fun VoiceEmojiEntity.getUsageKey() = "${this.emojiIcon}-${this.id}"