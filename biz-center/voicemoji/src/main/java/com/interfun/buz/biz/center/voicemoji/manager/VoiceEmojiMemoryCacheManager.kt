package com.interfun.buz.biz.center.voicemoji.manager

import android.util.LruCache
import com.interfun.buz.common.manager.GlobalEventManager
import com.interfun.buz.common.manager.UserSessionManager
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch

/**
 * Author: Chen<PERSON>ouSheng
 * Date: 2025/1/6
 * Email: ch<PERSON><PERSON><PERSON><PERSON>@vocalbeats.com
 * Desc: ve相关内存缓存
 */
internal object VoiceEmojiMemoryCacheManager {

    private val cache = LruCache<String, Any>(100)

    init {
        // kill app 清除
        GlobalScope.launch {
            GlobalEventManager.onServiceDestroyStateFlow.collect {
                if (it) {
                    clear()
                }
            }
        }

        // 退出登录清除
        GlobalScope.launch {
            UserSessionManager.userSessionFlow.collect { state ->
                if (state?.isLogin == false) {
                    clear()
                }
            }
        }
    }

    fun <T> put(key: String, value: T) {
        cache.put(key, value)
    }

    fun <T> get(key: String, defValue: T? = null): T? {
        @Suppress("UNCHECKED_CAST")
        return (cache.get(key) as? T) ?: defValue
    }

    fun remove(key: String) {
        cache.remove(key)
    }

    fun clear() {
        cache.evictAll()
    }
}