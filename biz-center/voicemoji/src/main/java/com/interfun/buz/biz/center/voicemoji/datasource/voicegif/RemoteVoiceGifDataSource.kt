package com.interfun.buz.biz.center.voicemoji.datasource.voicegif

import com.buz.idl.voicemoji.request.RequestGetSubVoiceGifs
import com.buz.idl.voicemoji.response.ResponseGetSubVoiceGifs
import com.buz.idl.voicemoji.service.BuzNetVoiceGifServiceClient
import com.interfun.buz.common.bean.Resp
import com.interfun.buz.common.ktx.isSuccess
import com.interfun.buz.common.net.withConfig


/**
 * Author: ChenYouSheng
 * Date: 2024/12/17
 * Email: <EMAIL>
 * Desc: 语音Gif远端数据
 */
class RemoteVoiceGifDataSource {

    private val client by lazy {
        BuzNetVoiceGifServiceClient().withConfig()
    }

    companion object {
        const val TAG = "RemoteVoiceGifDataSource"
    }


    /**
     * 查询VoiceGif二级Tab数据列表
     * @param subTabId:VoiceGif二级导航栏id
     * @param subTabName:VoiceGif二级导名称
     * @param queryParams:分页信息透传参数，初始传空
     */
    suspend fun getSubVoiceGifList(
        subTabId: Long, subTabName: String, queryParams: String
    ): Resp<ResponseGetSubVoiceGifs> {
        val serverResp = client.getSubVoiceGifs(
            RequestGetSubVoiceGifs(
                subTabId = subTabId, name = subTabName, queryParams = queryParams
            )
        )
        val data = serverResp.data
        if (serverResp.isSuccess && data != null) {
            return Resp.Success(data, data.prompt)
        }
        return Resp.Error(serverResp.code, serverResp.msg, data?.prompt)
    }


}