package com.interfun.buz.biz.center.voicemoji.model.voiceemoji

sealed class VoiceEmojiCategoryType(val type: Int) {
    data object Unknown : VoiceEmojiCategoryType(-1)
    data object FrequentlyUsed : VoiceEmojiCategoryType(0)
    data object Default : VoiceEmojiCategoryType(1)
    data object Creative : VoiceEmojiCategoryType(2)
    data object BlindBox : VoiceEmojiCategoryType(3)

    fun Int.toType(): VoiceEmojiCategoryType {
        return when (this) {
            0 -> FrequentlyUsed
            1 -> Default
            2 -> Creative
            3 -> BlindBox
            else -> Unknown
        }
    }
}