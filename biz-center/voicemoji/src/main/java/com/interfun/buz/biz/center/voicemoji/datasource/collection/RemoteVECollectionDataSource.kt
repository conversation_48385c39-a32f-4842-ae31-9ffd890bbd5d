package com.interfun.buz.biz.center.voicemoji.datasource.collection

import com.buz.idl.voicemoji.request.RequestAddVoiceEmojiCollection
import com.buz.idl.voicemoji.request.RequestDelVoiceEmojiCollections
import com.buz.idl.voicemoji.request.RequestGetVoiceEmojiCollections
import com.buz.idl.voicemoji.response.ResponseAddVoiceEmojiCollection
import com.buz.idl.voicemoji.response.ResponseDelVoiceEmojiCollections
import com.buz.idl.voicemoji.response.ResponseGetVoiceEmojiCollections
import com.buz.idl.voicemoji.service.BuzNetVoicemojiServiceClient
import com.interfun.buz.biz.center.voicemoji.model.collection.CollectionRequestParams
import com.interfun.buz.biz.center.voicemoji.model.collection.CollectionType.VoiceGif.toValue
import com.interfun.buz.common.bean.Resp
import com.interfun.buz.common.ktx.isSuccess
import com.interfun.buz.common.net.withConfig

/**
 * Author: ChenYouSheng
 * Date: 2024/12/16
 * Email: <EMAIL>
 * Desc: VE收藏夹远端数据
 */
class RemoteVECollectionDataSource {

    private val client by lazy {
        BuzNetVoicemojiServiceClient().withConfig()
    }

    /**
     * 获取收藏列表
     */
    suspend fun requestVECollectionList(): Resp<ResponseGetVoiceEmojiCollections> {
        val serverResp = client.getVoiceEmojiCollections(RequestGetVoiceEmojiCollections())
        val data = serverResp.data
        if (serverResp.isSuccess && data != null) {
            return Resp.Success(data, data.prompt)
        }
        return Resp.Error(serverResp.code, serverResp.msg, data?.prompt)
    }

    /**
     * 新增/移动到收藏夹顶部
     */
    suspend fun addVoiceEmojiCollection(params: CollectionRequestParams): Resp<ResponseAddVoiceEmojiCollection> {
        val requestParams = params as? CollectionRequestParams.AddOrMoveToFirst ?: return Resp.Error(
            -1,
            "request params error"
        )
        val serverResp = client.addVoiceEmojiCollection(
            RequestAddVoiceEmojiCollection(
                type = requestParams.veType.toValue(), objectId = requestParams.objectId
            )
        )
        val result = serverResp.data
        if (serverResp.isSuccess && result != null) {
            return Resp.Success(result, result.prompt)
        }
        return Resp.Error(serverResp.code, serverResp.msg, result?.prompt)
    }

    /**
     * 从收藏夹中移除
     */
    suspend fun delVoiceEmojiCollection(params: CollectionRequestParams): Resp<ResponseDelVoiceEmojiCollections> {
        val requestParams = params as? CollectionRequestParams.Remove ?: return Resp.Error(
            -1,
            "request params error"
        )
        val serverResp = client.delVoiceEmojiCollections(
            RequestDelVoiceEmojiCollections(
                collectionIds = listOf(requestParams.collectId)
            )
        )
        val result = serverResp.data
        if (serverResp.isSuccess && result != null) {
            return Resp.Success(result, result.prompt)
        }
        return Resp.Error(serverResp.code, serverResp.msg, result?.prompt)
    }
}