package com.interfun.buz.biz.center.voicemoji.datasource.tabnavigation

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.stringPreferencesKey
import com.buz.idl.voicemoji.response.ResponseGetNavigationTabList
import com.google.gson.Gson
import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.biz.center.voicemoji.manager.VoiceEmojiMemoryCacheManager
import com.interfun.buz.biz.center.voicemoji.model.tabnavigation.SubNavigationTab
import com.interfun.buz.biz.center.voicemoji.model.tabnavigation.VEMainTabNavType.VoiceGif.toType
import com.interfun.buz.biz.center.voicemoji.model.tabnavigation.VETabNavigation
import com.interfun.buz.common.ktx.getIntDefault
import com.interfun.buz.common.manager.cache.userPreferencesDataStore
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext

/**
 * Author: ChenYouSheng
 * Date: 2024/12/17
 * Email: <EMAIL>
 * Desc:
 */
class LocalTabNavigationDataSource(val context: Context = appContext) {

    companion object {
        const val TAG = "LocalTabNavigationDataSource"
    }

    private val Context.veTabNavigationDataStore: DataStore<Preferences> by userPreferencesDataStore(
        "voice-tab-navigation"
    )
    private val KEY_TAB_NAVIGATION_LIST = stringPreferencesKey("tab-navigation-list")
    private val KEY_TAB_NAVIGATION_FIRST = "KEY_TAB_NAVIGATION_FIRST"
    private val KEY_TAB_NAVIGATION_SELECTED = "KEY_TAB_NAVIGATION_SELECTED"

    private val gson = Gson()


    /**
     * 获取表情导航栏列表
     */
    fun getVENavigationTabList(): Flow<List<VETabNavigation>?> {
        return context.veTabNavigationDataStore.data.map { sp ->
            val json = sp[KEY_TAB_NAVIGATION_LIST]
            try {
                val response = gson.fromJson(json, ResponseGetNavigationTabList::class.java)
                response.navigationTabs?.map {
                    val subTabList = it.subNavigationTab?.map { subTab ->
                        SubNavigationTab(
                            subTabId = subTab.subTabId ?: 0,
                            name = subTab.name ?: "",
                            icon = subTab.icon ?: "",
                            latestTimestamp = subTab.latestTimestamp.getIntDefault()
                        )
                    } ?: emptyList()
                    VETabNavigation(
                        id = it.id ?: 0,
                        name = it.name ?: "",
                        icon = it.icon ?: "",
                        type = it.type.toType(),
                        subNavigationTab = subTabList,
                        latestTimestamp = it.latestTimestamp.getIntDefault()
                    )
                }
            } catch (t: Throwable) {
                null
            }
        }.flowOn(Dispatchers.IO)
    }


    /**
     * 更新缓存
     */
    suspend fun updateLocalCache(data: ResponseGetNavigationTabList?) {
        withContext(Dispatchers.IO) {
            val json = gson.toJson(data)
            context.veTabNavigationDataStore.updateData { pref ->
                pref.toMutablePreferences().apply {
                    this[KEY_TAB_NAVIGATION_LIST] = json
                }
            }
        }
        if (!data?.navigationTabs.isNullOrEmpty()) {
            setFirstRequest(false)
        }
    }

    /**
     * 是否是首次请求，默认是true
     */
    fun isFirstRequest(): Boolean {
        return VoiceEmojiMemoryCacheManager.get(KEY_TAB_NAVIGATION_FIRST) ?: true
    }

    /**
     * 设置是否首次请求
     */
    fun setFirstRequest(isFirst: Boolean) {
        VoiceEmojiMemoryCacheManager.put(KEY_TAB_NAVIGATION_FIRST, isFirst)
    }

    /**
     * 保存选中的 Tab, 保存后，不需要立即通知出去，所以这里使用了userMMKV来保存
     */
    fun saveSelectTab(tab: VETabNavigation) {
        VoiceEmojiMemoryCacheManager.put(KEY_TAB_NAVIGATION_SELECTED, tab)
    }

    /**
     * 获取已选中的 Tab
     */
    fun getSelectedTab(): Flow<VETabNavigation?> {
        return flow {
            val veTabNavigation =
                VoiceEmojiMemoryCacheManager.get<VETabNavigation>(KEY_TAB_NAVIGATION_SELECTED)
            emit(veTabNavigation)
        }.flowOn(Dispatchers.IO)
    }

    /**
     * 获取已选中的 Tab
     */
    fun getSelectedTabWithoutFlow(): VETabNavigation? {
        return VoiceEmojiMemoryCacheManager.get<VETabNavigation>(KEY_TAB_NAVIGATION_SELECTED)
    }
}