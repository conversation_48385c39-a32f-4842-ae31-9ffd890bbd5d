package com.interfun.buz.biz.center.voicemoji.datasource.search

import com.buz.idl.voicemoji.request.RequestSearchVoiceGifs
import com.buz.idl.voicemoji.response.ResponseSearchVoiceGifs
import com.buz.idl.voicemoji.service.BuzNetVoiceGifServiceClient
import com.interfun.buz.common.bean.Resp
import com.interfun.buz.common.ktx.isSuccess
import com.interfun.buz.common.net.withConfig

/**
 * Author: ChenYouSheng
 * Date: 2024/12/17
 * Email: <EMAIL>
 * Desc: VoiceGif搜索远端数据
 */
class RemoteVoiceGifSearchDataSource {

    private val client by lazy {
        BuzNetVoiceGifServiceClient().withConfig()
    }

    companion object {
        const val TAG = "RemoteVoiceGifSearchDataSource"
    }

    /**
     * 获取远端搜索列表
     * @param queryContent:	String	搜索内容(搜索内容等于空，返回推荐数据)
     * @param queryParams:	String	分页信息透传参数，初始传空
     */
    suspend fun getVoiceGifSearchList(
        queryContent: String?, queryParams: String?
    ): Resp<ResponseSearchVoiceGifs?> {
        val serverResp = client.searchVoiceGifs(
            request = RequestSearchVoiceGifs(queryContent ?: "", queryParams ?: "")
        )
        val data = serverResp.data
        if (serverResp.isSuccess && data != null) {
            return Resp.Success(data, data.prompt)
        }
        return Resp.Error(serverResp.code, serverResp.msg, data?.prompt)
    }

}