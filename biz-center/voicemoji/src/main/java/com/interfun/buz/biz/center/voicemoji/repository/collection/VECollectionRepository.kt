package com.interfun.buz.biz.center.voicemoji.repository.collection

import android.annotation.SuppressLint
import com.buz.idl.voicemoji.response.ResponseAddVoiceEmojiCollection
import com.buz.idl.voicemoji.response.ResponseDelVoiceEmojiCollections
import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.biz.center.voicemoji.datasource.collection.LocalVECollectionDataSource
import com.interfun.buz.biz.center.voicemoji.datasource.collection.RemoteVECollectionDataSource
import com.interfun.buz.biz.center.voicemoji.model.collection.CollectionModifyType
import com.interfun.buz.biz.center.voicemoji.model.collection.CollectionRequestParams
import com.interfun.buz.biz.center.voicemoji.model.collection.CollectionState.Offline.toState
import com.interfun.buz.biz.center.voicemoji.model.collection.CollectionType.Unknown.toType
import com.interfun.buz.biz.center.voicemoji.model.collection.VECollectionData
import com.interfun.buz.common.bean.Resp.Success
import com.interfun.buz.common.bean.Resp.Error
import com.interfun.buz.common.ktx.CODE_SUCCESS
import com.interfun.buz.common.utils.parse
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.flowOn

/**
 * Author: ChenYouSheng
 * Date: 2024/12/16
 * Email: <EMAIL>
 * Desc:
 */
object VECollectionRepository {

    @SuppressLint("StaticFieldLeak")
    private val TAG = "VECollectionRepository"
    private val localDS = LocalVECollectionDataSource()
    private val remoteDS = RemoteVECollectionDataSource()

    /**
     * 获取收藏夹列表
     */
    fun getVECollectionList(): Flow<List<VECollectionData>?> {
        return localDS.getVECollectionList().flowOn(Dispatchers.IO)
    }

    /**
     * 判断是否存在收藏夹列表中
     * @param objectId VE or VG id
     */
    suspend fun existInCollectionList(objectId: String): Boolean {
        return getVECollectionList().firstOrNull()?.run {
            return this.any { it.objectId == objectId }
        } ?: false
    }

    /**
     * 同步收藏夹数据
     * App生命周期内只触发一次
     */
    suspend fun syncCollectionList() {
        val noCache = localDS.getVECollectionList().firstOrNull().isNullOrEmpty()
        if (isFirstRequest() || noCache) {
            val resp = remoteDS.requestVECollectionList()
            if (resp is Success) {
                localDS.updateVoiceEmojiCollections(resp.data)
            }
        }
    }

    /**
     * 增删改收藏夹
     * @return true：成功（会更新本地缓存），false失败
     */
    suspend fun modifyLocalCache(
        modifyType: CollectionModifyType,
        params: CollectionRequestParams
    ): Int {
        val resp = when (modifyType) {
            CollectionModifyType.AddToFirst, CollectionModifyType.MoveToFirst -> {
                remoteDS.addVoiceEmojiCollection(params)
            }

            CollectionModifyType.Remove -> {
                remoteDS.delVoiceEmojiCollection(params)
            }
        }
        resp.prompt.parse()
       return when (resp) {
           is Success -> {
               // 新增或者更新成功操作成功后，更新本地缓存
               if (resp.data is ResponseAddVoiceEmojiCollection) {
                   val veCollectionData =
                       (resp.data as ResponseAddVoiceEmojiCollection).voiceEmojiCollection?.run {
                           VECollectionData(
                               id = this.id,
                               type = this.type.toType(),
                               objectId = this.objectId ?: "",
                               data = this.dataJson ?: "",
                               status = this.status.toState()
                           )
                       }
                   veCollectionData?.let { localDS.modifyLocalCache(modifyType, it) }

               } else if (resp.data is ResponseDelVoiceEmojiCollections) {
                   // 删除成后，删除本地缓存
                   val collectId = (params as? CollectionRequestParams.Remove)?.collectId
                   collectId?.let { localDS.deleteLocalCache(it) }
               }
               CODE_SUCCESS
           }
           is Error -> resp.code ?: -1
           else -> -1
       }
    }

    private fun isFirstRequest(): Boolean {
        return localDS.isFirstRequest()
    }

    fun hasBeenAddedToFavorite(): Flow<Boolean> {
        return localDS.hasBeenFavoritedFlow().distinctUntilChanged()
    }
}