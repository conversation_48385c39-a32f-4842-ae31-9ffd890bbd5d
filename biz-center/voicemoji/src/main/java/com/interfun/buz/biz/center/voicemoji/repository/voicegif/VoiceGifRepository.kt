package com.interfun.buz.biz.center.voicemoji.repository.voicegif

import android.annotation.SuppressLint
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.biz.center.voicemoji.datasource.voicegif.LocalVoiceGifDataSource
import com.interfun.buz.biz.center.voicemoji.datasource.voicegif.RemoteVoiceGifDataSource
import com.interfun.buz.biz.center.voicemoji.datasource.voicegif.StoredVoiceGifs
import com.interfun.buz.biz.center.voicemoji.model.tabnavigation.SubNavigationTab
import com.interfun.buz.biz.center.voicemoji.model.voicegif.VoiceGifData
import com.interfun.buz.biz.center.voicemoji.model.voicegif.VoiceGifEntity
import com.interfun.buz.biz.center.voicemoji.model.voicegif.VoiceGifUsage
import com.interfun.buz.biz.center.voicemoji.model.voicegif.convert
import com.interfun.buz.common.bean.Resp
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch

/**
 * Author: ChenYouSheng
 * Date: 2024/12/18
 * Email: <EMAIL>
 * Desc: 语音gif表情仓库
 */
object VoiceGifRepository {

    @SuppressLint("StaticFieldLeak")
    private val localDS = LocalVoiceGifDataSource()
    private val remoteDS = RemoteVoiceGifDataSource()
    const val TAG = "VoiceGifRepository"

    /**
     * 获取gif表情列表
     * @param subTabId ：VoiceGif二级导航栏id
     * @param subTabName ：VoiceGif二级导航栏名称
     * @param queryParams: 分页信息透传参数，初始传空(第一页支持缓存）
     */
    suspend fun getVoiceGifTabList(
        subTabId: Long,
        subTabName: String,
        queryParams: String?
    ): Flow<StoredVoiceGifs?> {
        logInfo(TAG, "getVoiceGifTabList subTabId: $subTabId,subTabName: $subTabName, queryParams: $queryParams")
        return if (queryParams.isNullOrEmpty()) { // 第一页请求，优先返回缓存flow，再网络刷新缓存
            val cacheFlow = localDS.getVoiceGifTabList(subTabId)
            cacheFlow.onEach {
                CoroutineScope(Dispatchers.IO).launch {
                    val resp = remoteDS.getSubVoiceGifList(
                        subTabId = subTabId,
                        subTabName = subTabName,
                        queryParams = ""
                    )
                    if (resp is Resp.Success) {
                        localDS.updateGifTabList(subTabId, resp.data) // 更新缓存
                        logInfo(TAG, "getVoiceGifTabList first page net success")
                    } else {
                        logInfo(TAG, "getVoiceGifTabList first page net failure")
                    }
                }
            }
        } else flow { // 分页请求，直接拿网络
            val resp = remoteDS.getSubVoiceGifList(
                subTabId = subTabId,
                subTabName = subTabName,
                queryParams = queryParams
            )
            if (resp is Resp.Success) {
                val voiceGifs = resp.data.voiceGifs?.map { it.convert() } ?: emptyList()
                val data = StoredVoiceGifs(
                    voiceGifs = VoiceGifData(voiceGifs, subTabId),
                    queryParams = resp.data.queryParams,
                    isLastPage = resp.data.isLastPage ?: false,
                    isSuccess = true
                )
                logInfo(TAG, "getVoiceGifTabList next page net success")
                emit(data)
            } else { // 分页请求失败，发射空数据
                val empty = StoredVoiceGifs(
                    voiceGifs = VoiceGifData(emptyList(), subTabId),
                    queryParams = queryParams,
                    isLastPage = false,
                    isSuccess = false
                )
                logInfo(TAG, "getVoiceGifTabList next page net failure")
                emit(empty)
            }
        }.flowOn(Dispatchers.IO)
    }

    /**
     * 新增最近使用
     */
    fun increaseVoiceGifUsage(voiceGifEntity: VoiceGifEntity) {
        GlobalScope.launch {
            localDS.increaseVoiceGifUsage(voiceGifEntity)
        }
    }

    /**
     * 更新最近使用中的remoteUrl
     */
    fun updateVoiceGifUsageOfRemoteUrl(id:String,remoteUrl:String){
        GlobalScope.launch {
            localDS.updateVoiceGifUsageOfRemoteUrl(id,remoteUrl)
        }
    }

    /**
     * 获取最近使用
     */
    fun getVoiceGifUsageList(): Flow<List<VoiceGifUsage>> {
        return localDS.getVoiceGifUsageList()
    }

    /**
     * 清空最近使用
     */
    fun clearVoiceGifUsageList() {
        localDS.clearVoiceGifUsageList()
    }

    /**
     * 保存选中的voiceGif tab
     */
    fun saveSelectTab(tab: SubNavigationTab) {
        localDS.saveSelectTab(tab)
    }

    /**
     * 获取选中的voiceGif tab
     */
    fun getSelectedTab(): Flow<SubNavigationTab?> {
        return localDS.getSelectedTab()
    }
}