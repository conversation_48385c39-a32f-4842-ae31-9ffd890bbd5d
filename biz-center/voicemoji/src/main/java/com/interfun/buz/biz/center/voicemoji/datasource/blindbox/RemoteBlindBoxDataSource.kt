package com.interfun.buz.biz.center.voicemoji.datasource.blindbox

import com.buz.idl.voicemoji.request.RequestGetBlindBoxResource
import com.buz.idl.voicemoji.request.RequestGetBlindBoxes
import com.buz.idl.voicemoji.request.RequestOpenBlindBox
import com.buz.idl.voicemoji.response.ResponseGetBlindBoxResource
import com.buz.idl.voicemoji.response.ResponseGetBlindBoxes
import com.buz.idl.voicemoji.service.BuzNetVoicemojiServiceClient
import com.interfun.buz.base.ktx.log
import com.interfun.buz.biz.center.voicemoji.model.blindbox.RequestBlindType
import com.interfun.buz.common.bean.CancelableResp
import com.interfun.buz.common.bean.Resp
import com.interfun.buz.common.ktx.isSuccess
import com.interfun.buz.common.net.withConfig
import com.lizhi.itnet.lthrift.service.ITResponse

/**
 * Author: ChenYouSheng
 * Date: 2024/12/17
 * Email: <EMAIL>
 * Desc: 盲盒表情远端数据
 */
class RemoteBlindBoxDataSource {

    private val client by lazy {
        BuzNetVoicemojiServiceClient().withConfig()
    }

    companion object {
        const val TAG = "RemoteBlindBoxDataSource"
    }

    /**
     * 获取远端盲盒表情
     */
    suspend fun getBlindBoxList(
        requestType: RequestBlindType = RequestBlindType.Normal,
        extra: String
    ): CancelableResp<ResponseGetBlindBoxes> {
        val serverResp = client.getBlindBoxes(
            RequestGetBlindBoxes(
                requestType = requestType.type,
                queryParams = extra
            )
        )
        log(TAG, "getBlindBoxList:result:${serverResp.code}")
        val data = serverResp.data
        if (serverResp.isSuccess && data != null) {
            return Resp.Success(data, data.prompt)
        }
        return Resp.Error(serverResp.code, serverResp.msg, data?.prompt)
    }

    /**
     * 获取盲盒资源
     */
    suspend fun getBlindBoxResources(): Resp<ResponseGetBlindBoxResource> {
        val serverResp = client.getBlindBoxResource(RequestGetBlindBoxResource())
        val data = serverResp.data
        if (serverResp.isSuccess && data != null) {
            return Resp.Success(data, data.prompt)
        }
        return Resp.Error(serverResp.code, serverResp.msg, data?.prompt)
    }

    /**
     * 开盲盒
     */
    suspend fun openBlindBox(srvOpenIndex: Int): ITResponse<ResponseGetBlindBoxes> {
        return client.openBlindBox(RequestOpenBlindBox(srvOpenIndex))
    }
}