package com.interfun.buz.biz.center.voicemoji.model.collection

import com.interfun.buz.biz.center.voicemoji.model.collection.VECollectionDataType.VoiceEmojiCollection
import com.interfun.buz.biz.center.voicemoji.model.collection.VECollectionDataType.VoiceGifCollection
import com.interfun.buz.biz.center.voicemoji.model.voiceemoji.VoiceEmojiEntity
import com.interfun.buz.biz.center.voicemoji.model.voicegif.VoiceGifEntity

/**
 * Author: ChenYouSheng
 * Date: 2024/12/19
 * Email: <EMAIL>
 * Desc:
 */
sealed interface VECollectionDataType {
    /**
     * 普通表情 or 盲盒表情
     */
    data class VoiceEmojiCollection(
        val collectionInfo: VECollectionData,
        val entity: VoiceEmojiEntity
    ) : VECollectionDataType


    /**
     * VoiceGif or VoiceSticker
     */
    data class VoiceGifCollection(
        val collectionInfo: VECollectionData,
        val entity: VoiceGifEntity
    ) : VECollectionDataType

}

fun VECollectionDataType.toCollectionInfo(): VECollectionData {
    return when (this) {
        is VoiceEmojiCollection -> {
            this.collectionInfo
        }

        is VoiceGifCollection -> {
            this.collectionInfo
        }
    }
}


/**
 * 修改类型
 */
sealed interface CollectionModifyType {
    data object AddToFirst : CollectionModifyType
    data object Remove : CollectionModifyType
    data object MoveToFirst : CollectionModifyType
}

/**
 * 收藏请求参数
 */
sealed interface CollectionRequestParams {
    /**objectId：表情id*/
    data class AddOrMoveToFirst(val veType: CollectionType, val objectId: String) : CollectionRequestParams

    /**collectId：收藏id*/
    data class Remove(val collectId: Long) : CollectionRequestParams
}