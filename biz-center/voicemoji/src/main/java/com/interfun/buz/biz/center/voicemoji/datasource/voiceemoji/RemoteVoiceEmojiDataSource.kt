package com.interfun.buz.biz.center.voicemoji.datasource.voiceemoji

import com.buz.idl.voicemoji.request.RequestGetSystemVoicemojis
import com.buz.idl.voicemoji.response.ResponseGetSystemVoicemojis
import com.buz.idl.voicemoji.service.BuzNetVoicemojiServiceClient
import com.interfun.buz.base.ktx.log
import com.interfun.buz.common.bean.Resp
import com.interfun.buz.common.ktx.isSuccess
import com.interfun.buz.common.net.withConfig

/**
 * Author: ChenYouSheng
 * Date: 2024/12/17
 * Email: <EMAIL>
 * Desc: 普通表情远端数据
 */
class RemoteVoiceEmojiDataSource {

    private val client by lazy {
        BuzNetVoicemojiServiceClient().withConfig()
    }

    companion object {
        const val TAG = "RemoteVoiceEmojiDataSource"
    }

    /**
     * 获取远端普通表情
     */
    suspend fun getVoiceEmojiList(extra: String): Resp<ResponseGetSystemVoicemojis> {
        val serverResp = client.getSystemVoicemojis(RequestGetSystemVoicemojis(extra))
        log(TAG, "getVoiceEmojiList:result:${serverResp.code}")
        val data = serverResp.data
        if (serverResp.isSuccess && data != null) {
            return Resp.Success(data, data.prompt)
        }
        return Resp.Error(serverResp.code, serverResp.msg, data?.prompt)
    }
}