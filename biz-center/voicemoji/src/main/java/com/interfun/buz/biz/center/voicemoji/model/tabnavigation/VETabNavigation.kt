package com.interfun.buz.biz.center.voicemoji.model.tabnavigation

import com.interfun.buz.common.constants.CommonMMKV

/**
 * Author: ChenYouSheng
 * Date: 2024/12/31
 * Email: chenyoush<PERSON>@vocalbeats.com
 * Desc:
 */
data class VETabNavigation(
    val id: Long,
    val name: String, //展示文案
    val icon: Any,//展示icon
    val type: VEMainTabNavType, // 表情类型 1: VE ,2: VG, 3: sticker, -1:收藏夹
    val latestTimestamp: Int = 0,
    val subNavigationTab: List<SubNavigationTab>
)

data class SubNavigationTab(
    val subTabId: Long,//二级导航栏ID
    val name: String,
    val icon: Any,
    val latestTimestamp: Int = 0
)

fun VETabNavigation.hasUpdated(): Boolean {
    when (type) {
        VEMainTabNavType.VoiceEmoji -> {
            CommonMMKV.voiceEmojiLatestTabNotifyTimestamp.let {
                return it > 0 && this.latestTimestamp > it
            }
        }
        else -> {}
    }
    return false
}

/**
 * 表情Tab类型
 */
sealed class VEMainTabNavType(val type: Int) {
    data object VoiceEmoji : VEMainTabNavType(1)
    data object VoiceGif : VEMainTabNavType(2)
    data object VoiceSticker : VEMainTabNavType(3)
    data object Collection : VEMainTabNavType(-1)
    data object Unknown : VEMainTabNavType(Int.MIN_VALUE)

    fun Int?.toType(): VEMainTabNavType {
        return when (this) {
            -1 -> Collection
            1 -> VoiceEmoji
            2 -> VoiceGif
            3 -> VoiceSticker
            else -> Unknown
        }
    }

    fun VEMainTabNavType.toValue(): Int {
        return when (this) {
            Collection -> -1
            VoiceEmoji -> 1
            VoiceGif -> 2
            else -> 3
        }
    }
}
