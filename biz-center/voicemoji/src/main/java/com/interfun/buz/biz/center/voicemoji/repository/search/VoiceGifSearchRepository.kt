package com.interfun.buz.biz.center.voicemoji.repository.search

import android.annotation.SuppressLint
import com.interfun.buz.biz.center.voicemoji.datasource.search.LocalVoiceGifSearchDataSource
import com.interfun.buz.biz.center.voicemoji.datasource.search.RemoteVoiceGifSearchDataSource
import com.interfun.buz.biz.center.voicemoji.datasource.search.StoredVoiceGif
import com.interfun.buz.biz.center.voicemoji.model.voicegif.convert
import com.interfun.buz.common.bean.Resp
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn

/**
 * Author: ChenYouSheng
 * Date: 2024/12/23
 * Email: <EMAIL>
 * Desc:
 */
object VoiceGifSearchRepository {

    @SuppressLint("StaticFieldLeak")
    private val localDS = LocalVoiceGifSearchDataSource()
    private val remoteDS = RemoteVoiceGifSearchDataSource()

    private fun isFirstSearch(): Boolean = localDS.isFirstSearch()

    /**
     * 触发请求的条件：
     * 1.有搜索内容
     * 2.分页加载（无论是否有搜索内容）
     * 3.没有搜索内容，但是应用生命周期内首次打开
     * @param queryContent:	String	搜索内容(搜索内容等于空，返回推荐数据)
     * @param queryParams:	String	分页信息透传参数，初始传空
     */
    suspend fun startVoiceGifSearch(
        queryContent: String?, queryParams: String?
    ): Flow<StoredVoiceGif?> {
        val isTrendingRequest = queryContent.isNullOrEmpty() && queryParams.isNullOrEmpty()
        val isSearchByContent = queryContent.isNullOrEmpty().not()
        val isLoadMore = queryParams != null
        val canRequest = isSearchByContent || isLoadMore || isFirstSearch()
        val noTrendingCache =
            localDS.getVoiceGifTrendingList().firstOrNull()?.voiceGifs.isNullOrEmpty()
        if (canRequest || (isTrendingRequest && noTrendingCache)) {
            val resp = remoteDS.getVoiceGifSearchList(queryContent, queryParams)
            if (resp is Resp.Success) {
                if (isSearchByContent || isLoadMore) {
                    return flow {
                        val response = resp.data
                        val result = if (null == response) {
                            StoredVoiceGif(voiceGifs = emptyList(), queryParams = queryParams)
                        } else {
                            StoredVoiceGif(voiceGifs = response.voiceGifs?.map { it.convert() }
                                ?: emptyList(),
                                queryParams = response.queryParams,
                                isLastPage = response.isLastPage ?: false)
                        }
                        emit(result)
                    }.flowOn(Dispatchers.Default)
                } else {
                    // 第一次搜索空内容成功后，更新缓存，并返回缓存
                    localDS.updateVoiceGifTrendingList(resp.data)
                    return localDS.getVoiceGifTrendingList()
                }
            } else return flow { // 请求失败返回空
                val error = resp as? Resp.Error
                val errorMsg = error?.msg
                val errorCode = error?.code
                emit(
                    StoredVoiceGif(
                        voiceGifs = emptyList(),
                        queryParams = queryParams,
                        isReqError = true,
                        errorCode = errorCode,
                        errorMsg = errorMsg
                    )
                )
            }.flowOn(Dispatchers.Default)
        }
        return localDS.getVoiceGifTrendingList()
    }


    suspend fun clearDataStore() {
        localDS.clearDataStore()
    }
}