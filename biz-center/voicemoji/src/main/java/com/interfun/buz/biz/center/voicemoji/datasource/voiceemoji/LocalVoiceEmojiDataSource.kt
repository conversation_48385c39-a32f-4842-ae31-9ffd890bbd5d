package com.interfun.buz.biz.center.voicemoji.datasource.voiceemoji

import android.content.Context
import androidx.annotation.Keep
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import com.buz.idl.voicemoji.response.ResponseGetSystemVoicemojis
import com.google.gson.Gson
import com.interfun.buz.base.ktx.*
import com.interfun.buz.biz.center.voicemoji.manager.VoiceEmojiMemoryCacheManager
import com.interfun.buz.biz.center.voicemoji.model.voiceemoji.VoiceEmojiCategory
import com.interfun.buz.biz.center.voicemoji.model.voiceemoji.VoiceEmojiData
import com.interfun.buz.biz.center.voicemoji.model.voiceemoji.convert
import com.interfun.buz.common.constants.CommonMMKV
import com.interfun.buz.common.manager.AppConfigRequestManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.withContext

/**
 * Author: ChenYouSheng
 * Date: 2024/12/17
 * Email: <EMAIL>
 * Desc:
 */
@Keep
data class StoredVoiceEmojis(val emojiList: List<VoiceEmojiData>, val extra: String?)

/**
 * 普通表情本地数据
 */
class LocalVoiceEmojiDataSource(val context: Context = appContext) {

    companion object {
        const val TAG = "LocalVoiceEmojiDataSource"
    }

    private val voiceEmojiLatestEntryNotifyTimestamp = MutableStateFlow(CommonMMKV.voiceEmojiLatestEntryNotifyTimestamp)
    private val voiceEmojiLatestTabNotifyTimestamp = MutableStateFlow(CommonMMKV.voiceEmojiLatestTabNotifyTimestamp)
    private val Context.voiceEmojiDataStore: DataStore<Preferences> by preferencesDataStore("voice-emoji")
    private val KEY_VE_LIST = stringPreferencesKey("voice-emoji-list")
    private val KEY_VE_BANNER_TIPS = booleanPreferencesKey("voice-emoji-banner-tips")
    private val KEY_VE_SELECTED_TAB = "KEY_VE_SELECTED_TAB"
    private val gson = Gson()

    /**
     * 获取本地普通表情
     */
    fun getVoiceEmojiList(): Flow<StoredVoiceEmojis?> {
        return context.voiceEmojiDataStore.data.map { sp ->
            val jsonFromDataStore = sp[KEY_VE_LIST]
            val json =
                if (jsonFromDataStore?.isNotEmpty() == true) jsonFromDataStore else getVoiceEmojiFromAssets()
            try {
                logInfo(TAG, "getVoiceEmojiList:$json")
                val response = gson.fromJson(json, ResponseGetSystemVoicemojis::class.java)
                val voiceEmojiList = response.categories?.map { it.convert() } ?: emptyList()
                val extra = response.extra
                StoredVoiceEmojis(voiceEmojiList, extra)
            } catch (t: Throwable) {
                null
            }
        }.flowOn(Dispatchers.Default)
    }

    /**
     * 从内置文件获取普通表情
     */
    private suspend fun getVoiceEmojiFromAssets(): String? = withContext(Dispatchers.IO) {
        try {
            val inputStream = context.assets.open("ve/voiceEmoji.json")
            inputStream.readAsStringCompat()
        } catch (t: Throwable) {
            logError(TAG, t, "getFromAssets error")
            null
        }
    }

    /**
     * 更新普通表情本地缓存
     */
    suspend fun updateVoiceEmojiList(data: ResponseGetSystemVoicemojis) {
        withContext(Dispatchers.Default) {
            try {
                val json = gson.toJson(data)
                logInfo(TAG, "saveVoiceEmojiList:$json")
                context.voiceEmojiDataStore.updateData { pref ->
                    pref.toMutablePreferences().apply {
                        this[KEY_VE_LIST] = json
                    }
                }
            } catch (ignore: Exception) {

            }
        }
    }

    /**
     * 保存选中的 Tab
     */
    fun saveSelectTab(category: VoiceEmojiCategory) {
        VoiceEmojiMemoryCacheManager.put(KEY_VE_SELECTED_TAB, category)
    }

    /**
     * 获取已选中的 Tab
     */
    fun getSelectedTab(): Flow<VoiceEmojiCategory?> {
        return flow {
            val veTabNavigation =
                VoiceEmojiMemoryCacheManager.get<VoiceEmojiCategory>(KEY_VE_SELECTED_TAB)
            emit(veTabNavigation)
        }.flowOn(Dispatchers.Default)
    }

    /**
     * 获取已选中的 Tab
     */
    fun getSelectedTabWithoutFlow(): VoiceEmojiCategory? {
        return VoiceEmojiMemoryCacheManager.get<VoiceEmojiCategory>(KEY_VE_SELECTED_TAB)
    }

    /**
     * 设置是否展示banner tips
     */
    suspend fun setShowBannerTips(show: Boolean) {
        withContext(Dispatchers.Default) {
            context.voiceEmojiDataStore.updateData { pref ->
                pref.toMutablePreferences().apply {
                    this[KEY_VE_BANNER_TIPS] = show
                }
            }
        }
    }

    /**
     * 是否展示banner tips
     */
    fun isShowBannerTips(): Flow<Boolean> {
        return context.voiceEmojiDataStore.data.map { sp ->
            sp[KEY_VE_BANNER_TIPS] ?: true
        }.flowOn(Dispatchers.Default)
    }

    /**
     * 更新入口红点时间戳
     */
    suspend fun  updateVELatestEntryNotifyTimestamp(timestamp: Int) {
        withContext(Dispatchers.Default) {
            CommonMMKV.voiceEmojiLatestEntryNotifyTimestamp = timestamp
            voiceEmojiLatestEntryNotifyTimestamp.emit(timestamp)
        }
    }

    /**
     * 获取入口红点时间戳
     */
    fun getVELatestEntryNotifyTimestamp(): StateFlow<Int> {
        return voiceEmojiLatestEntryNotifyTimestamp
    }

    /**
     * 更新1级tab红点时间戳
     */
    suspend fun updateVELatestTabTimestamp(timestamp: Int) {
        withContext(Dispatchers.Default) {
            CommonMMKV.voiceEmojiLatestTabNotifyTimestamp = timestamp
            voiceEmojiLatestTabNotifyTimestamp.emit(timestamp)
        }
    }

    /**
     * 获取1级tab红点时间戳
     */
    fun getVELatestTabTimestamp(): StateFlow<Int> {
        return voiceEmojiLatestTabNotifyTimestamp
    }

    /**
     * 更新2级tab红点时间戳
     */
    fun updateVELatestTimestamp(type: Int, timestamp: Int) {
        val timestampList = getVELatestTimestamp().toMutableList()
        if (timestampList.isEmpty()) return
        val value = type to timestamp

        // Remove any existing entry with the same category type
        timestampList.removeAll { it.first == value.first }
        // Add the new/updated pair
        timestampList.add(value)

        // Save the updated list as string
        CommonMMKV.voiceEmojiLatestCategoryTimestamp = timestampList.joinToString(";") { "${it.first},${it.second}" }

    }

    /**
     * 获取2级tab红点时间戳
     */
    fun getVELatestTimestamp(): List<Pair<Int, Int>> {
        return CommonMMKV.voiceEmojiLatestCategoryTimestamp
            ?.takeIf { it.isNotBlank() }
            ?.split(";")
            ?.mapNotNull { pair ->
                val parts = pair.split(",")
                if (parts.size == 2) {
                    val categoryType = parts[0].toIntOrNull()
                    val latestTimestamp = parts[1].toIntOrNull()
                    if (categoryType != null && latestTimestamp != null) {
                        categoryType to latestTimestamp
                    } else null
                } else null
            } ?: listOf()
    }
}