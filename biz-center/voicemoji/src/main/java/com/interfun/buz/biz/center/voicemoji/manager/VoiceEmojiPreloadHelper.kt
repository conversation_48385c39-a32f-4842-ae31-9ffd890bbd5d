package com.interfun.buz.biz.center.voicemoji.manager

import android.graphics.drawable.Drawable
import coil.imageLoader
import coil.request.ImageRequest
import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.base.ktx.dp
import com.interfun.buz.base.ktx.launchIO
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.base.manager.download.CommonDownloadManager
import com.interfun.buz.base.manager.download.MediaType
import com.interfun.buz.biz.center.voicemoji.repository.blindbox.BlindBoxRepository
import com.interfun.buz.biz.center.voicemoji.repository.voiceemoji.VoiceEmojiRepository
import com.interfun.buz.common.bean.Resp
import com.interfun.buz.common.manager.cache.voicemoji.PAGCacheFixer
import com.interfun.buz.common.manager.userLifecycleScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

/**
 * Author: ChenYouSheng
 * Date: 2024/8/21
 * Email: chenyoush<PERSON>@vocalbeats.com
 * Desc: 后台返回或者启动App会进行盲盒资源的预加载
 */
object VoiceEmojiPreloadHelper {

    const val TAG = "VoiceEmojiPreloadHelper"

    private var preloadJob: Job? = null
    private var forceReloadJob: Job? = null

    /**
     * 预加载盲盒和表情动画资源和图片表情
     */
    fun preloadVoiceEmoji() {
        userLifecycleScope?.launchIO {
            PAGCacheFixer.fixPagCacheError()
            preloadResource()
            preloadData()
        }
    }

    /**
     * 预加载数据，包括盲盒表情数据和普通表情数据
     */
    private suspend fun preloadData() {
        if (forceReloadJob?.isActive == true) return
        coroutineScope {
            forceReloadJob = launch {
                // 预加载盲盒表情数据
                BlindBoxRepository.syncBlindBoxList()
                // 预加载普通表情数据
                VoiceEmojiRepository.syncVoiceEmojiList()
            }
        }
    }

    /**
     * 预加载解锁盲盒动画 + 已领取的emoji表情动画 + 静态图片资源 + 图片emoji的音频资源
     */
    private suspend fun preloadResource() {
        if (preloadJob?.isActive == true) return
        coroutineScope {
            preloadJob = launch {
                BlindBoxRepository.getBlindBoxResources().collectLatest { resp ->
                    if (resp is Resp.Success) {
                        resp.data.resources?.asSequence()
                            ?.filter { it.isNotEmpty() }
                            ?.groupBy { it.endsWith(".json") || it.endsWith(".pag") }
                            ?.forEach { (isAnimation, list) ->
                                if (isAnimation) {
                                    list.forEach { animationUrl ->
                                        //AnimContainerView.preLoad(animationUrl)
                                        if (animationUrl.endsWith(".json")) {
                                            CommonDownloadManager.startDownload(
                                                animationUrl,
                                                MediaType.VoiceEmojiLottie
                                            )
                                        } else if (animationUrl.endsWith(".pag")) {
                                            CommonDownloadManager.startDownload(
                                                animationUrl,
                                                MediaType.VoiceEmojiPag
                                            )
                                        }
                                        logInfo(
                                            TAG,
                                            "preloadVoiceEmoji==>animationUrl=${animationUrl}"
                                        )
                                    }
                                }
                                if (isAnimation.not()) {
                                    list.groupBy {
                                        it.endsWith(".mp3") || it.endsWith(".aac") || it.endsWith(".wav")
                                    }.forEach { (isAudio, list) ->
                                        if (isAudio) {
                                            list.forEach { audioUrl ->
                                                logInfo(
                                                    TAG,
                                                    "preloadVoiceEmoji==>audioUrl=${audioUrl}"
                                                )
                                                CommonDownloadManager.startDownload(
                                                    audioUrl, MediaType.VoiceEmojiSound
                                                )
                                            }
                                        } else {
                                            list.forEach { emojiIcon ->
                                                preloadImageEmoji(emojiIcon)
                                                logInfo(
                                                    TAG,
                                                    "preloadVoiceEmoji==>emojiIcon=${emojiIcon}"
                                                )
                                            }
                                        }
                                    }
                                }
                            }
                    }
                }
            }
        }
    }

    /**
     * 预加载图片表情
     */
    suspend fun preloadImageEmoji(emojiIcon: String): Drawable? {
        return appContext.imageLoader.execute(
            ImageRequest.Builder(appContext).data(emojiIcon).size(180.dp).build()
        ).drawable
    }

}