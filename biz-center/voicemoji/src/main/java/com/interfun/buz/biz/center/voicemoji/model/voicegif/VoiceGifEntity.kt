package com.interfun.buz.biz.center.voicemoji.model.voicegif

import android.os.Parcelable
import com.buz.idl.voicemoji.bean.VoiceGif
import com.google.errorprone.annotations.Keep
import com.google.gson.Gson
import com.interfun.buz.base.ktx.logInfo
import com.lizhi.component.itnet.base.ext.gson
import kotlinx.parcelize.Parcelize
import org.json.JSONObject


/**
 * Author: ChenYouSheng
 * Date: 2024/12/18
 * Email: <EMAIL>
 * Desc:
 */
@Parcelize
@Keep
data class VoiceGifEntity(
    val id: String,
    val duration: Int?, // 时长，单位秒
    val animationUrl: String?, // 动图gif
    val thumbnailUrl: String?, // 缩略图gif
    val smallThumbnailUrl: String?, // 小Size缩略图gif
    val videoUrl: String?, // 视频url
    val width: Int?, // 宽度大小
    val height: Int?, // 高度大小
    /**
     * remoteUrl是指videoUrl转出来的音频地址
     * 这个字段不一定有，看服务端的缓存。
     * 如果有这个字段，则直接通过普通的发送消息将消息发送出去即可。
     * 如果没有这个字段，则需要用预处理的接口发送出去。
     */
    val remoteUrl: String?, // 音频地址Url
    val isPlaying: Boolean = false // 仅用于客户端刷新播放状态使用,非接口返回

) : Parcelable {

    companion object {

        // 兼容旧版本升级问题 1.58.0_17100_release_google_32and64Bit.apk,
        // 该版本的发包地址：https://poseidon.vico.voxicer.com/#/workflow/workflowGraph/931
        // {"a":"","b":0,"c":"","d":"","e":"","f":"","g":0,"h":0,"i":"","j":false}
        // 原因：1.58.0版本没有添加Keep，所有字段名被混淆了，导致升级后的版本gson解析失败，无法获取最近使用的VG和VG收藏数据
        // 现在改为手动解析
        private val fieldMapping = mapOf(
            "id" to "a",
            "duration" to "b",
            "animationUrl" to "c",
            "thumbnailUrl" to "d",
            "smallThumbnailUrl" to "e",
            "videoUrl" to "f",
            "width" to "g",
            "height" to "h",
            "remoteUrl" to "i",
            "isPlaying" to "j"
        )

        fun parse(data: String): VoiceGifEntity? = runCatching {
            logInfo("VoiceGifEntity", "parse data: $data")
            val jsonObject = JSONObject(data)
            val newVersion = jsonObject.has("id")
            val result = if (newVersion) {
                // 🔹 **新版本 JSON（字段未混淆）**
                logInfo("VoiceGifEntity", "Detected new version JSON")
                VoiceGifEntity(
                    id = jsonObject.optString("id"),
                    duration = jsonObject.optInt("duration"),
                    animationUrl = jsonObject.optString("animationUrl"),
                    thumbnailUrl = jsonObject.optString("thumbnailUrl"),
                    smallThumbnailUrl = jsonObject.optString("smallThumbnailUrl"),
                    videoUrl = jsonObject.optString("videoUrl"),
                    width = jsonObject.optInt("width"),
                    height = jsonObject.optInt("height"),
                    remoteUrl = jsonObject.optString("remoteUrl")
                )
            } else {
                // 🔹 **旧版本 JSON（字段已混淆）**
                logInfo("VoiceGifEntity", "Detected old version JSON, attempting to map fields")
                VoiceGifEntity(
                    id = jsonObject.optString(fieldMapping["id"]),
                    duration = jsonObject.optInt(fieldMapping["duration"]),
                    animationUrl = jsonObject.optString(fieldMapping["animationUrl"]),
                    thumbnailUrl = jsonObject.optString(fieldMapping["thumbnailUrl"]),
                    smallThumbnailUrl = jsonObject.optString(fieldMapping["smallThumbnailUrl"]),
                    videoUrl = jsonObject.optString(fieldMapping["videoUrl"]),
                    width = jsonObject.optInt(fieldMapping["width"]),
                    height = jsonObject.optInt(fieldMapping["height"]),
                    remoteUrl = jsonObject.optString(fieldMapping["remoteUrl"]),
                )
            }
            logInfo("VoiceGifEntity", "parse finish result id: ${result.id}")
            result
        }.onFailure {
            logInfo("VoiceGifEntity", "parse error: ${it.message}")
        }.getOrNull()
    }


    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other !is VoiceGifEntity) return false
        return this.id == other.id
    }

    override fun hashCode(): Int {
        return id.hashCode()
    }
}

fun VoiceGif.convert(): VoiceGifEntity {
    return VoiceGifEntity(
        id,
        duration,
        animationUrl,
        thumbnailUrl,
        smallThumbnailUrl,
        videoUrl,
        width,
        height,
        remoteUrl
    )
}

fun mergeAndDeduplicateVGList(
    originalList: List<Any>,
    newList: List<VoiceGifEntity>
): List<Any> {
    val combinedList = originalList.toMutableList()
    for (item in newList) {
        if (item !in combinedList) {
            combinedList.add(item)
        }
    }
    return combinedList
}