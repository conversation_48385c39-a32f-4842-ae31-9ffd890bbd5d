package com.interfun.buz.biz.center.voicemoji.datasource.tabnavigation

import com.buz.idl.voicemoji.request.RequestGetNavigationTabList
import com.buz.idl.voicemoji.request.RequestSearchVoiceGifs
import com.buz.idl.voicemoji.response.ResponseGetNavigationTabList
import com.buz.idl.voicemoji.response.ResponseSearchVoiceGifs
import com.buz.idl.voicemoji.service.BuzNetVoiceGifServiceClient
import com.buz.idl.voicemoji.service.BuzNetVoicemojiServiceClient
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.common.bean.Resp
import com.interfun.buz.common.ktx.isSuccess
import com.interfun.buz.common.net.withConfig

/**
 * Author: ChenYouSheng
 * Date: 2024/12/17
 * Email: <EMAIL>
 * Desc: 获取导航栏列表
 */
class RemoteTabNavigationDataSource {

    private val client by lazy {
        BuzNetVoicemojiServiceClient().withConfig()
    }

    companion object {
        const val TAG = "RemoteTabNavigationDataSource"
    }

    /**
     * 获取导航栏列表
     */
    suspend fun getNavigationTabList(): Resp<ResponseGetNavigationTabList?> {
        val serverResp = client.getNavigationTabList(
            request = RequestGetNavigationTabList()
        )
        val data = serverResp.data
        logInfo(TAG, "getNavigationTabList data=$data")
        if (serverResp.isSuccess && data != null) {
            return Resp.Success(data, data.prompt)
        }
        return Resp.Error(serverResp.code, serverResp.msg, data?.prompt)
    }

}