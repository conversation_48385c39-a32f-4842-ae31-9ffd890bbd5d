package com.interfun.buz.biz.center.voicemoji.datasource.search

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import com.buz.idl.voicemoji.response.ResponseSearchVoiceGifs
import com.google.gson.Gson
import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.biz.center.voicemoji.manager.VoiceEmojiMemoryCacheManager
import com.interfun.buz.biz.center.voicemoji.model.voicegif.VoiceGifEntity
import com.interfun.buz.biz.center.voicemoji.model.voicegif.convert
import com.interfun.buz.common.manager.cache.userPreferencesDataStore
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext

/**
 * Author: ChenYouSheng
 * Date: 2024/12/17
 * Email: <EMAIL>
 * Desc:
 */
data class StoredVoiceGif(
    val voiceGifs: List<VoiceGifEntity>,
    val queryParams: String?, // 分页信息透传参数，初始传空
    val isLastPage: Boolean = false, // 是否是最后一页
    val isReqError: Boolean = false, // 是否响应失败
    val errorCode: Int? = null, // 错误码
    val errorMsg: String? = null // 错误信息
)

/**
 * VoiceGif搜索本地数据（只在搜索内容为空的时候会有缓存）
 */
class LocalVoiceGifSearchDataSource(val context: Context = appContext) {

    companion object {
        const val TAG = "LocalVoiceGifSearchDataSource"
    }

    private val Context.veSearchDataStore: DataStore<Preferences> by userPreferencesDataStore("voice-gif-search")
    private val KEY_VG_SEARCH_LIST = stringPreferencesKey("voice-gif-search-list")
    private val KEY_VG_FIRST_SEARCH = "KEY_VG_FIRST_SEARCH"
    private val gson = Gson()


    /**
     * 获取推荐内容
     */
    fun getVoiceGifTrendingList(): Flow<StoredVoiceGif?> {
        return context.veSearchDataStore.data.map { sp ->
            val json = sp[KEY_VG_SEARCH_LIST]
            try {
                val response = gson.fromJson(json, ResponseSearchVoiceGifs::class.java)
                StoredVoiceGif(
                    voiceGifs = response.voiceGifs?.map { it.convert() } ?: emptyList(),
                    queryParams = response.queryParams,
                    isLastPage = response.isLastPage ?: false)
            } catch (t: Throwable) {
                null
            }
        }.flowOn(Dispatchers.IO)
    }


    /**
     * 更新推荐内容
     */
    suspend fun updateVoiceGifTrendingList(data: ResponseSearchVoiceGifs?) {
        withContext(Dispatchers.IO) {
            val json = gson.toJson(data)
            context.veSearchDataStore.updateData { pref ->
                pref.toMutablePreferences().apply {
                    this[KEY_VG_SEARCH_LIST] = json
                }
            }
        }
        if (!data?.voiceGifs.isNullOrEmpty()) {
            setFirstSearch(false)
        }
    }

    /**
     * 是否是首次搜索，默认是true
     */
    fun isFirstSearch(): Boolean {
        return VoiceEmojiMemoryCacheManager.get(KEY_VG_FIRST_SEARCH) ?: true
    }

    /**
     * 设置是否首次搜索
     */
    fun setFirstSearch(isFirst: Boolean) {
        VoiceEmojiMemoryCacheManager.put(KEY_VG_FIRST_SEARCH, isFirst)
    }

    /**
     * 清空缓存
     */
    suspend fun clearDataStore() {
        context.veSearchDataStore.edit { preferences ->
            preferences.clear()
        }
    }
}