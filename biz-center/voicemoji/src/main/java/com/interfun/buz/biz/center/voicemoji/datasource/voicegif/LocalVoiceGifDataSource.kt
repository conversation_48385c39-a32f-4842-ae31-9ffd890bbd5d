package com.interfun.buz.biz.center.voicemoji.datasource.voicegif

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import com.buz.idl.voicemoji.response.ResponseGetSubVoiceGifs
import com.google.gson.Gson
import com.interfun.buz.base.ktx.*
import com.interfun.buz.biz.center.voicemoji.datasource.voicegif.VoiceGifUsageMMKV.voiceGifUsageList
import com.interfun.buz.biz.center.voicemoji.manager.VoiceEmojiMemoryCacheManager
import com.interfun.buz.biz.center.voicemoji.model.tabnavigation.SubNavigationTab
import com.interfun.buz.biz.center.voicemoji.model.voicegif.VoiceGifData
import com.interfun.buz.biz.center.voicemoji.model.voicegif.VoiceGifEntity
import com.interfun.buz.biz.center.voicemoji.model.voicegif.VoiceGifUsage
import com.interfun.buz.biz.center.voicemoji.model.voicegif.convert
import com.interfun.buz.common.ktx.uid
import com.interfun.buz.common.manager.UserSessionManager
import com.tencent.mmkv.MMKV
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import org.json.JSONArray
import org.json.JSONObject

/**
 * Author: ChenYouSheng
 * Date: 2024/12/17
 * Email: <EMAIL>
 * Desc:
 */
data class StoredVoiceGifs(
    val voiceGifs: VoiceGifData, val queryParams: String?, val isLastPage: Boolean, val isSuccess:Boolean
)

/**最近使用的语音Gif，由于不需要实时通知，所以保存MMKV*/
internal object VoiceGifUsageMMKV : MMKVOwner {
    override val kv: MMKV
        get() = MMKV.mmkvWithID("VOICE_GIF_USAGE_${UserSessionManager.uid}")

    var voiceGifUsageList by mmkvParcelableList<VoiceGifUsage>(
        // 兼容旧版本升级问题：https://project.larksuite.com/yewutest/issue/detail/6145989?tabKey=comment#comment
        deserializer = { encodedList ->
            try {
                val jsonArray = JSONArray(encodedList)
                val resultList = mutableListOf<VoiceGifUsage>()
                for (i in 0 until jsonArray.length()) {
                    val obj = jsonArray.optJSONObject(i)

                    val jsonKeys = obj.keys()
                    var voiceGifEntity: VoiceGifEntity? = null
                    var frequency = 0
                    var lastUsedTimestamp = 0L

                    while (jsonKeys.hasNext()) {
                        val key = jsonKeys.next()
                        when (val value = obj.opt(key)) {
                            is JSONObject -> {
                                voiceGifEntity = VoiceGifEntity.parse(value.toString()) ?: continue
                            }
                            is Int -> {
                                frequency = value
                            }
                            is Long -> {
                                lastUsedTimestamp = value
                            }
                        }
                    }
                    if (voiceGifEntity?.id.isNullOrEmpty()) continue
                    val entity = voiceGifEntity?.let { VoiceGifUsage(it, frequency, lastUsedTimestamp) } ?: continue
                    resultList.add(entity)
                }
                logInfo("VoiceGifUsageMMKV", "deserializer success,resultList size=${resultList.size}")
                resultList
            } catch (e: Exception) {
                logInfo("VoiceGifUsageMMKV", "deserializer failed,error=${e.message}")
                mutableListOf()
            }
        }
    )
}

/**
 * 语音Gif本地缓存
 */
class LocalVoiceGifDataSource(val context: Context = appContext) {


    companion object {
        private const val KEY_VG_LIST_BASE_PREFIX = "voice-gif-list"
        private const val KEY_VG_SELECTED_TAB = "KEY_VG_SELECTED_TAB"
        private const val RECENT_USAGE_MAX_COUNT = 18 // 最大最近使用VG数量
    }

    private val Context.veGifDataStore: DataStore<Preferences> by preferencesDataStore("voice-gif")


    private val gson = Gson()

    fun getVoiceGifTabList(subTabId: Long): Flow<StoredVoiceGifs?> {
        return context.veGifDataStore.data.map { sp ->
            val json = sp[createKey(subTabId)]
            try {
                val response = gson.fromJson(json, ResponseGetSubVoiceGifs::class.java)
                val voiceGifs = response.voiceGifs?.map { it.convert() } ?: emptyList()
                StoredVoiceGifs(
                    voiceGifs = VoiceGifData(voiceGifs, subTabId),
                    queryParams = response.queryParams,
                    isLastPage = response.isLastPage ?: false,
                    isSuccess = true
                )
            } catch (ignore: Exception) {
                null
            }
        }.flowOn(Dispatchers.IO)
    }


    suspend fun updateGifTabList(subTabId: Long, data: ResponseGetSubVoiceGifs) {
        withContext(Dispatchers.IO) {
            val json = gson.toJson(data)
            context.veGifDataStore.updateData { pref ->
                pref.toMutablePreferences().apply {
                    this[createKey(subTabId)] = json
                }
            }
        }
    }

    private fun createKey(subTabId: Long) =
        stringPreferencesKey("${KEY_VG_LIST_BASE_PREFIX}_${subTabId}")


    /**新增或者更新最近使用，不需要实时刷新*/
    suspend fun increaseVoiceGifUsage(voiceGifEntity: VoiceGifEntity) {
        withContext(Dispatchers.IO) {
            val usageList: MutableList<VoiceGifUsage> =
                // 这里之所以要过滤掉id为空的，是因为id为空的缓存是无效缓存，不能用于发送消息
                // 兼容旧版本升级问题：https://project.larksuite.com/yewutest/issue/detail/6145989?tabKey=comment#comment
                voiceGifUsageList.filter { it.voiceGifEntity.id.isNotNull() }.toMutableList()
            val index = usageList.indexOfFirst { it.voiceGifEntity.id == voiceGifEntity.id }
            if (index != -1) {
                val originalUsage = usageList[index]
                usageList[index] = originalUsage.copy(
                    frequency = originalUsage.frequency + 1,
                    lastUsedTimestamp = System.currentTimeMillis()
                )
            } else {
                usageList.add(
                    VoiceGifUsage(
                        voiceGifEntity = voiceGifEntity,
                        frequency = 1,
                        lastUsedTimestamp = System.currentTimeMillis()
                    )
                )
            }
            voiceGifUsageList = doSortAndLimit(usageList)
        }
    }



    /**修改最近使用列表中voiceGif的remoteUrl*/
    suspend fun updateVoiceGifUsageOfRemoteUrl(id: String, remoteUrl: String) {
        withContext(Dispatchers.IO) {
            val usageList: MutableList<VoiceGifUsage> = voiceGifUsageList
            val index = usageList.indexOfFirst { it.voiceGifEntity.id == id }
            if (index != -1) {
                val originalUsage = usageList[index]
                usageList[index] = originalUsage.copy(
                    voiceGifEntity = originalUsage.voiceGifEntity.copy(remoteUrl = remoteUrl)
                )
                voiceGifUsageList = usageList
            }
        }
    }

    /**获取已使用的语音gif*/
    fun getVoiceGifUsageList(): Flow<List<VoiceGifUsage>> = flow {
        // 兼容旧版本升级问题：https://project.larksuite.com/yewutest/issue/detail/6145989?tabKey=comment#comment
        val list = voiceGifUsageList.toList().filter { it.voiceGifEntity.id.isNotNull() }
        if (list.size > RECENT_USAGE_MAX_COUNT) {
            // 兼容旧版本升级上来可能超过[RECENT_USAGE_MAX_COUNT]的情况，需要重新排序并返回最大个数
            emit(doSortAndLimit(list))
        } else {
            emit(list)
        }
    }.flowOn(Dispatchers.IO)

    /**清空最近使用*/
    fun clearVoiceGifUsageList() {
        voiceGifUsageList = mutableListOf()
    }

    /**保存选中的tab*/
    fun saveSelectTab(tab: SubNavigationTab) {
        VoiceEmojiMemoryCacheManager.put(KEY_VG_SELECTED_TAB, tab)
    }

    /**
     * 获取已选中的 Tab
     */
    fun getSelectedTab(): Flow<SubNavigationTab?> {
        return flow {
            val subNavigationTab =
                VoiceEmojiMemoryCacheManager.get<SubNavigationTab>(KEY_VG_SELECTED_TAB)
            emit(subNavigationTab)
        }.flowOn(Dispatchers.Default)
    }

    /**排序并限制数量*/
    private fun doSortAndLimit(usageList: List<VoiceGifUsage>): MutableList<VoiceGifUsage> {
        val comparator = compareByDescending<VoiceGifUsage> { it.lastUsedTimestamp }
            .thenByDescending { it.frequency }
        val finalList =
            usageList.sortedWith(comparator).take(RECENT_USAGE_MAX_COUNT).toMutableList()
        return finalList
    }
}