package com.interfun.buz.biz.center.voicemoji.service

import com.alibaba.android.arouter.facade.annotation.Route
import com.interfun.buz.biz.center.voicemoji.repository.collection.VECollectionRepository
import com.interfun.buz.common.constants.PATH_VE_SERVICE
import com.interfun.buz.common.service.IVoiceEmojiService

/**
 * Author: ChenYouSheng
 * Date: 2025/2/27
 * Email: chen<PERSON><PERSON><PERSON>@vocalbeats.com
 * Desc:
 */
@Route(path = PATH_VE_SERVICE)
class VoiceEmojiService : IVoiceEmojiService {

    override suspend fun existInCollectionList(objectId: String): <PERSON><PERSON><PERSON> {
        return VECollectionRepository.existInCollectionList(objectId)
    }
}