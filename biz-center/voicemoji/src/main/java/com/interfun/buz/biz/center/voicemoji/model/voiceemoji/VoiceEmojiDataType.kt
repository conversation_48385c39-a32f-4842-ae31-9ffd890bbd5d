package com.interfun.buz.biz.center.voicemoji.model.voiceemoji

import com.interfun.buz.base.ktx.notEmpty
import com.interfun.buz.biz.center.voicemoji.BuildConfig
import com.interfun.buz.common.ktx.getIfNullOrEmpty

interface VoiceEmojiDataType

data class VoiceEmojiCategory(
    val name: String,
    val type: Int,
    val categoryIconUrl: Any? = null,
    val style: Int? = VoiceEmojiCategoryStyle.NO_BADGE.value, // 0 不展示红点 1 展示红点
    val latestTimestamp: Int = 0
) : VoiceEmojiDataType {
    fun hasUpdated(timestampList: List<Pair<Int, Int>>, latestUpdatedTimestamp: Int?): Boolean {
        if (timestampList.isEmpty()) return false
        val max = latestUpdatedTimestamp ?: return false
        val weekTimestamp = 7 * 24 * 60 * 60
        val min = latestUpdatedTimestamp.minus(weekTimestamp)
        return timestampList.firstOrNull { it.first == this.type }?.let {
            this.latestTimestamp in min..max && this.latestTimestamp > it.second
        } ?: true
    }

    fun updatedBlindBox(): Boolean {
        return style == VoiceEmojiCategoryStyle.SHOW_BADGE.value
    }

    fun isBlindBox(): Boolean {
        return type == VoiceEmojiCategoryType.BlindBox.type
    }
}


data class VoiceEmojiEntity(
    var id: Long,
    val superscript: String,
    val emojiIcon: String, // 当emojiType为0时，emojiIcon为字符表情，当emojiType为1时，emojiIcon为图片表情
    val oldAnimUrl: String,
    val oldAudioUrl: String,
    val newAnimUrl: String, // 新版 Lottie 动效文件地址
    val newAudioUrl: String, // 新版 Lottie 对应的音效地址
    val animType: Int, // 1 - 全局动画 2 - 局部居中动画
    val emojiType: Int,  // 0:字符表情，1:图片表情
    val category: VoiceEmojiCategory,
    val emojiDescription: String // 图片表情的描述文案
) : VoiceEmojiDataType {
    //https://vocalbeats.sg.larksuite.com/wiki/KasnwOc1finC0AkwZTVlbhd7gch
    fun getAnimUrl(): String {
        return if (newAudioUrl.notEmpty()) {
            newAnimUrl.getIfNullOrEmpty(oldAnimUrl)
        } else {
            oldAnimUrl
        }
    }

    fun getAudioUrl(): String {
        return newAudioUrl.ifEmpty { oldAudioUrl }
    }

    fun isImageEmoji(): Boolean {
        return emojiType == VoiceEmojiType.Image.value
    }

    fun isTextEmoji(): Boolean {
        return emojiType == VoiceEmojiType.Text.value
    }

    fun getVoiceEmojiType(): VoiceEmojiType {
        return if (emojiType == 0) {
            VoiceEmojiType.Text
        } else {
            VoiceEmojiType.Image
        }
    }
}

enum class VoiceEmojiCategoryStyle(val value: Int) {
    NO_BADGE(0),
    SHOW_BADGE(1),
}

enum class VoiceEmojiAnimType(val value: Int) {
    FULL(1),
    PARTIAL(2),
}

enum class VoiceEmojiType(val value: Int) {
    Text(0),
    Image(1),
}