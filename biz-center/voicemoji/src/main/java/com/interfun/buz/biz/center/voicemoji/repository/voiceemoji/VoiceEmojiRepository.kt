package com.interfun.buz.biz.center.voicemoji.repository.voiceemoji

import android.annotation.SuppressLint
import com.buz.idl.voicemoji.response.ResponseGetSystemVoicemojis
import com.interfun.buz.base.ktx.log
import com.interfun.buz.base.ktx.logError
import com.interfun.buz.biz.center.voicemoji.datasource.voiceemoji.LocalVoiceEmojiDataSource
import com.interfun.buz.biz.center.voicemoji.datasource.voiceemoji.RemoteVoiceEmojiDataSource
import com.interfun.buz.biz.center.voicemoji.model.voiceemoji.VoiceEmojiCategory
import com.interfun.buz.biz.center.voicemoji.model.voiceemoji.VoiceEmojiData
import com.interfun.buz.biz.center.voicemoji.model.voiceemoji.VoiceEmojiEntity
import com.interfun.buz.common.bean.Resp.Error
import com.interfun.buz.common.bean.Resp.Success
import com.interfun.buz.common.constants.CommonMMKV
import com.interfun.buz.common.ktx.getIntDefault
import com.interfun.buz.common.manager.AppConfigRequestManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.*

/**
 * Author: ChenYouSheng
 * Date: 2024/12/16
 * Email: <EMAIL>
 * Desc: 普通表情
 */
object VoiceEmojiRepository {

    @SuppressLint("StaticFieldLeak")
    private val localDS = LocalVoiceEmojiDataSource()
    private val remoteDS = RemoteVoiceEmojiDataSource()

    const val TAG = "VoiceEmojiRepository"

    /**
     * 获取普通表情列表
     */
    fun getVoiceEmojiData(): Flow<List<VoiceEmojiData>> {
        return localDS.getVoiceEmojiList().map { it?.emojiList ?: emptyList() }
    }

    /**
     * 获取所有的表情列表
     */
    fun getVoiceEmojiList(): Flow<List<VoiceEmojiEntity>> {
        return getVoiceEmojiData().map { voiceEmojiDataList -> voiceEmojiDataList.flatMap { it.emojiList } }
    }

    /**
     * 获取指定 ID 的表情
     */
    fun getVoiceEmojiById(id: Long?): Flow<VoiceEmojiEntity?> = flow {
        // 首先尝试从缓存中获取
        val cachedEntity = getVoiceEmojiList()
            .map { emojiList -> emojiList.find { it.id == id } }
            .firstOrNull()

        // 有缓存返回缓存，没有返回网络结果
        if (cachedEntity != null) {
            emit(cachedEntity)
        } else {
            // 先同步再返回
            syncVoiceEmojiList()
            getVoiceEmojiList()
                .collect { emojiList ->
                    val entity = emojiList.find { it.id == id }
                    if (entity != null) {
                        emit(entity)
                    }else{
                        emit(null)
                    }
                }
        }
    }


    /**
     * 获取长按表情列表
     */
    fun getDefaultQREmojiList(): Flow<List<VoiceEmojiEntity>> {
        return getVoiceEmojiData().map { voiceEmojiDataList ->
            // 提取所有的 emojiList 并合并成一个列表
            val allList = voiceEmojiDataList.flatMap { it.emojiList }

            if (allList.isEmpty()) return@map emptyList()

            val qrIds = AppConfigRequestManager.quickReactionIds
            val veIdsMap = allList.associateBy { it.id }

            // 已添加的 VoiceEmojiEntity 对象集合，用于避免重复
            val resultSet = LinkedHashSet<VoiceEmojiEntity>(6)

            // 首先按 qrIds 顺序添加，保证顺序
            qrIds.forEach { id ->
                veIdsMap[id]?.let { resultSet.add(it) }
            }

            // 如果 resultSet 的大小小于 6 个，尝试补充
            if (resultSet.size < 6) {
                allList.forEach { entity ->
                    if (resultSet.size >= 6) return@forEach
                    if (entity.id !in qrIds) {
                        resultSet.add(entity)
                    }
                }
            }

            resultSet.toList()
        }.flowOn(Dispatchers.Default)
    }


    /**
     * 同步普通表情列表
     */
    suspend fun syncVoiceEmojiList() {
        log(TAG, "syncVoiceEmojiList")
        val extra = localDS.getVoiceEmojiList().first()?.extra
        val resp = remoteDS.getVoiceEmojiList(extra ?: "")
        when (resp) {
            is Error -> {
                logError(TAG, "syncVoiceEmojiList error(${resp.code}):${resp.msg}")
            }
            is Success -> {
                localDS.updateVoiceEmojiList(resp.data)
                initVECategoryList(resp.data)
            }
        }
    }

    /**
     * 保存选中的 Tab
     */
    fun saveSelectTab(category: VoiceEmojiCategory) {
        localDS.saveSelectTab(category)
    }

    fun getSelectedTab(): Flow<VoiceEmojiCategory?> {
        return localDS.getSelectedTab()
    }

    fun getSelectedTabWithoutFlow(): VoiceEmojiCategory? {
        return localDS.getSelectedTabWithoutFlow()
    }

    /**
     * 是否展示banner tips
     */
    fun isShowBannerTips(): Flow<Boolean> = localDS.isShowBannerTips()

    suspend fun setShowBannerTips(show: Boolean) {
        localDS.setShowBannerTips(show)
    }

    fun initVELatestTimestamp() {
        if (CommonMMKV.voiceEmojiLatestEntryNotifyTimestamp == 0) {
            CommonMMKV.voiceEmojiLatestEntryNotifyTimestamp = AppConfigRequestManager.voiceEmojiLatestTimestamp
        }
        if (CommonMMKV.voiceEmojiLatestTabNotifyTimestamp == 0) {
            CommonMMKV.voiceEmojiLatestTabNotifyTimestamp = AppConfigRequestManager.voiceEmojiLatestTimestamp
        }
    }

    private fun initVECategoryList(data: ResponseGetSystemVoicemojis) {
        if (CommonMMKV.voiceEmojiLatestCategoryTimestamp.isNullOrEmpty()) {
            val list = data.categories?.map {
                it.categoryType to it.latestTimestamp.getIntDefault()
            }
            if (list != null) {
                CommonMMKV.voiceEmojiLatestCategoryTimestamp =
                    list.joinToString(";") { "${it.first},${it.second}" }
            }
        }
    }

    /**
     * 更新入口红点时间戳
     */
    suspend fun updateVELatestEntryNotifyTimestamp(timestamp: Int) {
        localDS.updateVELatestEntryNotifyTimestamp(timestamp)
    }

    /**
     * 获取入口红点时间戳
     */
    fun getVELatestEntryNotifyTimestamp(): StateFlow<Int> {
        return localDS.getVELatestEntryNotifyTimestamp()
    }

    /**
     * 更新1级tab红点时间戳
     */
    suspend fun updateVELatestTabTimestamp(timestamp: Int) {
        localDS.updateVELatestTabTimestamp(timestamp)
    }

    /**
     * 获取1级tab红点时间戳
     */
    fun getVELatestTabTimestamp(): StateFlow<Int> {
        return localDS.getVELatestTabTimestamp()
    }

    /**
     * 更新2级tab红点时间戳
     */
    fun updateVELatestTimestamp(type: Int, timestamp: Int) {
        localDS.updateVELatestTimestamp(type, timestamp)
    }

    /**
     * 获取2级tab红点时间戳
     */
    fun getVELatestTimestamp(): List<Pair<Int, Int>> {
        return localDS.getVELatestTimestamp()
    }
}