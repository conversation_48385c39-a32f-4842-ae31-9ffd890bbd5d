package com.interfun.buz.biz.center.voicemoji.datasource.blindbox

import android.content.Context
import androidx.annotation.Keep
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.stringPreferencesKey
import com.buz.idl.voicemoji.response.ResponseGetBlindBoxes
import com.google.gson.Gson
import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.biz.center.voicemoji.model.blindbox.RequestBlindType
import com.interfun.buz.biz.center.voicemoji.model.blindbox.VoiceEmojiBlindData
import com.interfun.buz.biz.center.voicemoji.model.blindbox.convert
import com.interfun.buz.common.manager.cache.userPreferencesDataStore
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext

/**
 * Author: ChenYouSheng
 * Date: 2024/12/17
 * Email: <EMAIL>
 * Desc:
 */
@Keep
data class StoredBlindBox(val blindBox: VoiceEmojiBlindData, val extra: String?)

/**
 * 盲盒表情本地数据
 */
class LocalBlindBoxDataSource(val context: Context = appContext) {

    companion object {
        const val TAG = "LocalBlindBoxDataSource"
    }

    private val Context.blindBoxDataStore: DataStore<Preferences> by userPreferencesDataStore("blind-box")
    private val KEY_BB_LIST = stringPreferencesKey("blind-box-list")
    private val KEY_BB_INIT = booleanPreferencesKey("blind-box-init")
    private val gson = Gson()


    /**
     * 获取盲盒表情
     */
    fun getBlindBoxList(): Flow<StoredBlindBox?> {
        return context.blindBoxDataStore.data.map { sp ->
            val json = sp[KEY_BB_LIST]
            try {
                logInfo(TAG, "getBlindBoxList:$json")
                val response = gson.fromJson(json, ResponseGetBlindBoxes::class.java)
                val voiceEmojiBlindData = response.convert()
                StoredBlindBox(voiceEmojiBlindData, response.extra)
            } catch (t: Throwable) {
                null
            }
        }.flowOn(Dispatchers.IO)
    }

    fun isInit(): Flow<Boolean> {
        return context.blindBoxDataStore.data.map { sp ->
            sp[KEY_BB_INIT] ?: false
        }.flowOn(Dispatchers.IO)
    }


    /**
     * 更新盲盒表情本地缓存
     */
    suspend fun updateBlindBoxList(requestType: RequestBlindType, data: ResponseGetBlindBoxes) {
        withContext(Dispatchers.Default) {
            val json = gson.toJson(data)
            logInfo(TAG, "updateBlindBoxList:$json")
            context.blindBoxDataStore.updateData { pref ->
                pref.toMutablePreferences().apply {
                    this[KEY_BB_LIST] = json
                    if (requestType == RequestBlindType.Init) {
                        this[KEY_BB_INIT] = true
                    }
                }
            }
        }
    }

}