package com.interfun.buz.biz.center.voicemoji.model.blindbox

import com.interfun.buz.biz.center.voicemoji.model.voiceemoji.VoiceEmojiCategory
import com.interfun.buz.biz.center.voicemoji.model.voiceemoji.VoiceEmojiDataType

interface BlindBoxDataType: VoiceEmojiDataType

/**
 * 盲盒tips
 */
data class VoiceEmojiBlindTips(val tips: String, val status: BlindBoxTipsStatus) : BlindBoxDataType

/**
 * 盲盒分类
 */
data class VoiceEmojiBlindCategory(
    val category: VoiceEmojiCategory,
    val startedDays: Int,
    val h5Url: String?
) : BlindBoxDataType

/**
 * 盲盒
 */
data class VoiceEmojiBlindBoxItemBean(
    val blindBox: VoiceEmojiBlindBox,
    val tipStatus: BlindBoxTipsStatus
) : BlindBoxDataType

