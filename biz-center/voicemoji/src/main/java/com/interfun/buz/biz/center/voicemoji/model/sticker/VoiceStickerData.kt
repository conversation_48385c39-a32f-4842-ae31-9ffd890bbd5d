package com.interfun.buz.biz.center.voicemoji.model.sticker

import com.interfun.buz.biz.center.voicemoji.model.voiceemoji.VoiceEmojiCategory
import com.interfun.buz.biz.center.voicemoji.model.voicegif.VoiceGifEntity

/**
 * Author: <PERSON><PERSON>ouSheng
 * Date: 2024/12/20
 * Email: chenyo<PERSON><PERSON>@vocalbeats.com
 * Desc: 语音sticker表情
 */
data class VoiceStickerData(val stickerList: List<VoiceGifEntity>, val category: VoiceEmojiCategory)

