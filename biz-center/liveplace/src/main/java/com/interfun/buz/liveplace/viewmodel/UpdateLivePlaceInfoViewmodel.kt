package com.interfun.buz.liveplace.viewmodel

import android.app.Application
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.SystemClock
import androidx.annotation.FloatRange
import androidx.lifecycle.viewModelScope
import androidx.media3.common.Player
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.base.ktx.toast
import com.interfun.buz.base.ktx.withIOContext
import com.interfun.buz.base.ktx.withMainContext
import com.interfun.buz.base.utils.FileUtil
import com.interfun.buz.biz.center.liveplace.R
import com.interfun.buz.common.ktx.isSuccess
import com.interfun.buz.common.utils.ImageUtils
import com.interfun.buz.compose.components.MusicItemStatus
import com.interfun.buz.compose.components.SongInfo
import com.interfun.buz.liveplace.bean.update.BgImgInfoWrapper
import com.interfun.buz.liveplace.bean.update.SongInfoWrapper
import com.interfun.buz.liveplace.repository.BgImgResource
import com.interfun.buz.liveplace.repository.BgmResource
import com.interfun.buz.liveplace.repository.LivePlaceBaseInfoUpdateRepository
import com.interfun.buz.liveplace.repository.LivePlaceVolumeRepository
import com.interfun.buz.liveplace.track.LivePlaceTracker
import com.interfun.buz.onair.helper.SeatBgPaletteHelper
import com.interfun.buz.onair.standard.*
import com.lizhi.component.tekiapm.TekiApm
import com.yibasan.lizhifm.lzlogan.Logz
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.collections.immutable.PersistentList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toPersistentList
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import okhttp3.internal.toHexString
import java.io.File
import java.util.Locale
import javax.inject.Inject

interface IUpdateLivePlaceInfo {

    fun requestInfo()

    // ==[ 获取信息类 ]==
    fun obtainSongListFlow(): StateFlow<PersistentList<SongInfoWrapper>>
    fun obtainVisibleFlow(): StateFlow<LivePlaceVisibleTypeEnum>
    fun obtainBgImgFlow(): StateFlow<PersistentList<BgImgInfoWrapper>>
    fun obtainTopicFlow(): StateFlow<String>
    fun obtainUpdateRetFlow(): StateFlow<UpdateLivePlaceStatus>
    fun obtainVolumeFlow(): StateFlow<Float>
    // ==[ 获取信息类结束 ]==

    // ==[ 更新接口 ]==
    fun userUpdateTopic(topic: String)
    fun resetVolume()
    fun userUpdateBgInfo(selectInfo: BgImgInfoWrapper)
    fun userUpdateVisible(visible: LivePlaceVisibleTypeEnum)
    fun userUpdateSong(songInfoWrapper: SongInfoWrapper)
    fun userUpdateVolume(@FloatRange(from = 0.0, to = 0.0) volume: Float)
    fun updateInfoToServer()
    fun pauseMusic()
    fun resumeMusic()
    // ==[ 更新接口结束 ]==
}

@HiltViewModel
class UpdateLivePlaceInfoViewmodel @Inject constructor(
    private val infoRepository: LivePlaceBaseInfoRepository,
    private val updateRepository: LivePlaceBaseInfoUpdateRepository,
    private val volumeRepository: LivePlaceVolumeRepository,
    private val application: Application,
) : AConfigBgImgViewModel(), ILivePlaceViewModel by LivePlaceViewModelHelp(), IUpdateLivePlaceInfo {
    // ==[ 请求状态 ]==
    private val _updateLivePlaceStateFlow = MutableStateFlow(UpdateLivePlaceStatus.NOT_DO)
    private val updateLpStateFlow = _updateLivePlaceStateFlow.asStateFlow()
    // ==[ 请求状态结束 ]==

    private val TAG = "UpdateLivePlaceInfoViewmodel"

    // ==[ 可见性 ]==
    private val _visibleTypeFlow = MutableStateFlow(LivePlaceVisibleTypeEnum.EVERYONE)
    private var manualSelectVisibleFlag = false
    private val visibleTypeFlow = _visibleTypeFlow.asStateFlow()
    private var serverVisibleType: LivePlaceVisibleTypeEnum? = null
    // ==[ 可见性结束 ]==

    // ==[ 话题 ]==
    private val _topicFlow: MutableStateFlow<String> = MutableStateFlow<String>("")
    private var manualUpdateTopicFlag = false
    private var serverTopic: String? = null
    private val topicFlow: StateFlow<String> = _topicFlow.asStateFlow()
    // ==[ 话题结束 ]==

    // ==[ 背景相关 ]==
    private val _bgImgOptionsInfoFlow =
        MutableStateFlow<PersistentList<BgImgInfoWrapper>>(persistentListOf())

    //服务端配置的背景
    private var serverSelectBgImgUrl: Uri? = null
    private var serverSelectBgImgUrlIsCustom: Boolean = false
    private val bgOptionsInfoFlow = _bgImgOptionsInfoFlow.asStateFlow()
    private var manualUpdateBgImgFlag = false
    private var uploadImgDir = FileUtil.getCacheDir(application, "upLoadTmp")

    // ==[ 背景相关结束 ]==

    // ==[ 背景音乐相关 ]==
    private val _songListFlow by lazy {
        MutableStateFlow(
            persistentListOf(
                SongInfoWrapper.NO_SOUND.copy(
                    isSelect = true
                )
            )
        )
    }
    private val songListFlow = _songListFlow.asStateFlow()

    //音量
    private val _volumeFlow: MutableStateFlow<Float> = MutableStateFlow(1f)

    //存储原始音量
    private var originVolume: Float? = null

    //服务端配置的音乐
    private var serverSelectSongUrl: Uri? = null

    //手动选择音乐的标记
    private var manualSelectSongFlag = false
    // ==[ 背景音乐相关结束 ]==

    private var initRequestJob: Job? = null
    override fun requestInfo() {
        if (initRequestJob != null) {
            return
        }
        initRequestJob?.cancel()
        initRequestJob = viewModelScope.launch {
            //获取音量
            volumeRepository.volumeDataFlow().onEach {
                val bgmVolume = it.bgmVolume
                originVolume = bgmVolume
                _volumeFlow.emit(bgmVolume)
            }.launchIn(this)
            //获取所有音乐配置
            val targetIdWrapper = obtainLivePlaceParam().targetIdWrapper
            updateRepository
                //获取音乐列表配置
                .lpBgsConfigFromCacheAndNetFlow(
                    targetIdWrapper.uid, targetIdWrapper.gid
                ).map { config ->
                    //==[处理背景相关]==
                    handleBgImg(config.bgImgList)
                    //==[处理音乐相关]==
                    handleBgm(config.bgmList)
                }.launchIn(this)

            //服务端配置主要用于之前设置的音乐是哪个
            infoRepository.livePlaceFlow(targetIdWrapper.uid, targetIdWrapper.gid).filterNotNull()
                .map { it.bgmUrl }.distinctUntilChanged().map { Uri.parse(it) }.onEach { data ->
                    serverSelectSongUrl = data
                    //如果用户手动选择了音乐,则不需要自动选择
                    if (!manualSelectSongFlag) {
                        //获取当前选中的音乐
                        val selectUrl = _songListFlow.value.firstOrNull { it.baseInfo.uri == data }
                        //判断当前选中的音乐是否在新的音乐列表中,如果不在,则将新的音乐列表的第一个音乐选中
                        if (selectUrl != null && !selectUrl.isSelect) {
                            selectSong(selectUrl)
                        }
                    }
                }.launchIn(this)
            //服务端配置之前的主题
            infoRepository.livePlaceFlow(targetIdWrapper.uid, targetIdWrapper.gid).filterNotNull()
                .map { it.topic }.distinctUntilChanged().onEach { data ->
                    serverTopic = data
                    //如果用户手动选择了音乐,则不需要自动选择
                    if (!manualUpdateTopicFlag) {
                        _topicFlow.value = data
                    }
                }.launchIn(this)
            if (obtainLivePlaceParam().isMineLp()) {
                //服务端配置之前的主题
                infoRepository.livePlaceFlow(targetIdWrapper.uid, targetIdWrapper.gid)
                    .filterNotNull()
                    .map { it.visibleType }.distinctUntilChanged().onEach { data ->
                        serverVisibleType = data
                        //如果用户手动选择了音乐,则不需要自动选择
                        if (!manualSelectVisibleFlag) {
                            _visibleTypeFlow.value = data
                        }
                    }.launchIn(this)
            }

            //服务端配置当前使用哪个背景
            infoRepository.livePlaceFlow(targetIdWrapper.uid, targetIdWrapper.gid).filterNotNull()
                .map { Pair(it.bgImgUrl, it.isCustomizeBg) }.distinctUntilChanged()
                .map { Pair(Uri.parse(it.first), it.second) }.onEach { (data, isCustomBg) ->
                    serverSelectBgImgUrl = data
                    serverSelectBgImgUrlIsCustom = isCustomBg

                    //如果用户手动选择了音乐,则不需要自动选择
                    if (!manualUpdateBgImgFlag) {
                        val firstSelect = _songListFlow.first().firstOrNull { it.isSelect }
                        if (
                        //这个判断用于确保里面有一个可以被选中
                            _songListFlow.value.any { it.baseInfo.uri == data }
                            &&
                            //如果之前没有选中的元素或者选中的元素不是当前的元素
                            (firstSelect == null || firstSelect.baseInfo.uri != data)
                        ) {
                            _songListFlow.value = _songListFlow.value.map { song ->
                                val select = song.baseInfo.uri == data
                                song.copy(isSelect = select)
                            }.toPersistentList()
                        }
                    }
                }.launchIn(this)

            //监听音乐播放状态
            obtainLivePlaceRoom()?.obtainPlayBgmStatusFlow()
                ?.combine(songListFlow, { state, infos ->
                    updatePlayState(state)
                })?.launchIn(this)
        }
    }

    private fun updatePlayState(state: @Player.State Int) {
        val musicItemStatus = if (state == Player.STATE_BUFFERING) {
            MusicItemStatus.LOADING
        } else {
            MusicItemStatus.Playing
        }
        _songListFlow.value = _songListFlow.value.map { song ->
            if (song.baseInfo.uri == Uri.EMPTY) {
                song
            } else if (song.isSelect) {
                //只有当前选中的音乐才会有LOADING状态
                song.copy(status = musicItemStatus)
            } else {
                song.copy(status = MusicItemStatus.NO_PLAYING)
            }
        }.toPersistentList()
    }

    override fun obtainSongListFlow(): StateFlow<PersistentList<SongInfoWrapper>> {
        return songListFlow
    }

    override fun obtainVisibleFlow(): StateFlow<LivePlaceVisibleTypeEnum> {
        return visibleTypeFlow
    }

    override fun obtainBgImgFlow(): StateFlow<PersistentList<BgImgInfoWrapper>> {
        return bgOptionsInfoFlow
    }

    override fun obtainTopicFlow(): StateFlow<String> {
        return topicFlow
    }

    override fun obtainUpdateRetFlow(): StateFlow<UpdateLivePlaceStatus> {
        return updateLpStateFlow
    }

    override fun obtainVolumeFlow(): StateFlow<Float> {
        return _volumeFlow.asStateFlow()
    }

    override fun userUpdateTopic(topic: String) {
        if (topic == _topicFlow.value) {
            return
        }
        manualUpdateTopicFlag = true
        _topicFlow.value = topic
    }

    override fun resetVolume() {
        val originVolumeBackup = originVolume
        if (_volumeFlow.value != originVolumeBackup && originVolumeBackup != null) {
            obtainLivePlaceRoom()?.handleAction(AdjustBgmVolumeAction(originVolumeBackup))
        }
    }

    override fun userUpdateBgInfo(selectInfo: BgImgInfoWrapper) {
        if (selectInfo.isSelect) {
            return
        }
        viewModelScope.launch {
            manualUpdateBgImgFlag = true
            if (selectInfo.isUserUpdate) {
                _bgImgOptionsInfoFlow.value
                    //移除用之前冗余数据
                    .filter { !it.isUserUpdate }
                    //取消选中
                    .map { cur ->
                        cur.copy(isSelect = false)
                    }.toPersistentList().apply {
                        _bgImgOptionsInfoFlow.emit(this.add(selectInfo.copy(isSelect = true)))
                    }
            } else {
                _bgImgOptionsInfoFlow.value.map { cur ->
                    if (selectInfo == cur) {
                        cur.copy(isSelect = true)
                    } else {
                        cur.copy(isSelect = false)
                    }
                }.toPersistentList().apply {
                    _bgImgOptionsInfoFlow.emit(this)
                }
            }
        }
    }

    override fun userUpdateVisible(visible: LivePlaceVisibleTypeEnum) {
        if (visible == _visibleTypeFlow.value) {
            return
        }
        manualSelectVisibleFlag = true
        _visibleTypeFlow.value = visible
    }

    override fun userUpdateSong(songInfoWrapper: SongInfoWrapper) {
        if (songInfoWrapper.isSelect) {
            return
        }
        viewModelScope.launch {
            selectSong(songInfoWrapper)
            manualSelectSongFlag = true
        }
    }


    override fun userUpdateVolume(volume: Float) {
        viewModelScope.launch {
            _volumeFlow.emit(volume)
            obtainLivePlaceRoom()?.handleAction(AdjustBgmVolumeAction(volume))
        }
    }

    override fun updateInfoToServer() {
        viewModelScope.launch {
            //本地更新音量
            volumeRepository.updateVolumeForBgm(_volumeFlow.value)
            _updateLivePlaceStateFlow.emit(UpdateLivePlaceStatus.LOADING)
            //要上传的背景 ID
            var upLoadImgId: String? = null
            //要上传的背景取色
            var upLoadBgColor: String? = null
            //要上传的音乐 ID
            var upLoadBgmId: String? = null
            //要上传的 topic
            var upLoadTopic: String? = null
            //要上传的可见性配置
            var upLoadVisibleType: LivePlaceVisibleTypeEnum? = null
            //==[背景处理]==
            val bgInfo = _bgImgOptionsInfoFlow.value.firstOrNull { it.isSelect }
            if (bgInfo != null && bgInfo.isUserUpdate && bgInfo.upToServerPath == null) {
                val updateImgRet: UpdateRet? = withIOContext {
                    try {
                        uploadImgDir.mkdirs()
                        val tmpUploadTmpFile =
                            File(uploadImgDir, "bg_${System.currentTimeMillis()}.jpg")
                        if (tmpUploadTmpFile.exists()) {
                            tmpUploadTmpFile.delete()
                        }
                        val tmpUploadFile =
                            File(uploadImgDir, "bg_${System.currentTimeMillis()}_2.jpg")
                        val outputStream = tmpUploadTmpFile.outputStream()
                        application.contentResolver.openInputStream(bgInfo.baseInfo.imgUrl)
                            .use { imgIns ->
                                outputStream.use { out ->
                                    imgIns?.copyTo(out)
                                }
                            }
                        logInfo(
                            TAG,
                            "call createLivePlace write tmpUploadTmpFile ret: ${tmpUploadTmpFile.length()}"
                        )

                        if (tmpUploadFile.exists()) {
                            tmpUploadFile.delete()
                        }
                        ImageUtils.compressImage(tmpUploadTmpFile, tmpUploadFile)
                        val compressedBitmap = BitmapFactory.decodeFile(tmpUploadFile.absolutePath)
                        // 拿到压缩后的 bitmap 取色
                        if (compressedBitmap != null) {
                            upLoadBgColor = SeatBgPaletteHelper.obtainColorFromBitmap(compressedBitmap).let {
                                //转为要上传给服务端的 hex 色值 String
                                "#${it.toHexString().uppercase(Locale.US)}"
                            }
                        }
                        logInfo(
                            TAG,
                            "call createLivePlace compress tmpUploadFile " +
                                "ret: ${tmpUploadFile.length()}, bgColor: $upLoadBgColor"
                        )

                        val updateImgRet =
                            withMainContext { updateImg(Uri.fromFile(tmpUploadFile)) }

                        logInfo(
                            TAG, "call createLivePlace upload ret: ${updateImgRet}"
                        )
                        return@withIOContext updateImgRet
                    } catch (e: Exception) {
                        Logz.tag(TAG).e(e)
                        TekiApm.reportException(e)
                        return@withIOContext null
                    }
                }

                //复写成功进行压缩
                if (updateImgRet != null && updateImgRet.success) {
                    val bgImgUrl = updateImgRet.serverPath
                    _bgImgOptionsInfoFlow.emit(_bgImgOptionsInfoFlow.value.filter { !it.isUserUpdate }
                        .toPersistentList().add(bgInfo.copy(upToServerPath = bgImgUrl)))
                    upLoadImgId = bgImgUrl
                } else {
                    _updateLivePlaceStateFlow.emit(UpdateLivePlaceStatus.FAIL)
                    return@launch
                }
            } else if (bgInfo != null && serverSelectBgImgUrl != bgInfo.baseInfo.imgUrl) {
                upLoadImgId = bgInfo.baseInfo.imgUrl.toString()
                if (bgInfo.isUserUpdate) {
                    // 如果没有上传新的，而是重新选择回之前上传的图片，那么还得拿回已知的取色的色值，再传回去
                    upLoadBgColor = bgInfo.baseInfo.bgImgColor
                }
            }

            //==[音乐处理]==
            val songInfo = _songListFlow.value.firstOrNull { it.isSelect }
            if (songInfo != null && songInfo.baseInfo.uri != serverSelectSongUrl && manualSelectSongFlag) {
                upLoadBgmId = songInfo.baseInfo.uri.toString()
            }

            //==[topic处理]==
            val topic = _topicFlow.value
            if (topic.isNotEmpty() && topic != serverTopic && manualUpdateTopicFlag) {
                upLoadTopic = topic
            }

            //==[可见性处理]==
            val visibleValue = _visibleTypeFlow.value
            if (obtainLivePlaceParam().isMineLp() && manualSelectVisibleFlag && visibleValue != serverVisibleType) {
                upLoadVisibleType = visibleValue
            }

            val targetIdWrapper = obtainLivePlaceParam().targetIdWrapper
            val reqStartTime = SystemClock.uptimeMillis()
            logInfo(
                TAG,
                "needUpLoadBgColor:${upLoadBgColor}, currentServerBgColor:${bgInfo?.baseInfo?.bgImgColor}"
            )
            val updateResponse = updateRepository.updateLpConfig(
                bgImgId = upLoadImgId,
                bgmId = upLoadBgmId,
                topic = upLoadTopic,
                bgImgColor = upLoadBgColor,
                visibleType = upLoadVisibleType,
                gid = targetIdWrapper.gid,
                uid = targetIdWrapper.uid
            )
            val endTime = SystemClock.uptimeMillis()
            if (updateResponse.isSuccess) {
                _updateLivePlaceStateFlow.emit(UpdateLivePlaceStatus.SUCCESS)
            } else {
                if (endTime - reqStartTime < 1000) {
                    //如果请求时间小于 1s 则延迟 1s
                    delay(1000)
                }
                toast(R.string.wt_network_error)
                _updateLivePlaceStateFlow.emit(UpdateLivePlaceStatus.FAIL)
            }

            LivePlaceTracker.onRB2025010206(
                isSuccess = updateResponse.isSuccess,
                topic = topic,
                whoCanJoin = if (visibleValue == LivePlaceVisibleTypeEnum.EVERYONE) {
                    "all"
                } else {
                    "invite"
                },
                backgroundId = if (bgInfo == null) "" else {
                    if (bgInfo.isUserUpdate) {
                        "$upLoadImgId"
                    } else {
                        "${bgInfo.baseInfo.id}"
                    }
                },
                bgmId = "${songInfo?.originData?.id ?: ""}",
            )
        }
    }

    override fun pauseMusic() {
        obtainLivePlaceRoom()?.handleAction(PauseBgmAction)
    }

    override fun resumeMusic() {
        obtainLivePlaceRoom()?.handleAction(ResumeBgmAction)
    }

    suspend fun selectSong(songInfoWrapper: SongInfoWrapper) {
        _songListFlow.value = _songListFlow.value.map { song ->
            song.copy(isSelect = song.baseInfo.uri == songInfoWrapper.baseInfo.uri)
        }.toPersistentList()
        obtainLivePlaceRoom()?.handleAction(PlayBgmAction(songInfoWrapper.baseInfo.uri))
    }

    private suspend fun handleBgm(config: PersistentList<BgmResource>) {
        val serverSelectSongUrlBackup = serverSelectSongUrl
        val selectUrl = if (manualSelectSongFlag || serverSelectSongUrlBackup == null) {
            //获取当前选中的音乐
            _songListFlow.first().first { it.isSelect }.baseInfo.uri
        } else {
            serverSelectSongUrlBackup
        }
        //判断当前选中的音乐是否在新的音乐列表中,如果不在,则将新的音乐列表的第一个音乐选中
        var selectUrlHit = false
        val newSongList = config.map { bgmResource ->
            //Start 转化为 UI 所需的数据结构 Start
            val songInfo = SongInfo(
                bgmResource, Uri.parse(bgmResource.musicUrl), bgmResource.name ?: ""
            )
            val isSelect = songInfo.uri == selectUrl
            if (isSelect) {
                selectUrlHit = true
            }
            SongInfoWrapper(
                songInfo,
                bgmResource,
                MusicItemStatus.NO_PLAYING,
                isSelect = isSelect,
            )
            //END 转化为 UI 所需的数据结构 END
        }.toPersistentList()//转化为可编辑列表
            //添加一个无声音的占位
            .add(
                0, SongInfoWrapper.NO_SOUND.copy(
                    //如果新列表没有那么选中第一个
                    isSelect = !selectUrlHit
                )
            )

        selectSong(newSongList.first { it.isSelect })
        _songListFlow.emit(newSongList)
    }

    private suspend fun handleBgImg(bgImgList: PersistentList<BgImgResource>) {
        var hitSelect = false
        val serverSelectBgImgUrlBackup = serverSelectBgImgUrl
        var bgImgCovertList = bgImgList.map { bgImgResource ->
            val select =
                serverSelectBgImgUrlBackup != null && bgImgResource.imgUrl == serverSelectBgImgUrlBackup
            if (select) {
                hitSelect = true
            }
            BgImgInfoWrapper(
                baseInfo = bgImgResource, isSelect = select, isUserUpdate = bgImgResource.customizeImg, bgImgResource.imgUrl.toString()
            )
        }.toPersistentList()


        //没有选中那么插入一个自定义
        if (!hitSelect && bgImgCovertList.isNotEmpty() && serverSelectBgImgUrlIsCustom &&serverSelectBgImgUrlBackup != null && !bgImgCovertList.any { it.baseInfo.customizeImg }) {
            //没有命中给第一个选中
            bgImgCovertList = bgImgCovertList.add(
                0,
                BgImgInfoWrapper(
                    isSelect = true,
                    isUserUpdate = true,
                    baseInfo = BgImgResource(
                        serverSelectBgImgUrlBackup,
                        null,
                        true,
                        -1L
                    ),
                    upToServerPath = serverSelectBgImgUrlBackup.toString()
                )
            ).toPersistentList()
        } else if (!hitSelect) {
            //没有命中给第一个选中
            bgImgCovertList = bgImgCovertList.mapIndexed { index, bgImgInfoWrapper ->
                if (index == 0) {
                    bgImgInfoWrapper.copy(isSelect = true)
                } else {
                    bgImgInfoWrapper
                }
            }.toPersistentList()
        }
        _bgImgOptionsInfoFlow.emit(bgImgCovertList)
    }
}