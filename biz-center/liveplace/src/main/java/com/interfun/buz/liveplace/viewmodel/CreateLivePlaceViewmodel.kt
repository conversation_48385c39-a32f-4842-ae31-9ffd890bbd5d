package com.interfun.buz.liveplace.viewmodel

import android.app.Application
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.SystemClock
import androidx.annotation.FloatRange
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import androidx.media3.common.Player
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.base.ktx.toast
import com.interfun.buz.base.ktx.withIOContext
import com.interfun.buz.base.ktx.withMainContext
import com.interfun.buz.base.utils.FileUtil
import com.interfun.buz.biz.center.liveplace.R
import com.interfun.buz.common.utils.ImageUtils
import com.interfun.buz.compose.components.MusicItemStatus
import com.interfun.buz.compose.components.SongInfo
import com.interfun.buz.liveplace.bean.update.BgImgInfoWrapper
import com.interfun.buz.liveplace.bean.update.SongInfoWrapper
import com.interfun.buz.liveplace.datasource.RemoteLivePlaceInfoDataSource.PlaceType
import com.interfun.buz.liveplace.repository.BgImgResource
import com.interfun.buz.liveplace.repository.BgmResource
import com.interfun.buz.liveplace.repository.LivePlaceBaseInfoUpdateRepository
import com.interfun.buz.liveplace.repository.LivePlaceVolumeRepository
import com.interfun.buz.liveplace.track.LivePlaceTracker
import com.interfun.buz.onair.helper.SeatBgPaletteHelper
import com.interfun.buz.onair.standard.AdjustBgmVolumeAction
import com.interfun.buz.onair.standard.LivePlaceType
import com.interfun.buz.onair.standard.PlayBgmAction
import com.lizhi.component.tekiapm.TekiApm
import com.yibasan.lizhifm.lzlogan.Logz
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.collections.immutable.PersistentList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toPersistentList
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import okhttp3.internal.toHexString
import java.io.File
import java.util.Locale
import javax.inject.Inject
import kotlin.random.Random

interface ICreateLivePlaceInfo {

    fun requestInfo()
    fun initJobIsRequestIsDone(): Boolean

    // ==[ 获取信息类 ]==
    fun obtainSongListFlow(): StateFlow<PersistentList<SongInfoWrapper>>
    fun obtainBgImgFlow(): StateFlow<PersistentList<BgImgInfoWrapper>>
    fun obtainUpdateRetFlow(): StateFlow<UpdateLivePlaceStatus>
    fun obtainVolumeFlow(): StateFlow<Float>
    // ==[ 获取信息类结束 ]==

    // ==[ 更新接口 ]==
    fun userUpdateBgInfo(selectInfo: BgImgInfoWrapper)
    fun userUpdateSong(songInfoWrapper: SongInfoWrapper)
    fun userUpdateVolume(@FloatRange(from = 0.0, to = 0.0) volume: Float)
    fun createLivePlace(topic: String)
    // ==[ 更新接口结束 ]==
}

@HiltViewModel
class CreateLivePlaceViewmodel @Inject constructor(
    private val repository: LivePlaceBaseInfoUpdateRepository,
    private val application: Application,
    private val updateRepository: LivePlaceBaseInfoUpdateRepository,
    private val savedStateHandle: SavedStateHandle,
    private val volumeRepository: LivePlaceVolumeRepository,
) :
    AConfigBgImgViewModel(), ILivePlaceViewModel by LivePlaceViewModelHelp(), ICreateLivePlaceInfo {
    private val TAG = "CreateLivePlaceViewmodel"


    // ==[ 背景相关 ]==
    private val _bgImgOptionsInfoFlow =
        MutableStateFlow<PersistentList<BgImgInfoWrapper>>(persistentListOf())

    //服务端配置的背景
    private val bgImgOptionsInfoFlow = _bgImgOptionsInfoFlow.asStateFlow()

    //标记上传过自定义背景
    var selectedCustomImg = false
    private var manualUpdateBgImgFlag = false
    private var isRandomFlag = false
    private var uploadImgDir = FileUtil.getCacheDir(application, "upLoadTmp")

    //音量
    private val _volumeFlow: MutableStateFlow<Float> = MutableStateFlow(1f)
    // ==[ 背景相关结束 ]==

    // ==[ 背景音乐相关 ]==
    private val _songListFlow by lazy {
        MutableStateFlow(
            persistentListOf(
                SongInfoWrapper.NO_SOUND.copy(
                    isSelect = true
                )
            )
        )
    }
    private val songListFlow = _songListFlow.asStateFlow()


    //手动选择音乐的标记
    private var manualSelectSongFlag = false
    // ==[ 背景音乐相关结束 ]==

    private val _updateLivePlaceStateFlow = MutableStateFlow(UpdateLivePlaceStatus.NOT_DO)
    private val updateLpStateFlow = _updateLivePlaceStateFlow.asStateFlow()


    private var initRequestJob: Job? = null
    override fun initJobIsRequestIsDone(): Boolean {
        return initRequestJob != null
    }

    override fun requestInfo() {
        if (initRequestJob != null && initRequestJob?.isActive == true) {
            return
        }
        initRequestJob?.cancel()
        initRequestJob = viewModelScope.launch {
            //获取音量
            volumeRepository.volumeDataFlow().onEach { _volumeFlow.emit(it.bgmVolume) }
                .launchIn(this)
            //获取所有音乐配置
            val targetIdWrapper = obtainLivePlaceParam().targetIdWrapper
            updateRepository
                //获取音乐列表配置
                .lpBgsConfigFromCacheAndNetFlow(
                    targetIdWrapper.uid, targetIdWrapper.gid
                ).map { config ->
                    Logz.tag(TAG).d("call requestInfo config $config")
                    //==[处理背景相关]==
                    handleBgImg(config.bgImgList)
                    //==[处理音乐相关]==
                    handleBgm(config.bgmList)
                }.launchIn(this)


            //监听音乐播放状态
            obtainLivePlaceRoom()?.obtainPlayBgmStatusFlow()
                ?.combine(songListFlow, { state, list ->
                    updateSongLoadingState(state)
                })?.launchIn(this)
        }
    }

    private fun updateSongLoadingState(state: Int) {
        //@Player.State
        val musicItemStatus = if (state == Player.STATE_BUFFERING) {
            MusicItemStatus.LOADING
        } else {
            MusicItemStatus.Playing
        }
        _songListFlow.value = _songListFlow.value.map { song ->
            if (song.baseInfo.uri == Uri.EMPTY) {
                song
            } else if (song.isSelect) {
                //只有当前选中的音乐才会有LOADING状态
                song.copy(status = musicItemStatus)
            } else {
                song.copy(status = MusicItemStatus.NO_PLAYING)
            }
        }.toPersistentList()
    }

    override fun obtainSongListFlow(): StateFlow<PersistentList<SongInfoWrapper>> {
        return songListFlow
    }

    override fun obtainBgImgFlow(): StateFlow<PersistentList<BgImgInfoWrapper>> {
        return bgImgOptionsInfoFlow
    }

    override fun obtainUpdateRetFlow(): StateFlow<UpdateLivePlaceStatus> {
        return updateLpStateFlow
    }

    override fun obtainVolumeFlow(): StateFlow<Float> {
        return _volumeFlow.asStateFlow()
    }

    override fun userUpdateBgInfo(selectInfo: BgImgInfoWrapper) {
        if (selectInfo.isSelect && !selectInfo.isUserUpdate) {
            return
        }
        viewModelScope.launch {
            if (selectInfo.isUserUpdate) {
                selectedCustomImg = true
            }
            manualUpdateBgImgFlag = true
            //产品需求:如果用户没有操作过音乐选择,那么选择背景的时候自动切换对应的音乐
            val relativeBgm = selectInfo.baseInfo.relativeBgm?.musicUrl
            if (!manualSelectSongFlag && !selectedCustomImg && !relativeBgm.isNullOrEmpty()) {
                val paddingSongUri = Uri.parse(relativeBgm)
                val paddingSong =
                    _songListFlow.value.firstOrNull { it.baseInfo.uri == paddingSongUri }
                if (paddingSong != null && !paddingSong.isSelect) {
                    selectSong(paddingSong)
                }
            }
            if (selectInfo.isUserUpdate) {
                _bgImgOptionsInfoFlow.value
                    //移除用之前冗余数据
                    .filter { !it.isUserUpdate }
                    //取消选中
                    .map { cur ->
                        cur.copy(isSelect = false)
                    }.toPersistentList().apply {
                        _bgImgOptionsInfoFlow.emit(this.add(selectInfo.copy(isSelect = true)))
                    }
            } else {
                _bgImgOptionsInfoFlow.value.map { cur ->
                    if (selectInfo == cur) {
                        cur.copy(isSelect = true)
                    } else {
                        cur.copy(isSelect = false)
                    }
                }.toPersistentList().apply {
                    _bgImgOptionsInfoFlow.emit(this)
                }
            }
        }
    }


    override fun userUpdateSong(songInfoWrapper: SongInfoWrapper) {
        if (songInfoWrapper.isSelect) {
            return
        }
        viewModelScope.launch {
            selectSong(songInfoWrapper)
            manualSelectSongFlag = true
        }
    }

    override fun userUpdateVolume(volume: Float) {
        viewModelScope.launch {
            _volumeFlow.emit(volume)
            obtainLivePlaceRoom()?.handleAction(AdjustBgmVolumeAction(volume))
        }
    }

    override fun createLivePlace(topic: String) {
        viewModelScope.launch {
            //更新本地音量
            volumeRepository.updateVolumeForBgm(_volumeFlow.value)

            val bgInfo = bgImgOptionsInfoFlow.value.firstOrNull { it.isSelect }
            val songInfo = songListFlow.value.firstOrNull { it.isSelect }
            logInfo(TAG, "call createLivePlace bgInfo $bgInfo songInfo $songInfo")
            if (bgInfo == null || songInfo == null) {
                _updateLivePlaceStateFlow.emit(UpdateLivePlaceStatus.FAIL)
                return@launch
            }
            _updateLivePlaceStateFlow.emit(UpdateLivePlaceStatus.LOADING)
            var bgImgUrl = bgInfo.baseInfo.imgUrl.toString()
            var bgColor: String? = null

            //上传自定义图片
            if (bgInfo.isUserUpdate && bgInfo.upToServerPath == null) {
                var uploadFailure: String = ""
                val updateImgRet: UpdateRet? = withIOContext {
                    try {
                        uploadImgDir.mkdirs()
                        val tmpUploadTmpFile =
                            File(uploadImgDir, "bg_${System.currentTimeMillis()}.jpg")
                        if (tmpUploadTmpFile.exists()) {
                            tmpUploadTmpFile.delete()
                        }
                        val tmpUploadFile =
                            File(uploadImgDir, "bg_${System.currentTimeMillis()}_2.jpg")
                        val outputStream = tmpUploadTmpFile.outputStream()
                        application.contentResolver.openInputStream(bgInfo.baseInfo.imgUrl)
                            .use { imgIns ->
                                outputStream.use { out ->
                                    imgIns?.copyTo(out)
                                }
                            }
                        logInfo(
                            TAG,
                            "call createLivePlace write tmpUploadTmpFile ret: ${tmpUploadTmpFile.length()}"
                        )

                        if (tmpUploadFile.exists()) {
                            tmpUploadFile.delete()
                        }
                        ImageUtils.compressImage(tmpUploadTmpFile, tmpUploadFile)
                        val compressedBitmap = BitmapFactory.decodeFile(tmpUploadFile.absolutePath)
                        if (compressedBitmap != null) {
                            bgColor = SeatBgPaletteHelper.obtainColorFromBitmap(compressedBitmap).let {
                                "#${it.toHexString().uppercase(Locale.US)}"
                            }
                        }
                        logInfo(
                            TAG,
                            "call createLivePlace compress tmpUploadFile " +
                                "ret: ${tmpUploadFile.length()}, bgColor: $bgColor"
                        )

                        val updateImgRet =
                            withMainContext { updateImg(Uri.fromFile(tmpUploadFile)) }

                        logInfo(
                            TAG,
                            "call createLivePlace upload ret: ${updateImgRet}"
                        )
                        return@withIOContext updateImgRet
                    } catch (e: Exception) {
                        Logz.tag(TAG).e(e)
                        uploadFailure = e.message ?: ""
                        TekiApm.reportException(e)
                        return@withIOContext null
                    }
                }
                //复写成功进行压缩
                if (updateImgRet != null && updateImgRet.success) {
                    bgImgUrl = updateImgRet.serverPath
                    _bgImgOptionsInfoFlow.emit(bgImgOptionsInfoFlow.value.filter { !it.isUserUpdate }
                        .toPersistentList().add(bgInfo.copy(upToServerPath = bgImgUrl)))
                } else {
                    _updateLivePlaceStateFlow.emit(UpdateLivePlaceStatus.FAIL)
                    toast(R.string.wt_network_error)
                    LivePlaceTracker.onRB2025010203(
                        false,
                        topic,
                        backgroundMusicId = "${songInfo.originData.id}",
                        backgroundId = "unknown",
                        "UploadImgFail:$uploadFailure"
                    )
                    return@launch
                }
            } else if (bgInfo.isUserUpdate && bgInfo.upToServerPath != null) {
                bgImgUrl = bgInfo.upToServerPath
                bgColor = bgInfo.baseInfo.bgImgColor
            }

            val bgmUrl = songInfo.originData.musicUrl

            logInfo(TAG, "call createLivePlace bgImgUrl $bgImgUrl bgmUrl $bgmUrl ")
            val gid =
                if (obtainLivePlaceParam().livePlaceType == LivePlaceType.GROUP) obtainLivePlaceParam().targetId else null
            val placeType = if (obtainLivePlaceParam().livePlaceType == LivePlaceType.GROUP) {
                PlaceType.GroupSpace
            } else {
                PlaceType.PersonalSpace
            }
            val reqTime = SystemClock.uptimeMillis()
            val createRet = repository.createLivePlace(
                gid = gid,
                bgImgId = bgImgUrl,
                bgmUrl = bgmUrl,
                topic = topic,
                placeType = placeType,
                bgImgColor = bgColor
            )
            val reqDoneTime = SystemClock.uptimeMillis()
            logInfo(TAG, "call createLivePlace req  ${createRet.code}")
            if (createRet.code == 0 && isActive) {
                LivePlaceTracker.onRB2025010203(
                    true,
                    topic,
                    backgroundMusicId = "${songInfo.originData.id}",
                    backgroundId = if (bgInfo.isUserUpdate) bgImgUrl else "${bgInfo.baseInfo.id}",
                    ""
                )
                _updateLivePlaceStateFlow.emit(UpdateLivePlaceStatus.SUCCESS)
            } else {
                if (reqDoneTime - reqTime < 1000) {
                    delay(1000)
                }
                LivePlaceTracker.onRB2025010203(
                    false,
                    topic,
                    backgroundMusicId = "${songInfo.originData.id}",
                    backgroundId = if (bgInfo.isUserUpdate) bgImgUrl else "${bgInfo.baseInfo.id}",
                    "${createRet.code}"
                )
                toast(R.string.network_error)
                _updateLivePlaceStateFlow.emit(UpdateLivePlaceStatus.FAIL)
            }

        }
    }

    private suspend fun handleBgm(config: PersistentList<BgmResource>) {
        val selectUrl = if (manualSelectSongFlag) {
            //获取当前选中的音乐
            _songListFlow.first().firstOrNull { it.isSelect }?.baseInfo?.uri
        } else {
            val musicUrl =
                _bgImgOptionsInfoFlow.value.firstOrNull { it.isSelect }?.baseInfo?.relativeBgm?.musicUrl
            //没有手动选中过音乐,那么选和背景对应的关联音乐
            if (!musicUrl.isNullOrEmpty()) {
                Uri.parse(musicUrl)
            } else {
                //获取当前选中的音乐
                Uri.EMPTY
            }
        }
        //判断当前选中的音乐是否在新的音乐列表中,如果不在,则将新的音乐列表的第一个音乐选中
        var selectUrlHit = false
        val newSongList = config.map { bgmResource ->
            //Start 转化为 UI 所需的数据结构 Start
            val songInfo = SongInfo(
                bgmResource, Uri.parse(bgmResource.musicUrl), bgmResource.name ?: ""
            )
            val isSelect = songInfo.uri == selectUrl
            if (isSelect) {
                selectUrlHit = true
            }
            SongInfoWrapper(
                songInfo,
                bgmResource,
                MusicItemStatus.NO_PLAYING,
                isSelect = isSelect,
            )
            //END 转化为 UI 所需的数据结构 END
        }.toPersistentList()//转化为可编辑列表
            //添加一个无声音的占位
            .add(
                0, SongInfoWrapper.NO_SOUND.copy(
                    //如果新列表没有那么选中第一个
                    isSelect = !selectUrlHit
                )
            )
        selectSong(newSongList.first { it.isSelect })
        _songListFlow.emit(newSongList)
    }

    private suspend fun handleBgImg(bgImgList: PersistentList<BgImgResource>) {
        val selectUrl = if (manualUpdateBgImgFlag || isRandomFlag) {
            _bgImgOptionsInfoFlow.value.firstOrNull { it.isSelect }?.baseInfo?.imgUrl
        } else {
            //产品要求随机选中一个
            bgImgList[Random.nextInt(0, bgImgList.size)].imgUrl
        }
        isRandomFlag = true
        val bgImgCovertList = bgImgList.mapIndexed { index, bgImgResource ->
            BgImgInfoWrapper(
                baseInfo = bgImgResource,
                isSelect = selectUrl == bgImgResource.imgUrl,
                isUserUpdate = false,
                ""
            )
        }.toPersistentList()
        _bgImgOptionsInfoFlow.emit(bgImgCovertList)
    }

    private suspend fun selectSong(songInfoWrapper: SongInfoWrapper) {
        _songListFlow.emit(_songListFlow.value.map { song ->
            song.copy(isSelect = song.baseInfo.uri == songInfoWrapper.baseInfo.uri)
        }.toPersistentList())
        obtainLivePlaceRoom()?.handleAction(PlayBgmAction(songInfoWrapper.baseInfo.uri))
    }

}