package com.interfun.buz.liveplace.repository

import android.app.Application
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import com.buz.idl.liveplace.bean.LivePlaceInfo
import com.buz.idl.liveplace.response.ResponseGetBackgroundResources
import com.buz.idl.liveplace.response.ResponseUpdateLivePlaceInfo
import com.interfun.buz.base.ktx.launchIO
import com.interfun.buz.base.ktx.logError
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.common.bean.Resp.Error
import com.interfun.buz.common.bean.Resp.Success
import com.interfun.buz.common.database.dao.LivePlaceBaseInfoDao
import com.interfun.buz.common.database.entity.ExistPlaceType
import com.interfun.buz.common.database.entity.LivePlaceBaseExistInfo
import com.interfun.buz.common.database.entity.LivePlaceBaseInfo
import com.interfun.buz.common.database.entity.PlaceType
import com.interfun.buz.common.ktx.isSuccess
import com.interfun.buz.common.utils.PromptUtil
import com.interfun.buz.common.utils.fromJson
import com.interfun.buz.common.utils.toJson
import com.interfun.buz.liveplace.datasource.RemoteLivePlaceInfoDataSource
import com.interfun.buz.onair.standard.LivePlaceBaseInfoRepository
import com.interfun.buz.onair.standard.LivePlaceScoped
import com.interfun.buz.onair.standard.UpdateLpInfoConfig
import com.interfun.buz.onair.standard.lpVisibleTypeConvertersInstance
import com.lizhi.itnet.lthrift.service.ITResponse
import com.yibasan.lizhifm.lzlogan.Logz
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.withContext
import javax.inject.Inject

class LivePlaceBaseInfoRepositoryImpl @Inject constructor(
    val dao: LivePlaceBaseInfoDao?,
    val remoteDataSource: RemoteLivePlaceInfoDataSource,
    @LivePlaceScoped val dataStore: DataStore<Preferences>,
    val application: Application
) : LivePlaceBaseInfoRepository {

    companion object {
        const val TAG = "LivePlaceBaseInfoRepositoryImpl"
    }

    override suspend fun updateLivePlaceInfoForUser(livePlaceInfo: LivePlaceInfo, uid: Long) {
        updateLivePlaceInfoToDB(livePlaceInfo = livePlaceInfo, uid = uid)
    }

    override suspend fun updateLivePlaceInfoToDB(
        livePlaceInfo: LivePlaceInfo,
        gid: Long?,
        uid: Long?
    ) {
        val type = if (uid != null) {
            PlaceType.PRIVATE
        } else {
            PlaceType.GROUP
        }
        val finalUid = uid ?: gid!!
        dao?.autoInsertOrUpdate(finalUid, livePlaceInfo, type)

    }

    override suspend fun updateLivePlaceInfoForGroup(livePlaceInfo: LivePlaceInfo, gid: Long) {
        updateLivePlaceInfoToDB(livePlaceInfo = livePlaceInfo, gid = gid)
    }


    private suspend fun insertExistImpl(
        livePlaceInfo: LivePlaceInfo?,
        gid: Long?,
        uid: Long?
    ) {
        val type = if (uid != null) {
            PlaceType.PRIVATE
        } else {
            PlaceType.GROUP
        }
        val finalUid = uid ?: gid!!
        dao?.insertLivePlaceBaseExistInfo(
            LivePlaceBaseExistInfo(
                finalUid,
                type,
                if (livePlaceInfo == null) {
                    ExistPlaceType.NO_EXIST
                } else {
                    ExistPlaceType.EXIST
                }
            )
        )
    }

    //- 进入他人/群组资料页
    //   - 本地判断，非好友关系，不请求
    //   - 非普通用户（官号、AI)，不请求
    //   - 优先展示本地缓存，异步请求刷新
    override fun livePlaceForUserFlow(
        uid: Long
    ): Flow<LivePlaceBaseInfo?> = livePlaceFlow(uid = uid)

    override fun livePlaceFlow(uid: Long?, gid: Long?): Flow<LivePlaceBaseInfo?> {
        return if (uid != null) {
            dao?.queryOriginLivePlace(uid, PlaceType.PRIVATE.serverNum) ?: flow { }
        } else if (gid != null) {
            dao?.queryOriginLivePlace(gid, PlaceType.GROUP.serverNum) ?: flow { }
        } else {
            flow { }
        }
    }

    override suspend fun getLivePlaceExistInfo(targetId: Long, type: PlaceType): ExistPlaceType {
        return dao?.queryLivePlaceBaseExistInfo(targetId, type.serverNum)?.existPlaceType
            ?: ExistPlaceType.PENDING
    }

    override fun getLivePlaceExistInfoFlow(
        targetId: Long,
        type: PlaceType
    ): Flow<LivePlaceBaseExistInfo?> {
        return dao?.queryLivePlaceBaseExistInfoFlow(targetId, type.serverNum) ?: flow { }
    }

    override fun livePlaceForGroupFlow(
        gid: Long
    ): Flow<LivePlaceBaseInfo?> = livePlaceFlow(gid = gid)

    override fun lpBgsConfigFlow(): SharedFlow<ResponseGetBackgroundResources> {
        return lpBgsConfigFlow.asSharedFlow()
    }

    override suspend fun requestLivePlaceForUser(uid: Long) {
        requestLivePlace(uid = uid)
    }

    private suspend fun requestLivePlaceImpl(uid: Long? = null, gid: Long? = null): Boolean {
        when (val resp = remoteDataSource.getLivePlaceInfo(uid = uid, gid = gid)) {
            is Error -> {
                logError(TAG, resp)
                return false
            }

            is Success -> {
                PromptUtil.parse(resp.prompt)
                insertExistImpl(livePlaceInfo = resp.data.livePlaceInfo, gid = gid, uid = uid)
                resp.data.livePlaceInfo?.let {
                    updateLivePlaceInfoToDB(it, gid = gid, uid = uid)
                }
                return true
            }
        }
    }

    override suspend fun requestLivePlaceForGroup(gid: Long) {
        requestLivePlace(gid = gid)
    }

    override suspend fun requestLivePlace(gid: Long?, uid: Long?): Boolean {
        return requestLivePlaceImpl(uid = uid, gid = gid)
    }

    private val lpBgsConfigFlow = MutableSharedFlow<ResponseGetBackgroundResources>(replay = 1)
    override suspend fun requestLPBgsConfigForUser(uid: Long) {
        requestLPBgsConfig(uid = uid)
    }

    override suspend fun requestLPBgsConfigForGroup(gid: Long) {
        requestLPBgsConfig(gid = gid)
    }

    override suspend fun updateLpInfo(
        gid: Long?,
        uid: Long?,
        updateLpInfoConfig: UpdateLpInfoConfig
    ): ITResponse<ResponseUpdateLivePlaceInfo> {
        val updateLpInfoResp =
            remoteDataSource.updateLpInfo(gid = gid, uid = uid, config = updateLpInfoConfig)
        val data = updateLpInfoResp.data?.livePlaceInfo
        if (updateLpInfoResp.isSuccess && data != null) {
            //更新到缓存
            updateLivePlaceInfoToDB(livePlaceInfo = data, gid = gid, uid = uid)
        }
        //更新是否存在
        if (updateLpInfoResp.isSuccess) {
            insertExistImpl(livePlaceInfo = data, gid = gid, uid = uid)
        }

        return updateLpInfoResp
    }

    private suspend fun requestLPBgsConfig(
        uid: Long? = null,
        gid: Long? = null
    ): ResponseGetBackgroundResources? {
        val backgroundResources = remoteDataSource.getBackgroundResources(uid = uid, gid = gid)
        val data = backgroundResources.data
        if (backgroundResources.code == 0 && data != null) {
            lpBgsConfigFlow.emit(data)
//            dataStore.data.map { preferences -> preferences[lpBgConfigLocalKey]="" }.firstOrNull()
            withContext(Dispatchers.IO) {
                dataStore.edit { settings ->
                    settings[lpBgConfigLocalKey] =
                        data.copy(bgImgResources = data.bgImgResources?.filter { !it.customizeImg })
                            .toJson()
                }
            }
            return data
        }
        return null

    }


    private val lpBgConfigLocalKey = stringPreferencesKey("lpBgConfigLocalKey")

    override fun lpConfigFromCacheAndNetFlow(
        uid: Long?,
        gid: Long?
    ): Flow<ResponseGetBackgroundResources> = channelFlow<ResponseGetBackgroundResources> {

        launchIO {
            var sendRet: ResponseGetBackgroundResources? = null
            val cacheInfo =
                dataStore.data.map { preferences -> preferences[lpBgConfigLocalKey] }
                    .firstOrNull()
            if (cacheInfo != null) {
                val cacheData = cacheInfo.fromJson<ResponseGetBackgroundResources>()
                if (cacheData != null) {
                    sendRet = cacheData
                    channel.send(cacheData)
                }
            } else {
                //磁盘缓存
                application.assets.open("lpBuiltInConfig.json").bufferedReader().use {
                    val cacheData = it.readText().fromJson<ResponseGetBackgroundResources>()
                    if (cacheData != null) {
                        sendRet = cacheData
                        channel.send(cacheData)
                    }
                }
            }
            val netRes = requestLPBgsConfig(uid = uid, gid = gid)
            if (netRes != null && sendRet != netRes) {
                channel.send(netRes)
            }
        }

    }


}
