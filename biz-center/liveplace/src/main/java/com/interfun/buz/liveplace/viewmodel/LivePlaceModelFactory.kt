package com.interfun.buz.liveplace.viewmodel

import android.os.Bundle
import androidx.lifecycle.AbstractSavedStateViewModelFactory
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewmodel.CreationExtras
import androidx.savedstate.SavedStateRegistryOwner
import com.interfun.buz.onair.standard.OnAirRoom
import com.interfun.buz.onair.standard.RoomParam
import kotlin.reflect.KClass

class LivePlaceModelFactory(
    private val owner: SavedStateRegistryOwner,
    private val roomParam: RoomParam,
    private val delegate: ViewModelProvider.Factory
) : ViewModelProvider.Factory {
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        val vm = delegate.create(modelClass)
        if (vm is ILivePlaceViewModel) {
            vm.initLivePlaceParam(roomParam)
        }
        return vm
    }

    override fun <T : ViewModel> create(modelClass: Class<T>, extras: CreationExtras): T {
        val vm = delegate.create(modelClass,extras)
        if (vm is ILivePlaceViewModel) {
            vm.initLivePlaceParam(roomParam)
        }
        return vm
    }

    override fun <T : ViewModel> create(modelClass: KClass<T>, extras: CreationExtras): T {
        val vm = delegate.create(modelClass,extras)
        if (vm is ILivePlaceViewModel) {
            vm.initLivePlaceParam(roomParam)
        }
        return vm
    }


}