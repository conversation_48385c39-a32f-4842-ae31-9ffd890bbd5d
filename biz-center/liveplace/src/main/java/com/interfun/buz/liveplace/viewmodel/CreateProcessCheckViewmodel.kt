package com.interfun.buz.liveplace.viewmodel

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.interfun.buz.onair.standard.ActivityReason
import com.interfun.buz.onair.standard.ChangeToPaddingAction
import com.interfun.buz.onair.standard.ExitRoomAtCreateByUser
import com.interfun.buz.onair.standard.FlagCreateRoomSuccessAction
import com.interfun.buz.onair.standard.OnAirLifecycle
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class CreateProcessCheckViewmodel @Inject constructor(val handle: SavedStateHandle) : ViewModel(),
    ILivePlaceViewModel by LivePlaceViewModelHelp() {

    companion object {
        val KEY_FLAG_CREATE_ROOM_SUCCESS = "KEY_FLAG_CREATE_ROOM_SUCCESS"
        val KEY_FLAG_TOPIC_SUCCESS = "KEY_FLAG_TOPIC_SUCCESS"
    }

    fun requestCreateRoomSuccess() {
        handle[KEY_FLAG_CREATE_ROOM_SUCCESS] = true
        obtainLivePlaceRoom()?.handleAction(FlagCreateRoomSuccessAction)
    }

    fun requestTopicSettingSuccess() {
        handle[KEY_FLAG_TOPIC_SUCCESS] = true
    }


    fun createRoomSuccess(): Boolean {
        val createRoomSuccess = handle.get<Boolean>(KEY_FLAG_CREATE_ROOM_SUCCESS) ?: false
        return createRoomSuccess

    }

    fun topicSettingSuccess(): Boolean {
        val createRoomSuccess = handle.get<Boolean>(KEY_FLAG_TOPIC_SUCCESS) ?: false
        return createRoomSuccess

    }

    fun exitRoomDurationCreate() {
        obtainLivePlaceRoom()?.handleAction(ExitRoomAtCreateByUser)
    }

    fun initCheck() = viewModelScope.launch {
        val room = obtainLivePlaceRoom()
        val param = obtainLivePlaceParam()
        if (param.activityReason == ActivityReason.JOIN_ROOM) {
            room?.handleAction(ChangeToPaddingAction)
        }
    }

    fun haveJoinDone(): Boolean {
        val lifecycleState = obtainLivePlaceRoom()?.obtainOnAirLifecycleFLow()?.value
        return lifecycleState is OnAirLifecycle.REQ_CREATE_SUCCESS_ON_AIR || lifecycleState is OnAirLifecycle.REQ_JOIN_SUCCESS_ON_AIR
    }

}