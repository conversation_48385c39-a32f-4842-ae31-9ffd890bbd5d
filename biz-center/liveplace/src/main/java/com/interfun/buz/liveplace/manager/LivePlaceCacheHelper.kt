package com.interfun.buz.liveplace.manager

import com.interfun.buz.base.ktx.launchIO
import com.interfun.buz.common.database.UserDatabase
import com.interfun.buz.common.database.entity.ExistPlaceType
import com.interfun.buz.common.database.entity.PlaceType
import com.interfun.buz.common.manager.userLifecycleScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * <AUTHOR>
 * @date 2025/2/7
 * @desc
 */
object LivePlaceCacheHelper {

    private val livePlaceDao get() = UserDatabase.currInstance?.getLivePlaceBaseInfoDao()
    private val existsInfoMap = mutableMapOf<Long, ExistPlaceType>()

    fun getExistsInfoFromMem(targetId: Long) = existsInfoMap[targetId] ?: ExistPlaceType.PENDING

    suspend fun getExistsInfoFromDao(targetId: Long, placeType: PlaceType): ExistPlaceType {
        return withContext(Dispatchers.IO) {
            val existType = livePlaceDao?.queryLivePlaceBaseExistInfo(
                targetId,
                placeType.serverNum
            )?.existPlaceType ?: ExistPlaceType.PENDING
            existsInfoMap[targetId] = existType
            existType
        }
    }

    /**
     * 在打开资料页之前同时获取数据库是否有创建 LivePlace，用于预设资料页初始化时的高度
     */
    fun updateExistsInfoToMem(targetId: Long, placeType: PlaceType) {
        userLifecycleScope?.launchIO {
            getExistsInfoFromDao(targetId, placeType)
        }
    }

    fun isLivePlaceExist(targetId: Long?) =
        targetId?.let { existsInfoMap[it] } == ExistPlaceType.EXIST
}