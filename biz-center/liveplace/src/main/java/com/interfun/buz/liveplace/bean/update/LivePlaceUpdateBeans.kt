package com.interfun.buz.liveplace.bean.update

import android.net.Uri
import android.os.Parcelable
import androidx.compose.runtime.Immutable
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.biz.center.liveplace.R
import com.interfun.buz.compose.components.MusicItemStatus
import com.interfun.buz.compose.components.SongInfo
import com.interfun.buz.liveplace.repository.BgImgResource
import com.interfun.buz.liveplace.repository.BgmResource
import kotlinx.parcelize.Parcelize


@Immutable
@Parcelize
data class SongInfoWrapper(
    val baseInfo: SongInfo,
    val originData: BgmResource,
    val status: MusicItemStatus,
    val isSelect: Boolean = false,
) : Parcelable {
    companion object {
        // 无声音的特殊占位
        val NO_SOUND = SongInfoWrapper(
            SongInfo(Uri.EMPTY, Uri.EMPTY, R.string.no_sound.asString()),
            BgmResource.EmptyBgmResource,
            MusicItemStatus.Playing,
            isSelect = false,
        )
    }
}


@Immutable
@Parcelize
data class BgImgInfoWrapper(
    val baseInfo: BgImgResource,
    val isSelect: Boolean = false,
    /**
     * 标记用户当前自己去上传了图
     */
    val isUserUpdate: Boolean = false,
    /**
     * 配合isUserUpdate使用,仅在isUserUpdate为true时有效
     * 用于上传图片的服务器路径
     */
    val upToServerPath: String? = null
) : Parcelable