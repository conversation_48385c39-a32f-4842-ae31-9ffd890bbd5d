package com.interfun.buz.liveplace.datasource

import androidx.annotation.FloatRange
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import com.interfun.buz.common.utils.fromJson
import com.interfun.buz.common.utils.toJson
import com.interfun.buz.onair.standard.LivePlaceDevicesScoped
import com.interfun.buz.onair.standard.LivePlaceScoped
import com.interfun.buz.onair.standard.OnAirVolumeInfo
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject

class LivePlaceLocalVolumeDatasource @Inject constructor(@LivePlaceDevicesScoped val dataStore: DataStore<Preferences>) {


    private val volumeKey = stringPreferencesKey("volume")
    private val SOUND_BOARD_INDEX = 1
    private val BGM_INDEX = SOUND_BOARD_INDEX + 1

    suspend fun updateVolumeForSoundBoard(@FloatRange(from = 0.0, to = 1.0) volumeInfo: Float) {
        editVolume(volumeInfo, SOUND_BOARD_INDEX)
    }

    suspend fun updateVolumeForBgm(@FloatRange(from = 0.0, to = 1.0) volumeInfo: Float) {
        editVolume(volumeInfo, BGM_INDEX)
    }

    fun volumeDataFlow(): Flow<OnAirVolumeInfo> {
        return dataStore.data.map { preferences ->
            preferences[volumeKey].fromJson<OnAirVolumeInfo>() ?: OnAirVolumeInfo()
        }
    }

    suspend private fun editVolume(
        @FloatRange(from = 0.0, to = 1.0) volumeInfo: Float,
        changeIndex: Int
    ) {
        dataStore.edit { settings ->
            val currentCounterValue = settings[volumeKey]
            val oldVolumeConfig =
                currentCounterValue.fromJson<OnAirVolumeInfo>() ?: OnAirVolumeInfo()
            settings[volumeKey] = if (changeIndex == SOUND_BOARD_INDEX) {
                oldVolumeConfig.copy(
                    soundBoardVolume = volumeInfo.coerceAtMost(1.0f).coerceAtLeast(0f)
                ).toJson()
            } else {
                oldVolumeConfig.copy(
                    bgmVolume = volumeInfo.coerceAtMost(1.0f).coerceAtLeast(0f)
                ).toJson()
            }
        }
    }

}