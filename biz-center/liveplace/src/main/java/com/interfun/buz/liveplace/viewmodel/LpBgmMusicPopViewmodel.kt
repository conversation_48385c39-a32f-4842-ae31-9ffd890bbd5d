package com.interfun.buz.liveplace.viewmodel

import android.app.Application
import android.net.Uri
import androidx.annotation.FloatRange
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.interfun.buz.compose.components.MusicItemStatus
import com.interfun.buz.compose.components.SongInfo
import com.interfun.buz.liveplace.bean.update.SongInfoWrapper
import com.interfun.buz.liveplace.repository.LivePlaceBaseInfoUpdateRepository
import com.interfun.buz.liveplace.repository.LivePlaceVolumeRepository
import com.interfun.buz.onair.standard.AdjustBgmVolumeAction
import com.interfun.buz.onair.standard.LivePlaceBaseInfoRepository
import com.interfun.buz.onair.standard.PlayBgmAction
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.collections.immutable.PersistentList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toPersistentList
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import javax.inject.Inject

interface ILpBgmMusicPopSetting {
    /**
     *  请求音乐列表
     */
    fun requestInfo()

    /**
     * 选择音乐
     */
    fun userSelectSong(songInfoWrapper: SongInfoWrapper)

    /**
     * 请求选中的音乐保存到服务端
     */
    fun requestSaveToServer()

    /**
     * 音乐列表流
     */
    fun songListFlow(): StateFlow<PersistentList<SongInfoWrapper>>

    /**
     * 改变音量
     */
    fun volumeChange(@FloatRange(from = 0.0, to = 0.0) volume: Float)
//    fun obtainVolumeFlow(): StateFlow<Float>

   suspend fun obtainInitVolume():Float
}

@HiltViewModel
class LpBgmMusicPopViewmodel @Inject constructor(
    private val infoRepository: LivePlaceBaseInfoRepository,
    private val updateRepository: LivePlaceBaseInfoUpdateRepository,
    private val application: Application,
    private val livePlaceVolumeRepository: LivePlaceVolumeRepository
) : ViewModel(), ILivePlaceViewModel by LivePlaceViewModelHelp(), ILpBgmMusicPopSetting {

    private val _songListFlow by lazy {
        MutableStateFlow(
            persistentListOf(
                SongInfoWrapper.NO_SOUND.copy(
                    isSelect = true
                )
            )
        )
    }

    //服务端配置的音乐
    private var serverSelectSongUrl: Uri? = null

    //手动选择音乐的标记
    private var manualSelectSongFlag = false

    //音量
    private val _volumeFlow: MutableStateFlow<Float> = MutableStateFlow(1f)

    private var beforeReqConfigJob: Job? = null

    override fun requestInfo() {
        beforeReqConfigJob?.cancel()
        beforeReqConfigJob = viewModelScope.launch {
            //获取音量
            livePlaceVolumeRepository.volumeDataFlow().onEach { _volumeFlow.emit(it.bgmVolume) }
                .launchIn(this)
            //获取所有音乐配置
            val targetIdWrapper = obtainLivePlaceParam().targetIdWrapper
            updateRepository
                //获取音乐列表配置
                .lpBgsConfigFromCacheAndNetFlow(
                    targetIdWrapper.uid, targetIdWrapper.gid
                ).map { it.bgmList }//转化为音乐列表
                //转化 UI 所需的数据结构,并添加一个无声音的占位
                .map { bgmResourceList ->
                    val serverSelectSongUrlBackup = serverSelectSongUrl
                    val selectUrl = if (manualSelectSongFlag || serverSelectSongUrlBackup == null) {
                        //获取当前选中的音乐
                        _songListFlow.first().first { it.isSelect }.baseInfo.uri
                    } else {
                        serverSelectSongUrlBackup
                    }
                    //判断当前选中的音乐是否在新的音乐列表中,如果不在,则将新的音乐列表的第一个音乐选中
                    var selectUrlHit = false
                    bgmResourceList.map { bgmResource ->
                        //Start 转化为 UI 所需的数据结构 Start
                        val songInfo = SongInfo(
                            bgmResource, Uri.parse(bgmResource.musicUrl), bgmResource.name ?: ""
                        )
                        val isSelect = songInfo.uri == selectUrl
                        if (isSelect) {
                            selectUrlHit = true
                        }
                        SongInfoWrapper(
                            songInfo,
                            bgmResource,
                            MusicItemStatus.NO_PLAYING,
                            isSelect = isSelect,
                        )
                        //END 转化为 UI 所需的数据结构 END
                    }.toPersistentList()//转化为可编辑列表
                        //添加一个无声音的占位
                        .add(
                            0, SongInfoWrapper.NO_SOUND.copy(
                                //如果新列表没有那么选中第一个
                                isSelect = !selectUrlHit
                            )
                        )
                }.onEach { songList ->
                    _songListFlow.value = songList
                }.launchIn(this)

            //服务端配置主要用于之前设置的音乐是哪个
            infoRepository.livePlaceFlow(targetIdWrapper.uid, targetIdWrapper.gid).filterNotNull()
                .map { it.bgmUrl }.distinctUntilChanged().map { Uri.parse(it) }.onEach { data ->
                    serverSelectSongUrl = data
                    //如果用户手动选择了音乐,则不需要自动选择
                    if (!manualSelectSongFlag) {
                        //获取当前选中的音乐
                        val selectUrl = _songListFlow.value.firstOrNull { it.baseInfo.uri == data }
                        //判断当前选中的音乐是否在新的音乐列表中,如果不在,则将新的音乐列表的第一个音乐选中
                        if (selectUrl != null && !selectUrl.isSelect) {
                            selectSong(selectUrl)
                        }
                    }
                }.launchIn(this)

            //监听音乐播放状态
            obtainLivePlaceRoom()?.obtainPlayBgmStatusFlow()?.onEach { state ->
                //@Player.State
                val musicItemStatus =
                    if (state == androidx.media3.common.Player.STATE_BUFFERING) {
                        MusicItemStatus.LOADING
                    } else {
                        MusicItemStatus.Playing
                    }
                _songListFlow.value = _songListFlow.value.map { song ->
                    if (song.baseInfo.uri == Uri.EMPTY) {
                        song
                    } else if (song.isSelect) {
                        //只有当前选中的音乐才会有LOADING状态
                        song.copy(status = musicItemStatus)
                    } else {
                        song.copy(status = MusicItemStatus.NO_PLAYING)
                    }
                }.toPersistentList()

            }?.launchIn(this)
        }
    }

    suspend fun selectSong(songInfoWrapper: SongInfoWrapper) {
        if (songInfoWrapper.isSelect) {
            return
        }
        _songListFlow.value = _songListFlow.value.map { song ->
            song.copy(isSelect = song.baseInfo.uri == songInfoWrapper.baseInfo.uri)
        }.toPersistentList()
        obtainLivePlaceRoom()?.handleAction(PlayBgmAction(songInfoWrapper.baseInfo.uri))
    }


    /**
     * 对外暴露函数,用户选择音乐
     */
    override fun userSelectSong(songInfoWrapper: SongInfoWrapper) {
        if (songInfoWrapper.isSelect) {
            return
        }
        viewModelScope.launch {
            manualSelectSongFlag = true
            selectSong(songInfoWrapper)
        }

    }

    override fun requestSaveToServer() {
        viewModelScope.launch {
            val songUri = _songListFlow.value.first { it.isSelect }.baseInfo.uri
            //只有跟服务端不一样才需要更新
            if (serverSelectSongUrl != songUri) {
                val param = obtainLivePlaceParam()
                val targetIdWrapper = param.targetIdWrapper
                updateRepository.updateLpConfig(
                    bgmId = songUri.toString(),
                    uid = targetIdWrapper.uid,
                    gid = targetIdWrapper.gid
                )
            }
        }
    }

    override fun songListFlow(): StateFlow<PersistentList<SongInfoWrapper>> {
        return _songListFlow.asStateFlow()
    }

    override fun volumeChange(volume: Float) {
        viewModelScope.launch {
            livePlaceVolumeRepository.updateVolumeForBgm(volume)
            obtainLivePlaceRoom()?.handleAction(AdjustBgmVolumeAction(volume))
        }
    }

//    override fun obtainVolumeFlow(): StateFlow<Float> {
//        return _volumeFlow.asStateFlow()
//    }

    override suspend fun obtainInitVolume(): Float {
       return livePlaceVolumeRepository.volumeDataFlow().first().bgmVolume
    }

}