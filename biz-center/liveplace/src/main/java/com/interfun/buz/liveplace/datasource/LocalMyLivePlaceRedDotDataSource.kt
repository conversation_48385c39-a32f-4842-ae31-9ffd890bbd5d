package com.interfun.buz.liveplace.datasource

import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.booleanPreferencesKey
import com.interfun.buz.onair.standard.LivePlaceScoped
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import javax.inject.Inject

/**
 * <AUTHOR>
 * @date 2025/1/7
 * @desc 新注册的用户（重登账号或重启 APP，都不再算新注册了）在以下位置显示红点
 * 1. 首页个人头像处
 * 2. 个人资料页创建空间入口处
 * 两个红点一起出现、一起消失
 * 红点记录为用户维度，同一台设备重新注册账号，符合条件仍然会显示红点
 */
class LocalMyLivePlaceRedDotDataSource @Inject constructor(
    @LivePlaceScoped val myLivePlaceDataStore: DataStore<Preferences>
) {
    companion object {
        const val TAG = "LocalMyLivePlaceDataSource"
    }

    private val keyHasShowRedDot = booleanPreferencesKey("hasShowRedDot")

    val hasShowRedDot = myLivePlaceDataStore.data.map {
        it[keyHasShowRedDot] ?: false
    }.distinctUntilChanged()

    suspend fun setHasShowRedDot(hasShown: Boolean) {
        myLivePlaceDataStore.updateData { pref ->
            pref.toMutablePreferences().apply {
                this[keyHasShowRedDot] = hasShown
            }
        }
    }
}