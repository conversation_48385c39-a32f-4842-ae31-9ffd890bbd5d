package com.interfun.buz.liveplace.track

import com.interfun.buz.base.ktx.application
import com.interfun.buz.base.ktx.launchIO
import com.interfun.buz.base.utils.FloatPermissionUtils
import com.interfun.buz.common.constants.TrackConstant
import com.interfun.buz.common.database.entity.ExistPlaceType
import com.interfun.buz.common.database.entity.PlaceType
import com.interfun.buz.common.ktx.uid
import com.interfun.buz.common.manager.ChannelStatusManager
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.manager.userLifecycleScope
import com.interfun.buz.common.utils.BuzTracker
import com.interfun.buz.liveplace.manager.LivePlaceCacheHelper
import kotlinx.coroutines.flow.dropWhile
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.withTimeoutOrNull

/**
 * 完全由 AI 读取打点文档生成
 * 一定有错误，需要人工矫正
 * 一定有错误，需要人工矫正
 * 一定有错误，需要人工矫正
 */
object LivePlaceTracker {

    /**
     * when user landing at my homepage(我的主页)
     */
    fun onAVS2022091402(
        hasCreated: Boolean,
        isLive: Boolean
    ) {
        BuzTracker.onPageViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AVS2022091402")
            put(TrackConstant.KEY_TITLE, "我的主页")
            put(TrackConstant.KEY_PAGE_TYPE, "home")
            put(TrackConstant.KEY_PAGE_CONTENT, if (hasCreated) "created" else "not_created")
            put(TrackConstant.KEY_PAGE_STATUS, if (isLive) "onlive" else "not_onlive")
        }
    }

    /**
     * when the live place is streaming
     */
    fun onAVS2025010204(
        topic: String?,
        joinOrStart: String,
        whoCanJoin: String,
        minimizeDuration: Long?,
        personNumber: Int
    ) {
        BuzTracker.onPageViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AVS2025010204")
            put(TrackConstant.KEY_TITLE, "live_place_stream_empty")
            put(TrackConstant.KEY_PAGE_TYPE, "live_place")
            if (topic != null) {
                put(TrackConstant.KEY_PAGE_CONTENT, topic)
            }
            put(TrackConstant.KEY_PAGE_STATUS, joinOrStart)
            put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, whoCanJoin)


            put(TrackConstant.KEY_BUSINESS_NUM, personNumber)
            if (minimizeDuration != null) {
                put(TrackConstant.KEY_ELEMENT_BUSINESS_ID, minimizeDuration.toString())
            }
        }
    }

    /**
     * when user landing at homepage
     */
    fun onAVS2025010205() {
        val myUid = UserSessionManager.uid
        userLifecycleScope?.launchIO {
            val existType = LivePlaceCacheHelper.getExistsInfoFromDao(myUid, PlaceType.PRIVATE)
            if (!ChannelStatusManager.hasRequestedFlow.value) {
                withTimeoutOrNull(5000) {
                    ChannelStatusManager.hasRequestedFlow
                        .dropWhile { !it }
                        .first()
                }
            }
            val privateOnLiveNumber = ChannelStatusManager.getPrivateOnLiveCount()
            val groupOnLiveNumber = ChannelStatusManager.getGroupOnLiveCount()
            BuzTracker.onPageViewScreen {
                put(TrackConstant.KEY_EXCLUSIVE_ID, "AVS2025010205")
                put(TrackConstant.KEY_TITLE, "homepage_liveplace_usage")
                put(TrackConstant.KEY_PAGE_TYPE, "home")
                put(
                    TrackConstant.KEY_PAGE_BUSINESS_TYPE,
                    if (privateOnLiveNumber > 0 || groupOnLiveNumber > 0) {
                        "able_to_join"
                    } else {
                        "not_able_to_join"
                    }
                )
                put(TrackConstant.KEY_PAGE_BUSINESS_ID, privateOnLiveNumber.toString())
                put(TrackConstant.KEY_PAGE_CONTENT, groupOnLiveNumber.toString())
                val status = when(existType){
                    ExistPlaceType.EXIST -> "created"
                    ExistPlaceType.NO_EXIST -> "not_created"
                    ExistPlaceType.PENDING -> "pending"
                }
                put(TrackConstant.KEY_PAGE_STATUS, status)
            }
        }
    }

    /**
     * when the live place introduction window being pop up to user
     */
    fun onVS2025010201(
        privateOrGroup: String
    ) {
        BuzTracker.onDialogViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "VS2025010201")
            put(TrackConstant.KEY_TITLE, "live_place_introduction")
            put(TrackConstant.KEY_PAGE_TYPE, "home")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, privateOrGroup)
        }
    }



    /**
     * when user click 'create my live place' button
     */
    fun onAC2025010201() {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2025010201")
            put(TrackConstant.KEY_TITLE, "my_homepage")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "live_place_entrance")
            put(TrackConstant.KEY_PAGE_TYPE, "home")
        }
    }



    /**
     * when user click 'who can join' button
     */
    fun onAC2025010203() {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2025010203")
            put(TrackConstant.KEY_TITLE, "live_place_settings")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "live_place_visibility")
            put(TrackConstant.KEY_PAGE_TYPE, "live_place")
        }
    }

    /**
     * when user click 'save' button in the live place visibility page
     */
    fun onAC2025010204(
        whoCanJoin: String
    ) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2025010204")
            put(TrackConstant.KEY_TITLE, "live_place_visibility_settings")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "live_place_visibility_save")
            put(TrackConstant.KEY_PAGE_TYPE, "live_place")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT, whoCanJoin)
        }
    }

    /**
     * when user click the 'knock knock' button
     */
    fun onAC2025010205(targetId:Long) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2025010205")
            put(TrackConstant.KEY_TITLE, "live_place_frontpage")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "live_place_knock")
            put(TrackConstant.KEY_PAGE_TYPE, "live_place")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, "$targetId")
            put(TrackConstant.KEY_PAGE_STATUS, "${UserSessionManager.uid}")
        }
    }

    /**
     * when user click 'place name' / 'ambient sound' / 'background' when in the live place modified setting
     */
    fun onAC2025010206(
        settingType: String
    ) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2025010206")
            put(TrackConstant.KEY_TITLE, "live_place_modified_settings")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "live_place_settings")
            put(TrackConstant.KEY_PAGE_TYPE, "live_place")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, settingType)
        }
    }

    /**
     * when click the share entrance during stream
     */
    fun onAC2025010207() {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2025010207")
            put(TrackConstant.KEY_TITLE, "live_place_ongoing")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "live_place_share_entrance")
            put(TrackConstant.KEY_PAGE_TYPE, "live_place")
        }
    }

    /**
     * when user click the respective share button
     */
    fun onAC2025010208(
        shareToApp: String?,
        shareWithinApp: String?
    ) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2025010208")
            put(TrackConstant.KEY_TITLE, "live_place_ongoing")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "live_place_share")
            put(TrackConstant.KEY_PAGE_TYPE, "live_place")
            if (shareToApp != null) {
                put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT, shareToApp)
            }
            if (shareWithinApp != null) {
                put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, shareWithinApp)
            }
        }
    }

    /**
     * when user click the minimize button during stream
     */
    fun onAC2025010209() {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2025010209")
            put(TrackConstant.KEY_TITLE, "live_place_ongoing")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "live_place_minimize")
            put(TrackConstant.KEY_PAGE_TYPE, "live_place")
            val type = if (FloatPermissionUtils.checkPermission(application)) "yes" else "no"
            put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, type)
        }
    }

    /**
     * when user click the 'live place chat entrance' during stream
     */
    fun onAC2025010210() {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2025010210")
            put(TrackConstant.KEY_TITLE, "live_place_ongoing")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "live_place_chat")
            put(TrackConstant.KEY_PAGE_TYPE, "live_place")
        }
    }

    /**
     * when user click 'send' in the live place chat
     */
    fun onAC2025010211(
        contextSent: String,
        privateOrGroup: String
    ) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2025010211")
            put(TrackConstant.KEY_TITLE, "live_place_chat")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "live_place_chat_send")
            put(TrackConstant.KEY_PAGE_TYPE, "live_place")
            // 修正此处，将 contextSent 放入正确的字段
            put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT, contextSent)
            put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, privateOrGroup)
        }
    }

    /**
     * when user click 'end' button
     */
    fun onAC2025010212(
        isPrivate: Boolean,
        isOwner: Boolean
    ) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2025010212")
            put(TrackConstant.KEY_TITLE, "live_place_ongoing")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "live_place_end")
            put(TrackConstant.KEY_PAGE_TYPE, "live_place")
            if (isPrivate) {
                put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT, if (isOwner) "host" else "others")
            }
            put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, if (isPrivate) "private" else "group")
        }
    }

    /**
     * when user either click 'accept' / 'cancel' / 'slide' for the live place in app notification
     */
    fun onAC2025010213(
        action: String
    ) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2025010213")
            put(TrackConstant.KEY_TITLE, "homepage")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "live_place_notification_action")
            put(TrackConstant.KEY_PAGE_TYPE, "home")
            // 修正此处，将 action 放入正确的字段
            put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, action)
        }
    }

    /**
     * when the live place notification being clicked by user
     */
    fun onAC2025010214(
        notificationType: String,
        privateOrGroup: String
    ) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2025010214")
            put(TrackConstant.KEY_TITLE, "live_place_notification")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "live_place_push_notification")
            put(TrackConstant.KEY_PAGE_TYPE, "push")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, privateOrGroup)
            // 修正此处，将 notificationType 放入正确的字段
            put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT, notificationType)
        }
    }



    /**
     * when user click 'edit' button for the live place chat message
     */
    fun onAC2025010216(
        privateOrGroup: String,
        messageId: String
    ) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2025010216")
            put(TrackConstant.KEY_TITLE, "live_place_chat")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "live_place_chat_edit")
            put(TrackConstant.KEY_PAGE_TYPE, "live_place")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, privateOrGroup)
            put(TrackConstant.KEY_ELEMENT_BUSINESS_ID, messageId)
        }
    }

    /**
     * when the live place notification being expose to user
     */
    fun onEE2025010201(
        privateOrGroup: String,
        notificationType: String
    ) {
        BuzTracker.onElementExposure {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "EE2025010201")
            put(TrackConstant.KEY_TITLE, "notification")
            put(TrackConstant.KEY_PAGE_TYPE, "push")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, privateOrGroup)
            put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, notificationType)
        }
    }

    /**
     * when there is result from user that has successfully set their live place ambient
     */
    fun onRB2025010201(
        isSuccess: Boolean,
        photoUrl: String?,
        failReason: String?
    ) {
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2025010201")
            put(TrackConstant.KEY_RESULT_TYPE, "live_place_background_photo_update_result")
            put(TrackConstant.KEY_PAGE_TYPE, "live_place")
            put(TrackConstant.KEY_IS_SUCCESS, if (isSuccess) "success" else "fail")
            if (photoUrl != null) {
                put(TrackConstant.KEY_CONTENT_NAME, photoUrl)
            }
            if (failReason != null) {
                put(TrackConstant.KEY_FAIL_REASON, failReason)
            }
        }
    }

    /**
     * when user has succesfully submit the live place's naming result
     */
    fun onRB2025010202(
        isSuccess: Boolean,
        livePlaceName: String
    ) {
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2025010202")
            put(TrackConstant.KEY_RESULT_TYPE, "live_place_naming_edit_result")
            put(TrackConstant.KEY_PAGE_TYPE, "live_place")
            put(TrackConstant.KEY_IS_SUCCESS, isSuccess)
            put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT, livePlaceName)
        }
    }

    /**
     * when user has created the live place
     */
    fun onRB2025010203(
        isSuccess: Boolean,
        topic: String?,
        backgroundMusicId: String,
        backgroundId: String?,
        failReason: String?
    ) {
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2025010203")
            put(TrackConstant.KEY_RESULT_TYPE, "live_place_create_result")
            put(TrackConstant.KEY_PAGE_TYPE, "live_place")
            put(TrackConstant.KEY_IS_SUCCESS, if (isSuccess) "success" else "fail")
            if (topic != null) {
                put(TrackConstant.KEY_BUSINESS_TYPE, topic)
            }
            put(TrackConstant.KEY_BUSINESS_ID, backgroundMusicId)
            if (backgroundId != null) {
                put(TrackConstant.KEY_CONTENT_ID, backgroundId)
            }
            if (failReason != null) {
                put(TrackConstant.KEY_FAIL_REASON, failReason)
            }
        }
    }

    /**
     * when user has start the stream
     */
    fun onRB2025010204(
        isSuccess: Boolean,
        notifyFriend: Boolean,
        topic: String,
        whoCanJoin: String,
        livePlaceId: String,
        failReason: String,
        source: String,
        pageBusinessType: String
    ) {
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2025010204")
            put(TrackConstant.KEY_RESULT_TYPE, "live_place_stream_result")
            put(TrackConstant.KEY_PAGE_TYPE, "live_place")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, pageBusinessType)
            put(TrackConstant.KEY_PAGE_STATUS, if (notifyFriend) "notify" else "dont_notify")
            put(TrackConstant.KEY_BUSINESS_TYPE, topic)
            put(TrackConstant.KEY_BUSINESS_ID, whoCanJoin)
            put(TrackConstant.KEY_SOURCE, source)
            put(TrackConstant.KEY_CONTENT_ID, livePlaceId)
            put(TrackConstant.KEY_IS_SUCCESS, if (isSuccess) "success" else "fail")
            put(TrackConstant.KEY_FAIL_REASON, failReason)
        }
    }

    /**
     * when user has enter the stream
     */
    fun onRB2025010205(
        isSuccess: Boolean,
        source: String,
        failReason: String
    ) {
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2025010205")
            put(TrackConstant.KEY_RESULT_TYPE, "live_place_enter_result")
            put(TrackConstant.KEY_PAGE_TYPE, "live_place")
            put(TrackConstant.KEY_SOURCE, source)
            put(TrackConstant.KEY_IS_SUCCESS,  if (isSuccess) "success" else "fail")
            put(TrackConstant.KEY_FAIL_REASON, failReason)
        }
    }

    /**
     * when user has succesfully modify a live place settings
     */
    fun onRB2025010206(
        isSuccess: Boolean,
        topic: String,
        whoCanJoin: String,
        backgroundId: String,
        bgmId: String,
    ) {
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2025010206")
            put(TrackConstant.KEY_RESULT_TYPE, "live_place_modify_settings")
            put(TrackConstant.KEY_PAGE_TYPE, "live_place")
            put(TrackConstant.KEY_IS_SUCCESS, isSuccess)
            put(TrackConstant.KEY_BUSINESS_TYPE, topic)
            put(TrackConstant.KEY_BUSINESS_ID, whoCanJoin)
            put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, backgroundId)
            put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT, bgmId)
        }
    }

    /**
     * when user has modify their volume during live place
     */
    fun onRB2025010207(volume: Float) {
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2025010207")
            put(TrackConstant.KEY_RESULT_TYPE, "live_place_background_music_volume")
            put(TrackConstant.KEY_PAGE_TYPE, "live_place")
            put(TrackConstant.KEY_BUSINESS_NUM, volume)
        }
    }

    /**
     * when the live place is being closed succesfully
     */
    fun onRB2025010208(
        livePlaceId: String,
//        duration: Long
    ) {
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2025010208")
            put(TrackConstant.KEY_RESULT_TYPE, "live_place_ended_result")
            put(TrackConstant.KEY_PAGE_TYPE, "live_place")
            put(TrackConstant.KEY_CONTENT_ID, livePlaceId)
//            put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT, duration.toString())
        }
    }

    /**
     * when received the rating for the live place from suer
     */
    fun onRB2025010209(
        rating: Int
    ) {
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2025010209")
            put(TrackConstant.KEY_RESULT_TYPE, "live_place_rating_result")
            put(TrackConstant.KEY_PAGE_TYPE, "home")
            put(TrackConstant.KEY_IS_SUCCESS, true)
            put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT, rating.toString())
        }
    }

    /**
     * every 5 minutes will trigeer it once (even when the app is in background)
     */
    fun onRB2025010210(
        inAppDuration: Long
    ) {
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2025010210")
            put(TrackConstant.KEY_RESULT_TYPE, "in_app_duration_result")
            put(TrackConstant.KEY_IS_SUCCESS, true)
            put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT, inAppDuration.toString())
        }
    }


    /**
     * 记录LivePlace访问页面的停留时间
     * @param targetId 目标ID（用户ID或群组ID）
     * @param isGroup 是否为群组
     * @param source 来源，可能值为 "profile_page" 或 "chat_history"
     * @param durationSeconds 停留时间，单位秒
     */
    fun onLivePlaceVisitPageDuration(source: String, durationSeconds: Int) {
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2025041601")
            put(TrackConstant.KEY_RESULT_TYPE, "live_place_knock_knock_stay_duration")
            put(TrackConstant.KEY_PAGE_TYPE, "live_place")
            put(TrackConstant.KEY_SOURCE, source)
            put(TrackConstant.KEY_LOG_TIME, "$durationSeconds")
        }
    }
}