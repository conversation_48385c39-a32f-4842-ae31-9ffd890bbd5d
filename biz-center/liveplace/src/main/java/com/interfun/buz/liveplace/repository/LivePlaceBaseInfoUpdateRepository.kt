package com.interfun.buz.liveplace.repository

import android.net.Uri
import android.os.Parcelable
import com.buz.idl.liveplace.response.ResponseCreateLivePlace
import com.interfun.buz.common.ktx.isSuccess
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.liveplace.datasource.RemoteLivePlaceInfoDataSource
import com.interfun.buz.liveplace.datasource.RemoteLivePlaceInfoDataSource.PlaceType
import com.interfun.buz.onair.standard.LivePlaceBaseInfoRepository
import com.interfun.buz.onair.standard.LivePlaceVisibleTypeEnum
import com.lizhi.itnet.lthrift.service.ITResponse
import kotlinx.collections.immutable.PersistentList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toPersistentList
import kotlinx.coroutines.flow.map
import kotlinx.parcelize.Parcelize
import javax.inject.Inject

@Parcelize
data class BgImgResource(
    val imgUrl: Uri,
    val relativeBgm: BgmResource?,
    val customizeImg: Boolean = false,
    val id: Long,
    val bgImgColor: String? = null
) : Parcelable

private fun com.buz.idl.liveplace.bean.BgmResource.convert(): BgmResource {
    return BgmResource(this.musicUrl, this.name, this.id ?: -1)
}

private fun com.buz.idl.liveplace.bean.BgImgResource.convert(bgmResources: List<com.buz.idl.liveplace.bean.BgmResource>): BgImgResource {
    val relationBgmResource = bgmResources.firstOrNull { it.id == this.bgmId }
    return BgImgResource(Uri.parse(this.imgUrl), relationBgmResource?.convert(), this.customizeImg,this.id?:-1, this.bgImgColor)
}

@Parcelize
data class BgmResource(
    val musicUrl: String,
    val name: String?,
    val id: Long
) : Parcelable {
    companion object {
        val EmptyBgmResource = BgmResource("", "", -1)
    }
}


data class ConfigResource(
    val bgImgList: PersistentList<BgImgResource>,
    val bgmList: PersistentList<BgmResource>
)


class LivePlaceBaseInfoUpdateRepository @Inject constructor(
    val livePlaceBaseInfo: LivePlaceBaseInfoRepository,
    val remoteDataSource: RemoteLivePlaceInfoDataSource
) {
    fun lpBgsConfigFlow() = livePlaceBaseInfo.lpBgsConfigFlow().map { remoteSourceResponse ->
        val bgmResourceList =
            remoteSourceResponse.bgmResources?.map { it.convert() }
                ?.toPersistentList() ?: persistentListOf()

        val bgImgResourceList =
            remoteSourceResponse.bgImgResources?.map {
                it.convert(
                    remoteSourceResponse.bgmResources ?: emptyList()
                )
            }
                ?.toPersistentList() ?: persistentListOf()
        ConfigResource(bgImgResourceList, bgmResourceList)
    }


    fun lpBgsConfigFromCacheAndNetFlow(
        uid: Long? = null,
        gid: Long? = null
    ) = livePlaceBaseInfo.lpConfigFromCacheAndNetFlow(uid, gid).map { remoteSourceResponse ->
        val bgmResourceList =
            remoteSourceResponse.bgmResources?.map { it.convert() }
                ?.toPersistentList() ?: persistentListOf()

        val bgImgResourceList =
            remoteSourceResponse.bgImgResources?.map {
                it.convert(
                    remoteSourceResponse.bgmResources ?: emptyList()
                )
            }
                ?.toPersistentList() ?: persistentListOf()
        ConfigResource(bgImgResourceList, bgmResourceList)
    }


    suspend fun requestLpBgs(
        ownerId: Long? = null,
        groupId: Long? = null
    ) {
        if (ownerId != null) {
            livePlaceBaseInfo.requestLPBgsConfigForUser(ownerId)
        } else if (groupId != null) {
            livePlaceBaseInfo.requestLPBgsConfigForGroup(groupId)
        }
    }

    suspend fun createLivePlace(
        gid: Long?,
        bgImgId: String,
        bgmUrl: String,
        topic: String,
        placeType: PlaceType,
        bgImgColor: String?
    ): ITResponse<ResponseCreateLivePlace> {
        val remoteSourceResponse =
            remoteDataSource.createLivePlace(
                gid = gid,
                bgImgUrl = bgImgId,
                bgmUrl = bgmUrl,
                topic = topic,
                placeType = placeType,
                bgImgColor = bgImgColor
            )
        val livePlaceInfo = remoteSourceResponse.data?.livePlaceInfo
        if (remoteSourceResponse.code == 0 && livePlaceInfo != null) {
            if (placeType == PlaceType.GroupSpace) {
                livePlaceBaseInfo.updateLivePlaceInfoForGroup(livePlaceInfo, gid!!)
            } else {
                livePlaceBaseInfo.updateLivePlaceInfoForUser(
                    livePlaceInfo,
                    UserSessionManager.getSessionUid()
                )
            }
        }
        return remoteSourceResponse
    }

    suspend fun updateLpConfig(
        bgImgId: String? = null,
        bgmId: String? = null,
        topic: String? = null,
        bgImgColor: String? = null,
        visibleType: LivePlaceVisibleTypeEnum? = null,
        gid: Long? = null,
        uid: Long? = null,
    ): Int {
        val updateLpConfigResp = remoteDataSource.updateLpConfig(
            bgImgUrl = bgImgId,
            bgmUrl = bgmId,
            topic = topic,
            bgImgColor = bgImgColor,
            gid = gid,
            uid = uid,
            visibleType = visibleType
        )
        val livePlaceInfo = updateLpConfigResp.data?.livePlaceInfo
        if (livePlaceInfo != null && updateLpConfigResp.isSuccess) {
            livePlaceBaseInfo.updateLivePlaceInfoToDB(livePlaceInfo, gid = gid, uid = uid)
        }
        return updateLpConfigResp.code
    }

}