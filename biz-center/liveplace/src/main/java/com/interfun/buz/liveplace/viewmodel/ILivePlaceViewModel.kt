package com.interfun.buz.liveplace.viewmodel

import com.interfun.buz.onair.standard.OnAirRoom
import com.interfun.buz.onair.standard.RoomParam

interface ILivePlaceViewModel {
    fun initLivePlaceParam(params: RoomParam)
    fun obtainLivePlaceRoom(): OnAirRoom?
    fun obtainLivePlaceParam(): RoomParam
}

class LivePlaceViewModelHelp : ILivePlaceViewModel {
    var onAirRoom: OnAirRoom? = null
    var roomParam: RoomParam? = null
    override fun initLivePlaceParam(params: RoomParam) {
        this.roomParam = params
        onAirRoom = params.obtainCorrespondingRoom()
    }

    override fun obtainLivePlaceRoom(): OnAirRoom? {
        return onAirRoom
    }

    override fun obtainLivePlaceParam(): RoomParam {
        return roomParam!!
    }

}