package com.interfun.buz.liveplace.viewmodel

import android.net.Uri
import androidx.lifecycle.ViewModel
import com.interfun.buz.common.manager.upload.CommonUploadManager
import com.interfun.buz.common.manager.upload.UploadParams
import com.interfun.buz.liveplace.track.LivePlaceTracker
import com.lizhi.itnet.upload.common.UploadTask
import com.lizhi.itnet.upload.model.UploadStatus

enum class UpdateLivePlaceStatus {
    NOT_DO, LOADING, SUCCESS, FAIL
}
internal data class UpdateRet(val success: <PERSON>olean, val serverPath: String)

abstract class AConfigBgImgViewModel : ViewModel() {

    internal suspend fun updateImg(uri: Uri): UpdateRet {
        val uploadParams = UploadParams()
        uploadParams.uri = uri
        cancelUpload()
        val task = CommonUploadManager.upload(uploadParams)!!
        upPicTask = task
        CommonUploadManager.untilComplete(task)

        if (task.status==UploadStatus.SUCCESS && task.uploadPath.isNotEmpty()) {
            LivePlaceTracker.onRB2025010201(true,task.uploadPath,"")
            return UpdateRet(true, task.uploadPath)
        }
        LivePlaceTracker.onRB2025010201(false,"","${task?.status?.code} ${task?.status?.msg}")
        return UpdateRet(false, "")
    }

    private var upPicTask: UploadTask? = null

    fun cancelUpload() {
        CommonUploadManager.cancel(upPicTask?.taskId)
        upPicTask = null
    }

    override fun onCleared() {
        super.onCleared()
        cancelUpload()
    }
}