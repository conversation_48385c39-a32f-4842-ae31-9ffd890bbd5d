package com.interfun.buz.liveplace.repository

import com.interfun.buz.common.manager.RegisterStatusManager
import com.interfun.buz.liveplace.datasource.LocalMyLivePlaceRedDotDataSource
import kotlinx.coroutines.flow.transform
import javax.inject.Inject

/**
 * <AUTHOR>
 * @date 2025/1/7
 * @desc 仅在非首次注册场景下展示红点
 */
class MyLivePlaceRedDotRepository @Inject constructor(
    private val localDS: LocalMyLivePlaceRedDotDataSource
) {

    val showRedDotFlow = localDS.hasShowRedDot.transform { hasShown ->
        emit(!hasShown && !RegisterStatusManager.isNewUserForOneTime)
    }

    suspend fun setHasShowRedDot(hasShown: Boolean = true) {
        localDS.setHasShowRedDot(hasShown)
    }
}