package com.interfun.buz.liveplace.repository

import androidx.annotation.FloatRange
import com.interfun.buz.liveplace.datasource.LivePlaceLocalVolumeDatasource
import javax.inject.Inject

class LivePlaceVolumeRepository @Inject constructor(private val data: LivePlaceLocalVolumeDatasource) {
    suspend fun updateVolumeForSoundBoard(@FloatRange(from = 0.0, to = 1.0) volumeInfo: Float) =
        data.updateVolumeForSoundBoard(volumeInfo)

    suspend fun updateVolumeForBgm(@FloatRange(from = 0.0, to = 1.0) volumeInfo: Float) =
        data.updateVolumeForBgm(volumeInfo)

    fun volumeDataFlow() = data.volumeDataFlow()
}