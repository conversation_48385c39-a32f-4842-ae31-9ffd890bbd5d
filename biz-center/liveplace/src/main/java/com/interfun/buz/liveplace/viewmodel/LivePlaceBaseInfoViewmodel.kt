package com.interfun.buz.liveplace.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.interfun.buz.common.database.entity.LivePlaceBaseInfo
import com.interfun.buz.onair.standard.LivePlaceBaseInfoRepository
import com.interfun.buz.onair.standard.LivePlaceType
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class LivePlaceBaseInfoViewmodel @Inject constructor(
    private val livePlaceInfoRepository: LivePlaceBaseInfoRepository,

    ) : ViewModel(), ILivePlaceViewModel by LivePlaceViewModelHelp() {

    val bgmUrlFlow: Flow<String?> by lazy {

        val lpBaseInfo = obtainOriginBaseInfo()
        lpBaseInfo.map {
            it?.bgmUrl
        }
    }


    val livePlaceInfoFlow by lazy {
        obtainOriginBaseInfo().stateIn(
            viewModelScope,
            SharingStarted.Eagerly, null
        )
    }

    fun obtainOriginBaseInfo(): Flow<LivePlaceBaseInfo?> {
        val param = obtainLivePlaceParam()
        return if (param.livePlaceType == LivePlaceType.PRIVATE) {
            livePlaceInfoRepository.livePlaceForUserFlow(param.targetId)
        } else {
            livePlaceInfoRepository.livePlaceForGroupFlow(param.targetId)
        }
    }


    fun requestUpdate() = viewModelScope.launch {
        val targetIdWrapper = obtainLivePlaceParam().targetIdWrapper
        livePlaceInfoRepository.requestLivePlace(
            uid = targetIdWrapper.uid,
            gid = targetIdWrapper.gid
        )
    }


}