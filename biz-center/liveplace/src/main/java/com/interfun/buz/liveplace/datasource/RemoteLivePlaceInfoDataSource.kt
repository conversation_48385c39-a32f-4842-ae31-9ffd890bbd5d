package com.interfun.buz.liveplace.datasource

import com.buz.idl.liveplace.bean.LivePlaceInfoUpdate
import com.buz.idl.liveplace.request.RequestCreateLivePlace
import com.buz.idl.liveplace.request.RequestGetBackgroundResources
import com.buz.idl.liveplace.request.RequestGetLivePlaceInfo
import com.buz.idl.liveplace.request.RequestUpdateLivePlaceInfo
import com.buz.idl.liveplace.response.ResponseCreateLivePlace
import com.buz.idl.liveplace.response.ResponseGetBackgroundResources
import com.buz.idl.liveplace.response.ResponseGetLivePlaceInfo
import com.buz.idl.liveplace.response.ResponseUpdateLivePlaceInfo
import com.buz.idl.liveplace.service.BuzNetLivePlaceServiceClient
import com.interfun.buz.assertutil.buzAssert
import com.interfun.buz.common.bean.Resp
import com.interfun.buz.common.ktx.isSuccess
import com.interfun.buz.common.net.withConfig
import com.interfun.buz.common.utils.PromptUtil
import com.interfun.buz.onair.standard.LivePlaceVisibleTypeEnum
import com.interfun.buz.onair.standard.UpdateLpInfoConfig
import com.lizhi.itnet.lthrift.service.ITResponse
import org.json.JSONObject
import javax.inject.Inject

/**
 * <AUTHOR>
 * @date 2025/1/6
 * @desc
 */
class RemoteLivePlaceInfoDataSource @Inject constructor() {

    companion object {
        const val TAG = "RemoteLivePlaceInfoDataSource"
    }

    private val client by lazy { BuzNetLivePlaceServiceClient().withConfig() }

    suspend fun getLivePlaceInfo(uid: Long?, gid: Long?): Resp<ResponseGetLivePlaceInfo> {
        val serverResp = client.getLivePlaceInfo(RequestGetLivePlaceInfo(uid, gid))
        val data = serverResp.data
        return if (serverResp.isSuccess && data != null) {
            Resp.Success(data, data.prompt)
        } else {
            Resp.Error(serverResp.code, serverResp.msg, data?.prompt)
        }
    }


    suspend fun getBackgroundResources(
        uid: Long?,
        gid: Long?
    ): ITResponse<ResponseGetBackgroundResources> {
        buzAssert(gid, uid)
        val requestRet =
            client.getBackgroundResources(RequestGetBackgroundResources(uid, gid))
        PromptUtil.parse(requestRet.data?.prompt)
        return requestRet
    }


    sealed class PlaceType(val origin: Int) {
        data object PersonalSpace : PlaceType(1)
        data object GroupSpace : PlaceType(2)
    }

    suspend fun createLivePlace(
        gid: Long?,
        bgImgUrl: String,
        bgmUrl: String,
        topic: String,
        placeType: PlaceType,
        bgImgColor: String?,
        startNotification: Boolean = true,
        visibleType: LivePlaceVisibleTypeEnum = LivePlaceVisibleTypeEnum.EVERYONE
    ): ITResponse<ResponseCreateLivePlace> {
        val createGroupRequest = RequestCreateLivePlace(
            livePlaceInfo = LivePlaceInfoUpdate(
                topic = topic,
                bgmUrl = bgmUrl,
                bgImgUrl = bgImgUrl,
                visibleType = visibleType.originNum,
                startNotification = startNotification,
                bgImgColor = bgImgColor
            ),
            groupId = gid,
            placeType = placeType.origin
        )
        val requestRet =
            client.createLivePlace(createGroupRequest)
        PromptUtil.parse(requestRet.data?.prompt)
        return requestRet


    }

    private fun buzAssert(
        groundId: Long?,
        uid: Long?,
    ) = buzAssert({
        (groundId == null && uid != null) || (groundId != null && uid == null)
    })


    suspend fun updateLpConfig(
        bgImgUrl: String? = null,
        bgmUrl: String? = null,
        topic: String? = null,
        bgImgColor: String? = null,
        visibleType: LivePlaceVisibleTypeEnum? = null,
        gid: Long?,
        uid: Long?,
    ): ITResponse<ResponseUpdateLivePlaceInfo> {
        val configJson = JSONObject()
        buzAssert(gid, uid)
        topic?.let { configJson.put("topic", it) }
        bgmUrl?.let { configJson.put("bgmUrl", it) }
        bgImgUrl?.let { configJson.put("bgImgUrl", it) }
        visibleType?.let { configJson.put("visibleType", it.originNum) }
        bgImgColor?.let { configJson.put("bgImgColor", it) }

        val createGroupRequest = RequestUpdateLivePlaceInfo(
            configJson.toString(),
            uid,
            gid
        )
        val requestRet =
            client.updateLivePlaceInfo(createGroupRequest)
        PromptUtil.parse(requestRet.data?.prompt)
        return requestRet
    }


    suspend fun updateLpInfo(
        gid: Long?,
        uid: Long?,
        config: UpdateLpInfoConfig
    ): ITResponse<ResponseUpdateLivePlaceInfo> {
        val inParamJson = JSONObject()

        if (!config.topic.isNullOrEmpty()) {
            inParamJson.put("topic", config.topic)
        }
        val visibleType = config.visibleType
        if (visibleType != null) {
            inParamJson.put("visibleType", visibleType.originNum)
        }

        val bgImgUrl = config.bgImgUrl
        if (bgImgUrl != null) {
            inParamJson.put("bgImgUrl", bgImgUrl)
        }
        val bgmUrl = config.bgmUrl
        if (bgmUrl != null) {
            inParamJson.put("bgmUrl", bgmUrl)
        }

        val updateLivePlaceInfo =
            client.updateLivePlaceInfo(RequestUpdateLivePlaceInfo(inParamJson.toString(), uid, gid))
        PromptUtil.parse(updateLivePlaceInfo.data?.prompt)
        return updateLivePlaceInfo
    }

}