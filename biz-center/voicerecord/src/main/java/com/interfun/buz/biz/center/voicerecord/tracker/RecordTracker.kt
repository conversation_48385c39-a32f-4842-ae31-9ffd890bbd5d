package com.interfun.buz.biz.center.voicerecord.tracker

import com.interfun.buz.common.constants.TrackConstant
import com.interfun.buz.common.utils.BuzTracker

/**
 * <AUTHOR>
 * @date 2025/6/13
 * @desc
 */
object RecordTracker {

    const val TYPE_CANCEL = "delete"
    const val TYPE_PREVIEW = "preview"
    const val TYPE_SEND = "send"

    fun onReleaseWhenLocking(
        isGroup: <PERSON><PERSON><PERSON>,
        targetId: Long,
        pageType: String,
        isVoiceFilterMode: Boolean,
        type: String,
        filterId: Long,
    ) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2025061102")
            put(TrackConstant.KEY_TITLE, "send_voice")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "handsfree_voicemsg_selection")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, if (isGroup) "group" else "private")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, targetId.toString())
            put(TrackConstant.KEY_SOURCE, pageType)
            put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT, type)
            put(TrackConstant.KEY_CONTENT_NAME, "$filterId")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, if (isVoiceFilterMode) "Y" else "N")
        }
    }

    fun onLockingModeResult(
        isGroup: Boolean,
        targetId: Long,
        pageType: String,
        isVoiceFilterMode: Boolean,
        filterId: Long,
    ){
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2025061101")
            put(TrackConstant.KEY_RESULT_TYPE, "handsfree_voicemsg_result")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, if (isGroup) "group" else "private")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, targetId.toString())
            put(TrackConstant.KEY_SOURCE, pageType)
            put(TrackConstant.KEY_CONTENT_NAME, "$filterId")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, if (isVoiceFilterMode) "Y" else "N")
        }
    }
}