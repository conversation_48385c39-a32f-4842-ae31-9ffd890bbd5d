package com.interfun.buz.biz.center.voicerecord.di

import android.content.Context
import android.media.AudioAttributes
import android.media.AudioManager
import com.interfun.buz.biz.center.voicerecord.repository.VoiceRecordRepository
import com.interfun.buz.biz.center.voicerecord.repository.VoiceRecordRepositoryImpl
import com.interfun.buz.common.manager.BuzAudioFocusManager
import com.interfun.buz.common.manager.FocusRequestConfig
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * <AUTHOR>
 * @date 2025/5/9
 * @desc Hilt 模块，提供 VoiceRecord 相关的依赖注入
 */
@Module
@InstallIn(SingletonComponent::class)
object VoiceRecordModule {

    /**
     * 提供 BuzAudioFocusManager 实例
     */
    @Provides
    @Singleton
    fun provideBuzAudioFocusManager(@ApplicationContext context: Context): BuzAudioFocusManager {
        return BuzAudioFocusManager(
            context = context,
            debugTag = "VoiceRecord",
            config = FocusRequestConfig(
                focusGain = AudioManager.AUDIOFOCUS_GAIN_TRANSIENT_EXCLUSIVE,
                contentType = AudioAttributes.CONTENT_TYPE_SPEECH
            )
        )
    }

    /**
     * 提供 VoiceRecordRepository 实例
     */
    @Provides
    @Singleton
    fun provideVoiceRecordRepository(
        buzAudioFocusManager: BuzAudioFocusManager
    ): VoiceRecordRepository {
        return VoiceRecordRepositoryImpl(buzAudioFocusManager) as VoiceRecordRepository
    }
}