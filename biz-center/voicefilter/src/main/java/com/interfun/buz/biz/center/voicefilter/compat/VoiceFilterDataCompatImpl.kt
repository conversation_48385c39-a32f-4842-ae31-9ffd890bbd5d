package com.interfun.buz.biz.center.voicefilter.compat

import com.interfun.buz.biz.center.voicefilter.di.VoiceFilterBizCenterMediator
import com.interfun.buz.biz.center.voicefilter.di.VoiceFilterSingletonEntryPoint
import com.interfun.buz.biz.center.voicefilter.model.VoiceFilterData
import com.interfun.buz.biz.center.voicefilter.model.toVoiceFilterData
import dagger.hilt.android.EntryPointAccessors

/**
 * <AUTHOR>
 * @date 2025/5/20
 * @desc
 */
@Deprecated("use VoiceFilterDataRepository instead,don't use it in new code.")
class VoiceFilterDataCompatImpl : VoiceFilterDataCompat {

    @Deprecated("Use hilt to inject a VoiceFilterDataRepository in UserComponent instead.")
    private val dataRepository
        get() = EntryPointAccessors.fromApplication<VoiceFilterSingletonEntryPoint>(
            VoiceFilterBizCenterMediator.appContext
        ).getDataRepository()

    override suspend fun getCachedVoiceFilterDataById(filterId: Long): VoiceFilterData? {
        return dataRepository.getCachedVoiceFilterInfoById(filterId)?.toVoiceFilterData()
    }

    override fun getCachedVoiceFilterById(filterId: Long): VoiceFilterData {
        // 找不到时兜底使用“VoiceFilter”作为名字
        var result = VoiceFilterData.default(filterId)
        dataRepository.getVoiceFilterCache()?.let { resp ->
            result = resp.voiceFilterInfos?.firstOrNull {
                it.filterId == filterId
            }?.toVoiceFilterData() ?: result
        }
        return result
    }

    override fun getCachedVoiceFilterNameById(filterId: Long): String {
        return getCachedVoiceFilterById(filterId).filterName
    }
}