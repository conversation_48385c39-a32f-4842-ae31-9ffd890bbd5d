package com.interfun.buz.biz.center.voicefilter.tracker

import com.interfun.buz.common.constants.TrackConstant
import com.interfun.buz.common.utils.BuzTracker

object VoiceFilterTracker {

    const val TYPE_CLEAR = "clear"
    const val TYPE_PREVIEW_FILTER = "preview_filter"
    const val TYPE_QUIT = "quit"
    const val TYPE_CANCEL = "cancel"
    const val TYPE_PREVIEW = "preview"

    fun clickVoiceFilterEntrance(
        isGroup: <PERSON>ole<PERSON>,
        targetId: Long,
        source: String
    ) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024112101")
            put(TrackConstant.KEY_TITLE, "homepage")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "voice_filter_entrance")
            put(TrackConstant.KEY_PAGE_TYPE, "home")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, if (isGroup) "group" else "private")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, targetId.toString())
            put(TrackConstant.KEY_SOURCE, source)
        }
    }

    fun onViewVoiceFilter(isGroup: Boolean, pageType: String) {
        BuzTracker.onPageViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AVS2024112101")
            put(TrackConstant.KEY_TITLE, "voice_filter")
            put(TrackConstant.KEY_PAGE_TYPE, pageType)
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, if (isGroup) "group" else "private")
        }
    }

    fun onClickVoiceFilterRecord(
        isGroup: Boolean,
        targetId: Long,
        pageType: String,
        filterId: Long,
        ) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024112103")
            put(TrackConstant.KEY_TITLE, "voice_filter")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "voice_filter_record")
            put(TrackConstant.KEY_CONTENT_NAME, "$filterId")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, if (isGroup) "group" else "private")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, targetId.toString())
            put(TrackConstant.KEY_PAGE_STATUS, pageType)
        }
    }

    fun onClickVoiceFilterBtn(
        isGroup: Boolean,
        targetId: Long,
        type: String,
        filterId: Long,
        pageType: String,
        quitTime: Long? = null
    ) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024112104")
            put(TrackConstant.KEY_TITLE, "voice_filter")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "voice_filter_button_selection")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT, type)
            put(TrackConstant.KEY_CONTENT_NAME, "$filterId")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, if (isGroup) "group" else "private")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, targetId.toString())
            put(TrackConstant.KEY_PAGE_STATUS, pageType)
            quitTime?.let {
                put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, "$it")
            }
        }
    }

    fun onClickRecordCancelOrPreview(
        isGroup: Boolean,
        targetId: Long,
        pageType: String,
        type: String,
        isVoiceFilterMode: Boolean,
        filterId: Long,
    ) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024112108")
            put(TrackConstant.KEY_TITLE, "homepage")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "record_voice_cancel_preview")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, if (isGroup) "group" else "private")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, targetId.toString())
            put(TrackConstant.KEY_PAGE_STATUS, pageType)
            put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT, type)
            put(TrackConstant.KEY_CONTENT_NAME, "$filterId")
            put(TrackConstant.KEY_SOURCE, if (isVoiceFilterMode) "voice_filter_mode" else "home")
        }
    }

    fun onResultBackOpenVoiceFilter(
        isGroup: Boolean,
        targetId: Long,
        source: String,
        isSuccess: Boolean,
        rCode: Int? = null
    ) {
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2024112101")
            put(TrackConstant.KEY_PAGE_TYPE, "home")
            put(TrackConstant.KEY_RESULT_TYPE, "voice_filter_entrance_result")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, if (isGroup) "group" else "private")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, targetId.toString())
            put(TrackConstant.KEY_SOURCE, source)
            put(TrackConstant.KEY_IS_SUCCESS, if (isSuccess) "success" else "fail")
            rCode?.let {
                put(TrackConstant.KEY_FAIL_REASON, "$it")
            }
        }
    }

    fun onVoiceFilterPreviewDialogButtonClick(
        isPrivate: Boolean,
        targetId: String,
        pageType: String,
        filterId: Long,
        buttonString: String,
        campaignId: String? = null
    ) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024112105")
            put(TrackConstant.KEY_TITLE, "voice_filter_preview")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "voice_filter_preview_selection")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, if (isPrivate) "private" else "group")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, targetId)
            put(TrackConstant.KEY_PAGE_STATUS, pageType)
            put(TrackConstant.KEY_SOURCE, if (filterId == 0L) "non_filter" else "voice_filter")
            put(TrackConstant.KEY_CONTENT_NAME, filterId.toString())
            put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT, buttonString)
            if (!campaignId.isNullOrEmpty()) {
                put(TrackConstant.KEY_ELEMENT_BUSINESS_ID, campaignId)
            }
        }
    }

    fun onVoiceFilterTabClick(
        isGroup: Boolean,
        convId: String,
        source: String,
        tabType: String
    ) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2025061901")
            put(TrackConstant.KEY_TITLE, "voice_filter")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "voice_filter_tab")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, if (isGroup) "group" else "private")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, convId)
            put(TrackConstant.KEY_SOURCE, source)
            put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT, tabType)
        }
    }

    fun onVoiceFilterCampaignEntryClick(
        isGroup: Boolean,
        convId: String,
        source: String,
        voiceFilterId: String,
        campaignId: String?
    ) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2025061904")
            put(TrackConstant.KEY_TITLE, "voice_filter")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "voice_filter_event_entrance")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, if (isGroup) "group" else "private")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, convId)
            put(TrackConstant.KEY_SOURCE, source)
            put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT, voiceFilterId)
            if (campaignId != null) {
                put(TrackConstant.KEY_ELEMENT_BUSINESS_ID, campaignId)
            }
        }
    }
}