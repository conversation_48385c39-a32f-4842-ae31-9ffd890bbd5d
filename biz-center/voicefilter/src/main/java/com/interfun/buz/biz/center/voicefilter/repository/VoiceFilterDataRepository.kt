package com.interfun.buz.biz.center.voicefilter.repository

import com.buz.idl.bot.bean.VoiceFilterInfo
import com.buz.idl.bot.response.ResponseGetVoiceFilterList
import com.interfun.buz.biz.center.voicefilter.model.VoiceFilterOriginData
import com.interfun.buz.common.bean.LoadingState
import kotlinx.collections.immutable.PersistentList
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.StateFlow

interface VoiceFilterDataRepository {

    val voiceFilterListLoadStatusFlow: Flow<LoadingState>

    fun initRepository()

    fun getVoiceFilterOriginData(): Flow<VoiceFilterOriginData>

    suspend fun getCachedVoiceFilterInfoById(voiceFilterId: Long): VoiceFilterInfo?

    suspend fun syncVoiceFilterList()

    suspend fun clearVoiceFilterData()

    /**
     * 更新+号入口红点状态
     */
    suspend fun updateVFLatestEntryForMoreButtonTimestamp()

    fun getVFLatestEntryNotifyTimestamp(): StateFlow<Int>

    fun getVFLatestTabTimestamp(): StateFlow<Int>

    fun updateVFLatestTimestamp(filterId: Long, timestamp: Int)

    fun getVFLatestTimestamp(): Flow<PersistentList<Pair<Long, Int>>>

    @Deprecated("仅提供给旧代码使用")
    fun getVoiceFilterCache(): ResponseGetVoiceFilterList?

    /**
     * 更新语音滤镜入口红点状态
     */
    suspend fun updateVFEntryRedDotStatus()
}