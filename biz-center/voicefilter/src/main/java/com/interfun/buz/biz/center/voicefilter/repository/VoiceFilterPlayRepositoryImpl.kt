package com.interfun.buz.biz.center.voicefilter.repository

import android.media.MediaPlayer
import androidx.annotation.OptIn
import androidx.media3.common.util.UnstableApi
import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.base.ktx.toast
import com.interfun.buz.base.ktx.withIOContext
import com.interfun.buz.biz.center.voicefilter.R
import com.interfun.buz.biz.center.voicefilter.model.PLAYING
import com.interfun.buz.biz.center.voicefilter.model.STOP
import com.interfun.buz.biz.center.voicefilter.model.SoundPreviewData
import com.interfun.buz.common.soundaffect.SoundAffectCache
import com.interfun.buz.component.hilt.UserQualifier
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.job
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 语音滤镜播放仓库实现类
 */
@OptIn(UnstableApi::class)
class VoiceFilterPlayRepositoryImpl @Inject constructor(
    @UserQualifier val userScope: CoroutineScope
) : VoiceFilterPlayRepository {
    
    companion object {
        const val TAG = "VoiceFilterPlayRepository"
    }
    
    private val _isPlayingStateFlow = MutableStateFlow(false)
    override val isVoiceFilterPreviewPlayingFlow: StateFlow<Boolean> = _isPlayingStateFlow.asStateFlow()
    
    private val _filterSoundPlayFlow = MutableSharedFlow<String>()
    private val _filterSoundStopFlow = MutableSharedFlow<Unit>()
    
    // 记录最新一次播放滤镜的demo声音是源于点击还是滑动
    private var playPreviewFrom: Int? = null
    
    // 当前播放的音频的url
    private var currentPlaySoundUrl: String? = null
    
    private val mediaPlayer: MediaPlayer by lazy { MediaPlayer() }
    
    override fun initRepository() {
        userScope.launch {
            mediaPlayer.setOnCompletionListener {
                logDebug(TAG, "Player OnCompletion")
                _isPlayingStateFlow.value = false
            }
            coroutineContext.job.invokeOnCompletion {
                mediaPlayer.release()
            }
            
            getSoundStateFlow().collect { soundPreviewData ->
                withIOContext {
                    if (soundPreviewData.state == PLAYING) {
                        soundPreviewData.soundPath?.let { path ->
                            playSound(mediaPlayer, path)
                            _isPlayingStateFlow.value = true
                        }
                    } else if (soundPreviewData.state == STOP) {
                        mediaPlayer.stop()
                        _isPlayingStateFlow.value = false
                    }
                }
            }
        }
    }
    
    override suspend fun playFilterSound(soundUrl: String, from: Int) {
        playPreviewFrom = from
        currentPlaySoundUrl = soundUrl
        _filterSoundPlayFlow.emit(soundUrl)
    }
    
    override suspend fun stopFilterSound() {
        _filterSoundStopFlow.emit(Unit)
    }
    
    private fun getSoundStateFlow() = channelFlow {
        val sendChannel = this
        launch {
            _filterSoundPlayFlow.collect { filterSoundUrl ->
                launch {
                    val filePath = SoundAffectCache.cache(filterSoundUrl)
                    if (filePath.isNullOrEmpty() && playPreviewFrom == VoiceFilterPlayRepository.FROM_CLICK) {
                        R.string.wt_network_error.toast()
                    }
                    if (filterSoundUrl == currentPlaySoundUrl) {
                        sendChannel.send(SoundPreviewData(filePath, PLAYING))
                    }
                }
            }
        }
        launch {
            _filterSoundStopFlow.collect {
                sendChannel.send(SoundPreviewData(null, STOP))
            }
        }
    }.flowOn(Dispatchers.Default)
    
    private fun playSound(player: MediaPlayer, path: String) {
        logDebug(TAG, "path: $path")
        try {
            player.stop()
            player.reset()
            player.setDataSource(path)
            player.prepare()
            player.start()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}
