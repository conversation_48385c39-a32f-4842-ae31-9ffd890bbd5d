package com.interfun.buz.biz.center.voicefilter.model

import com.buz.idl.bot.bean.VoiceFilterInfo
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.biz.center.voicefilter.R
import com.interfun.buz.common.ktx.getIntDefault
import com.interfun.buz.common.manager.AppConfigRequestManager

/**
 * <AUTHOR>
 * @date 2025/5/8
 * @desc
 */
data class VoiceFilterData(
    val filterId: Long,
    val filterName: String,
    val playingAnimationType: Int?,
    val playingAnimationContent: String?,
    val eventId: Long?,
    val bubbleType: VoiceFilterType,
    val backgroundColor: String?,
    val microphoneColor: String?,
    val videoTemplateUrl: String?,
    val videoTemplateMd5: String?
) {
    companion object {
        fun default(voiceFilterId: Long): VoiceFilterData {
            return VoiceFilterData(
                filterId = voiceFilterId,
                filterName = R.string.voice_filter.asString(),
                playingAnimationType = null,
                playingAnimationContent = null,
                eventId = null,
                bubbleType = VoiceFilterType.DEFAULT,
                backgroundColor = null,
                microphoneColor = null,
                videoTemplateUrl = AppConfigRequestManager.voiceFilterVideoTemplateUrl,
                videoTemplateMd5 = AppConfigRequestManager.voiceFilterVideoTemplateMd5
            )
        }
    }
}

fun VoiceFilterInfo.toVoiceFilterData(): VoiceFilterData {
    return VoiceFilterData(
        filterId = this.filterId ?: 0L,
        filterName = this.filterName ?: R.string.voice_filter.asString(),
        playingAnimationType = this.playingAnimationType,
        playingAnimationContent = this.playingAnimationContent,
        eventId = this.campaignConfig?.id,
        bubbleType = VoiceFilterType.getVoiceFilterType(this.itemConfig?.bubbleStyle.getIntDefault()),
        backgroundColor = this.backgroundColor,
        microphoneColor = this.microphoneColor,
        videoTemplateUrl = this.videoTemplate?.videoTemplateUrl ?: AppConfigRequestManager.voiceFilterVideoTemplateUrl,
        videoTemplateMd5 = this.videoTemplate?.videoTemplateMd5 ?: AppConfigRequestManager.voiceFilterVideoTemplateMd5
    )
}

sealed class VoiceFilterType(val value: Int) {
    data object DEFAULT : VoiceFilterType(0)
    data object FEATURE : VoiceFilterType(1)

    companion object {
        fun getVoiceFilterType(value: Int): VoiceFilterType {
            return when (value) {
                1 -> FEATURE
                else -> DEFAULT
            }
        }

        fun isFeature(type: VoiceFilterType): Boolean {
            return type == FEATURE
        }
    }
}

