package com.interfun.buz.biz.center.voicefilter.repository

import kotlinx.coroutines.flow.StateFlow

/**
 * 滤镜状态仓库接口
 * 用于在不同 Activity 之间共享滤镜的状态
 */
interface VoiceFilterStatusRepository {

    /**
     * 标记滤镜入口是否被点击过, 以及点击的来源
     */
    val hasClickedSwitchVoiceFilterModeFlow: StateFlow<Pair<Boolean, String?>>

    /**
     * 滤镜模式是否开启
     */
    val isVoiceFilterModeStateFlow: StateFlow<Boolean>

    /**
     * 当前选中的滤镜tab type
     */
    val currentSelectTabTypeFlow: StateFlow<Int?>

    /**
     * 是否用户已滚动
     */
    val isVoiceFilterUserScrolledStateFlow: StateFlow<Boolean>

    /**
     * 按tab类型记录选中的VF ID的Map
     */
    val tabSelectedVoiceFilterIdMapFlow: StateFlow<Map<Int, Long>>

    /**
     * 当前是否正在滚动（包括用户滚动和动画滚动）
     */
    val isVoiceFilterScrollingStateFlow: StateFlow<Boolean>


    /**
     * 更新滤镜入口点击状态
     */
    suspend fun updateHasClickedSwitchVoiceFilterMode(hasClicked: Boolean, from: String?)
    
    /**
     * 更新滤镜模式状态
     */
    suspend fun updateVoiceFilterMode(isOpen: Boolean)

    /**
     * 更新用户滚动状态
     */
    suspend fun updateUserIsScrolled(isScrolled: Boolean)

    /**
     * 更新滤镜滚动状态
     */
    suspend fun updateVoiceFilterScrolling(isScrolling: Boolean)

    /**
     * 更新当前选中的tab
     */
    suspend fun updateSelectTabType(tabType: Int)

    /**
     * 更新指定tab的选中VF ID
     */
    suspend fun updateTabSelectedVoiceFilterId(tabType: Int, voiceFilterId: Long)

    /**
     * 获取指定tab的选中VF ID
     */
    fun getTabSelectedVoiceFilterId(tabType: Int): Long?

    /**
     * 清除所有tab的选中状态
     */
    suspend fun clearAllTabSelectedVoiceFilterIds()
}
