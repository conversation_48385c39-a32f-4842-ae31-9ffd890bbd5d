package com.interfun.buz.biz.center.voicefilter.repository

import kotlinx.coroutines.flow.StateFlow

/**
 * 语音滤镜播放仓库接口
 * 负责处理语音滤镜的播放逻辑
 */
interface VoiceFilterPlayRepository {

    companion object {
        const val FROM_CLICK = 1
        const val FROM_SCROLL = 2
    }


    /**
     * 是否正在播放语音滤镜
     */
    val isVoiceFilterPreviewPlayingFlow: StateFlow<Boolean>
    
    /**
     * 初始化播放器
     */
    fun initRepository()
    
    /**
     * 播放语音滤镜
     * @param soundUrl 语音滤镜的音频 URL
     * @param from 播放来源，1: 点击，2: 滚动
     */
    suspend fun playFilterSound(soundUrl: String, from: Int)
    
    /**
     * 停止播放语音滤镜
     */
    suspend fun stopFilterSound()

}
