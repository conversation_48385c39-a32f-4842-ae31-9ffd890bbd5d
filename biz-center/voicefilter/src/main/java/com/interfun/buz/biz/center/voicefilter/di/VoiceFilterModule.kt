package com.interfun.buz.biz.center.voicefilter.di

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.PreferenceDataStoreFactory
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.preferencesDataStoreFile
import com.interfun.buz.biz.center.voicefilter.datasource.IRemoteVoiceFilterDataSource
import com.interfun.buz.biz.center.voicefilter.datasource.LocalVoiceFilterDataSource
import com.interfun.buz.biz.center.voicefilter.datasource.RemoteVoiceFilterDataSource
import com.interfun.buz.biz.center.voicefilter.repository.*
import com.interfun.buz.component.hilt.UserAuth
import com.interfun.buz.component.hilt.UserComponent
import com.interfun.buz.component.hilt.UserQualifier
import com.interfun.buz.component.hilt.UserScope
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.EntryPoint
import dagger.hilt.EntryPoints
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent

@EntryPoint
@InstallIn(UserComponent::class)
internal interface VoiceFilterEntryPoint {

    @UserQualifier
    fun getVoiceFilterBizCenterUserEntry(): VoiceFilterBizCenterUserEntry

    @UserQualifier
    fun getDataRepository(): VoiceFilterDataRepository

    @UserQualifier
    fun getPlayRepository(): VoiceFilterPlayRepository

    @UserQualifier
    fun getStatusRepository(): VoiceFilterStatusRepository
}

@EntryPoint
@InstallIn(SingletonComponent::class)
internal interface VoiceFilterSingletonEntryPoint {
    fun getDataRepository(): VoiceFilterDataRepository
    fun getPlayRepository(): VoiceFilterPlayRepository
    fun getStatusRepository(): VoiceFilterStatusRepository
}

@Module
@InstallIn(UserComponent::class)
internal abstract class VoiceFilterUserModule {

    companion object{
        @Provides
        @UserQualifier
        @UserScope
        fun provideVoiceFilterDatastore(
            @ApplicationContext appContext: Context,
            @UserQualifier userAuth: UserAuth
        ): DataStore<Preferences> {
            return PreferenceDataStoreFactory.create(scope = userAuth.userScope) {
                appContext.preferencesDataStoreFile("voiceFilter_${userAuth.userId}")
            }
        }
    }

    @Binds
    @UserQualifier
    abstract fun provideVoiceFilterEntry(entry: VoiceFilterBizCenterUserEntryImpl): VoiceFilterBizCenterUserEntry

    @Binds
    @UserQualifier
    @UserScope
    abstract fun provideRemoteVoiceFilterDS(impl: RemoteVoiceFilterDataSource): IRemoteVoiceFilterDataSource

    @Binds
    @UserQualifier
    @UserScope
    abstract fun provideLocalVoiceFilterDS(impl: LocalVoiceFilterDataSource): LocalVoiceFilterDataSource

    @Binds
    @UserQualifier
    @UserScope
    abstract fun provideDataRepository(impl: VoiceFilterDataRepositoryImpl): VoiceFilterDataRepository

    @Binds
    @UserQualifier
    @UserScope
    abstract fun providePlayRepository(impl: VoiceFilterPlayRepositoryImpl): VoiceFilterPlayRepository

    @Binds
    @UserQualifier
    @UserScope
    abstract fun provideStatusRepository(impl: VoiceFilterStatusRepositoryImpl): VoiceFilterStatusRepository

}

@Module
@InstallIn(SingletonComponent::class)
internal object VoiceFilterModule {

    @Provides
    fun provideVoiceFilterBizCenterUserEntry(userComponent: UserComponent): VoiceFilterBizCenterUserEntry {
        return EntryPoints.get(userComponent, VoiceFilterEntryPoint::class.java).getVoiceFilterBizCenterUserEntry()
    }

    @Provides
    fun provideDataRepository(userComponent: UserComponent): VoiceFilterDataRepository {
        return EntryPoints.get(userComponent, VoiceFilterEntryPoint::class.java).getDataRepository()
    }

    @Provides
    fun providePlayRepository(userComponent: UserComponent): VoiceFilterPlayRepository {
        return EntryPoints.get(userComponent, VoiceFilterEntryPoint::class.java).getPlayRepository()
    }

    @Provides
    fun provideStatusRepository(userComponent: UserComponent): VoiceFilterStatusRepository {
        return EntryPoints.get(userComponent, VoiceFilterEntryPoint::class.java).getStatusRepository()
    }
}
