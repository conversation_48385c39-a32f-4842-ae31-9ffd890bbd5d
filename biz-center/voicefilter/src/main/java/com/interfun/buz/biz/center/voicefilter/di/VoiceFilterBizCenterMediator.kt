package com.interfun.buz.biz.center.voicefilter.di

import android.content.Context
import com.interfun.buz.biz.center.voicefilter.compat.VoiceFilterDataCompatImpl
import com.interfun.buz.biz.center.voicefilter.compat.VoiceFilterHelper
import com.interfun.buz.component.hilt.GlobalQualifier
import com.interfun.buz.component.hilt.UserComponentManager
import dagger.hilt.EntryPoints
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * <AUTHOR>
 * @date 2025/5/16
 * @desc
 */
class VoiceFilterBizCenterMediator @Inject constructor(
    private val userComponentManager: UserComponentManager,
    @GlobalQualifier private val globalScope: CoroutineScope
) {

    companion object {
        @Volatile
        internal lateinit var appContext : Context
    }

    fun initRepository(appContext : Context) {
        Companion.appContext = appContext
        globalScope.launch {
            userComponentManager.getUserComponentFlow().collect { userComponent ->
                EntryPoints.get(userComponent, VoiceFilterEntryPoint::class.java)
                    .getVoiceFilterBizCenterUserEntry().initRepository()
            }
        }
        VoiceFilterHelper.delegate = VoiceFilterDataCompatImpl()
    }
}