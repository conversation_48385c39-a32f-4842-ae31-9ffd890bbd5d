package com.interfun.buz.biz.center.voicefilter.di

import com.interfun.buz.biz.center.voicefilter.repository.VoiceFilterDataRepository
import com.interfun.buz.biz.center.voicefilter.repository.VoiceFilterPlayRepository
import com.interfun.buz.biz.center.voicefilter.repository.VoiceFilterStatusRepository
import com.interfun.buz.component.hilt.UserQualifier
import javax.inject.Inject

/**
 * <AUTHOR>
 * @date 2025/5/16
 * @desc
 */
internal class VoiceFilterBizCenterUserEntryImpl @Inject constructor(
    @UserQualifier val dataRepository: VoiceFilterDataRepository,
    @UserQualifier val playRepository: VoiceFilterPlayRepository,
    @UserQualifier val statusRepository: VoiceFilterStatusRepository
): VoiceFilterBizCenterUserEntry {
    override fun initRepository() {
        dataRepository.initRepository()
        playRepository.initRepository()
    }
}