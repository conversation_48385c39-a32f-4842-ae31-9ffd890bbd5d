package com.interfun.buz.user.repository

import android.app.AlarmManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import com.interfun.buz.component.hilt.UserQualifier
import com.interfun.buz.user.entity.TimeScheduledEvent
import com.interfun.buz.user.receiver.TimeTriggerReceiver
import com.interfun.buz.user.utils.ExactAlarmPermissionUtil
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import java.util.Calendar
import javax.inject.Inject

/**
 * Author: ChenYouSheng
 * Date: 2025/7/25
 * Email: <EMAIL>
 * Desc:
 */
class TimeTriggerRepositoryImpl @Inject constructor(
    @UserQualifier private val userScope: CoroutineScope,
    @ApplicationContext private val appContext: Context,
) : TimeTriggerRepository {


    private val _timeTriggerShareFlow = MutableSharedFlow<TimeScheduledEvent>()

    override val timeTriggerShareFlow: SharedFlow<TimeScheduledEvent>
        get() = _timeTriggerShareFlow.asSharedFlow()


    override fun scheduleAlarms(
        hour: Int,
        minute: Int,
        alarmType: AlarmType,
        weekDay: Boolean
    ): TimeTriggerType {
        val hasPermission = ExactAlarmPermissionUtil.hasExactAlarmPermission(appContext)

        val (action, requestCode) = when (alarmType) {
            AlarmType.START -> TimeTriggerRepository.ACTION_ENTER_SCHEDULE_TIME to TimeTriggerRepository.START_CODE
            AlarmType.END -> TimeTriggerRepository.ACTION_EXIT_SCHEDULE_TIME to TimeTriggerRepository.END_CODE
        }
        val triggerTime = calculateTriggerTimeMillis(hour, minute)

        val alarmManager = appContext.getSystemService(Context.ALARM_SERVICE) as AlarmManager

        val pending = createPendingIntent(
            action = action,
            hour = hour,
            minute = minute,
            weekDay = weekDay,
            alarmType = alarmType.type,
            requestCode = requestCode,
            includeUpdateFlag = true
        )

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (hasPermission) {
                alarmManager.setExactAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, triggerTime, pending)
            } else {
                alarmManager.setAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, triggerTime, pending)
            }
        } else {
            if (hasPermission) {
                alarmManager.setExact(AlarmManager.RTC_WAKEUP, triggerTime, pending)
            } else {
                alarmManager.set(AlarmManager.RTC_WAKEUP, triggerTime, pending)
            }
        }

        return if (hasPermission) TimeTriggerType.Exact else TimeTriggerType.NonExact
    }

    override fun cancelScheduleAlarms() {
        val alarmManager = appContext.getSystemService(Context.ALARM_SERVICE) as AlarmManager

        val startPending = createPendingIntent(
            action = TimeTriggerRepository.ACTION_ENTER_SCHEDULE_TIME,
            requestCode = TimeTriggerRepository.START_CODE,
            alarmType = AlarmType.START.type,
            includeUpdateFlag = false
        )

        val endPending = createPendingIntent(
            action = TimeTriggerRepository.ACTION_EXIT_SCHEDULE_TIME,
            requestCode = TimeTriggerRepository.END_CODE,
            alarmType = AlarmType.END.type,
            includeUpdateFlag = false
        )

        alarmManager.cancel(startPending)
        alarmManager.cancel(endPending)
    }

    override fun calculateTriggerTimeMillis(hour: Int, minute: Int): Long {
        val now = Calendar.getInstance()
        val trigger = Calendar.getInstance().apply {
            set(Calendar.HOUR_OF_DAY, hour)
            set(Calendar.MINUTE, minute)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
            if (timeInMillis <= now.timeInMillis) {
                // 如果已经过去了，就设为明天
                add(Calendar.DAY_OF_YEAR, 1)
            }
        }
        return trigger.timeInMillis
    }

    private fun createPendingIntent(
        action: String,
        hour: Int = 0,
        minute: Int = 0,
        weekDay: Boolean = false,
        alarmType: Int,
        requestCode: Int,
        includeUpdateFlag: Boolean
    ): PendingIntent {
        val intent = Intent(appContext, TimeTriggerReceiver::class.java).apply {
            this.action = action
            putExtra(TimeTriggerReceiver.HOUR, hour)
            putExtra(TimeTriggerReceiver.MINUTE, minute)
            putExtra(TimeTriggerReceiver.ALARM_TYPE, alarmType)
            putExtra(TimeTriggerReceiver.WEEK_DAY, weekDay)
        }
        val flags = PendingIntent.FLAG_IMMUTABLE or
                if (includeUpdateFlag) PendingIntent.FLAG_UPDATE_CURRENT else 0

        return PendingIntent.getBroadcast(appContext, requestCode, intent, flags)
    }

    override fun notifyTimeTriggerEvent(event: TimeScheduledEvent) {
        userScope.launch {
            _timeTriggerShareFlow.emit(event)
        }
    }


}