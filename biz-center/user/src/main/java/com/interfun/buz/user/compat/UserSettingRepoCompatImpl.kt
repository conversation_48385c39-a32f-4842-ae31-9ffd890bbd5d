package com.interfun.buz.user.compat

import com.interfun.buz.common.manager.cache.user.UserSettingRepoCompat
import com.interfun.buz.user.UserBizCenterMediator
import com.interfun.buz.user.di.BizUserSingletonEntryPoint
import dagger.hilt.android.EntryPointAccessors
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.runBlocking

/**
 * Author: ChenYouSheng
 * Date: 2025/7/30
 * Email: <EMAIL>
 * Desc:
 */
@Deprecated("use UserSettingRepository instead,don't use it in new code.")
class UserSettingRepoCompatImpl : UserSettingRepoCompat {

    companion object {
        @Deprecated("Use hilt to inject a UserSettingRepository in UserComponent instead.")
        val userSettingRepository
            get() = EntryPointAccessors.fromApplication<BizUserSingletonEntryPoint>(
                UserBizCenterMediator.appContext
            ).userSettingRepository()
    }

    override val isQuietModeEnable: Boolean
        get() = runBlocking {
            userSettingRepository.getQuiteMode().map { it.enable }.firstOrNull() ?: false
        }

}