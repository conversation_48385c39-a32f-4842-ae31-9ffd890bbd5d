package com.interfun.buz.user.entity

import kotlinx.serialization.*

/**
 * Author: Chen<PERSON>ouSheng
 * Date: 2025/7/24
 * Email: chen<PERSON><PERSON><PERSON>@vocalbeats.com
 * Desc: 用户首页左上角4种状态模式
 */
@Serializable
sealed interface UserStateMode {
    // 静音模式（手动关闭，DND同步关闭，定时关闭）
    @Serializable
    sealed interface QuiteMode : UserStateMode

    // 自动播放模式
    @Serializable
    sealed interface AutoPlay : UserStateMode
}

@Serializable
@SerialName("Enable")
object Enable : UserStateMode.AutoPlay

@Serializable
@SerialName("Disable")
object Disable : UserStateMode.QuiteMode

@Serializable
@SerialName("SyncDND")
object SyncDND : UserStateMode.QuiteMode

@Serializable
@SerialName("QuiteTime")
data class QuiteTime(val untilHour: Int, val untilMinute: Int) : UserStateMode.QuiteMode
