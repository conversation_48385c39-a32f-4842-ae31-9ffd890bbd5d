package com.interfun.buz.user.di

import com.interfun.buz.component.hilt.UserComponent
import com.interfun.buz.component.hilt.UserQualifier
import com.interfun.buz.user.repository.TimeTriggerRepository
import com.interfun.buz.user.repository.UserSettingRepository
import dagger.hilt.EntryPoint
import dagger.hilt.InstallIn

/**
 * Author: ChenYouSheng
 * Date: 2025/7/24
 * Email: <EMAIL>
 * Desc:
 */

@EntryPoint
@InstallIn(UserComponent::class)
internal interface BizUserEntryPoint {

    @UserQualifier
    fun userSettingRepository(): UserSettingRepository

    @UserQualifier
    fun timeTriggerRepository(): TimeTriggerRepository

}