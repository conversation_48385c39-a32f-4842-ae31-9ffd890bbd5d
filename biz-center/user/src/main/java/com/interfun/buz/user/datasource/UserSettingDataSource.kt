package com.interfun.buz.user.datasource

import com.interfun.buz.user.entity.QuiteMode
import com.interfun.buz.user.entity.TimingCycle
import com.interfun.buz.user.entity.TimingQuiteMode
import kotlinx.coroutines.flow.Flow

/**
 * Author: Chen<PERSON>ouSheng
 * Date: 2025/7/24
 * Email: ch<PERSON><PERSON><PERSON><PERSON>@vocalbeats.com
 * Desc:
 */
interface UserSettingDataSource {

    /**开启定时静音模式*/
    suspend fun enableTimingQuiteMode(
        enable: Boolean,
        startHour: Int,
        startMinute: Int,
        endHour: Int,
        endMinute: Int,
        timingCycle: TimingCycle
    )

    /**开启或者关闭静音模式*/
    suspend fun enableQuiteMode(quiteMode: QuiteMode)

    /**开启或者关闭同步DND模式*/
    suspend fun enableSyncDND(bind: Boolean)

    /**获取定时静音模式*/
    fun getTimingQuiteMode(): Flow<TimingQuiteMode?>

    /**获取是否开启静音模式*/
    fun getQuiteMode(): Flow<QuiteMode>

    /**获取是否开启同步DND模式*/
    fun getEnableSyncDND(): Flow<Boolean>

    /**一次性获取定时静音模式*/
    suspend fun getTimingQuietModeOnce(): TimingQuiteMode?

}