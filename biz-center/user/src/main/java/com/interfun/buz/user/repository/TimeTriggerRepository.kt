package com.interfun.buz.user.repository

import com.interfun.buz.user.entity.TimeScheduledEvent
import kotlinx.coroutines.flow.SharedFlow

/**
 * Author: ChenYouSheng
 * Date: 2025/7/25
 * Email: chenyoush<PERSON>@vocalbeats.com
 * Desc:
 */
interface TimeTriggerRepository {

    companion object {
        const val TAG = "TimeTriggerRepository"
        const val ACTION_ENTER_SCHEDULE_TIME = "ACTION_ENTER_SCHEDULE_TIME"
        const val ACTION_EXIT_SCHEDULE_TIME = "ACTION_EXIT_SCHEDULE_TIME"
        const val START_CODE = 1001
        const val END_CODE = 1002
    }

    val timeTriggerShareFlow: SharedFlow<TimeScheduledEvent>

    fun scheduleAlarms(
        hour: Int,
        minute: Int,
        alarmType: AlarmType = AlarmType.START,
        weekDay: Boolean = false,
    ): TimeTriggerType


    fun cancelScheduleAlarms()

    fun notifyTimeTriggerEvent(event: TimeScheduledEvent)

    fun calculateTriggerTimeMillis(hour: Int, minute: Int): Long
}

sealed interface TimeTriggerType {
    object Exact : TimeTriggerType
    object NonExact : TimeTriggerType
}

sealed class AlarmType(val type: Int) {
    object START : AlarmType(1)
    object END : AlarmType(2)

    companion object {
        fun Int.toAlarmType(): AlarmType {
            return when (this) {
                1 -> START
                else -> END
            }
        }
    }
}