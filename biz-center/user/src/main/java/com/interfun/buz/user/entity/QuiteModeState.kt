package com.interfun.buz.user.entity

import kotlinx.serialization.*

/**
 * Author: <PERSON><PERSON>ouSheng
 * Date: 2025/7/24
 * Email: chen<PERSON><PERSON><PERSON>@vocalbeats.com
 * Desc: 用户首页左上角4种状态模式
 */
sealed interface QuiteModeState {
    // 静音模式（手动关闭，DND同步关闭，定时关闭）
    sealed interface QuiteMode : QuiteModeState

    // 自动播放模式
    data object AutoPlay : QuiteModeState
}

data object Manual : QuiteModeState.QuiteMode

data object SyncDND : QuiteModeState.QuiteMode

data class QuiteTime(val untilHour: Int, val untilMinute: Int) : QuiteModeState.QuiteMode
