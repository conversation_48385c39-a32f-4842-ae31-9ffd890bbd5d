package com.interfun.buz.user.repository

import com.interfun.buz.base.exception.UserLogoutException
import com.interfun.buz.common.manager.*
import com.interfun.buz.component.hilt.UserAuth
import com.interfun.buz.component.hilt.UserAuth.Authenticated
import com.interfun.buz.component.hilt.UserAuth.Unauthenticated
import com.interfun.buz.component.hilt.GlobalQualifier
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * common 模块有一个[com.interfun.buz.common.manager.UserSessionManager],这个应该要迁移到biz-center/auth中，
 * 但是目前本期重构的内容主要是social模块，这部分暂时先不迁移，后续再迁移，但是对于外部的一些依赖注入，这期都先处理，让新业务可以使用新方式，
 * 等social模块重构完成之后，再把UserSessionManager迁移到biz-center/auth中，放到UserAuthManagerImpl中。
 */
@Singleton
class UserAuthRepositoryImpl @Inject constructor(
    @GlobalQualifier val globalScope: CoroutineScope
) : UserAuthRepository {

    override val userAuthFlow =
        MutableStateFlow(loginStateToUserAuth(UserSessionManager.userLoginStateFlow.value))

    init {
        globalScope.launch {
            UserSessionManager
                .userLoginStateFlow
                .collect { loginState ->
                    val previousUserAuth = userAuthFlow.value
                    val newAuth = loginStateToUserAuth(loginState)
                    if (previousUserAuth.userId != newAuth.userId) {
                        previousUserAuth.userScope.cancel(
                            UserLogoutException(
                                errorMsg = "user logout",
                                userId = previousUserAuth.userId
                            )
                        )
                        userAuthFlow.emit(
                            loginStateToUserAuth(loginState)
                        )
                    }
                }
        }
    }

    private fun loginStateToUserAuth(loginState: LoginState): UserAuth {
        return when (loginState) {
            is OnLogin -> {
                Authenticated(
                    loginState.userId,
                    CoroutineScope(SupervisorJob() + Dispatchers.Default)
                )
            }

            else -> Unauthenticated(
                -1L,
                CoroutineScope(SupervisorJob() + Dispatchers.Default)
            )
        }
    }
}