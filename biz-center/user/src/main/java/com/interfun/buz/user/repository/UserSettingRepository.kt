package com.interfun.buz.user.repository

import com.interfun.buz.user.entity.QuiteMode
import com.interfun.buz.user.entity.QuiteModeFromType
import com.interfun.buz.user.entity.TimingCycle
import com.interfun.buz.user.entity.TimingQuietMode
import kotlinx.coroutines.flow.Flow

/**
 * Author: ChenYouSheng
 * Date: 2025/7/24
 * Email: ch<PERSON><PERSON><PERSON><PERSON>@vocalbeats.com
 * Desc:
 */
interface UserSettingRepository {
    val inSystemDndFlow: Flow<Boolean>
    fun enableQuiteMode(enable: Boolean, quiteModeFromType: QuiteModeFromType)
    fun enableTimingQuiteMode(
        enable: Boolean? = null,
        startHour: Int? = null,
        startMinute: Int? = null,
        endHour: Int? = null,
        endMinute: Int? = null,
        timingCycle: TimingCycle? = null
    )
    fun enableSyncDND(bind: Boolean)
    fun getQuiteMode(): Flow<QuiteMode>
    fun getTimingQuietMode(): Flow<TimingQuietMode?>
    suspend fun getTimingQuietModeOnce(): TimingQuietMode?
    fun getEnableSyncDND(): Flow<Boolean>
}