package com.interfun.buz.user.repository

import com.interfun.buz.user.entity.QuiteMode
import com.interfun.buz.user.entity.QuiteModeFromType
import com.interfun.buz.user.entity.TimingCycle
import com.interfun.buz.user.entity.TimingQuiteMode
import kotlinx.coroutines.flow.Flow

/**
 * Author: ChenYouSheng
 * Date: 2025/7/24
 * Email: ch<PERSON><PERSON><PERSON><PERSON>@vocalbeats.com
 * Desc:
 */
interface UserSettingRepository {
    val inSystemDndFlow: Flow<Boolean>
    fun enableQuiteMode(enable: Boolean, quiteModeFromType: QuiteModeFromType)
    fun enableTimingQuiteMode(
        enable: Boolean,
        startHour: Int,
        startMinute: Int,
        endHour: Int,
        endMinute: Int,
        timingCycle: TimingCycle
    )
    fun enableSyncDND(bind: Boolean)
    fun getQuiteMode(): Flow<QuiteMode>
    fun getTimingQuiteMode(): Flow<TimingQuiteMode?>
    suspend fun getTimingQuietModeOnce(): TimingQuiteMode?
    fun getEnableSyncDND(): Flow<Boolean>
}