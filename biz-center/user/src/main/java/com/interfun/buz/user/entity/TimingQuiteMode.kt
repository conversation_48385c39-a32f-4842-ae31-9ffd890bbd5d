package com.interfun.buz.user.entity

import androidx.annotation.Keep
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * Author: ChenYouSheng
 * Date: 2025/7/24
 * Email: chenyoush<PERSON>@vocalbeats.com
 * Desc: 定时静音
 */
@Serializable
sealed interface TimingCycle {

    @Serializable
    @SerialName("Daily")
    object Daily : TimingCycle // 每天重复

    @Serializable
    @SerialName("WeekDays")
    object WeekDays : TimingCycle // 工作日重复
}

@Keep
@Serializable
data class TimingQuiteMode(
    val enable: Boolean,
    val startHour: Int,
    val startMinute: Int,
    val endHour: Int,
    val endMinute: Int,
    val timingCycle: TimingCycle
)
