package com.interfun.buz.user

import android.content.Context
import com.interfun.buz.common.manager.cache.user.UserSettingManager
import com.interfun.buz.user.compat.UserSettingRepoCompatImpl
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Author: <PERSON><PERSON>ouSheng
 * Date: 2025/7/30
 * Email: chenyo<PERSON><PERSON>@vocalbeats.com
 * Desc:
 */
@Singleton
class UserBizCenterMediator @Inject constructor() {

    companion object {
        @Volatile
        internal lateinit var appContext: Context
    }

    fun init(appContext: Context) {
        Companion.appContext = appContext
        UserSettingManager.delegate = UserSettingRepoCompatImpl()
    }
}