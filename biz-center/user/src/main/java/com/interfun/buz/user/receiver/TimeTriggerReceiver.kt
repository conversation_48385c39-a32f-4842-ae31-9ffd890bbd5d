package com.interfun.buz.user.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.base.ktx.logWarn
import com.interfun.buz.user.entity.TimeScheduledEvent
import com.interfun.buz.user.repository.AlarmType
import com.interfun.buz.user.repository.AlarmType.Companion.toAlarmType
import com.interfun.buz.user.repository.TimeTriggerRepository
import dagger.hilt.android.AndroidEntryPoint
import java.util.Calendar
import javax.inject.Inject

/**
 * Author: ChenYouSheng
 * Date: 2025/7/24
 * Email: <EMAIL>
 * Desc:
 */
@AndroidEntryPoint
class TimeTriggerReceiver : BroadcastReceiver() {
    companion object {
        const val TAG = "TimeTriggerReceiver"
        const val HOUR = "START_HOUR"
        const val MINUTE = "START_MINUTE"
        const val WEEK_DAY = "WEEK_DAY"
        const val ALARM_TYPE = "ALARM_TYPE"
    }

    @Inject
    lateinit var timeTriggerRepository: TimeTriggerRepository


    override fun onReceive(context: Context?, intent: Intent?) {
        val hour = intent?.getIntExtra(HOUR, 0) ?: 0
        val minute = intent?.getIntExtra(MINUTE, 0) ?: 0
        val weekDay = intent?.getBooleanExtra(WEEK_DAY, false) ?: false
        val alarmType = intent?.getIntExtra(ALARM_TYPE, AlarmType.START.type)
            ?: AlarmType.START.type
        val action = intent?.action
        logDebug(
            TAG,
            "onReceive: action=${action}, startHour=$hour, startMinute=$minute," +
                    " weekDay=$weekDay, alarmType=${alarmType}"
        )

        // 重复闹钟
        timeTriggerRepository.scheduleAlarms(
            hour = hour,
            minute = minute,
            alarmType = alarmType.toAlarmType(),
            weekDay = weekDay
        )

        val isWeekDay = isWeekDay()
        if (isWeekDay && weekDay) {
            // 仅周一～周五通知
            notifyObserver(intent?.action)
        } else {
            // 每天通知
            notifyObserver(intent?.action)
        }

    }


    private fun isWeekDay(): Boolean {
        val calendar = Calendar.getInstance()
        val day = calendar.get(Calendar.DAY_OF_WEEK)
        val isWeekDay = day in Calendar.MONDAY..Calendar.THURSDAY
        return isWeekDay
    }

    private fun notifyObserver(action: String?) {
        when (action) {
            TimeTriggerRepository.ACTION_ENTER_SCHEDULE_TIME -> {
                timeTriggerRepository.notifyTimeTriggerEvent(TimeScheduledEvent.Start)
            }

            TimeTriggerRepository.ACTION_EXIT_SCHEDULE_TIME -> {
                timeTriggerRepository.notifyTimeTriggerEvent(TimeScheduledEvent.End)
            }

            else -> {
                logWarn(TAG, "Unknown action: $action")
            }
        }
    }
}
