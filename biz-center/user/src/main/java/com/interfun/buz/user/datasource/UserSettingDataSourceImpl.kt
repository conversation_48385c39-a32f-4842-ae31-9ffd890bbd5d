package com.interfun.buz.user.datasource

import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import com.interfun.buz.user.di.BizUserQualifier
import com.interfun.buz.user.entity.QuiteMode
import com.interfun.buz.user.entity.QuiteModeFromType
import com.interfun.buz.user.entity.TimingCycle
import com.interfun.buz.user.entity.TimingQuietMode
import com.interfun.buz.user.storage.UserDataStoreKey
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.Json
import javax.inject.Inject

/**
 * Author: ChenYouSheng
 * Date: 2025/7/24
 * Email: <EMAIL>
 * Desc:
 */
class UserSettingDataSourceImpl @Inject constructor(
    @BizUserQualifier private val userDataStore: DataStore<Preferences>,
) : UserSettingDataSource {

    override suspend fun enableQuiteMode(quiteMode: QuiteMode) {
        withContext(Dispatchers.IO) {
            userDataStore.edit { preferences ->
                val json = preferences[UserDataStoreKey.quiteModeKey]
                val currentQuiteMode = json?.let {
                    try {
                        Json.decodeFromString(QuiteMode.serializer(), it)
                    } catch (e: Exception) {
                        null
                    }
                }
                // 恢复为非静音
                if (!quiteMode.enable) {
                    // 之前是手动，现在不是手动，返回
                    if (quiteMode.fromType != QuiteModeFromType.Manual && currentQuiteMode?.fromType == QuiteModeFromType.Manual) {
                        return@edit
                    }
                }
                preferences[UserDataStoreKey.quiteModeKey] =
                    Json.encodeToString(QuiteMode.serializer(), quiteMode)
            }
        }
    }

    override suspend fun enableTimingQuietMode(
        enable: Boolean?,
        startHour: Int?,
        startMinute: Int?,
        endHour: Int?,
        endMinute: Int?,
        timingCycle: TimingCycle?
    ) {
        withContext(Dispatchers.IO) {
            userDataStore.edit { preferences ->
                val json = preferences[UserDataStoreKey.quiteModeByTimingKey]
                val current = json?.let {
                    try {
                        Json.decodeFromString(TimingQuietMode.serializer(), it)
                    } catch (e: Exception) {
                        null
                    }
                }

                val newTiming = TimingQuietMode(
                    enable = enable ?: current?.enable ?: false,
                    startHour = startHour ?: current?.startHour ?: 0,
                    startMinute = startMinute ?: current?.startMinute ?: 0,
                    endHour = endHour ?: current?.endHour ?: 0,
                    endMinute = endMinute ?: current?.endMinute ?: 0,
                    timingCycle = timingCycle ?: current?.timingCycle?: TimingCycle.Daily
                )

                preferences[UserDataStoreKey.quiteModeByTimingKey] =
                    Json.encodeToString(TimingQuietMode.serializer(), newTiming)
            }
        }
    }

    override suspend fun enableSyncDND(bind: Boolean) {
        withContext(Dispatchers.IO) {
            userDataStore.edit { preferences ->
                val current = preferences[UserDataStoreKey.bindDNDKey]
                if (current == bind) {
                    return@edit
                }
                preferences[UserDataStoreKey.bindDNDKey] = bind
            }
        }
    }

    override fun getQuiteMode(): Flow<QuiteMode> {
        return userDataStore.data.map { preferences ->
            val json = preferences[UserDataStoreKey.quiteModeKey]
                ?: return@map QuiteMode(false, QuiteModeFromType.Manual)
            Json.decodeFromString(QuiteMode.serializer(), json)
        }.distinctUntilChanged()
    }


    override fun getTimingQuietMode(): Flow<TimingQuietMode?> {
        return userDataStore.data.map { preferences ->
            val json = preferences[UserDataStoreKey.quiteModeByTimingKey] ?: return@map null
            Json.decodeFromString(TimingQuietMode.serializer(), json)
        }.flowOn(Dispatchers.Default)
            .distinctUntilChanged()
    }

    override suspend fun getTimingQuietModeOnce(): TimingQuietMode? =
        getTimingQuietMode().firstOrNull()


    override fun getEnableSyncDND(): Flow<Boolean> {
        return userDataStore.data.map { preferences ->
            preferences[UserDataStoreKey.bindDNDKey] ?: false
        }.distinctUntilChanged()
    }

}