package com.interfun.buz.user.datasource

import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import com.interfun.buz.user.di.BizUserQualifier
import com.interfun.buz.user.entity.QuiteMode
import com.interfun.buz.user.entity.QuiteModeFromType
import com.interfun.buz.user.entity.TimingCycle
import com.interfun.buz.user.entity.TimingQuiteMode
import com.interfun.buz.user.storage.UserDataStoreKey
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.Json
import javax.inject.Inject

/**
 * Author: ChenYouSheng
 * Date: 2025/7/24
 * Email: <EMAIL>
 * Desc:
 */
class UserSettingDataSourceImpl @Inject constructor(
    @BizUserQualifier private val userDataStore: DataStore<Preferences>,
) : UserSettingDataSource {

    override suspend fun enableQuiteMode(quiteMode: QuiteMode) {
        withContext(Dispatchers.IO) {
            userDataStore.edit { preferences ->
                val json = preferences[UserDataStoreKey.quiteModeKey]
                val currentQuiteMode = json?.let {
                    try {
                        Json.decodeFromString(QuiteMode.serializer(), it)
                    } catch (e: Exception) {
                        null
                    }
                }
                // 恢复为非静音
                if (!quiteMode.enable && quiteMode.fromType != QuiteModeFromType.Manual) {
                    // 之前是手动，现在不是手动，返回
                    if (currentQuiteMode?.fromType == QuiteModeFromType.Manual) {
                        return@edit
                    }
                }
                preferences[UserDataStoreKey.quiteModeKey] =
                    Json.encodeToString(QuiteMode.serializer(), quiteMode)
            }
        }
    }

    override suspend fun enableTimingQuiteMode(
        enable: Boolean,
        startHour: Int,
        startMinute: Int,
        endHour: Int,
        endMinute: Int,
        timingCycle: TimingCycle
    ) {
        withContext(Dispatchers.IO) {
            userDataStore.edit { preferences ->
                val json = preferences[UserDataStoreKey.quiteModeByTimingKey]
                val current = json?.let {
                    try {
                        Json.decodeFromString(TimingQuiteMode.serializer(), it)
                    } catch (e: Exception) {
                        null
                    }
                }
                // 时间是有效的才更新时间，否则保留之前的时间
                val newTiming = TimingQuiteMode(
                    enable = enable,
                    startHour = if (startHour != 0) startHour else current?.startHour ?: 0,
                    startMinute = if (startMinute != 0) startMinute else current?.startMinute ?: 0,
                    endHour = if (endHour != 0) endHour else current?.endHour ?: 0,
                    endMinute = if (endMinute != 0) endMinute else current?.endMinute ?: 0,
                    timingCycle = timingCycle
                )

                preferences[UserDataStoreKey.quiteModeByTimingKey] =
                    Json.encodeToString(TimingQuiteMode.serializer(), newTiming)
            }
        }
    }

    override suspend fun enableSyncDND(bind: Boolean) {
        withContext(Dispatchers.IO) {
            userDataStore.edit { preferences ->
                val current = preferences[UserDataStoreKey.bindDNDKey]
                if (current == bind) {
                    return@edit
                }
                preferences[UserDataStoreKey.bindDNDKey] = bind
            }
        }
    }

    override fun getQuiteMode(): Flow<QuiteMode> {
        return userDataStore.data.map { preferences ->
            val json = preferences[UserDataStoreKey.quiteModeKey]
                ?: return@map QuiteMode(false, QuiteModeFromType.Manual)
            Json.decodeFromString(QuiteMode.serializer(), json)
        }
    }


    override fun getTimingQuiteMode(): Flow<TimingQuiteMode?> {
        return userDataStore.data.map { preferences ->
            val json = preferences[UserDataStoreKey.quiteModeByTimingKey] ?: return@map null
            Json.decodeFromString(TimingQuiteMode.serializer(), json)
        }.flowOn(Dispatchers.Default)
    }

    override suspend fun getTimingQuietModeOnce(): TimingQuiteMode? =
        getTimingQuiteMode().firstOrNull()


    override fun getEnableSyncDND(): Flow<Boolean> {
        return userDataStore.data.map { preferences ->
            preferences[UserDataStoreKey.bindDNDKey] ?: false
        }
    }

}