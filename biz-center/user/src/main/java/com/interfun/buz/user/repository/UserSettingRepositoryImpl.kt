package com.interfun.buz.user.repository

import android.app.NotificationManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.component.hilt.UserQualifier
import com.interfun.buz.user.datasource.UserSettingDataSource
import com.interfun.buz.user.entity.*
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.onSubscription
import kotlinx.coroutines.launch
import java.util.Calendar
import javax.inject.Inject

/**
 * Author: ChenYouSheng
 * Date: 2025/7/24
 * Email: <EMAIL>
 * Desc:
 */
class UserSettingRepositoryImpl @Inject constructor(
    @UserQualifier private val userSettingDataSource: UserSettingDataSource,
    @UserQualifier private val userScope: CoroutineScope,
    @UserQualifier private val timeTriggerRepository: TimeTriggerRepository,
    @ApplicationContext private val appContext: Context
) : UserSettingRepository {

    companion object {
        const val TAG = "UserSettingRepositoryImpl"
    }

    init {

        userScope.launch {
            combine(
                getEnableSyncDND(),
                inSystemDndFlow,
                getTimingQuiteMode(),
                timeTriggerRepository.timeTriggerShareFlow.onSubscription {
                    val timing = getTimingQuietModeOnce()?:let {
                        emit(TimeScheduledEvent.End)
                        return@onSubscription
                    }
                    if (timing.enable && isInTimingRange(timing)) {
                        emit(TimeScheduledEvent.Start)
                    } else {
                        emit(TimeScheduledEvent.End)
                    }
                }
            ) { enableSyncDND, inSystemDnd, timingMode, timeTriggerEvent ->
                if (enableSyncDND && inSystemDnd) {
                    true to QuiteModeFromType.DND
                } else {
                    if (isInTimingRange(timingMode)) {
                        true to QuiteModeFromType.Timing
                    } else {
                        false to QuiteModeFromType.Manual
                    }
                }
            }.distinctUntilChanged()
                .flowOn(Dispatchers.Default)
                .collect { (enable, fromType) ->
                    logDebug(TAG, "enableDND: $enable")
                    userSettingDataSource.enableQuiteMode(
                        QuiteMode(
                            enable = enable,
                            fromType = fromType
                        )
                    )
                }
        }


        // 用户设置定时Quite mode，触发闹钟
        userScope.launch {
            userSettingDataSource.getTimingQuiteMode().collect { timingQuiteMode ->
                if (timingQuiteMode != null && timingQuiteMode.enable) {
                    // 开始闹钟
                    timeTriggerRepository.scheduleAlarms(
                        hour = timingQuiteMode.startHour,
                        minute = timingQuiteMode.startMinute,
                        weekDay = timingQuiteMode.timingCycle is TimingCycle.WeekDays
                    )

                    // 结束闹钟
                    timeTriggerRepository.scheduleAlarms(
                        hour = timingQuiteMode.endHour,
                        minute = timingQuiteMode.endMinute,
                        alarmType = AlarmType.END,
                        weekDay = timingQuiteMode.timingCycle is TimingCycle.WeekDays
                    )
                } else {
                    timeTriggerRepository.cancelScheduleAlarms()
                }
            }
        }
    }

    override val inSystemDndFlow: Flow<Boolean>
        get() = callbackFlow {
            val receiver = object : BroadcastReceiver() {
                override fun onReceive(context: Context?, intent: Intent?) {
                    trySend(obtainDNDStateFromSystemService())
                }
            }
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                val filter = IntentFilter()
                filter.addAction(NotificationManager.ACTION_INTERRUPTION_FILTER_CHANGED)
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                    appContext.registerReceiver(receiver, filter, Context.RECEIVER_NOT_EXPORTED)
                } else {
                    appContext.registerReceiver(receiver, filter)
                }
            }
            val initData = obtainDNDStateFromSystemService()
            trySend(initData)
            awaitClose { appContext.unregisterReceiver(receiver) }
        }

    override fun enableQuiteMode(enable: Boolean, quiteModeFromType: QuiteModeFromType) {
        userScope.launch {
            userSettingDataSource.enableQuiteMode(QuiteMode(enable, quiteModeFromType))
        }
    }

    override fun enableTimingQuiteMode(
        enable: Boolean?,
        startHour: Int?,
        startMinute: Int?,
        endHour: Int?,
        endMinute: Int?,
        timingCycle: TimingCycle?
    ) {
        userScope.launch {
            userSettingDataSource.enableTimingQuiteMode(
                enable,
                startHour,
                startMinute,
                endHour,
                endMinute,
                timingCycle
            )
        }
    }

    override fun enableSyncDND(bind: Boolean) {
        userScope.launch {
            userSettingDataSource.enableSyncDND(bind)
        }
    }

    override fun getQuiteMode(): Flow<QuiteMode> {
        return userSettingDataSource.getQuiteMode()
    }

    override fun getTimingQuiteMode(): Flow<TimingQuiteMode?> {
        return userSettingDataSource.getTimingQuiteMode()
    }

    override suspend fun getTimingQuietModeOnce(): TimingQuiteMode? {
        return userSettingDataSource.getTimingQuietModeOnce()
    }

    override fun getEnableSyncDND(): Flow<Boolean> {
        return userSettingDataSource.getEnableSyncDND()
    }

    private fun shouldScheduleToday(
        timingCycle: TimingCycle
    ): Boolean {
        return when (timingCycle) {
            is TimingCycle.Daily -> true
            is TimingCycle.WeekDays -> {
                val dayOfWeek = Calendar.getInstance().get(Calendar.DAY_OF_WEEK)
                dayOfWeek != Calendar.SATURDAY && dayOfWeek != Calendar.SUNDAY
            }
        }
    }

    private fun isInTimingRange(timingMode: TimingQuiteMode?): Boolean {
        val now = Calendar.getInstance()
        if (timingMode == null) {
            return false
        }
        val startTimeMills = timeTriggerRepository.calculateTriggerTimeMillis(
            timingMode.startHour,
            timingMode.startMinute
        )
        val endTimeMills = timeTriggerRepository.calculateTriggerTimeMillis(
            timingMode.endHour,
            timingMode.endMinute
        )
        return now.timeInMillis in startTimeMills..endTimeMills
    }

    private fun obtainDNDStateFromSystemService(): Boolean {
        val notificationManager =
            appContext.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) { // android 7.0后才正式有勿扰模式，
            val interruptionFilter: Int = notificationManager.getCurrentInterruptionFilter()
            return interruptionFilter != NotificationManager.INTERRUPTION_FILTER_ALL
        }
        return false
    }
}