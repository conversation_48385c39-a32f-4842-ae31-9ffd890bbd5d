package com.interfun.buz.user.utils

import android.app.AlarmManager
import android.content.Context
import android.content.Intent
import android.os.Build
import android.provider.Settings
import androidx.core.net.toUri

object ExactAlarmPermissionUtil {

    fun hasExactAlarmPermission(context: Context): <PERSON><PERSON><PERSON> {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
            alarmManager.canScheduleExactAlarms()
        } else {
            true // Android 12 以下不需要权限
        }
    }

    fun requestExactAlarmPermission(context: Context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            val intent = Intent(Settings.ACTION_REQUEST_SCHEDULE_EXACT_ALARM).apply {
                data = "package:${context.packageName}".toUri()
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            context.startActivity(intent)
        }
    }

    fun shouldShowExactAlarmPrompt(context: Context): <PERSON><PERSON><PERSON> {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.S &&
               !hasExactAlarmPermission(context)
    }
}
