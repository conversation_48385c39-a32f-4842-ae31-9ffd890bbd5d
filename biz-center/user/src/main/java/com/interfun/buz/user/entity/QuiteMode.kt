package com.interfun.buz.user.entity

import androidx.annotation.Keep
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * Author: ChenYouSheng
 * Date: 2025/7/29
 * Email: chenyoush<PERSON>@vocalbeats.com
 * Desc:
 */
@Serializable
@Keep
data class QuiteMode(val enable: <PERSON><PERSON><PERSON>, val fromType: QuiteModeFromType)

@Serializable
sealed interface QuiteModeFromType {
    @Serializable
    @SerialName("Manual")
    object Manual : QuiteModeFromType

    @Serializable
    @SerialName("DND")
    object DND : QuiteModeFromType

    @Serializable
    @SerialName("Timing")
    object Timing : QuiteModeFromType
}
