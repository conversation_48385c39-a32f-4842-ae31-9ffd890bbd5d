package com.interfun.buz.user.usecase

import com.interfun.buz.user.entity.Disable
import com.interfun.buz.user.entity.Enable
import com.interfun.buz.user.entity.QuiteModeFromType
import com.interfun.buz.user.entity.QuiteTime
import com.interfun.buz.user.entity.SyncDND
import com.interfun.buz.user.entity.UserStateMode
import com.interfun.buz.user.repository.UserSettingRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import javax.inject.Inject

/**
 * Author: ChenYouSheng
 * Date: 2025/7/25
 * Email: <EMAIL>
 * Desc: https://vocalbeats.sg.larksuite.com/wiki/DgQ9wJCWZi9KYNkcn6FlNdXEgod#share-OSsAdzZFcovIwBxkbPTlEIGZgJh
 */
class UserStateUseCase @Inject constructor(
    private val userSettingRepository: UserSettingRepository
) {

    /**
     * “手动优先” ➜ 再判断是否同步 DND ➜ 再结合定时策略
     */
    operator fun invoke(): Flow<UserStateMode> = combine(
        userSettingRepository.getQuiteMode(),
        userSettingRepository.getTimingQuiteMode(),
    ) { (quiteModeEnable, fromType), timingQuiteMode ->

        val untilHour = timingQuiteMode?.endHour ?: 0
        val untilMinute = timingQuiteMode?.endMinute ?: 0

        val isManual = fromType == QuiteModeFromType.Manual

        val isDND = fromType == QuiteModeFromType.DND

        val isTiming = fromType == QuiteModeFromType.Timing && timingQuiteMode?.enable == true

        if (quiteModeEnable) {
            when {
                isManual -> Disable// 手动off优先级最高
                isDND -> SyncDND // DND静音次优先级
                isTiming -> QuiteTime(untilHour, untilMinute) // 定时优先级最低
                else -> Disable // 其他情况，默认静音
            }
        } else {
            Enable
        }
    }
}