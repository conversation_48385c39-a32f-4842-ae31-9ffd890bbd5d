package com.interfun.buz.user.di

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.PreferenceDataStoreFactory
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.preferencesDataStoreFile
import com.interfun.buz.common.constants.CommonMMKV
import com.interfun.buz.common.ktx.DefaultPreferencesMigration
import com.interfun.buz.user.repository.UserAuthRepository
import com.interfun.buz.component.hilt.UserComponentManager
import com.interfun.buz.user.manager.UserComponentManagerImpl
import com.interfun.buz.user.repository.UserAuthRepositoryImpl
import com.interfun.buz.component.hilt.UserAuth
import com.interfun.buz.component.hilt.UserComponent
import com.interfun.buz.component.hilt.UserQualifier
import com.interfun.buz.component.hilt.UserScope
import com.interfun.buz.user.datasource.UserSettingDataSource
import com.interfun.buz.user.datasource.UserSettingDataSourceImpl
import com.interfun.buz.user.entity.QuiteMode
import com.interfun.buz.user.entity.QuiteModeFromType
import com.interfun.buz.user.repository.TimeTriggerRepository
import com.interfun.buz.user.repository.TimeTriggerRepositoryImpl
import com.interfun.buz.user.repository.UserSettingRepository
import com.interfun.buz.user.repository.UserSettingRepositoryImpl
import com.interfun.buz.user.storage.UserDataStoreKey
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.EntryPoints
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineScope
import kotlinx.serialization.json.Json

@Module
@InstallIn(SingletonComponent::class)
internal abstract class UserSingletonModule {

    companion object {


        /**
         * 如果谁修改，这里一定不要添加 singleton 注解,这个需要每次都执行，否则会一直缓存第一个用户
         */
        @Provides
        fun provideUserComponent(userComponentManager: UserComponentManager): UserComponent {
            return userComponentManager.getUserComponentFlow().value
        }

        @Provides
        fun providerUserSettingRepository(userComponent: UserComponent): UserSettingRepository {
            return EntryPoints.get(userComponent, BizUserEntryPoint::class.java)
                .userSettingRepository()
        }

        @Provides
        fun providerTimeTriggerRepository(userComponent: UserComponent): TimeTriggerRepository {
            return EntryPoints.get(userComponent, BizUserEntryPoint::class.java)
                .timeTriggerRepository()
        }
    }


    @Binds
    abstract fun provideUserComponentManager(manager: UserComponentManagerImpl): UserComponentManager

    @Binds
    abstract fun provideUserAuthManager(userAuthRepository: UserAuthRepositoryImpl): UserAuthRepository

}

@Module
@InstallIn(UserComponent::class)
internal abstract class UserModule {

    /**
     * CoroutineScope在UserComponent中默认提供
     */
    @Binds
    @UserQualifier
    abstract fun provideUserScope(coroutineScope: CoroutineScope): CoroutineScope


    /**
     * userId在UserComponent中默认提供
     */
    @Binds
    @UserQualifier
    abstract fun provideUserId(userId: Long): Long


    /**
     * UserAuth在UserComponent中默认提供
     */
    @Binds
    @UserQualifier
    abstract fun provideUserAuth(userAuth: UserAuth): UserAuth


    @Binds
    @UserQualifier
    @UserScope
    abstract fun bindUserSettingDataSource(userSettingDataSource: UserSettingDataSourceImpl): UserSettingDataSource

    @Binds
    @UserQualifier
    @UserScope
    abstract fun bindUserSettingRepository(userSettingRepository: UserSettingRepositoryImpl): UserSettingRepository


    @Binds
    @UserQualifier
    @UserScope
    abstract fun bindTimeTriggerRepository(timeTriggerRepository: TimeTriggerRepositoryImpl): TimeTriggerRepository


    companion object {

        @Provides
        @BizUserQualifier
        @UserScope
        fun provideSocialDatastore(
            @ApplicationContext appContext: Context,
            @UserQualifier userAuth: UserAuth
        ): DataStore<Preferences> {

            val defaultPrefs = mapOf<Preferences.Key<*>, Any?>(
                Pair(UserDataStoreKey.quiteModeKey, QuiteMode(CommonMMKV.isQuietModeEnable, QuiteModeFromType.Manual)),
                Pair(UserDataStoreKey.bindDNDKey, CommonMMKV.settingSyncDND),
            )

            val migration = DefaultPreferencesMigration(
                defaults = defaultPrefs,
                valueSerializer = { key, value ->
                    if (key == UserDataStoreKey.quiteModeKey) {
                        Pair(
                            key, Json.encodeToString(
                                QuiteMode.serializer(),
                                value as QuiteMode
                            )
                        )
                    }
                    null
                })

            return PreferenceDataStoreFactory.create(
                scope = userAuth.userScope,
                migrations = listOf(migration)
            ) {
                appContext.preferencesDataStoreFile("biz_user_preferences_${userAuth.userId}")
            }
        }
    }
}

