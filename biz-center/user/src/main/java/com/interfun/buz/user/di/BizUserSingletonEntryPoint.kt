package com.interfun.buz.user.di

import com.interfun.buz.user.repository.TimeTriggerRepository
import com.interfun.buz.user.repository.UserSettingRepository
import dagger.hilt.EntryPoint
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

/**
 * Author: <PERSON><PERSON>ouSheng
 * Date: 2025/7/24
 * Email: chenyo<PERSON><PERSON>@vocalbeats.com
 * Desc:
 */

@EntryPoint
@InstallIn(SingletonComponent::class)
interface BizUserSingletonEntryPoint {
    fun userSettingRepository(): UserSettingRepository
    fun timeTriggerRepository(): TimeTriggerRepository
}