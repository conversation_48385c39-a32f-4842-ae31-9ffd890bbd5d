package com.interfun.buz.social.db.entity

import android.os.Parcelable
import androidx.annotation.Keep
import androidx.room.*
import kotlinx.parcelize.Parcelize

/**
 * Design for group's bot list
 */
@Keep
@Parcelize
@Entity(tableName = "group_bot_table", primaryKeys = ["groupId", "botUserId"])
data class GroupBotUserEntity(
    var groupId: Long,
    var botUserId: Long,
    @ColumnInfo(defaultValue = "0")
    val joinGroupTime : Long = 0
) : Parcelable

