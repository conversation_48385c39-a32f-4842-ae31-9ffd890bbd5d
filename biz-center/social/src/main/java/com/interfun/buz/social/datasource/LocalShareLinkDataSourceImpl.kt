package com.interfun.buz.social.datasource

import com.interfun.buz.component.hilt.UserQualifier
import com.interfun.buz.social.db.dao.ShareLinkDao
import com.interfun.buz.social.entity.ShareLinkEntity
import javax.inject.Inject

class LocalShareLinkDataSourceImpl @Inject constructor(
    @UserQualifier val shareLinkDao: ShareLinkDao
) : LocalShareLinkDataSource {
    override suspend fun getShareLink(targetId: Long, convType: Int): ShareLinkEntity? {
        return shareLinkDao.getShareLink(targetId,convType)
    }

    override suspend fun insert(shareLinkEntity: ShareLinkEntity) {
        shareLinkDao.insert(shareLinkEntity)
    }
}