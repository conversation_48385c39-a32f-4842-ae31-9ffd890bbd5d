package com.interfun.buz.social.repo

import com.interfun.buz.common.bean.Resp
import com.interfun.buz.social.db.entity.BuzGroup
import com.interfun.buz.social.db.entity.BuzGroupExtra
import com.interfun.buz.social.db.entity.BuzGroupComposite
import com.interfun.buz.social.entity.CreateGroupResult
import com.interfun.buz.social.entity.JoinGroupResult
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.Flow

interface GroupRepository {

    @Deprecated("为了处理旧代码，别用")
    fun getUserScope() : CoroutineScope

    fun init()

    //-------------------------------Group info start-------------------------------
    /**
     * memory cache -> db cache -> server
     * !!!Note!!!
     * !!!Note!!!
     * !!!Note!!!
     * This method will suspend the caller until cached is not null or request server done
     */
    suspend fun getGroup(groupId: Long): BuzGroup?

    /**
     * memory cache -> db cache -> server
     *
     * 这个跟getGroupFromCacheFlow的区别是，如果本地没有数据缓存或者forceRefresh未true时，
     * 会请求刷新服务器数据
     * @param forceRefresh if force refresh from server even if local data is not null
     */
    fun getGroupFlow(groupId: Long, forceRefresh: Boolean = false): Flow<BuzGroup>

    fun getGroupFlow(groupList: List<Long>): Flow<Map<Long, BuzGroup>>

    /**
     * Get group info from memory cache or db cache, return null if not found
     */
    suspend fun getGroupFromCache(groupId: Long): BuzGroup?

    /**
     * 当本地数据没有时，会先emit一个null
     * 这个跟getGroupFlow的区别是，如果本地没有数据，不会主动请求服务端刷新，除非其他地方刷新了缓存，这里也会收到变更
     */
    fun getGroupFromCacheFlow(groupId: Long): Flow<BuzGroup?>

    fun getGroupFromCacheFlow(groupList: List<Long>): Flow<Map<Long, BuzGroup>>
    
    suspend fun clearGroupInfo()

    // --------------------------------Group info end------------------------------


    // -----------------------------Group extra info start-------------------------
    /**
     * memory cache -> db cache -> server
     * !!!Note!!!
     * !!!Note!!!
     * !!!Note!!!
     * This method will suspend the caller until cached is not null or request server done
     */
    suspend fun getGroupExtra(groupId: Long): BuzGroupExtra?

    /**
     * memory cache -> db cache -> server
     *
     * 这个跟getGroupFromCacheFlow的区别是，如果本地没有数据缓存或者forceRefresh未true时，
     * 会请求刷新服务器数据
     */
    fun getGroupExtraFlow(
        groupId: Long,
        forceRefresh: Boolean = false
    ): Flow<BuzGroupExtra>

    fun getGroupExtraFlow(groupList: List<Long>): Flow<Map<Long, BuzGroupExtra>>

    /**
     * Get group extra info from memory cache or db cache, return null if not found
     */
    suspend fun getGroupExtraFromCache(groupId: Long): BuzGroupExtra?

    @Deprecated("兼容旧代码，新代码不要使用")
    fun getGroupExtraFromCacheNotSuspend(groupId: Long) : BuzGroupExtra?

    /**
     * 当本地数据没有时，会先emit一个null
     * 这个跟getGroupExtraFlow的区别是，如果本地没有数据，不会主动请求服务端刷新，除非其他地方刷新了缓存，这里也会收到变更
     */
    fun getGroupExtraFromCacheFlow(groupId: Long): Flow<BuzGroupExtra?>

    fun getGroupExtraFromCacheFlow(groupList: List<Long>): Flow<Map<Long, BuzGroupExtra>>

    /**
     * refresh group extra info from server and retry automatically if failed
     */
    fun syncGroupExtraFromServer(groupId: Long)

    suspend fun updateLocalGroupExtra(extraInfo: List<BuzGroupExtra>): List<BuzGroupExtra>

    suspend fun updateLocalGroupExtra(extra: BuzGroupExtra): BuzGroupExtra

    // ----------------------Group extra info end------------------------


    // ----------------------Group composite info start-------------------------

    /**
     * memory cache -> db cache -> server
     * !!!Note!!!
     * !!!Note!!!
     * !!!Note!!!
     * This method will suspend the caller until cached is not null or request server done
     */
    suspend fun getGroupComposite(groupId: Long): BuzGroupComposite?

    @Deprecated("兼容旧代码，新代码别用")
    fun getGroupCompositeNotSuspend(groupId: Long): BuzGroupComposite?

    @Deprecated("兼容旧代码，新代码别用")
    fun getGroupCompositeFromMem(groupId: Long): BuzGroupComposite?

    /**
     * memory cache -> db cache -> server
     *
     * 这个跟getGroupCompositeFromCacheFlow()的区别是，如果本地没有数据缓存或者forceRefresh未true时，
     * 会请求刷新服务器数据
     */
    fun getGroupCompositeFlow(groupId: Long, forceRefresh: Boolean = false): Flow<BuzGroupComposite>

    fun getGroupCompositeFlow(groupList: List<Long>): Flow<Map<Long, BuzGroupComposite>>

    /**
     * Get group composite info from memory cache or db cache, return null if not found
     * Note that it will return null if BuzGroupComposite.BuzGroup is null but BuzGroupComposite.BuzGroupExtra is not null
     */
    suspend fun getGroupCompositeFromCache(groupId: Long): BuzGroupComposite?

    /**
     * 当本地数据没有时，会先emit一个null
     * 这个跟getGroupCompositeFlow的区别是，如果本地没有数据，不会主动请求服务端刷新，除非其他地方刷新了缓存，这里也会收到变更
     */
    fun getGroupCompositeFromCacheFlow(
        groupId: Long,
        refreshIfNull: Boolean = false
    ): Flow<BuzGroupComposite?>

    fun getGroupCompositeFromCacheFlow(groupList: List<Long>): Flow<Map<Long, BuzGroupComposite>>

    /**
     * get group composite info from server, return null if failed
     * it will not retry automatically when failed
     */
    suspend fun getGroupCompositeFromServer(groupId: Long): Resp<BuzGroupComposite?>

    /**
     * refresh group info from server and retry automatically if failed
     */
    fun syncGroupCompositeFromServer(groupId: Long)

    suspend fun getAllJoinedGroupsFromCache() : List<BuzGroupComposite>

    suspend fun getAllJoinedGroups() : List<BuzGroupComposite>

    suspend fun getAllJoinedGroupsFromServer() : List<BuzGroupComposite>

    fun getAllJoinedGroupsFromCacheFlow() : Flow<List<BuzGroupComposite>>

    fun getAllJoinedGroupsFlow(forceRefresh: Boolean) : Flow<List<BuzGroupComposite>>

    fun syncAllJoinedGroupsFromServer()
    //--------------------------Group composite info end---------------------------


    //--------------------------Group management---------------------------

    suspend fun updateGroupSetting(
        groupId: Long,
        muteMessages: Int? = null,
        muteNotification: Int? = null,
    )

    suspend fun updateGroupInfo(groupId: Long, name: String?, portraitUploadId: Long?) : Resp<BuzGroupComposite?>

    suspend fun createGroup(
        groupName: String?,
        invitedUserIds: List<Long>?,
        portraitUploadId: Long?
    ): Resp<CreateGroupResult>

    suspend fun joinGroup(groupId: Long, inviterId: Long? = null): Resp<JoinGroupResult>

    suspend fun quitGroup(groupId: Long): Resp<Unit>

    suspend fun reportGroup(groupId: Long) : Resp<Unit>
} 