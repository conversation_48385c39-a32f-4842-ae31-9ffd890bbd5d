package com.interfun.buz.social.db.entity

import com.interfun.buz.common.widget.portrait.group.BuzPortrait

sealed interface BuzSocialEntity {
    data class Group(val groupComposite: BuzGroupComposite) : BuzSocialEntity
    data class User(val userComposite: BuzUserComposite) : BuzSocialEntity
}

data class BuzSimpleSocialEntity(
    val targetId: Long,
    val isGroup: <PERSON><PERSON>an,
    val name: String,
    val portrait: BuzPortrait?
)