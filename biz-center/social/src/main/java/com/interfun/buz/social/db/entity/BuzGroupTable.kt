package com.interfun.buz.social.db.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.TypeConverter
import androidx.room.TypeConverters
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import com.google.gson.reflect.TypeToken
import com.interfun.buz.base.ktx.takeIfNotNullAndNotEmpty
import java.lang.reflect.Type


/**
 * ！！！注意 ！！！
 * ！！！注意 ！！！
 * ！！！注意 ！！！
 * 所有字段请都添加defaultValue，方便以后做部分内容更新可以直接使用upsert
 */
@Entity("buz_group", primaryKeys = ["groupId"])
@TypeConverters(SimpleUserConverter::class,PortraitsConverter::class)
class BuzGroupTable(
    @ColumnInfo(name = "groupId")
    val groupId: Long,
    @ColumnInfo(name = "serverGroupName", defaultValue = "")
    val serverGroupName: String? = "",
    //客户端本地生成的名称，根据firstFewUsers来生成的名称,为了兼容旧代码
    @Deprecated("请不用使用这个，这个是为了兼容旧代码，新代码请使用GroupRepository获取groupFlow，里面的名称可以动态变更")
    @ColumnInfo(name = "displayName", defaultValue = "")
    val displayName: String? = "",
    //用户主动设置的群头像，目前已经关闭自定义头像入口
    @ColumnInfo(name = "portraitUrl", defaultValue = "")
    val portraitUrl: String? = "",
    //服务端根据firstFewUsers生成的用户头像，当用户没有自定义头像时，可以使用这个或者使用firstFewUsers的用户头像拼接
    @ColumnInfo(name = "serverPortraitUrl", defaultValue = "")
    val serverPortraitUrl: String? = "",
    @ColumnInfo(name = "memberNum", defaultValue = "0")
    val memberNum: Int = 0,
    @ColumnInfo(name = "maxMemberNum", defaultValue = "0")
    val maxMemberNum: Int = 0,
    @ColumnInfo(name = "groupStatus", defaultValue = "1")
    val groupStatus: Int = 1,
    @ColumnInfo(name = "groupType", defaultValue = "1")
    val groupType: Int = 1, //1 - 普通群,  2 - 大群
    @ColumnInfo(name = "firstFewUsers", defaultValue = "NULL")
    val firstFewUsers: List<GroupSimpleUser>? = listOf(),
    @Deprecated("请不要赋值，不要使用，为了兼容旧数据库，迁移数据过来,新的数据都有firstFewUsers，应该都使用firstFewUsers，没有的情况考虑使用这个")
    @ColumnInfo(name = "oldFirstFewPortraits", defaultValue = "NULL")
    val oldFirstFewPortraits: List<String?>? = listOf(),
    /**
     * ！！！注意 ！！！
     * ！！！注意 ！！！
     * ！！！注意 ！！！
     * 所有字段请都添加defaultValue，方便以后做部分内容更新可以直接使用upsert
     */
)

class SimpleUserConverter {
    private val gson = Gson()

    @TypeConverter
    fun simpleUserListToString(list: List<GroupSimpleUser>?): String? {
        return gson.toJson(list)
    }

    @TypeConverter
    fun stringToSimpleUserList(json: String?): List<GroupSimpleUser>? {
        val listType: Type = object : TypeToken<List<GroupSimpleUser>?>() {}.type
        return gson.fromJson(json, listType)
    }
}

class PortraitsConverter {
    private val gson = Gson()

    @TypeConverter
    fun stringListToString(list: List<String?>?): String? {
        return gson.toJson(list)
    }

    @TypeConverter
    fun stringToStringList(json: String?): List<String?>? {
        val listType: Type = object : TypeToken<List<String?>?>() {}.type
        return gson.fromJson(json, listType)
    }
}

class GroupSimpleUser(
    @SerializedName("uid")
    val userId: Long,
    @SerializedName("fN")
    val firstName: String?,
    @SerializedName("lN")
    val lastName: String?,
    @SerializedName("uN")
    val userName: String?,
    @SerializedName("pt")
    val portrait: String?
) {
    val firstNickName = firstName.takeIfNotNullAndNotEmpty()
        ?: lastName.takeIfNotNullAndNotEmpty()
        ?: userName
        ?: "$userId"
}