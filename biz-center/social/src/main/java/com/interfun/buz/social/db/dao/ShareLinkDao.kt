package com.interfun.buz.social.db.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.interfun.buz.social.entity.ShareLinkEntity

@Dao
interface ShareLinkDao {
    @Insert(onConflict = OnConflictStrategy.Companion.REPLACE)
    suspend fun insert(shareLinkEntity: ShareLinkEntity)

    @Query("SELECT * FROM share_link WHERE targetId = :targetId AND convType = :convType")
    fun getShareLink(targetId: Long,convType:Int): ShareLinkEntity?
}