package com.interfun.buz.social.repo

import androidx.collection.LruCache
import com.google.gson.Gson
import com.interfun.buz.base.ktx.logError
import com.interfun.buz.common.bean.Resp
import com.interfun.buz.common.bean.push.PushBusinessType
import com.interfun.buz.component.hilt.UserQualifier
import com.interfun.buz.signal.ISignalManagerPresenter
import com.interfun.buz.signal.getProtocolDataChangeFlow
import com.interfun.buz.social.datasource.GroupMembersRemoteDataSource
import com.interfun.buz.social.entity.GroupOnlineMembersInfo
import com.interfun.buz.social.entity.GroupOnlineMembersPushData
import com.interfun.buz.social.entity.GroupOnlineUserInfo
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import javax.inject.Inject

internal class GroupOnlineMembersRepositoryImpl @Inject constructor(
    @UserQualifier private val groupMembersRemoteDs: GroupMembersRemoteDataSource,
    @UserQualifier private val userScope: CoroutineScope,
    private val signalManager: ISignalManagerPresenter,
) : GroupOnlineMembersRepository {

    private val TAG = "GroupOnlineMembersRepository"
    private val onlineGroupMembersCache = LruCache<Long, GroupOnlineMembersInfo>(1000)
    private val updateFlow = MutableSharedFlow<Set<Long>>()
    private val mutex = Mutex()
    private val gson = Gson()

    init {
        userScope.launch {
            signalManager.getProtocolDataChangeFlow()
                .filter { it.businessType == PushBusinessType.WT_GROUP_MEMBER_CHANGE.type }
                .collect { pushData ->
                    val groupId = pushData.businessId?.toLongOrNull() ?: return@collect
                    val dataStr = pushData.data?.toString()
                    val onlineMembersPushData = try {
                        gson.fromJson(dataStr, GroupOnlineMembersPushData::class.java)
                    } catch (t: Throwable) {
                        logError(TAG, t, "parse push data error")
                        return@collect
                    }
                    onlineMembersPushData?.let {
                        updateCache(
                            groupId,
                            GroupOnlineMembersInfo(
                                it.onlineMembers,
                                pushData.sendTimeStamp,
                                it.onlineMemberCount
                            )
                        )
                    }
                }
        }
    }

    override fun getOnlineGroupMembersFromCache(groupIds: List<Long>): Flow<Map<Long, GroupOnlineMembersInfo?>> =
        flow {
            if (groupIds.isEmpty()) {
                emit(emptyMap())
                return@flow
            }
            val result = HashMap<Long, GroupOnlineMembersInfo?>(groupIds.size)
            updateFlow.onSubscription { emit(groupIds.toSet()) }
                .map { updatedSet ->
                    val containedSet = hashSetOf<Long>()
                    groupIds.forEach { groupId ->
                        if (updatedSet.contains(groupId)) {
                            containedSet.add(groupId)
                        }
                    }
                    containedSet
                }.collect { updatedSet ->
                    updatedSet.forEach { groupId ->
                        val onlineMembersInfo = onlineGroupMembersCache[groupId]
                        result[groupId] = onlineMembersInfo
                    }
                    emit(result.toMap())
                }
        }

    override fun getOnlineGroupMembersFromCache(groupId: Long): Flow<GroupOnlineMembersInfo?> =
        updateFlow.onSubscription { emit(setOf(groupId)) }
            .filter { it.contains(groupId) }
            .map {
                onlineGroupMembersCache[groupId]
            }


    override suspend fun refreshOnlineGroupMembers(groupId: Long): Resp<GroupOnlineMembersInfo?> {
        val resp = groupMembersRemoteDs.requestOnlineGroupMembers(groupId)
        return when (resp) {
            is Resp.Success -> {
                val data = resp.data
                if (data == null) {
                    onlineGroupMembersCache.remove(groupId)
                    return Resp.Success(null, resp.prompt)
                }
                val userList = data.onlineMemberList.map {
                    val userInfo = it.userInfo
                    GroupOnlineUserInfo(userInfo.userId ?: 0L, userInfo.portrait)
                }
                updateCache(
                    groupId,
                    GroupOnlineMembersInfo(userList, data.timestamp, data.onlineMemberNumber)
                )
                Resp.Success(onlineGroupMembersCache[groupId], resp.prompt)
            }

            is Resp.Error -> {
                Resp.Error(resp.code, resp.msg, resp.prompt)
            }
        }
    }

    private suspend fun updateCache(groupId: Long, onlineMembersInfo: GroupOnlineMembersInfo) {
        val result = mutex.withLock {
            val oldInfo = onlineGroupMembersCache[groupId]
            if (oldInfo == null || onlineMembersInfo.timestamp > oldInfo.timestamp) {
                onlineGroupMembersCache.put(
                    groupId,
                    GroupOnlineMembersInfo(
                        onlineMembersInfo.onlineMembers,
                        onlineMembersInfo.timestamp,
                        onlineMembersInfo.onlineMemberCount
                    )
                )
                true
            } else {
                false
            }
        }
        if (result) {
            updateFlow.emit(setOf(groupId))
        }
    }
}