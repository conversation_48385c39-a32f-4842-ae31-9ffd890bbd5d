package com.interfun.buz.social.db.entity

import android.os.Parcelable
import androidx.room.Ignore
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize

@Parcelize
data class BuzOfficialAccountInfo(
    val user: BuzUser,
    val buzRelation: BuzUserRelationEntity?,
    val extra: BuzOfficialAccountExtra?
) : Parcelable {

    @IgnoredOnParcel
    @Ignore
    val userComposite = BuzUserComposite(user, buzRelation)
}