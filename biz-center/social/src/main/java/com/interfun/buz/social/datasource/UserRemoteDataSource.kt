package com.interfun.buz.social.datasource

import com.buz.idl.user.bean.UserInfo
import com.buz.idl.user.bean.UserRelation
import com.buz.idl.user.bean.UserRelationInfo
import com.buz.idl.user.response.ResponseGetFriendList
import com.buz.idl.user.response.ResponseGetUserInfo
import com.interfun.buz.common.bean.Resp
import com.interfun.buz.social.db.entity.BuzUser
import com.interfun.buz.social.db.entity.BuzUserComposite

internal interface UserRemoteDataSource {
    suspend fun requestUserComposite(userId: Long): Resp<ResponseGetUserInfo?>
    suspend fun requestUserCompositeBatch(userIds: List<Long>): Resp<List<UserRelationInfo>?>
    suspend fun getUserInfo(userId: Long): Resp<UserInfo?>
    suspend fun requestUserInfoBatch(userIds: List<Long>): Resp<List<UserInfo>?>
    suspend fun requestUserRelation(userId: Long): Resp<UserRelation?>
    suspend fun requestUserRelationBatch(userIds: List<Long>): Resp<List<UserRelation>?>

    suspend fun getFriendList(timestamp : Long): Resp<ResponseGetFriendList?>
    suspend fun getBlockList(): Resp<List<UserInfo>>
    suspend fun addFriend(userId: Long, source: Int, businessId: String?): Resp<UserRelationInfo>
    suspend fun addBuzAIAsFriend(): Resp<UserRelationInfo>
    suspend fun deleteFriend(userId: Long): Resp<UserRelation>
    suspend fun blockUser(userId: Long): Resp<UserRelation>
    suspend fun unblockUser(userId: Long): Resp<UserRelation>
    suspend fun reportUser(userId: Long): Resp<Boolean>

    suspend fun agreeFriendApply(userId: Long) : Resp<UserRelation>
    suspend fun refuseFriendApply(userId: Long) : Resp<UserRelation>
    suspend fun agreeAddResearchAccount() : Resp<UserRelationInfo?>
    suspend fun refuseAddResearchAccount() : Resp<UserRelationInfo?>

    /**
     * @param muteNotification see [com.interfun.buz.common.bean.chat.MuteType]
     * @param muteMessages see [com.interfun.buz.common.bean.chat.MuteType]
     * @param remark 备注信息
     */
    suspend fun updateUserSetting(
        userId: Long,
        muteMessages: Int? = null,
        muteNotification: Int? = null,
        remark: String? = null
    ): Resp<Boolean>
}