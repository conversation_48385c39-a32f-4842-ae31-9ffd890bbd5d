package com.interfun.buz.social.datasource

import com.buz.idl.bot.response.ResponseBotGroupChange
import com.buz.idl.group.bean.GroupMember
import com.buz.idl.group.response.ResponseGetGroupMembers
import com.buz.idl.group.response.ResponseGetGroupOnlineMembers
import com.interfun.buz.common.bean.Resp
import com.interfun.buz.social.entity.InviteUserToGroupResult

internal interface GroupMembersRemoteDataSource {
    suspend fun requestGroupMemberList(groupId: Long, queryParams: String? = null): Resp<ResponseGetGroupMembers>
    suspend fun kickOutGroupMembers(groupId: Long, userIds: List<Long>): Resp<Boolean>
    suspend fun kickOutGroupBots(groupId: Long, botIds: List<Long>): Resp<List<GroupMember>?>
    suspend fun addBotToGroup(botUserIds: List<Long>, groupIds: List<Long>): Resp<ResponseBotGroupChange?>
    suspend fun inviteUserToGroup(groupId: Long, userIds: List<Long>): Resp<InviteUserToGroupResult>
    suspend fun requestOnlineGroupMembers(groupId: Long): Resp<ResponseGetGroupOnlineMembers?>
}