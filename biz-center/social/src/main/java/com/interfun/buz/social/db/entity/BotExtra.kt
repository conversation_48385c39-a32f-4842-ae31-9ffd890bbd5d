package com.interfun.buz.social.db.entity

import android.os.Parcelable
import androidx.annotation.Keep
import androidx.room.ColumnInfo
import androidx.room.Embedded
import androidx.room.Entity
import androidx.room.TypeConverter
import androidx.room.TypeConverters
import com.buz.idl.bot.bean.BotInfo
import com.buz.idl.bot.bean.BotSettingOption
import com.buz.idl.bot.bean.BotTopic
import com.buz.idl.bot.bean.BotTranslateLanguage
import com.buz.idl.bot.bean.BotUIConfig
import com.buz.idl.bot.bean.BotVoiceStyle
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.base.ktx.isNull
import com.interfun.buz.common.R
import com.interfun.buz.common.database.entity.DbConvertor
import com.interfun.buz.common.ktx.getIntDefault
import com.interfun.buz.common.ktx.getStringDefault
import com.interfun.buz.common.manager.cache.ai.TranslatorLanguage
import com.interfun.buz.common.utils.fromJson
import com.interfun.buz.common.utils.gsonInstance
import com.interfun.buz.common.utils.language.LanguageBean
import com.interfun.buz.common.utils.language.LanguageManager
import com.interfun.buz.common.utils.language.language_en
import com.interfun.buz.social.db.entity.BotExtra.BotStatus
import com.interfun.buz.social.db.entity.BotExtra.BotStatus.*
import kotlinx.parcelize.Parcelize



/////////////////////////////////////////////////
///          database's bean                   //
////////////////////////////////////////////////
@Keep
@Parcelize
data class BotTopicWrapper(
    var id: Long = -1,
    var title: String = "",
    var prompt: String = "",
    var emoji: String = "",
    var description: String = "",
) : Parcelable

@Keep
@Parcelize
data class BotUIConfigWrapper(
    @ColumnInfo(name = "BotUIConfigWrapper_showTopic")
    val showTopic: Boolean = false,
    @ColumnInfo(name = "BotUIConfigWrapper_showLanguage")
    val showLanguage: Boolean = false,
    @ColumnInfo(name = "BotUIConfigWrapper_showVoiceStyle")
    val showVoiceStyle: Boolean = false,
    @ColumnInfo(name = "BotUIConfigWrapper_useRemotePortrait")
    val useRemotePortrait: Boolean = false,
    @ColumnInfo(name = "BotUIConfigWrapper_showTranslation")
    val showTranslation: Boolean = false,
    @ColumnInfo(name = "BotUIConfigWrapper_showImageButton")
    val showImageButton : Boolean = false,
    @ColumnInfo(name = "BotUIConfigWrapper_showJoinGroup")
    val showJoinGroup : Boolean = false,
    @ColumnInfo(name = "BotUIConfigWrapper_showPrivateChatMsgFeedbackEntrance")
    val showPrivateChatMsgFeedbackEntrance : Boolean = false,
) : Parcelable

@Parcelize
@Keep
data class BotVoiceStyleWrapper(
    val languageCode: String,
    val voiceStyleId: Long,
    val displayName: String,
    val sampleAudioUrl: String?
) : Parcelable

@Parcelize
@Keep
data class TranslatorLanguageItem(val displayName: String, val languageCode: String) : Parcelable {
    companion object {
        val AUTO_LANGUAGE_CODE = "auto"
    }
}

@Parcelize
@Keep
data class BotSettingOptionWrapper(
    val languageOptions: List<String>?,
    val voiceStyleOptions: List<BotVoiceStyleWrapper>?,
    val translateSourceLanguage: List<TranslatorLanguageItem>?,
    val translateTargetLanguage: List<TranslatorLanguageItem>?
) : Parcelable

class BotInfoConvert {
    @TypeConverter
    fun botTopicWrapperToJson(info: List<BotTopicWrapper>?): String? {
        if (info == null) {
            return null
        }
        return gsonInstance.toJson(info)
    }

    @TypeConverter
    fun jsonToBotTopicWrapper(json: String?): List<BotTopicWrapper>? {
        if (json.isNullOrEmpty()) {
            return null
        }
        return json.fromJson<List<BotTopicWrapper>>()
    }

    @TypeConverter
    fun botSettingOptionWrapperToJson(info: BotSettingOptionWrapper?): String? {
        if (info == null) {
            return null
        }
        return gsonInstance.toJson(info)
    }

    @TypeConverter
    fun jsonToBotSettingOptionWrapper(json: String?): BotSettingOptionWrapper? {
        if (json.isNullOrEmpty()) {
            return null
        }
        return json.fromJson<BotSettingOptionWrapper>()
    }

    @TypeConverter
    fun botStatusToInt(status: BotStatus): Int {
        return status.status
    }

    @TypeConverter
    fun intToBotStatus(status: Int): BotStatus {
        return BotStatus.entries.firstOrNull { it.status == status }
            ?: ENABLE
    }

    @TypeConverter
    fun botSingleSettingsToWholeSetting(botSingleSettings: List<BotSingleSettingEntity>): BotWholeSettingEntity? {
        if (botSingleSettings.isEmpty()) {
            return null
        }
        val botId = botSingleSettings.first().botUserId
        botSingleSettings.forEach {
            if (it.botUserId != botId) {
                throw IllegalArgumentException("botSingleSettings must have the same botUserId to build BotWholeSettingEntity")
            }
        }
        return BotWholeSettingEntity.build(botId, botSingleSettings)
    }
}

@Parcelize
@Keep
@TypeConverters(value = [DbConvertor::class, BotInfoConvert::class])
@Entity(tableName = "botinfo_table", primaryKeys = ["botUserId"])
data class BotExtra(
    val botUserId: Long = -1,
    val description: String = "",
    val topics: List<BotTopicWrapper> = emptyList(),
    val options: BotSettingOptionWrapper? = null,
    @Embedded val botUIConfig: BotUIConfigWrapper? = BotUIConfigWrapper(),
    val shortDescription: String? = "",
    @Embedded val descriptionLink: BotDescriptionLink? = BotDescriptionLink(),
    @ColumnInfo(defaultValue = "0")
    val status: BotStatus = ENABLE,
) : Parcelable {

    companion object {
        fun rCodeToBotStatus(code: Int): BotStatus {
            return when (code) {
                0 -> ENABLE
                1 -> AREA_LIMITED
                2 -> LANGUAGE_LIMITED
                else -> UNAVAILABLE
            }
        }
    }

    fun getTranslatorLanguageOrDefault(setting: BotWholeSettingEntity?): TranslatorLanguage {
        return getTranslatorLanguageOrDefault(setting?.sourceLanguage, setting?.targetLanguage)
    }

    fun getTranslatorLanguageOrDefault(
        settingSourceCode: String?,
        settingTargetCode: String?
    ): TranslatorLanguage {
        // Source language
        val sourceLanguage = options?.translateSourceLanguage
            ?.firstOrNull { it.languageCode == settingSourceCode }
        val sourceLang = if (settingSourceCode.isNullOrEmpty() || sourceLanguage.isNull()) {
            getDefaultSourceLanguage()
        } else {
            val sourceDisplayName = sourceLanguage?.displayName ?: settingSourceCode
            LanguageBean(sourceDisplayName, settingSourceCode)
        }

        // Target Language
        val targetLanguage = options?.translateTargetLanguage
            ?.firstOrNull { it.languageCode == settingTargetCode }
        val targetLang = if (settingTargetCode.isNullOrEmpty() || targetLanguage.isNull()) {
            getDefaultTargetLanguage()
        } else {
            val displayName = targetLanguage?.displayName ?: settingTargetCode
            LanguageBean(displayName, settingTargetCode)
        }
        return TranslatorLanguage(sourceLang, targetLang)
    }

    private fun getDefaultSourceLanguage(): LanguageBean {
        val optionList = options?.translateTargetLanguage
        if (optionList.isNullOrEmpty()) {
            return LanguageBean(R.string.auto_detect.asString(), "auto")
        }
        val autoItem =
            optionList.find { it.languageCode == "auto" }
        return if (autoItem != null) {
            LanguageBean(autoItem.displayName, autoItem.languageCode)
        } else {
            LanguageBean(R.string.auto_detect.asString(), "auto")
        }
    }

    private fun getDefaultTargetLanguage(): LanguageBean {
        val optionList = options?.translateTargetLanguage
        if (optionList.isNullOrEmpty()) {
            return LanguageBean(R.string.english.asString(), language_en.code)
        } else {
            val appLangCode = LanguageManager.getLocaleLanguageCode()
            val appItem = optionList.find { it.languageCode == appLangCode }
            if (appItem != null) {
                return LanguageBean(appItem.displayName, appItem.languageCode)
            }
            val enItem =
                optionList.find { it.languageCode == language_en.code }
            if (enItem != null) {
                return LanguageBean(enItem.displayName, enItem.languageCode)
            }
            val firstItem = optionList.first()
            return LanguageBean(firstItem.displayName, firstItem.languageCode)
        }
    }

    enum class BotStatus(val status: Int){
        ENABLE(0),
        AREA_LIMITED(1),
        LANGUAGE_LIMITED(2),
        UNAVAILABLE(99)
    }
}


@Keep
@Parcelize
data class BotDescriptionLink(
    @ColumnInfo(name = "BotDescriptionLink_displayName")
    val displayName: String = "",
    @ColumnInfo(name = "BotDescriptionLink_type")
    val type: Int = 0,
    @ColumnInfo(name = "BotDescriptionLink_scheme")
    val scheme: String = "",
    @ColumnInfo(name = "BotDescriptionLink_extraData")
    val extraData: String = ""
) : Parcelable {
    fun hasLink(): Boolean {
        return displayName.isNotEmpty() && scheme.isNotEmpty() && extraData.isNotEmpty()
    }
}

/////////////////////////////////////////////////
///       IDL's bean convert DB's bean         //
////////////////////////////////////////////////
fun BotTopic.convertDb(): BotTopicWrapper {
    return BotTopicWrapper(this.id?:-1, this.title?:"", this.prompt?:"",this.emoji?:"", this.description?:"")
}

fun BotUIConfig.convertDb(): BotUIConfigWrapper {
    return BotUIConfigWrapper(
        this.showTopic ?: false,
        this.showLanguage ?: false,
        this.showVoiceStyle ?: false,
        this.useRemotePortrait ?: false,
        this.showTranslation ?: false,
        this.showImageButton ?: false,
        this.showJoinGroup ?: false,
        this.showPrivateChatMsgFeedbackEntrance ?: false
    )
}

fun BotVoiceStyle.convertDb(): BotVoiceStyleWrapper {
    return BotVoiceStyleWrapper(
        this.languageCode?:"",
        this.voiceStyleId?:-1,
        this.displayName?:"",
        this.sampleAudioUrl
    )
}
fun BotTranslateLanguage.convertDb(): TranslatorLanguageItem {
    return TranslatorLanguageItem(this.displayName ?: "", this.languageCode ?: "")
}

fun BotSettingOption.convertDb(): BotSettingOptionWrapper {
    return BotSettingOptionWrapper(
        this.languageOptions,
        this.voiceStyleOptions?.map { it.convertDb() },
        this.translateSourceLanguage?.map { it.convertDb() },
        this.translateTargetLanguage?.map { it.convertDb() }
    )
}

fun BotInfo.convertDb(): BotExtra {
    return BotExtra(
        this.botUserId ?: -1,
        this.description ?: "",
        this.topics?.map { it.convertDb() } ?: emptyList(),
        this.options?.convertDb(),
        this.botUIConfig?.convertDb(),
        this.shortDescription ?: "",
        this.descriptionLink.convertDb(),
    )
}

private fun com.buz.idl.bot.bean.BotDescriptionLink?.convertDb(): BotDescriptionLink {
    return BotDescriptionLink(
        displayName = this?.displayName.getStringDefault(),
        type = this?.actionInfo?.type.getIntDefault(),
        scheme = this?.actionInfo?.router?.scheme.getStringDefault(),
        extraData = this?.actionInfo?.router?.extraData.getStringDefault()
    )
}