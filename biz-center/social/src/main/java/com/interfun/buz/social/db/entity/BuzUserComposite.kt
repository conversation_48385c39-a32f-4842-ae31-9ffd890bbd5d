package com.interfun.buz.social.db.entity

import android.os.Parcelable
import androidx.room.Embedded
import androidx.room.Ignore
import androidx.room.Relation
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.common.R
import com.interfun.buz.common.database.entity.UserRelationInfo
import com.interfun.buz.social.entity.SimpleBuzUser
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize

@Parcelize
data class BuzUserComposite(
    @Embedded val user: BuzUser,
    @Relation(parentColumn = "userId", entityColumn = "userId")
    val relationInfo: BuzUserRelationEntity?
) : Parcelable {

    @IgnoredOnParcel
    val fullNickName: String get() = user.fullNickName(relationInfo)

    @IgnoredOnParcel
    val firstNickName: String get() = user.firstNickName(relationInfo)

    @IgnoredOnParcel
    @Ignore
    val isFriend = relationInfo?.isFriend ?: false

    @IgnoredOnParcel
    @Ignore
    val isMe = relationInfo?.isMe ?: false

    @Deprecated("为了兼容旧的代码，新代码请不用使用")
    fun toOldUserRelationInfo() : UserRelationInfo{
        return UserRelationInfo(
            userId = user.userId,
            phone = "",
            userName = user.userName,
            firstName = user.firstName,
            lastName = user.lastName,
            portrait = user.portrait,
            registerTime = user.registerTime ?: 0L,
            relation = relationInfo?.relation?:0,
            remark = relationInfo?.remark,
            buzId = user.buzId,
            email = "",
            friendTime = relationInfo?.friendTime?:0L,
            userType = user.userType,
            muteMessages = relationInfo?.muteMessages,
            muteNotification = relationInfo?.muteNotification,
            userStatus = user.userStatus,
        )
    }

    fun toSimpleBuzUser(useFullName: Boolean): SimpleBuzUser {
        return SimpleBuzUser(
            userId = user.userId,
            displayName = if (useFullName) fullNickName else firstNickName,
            portrait = user.portrait,
            buzId = if (!user.buzId.isNullOrEmpty()) R.string.common_symbol_at.asString() + user.buzId else ""
        )
    }

}