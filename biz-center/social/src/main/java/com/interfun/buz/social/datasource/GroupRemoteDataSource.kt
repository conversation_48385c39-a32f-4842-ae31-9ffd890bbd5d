package com.interfun.buz.social.datasource

import com.buz.idl.group.bean.GroupInfo
import com.buz.idl.group.response.ResponseCreateGroup
import com.buz.idl.group.response.ResponseJoinGroup
import com.interfun.buz.common.bean.Resp

internal interface GroupRemoteDataSource {
    suspend fun createGroup(groupName: String?, invitedUserIds: List<Long>?, portraitUploadId: Long?): Resp<ResponseCreateGroup?>
    suspend fun joinGroup(groupId: Long, inviterId: Long?) : Resp<ResponseJoinGroup?>
    suspend fun quitGroup(groupId: Long) : Resp<Unit>
    suspend fun reportGroup(groupId: Long) : Resp<Unit>
    suspend fun requestGroup(groupId: Long): Resp<GroupInfo?>
    suspend fun requestGroupList(groupIds: List<Long>): Resp<List<GroupInfo>?>
    suspend fun getJoinedGroups(): Resp<List<GroupInfo>?>
    /**
     * @param muteNotification see [com.interfun.buz.common.bean.chat.MuteType]
     * @param muteMessages see [com.interfun.buz.common.bean.chat.MuteType]
     */
    suspend fun updateSetting(
        groupId: Long,
        muteMessages: Int? = null,
        muteNotification: Int? = null
    ): Resp<Boolean>

    suspend fun updateGroupInfo(
        groupId: Long,
        name: String? = null,
        uploadId: Long? = null
    ): Resp<GroupInfo?>

}