package com.interfun.buz.social.di

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.PreferenceDataStoreFactory
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.preferencesDataStoreFile
import com.buz.idl.bot.service.BuzNetBotService
import com.buz.idl.bot.service.BuzNetBotServiceClient
import com.buz.idl.common.service.BuzNetCommonService
import com.buz.idl.common.service.BuzNetCommonServiceClient
import com.buz.idl.group.service.BuzNetGroupService
import com.buz.idl.group.service.BuzNetGroupServiceClient
import com.buz.idl.user.service.BuzNetUserService
import com.interfun.buz.common.net.newInstanceBuzNetUserServiceClient
import com.interfun.buz.common.net.withConfig
import com.interfun.buz.component.hilt.*
import com.interfun.buz.social.SocialBizCenterUserEntry
import com.interfun.buz.social.SocialBizCenterUserEntryImpl
import com.interfun.buz.social.datasource.*
import com.interfun.buz.social.db.dao.GroupUserMemberDao
import com.interfun.buz.social.datasource.UserLocalDataSource
import com.interfun.buz.social.db.dao.GroupDao
import com.interfun.buz.social.db.dao.ShareLinkDao
import com.interfun.buz.social.db.SocialDatabase
import com.interfun.buz.social.repo.GroupMembersRepository
import com.interfun.buz.social.repo.GroupMembersRepositoryImpl
import com.interfun.buz.social.repo.ShareLinkRepository
import com.interfun.buz.social.repo.ShareLinkRepositoryImpl
import com.interfun.buz.social.repo.UserRepository
import com.interfun.buz.social.db.dao.BotDao
import com.interfun.buz.social.db.dao.OfficialAccountDao
import com.interfun.buz.social.db.dao.UserDao
import com.interfun.buz.social.repo.*
import com.interfun.buz.social.repo.BotRepositoryImpl
import com.interfun.buz.social.repo.SearchFriendRepository
import com.interfun.buz.social.repo.UserRepositoryImpl
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.EntryPoint
import dagger.hilt.EntryPoints
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent

@EntryPoint
@InstallIn(UserComponent::class)
internal interface SocialEntryPoint {

    @UserQualifier
    fun groupRepository(): GroupRepository

    @UserQualifier
    fun userRepository(): UserRepository

    @UserQualifier
    fun getSocialBizCenterUserEntry(): SocialBizCenterUserEntry

    @UserQualifier
    fun getOfficialAccountRepository(): OfficialAccountRepository

    @UserQualifier
    fun getBotRepository(): BotRepository

    @UserQualifier
    fun getUserOnlineStatusRepository(): UserOnlineStatusRepository

    @UserQualifier
    fun groupMembersRepository(): GroupMembersRepository

    @UserQualifier
    fun shareLinkRepository(): ShareLinkRepository

    @UserQualifier
    fun getGroupOnlineMembersRepository(): GroupOnlineMembersRepository

    @UserQualifier
    fun searchFriendRepository(): SearchFriendRepository
}

@EntryPoint
@InstallIn(SingletonComponent::class)
internal interface SocialSingletonEntryPoint {
    fun getUserComponentManger(): UserComponentManager
    fun userRepository(): UserRepository
    fun groupRepository(): GroupRepository
    fun getBotRepository(): BotRepository
    fun getGroupMembersRepository(): GroupMembersRepository
    fun getUserOnlineStatusRepository(): UserOnlineStatusRepository
    fun getOfficialAccountRepository(): OfficialAccountRepository
    fun searchFriendRepository(): SearchFriendRepository
}

@Module
@InstallIn(UserComponent::class)
internal abstract class SocialUserModule {

    companion object {

        @Provides
        @UserQualifier
        fun provideGroupDao(@UserQualifier socialDatabase: SocialDatabase): GroupDao {
            return socialDatabase.getGroupDao()
        }

        /**
         * 数据库属于UserComponent，且由于不需要暴露给外部，一定是User相关的Repository/DataSource使用，
         * 所以只需要定义在UserComponent即可，不需要定义在SingletonComponent给外部
         */
        @Provides
        @UserQualifier
        fun provideUserDao(@UserQualifier socialDatabase: SocialDatabase): UserDao {
            return socialDatabase.getUserDao()
        }

        @Provides
        @UserQualifier
        fun provideGroupUserMemberDao(@UserQualifier socialDatabase: SocialDatabase): GroupUserMemberDao {
            return socialDatabase.getGroupUserMemberDao()
        }

        @Provides
        @UserQualifier
        fun provideShareLinkDao(@UserQualifier socialDatabase: SocialDatabase): ShareLinkDao {
            return socialDatabase.getShareLinkDao()
        }

        @Provides
        @UserQualifier
        fun provideBotDao(@UserQualifier socialDatabase: SocialDatabase): BotDao {
            return socialDatabase.getBotDao()
        }

        @Provides
        @UserQualifier
        fun provideOADao(@UserQualifier socialDatabase: SocialDatabase): OfficialAccountDao {
            return socialDatabase.getOfficialDao()
        }

        @Provides
        @UserQualifier
        fun provideBuzNetUserService(): BuzNetUserService {
            return newInstanceBuzNetUserServiceClient()
        }

        @Provides
        @SocialUserQualifier
        fun provideCommonNetService(): BuzNetCommonService {
            return BuzNetCommonServiceClient().withConfig()
        }

        @Provides
        @UserQualifier
        fun provideBuzNetBotService(): BuzNetBotService {
            return BuzNetBotServiceClient().withConfig()
        }

        @Provides
        @UserQualifier
        fun provideBuzNetGroupService(): BuzNetGroupService {
            return BuzNetGroupServiceClient().withConfig()
        }


        @Provides
        @SocialUserQualifier
        @UserScope
        fun provideSocialDatastore(
            @ApplicationContext appContext: Context,
            @UserQualifier userAuth: UserAuth
        ): DataStore<Preferences> {
            return PreferenceDataStoreFactory.create(scope = userAuth.userScope) {
                appContext.preferencesDataStoreFile("social_preferences_${userAuth.userId}")
            }
        }
    }

    @Binds
    @UserQualifier
    abstract fun provideSocialEntry(entry: SocialBizCenterUserEntryImpl): SocialBizCenterUserEntry

    @Binds
    @UserQualifier
    @UserScope
    abstract fun provideUserRepository(userRepositoryImpl: UserRepositoryImpl): UserRepository

    @Binds
    @UserQualifier
    @UserScope
    abstract fun provideUserLocalDataSource(userLocalDataSourceImpl: UserLocalDataSourceImpl): UserLocalDataSource

    @Binds
    @UserQualifier
    @UserScope
    abstract fun provideUserRemoteDataSource(userRemoteDataSourceImpl: UserRemoteDataSourceImpl): UserRemoteDataSource

    @Binds
    @UserQualifier
    @UserScope
    abstract fun provideOALocalDataSource(oaLocalDataSourceImpl: OfficialAccountLocalDataSourceImpl): OfficialAccountLocalDataSource

    @Binds
    @UserQualifier
    @UserScope
    abstract fun provideOARemoteDataSource(oaRemoteDataSourceImpl: OfficialAccountRemoteDataSourceImpl): OfficialAccountRemoteDataSource

    @Binds
    @UserQualifier
    @UserScope
    abstract fun provideOARepository(oaRepositoryImpl: OfficialAccountRepositoryImpl): OfficialAccountRepository

    @Binds
    @UserQualifier
    @UserScope
    abstract fun provideBotLocalDataSource(localDS: BotLocalDataSourceImpl): BotLocalDataSource

    @Binds
    @UserQualifier
    @UserScope
    abstract fun provideBotRemoteDataSource(remoteDS: BotRemoteDataSourceImpl): BotRemoteDataSource

    @Binds
    @UserQualifier
    @UserScope
    abstract fun provideBotRepository(repo: BotRepositoryImpl): BotRepository

    @Binds
    @UserQualifier
    @UserScope
    abstract fun provideGroupLocalDataSource(groupLocalDS: GroupLocalDataSourceImpl): GroupLocalDataSource

    @Binds
    @UserQualifier
    @UserScope
    abstract fun provideGroupRemoteDataSource(groupRemoteDS: GroupRemoteDataSourceImpl): GroupRemoteDataSource

    @Binds
    @UserQualifier
    @UserScope
    abstract fun provideGroupRepository(groupRepository: GroupRepositoryImpl): GroupRepository

    @Binds
    @UserQualifier
    @UserScope
    abstract fun provideUserOnlineStatusRepository(repo: UserOnlineStatusRepositoryImpl): UserOnlineStatusRepository

    @Binds
    @UserQualifier
    @UserScope
    abstract fun provideGroupMembersRepository(groupMembersRepositoryImpl: GroupMembersRepositoryImpl): GroupMembersRepository

    @Binds
    @UserQualifier
    @UserScope
    abstract fun provideGroupMembersLocalDataSource(localGroupMembersDataSourceImpl: GroupMembersLocalDataSourceImpl): GroupMembersLocalDataSource

    @Binds
    @UserQualifier
    @UserScope
    abstract fun provideGroupMembersRemoteDataSource(remoteGroupMembersDataSourceImpl: GroupMembersRemoteDataSourceImpl): GroupMembersRemoteDataSource

    @Binds
    @UserQualifier
    @UserScope
    abstract fun provideShareLinkLocalDataSource(shareLinkLocalDataSource: LocalShareLinkDataSourceImpl): LocalShareLinkDataSource

    @Binds
    @UserQualifier
    @UserScope
    abstract fun provideShareLinkRemoteDataSource(shareLinkRemoteDataSource: RemoteShareLinkDataSourceImp): RemoteShareLinkDataSource

    @Binds
    @UserQualifier
    @UserScope
    abstract fun provideShareLinkRepository(shareLinkRepositoryImpl: ShareLinkRepositoryImpl): ShareLinkRepository

    @Binds
    @UserQualifier
    @UserScope
    abstract fun provideGroupOnlineMembersRepository(impl: GroupOnlineMembersRepositoryImpl): GroupOnlineMembersRepository

    @Binds
    @UserQualifier
    abstract fun provideSearchFriendRepository(impl: SearchFriendRepositoryImpl): SearchFriendRepository
}

@Module
@InstallIn(SingletonComponent::class)
internal object SocialModule {

    @Provides
    fun provideGroupRepository(userComponent: UserComponent): GroupRepository {
        return EntryPoints.get(userComponent, SocialEntryPoint::class.java).groupRepository()
    }

    @Provides
    fun provideUserRepository(userComponent: UserComponent): UserRepository {
        return EntryPoints.get(userComponent, SocialEntryPoint::class.java).userRepository()
    }


    @Provides
    fun provideOARepository(userComponent: UserComponent): OfficialAccountRepository {
        return EntryPoints.get(userComponent, SocialEntryPoint::class.java)
            .getOfficialAccountRepository()
    }

    @Provides
    fun provideBotRepository(userComponent: UserComponent): BotRepository {
        return EntryPoints.get(userComponent, SocialEntryPoint::class.java).getBotRepository()
    }

    @Provides
    fun provideUserOnlineStatusRepository(userComponent: UserComponent): UserOnlineStatusRepository {
        return EntryPoints.get(userComponent, SocialEntryPoint::class.java)
            .getUserOnlineStatusRepository()
    }

    @Provides
    fun provideGroupMembersRepository(userComponent: UserComponent): GroupMembersRepository {
        return EntryPoints.get(userComponent, SocialEntryPoint::class.java).groupMembersRepository()
    }

    @Provides
    fun provideShareLinkRepository(userComponent: UserComponent): ShareLinkRepository {
        return EntryPoints.get(userComponent, SocialEntryPoint::class.java).shareLinkRepository()
    }

    @Provides
    fun provideGroupOnlineMembersRepository(userComponent: UserComponent): GroupOnlineMembersRepository {
        return EntryPoints.get(userComponent, SocialEntryPoint::class.java).getGroupOnlineMembersRepository()
    }

    @Provides
    fun provideSearchFriendRepository(userComponent: UserComponent): SearchFriendRepository {
        return EntryPoints.get(userComponent, SocialEntryPoint::class.java).searchFriendRepository()
    }
}