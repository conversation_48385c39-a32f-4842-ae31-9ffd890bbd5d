package com.interfun.buz.social.repo

import com.interfun.buz.social.entity.SelectableUserList
import kotlinx.coroutines.flow.Flow

/**
 * Author: ChenYouSheng
 * Date: 2025/7/9
 * Email: chen<PERSON><PERSON><PERSON>@vocalbeats.com
 * Desc:
 */
interface SearchFriendRepository {
    suspend fun searchFriend(keyword: String)
    fun selectFriend(userId: Long)
    fun unSelectFriend(userId: Long)
    fun unSelectAllFriend()
    fun getSelectableFriendListFlow(): Flow<SelectableUserList>
}