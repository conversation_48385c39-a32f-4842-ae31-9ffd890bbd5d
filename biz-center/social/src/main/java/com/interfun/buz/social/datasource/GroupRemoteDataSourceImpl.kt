package com.interfun.buz.social.datasource

import com.buz.idl.group.bean.GroupInfo
import com.buz.idl.group.request.*
import com.buz.idl.group.response.ResponseCreateGroup
import com.buz.idl.group.response.ResponseJoinGroup
import com.buz.idl.group.service.BuzNetGroupService
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.common.bean.Resp
import com.interfun.buz.common.ktx.isSuccess
import com.interfun.buz.component.hilt.UserQualifier
import javax.inject.Inject

internal class GroupRemoteDataSourceImpl @Inject constructor(
    @UserQualifier private val groupNetService: BuzNetGroupService
) : GroupRemoteDataSource {

    private val TAG = "GroupRemoteDataSource"

    override suspend fun createGroup(
        groupName: String?,
        invitedUserIds: List<Long>?,
        portraitUploadId: Long?
    ): Resp<ResponseCreateGroup?> {
        logInfo(TAG,"createGroup:$groupName")
        val serverResp = groupNetService.createGroup(RequestCreateGroup(groupName, invitedUserIds, portraitUploadId))
        logInfo(TAG,"createGroup end,result:${serverResp.code}")
        return if (serverResp.isSuccess) {
            Resp.Success(serverResp.data, serverResp.data?.prompt)
        } else {
            Resp.Error(serverResp.code, serverResp.msg, serverResp.data?.prompt)
        }
    }

    override suspend fun joinGroup(groupId: Long, inviterId: Long?): Resp<ResponseJoinGroup?> {
        logInfo(TAG,"joinGroup: groupId:$groupId, inviterId:$inviterId")
        val serverResp = groupNetService.joinGroup(RequestJoinGroup(groupId, inviterId))
        logInfo(TAG,"joinGroup end,result:${serverResp.code}")
        return if (serverResp.isSuccess) {
            Resp.Success(serverResp.data, serverResp.data?.prompt)
        } else {
            Resp.Error(serverResp.code, serverResp.msg, serverResp.data?.prompt)
        }
    }

    override suspend fun quitGroup(groupId: Long): Resp<Unit> {
        logInfo(TAG,"quitGroup: groupId:$groupId")
        val serverResp = groupNetService.quitGroup(RequestQuitGroup(groupId))
        logInfo(TAG,"quitGroup end,result:${serverResp.code}")
        return if (serverResp.isSuccess) {
            Resp.Success(Unit, serverResp.data?.prompt)
        } else {
            Resp.Error(serverResp.code, serverResp.msg, serverResp.data?.prompt)
        }
    }

    override suspend fun reportGroup(groupId: Long): Resp<Unit> {
        logInfo(TAG,"reportGroup: groupId:$groupId")
        val serverResp = groupNetService.reportGroup(RequestReportGroup(groupId))
        logInfo(TAG,"reportGroup end,result:${serverResp.code}")
        return if (serverResp.isSuccess) {
            Resp.Success(Unit, serverResp.data?.prompt)
        } else {
            Resp.Error(serverResp.code, serverResp.msg, serverResp.data?.prompt)
        }
    }

    override suspend fun requestGroup(groupId: Long): Resp<GroupInfo?> {
        logInfo(TAG,"requestGroupComposite: groupId:$groupId")
        val serverResp = groupNetService.getGroupInfo(RequestGetGroupInfo(groupId))
        logInfo(TAG,"requestGroupComposite end,result:${serverResp.code}")
        return if (serverResp.isSuccess) {
            Resp.Success(serverResp.data?.groupInfo, serverResp.data?.prompt)
        } else {
            Resp.Error(serverResp.code, serverResp.msg, serverResp.data?.prompt)
        }
    }

    override suspend fun requestGroupList(groupIds: List<Long>): Resp<List<GroupInfo>?> {
        logInfo(TAG,"requestGroupList: groupIds:$groupIds")
        val serverResp = groupNetService.getGroupInfoBatch(RequestGetGroupInfoBatch(groupIds))
        logInfo(TAG,"requestGroupList end,result:${serverResp.code}")
        return if (serverResp.isSuccess) {
            Resp.Success(serverResp.data?.groupInfoList, serverResp.data?.prompt)
        } else {
            Resp.Error(serverResp.code, serverResp.msg, serverResp.data?.prompt)
        }
    }

    override suspend fun getJoinedGroups(): Resp<List<GroupInfo>?> {
        logInfo(TAG,"getJoinedGroups")
        val serverResp = groupNetService.getJoinedGroups(RequestGetJoinedGroups())
        logInfo(TAG,"getJoinedGroups end,result:${serverResp.code}")
        return if (serverResp.isSuccess) {
            Resp.Success(serverResp.data?.groupInfoList, serverResp.data?.prompt)
        } else {
            Resp.Error(serverResp.code, serverResp.msg, serverResp.data?.prompt)
        }
    }

    override suspend fun updateSetting(
        groupId: Long,
        muteMessages: Int?,
        muteNotification: Int?
    ): Resp<Boolean> {
        logInfo(TAG,"updateSetting: groupId:$groupId, muteMessages:$muteMessages, muteNotification:$muteNotification")
        val serverResp = groupNetService.updateGroupMemberConfig(
            RequestUpdateGroupMemberConfig(
                groupId,
                muteMessages,
                muteNotification
            )
        )
        return if (serverResp.isSuccess) {
            Resp.Success(true, serverResp.data?.prompt)
        } else {
            Resp.Error(serverResp.code, serverResp.msg, serverResp.data?.prompt)
        }
    }

    override suspend fun updateGroupInfo(
        groupId: Long,
        name: String?,
        uploadId: Long?
    ): Resp<GroupInfo?> {
        val serverResp = groupNetService.updateGroupInfo(
            RequestUpdateGroupInfo(
                groupId,
                name,
                uploadId
            )
        )
        return if (serverResp.isSuccess) {
            Resp.Success(serverResp.data?.groupInfo, serverResp.data?.prompt)
        } else {
            Resp.Error(serverResp.code, serverResp.msg, serverResp.data?.prompt)
        }
    }
} 