package com.interfun.buz.social

import com.interfun.buz.base.ktx.log
import com.interfun.buz.component.hilt.UserQualifier
import com.interfun.buz.social.repo.UserOnlineStatusRepository
import com.interfun.buz.social.repo.GroupMembersRepository
import com.interfun.buz.social.repo.GroupRepository
import com.interfun.buz.social.repo.UserRepository
import javax.inject.Inject

internal class SocialBizCenterUserEntryImpl @Inject constructor(
    @UserQualifier val userRepository: UserRepository,
    @UserQualifier val groupMembersRepository: GroupMembersRepository,
    @UserQualifier val userStatusRepository: UserOnlineStatusRepository,
    @UserQualifier val groupRepository: GroupRepository,
) : SocialBizCenterUserEntry {
    companion object {
        private const val TAG = "SocialBizCenterUserEntry"
    }

    override fun init() {
        log(TAG,"init")
        userRepository.init()
        groupMembersRepository.init()
        userStatusRepository.init()
        groupRepository.init()
    }
}