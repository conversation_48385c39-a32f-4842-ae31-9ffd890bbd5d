package com.interfun.buz.social.repo

import androidx.lifecycle.Lifecycle
import com.interfun.buz.base.ktx.collectInScope
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.common.bean.Resp
import com.interfun.buz.common.bean.Resp.Error
import com.interfun.buz.common.bean.Resp.Success
import com.interfun.buz.common.bean.push.PushBusinessType
import com.interfun.buz.common.di.NetworkStatus
import com.interfun.buz.common.eventbus.FriendsSyncCompleteEvent
import com.interfun.buz.common.ktx.codeRequestSuccess
import com.interfun.buz.common.net.dispatcher.DefaultRetryableDispatchInterval
import com.interfun.buz.common.net.dispatcher.MergedRequestDispatcher
import com.interfun.buz.common.net.dispatcher.Retryable
import com.interfun.buz.component.hilt.GlobalQualifier
import com.interfun.buz.component.hilt.UserQualifier
import com.interfun.buz.signal.ISignalManagerPresenter
import com.interfun.buz.signal.getProtocolDataChangeFlow
import com.interfun.buz.social.datasource.OfficialAccountLocalDataSource
import com.interfun.buz.social.datasource.OfficialAccountRemoteDataSource
import com.interfun.buz.social.db.entity.BuzOfficialAccountExtra
import com.interfun.buz.social.db.entity.BuzOfficialAccountInfo
import com.interfun.buz.social.db.entity.BuzUserComposite
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.*
import javax.inject.Inject

internal class OfficialAccountRepositoryImpl @Inject constructor(
    @UserQualifier private val oaLocalDS: OfficialAccountLocalDataSource,
    @UserQualifier private val oaRemoteDS: OfficialAccountRemoteDataSource,
    @UserQualifier private val userRepository: UserRepository,
    @UserQualifier private val userScope: CoroutineScope,
    @GlobalQualifier private val networkStatus: NetworkStatus,
    @GlobalQualifier private val appLifecycle: Lifecycle,
    private val signalRepository : ISignalManagerPresenter
) : OfficialAccountRepository {

    private val TAG = "OfficialAccountRepository"

    private val syncDispatcher = RequestDispatcher("OfficialRequestDispatcher") { mergedList ->
        when (mergedList.size) {
            0 -> true
            1 -> {
                val resp = loadOfficialAccountInfoFromServer(mergedList.first())
                if (resp is Resp.Error && resp.code?.codeRequestSuccess == false) {
                    syncOAAccountInfoInternal(mergedList, true)
                    false
                }else  {
                    true
                }
            }

            else -> {
                val resp = loadOfficialAccountInfoFromServer(mergedList)
                if (resp is Resp.Error && resp.code?.codeRequestSuccess == false) {
                    syncOAAccountInfoInternal(mergedList, true)
                    false
                } else {
                    true
                }
            }
        }
    }
    private val syncAllOAAccounts = MergedRequestDispatcher<Retryable<Unit>>(
        scope = userScope,
        dispatchInterval = DefaultRetryableDispatchInterval(networkStatus, appLifecycle),
        debugTag = "syncAllOAFriendsDispatcher",
        onUndeliveredElement = null,
        requestHandler = { _ ->
            val resp = loadAllOAFriendFromServer()
            if (resp is Resp.Error && resp.code?.codeRequestSuccess == false) {
                syncAllOAAccountsInternal(true)
                false
            }else {
                true
            }
        }
    )

    override fun init() {
        observerOAAccountListChanged()
    }

    private fun observerOAAccountListChanged(){
        signalRepository.getProtocolDataChangeFlow()
            .filter { it.businessType == PushBusinessType.OFFICIAL_ACCOUNT_INFO_CHANGE.type }
            .collectInScope(userScope){
                logInfo(TAG,"OAAccountListChanged from signal:OFFICIAL_ACCOUNT_INFO_CHANGE")
                syncAllOAAccountsInternal()
            }
    }

    override suspend fun getOAInfo(userId: Long): BuzOfficialAccountInfo? {
        // 先尝试获取本地数据
        return getOAInfoFromCache(userId)
            ?: (loadOfficialAccountInfoFromServer(userId) as? Resp.Success)?.data
    }

    override fun getOAInfoFlow(userId: Long, isForceRefresh: Boolean): Flow<BuzOfficialAccountInfo?> = flow {
        var isSync = false
        if (isForceRefresh) {
            syncOAAccountInfo(arrayListOf(userId))
            isSync = true
        }
        getOAInfoFromCacheFlow(userId).collect { oaInfo ->
            if (!isSync && (oaInfo == null || oaInfo.buzRelation == null || oaInfo.extra == null)) {
                syncOAAccountInfo(arrayListOf(userId))
                isSync = true
            }
            emit(oaInfo)
        }
    }


    override fun getOAInfoFromCacheFlow(userId: Long): Flow<BuzOfficialAccountInfo?> {
        return combine(
            // 使用getUserCompositeFromCacheFlow而不是getUserFlow，避免触发网络请求
            userRepository.getUserCompositeFromCacheFlow(userId),
            getOAExtraFromCacheFlow(userId)
        ) { userComposite, extra ->
            if (userComposite != null) {
                BuzOfficialAccountInfo(userComposite.user, userComposite.relationInfo, extra)
            } else {
                null
            }
        }.distinctUntilChanged()
    }

    override fun getOAExtraFromCacheFlow(userId: Long): Flow<BuzOfficialAccountExtra?> {
        return oaLocalDS.getOfficialAccountExtraFlow(userId)
    }

    override suspend fun getOAInfoFromServer(userId: Long): Resp<BuzOfficialAccountInfo?> {
        return loadOfficialAccountInfoFromServer(userId)
    }

    override fun syncOAAccountInfo(userId: List<Long>) {
        syncOAAccountInfoInternal(userId)
    }

    private fun syncOAAccountInfoInternal(userId: List<Long>, isRetry: Boolean = false) {
        syncDispatcher.request(Retryable(userId, isRetry))
    }

    override fun syncAllOAAccounts() {
        syncAllOAAccountsInternal()
    }

    override suspend fun getAllMyOfficialAccountsFromCache(): List<BuzUserComposite> {
        val ids = oaLocalDS.getAllMyOfficialAccountIds()
        return userRepository.getUserCompositeFromCache(ids).filterNotNull()
    }

    private fun syncAllOAAccountsInternal(isRetry: Boolean = false) {
        syncAllOAAccounts.request(Retryable(Unit, isRetry))
    }

    override suspend fun getOAInfoFromCache(userId: Long): BuzOfficialAccountInfo? {
        val userComposite = userRepository.getUserCompositeFromCache(userId) ?: return null
        val extra = oaLocalDS.getOfficialAccountExtra(userId)
        return BuzOfficialAccountInfo(userComposite.user, userComposite.relationInfo, extra)
    }

    override suspend fun getOAExtra(userId: Long): BuzOfficialAccountExtra? {
        return oaLocalDS.getOfficialAccountExtra(userId)
    }

    override fun getOAExtraFlow(userId: Long, forceRefresh: Boolean): Flow<BuzOfficialAccountExtra?> = flow {
        var isSync = false
        if (forceRefresh){
            syncOAAccountInfo(arrayListOf(userId))
            isSync = true
        }
        getOAExtraFromCacheFlow(userId).collect { extra ->
            if (!isSync && extra == null) {
                syncOAAccountInfo(arrayListOf(userId))
                isSync = true
            }
            emit(extra)
        }
    }

    override suspend fun updateOAExtra(extra: BuzOfficialAccountExtra) {
        oaLocalDS.updateOfficialAccountExtra(extra)
    }

    private suspend fun loadAllOAFriendFromServer(): Resp<List<BuzOfficialAccountInfo>?> {
        val resp = oaRemoteDS.getAllOfficialFriends()
        return when (resp) {
            is Success -> {
                val list = resp.data?.map {
                    val userComposite = userRepository.updateUserAndRelation(it.userRelationInfo)
                    val oaExtra = oaLocalDS.updateOfficialAccountExtra(
                        BuzOfficialAccountExtra(
                            userComposite.user.userId,
                            it.description,
                            it.shortDescription
                        )
                    )
                    BuzOfficialAccountInfo(userComposite.user, userComposite.relationInfo, oaExtra)
                }
                oaLocalDS.replaceAllMyOfficialAccounts(list?.map { it.user.userId } ?: emptyList())
                //为了兼容旧代码
                FriendsSyncCompleteEvent.post()
                Resp.Success(list)
            }

            is Error -> {
                Resp.Error(resp.code, resp.msg)
            }
        }
    }

    private suspend fun loadOfficialAccountInfoFromServer(userIds: List<Long>): Resp<List<BuzOfficialAccountInfo>?> {
        val resp = oaRemoteDS.getOfficialAccountInfo(userIds)
        return when (resp) {
            is Success -> {
                val list = resp.data?.map {
                    val userComposite = userRepository.updateUserAndRelation(it.userRelationInfo)
                    val oaExtra = oaLocalDS.updateOfficialAccountExtra(
                        BuzOfficialAccountExtra(
                            userComposite.user.userId,
                            it.description,
                            it.shortDescription
                        )
                    )
                    BuzOfficialAccountInfo(userComposite.user, userComposite.relationInfo, oaExtra)
                }
                Resp.Success(list)
            }

            is Error -> {
                Resp.Error(resp.code, resp.msg)
            }
        }
    }

    private suspend fun loadOfficialAccountInfoFromServer(userId: Long): Resp<BuzOfficialAccountInfo?> {
        val resp = oaRemoteDS.getOfficialAccountInfo(userId)
        return when (resp) {
            is Success -> {
                val result = resp.data?.let {
                    val userComposite = userRepository.updateUserAndRelation(it.userRelationInfo)
                    val oaExtra = oaLocalDS.updateOfficialAccountExtra(
                        BuzOfficialAccountExtra(
                            userComposite.user.userId,
                            it.description,
                            it.shortDescription
                        )
                    )
                    BuzOfficialAccountInfo(userComposite.user, userComposite.relationInfo, oaExtra)
                }
                Resp.Success(result)
            }

            is Error -> {
                Resp.Error(resp.code, resp.msg)
            }
        }
    }

    private fun RequestDispatcher(
        debugTag: String,
        handler: suspend (List<Long>) -> Boolean
    ) = MergedRequestDispatcher<Retryable<List<Long>>>(
        scope = userScope,
        dispatchInterval = DefaultRetryableDispatchInterval(networkStatus, appLifecycle),
        debugTag = debugTag,
        onUndeliveredElement = null,
        requestHandler = { requestList ->
            val userIds = requestList.flatMapTo(hashSetOf()) { it.data }
            handler(userIds.toList())
        }
    )
} 