package com.interfun.buz.social.db.entity

import android.os.Parcelable
import androidx.room.ColumnInfo
import androidx.room.Entity
import kotlinx.parcelize.Parcelize

/**
 * ！！！注意 ！！！
 * ！！！注意 ！！！
 * ！！！注意 ！！！
 * 所有字段请都添加defaultValue，方便以后做部分内容更新可以直接使用upsert
 */
@Parcelize
@Entity("buz_official_account_extra", primaryKeys = ["userId"])
data class BuzOfficialAccountExtra(
    @ColumnInfo(name = "userId")
    val userId: Long,
    @ColumnInfo(name = "description", defaultValue = "")
    val description: String? = "",
    @ColumnInfo(name = "shortDescription", defaultValue = "")
    val shortDescription: String? = ""
    /**
     * ！！！注意 ！！！
     * ！！！注意 ！！！
     * ！！！注意 ！！！
     * 所有字段请都添加defaultValue，方便以后做部分内容更新可以直接使用upsert
     */
) : Parcelable