package com.interfun.buz.social.repo

import com.buz.idl.user.bean.UserInfo
import com.interfun.buz.social.entity.UserOnlineStatus
import com.interfun.buz.social.entity.UserStateInfo
import kotlinx.coroutines.flow.Flow

interface UserOnlineStatusRepository {
    fun init()
    fun getStatusInfo(userId: Long): UserOnlineStatus?
    fun getStatusInfoFlow(userId: Long): Flow<UserOnlineStatus?>
    fun getStatusInfoFlow(userIds: List<Long>): Flow<Map<Long,UserOnlineStatus>>
    fun getOnlineTime(userId: Long): Long?
    fun isOnline(userId: Long): Boolean
    fun isOnlineFlow(userId: Long): Flow<Boolean>
    fun isInQuietMode(userId: Long): Boolean
    fun isInQuietModeFlow(userId: Long): Flow<Boolean>
    suspend fun updateStatus(userInfo: UserInfo?)
    suspend fun updateStatus(userInfos: List<UserInfo>)
    suspend fun clearStatus(userId: Long)
    @Deprecated("为了兼容旧版本")
    fun getUpdateFlow(): Flow<Set<Long>>
    fun getTotalOnlineFriendNum(): Int
    fun getUserStateInfoFlow(userId: Long): Flow<UserStateInfo?>
    fun getUserStateInfoFlow(userIds: List<Long>): Flow<Map<Long,UserStateInfo>>
}