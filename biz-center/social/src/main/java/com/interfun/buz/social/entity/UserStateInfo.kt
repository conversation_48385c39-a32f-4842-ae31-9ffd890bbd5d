package com.interfun.buz.social.entity

import java.time.Duration
import java.time.Instant

/**
 * Author: Chen<PERSON>ouSheng
 * Date: 2025/7/22
 * Email: chen<PERSON><PERSON><PERSON>@vocalbeats.com
 * Desc: 用户状态信息
 */
sealed interface UserStateInfo {
    val userId: Long
}

/**
 * 在线状态：
 *   - 在线自动播放显示为 Active (Autoplay)
 *   - 在线静音模式显示为 Active (Quiet)
 */
sealed interface OnlineState : UserStateInfo {
    data class AutoPlay(override val userId: Long) : OnlineState
    data class Quiet(override val userId: Long) : OnlineState
}

/**
 * 离线状态：
 *   - 60分钟内（不包含60分钟）：显示 Active X minute(s) ago
 *   - 1-8小时内（包含8小时）：显示 Active X hour(s) ago
 *   - 8-24小时（不包含8小时，包含24小时）：显示 Active within a day
 *   - 24小时-48小时（包含48小时）：显示 Active yesterday // “前一天上线”
 *   - 超过48小时，不显示活跃表达
 */
sealed interface OfflineState : UserStateInfo {
    data class LessThan60Minute(override val userId: Long, val minutes: Int) : OfflineState
    data class LessThan8Hour(override val userId: Long, val hours: Int) : OfflineState
    data class WithinADay(override val userId: Long) : OfflineState
    data class Yesterday(override val userId: Long) : OfflineState
    data class MoreThan48Hour(override val userId: Long) : OfflineState
}

/**
 * 转换用户在线/离线状态为用户状态信息
 */
fun UserOnlineStatus.convertStatusToState(): UserStateInfo {
    return if (isOnline) {
        if (isInQuietMode) {
            OnlineState.Quiet(userId)
        } else {
            OnlineState.AutoPlay(userId)
        }
    } else {
        val now = Instant.now()
        // todo: change mock data
        val lastOfflineMillis = Instant.ofEpochMilli(mockOfflineTime())
        val duration = Duration.between(lastOfflineMillis, now)
        val minutes = duration.toMinutes()
        val hours = duration.toHours()

        when {
            minutes < 60 -> OfflineState.LessThan60Minute(userId, minutes.toInt())
            hours in 1..8 -> OfflineState.LessThan8Hour(userId, hours.toInt())
            hours in 9..24 -> OfflineState.WithinADay(userId)
            hours in 25..48 -> OfflineState.Yesterday(userId)
            else -> OfflineState.MoreThan48Hour(userId)
        }
    }
}

// 生成一个 介于 1 分钟 和 48 小时 之间的随机毫秒数，用于构造一个“随机的离线时间”。
fun mockOfflineTime(): Long {
    val now = Instant.now().toEpochMilli()
    val randomOffsetMillis = (60_000L..48 * 60 * 60 * 1000L).random()
    return now - randomOffsetMillis
}