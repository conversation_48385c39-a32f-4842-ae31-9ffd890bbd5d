package com.interfun.buz.social.datasource

import com.buz.idl.user.bean.OfficialAccountUserInfo
import com.buz.idl.user.request.RequestGetOfficialAccount
import com.buz.idl.user.request.RequestGetOfficialAccountList
import com.buz.idl.user.request.RequestGetOfficialAccountsBatch
import com.buz.idl.user.service.BuzNetUserService
import com.interfun.buz.common.bean.Resp
import com.interfun.buz.common.ktx.isSuccess
import com.interfun.buz.component.hilt.UserQualifier
import javax.inject.Inject

internal class OfficialAccountRemoteDataSourceImpl @Inject constructor(
    @UserQualifier private val userNetUserService: BuzNetUserService
) : OfficialAccountRemoteDataSource {

    override suspend fun getOfficialAccountInfo(userId: Long): Resp<OfficialAccountUserInfo?> {
        val resp = userNetUserService.getOfficialAccount(RequestGetOfficialAccount(userId))
        if (resp.isSuccess) {
            val data = resp.data?.officialAccountUserInfo
            return Resp.Success(data, null)
        }
        return Resp.Error(resp.code, resp.msg)
    }

    override suspend fun getOfficialAccountInfo(userId: List<Long>): Resp<List<OfficialAccountUserInfo>?> {
        val resp =
            userNetUserService.getOfficialAccountsBatch(RequestGetOfficialAccountsBatch(userId))
        if (resp.isSuccess) {
            return Resp.Success(resp.data?.officialAccountUserInfoList, null)
        } else {
            return Resp.Error(resp.code, resp.msg)
        }
    }

    override suspend fun getAllOfficialFriends(): Resp<List<OfficialAccountUserInfo>?> {
        val resp = userNetUserService.getOfficialAccountList(RequestGetOfficialAccountList())
        if (resp.isSuccess) {
            return Resp.Success(resp.data?.officialAccountList, null)
        }
        return Resp.Error(resp.code, resp.msg)
    }
} 