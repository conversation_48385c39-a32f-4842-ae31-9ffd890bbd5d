package com.interfun.buz.social.db.entity

import android.os.Parcelable
import com.interfun.buz.base.ktx.takeIfNotNullAndNotEmpty
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize

@Parcelize
data class BotInfoAndRelation(
    val botInfo: BotInfo,
    val relation: BuzUserRelationEntity?
) : Parcelable {

    @IgnoredOnParcel
    val userComposite = BuzUserComposite(botInfo.buzUser,relation)

    @IgnoredOnParcel
    val fullNickName: String by lazy {
        botInfo.buzUser.fullNickName(relation)
    }

    @IgnoredOnParcel
    val firstNickName: String by lazy {
        botInfo.buzUser.firstNickName(relation)
    }
}