package com.interfun.buz.social.db.entity

import androidx.room.Embedded
import androidx.room.Relation

/**
 * 用于查询用户关系表和用户表
 */
class BuzRelationAndUser(
    @Embedded val relationInfo: BuzUserRelationEntity,
    @Relation(parentColumn = "userId", entityColumn = "userId")
    val user: BuzUser?,
) {
    fun toBuzUserComposite(): BuzUserComposite? {
        return user?.let {
            BuzUserComposite(user = it, relationInfo = relationInfo)
        }
    }
}