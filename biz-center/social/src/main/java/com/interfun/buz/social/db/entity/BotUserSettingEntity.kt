package com.interfun.buz.social.db.entity

import android.os.Parcelable
import androidx.annotation.Keep
import androidx.room.Entity
import com.buz.idl.bot.bean.BotUserSetting
import com.interfun.buz.base.ktx.isDebug
import kotlinx.parcelize.Parcelize
import java.lang.reflect.Field
import java.util.ArrayList


/**
 * Deprecated.use BotSingleSettingEntity instead
 */
@Parcelize
@Entity(tableName = "botuser_setting_table", primaryKeys = ["botUserId"])
@Deprecated("use BotSingleSettingEntity instead")
data class BotUserSettingEntity(
    val userId: Long = -1,
    val botUserId: Long = -1,
    val languageCode: String = "",
    val voiceStyleId: Long = -1
) : Parcelable

/**
 * 除所有机器人都有的设置外，后面新增其他一些某个机器人的独有的配置，都放这个表，不用[BotUserSettingEntity]，
 * 免得列越来越多，每次升级都要改表结构
 * ！！！注意！！！
 * ！！！注意！！！
 * ！！！注意！！！
 * key 必须定义在[BotSettingKeyMap]里
 * @param botUserId userId of robot
 * @param key There is one and only one key for each setting item to map the setting.See [BotSettingKeyMap]
 * @param value Value that user have saved for the given [key]
 */
@Parcelize
@Entity(tableName = "bot_single_setting", primaryKeys = ["botUserId", "key"])
data class BotSingleSettingEntity(
    val botUserId: Long,
    val key: Int,
    val value: String
) : Parcelable

/**
 * ！！！注意！！！
 * ！！！注意！！！
 * ！！！注意！！！
 * 字段名必须跟[BotSettingKeyMap]里面的fieldName完全一致，因为会使用到反射
 * 如果新加字段，且字段不是基本类型的话，需要完善[convertToFiledValue]里面的逻辑
 */
@Parcelize
@Keep
data class BotWholeSettingEntity(
    val botUserId: Long = -1,
    val languageCode: String = "",
    val voiceStyleId: Long = -1,
    val sourceLanguage: String? = null,
    val targetLanguage: String? = null,
) : Parcelable {
    companion object{

        private val fields by lazy { BotWholeSettingEntity::class.java.declaredFields}
        private val fieldMap by lazy {
            fields.fold(hashMapOf<String,Field>()){ map, field ->
                map[field.name] = field
                field.isAccessible = true
                return@fold map
            }
        }
        fun build(botUserId: Long, block: Builder.() -> Unit): BotWholeSettingEntity {
            val builder = Builder(botUserId)
            block.invoke(builder)
            return builder.build()
        }

        internal fun updateSingleValueInternal(
            wholeEntity: BotWholeSettingEntity,
            singleEntity: BotSingleSettingEntity
        ): BotWholeSettingEntity {
            val field = fieldMap[singleEntity.fieldName]
            if (field == null && isDebug) {
                throw IllegalAccessException("cannot find a field from BotWholeSettingEntity for the given fieldName:${singleEntity.fieldName},have you registered it to RobotSettingKeyMap?")
            }
            field?.set(wholeEntity, convertToFiledValue(singleEntity.value, field))
            return wholeEntity
        }

        /**
         * 使用反射构建，注意类[BotWholeSettingEntity]描述里面的注意事项
         */
        fun build(
            botUserId: Long,
            singleEntities: List<BotSingleSettingEntity>
        ): BotWholeSettingEntity {
            val result = BotWholeSettingEntity(botUserId)
            for (entity in singleEntities) {
                val field = fieldMap[entity.fieldName]
                if (field == null && isDebug) {
                    throw IllegalAccessException("cannot find a field from BotWholeSettingEntity for the given fieldName:${entity.fieldName},have you registered it to RobotSettingKeyMap?")
                }
                field?.set(result, convertToFiledValue(entity.value, field))
            }
            return result
        }

        fun convertToFiledValue(value: String, field: Field): Any {
            val type = field.type
            if (type == Int::class.java) {
                return value.toIntOrNull() ?: -1
            }
            if (type == Long::class.java) {
                return value.toLongOrNull() ?: -1L
            }
            if (type == Double::class.java) {
                return value.toDoubleOrNull() ?: -1.0
            }
            if (type == Float::class.java) {
                return value.toFloatOrNull() ?: -1f
            }
            if (type == String::class.java) {
                return value
            }
            throw IllegalAccessException("cannot cast value of field:${field} from String to $type,have you implemented it in convertToFiledValue?")
        }
    }


    class Builder(val botUserId: Long) {
        private var languageCode: String = ""
        private var voiceStyleId: Long = -1
        private var sourceLanguage: String? = null
        private var targetLanguage: String? = null

        fun setBaseEntity(baseEntity: BotUserSettingEntity) {
            this.languageCode = baseEntity.languageCode
            this.voiceStyleId = baseEntity.voiceStyleId
        }

        fun setSourceLanguage(sourceLanguage : String){
            this.sourceLanguage = sourceLanguage
        }

        fun setTargetLanguage(targetLanguage : String){
            this.targetLanguage = targetLanguage
        }

        fun build(): BotWholeSettingEntity {
            return BotWholeSettingEntity(
                botUserId,
                this.languageCode,
                this.voiceStyleId,
                this.sourceLanguage,
                this.targetLanguage
            )
        }
    }
}

private val botServerItemFields by lazy {
    BotUserSetting::class.java.declaredFields.apply {
        forEach { it.isAccessible = true }
    }
}
fun BotUserSetting.convertDb(): List<BotSingleSettingEntity> {
    val result = ArrayList<BotSingleSettingEntity>(botServerItemFields.size)
    val botUserId = botUserId ?: return result
    for (field in botServerItemFields) {
        val entity = BotSettingKeyMap.mapToSingleSettingEntity(
            botUserId,
            field.name,
            field.get(this)?.toString() ?: ""
        )
        if (entity != null) {
            result.add(entity)
        }
    }
    return result
}

