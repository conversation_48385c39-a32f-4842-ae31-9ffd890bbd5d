package com.interfun.buz.social.datasource

import com.interfun.buz.social.db.entity.*
import kotlinx.coroutines.flow.Flow

internal interface GroupLocalDataSource {
    suspend fun getGroup(groupId: Long): BuzGroupTable?

    /**
     * @return 返回的用户数据跟查询的对应index，如果查不到，则对应的位置为null
     */
    suspend fun getGroup(groupIds: List<Long>): List<BuzGroupTable?>
    fun getGroupFlow(groupId: Long): Flow<BuzGroupTable?>
    fun getGroupFlow(groupIds: List<Long>): Flow<Map<Long, BuzGroupTable>>
    suspend fun saveGroup(group: BuzGroupTable): BuzGroupTable
    suspend fun saveGroup(groups: List<BuzGroupTable>): List<BuzGroupTable>
    suspend fun getGroupExtra(groupId: Long): BuzGroupExtra?
    @Deprecated("兼容旧代码，新代码别用")
    fun getGroupExtraNotSuspend(groupId: Long): BuzGroupExtra?
    /**
     * @return 返回的用户数据跟查询的对应index，如果查不到，则对应的位置为null
     */
    suspend fun getGroupExtra(groupIds: List<Long>): List<BuzGroupExtra?>
    fun getGroupExtraFlow(groupId: Long): Flow<BuzGroupExtra?>
    fun getGroupExtraFlow(groupIds: List<Long>): Flow<Map<Long, BuzGroupExtra>>
    suspend fun saveGroupExtra(extra: BuzGroupExtra): BuzGroupExtra
    suspend fun saveGroupExtra(extraList: List<BuzGroupExtra>): List<BuzGroupExtra>
    suspend fun saveGroupComposite(
        composite: GroupTableComposite
    ): GroupTableComposite
    suspend fun saveGroupComposite(groupComposites: List<GroupTableComposite>): List<GroupTableComposite>
    fun getGroupCompositeFlow(groupId: Long): Flow<GroupTableComposite?>
    fun getGroupCompositeFlow(groupIds: List<Long>): Flow<Map<Long, GroupTableComposite>>
    suspend fun getGroupComposite(groupId: Long): GroupTableComposite?
    @Deprecated("兼容旧代码，新代码别用")
    fun getGroupCompositeNotSuspend(groupId: Long): GroupTableComposite?
    @Deprecated("兼容旧代码，新代码别用")
    fun getGroupCompositeFromMem(groupId: Long) : GroupTableComposite?
    suspend fun getGroupComposite(groupIds: List<Long>): List<GroupTableComposite?>?
    suspend fun getAllJoinedGroups(): List<GroupTableComposite>
    fun getAllJoinedGroupsFlow(): Flow<List<GroupTableComposite>>

    suspend fun clearAllGroupTables()

    suspend fun updateQuitGroup(groupId: Long)

    suspend fun updateGroupSettings(
        groupId: Long,
        muteMessages: Int? = null,
        muteNotification: Int? = null,
        remark: String? = null
    ): BuzGroupExtra?

    suspend fun insertOrUpdateUnUploadedGroupSettings(
        groupId: Long,
        muteMessages: Int? = null,
        muteNotification: Int? = null
    )

    suspend fun deleteGroupUnUploadedSettingsIfMatched(
        groupId: Long,
        updatedMuteMessages: Int? = null,
        updatedMuteNotification: Int? = null
    )

    suspend fun getGroupUnUploadedSettings(groupId: Long): GroupUnUploadedSetting?

    suspend fun getAllGroupUnUploadedSettings(): List<GroupUnUploadedSetting>?
} 