package com.interfun.buz.social.repo

import com.buz.idl.user.bean.UserInfo
import com.buz.idl.user.bean.UserRelationInfo
import com.interfun.buz.common.bean.Resp
import com.interfun.buz.component.hilt.UserScope
import com.interfun.buz.social.db.entity.BuzUser
import com.interfun.buz.social.db.entity.BuzUserRelationEntity
import com.interfun.buz.social.db.entity.BuzUserComposite
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.Flow

interface UserRepository {

    @Deprecated("兼容旧代码用")
    fun getUserScope(): CoroutineScope

    fun init()

    //-------------------------------User info start-------------------------------
    /**
     * memory cache -> db cache -> server
     * !!!Note!!!
     * !!!Note!!!
     * !!!Note!!!
     * This method will suspend the caller until cached is not null or request server done
     */
    suspend fun getUser(userId: Long): BuzUser?

    /**
     * memory cache -> db cache -> server
     *
     * 这个跟getUserFromCacheFlow的区别是，如果本地没有数据缓存或者forceRefresh未true时，
     * 会请求刷新服务器数据
     * @param forceRefresh if force refresh from server even if local data is not null
     */
    fun getUserFlow(userId: Long, forceRefresh: Boolean = false): Flow<BuzUser>

    fun getUserFlow(userList: List<Long>): Flow<Map<Long, BuzUser>>

    /**
     * Get user info from memory cache or db cache, return null if not found
     */
    suspend fun getUserFromCache(userId: Long): BuzUser?

    suspend fun getUserFromCache(userList: List<Long>): List<BuzUser?>

    @Deprecated("不要使用这个，请使用getUserFromCache")
    fun getUserFromCacheUnSuspend(userId: Long): BuzUser?

    /**
     * 当本地数据没有时，会先emit一个null
     * 这个跟getUserFlow的区别是，如果本地没有数据，不会主动请求服务端刷新，除非其他地方刷新了缓存，这里也会收到变更
     */
    fun getUserFromCacheFlow(userId: Long): Flow<BuzUser?>

    fun getUserFromCacheFlow(userList: List<Long>): Flow<Map<Long, BuzUser>>

    /**
     * refresh user info from server and retry automatically if failed
     */
    fun syncUserInfoFromServer(userId: Long)

    fun syncUserInfoFromServer(userList: List<Long>)

    suspend fun clearUserInfo()

    suspend fun updateUser(user: List<UserInfo>): List<BuzUser>
    suspend fun updateUser(user: UserInfo): BuzUser
    suspend fun markUserAccountDeleted(userId: Long) : BuzUser?

    // --------------------------------User info end------------------------------


    // -----------------------------User relation info start-------------------------
    /**
     * memory cache -> db cache -> server
     * !!!Note!!!
     * !!!Note!!!
     * !!!Note!!!
     * This method will suspend the caller until cached is not null or request server done
     */
    suspend fun getUserRelation(userId: Long): BuzUserRelationEntity?

    suspend fun getUserRelationFromServer(userId: Long): Resp<BuzUserRelationEntity?>

    suspend fun getFriendsSizeFromCache() : Int

    fun syncBlockList()

    fun getBlockListFromCacheFlow(): Flow<List<BuzUserComposite>>

    fun getAllFriendIdsFromCacheFlow(): Flow<List<Long>>

    fun getAllFriendsFlow(forceRefresh: Boolean = false): Flow<List<BuzUserComposite>>

    suspend fun getAllFriendsFromCache(): List<BuzUserComposite>

    suspend fun getAllOfficialFriendsFromCache(): List<BuzUserComposite>

    @Deprecated("兼容旧代码，新代码别用")
    fun getAllFriendsFromCacheNotSuspend(): List<BuzUserComposite>

    fun syncAllFriends()

    /**
     * memory cache -> db cache -> server
     *
     * 这个跟getUserFromCacheFlow的区别是，如果本地没有数据缓存或者forceRefresh未true时，
     * 会请求刷新服务器数据
     */
    fun getUserRelationFlow(
        userId: Long,
        forceRefresh: Boolean = false
    ): Flow<BuzUserRelationEntity>

    fun getUserRelationFlow(userList: List<Long>): Flow<Map<Long, BuzUserRelationEntity>>

    /**
     * Get user relation info from memory cache or db cache, return null if not found
     */
    suspend fun getUserRelationFromCache(userId: Long): BuzUserRelationEntity?

    @Deprecated("为了兼容旧代码，新代码别用")
    fun getUserRelationFromCacheNotSuspend(userId: Long): BuzUserRelationEntity?

    /**
     * 当本地数据没有时，会先emit一个null
     * 这个跟getUserRelationFlow的区别是，如果本地没有数据，不会主动请求服务端刷新，除非其他地方刷新了缓存，这里也会收到变更
     */
    fun getUserRelationFromCacheFlow(userId: Long): Flow<BuzUserRelationEntity?>

    fun getUserRelationFromCacheFlow(userList: List<Long>): Flow<Map<Long, BuzUserRelationEntity>>

    /**
     * refresh user relation info from server and retry automatically if failed
     */
    fun syncUserRelationFromServer(userId: Long)


    // ----------------------User relation info end------------------------


    // ----------------------User composite info start-------------------------

    /**
     * memory cache -> db cache -> server
     * !!!Note!!!
     * !!!Note!!!
     * !!!Note!!!
     * This method will suspend the caller until cached is not null or request server done
     */
    suspend fun getUserComposite(userId: Long): BuzUserComposite?

    /**
     * memory cache -> db cache -> server
     *
     * 这个跟getUserCompositeFromCacheFlow()的区别是，如果本地没有数据缓存或者forceRefresh未true时，
     * 会请求刷新服务器数据
     */
    fun getUserCompositeFlow(userId: Long, forceRefresh: Boolean = false): Flow<BuzUserComposite>

    fun getUserCompositeFlow(userList: List<Long>): Flow<Map<Long, BuzUserComposite>>

    /**
     * Get user composite info from memory cache or db cache, return null if not found
     * Note that it will return null if BuzUserComposite.BuzUser is null but BuzUserComposite.BuzUserRelationEntity is not null
     */
    suspend fun getUserCompositeFromCache(userId: Long): BuzUserComposite?

    /**
     * @return 返回的数据跟查询的对应index，如果查不到，则对应的位置为null
     */
    suspend fun getUserCompositeFromCache(userList: List<Long>): List<BuzUserComposite?>

    @Deprecated("为了兼容旧代码，新代码别用")
    fun getUserCompositeFromCacheNotSuspend(userId: Long): BuzUserComposite?

    @Deprecated("为了兼容旧代码，新代码别用")
    fun getUserCompositeFromMemory(userId: Long): BuzUserComposite?

    /**
     * 当本地数据没有时，会先emit一个null
     * 这个跟getUserCompositeFlow的区别是，如果本地没有数据，不会主动请求服务端刷新，除非其他地方刷新了缓存，这里也会收到变更
     */
    fun getUserCompositeFromCacheFlow(userId: Long, refreshIfNull: Boolean = false): Flow<BuzUserComposite?>

    fun getUserCompositeFromCacheFlow(userList: List<Long>): Flow<Map<Long, BuzUserComposite>>

    /**
     * get user composite info from server, return null if failed
     * it will not retry automatically when failed
     */
    suspend fun getUserCompositeFromServer(userId: Long): Resp<BuzUserComposite?>

    suspend fun getUserCompositeFromServer(userList: List<Long>): Resp<List<BuzUserComposite>?>

    /**
     * refresh user composite info from server and retry automatically if failed
     */
    fun syncUserCompositeFromServer(userId: Long)

    fun syncUserCompositeFromServerIfCacheAbsent(userList: List<Long>)

    suspend fun updateUserAndRelation(userRelationInfo: UserRelationInfo): BuzUserComposite

    suspend fun updateUserAndRelation(userRelationInfo: List<UserRelationInfo>): List<BuzUserComposite>

    //--------------------------User composite info end---------------------------


    //--------------------------User Management ---------------------------

    /**
     * @param source see [com.interfun.buz.common.bean.user.FriendApplySource] for more detail
     */
    suspend fun addFriend(userId: Long, source: Int, businessId: String?): Resp<BuzUserComposite>

    suspend fun addBuzAIAsFriend() : Resp<BuzUserComposite>

    suspend fun agreeAddResearchAccount() : Resp<BuzUserComposite>
    suspend fun refuseAddResearchAccount() : Resp<BuzUserComposite?>

    suspend fun agreeFriendApply(userId: Long) : Resp<BuzUserRelationEntity>
    suspend fun refuseFriendApply(userId: Long) : Resp<BuzUserRelationEntity>

    suspend fun deleteFriend(userId: Long): Resp<BuzUserRelationEntity>

    suspend fun blockUser(userId: Long): Resp<BuzUserRelationEntity>

    suspend fun unblockUser(userId: Long): Resp<BuzUserRelationEntity>

    suspend fun reportUser(userId: Long): Resp<Boolean>

    suspend fun updateUserSetting(
        userId: Long,
        muteMessages: Int? = null,
        muteNotification: Int? = null,
        remark: String? = null
    )

}