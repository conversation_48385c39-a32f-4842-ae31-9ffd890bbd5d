package com.interfun.buz.social.datasource

import com.buz.idl.user.bean.OfficialAccountUserInfo
import com.interfun.buz.common.bean.Resp

internal interface OfficialAccountRemoteDataSource {
    suspend fun getOfficialAccountInfo(userId: Long): Resp<OfficialAccountUserInfo?>
    suspend fun getOfficialAccountInfo(userId: List<Long>): Resp<List<OfficialAccountUserInfo>?>
    suspend fun getAllOfficialFriends(): Resp<List<OfficialAccountUserInfo>?>
} 