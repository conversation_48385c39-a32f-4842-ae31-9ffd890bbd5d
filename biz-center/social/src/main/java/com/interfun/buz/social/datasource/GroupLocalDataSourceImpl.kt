package com.interfun.buz.social.datasource

import androidx.collection.LruCache
import com.interfun.buz.base.coroutine.withReentrantLock
import com.interfun.buz.base.ktx.and
import com.interfun.buz.base.ktx.awaitInScopeIO
import com.interfun.buz.component.hilt.UserQualifier
import com.interfun.buz.social.db.dao.GroupDao
import com.interfun.buz.social.db.entity.BuzGroupExtra
import com.interfun.buz.social.db.entity.BuzGroupTable
import com.interfun.buz.social.db.entity.GroupTableComposite
import com.interfun.buz.social.db.entity.GroupUnUploadedSetting
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.sync.Mutex
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject

@OptIn(ExperimentalCoroutinesApi::class)
internal class GroupLocalDataSourceImpl @Inject constructor(
    @UserQualifier val groupDao: GroupDao,
    @UserQualifier val userScope: CoroutineScope,
) : GroupLocalDataSource {
    private val buzGroupLruCache = LruCache<Long, BuzGroupTable>(3000)
    private val buzGroupUpdateFlow = MutableSharedFlow<Set<Long>>()
    private val buzGroupExtraLruCache = LruCache<Long, BuzGroupExtra>(3000)
    private val buzGroupExtraUpdateFlow = MutableSharedFlow<Set<Long>>()

    @Volatile
    private var isAllUnUploadedSettingCacheInit = false
    private val buzGroupUnUploadedSettingCache = ConcurrentHashMap<Long, GroupUnUploadedSetting>()

    //数据库跟内存的更新是非原子性的，所以需要加锁将两者绑起来，保证数据库与内存数据的一致性
    private val groupMutex = Mutex()
    private val unUploadedSettingMutex = Mutex()

    override suspend fun getGroup(groupId: Long): BuzGroupTable? {
        return buzGroupLruCache[groupId] ?: queryGroupFromDb(groupId)
    }

    @Deprecated("兼容旧代码，新代码别用")
    private fun getGroupNotSuspend(groupId: Long): BuzGroupTable? {
        return buzGroupLruCache[groupId] ?: queryGroupFromDbNotSuspend(groupId)
    }

    /**
     * @return 返回的用户数据跟查询的对应index，如果查不到，则对应的位置为null
     */
    override suspend fun getGroup(groupIds: List<Long>): List<BuzGroupTable?> {
        if (groupIds.isEmpty()) return emptyList()
        if (groupIds.size == 1) {
            return listOf(getGroup(groupIds[0]))
        }
        //尽量复用缓存，没有缓存的在批量从数据库查，提升性能
        val memoryCacheList = arrayOfNulls<BuzGroupTable>(groupIds.size)
        //这个map的作用是为了保证返回的数据跟查询的用户顺序对应
        var notCachedMap: MutableMap<Long, Int>? = null
        groupIds.forEachIndexed { index, id ->
            val group = buzGroupLruCache[id]
            memoryCacheList[index] = group
            if (group == null) {
                if (notCachedMap == null) {
                    notCachedMap = HashMap()
                }
                notCachedMap!!.put(id, index)
            }
        }
        val map = notCachedMap
        if (map != null) {
            val notCachedList = map.keys.toList()
            queryGroupFromDb(notCachedList)?.forEach {
                val index = map[it.groupId]
                memoryCacheList[index!!] = it
            }
        }
        return memoryCacheList.toList()
    }

    override fun getGroupFlow(groupId: Long): Flow<BuzGroupTable?> =
        buzGroupUpdateFlow.onSubscription { emit(hashSetOf(groupId)) }
            .filter { it.contains(groupId) }
            .mapLatest { getGroup(groupId) }
            .distinctUntilChanged()
            .flowOn(Dispatchers.Default)

    override fun getGroupFlow(groupIds: List<Long>): Flow<Map<Long, BuzGroupTable>> = channelFlow {
        val groupMap = HashMap<Long, BuzGroupTable>(groupIds.size)
        val groupIdSet = groupIds.toSet()
        val updateFlow = buzGroupUpdateFlow.mapNotNull {
            val intersectionSet = it and groupIdSet
            intersectionSet.ifEmpty { null }
        }.shareIn(this, SharingStarted.WhileSubscribed())
        updateFlow.onSubscription {
            emit(groupIdSet)
        }.collect { updatedSet ->
            val groupIdList = updatedSet.toList()
            val groupList = getGroup(groupIdList)
            groupList.forEach {
                if (it != null) {
                    groupMap[it.groupId] = it
                }
            }
            send(groupMap.toMap())
        }
    }

    override suspend fun saveGroup(group: BuzGroupTable): BuzGroupTable {
        return saveGroup(listOf(group))[0]
    }

    override suspend fun saveGroup(groups: List<BuzGroupTable>): List<BuzGroupTable> {
        return awaitInScopeIO(userScope) {
            groupMutex.withReentrantLock {
                groupDao.insertGroup(groups)
                groups.forEach {
                    buzGroupLruCache.put(it.groupId, it)
                }
            }
            buzGroupUpdateFlow.emit(groups.mapTo(hashSetOf()) { it.groupId })
            groups
        }
    }

    override suspend fun getGroupExtra(groupId: Long): BuzGroupExtra? {
        return buzGroupExtraLruCache[groupId] ?: queryGroupExtraFromDb(groupId)
    }

    @Deprecated("兼容旧代码，新代码别用")
    override fun getGroupExtraNotSuspend(groupId: Long): BuzGroupExtra? {
        return buzGroupExtraLruCache[groupId] ?: queryGroupExtraFromDbNotSuspend(groupId)
    }

    /**
     * @return 返回的用户数据跟查询的对应index，如果查不到，则对应的位置为null
     */
    override suspend fun getGroupExtra(groupIds: List<Long>): List<BuzGroupExtra?> {
        if (groupIds.isEmpty()) return emptyList()
        if (groupIds.size == 1) {
            return listOf(getGroupExtra(groupIds[0]))
        }
        //尽量复用缓存，没有缓存的在批量从数据库查，提升性能
        val memoryCacheList = arrayOfNulls<BuzGroupExtra>(groupIds.size)
        //这个map的作用是为了保证返回的数据跟查询的用户顺序对应
        var notCachedMap: MutableMap<Long, Int>? = null
        groupIds.forEachIndexed { index, id ->
            val extra = buzGroupExtraLruCache[id]
            memoryCacheList[index] = extra
            if (extra == null) {
                if (notCachedMap == null) {
                    notCachedMap = HashMap()
                }
                notCachedMap!!.put(id, index)
            }
        }
        val map = notCachedMap
        if (map != null) {
            val notCachedList = map.keys.toList()
            queryGroupExtraFromDb(notCachedList)?.forEach {
                val index = map[it.groupId]
                memoryCacheList[index!!] = it
            }
        }
        return memoryCacheList.toList()
    }

    override fun getGroupExtraFlow(groupId: Long): Flow<BuzGroupExtra?> =
        buzGroupExtraUpdateFlow.onSubscription { emit(hashSetOf(groupId)) }
            .filter { it.contains(groupId) }
            .mapLatest { getGroupExtra(groupId) }
            .distinctUntilChanged()
            .flowOn(Dispatchers.Default)

    override fun getGroupExtraFlow(groupIds: List<Long>): Flow<Map<Long, BuzGroupExtra>> =
        channelFlow {
            val groupMap = HashMap<Long, BuzGroupExtra>(groupIds.size)
            val groupIdSet = groupIds.toSet()
            val updateFlow = buzGroupExtraUpdateFlow.mapNotNull {
                val intersectionSet = it and groupIdSet
                intersectionSet.ifEmpty { null }
            }.shareIn(this, SharingStarted.WhileSubscribed(5000))
            updateFlow.onSubscription {
                emit(groupIdSet)
            }.collect { updatedSet ->
                val groupIdList = updatedSet.toList()
                val groupList = getGroupExtra(groupIdList)
                groupList.forEach {
                    if (it != null) {
                        groupMap[it.groupId] = it
                    }
                }
                send(groupMap.toMap())
            }
        }

    override suspend fun saveGroupExtra(extra: BuzGroupExtra): BuzGroupExtra {
        return saveGroupExtra(listOf(extra))[0]
    }

    override suspend fun saveGroupExtra(extraList: List<BuzGroupExtra>): List<BuzGroupExtra> {
        return awaitInScopeIO(userScope) {
            groupMutex.withReentrantLock {
                groupDao.insertGroupExtra(extraList)
                extraList.forEach {
                    buzGroupExtraLruCache.put(it.groupId, it)
                }
                extraList
            }.also {
                buzGroupExtraUpdateFlow.emit(extraList.mapTo(hashSetOf()) { it.groupId })
            }
        }
    }

    override suspend fun saveGroupComposite(composite: GroupTableComposite): GroupTableComposite {
        return awaitInScopeIO(userScope) {
            groupMutex.withReentrantLock {
                groupDao.insertGroupAndExtra(composite)
                buzGroupLruCache.put(composite.buzGroupTable.groupId, composite.buzGroupTable)
                composite.buzGroupExtra?.let {
                    buzGroupExtraLruCache.put(it.groupId, it)
                }
                composite
            }.also {
                buzGroupUpdateFlow.emit(hashSetOf(composite.buzGroupTable.groupId))
                composite.buzGroupExtra?.let {
                    buzGroupExtraUpdateFlow.emit(hashSetOf(it.groupId))
                }
            }
        }
    }

    override suspend fun saveGroupComposite(groupComposites: List<GroupTableComposite>): List<GroupTableComposite> {
        return awaitInScopeIO(userScope) {
            groupMutex.withReentrantLock {
                groupDao.insertGroupAndExtra(groupComposites)
                groupComposites.forEach { (buzGroup, buzGroupExtra) ->
                    buzGroupLruCache.put(buzGroup.groupId, buzGroup)
                    buzGroupExtra?.let {
                        buzGroupExtraLruCache.put(buzGroupExtra.groupId, buzGroupExtra)
                    }
                }
                groupComposites
            }.also {
                val groupIds = groupComposites.mapTo(hashSetOf()) { it.buzGroupTable.groupId }
                buzGroupUpdateFlow.emit(groupIds)
                buzGroupExtraUpdateFlow.emit(groupIds)
            }
        }
    }

    override suspend fun getGroupComposite(groupId: Long): GroupTableComposite? {
        val group = getGroup(groupId) ?: return null
        val extra = getGroupExtra(groupId)
        return GroupTableComposite(group, extra)
    }

    override suspend fun getGroupComposite(groupIds: List<Long>): List<GroupTableComposite?>? {
        val groups = getGroup(groupIds)
        val result = ArrayList<GroupTableComposite?>(groupIds.size)
        for (group in groups) {
            if (group != null) {
                val extra = getGroupExtra(group.groupId)
                result.add(GroupTableComposite(group, extra))
            } else {
                result.add(null)
            }
        }
        return result
    }

    override suspend fun getAllJoinedGroups(): List<GroupTableComposite> {
        return groupDao.getAllJoinedGroups()
    }

    override fun getAllJoinedGroupsFlow(): Flow<List<GroupTableComposite>> {
        return groupDao.getAllJoinedGroupsFlow()
    }

    @Deprecated("兼容旧代码，新代码别用")
    override fun getGroupCompositeNotSuspend(groupId: Long): GroupTableComposite? {
        val group = getGroupNotSuspend(groupId) ?: return null
        val extra = getGroupExtraNotSuspend(groupId)
        return GroupTableComposite(group, extra)
    }

    @Deprecated("兼容旧代码，新代码别用")
    override fun getGroupCompositeFromMem(groupId: Long): GroupTableComposite? {
        val group = buzGroupLruCache[groupId] ?: return null
        val extra = buzGroupExtraLruCache[groupId]
        return GroupTableComposite(group, extra)
    }

    override fun getGroupCompositeFlow(groupId: Long): Flow<GroupTableComposite?> {
        return combine(
            getGroupFlow(groupId),
            getGroupExtraFlow(groupId)
        ) { group, extra ->
            if (group != null) {
                GroupTableComposite(group, extra)
            } else {
                null
            }
        }.distinctUntilChanged().flowOn(Dispatchers.Default)
    }

    override fun getGroupCompositeFlow(groupIds: List<Long>): Flow<Map<Long, GroupTableComposite>> =
        combine(
            getGroupFlow(groupIds),
            getGroupExtraFlow(groupIds)
        ) { groupMap, extraMap ->
            val result = HashMap<Long, GroupTableComposite>(groupIds.size)
            groupIds.forEach { groupId ->
                val group = groupMap[groupId]
                val extra = extraMap[groupId]
                if (group != null) {
                    result[groupId] = GroupTableComposite(group, extra)
                }
            }
            result
        }

    override suspend fun clearAllGroupTables() {
        awaitInScopeIO(userScope) {
            groupDao.clearAllGroupTables()
            buzGroupLruCache.evictAll()
            buzGroupExtraLruCache.evictAll()
        }
    }

    private suspend fun ensureLoadAllUploadedSetting() {
        if (isAllUnUploadedSettingCacheInit) {
            return
        }
        awaitInScopeIO(userScope) {
            unUploadedSettingMutex.withReentrantLock {
                val settings = groupDao.loadAllUnLoadedSettings()
                settings.forEach {
                    buzGroupUnUploadedSettingCache[it.groupId] = it
                }
                isAllUnUploadedSettingCacheInit = true
            }
        }
    }

    override suspend fun updateQuitGroup(groupId: Long) {
        return awaitInScopeIO(userScope){
            groupMutex.withReentrantLock {
                groupDao.updateQuitGroup(groupId)
            }
            //刷新缓存
            queryGroupExtraFromDb(groupId)
        }
    }

    override suspend fun updateGroupSettings(
        groupId: Long,
        muteMessages: Int?,
        muteNotification: Int?,
        remark: String?
    ): BuzGroupExtra? {
        return awaitInScopeIO(userScope) {
            groupMutex.withReentrantLock {
                groupDao.updateGroupSettings(
                    groupId,
                    muteMessages,
                    muteNotification,
                )?.also { groupExtra ->
                    buzGroupExtraLruCache.put(groupId, groupExtra)
                }
            }?.also { groupExtra ->
                buzGroupExtraUpdateFlow.emit(hashSetOf(groupExtra.groupId))
            }
        }
    }

    override suspend fun insertOrUpdateUnUploadedGroupSettings(
        groupId: Long,
        muteMessages: Int?,
        muteNotification: Int?
    ) {
        ensureLoadAllUploadedSetting()
        unUploadedSettingMutex.withReentrantLock {
            val newUnUploadedSetting = groupDao.insertOrUpdateUnUploadedGroupSettings(
                groupId,
                muteMessages,
                muteNotification,
            )
            newUnUploadedSetting?.let {
                buzGroupUnUploadedSettingCache[groupId] = newUnUploadedSetting
            }
        }
    }

    override suspend fun deleteGroupUnUploadedSettingsIfMatched(
        groupId: Long,
        updatedMuteMessages: Int?,
        updatedMuteNotification: Int?
    ) {
        unUploadedSettingMutex.withReentrantLock {
            val unUploadedSetting = getGroupUnUploadedSettings(groupId) ?: return@withReentrantLock
            val isMuteMessagesChanged = unUploadedSetting.muteMessages != updatedMuteMessages
            val isMuteNotificationChanged =
                unUploadedSetting.muteNotification != updatedMuteNotification
            if (!isMuteMessagesChanged && !isMuteNotificationChanged) {
                //向服务端更新的内容跟本地的内容一致，说明服务端请求过程中本地没有更新，可以删除本地记录
                groupDao.deleteGroupUnUploadedSetting(groupId)
                buzGroupUnUploadedSettingCache.remove(groupId)
            } else {
                //部分内容在请求期间又更新了，需要将没有变更的内容设置为null，下次就不需要再请求了
                val newSetting = groupDao.setUpLoadUserRelationSettingsNull(
                    groupId,
                    setMuteMessagesNull = !isMuteMessagesChanged,
                    setMuteNotificationNull = !isMuteNotificationChanged,
                )
                newSetting?.let {
                    buzGroupUnUploadedSettingCache[groupId] = newSetting
                }
            }
        }
    }

    override suspend fun getGroupUnUploadedSettings(groupId: Long): GroupUnUploadedSetting? {
        ensureLoadAllUploadedSetting()
        return buzGroupUnUploadedSettingCache[groupId]
    }

    override suspend fun getAllGroupUnUploadedSettings(): List<GroupUnUploadedSetting>? {
        ensureLoadAllUploadedSetting()
        return buzGroupUnUploadedSettingCache.values.toList()
    }

    @Deprecated("兼容旧代码，新代码别用")
    private fun queryGroupFromDbNotSuspend(groupId: Long): BuzGroupTable? {
        //没用锁，可能会有并发问题，但是为了兼容旧代码，没办法
        return groupDao.queryGroup(groupId)?.apply {
            buzGroupLruCache.put(groupId, this)
        }
    }

    private suspend fun queryGroupFromDb(groupId: Long): BuzGroupTable? {
        return awaitInScopeIO(userScope) {
            groupMutex.withReentrantLock {
                groupDao.queryGroup(groupId)?.apply {
                    buzGroupLruCache.put(groupId, this)
                }
            }
        }
    }

    private suspend fun queryGroupFromDb(groupIds: List<Long>): List<BuzGroupTable>? {
        return awaitInScopeIO(userScope) {
            groupMutex.withReentrantLock {
                groupDao.queryGroup(groupIds)?.apply {
                    this.forEach {
                        buzGroupLruCache.put(it.groupId, it)
                    }
                }
            }
        }
    }

    @Deprecated("兼容旧代码，新代码别用")
    private fun queryGroupExtraFromDbNotSuspend(groupId: Long): BuzGroupExtra? {
        //可能会有并发问题，但是兼容旧代码，没办法了，先这样处理
        return groupDao.queryGroupExtra(groupId)?.apply {
            buzGroupExtraLruCache.put(groupId, this)
        }
    }

    private suspend fun queryGroupExtraFromDb(groupId: Long): BuzGroupExtra? {
        return awaitInScopeIO(userScope) {
            groupMutex.withReentrantLock {
                groupDao.queryGroupExtra(groupId)?.apply {
                    buzGroupExtraLruCache.put(groupId, this)
                }
            }
        }
    }

    private suspend fun queryGroupExtraFromDb(groupIds: List<Long>): List<BuzGroupExtra>? {
        return awaitInScopeIO(userScope) {
            groupMutex.withReentrantLock {
                groupDao.queryGroupExtra(groupIds)?.apply {
                    this.forEach {
                        buzGroupExtraLruCache.put(it.groupId, it)
                    }
                }
            }
        }
    }
} 