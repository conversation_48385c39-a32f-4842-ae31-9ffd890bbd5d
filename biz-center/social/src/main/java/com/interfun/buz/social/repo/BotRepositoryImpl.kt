package com.interfun.buz.social.repo

import androidx.lifecycle.Lifecycle
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.common.bean.Resp
import com.interfun.buz.common.bean.Resp.Error
import com.interfun.buz.common.bean.Resp.Success
import com.interfun.buz.common.bean.user.BuzUserRelationValue
import com.interfun.buz.component.hilt.UserQualifier
import com.interfun.buz.common.di.NetworkStatus
import com.interfun.buz.common.ktx.codeRequestSuccess
import com.interfun.buz.common.net.dispatcher.*
import com.interfun.buz.common.utils.parse
import com.interfun.buz.component.hilt.GlobalQualifier
import com.interfun.buz.component.hilt.UserAuth
import com.interfun.buz.social.datasource.BotLocalDataSource
import com.interfun.buz.social.datasource.BotRemoteDataSource
import com.interfun.buz.social.datasource.UserLocalDataSource
import com.interfun.buz.social.db.entity.*
import com.interfun.buz.social.entity.AddBotResult
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.withContext
import javax.inject.Inject

internal class BotRepositoryImpl @Inject constructor(
    @UserQualifier private val userAuth: UserAuth,
    @UserQualifier private val botLocalDataSource: BotLocalDataSource,
    @UserQualifier private val botRemoteDataSource: BotRemoteDataSource,
    @UserQualifier private val userRepository: UserRepository,
    @UserQualifier private val userLocalDS: UserLocalDataSource,
    @UserQualifier private val userScope: CoroutineScope,
    @GlobalQualifier private val networkStatus: NetworkStatus,
    @GlobalQualifier private val appLifecycle: Lifecycle,
) : BotRepository {

    companion object {
        const val TAG = "BotRepository"
    }

    //因为机器人的接口比较特别，如果机器人不可用的话，单个的请求接口才会有code返回告知客户端原因
    //所以不能跟列表的合并，否则会拿不到机器人为啥不可用的原因
    private val syncSingleBotDispatcher =
        UniqueRequestDispatcher<Retryable<Long>>(scope = userScope,
            dispatchInterval = DefaultRetryableDispatchInterval(networkStatus, appLifecycle),
            debugTag = "syncSingleBotDispatcher",
            onUndeliveredElement = null,
            uniqueKey = { it.data }
        ) { request, _ ->
            val botUserId = request.data
            val resp = loadBotInfoFromServer(botUserId)
            if (resp is Resp.Error && resp.code?.codeRequestSuccess == false) {
                syncBotInfo(botUserId, true)
                false
            } else {
                true
            }
        }

    private val syncListDispatcher = RequestDispatcher("BotRequestDispatcher") { mergedList ->
        when (mergedList.size) {
            0 -> true
            1 -> {
                syncBotInfo(mergedList.first(), true)
                true
            }

            else -> {
                val resp = loadBotInfoFromServer(mergedList)
                if (resp is Resp.Error && resp.code?.codeRequestSuccess == false) {
                    syncBotInfo(mergedList, true)
                    false
                } else {
                    true
                }
            }
        }
    }

    private val syncMarketBotList = MergedRequestDispatcher<Retryable<Unit>>(
        scope = userScope,
        dispatchInterval = DefaultRetryableDispatchInterval(networkStatus, appLifecycle),
        debugTag = "syncMarketBotListDispatcher",
        onUndeliveredElement = null,
        requestHandler = { _ ->
            val resp = loadMarketAllBotsFromServer()
            if (resp is Resp.Error && resp.code?.codeRequestSuccess == false) {
                syncAllMarketBots(true)
                false
            } else {
                true
            }
        }
    )

    private val syncBotSettingsDispatcher =
        UniqueRequestDispatcher<Retryable<Long>>(scope = userScope,
            dispatchInterval = DefaultRetryableDispatchInterval(networkStatus, appLifecycle),
            debugTag = "syncBotSettingsDispatcher",
            onUndeliveredElement = null,
            uniqueKey = { it.data }
        ) { request, _ ->
            val botUserId = request.data
            val resp = loadBotSettingsFromServer(botUserId)
            if (resp is Resp.Error && resp.code?.codeRequestSuccess == false) {
                syncBotSettings(botUserId, true)
                false
            } else {
                true
            }
        }

    override suspend fun getBotInfo(botUserId: Long): BotInfo? {
        return getBotInfoFromCache(botUserId)
            ?: (loadBotInfoFromServer(botUserId) as? Success)?.data
    }

    override suspend fun getBotInfoFromCache(botUserId: Long): BotInfo? {
        val buzUser = userRepository.getUserFromCache(botUserId) ?: return null
        val botExtra = botLocalDataSource.getBotExtra(botUserId)
        return BotInfo(buzUser, botExtra)
    }

    override fun getBotInfoCacheFlow(botUserId: Long): Flow<BotInfo?> = combine(
        userRepository.getUserFromCacheFlow(botUserId),
        botLocalDataSource.getBotExtraFlow(botUserId)
    ) { buzUser, botExtra ->
        if (buzUser == null) {
            return@combine null
        }
        BotInfo(buzUser, botExtra)
    }

    override fun getBotInfoFlow(botUserId: Long): Flow<BotInfo> = flow {
        var isSync = false
        getBotInfoCacheFlow(botUserId).collect { info ->
            if (!isSync && (info == null || info.botExtraInfo == null)) {
                syncBotInfo(arrayListOf(botUserId), false)
                isSync = true
            }
            if (info != null) {
                emit(info)
            }
        }
    }

    override suspend fun getBotExtra(botUserId: Long): BotExtra? {
        return getBotExtraFromCache(botUserId)
            ?: (loadBotInfoFromServer(botUserId) as? Success)?.data?.botExtraInfo
    }

    override suspend fun getBotExtraFromCache(botUserId: Long): BotExtra? {
        return botLocalDataSource.getBotExtra(botUserId)
    }

    @Deprecated("recommend to use getBotExtraFromCache instead")
    override fun getBotExtraFromMemory(botUserId: Long): BotExtra? {
        return botLocalDataSource.getBotExtraFromMemory(botUserId)
    }

    override fun getBotExtraCacheFlow(botUserId: Long): Flow<BotExtra?> {
        return botLocalDataSource.getBotExtraFlow(botUserId)
    }

    override fun getBotExtraFlow(botUserId: Long): Flow<BotExtra> = flow {
        var isSync = false
        getBotExtraCacheFlow(botUserId).collect {
            if (it == null) {
                if (!isSync) {
                    syncBotInfo(botUserId)
                    isSync = true
                }
            } else {
                emit(it)
            }
        }
    }

    override fun getBotExtraFromCacheFlow(botUserIds: List<Long>): Flow<Map<Long, BotExtra>> {
        return botLocalDataSource.getBotExtraFlow(botUserIds)
    }

    override fun getBotExtraFlow(botUserIds: List<Long>): Flow<Map<Long, BotExtra>> = flow {
        var isSync = false
        getBotExtraFromCacheFlow(botUserIds).collect { localData ->
            if (!isSync) {
                val needSyncList = botUserIds.filter {
                    localData[it] == null
                }
                syncBotInfo(needSyncList)
                isSync = true
            }
            emit(localData)
        }
    }

    override fun loadAllBotExtraFromCache() {
        botLocalDataSource.loadAllBotExtraFromCache()
    }

    override suspend fun getBotInfoAndRelationFromCache(botUserId: Long): BotInfoAndRelation? {
        val botInfo = getBotInfoFromCache(botUserId) ?: return null
        val userRelation = userRepository.getUserRelationFromCache(botUserId)
        return BotInfoAndRelation(botInfo, userRelation)
    }

    override fun getBotInfoAndRelationCacheFlow(botUserId: Long): Flow<BotInfoAndRelation?> =
        combine(
            getBotInfoCacheFlow(botUserId),
            userRepository.getUserRelationFromCacheFlow(botUserId)
        ) { botInfo, userRelation ->
            botInfo ?: return@combine null
            BotInfoAndRelation(botInfo, userRelation)
        }

    override fun getBotInfoAndRelationFlow(
        botUserId: Long,
        forceRefresh: Boolean
    ): Flow<BotInfoAndRelation> = flow {
        var isSync = false
        if (forceRefresh) {
            syncBotInfoAndRelation(botUserId)
            isSync = true
        }
        getBotInfoAndRelationCacheFlow(botUserId).collect { info ->
            if (!isSync && (info == null || info.relation == null)) {
                syncBotInfoAndRelation(botUserId)
                isSync = true
            }
            if (info != null) {
                emit(info)
            }
        }
    }

    override fun syncBotInfoAndRelation(botUserId: Long) {
        syncBotInfo(botUserId)
    }

    override suspend fun getBotSettingsFromCache(botUserId: Long): BotWholeSettingEntity? {
        return botLocalDataSource.getBotSettings(botUserId)
    }

    @Deprecated("use getBotSettingsFromCache instead")
    override fun getBotSettingsFromMemory(botUserId: Long): BotWholeSettingEntity? {
        return botLocalDataSource.getBotSettingsFromMemory(botUserId)
    }

    override fun getBotSettingsCacheFlow(botUserId: Long): Flow<BotWholeSettingEntity?> {
        return botLocalDataSource.getBotSettingsFlow(botUserId)
    }

    override fun getBotSettingsFlow(botUserId: Long): Flow<BotWholeSettingEntity?> = flow {
        var isSync = false
        getBotSettingsCacheFlow(botUserId).collect {
            if (it == null) {
                if (!isSync) {
                    syncBotSettings(botUserId, false)
                    isSync = true
                }
            } else {
                emit(it)
            }
        }
    }

    override fun loadAllBotSettingsFromCache() {
        botLocalDataSource.loadAllBotSettingsFromCache()
    }

    override suspend fun updateLocalBotSettings(
        botUserId: Long,
        sourceLanguage: String?,
        targetLanguage: String?
    ): BotWholeSettingEntity? {
        return botLocalDataSource.updateBotSettings(
            botUserId,
            sourceLanguage = sourceLanguage,
            targetLanguage = targetLanguage
        )
    }

    override suspend fun updateServerBotSettings(
        botUserId: Long,
        languageCode: String?,
        voiceStyleId: Long?,
    ): Resp<BotWholeSettingEntity?> {
        val resp = botRemoteDataSource.updateBotSetting(
            userAuth.userId,
            botUserId,
            languageCode,
            voiceStyleId
        )
        resp.prompt?.parse()
        return when (resp) {
            is Success -> {
                val newSettingData = resp.data ?: return Error(
                    0,
                    "resp error,data is null",
                    resp.prompt
                )
                Success(
                    botLocalDataSource.updateBotSettings(
                        botUserId,
                        voiceStyleId = newSettingData.voiceStyleId,
                        languageCode = newSettingData.languageCode
                    )
                )
            }

            is Error -> {
                Error(resp.code, resp.msg, resp.prompt)
            }
        }
    }

    override suspend fun switchSourceAndTargetLanguage(
        botUserId: Long,
    ): BotWholeSettingEntity? {
        return botLocalDataSource.switchSourceAndTargetLanguage(botUserId)
    }

    override fun getRecentlyUsedTranslatorLanguage(botUserId: Long): Flow<List<String>?> {
        return botLocalDataSource.getRecentlyUsedTranslatorLanguage(botUserId)
    }

    override fun syncBotSettings(botUserId: Long) {
        syncBotSettings(botUserId, false)
    }

    override fun syncBotInfo(botList: List<Long>) {
        syncBotInfo(botList, false)
    }

    override fun syncBotInfo(botUserId: Long) {
        syncBotInfo(botUserId, false)
    }

    override fun syncBotMarket() {
        syncAllMarketBots(false)
    }


    override fun getAllMarketBotListCacheFlow(): Flow<List<BotInfo>?> = channelFlow {
        botLocalDataSource.getMarketBotListFlow().collectLatest { botIdList ->
            if (botIdList.isNullOrEmpty()) {
                send(null)
            } else {
                combine(
                    botLocalDataSource.getBotExtraFlow(botIdList),
                    userRepository.getUserFromCacheFlow(botIdList)
                ) { botExtraMap, userMap ->
                    botIdList.mapNotNull { id ->
                        val buzUser = userMap[id]
                        val botExtra = botExtraMap[id]
                        if (buzUser == null) {
                            null
                        } else {
                            BotInfo(buzUser, botExtra)
                        }
                    }
                }.collectLatest { botList ->
                    send(botList)
                }
            }
        }
    }

    override fun getAllMarketBotListFlow(forceRefresh: Boolean): Flow<List<BotInfo>> = flow {
        var isSync = false
        if (forceRefresh) {
            syncAllMarketBots()
            isSync = true
        }
        getAllMarketBotListCacheFlow().collect {
            if (it.isNullOrEmpty()) {
                if (!isSync) {
                    syncAllMarketBots()
                    isSync = true
                }
            } else {
                emit(it)
            }
        }
    }

    override fun hasNewAiInMarket(): Flow<Boolean> {
        return botLocalDataSource.getHasNewBotInMarketFlow()
    }

    override suspend fun addBotToChat(botUserId: Long): Resp<AddBotResult?> {
        val resp = botRemoteDataSource.addBotToChat(botUserId)
        if (resp !is Success) {
            return if (resp is Error) {
                Resp.Error(resp.code, resp.msg, resp.prompt)
            } else {
                Resp.Error(0, "", resp.prompt)
            }
        }
        val data = resp.data
        val userCompositeResp = userRepository.getUserCompositeFromServer(botUserId)
        if (data == null) {
            return Resp.Success(null, resp.prompt)
        }
        val isFirstAdd = data.firstChat ?: false
        val extra =
            if (data.botInfo == null) null else botLocalDataSource.updateBotExtra(
                data.botInfo!!.convertDb()
            )
        if (userCompositeResp is Success && userCompositeResp.data != null) {
            //请求的用户信息成功，直接拿来返回即可
            return Resp.Success(
                AddBotResult(
                    BotInfoAndRelation(
                        BotInfo(userCompositeResp.data!!.user, extra),
                        userCompositeResp.data!!.relationInfo
                    ), isFirstAdd
                ),
                resp.prompt
            )
        }
        //请求的用户信息不成功，那加机器人接口返回的内容入库
        data.botInfo?.userInfo?.let {
            val buzUser = userRepository.updateUser(it)
            val relation =
                userLocalDS.updateRelation(botUserId, BuzUserRelationValue.FRIEND.value)
            return Resp.Success(
                AddBotResult(
                    BotInfoAndRelation(BotInfo(buzUser, extra), relation),
                    isFirstAdd
                ),
                resp.prompt
            )
        }
        return Resp.Success(null, resp.prompt)
    }

    override suspend fun addBotToChat(botUserIds: List<Long>): Resp<List<BotInfoAndRelation>?> {
        val resp = botRemoteDataSource.addBotToChat(botUserIds)
        if (resp !is Success) {
            return if (resp is Error) {
                Resp.Error(resp.code, resp.msg, resp.prompt)
            } else {
                Resp.Error(0, "", resp.prompt)
            }
        }
        val data = resp.data
        val userCompositeResp = userRepository.getUserCompositeFromServer(botUserIds)
        if (data == null) {
            return Resp.Success(null, resp.prompt)
        }
        val extraList = botLocalDataSource.updateBotExtraList(
            data.map { it.convertDb() }
        )
        val extraMap = extraList.associateBy { it.botUserId }
        if (userCompositeResp is Success && userCompositeResp.data != null) {
            //请求的用户信息成功，直接拿来返回即可
            val userCompositeMap = userCompositeResp.data!!.associateBy { it.user.userId }
            val result = data.mapNotNull { botInfo ->
                val buzComposite = userCompositeMap[botInfo.botUserId]
                if (buzComposite?.user == null) {
                    null
                } else {
                    BotInfoAndRelation(
                        BotInfo(buzComposite.user, extraMap[botInfo.botUserId]),
                        buzComposite.relationInfo
                    )
                }
            }
            return Resp.Success(
                result,
                resp.prompt
            )
        }
        //请求的用户信息不成功，那加机器人接口返回的内容入库
        val buzUsers =
            userRepository.updateUser(data.mapNotNull { it.userInfo }).associateBy { it.userId }
        val relations = userLocalDS.updateRelation(data.map {
            (it.botUserId ?: 0L) to BuzUserRelationValue.FRIEND.value
        })?.associateBy { it.userId }
        val result = data.mapNotNull { botInfo ->
            val buzUser = buzUsers[botInfo.botUserId]
            if (buzUser == null) {
                null
            } else {
                BotInfoAndRelation(
                    BotInfo(buzUser, extraMap[botInfo.botUserId]),
                    relations?.get(botInfo.botUserId)
                )
            }
        }
        return Resp.Success(result, resp.prompt)
    }

    override suspend fun clearNewAiInMarketFlag() {
        botLocalDataSource.clearNewBotInMarketFlag()
    }

    private fun syncBotInfo(botList: List<Long>, isRetry: Boolean = false) {
        syncListDispatcher.request(Retryable(botList, isRetry))
    }

    private fun syncBotInfo(botUserId: Long, isRetry: Boolean = false) {
        syncSingleBotDispatcher.request(Retryable(botUserId, isRetry))
    }

    private fun syncAllMarketBots(isRetry: Boolean = false) {
        syncMarketBotList.request(Retryable(Unit, isRetry))
    }

    private fun syncBotSettings(botUserId: Long, isRetry: Boolean = false) {
        syncBotSettingsDispatcher.request(Retryable(botUserId, isRetry))
    }

    private suspend fun loadBotInfoFromServer(userId: Long): Resp<BotInfo?> {
        val resp = botRemoteDataSource.getBotInfo(userId)
        resp.prompt?.parse()
        return when (resp) {
            is Success -> {
                val botInfoFromServer = resp.data
                if (botInfoFromServer == null) {
                    Success(null)
                } else {
                    val buzUser = botInfoFromServer.userInfo?.let {
                        userRepository.updateUser(it)
                    } ?: return Success(null)
                    val botExtra =
                        botLocalDataSource.updateBotExtra(botInfoFromServer.convertDb())
                    Success(BotInfo(buzUser, botExtra))
                }
            }

            is Error -> {
                if (resp.code?.codeRequestSuccess == true) {
                    //请求成功，代表返回的机器人不可用，需要更新本地数据库
                    logInfo(TAG, "bot unavailable,code:${resp.code}")
                    val status = BotExtra.rCodeToBotStatus(resp.code ?: 0)
                    botLocalDataSource.updateBotStatus(userId, status)
                    Success(getBotInfoFromCache(userId), resp.prompt)
                } else {
                    Error(resp.code, resp.msg, resp.prompt)
                }
            }
        }
    }

    private suspend fun loadBotInfoFromServer(userIds: List<Long>): Resp<List<BotInfo>?> =
        withContext(Dispatchers.Default) {
            val resp = botRemoteDataSource.getBotInfoBatch(userIds)
            resp.prompt?.parse()
            return@withContext when (resp) {
                is Success -> {
                    val botListFromServer = resp.data
                    if (botListFromServer == null) {
                        Success(null)
                    } else {
                        val userInfoList = botListFromServer.mapNotNull { it.userInfo }
                        val buzUserList = userRepository.updateUser(userInfoList)
                        val botExtraList =
                            botLocalDataSource.updateBotExtraList(botListFromServer.map {
                                it.convertDb()
                            })
                        val extraMap = hashMapOf<Long, BotExtra>()
                        extraMap.putAll(botExtraList.map { it.botUserId to it })
                        val resultList = buzUserList.map { BotInfo(it, extraMap[it.userId]) }
                        Success(resultList)
                    }
                }

                is Error -> {
                    Error(resp.code, resp.msg, resp.prompt)
                }
            }
        }

    private suspend fun loadMarketAllBotsFromServer(): Resp<List<BotInfo>?> =
        withContext(Dispatchers.Default) {
            val resp = botRemoteDataSource.getMarketAllBots()
            resp.prompt?.parse()
            return@withContext when (resp) {
                is Success -> {
                    val botListFromServer = resp.data
                    if (botListFromServer == null) {
                        Success(null)
                    } else {
                        val userInfoList = botListFromServer.mapNotNull { it.userInfo }
                        val buzUserList = userRepository.updateUser(userInfoList)
                        val botExtraList =
                            botLocalDataSource.updateBotExtraList(botListFromServer.map {
                                it.convertDb()
                            })
                        val newMarketBotIdList = userInfoList.mapNotNull { it.userId }
                        botLocalDataSource.updateMarketBotList(newMarketBotIdList)
                        val extraMap = hashMapOf<Long, BotExtra>()
                        extraMap.putAll(botExtraList.map { it.botUserId to it })
                        val resultList = buzUserList.map { BotInfo(it, extraMap[it.userId]) }
                        Success(resultList)
                    }
                }

                is Error -> {
                    Error(resp.code, resp.msg, resp.prompt)
                }
            }
        }

    private suspend fun loadBotSettingsFromServer(botUserId: Long): Resp<BotWholeSettingEntity?> {
        val resp = botRemoteDataSource.getBotSettings(botUserId)
        resp.prompt?.parse()
        return when (resp) {
            is Success -> {
                val botSettingsFromServer = resp.data
                if (botSettingsFromServer == null) {
                    Success(null)
                } else {
                    val botSettings = botLocalDataSource.updateBotSettings(
                        botUserId,
                        languageCode = resp.data?.languageCode,
                        voiceStyleId = resp.data?.voiceStyleId
                    )
                    Success(botSettings)
                }
            }

            is Resp.Error -> {
                Error(resp.code, resp.msg, resp.prompt)
            }
        }
    }

    private fun RequestDispatcher(
        debugTag: String,
        handler: suspend (List<Long>) -> Boolean
    ) = MergedRequestDispatcher<Retryable<List<Long>>>(
        scope = userScope,
        dispatchInterval = DefaultRetryableDispatchInterval(networkStatus, appLifecycle),
        debugTag = debugTag,
        onUndeliveredElement = null,
        requestHandler = { requestList ->
            val userIds = requestList.flatMapTo(hashSetOf()) { it.data }
            handler(userIds.toList())
        }
    )


}