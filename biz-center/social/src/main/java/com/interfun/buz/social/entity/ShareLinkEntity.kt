package com.interfun.buz.social.entity

import androidx.room.Entity
import androidx.room.TypeConverter

/**
 *@property convType 1:私聊 2:群聊
 */

@Entity(tableName = "share_link", primaryKeys = ["targetId", "convType"])
data class ShareLinkEntity(
    val targetId: Long,
    val convType: ShareLinkType,
    val link: String,
    val expiredTime: Long
){
    companion object{

        fun toShareLinkTypeHobbiesImpl(enumNum: Int): ShareLinkType {
            return when (enumNum) {
                ShareLinkType.GROUP.serverNum -> {
                    ShareLinkType.GROUP
                }
                else -> {
                    ShareLinkType.PRIVATE
                }
            }
        }
    }
}

enum class ShareLinkType(val serverNum: Int) {
    PRIVATE(1), GROUP(2)

}


class ShareLinkEntityConverters {

    // 将 List<String> 转换为 String
    @TypeConverter
    fun fromHobbies(hobbies: ShareLinkType): Int {
        return hobbies.serverNum
    }

    // 将 String 转换回 List<String>
    @TypeConverter
    fun toHobbies(enumNum: Int): ShareLinkType {
        return ShareLinkEntity.toShareLinkTypeHobbiesImpl(enumNum)
    }
}
