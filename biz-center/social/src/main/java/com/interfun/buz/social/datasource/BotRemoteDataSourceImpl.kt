package com.interfun.buz.social.datasource

import com.buz.idl.bot.bean.BotInfo
import com.buz.idl.bot.bean.BotUserSetting
import com.buz.idl.bot.request.*
import com.buz.idl.bot.response.ResponseAddChat
import com.buz.idl.bot.service.BuzNetBotService
import com.interfun.buz.common.bean.Resp
import com.interfun.buz.common.ktx.isSuccess
import com.interfun.buz.common.utils.language.LanguageProvider
import com.interfun.buz.component.hilt.UserQualifier
import javax.inject.Inject

internal class BotRemoteDataSourceImpl @Inject constructor(
    @UserQualifier private val botService: BuzNetBotService,
) : BotRemoteDataSource {

    override suspend fun getBotInfo(botUserId: Long): Resp<BotInfo?> {
        val resp = botService.getBotInfo(RequestGetBotInfo(botUserId))
        return if (resp.isSuccess) {
            val data = resp.data
            if (data != null) {
                Resp.Success(data.botInfo, prompt = data.prompt)
            } else {
                Resp.Success(null, prompt = data?.prompt)
            }
        } else {
            Resp.Error(resp.code, resp.msg, resp.data?.prompt)
        }
    }

    override suspend fun getBotInfoBatch(botUserIds: List<Long>): Resp<List<BotInfo>?> {
        val resp = botService.getBotInfoList(RequestGetBotInfoList(botUserIds.toSet()))
        return if (resp.isSuccess) {
            val data = resp.data
            if (data != null) {
                Resp.Success(data.botInfoList, prompt = data.prompt)
            } else {
                Resp.Success(null, prompt = data?.prompt)
            }
        } else {
            Resp.Error(resp.code, resp.msg, resp.data?.prompt)
        }
    }

    override suspend fun getMarketAllBots(): Resp<List<BotInfo>?> {
        return getBotInfoBatch(emptyList())
    }

    override suspend fun updateBotSetting(
        currentUserId: Long,
        botUserId: Long,
        languageCode: String?,
        voiceStyleId: Long?
    ): Resp<BotUserSetting?> {
        val unitedCode =
            if (languageCode == null) null else LanguageProvider.transferToUnitedCode(languageCode)
        val reqInfo = RequestUpdateBotSetting(
            botUserId,
            BotUserSetting(
                currentUserId,
                botUserId,
                languageCode = unitedCode,
                voiceStyleId = voiceStyleId
            )
        )
        val resp = botService.updateBotSetting(reqInfo)
        return if (resp.isSuccess) {
            val data = resp.data
            if (data != null) {
                Resp.Success(data.userSetting, prompt = data.prompt)
            } else {
                Resp.Success(null, prompt = data?.prompt)
            }
        } else {
            Resp.Error(resp.code, resp.msg, resp.data?.prompt)
        }
    }

    override suspend fun getBotSettings(botUserId: Long): Resp<BotUserSetting?> {
        val resp = botService.getBotSetting(RequestGetBotSetting(botUserId))
        return if (resp.isSuccess) {
            val data = resp.data
            if (data != null) {
                Resp.Success(data.userSetting, prompt = data.prompt)
            } else {
                Resp.Success(null, prompt = data?.prompt)
            }
        } else {
            Resp.Error(resp.code, resp.msg, resp.data?.prompt)
        }
    }

    override suspend fun addBotToChat(botUserId: Long): Resp<ResponseAddChat?> {
        val resp = botService.addChat(RequestAddChat(botUserId))
        return if (resp.isSuccess) {
            val data = resp.data
            if (data != null) {
                Resp.Success(data, prompt = data.prompt)
            } else {
                Resp.Error(resp.code, resp.msg, resp.data?.prompt)
            }
        } else {
            Resp.Error(resp.code, resp.msg, resp.data?.prompt)
        }
    }

    override suspend fun addBotToChat(botUserIds: List<Long>): Resp<List<BotInfo>?> {
        val resp = botService.batchAddChat(RequestBatchAddChat(botUserIds))
        return if (resp.isSuccess) {
            val data = resp.data
            if (data != null) {
                Resp.Success(data.botInfoList, prompt = data.prompt)
            } else {
                Resp.Error(resp.code, resp.msg, resp.data?.prompt)
            }
        } else {
            Resp.Error(resp.code, resp.msg, resp.data?.prompt)
        }
    }
} 