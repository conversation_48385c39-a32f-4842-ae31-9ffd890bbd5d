package com.interfun.buz.social.repo

import com.interfun.buz.common.bean.Resp
import com.interfun.buz.social.db.entity.BuzOfficialAccountExtra
import com.interfun.buz.social.db.entity.BuzOfficialAccountInfo
import com.interfun.buz.social.db.entity.BuzUserComposite
import kotlinx.coroutines.flow.Flow


interface OfficialAccountRepository {

    fun init()
    suspend fun getOAInfo(userId: Long): BuzOfficialAccountInfo?
    fun getOAInfoFlow(userId: Long,isForceRefresh : Boolean = false): Flow<BuzOfficialAccountInfo?>
    fun getOAInfoFromCacheFlow(userId: Long): Flow<BuzOfficialAccountInfo?>
    suspend fun getOAInfoFromServer(userId: Long): Resp<BuzOfficialAccountInfo?>
    fun syncOAAccountInfo(userId: List<Long>)
    fun syncAllOAAccounts()
    suspend fun getAllMyOfficialAccountsFromCache(): List<BuzUserComposite>

    suspend fun getOAInfoFromCache(userId: Long): BuzOfficialAccountInfo?

    suspend fun getOAExtra(userId: Long): BuzOfficialAccountExtra?
    fun getOAExtraFlow(userId: Long, forceRefresh: Boolean = false): Flow<BuzOfficialAccountExtra?>
    fun getOAExtraFromCacheFlow(userId: Long): Flow<BuzOfficialAccountExtra?>

    suspend fun updateOAExtra(extra: BuzOfficialAccountExtra)

} 