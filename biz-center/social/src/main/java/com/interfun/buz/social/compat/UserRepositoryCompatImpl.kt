package com.interfun.buz.social.compat

import com.interfun.buz.common.bean.Resp
import com.interfun.buz.common.database.entity.UserRelationInfo
import com.interfun.buz.common.eventbus.QueryUserInfoSuccessEvent
import com.interfun.buz.common.manager.cache.user.UserRepoCompat
import com.interfun.buz.social.SocialBizCenterMediator
import com.interfun.buz.social.di.SocialSingletonEntryPoint
import com.interfun.buz.social.repo.UserRepository
import dagger.hilt.android.EntryPointAccessors
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlin.coroutines.cancellation.CancellationException


/**
 * only for old logic compatibility
 * !!!! Don't use it in new code !!!
 */
@Deprecated("use UserRepository instead,don't use it in new code.")
class UserRepositoryCompatImpl : UserRepoCompat{

    companion object {
        /**
         * @deprecated Use hilt to inject a UserRepository in UserComponent instead.
         *
         * Don't keep the reference of this object,because it will be change when user has been changed.
         * Should always use [userRepository] to get the UserRepository.
         */
        @Deprecated("Use hilt to inject a UserRepository in UserComponent instead.")
        val userRepository
            get() = EntryPointAccessors.fromApplication<SocialSingletonEntryPoint>(
                SocialBizCenterMediator.appContext
            ).userRepository()
    }

    override fun isUserMyFriend(userId: Long?): Boolean {
        userId ?: return false
        return userRepository.getUserRelationFromCacheNotSuspend(userId)?.isFriend == true
    }

    override suspend fun isUserOfficial(userId: Long?): Boolean {
        userId?: return false
        return userRepository.getUser(userId)?.isOfficial == true
    }

    override suspend fun isUserBlocked(userId: Long?): Boolean {
        userId ?: return false
        return userRepository.getUserRelationFromCacheNotSuspend(userId)?.isBlocked == true
    }

    override fun getUserRelationInfoByUid(uid: Long): UserRelationInfo? {
        if (uid <= 0L) {
            return null
        }
        val userRepository = userRepository
        val localData = userRepository.getUserCompositeFromCacheNotSuspend(uid)?.toOldUserRelationInfo()
        if (localData == null){
            //旧逻辑，需要处理没有缓存的情况,要拉数据，然后拉到后抛出这个事件：QueryUserInfoSuccessEvent
            userRepository.getUserScope().launch {
                userRepository.getUserCompositeFlow(uid).collect { userComposite ->
                    //抛出事件，结束协程
                    QueryUserInfoSuccessEvent.post(arrayListOf(userComposite.toOldUserRelationInfo()))
                    throw CancellationException("done")
                }
            }
        }
        return localData
    }

    override suspend fun getUserRelationInfoByUidSync(uid: Long): UserRelationInfo? {
        return userRepository.getUserComposite(uid)?.toOldUserRelationInfo()
    }

    override suspend fun getUserFromServer(userId: Long): UserRelationInfo? {
        val resp = userRepository.getUserCompositeFromServer(userId)
        return (resp as? Resp.Success)?.data?.toOldUserRelationInfo()
    }

    override suspend fun getUserRelationInfoFromCacheSync(uid: Long): UserRelationInfo? {
        return userRepository.getUserCompositeFromCache(uid)?.toOldUserRelationInfo()
    }

    override fun getUserRelationInfoFromCache(uid: Long): UserRelationInfo? {
        return userRepository.getUserCompositeFromCacheNotSuspend(uid)?.toOldUserRelationInfo()
    }

    override suspend fun getUserRelationInfoFromCacheSuspend(uid: Long) : UserRelationInfo? {
        return userRepository.getUserCompositeFromCache(uid)?.toOldUserRelationInfo()
    }

    override fun getUserRelationFromMem(uid: Long): UserRelationInfo? {
        return userRepository.getUserCompositeFromMemory(uid)?.toOldUserRelationInfo()
    }

}