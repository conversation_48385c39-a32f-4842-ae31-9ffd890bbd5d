package com.interfun.buz.social.datasource

import com.interfun.buz.base.ktx.awaitInScopeIO
import com.interfun.buz.social.db.entity.GroupBotUserEntity
import com.interfun.buz.component.hilt.UserQualifier
import com.interfun.buz.social.db.dao.GroupUserMemberDao
import com.interfun.buz.social.entity.GroupMemberUserEntity
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

internal class GroupMembersLocalDataSourceImpl @Inject constructor(
    @UserQualifier val groupUserMemberDao: GroupUserMemberDao,
    @UserQualifier val userScope: CoroutineScope
) : GroupMembersLocalDataSource {
    companion object {
        const val TAG = "LocalGroupMembersDataSource"
    }

    override suspend fun insertUserMembers(members: List<GroupMemberUserEntity>) {
        awaitInScopeIO(userScope) {
            groupUserMemberDao.insertUserMembers(members)
        }
    }

    override suspend fun insertBotMembers(members: List<GroupBotUserEntity>) {
        awaitInScopeIO(userScope) {
            groupUserMemberDao.insertBotMembers(members)
        }
    }

    override suspend fun replaceAllUserMembers(
        groupId: Long,
        newMembers: List<GroupMemberUserEntity>
    ) {
        return awaitInScopeIO(userScope) {
            groupUserMemberDao.replaceAllUserMembers(groupId, newMembers)
        }
    }

    override suspend fun replaceAllBots(groupId: Long, newBots: List<GroupBotUserEntity>) {
        awaitInScopeIO(userScope) {
            groupUserMemberDao.replaceAllBotMembers(groupId, newBots)
        }
    }


    override suspend fun deleteGroupUserMembers(groupId: Long, userIds: List<Long>) {
        awaitInScopeIO(userScope) {
            groupUserMemberDao.deleteUserMembers(groupId, userIds)
        }
    }

    override suspend fun deleteGroupBotMembers(groupId: Long, botIds: List<Long>) {
        awaitInScopeIO(userScope) {
            groupUserMemberDao.deleteRobotMembers(groupId, botIds)
        }
    }

    override suspend fun deleteGroupMembersByGroupId(groupId: Long) {
        awaitInScopeIO(userScope) {
            groupUserMemberDao.deleteGroupAllUserMembers(groupId)
        }
    }

    override fun getGroupUserMembers(groupId: Long): List<GroupMemberUserEntity> {
        return groupUserMemberDao.getGroupUserMembers(groupId)
    }

    override fun getGroupUserMembersFlow(
        groupId: Long,
        count: Int?
    ): Flow<List<GroupMemberUserEntity>> {
        return if (count != null) {
            groupUserMemberDao.getGroupUserMemberLimitFlow(groupId, count)
        } else {
            groupUserMemberDao.getGroupUserMemberFlow(groupId)
        }
    }

    override fun getGroupBotMembersFlow(groupId: Long): Flow<List<GroupBotUserEntity>> {
        return groupUserMemberDao.getGroupBotMemberListFlow(groupId)
    }

    override suspend fun getGroupBotMembers(groupId: Long): List<GroupBotUserEntity> {
        return awaitInScopeIO(userScope) {
            groupUserMemberDao.getGroupBotMemberList(groupId)
        }
    }
}