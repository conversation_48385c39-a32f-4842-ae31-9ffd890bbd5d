package com.interfun.buz.social.db.dao

import androidx.room.*
import com.interfun.buz.social.db.entity.*
import com.interfun.buz.social.db.entity.BuzRelationAndUser
import com.interfun.buz.social.db.entity.partial.UserBlock
import kotlinx.coroutines.flow.Flow

@Dao
interface UserDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertUser(buzUser: BuzUser)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertUser(buzUser: List<BuzUser>)

    @Query("update buz_user set isAccountDeleted = :deleted where userId = :userId")
    fun updateUserDeleted(userId: Long, deleted: Boolean) : Int

    @Transaction
    fun markUserAccountDeleted(userId: Long): BuzUser? {
        val updated = updateUserDeleted(userId, true) > 0
        if (updated) {
            return queryUser(userId)
        }
        val newUser = BuzUser(userId = userId, isAccountDeleted = true)
        insertUser(newUser)
        return newUser
    }

    @Query("select * from buz_user where userId = :userId")
    fun queryUser(userId: Long): BuzUser?

    @Query("select * from buz_user where userId in (:userId)")
    fun queryUser(userId: List<Long>): List<BuzUser>?


    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertUserRelation(userRelationEntity: BuzUserRelationEntity)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertUserRelation(userRelationEntity: List<BuzUserRelationEntity>)

    @Insert(onConflict = OnConflictStrategy.IGNORE)
    fun insertUserRelationOrIgnore(userRelationEntity: BuzUserRelationEntity)

    @Query("update buz_user_relation set relation = :relation where userId = :userId")
    fun updateUserRelation(userId: Long, relation: Int): Int

    @Query("update buz_user_relation set muteMessages = :muteMessages where userId = :userId")
    fun updateMuteMessagesSetting(userId: Long, muteMessages: Int)

    @Query("update buz_user_relation set muteNotification = :muteNotification where userId = :userId")
    fun updateMuteNotificationSetting(userId: Long, muteNotification: Int)

    @Query("update buz_user_relation set remark = :remark where userId = :userId")
    fun updateRemarkSetting(userId: Long, remark: String)

    @Upsert(entity = BuzUserRelationEntity::class)
    fun upsertBlocked(userIds: List<UserBlock>)

    @Transaction
    fun updateUserRelationSettings(
        userId: Long,
        muteMessages: Int? = null,
        muteNotification: Int? = null,
        remark: String? = null
    ): BuzUserRelationEntity? {
        //如果本身没有存在关系表的话不更新，这种场景是有问题的
        muteMessages?.let { updateMuteMessagesSetting(userId, it) }
        muteNotification?.let { updateMuteNotificationSetting(userId, it) }
        remark?.let { updateRemarkSetting(userId, it) }
        return queryUserRelation(userId)
    }

    @Transaction
    fun updateOrInsertUserRelation(userId: Long, relation: Int) {
        val updated = updateUserRelation(userId, relation) > 0
        if (!updated) {
            insertUserRelationOrIgnore(BuzUserRelationEntity(userId, relation = relation))
        }
    }

    @Transaction
    fun updateOrInsertUserRelation(userAndRelation: List<Pair<Long, Int>>) {
        userAndRelation.forEach { (userId, relation) ->
            updateOrInsertUserRelation(userId, relation)
        }
    }

    /**
     * @return changed list
     */
    @Transaction
    fun replaceAllBlockList(newBlockList: List<Long>) : List<Long> {
        val savedBlockList = getBlockUserIdsFromCache().toHashSet()
        val unBlockListIds = savedBlockList - newBlockList.toHashSet()
        val blockUserList = newBlockList.map { UserBlock(it,true) } + unBlockListIds.map { UserBlock(it,false) }
        upsertBlocked(blockUserList)
        return newBlockList + unBlockListIds
    }

    @Query("select * from buz_user_relation where userId = :userId")
    fun queryUserRelation(userId: Long): BuzUserRelationEntity?

    @Query("select * from buz_user_relation where userId in (:userId)")
    fun queryUserRelation(userId: List<Long>): List<BuzUserRelationEntity>?

    @Query("select count(*) from buz_user_relation where relation = 1 and isBlocked != 1")
    fun getFriendSize(): Int


    @Transaction
    @Query("select * from buz_user_relation where relation = 1 and isBlocked!= 1 order by friendTime desc")
    fun getAllFriends(): List<BuzRelationAndUser>

    @Query("select userId from buz_user_relation where relation = 1 and isBlocked!= 1 order by friendTime desc")
    fun getAllFriendIdsFlow(): Flow<List<Long>>

    @Query("select userId from buz_user_relation where isBlocked = 1")
    fun getBlockUserIdsFromCacheFlow(): Flow<List<Long>>

    @Query("select userId from buz_user_relation where isBlocked = 1")
    fun getBlockUserIdsFromCache(): List<Long>

    @Query("select userId from buz_user_relation where relation = 1 and isBlocked!= 1 order by friendTime desc")
    fun getAllFriendsIds(): List<Long>

    @Transaction
    @Query("select * from buz_user inner join buz_user_relation on buz_user.userId = buz_user_relation.userId where buz_user_relation.relation = 1 and buz_user_relation.isBlocked!= 1 and (buz_user.userType = 2 or buz_user.userType = 3)")
    fun getAllOfficialFriends(): List<BuzUserComposite>

    @Transaction
    fun insertUserAndRelation(buzUser: BuzUser, userRelationEntity: BuzUserRelationEntity?) {
        insertUser(buzUser)
        userRelationEntity?.let { insertUserRelation(it) }
    }

    @Transaction
    fun insertUserAndRelation(
        buzUsers: List<BuzUser>,
        userRelationEntities: List<BuzUserRelationEntity>
    ) {
        insertUser(buzUsers)
        insertUserRelation(userRelationEntities)
    }

    @Query("delete from buz_user")
    fun clearBuzUserTable()


    @Query("select * from buz_user_un_uploaded_setting")
    fun loadAllUnLoadedSettings(): List<UserUnUploadedSetting>


    @Query("delete from buz_user_un_uploaded_setting where userId = :userId")
    fun deleteUserUnUploadedSetting(userId: Long)

    @Query("select * from buz_user_un_uploaded_setting where userId = :userId")
    fun queryUserUnUploadedSettings(userId: Long): UserUnUploadedSetting?

    @Query("update buz_user_un_uploaded_setting set muteMessages = :muteMessages where userId = :userId")
    fun updateUnUploadedMuteMessagesSetting(userId: Long, muteMessages: Int?)

    @Query("update buz_user_un_uploaded_setting set muteNotification = :muteNotification where userId = :userId")
    fun updateUploadedMuteNotificationSetting(userId: Long, muteNotification: Int?)

    @Query("update buz_user_un_uploaded_setting set remark = :remark where userId = :userId")
    fun updateUploadedRemarkSetting(userId: Long, remark: String?)

    @Insert(onConflict = OnConflictStrategy.IGNORE)
    fun insertUnUploadedUserRelationSettingsOrIgnore(setting: UserUnUploadedSetting)

    @Transaction
    fun insertOrUpdateUnUploadedUserRelationSettings(
        userId: Long,
        muteMessages: Int? = null,
        muteNotification: Int? = null,
        remark: String? = null
    ): UserUnUploadedSetting? {
        val savedSettings = queryUserUnUploadedSettings(userId)
        if (savedSettings == null) {
            val newSettings = UserUnUploadedSetting(
                userId,
                muteMessages,
                muteNotification,
                remark
            )
            insertUnUploadedUserRelationSettingsOrIgnore(newSettings)
            return newSettings
        }
        muteMessages?.let { updateUnUploadedMuteMessagesSetting(userId, it) }
        muteNotification?.let { updateUploadedMuteNotificationSetting(userId, it) }
        remark?.let { updateUploadedRemarkSetting(userId, it) }
        return queryUserUnUploadedSettings(userId)
    }

    @Transaction
    fun setUpLoadUserRelationSettingsNull(
        userId: Long,
        setMuteMessagesNull: Boolean = false,
        setMuteNotificationNull: Boolean = false,
        setRemarkNull: Boolean = false
    ): UserUnUploadedSetting? {
        if (setMuteMessagesNull) {
            updateUnUploadedMuteMessagesSetting(userId, null)
        }
        if (setMuteNotificationNull) {
            updateUploadedMuteNotificationSetting(userId, null)
        }
        if (setRemarkNull) {
            updateUploadedRemarkSetting(userId, null)
        }
        return queryUserUnUploadedSettings(userId)
    }


}