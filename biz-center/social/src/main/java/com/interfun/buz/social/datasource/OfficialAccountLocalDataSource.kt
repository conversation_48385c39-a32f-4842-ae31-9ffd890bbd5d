package com.interfun.buz.social.datasource

import com.interfun.buz.social.db.entity.BuzOfficialAccountExtra
import kotlinx.coroutines.flow.Flow

internal interface OfficialAccountLocalDataSource {
    suspend fun getOfficialAccountExtra(userId: Long): BuzOfficialAccountExtra?
    fun getOfficialAccountExtraFlow(userId: Long): Flow<BuzOfficialAccountExtra?>
    suspend fun updateOfficialAccountExtra(extra: BuzOfficialAccountExtra) : BuzOfficialAccountExtra
    suspend fun replaceAllMyOfficialAccounts(ids: List<Long>)
    suspend fun getAllMyOfficialAccountIds(): List<Long>
}