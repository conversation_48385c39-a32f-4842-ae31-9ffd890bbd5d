package com.interfun.buz.social.db.entity

import android.os.Parcelable
import androidx.room.*
import com.interfun.buz.common.widget.portrait.group.GroupPortrait
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize

/**
 * BuzGroup 跟 BuzGroupTable 分开，因为部分数据存数据库的，但是为了方便业务使用，将数据库的内容解析出来后，
 * 还要处理一遍，所以将处理后的内容放到BuzGroup，职责上更清晰
 */
@Parcelize
data class BuzGroup(
    val groupId: Long,
    val serverGroupName: String? = "",
    //之前的表里面会存这个到数据库，现在不存了，改成每次读的时候取获取
    val groupName: String = "",
    val portraitUrl: String? = "",
    val firstFewPortraits: List<String?>? = listOf(),
    val memberNum: Int = 0,
    val maxMemberNum: Int = 0,
    val groupStatus: Int = 1,
    val groupType: Int = 1, //1 - 普通群,  2 - 大群
    val serverPortraitUrl: String? = ""
) : Parcelable {

    fun toGroupPortrait(): GroupPortrait {
        return GroupPortrait(groupId, firstFewPortraits, portraitUrl)
    }

    @IgnoredOnParcel
    val isBigGroup get() = groupType == 2
}
