package com.interfun.buz.social.datasource

import com.interfun.buz.social.db.entity.BotExtra
import com.interfun.buz.social.db.entity.BotExtra.BotStatus
import com.interfun.buz.social.db.entity.BotWholeSettingEntity
import kotlinx.coroutines.flow.Flow

internal interface BotLocalDataSource {
    suspend fun getBotExtra(botUserId: Long): BotExtra?
    @Deprecated("use getBotExtra instead")
    fun getBotExtraFromMemory(botUserId: Long): BotExtra?

    fun loadAllBotExtraFromCache()

    fun loadAllBotSettingsFromCache()

    suspend fun getBotExtra(botUserIds: List<Long>): List<BotExtra?>

    fun getBotExtraFlow(botUserId: Long): Flow<BotExtra?>

    fun getBotExtraFlow(botUserIds: List<Long>): Flow<Map<Long, BotExtra>>

    suspend fun updateBotExtra(botInfo: BotExtra): BotExtra

    suspend fun updateBotExtraList(botInfoList: List<BotExtra>): List<BotExtra>

    suspend fun updateBotStatus(botUserId: Long, botStatus: BotStatus)

    suspend fun updateMarketBotList(botUserIdList: List<Long>)

    fun getMarketBotListFlow(): Flow<List<Long>?>

    fun getHasNewBotInMarketFlow(): Flow<Boolean>

    suspend fun clearNewBotInMarketFlag()

    suspend fun getBotSettings(botUserId: Long): BotWholeSettingEntity?

    @Deprecated("use getBotSettings instead")
    fun getBotSettingsFromMemory(botUserId: Long): BotWholeSettingEntity?

    fun getBotSettingsFlow(botUserId: Long): Flow<BotWholeSettingEntity?>

    suspend fun updateBotSettings(
        botUserId: Long,
        languageCode: String? = null,
        voiceStyleId: Long? = null,
        sourceLanguage: String? = null,
        targetLanguage: String? = null,
    ): BotWholeSettingEntity?

    suspend fun switchSourceAndTargetLanguage(botUserId: Long): BotWholeSettingEntity?

    fun getRecentlyUsedTranslatorLanguage(botUserId: Long): Flow<List<String>?>

    suspend fun deleteBotInfo(botUserId: Long)
} 