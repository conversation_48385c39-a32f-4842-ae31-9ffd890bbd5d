package com.interfun.buz.social.compat

import com.interfun.buz.common.bean.Resp
import com.interfun.buz.common.bean.Resp.Error
import com.interfun.buz.common.bean.Resp.Success
import com.interfun.buz.common.database.entity.UserRelationInfo
import com.interfun.buz.common.manager.cache.ai.GroupBotMemberRepoCompat
import com.interfun.buz.social.SocialBizCenterMediator
import com.interfun.buz.social.di.SocialSingletonEntryPoint
import dagger.hilt.android.EntryPointAccessors

@Deprecated("兼容旧代码，请别使用")
class GroupBotMemberRepoCompatImpl : GroupBotMemberRepoCompat {

    /**
     * @deprecated Use hilt to inject a GroupMembersRepository in UserComponent instead.
     *
     * Don't keep the reference of this object,because it will be change when user has been changed.
     * Should always use [groupMembersRepository] to get the UserRepository.
     */
    @Deprecated("兼容旧代码，新代码请使用hilt inject GroupMembersRepository")
    val groupMembersRepository
        get() = EntryPointAccessors.fromApplication<SocialSingletonEntryPoint>(
            SocialBizCenterMediator.appContext
        ).getGroupMembersRepository()

    override suspend fun getFromCache(groupId: Long): List<UserRelationInfo>? {
        return groupMembersRepository.getGroupBotMemberFromCache(groupId).mapNotNull { it.buzUserComposite?.toOldUserRelationInfo() }
    }

    override suspend fun getFromServer(groupId: Long): Resp<List<UserRelationInfo>?> {
        val resp = groupMembersRepository.getGroupBotMembersFromServer(groupId)
        return when (resp) {
            is Error -> Resp.Error(resp.code, resp.msg, resp.prompt)
            is Success -> {
                val data = resp.data.mapNotNull { it.buzUserComposite?.toOldUserRelationInfo() }
                Resp.Success(data, resp.prompt)
            }
        }
    }
}