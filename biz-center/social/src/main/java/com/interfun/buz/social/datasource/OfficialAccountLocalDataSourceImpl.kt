package com.interfun.buz.social.datasource

import androidx.collection.LruCache
import com.interfun.buz.base.coroutine.withReentrantLock
import com.interfun.buz.base.ktx.awaitInScopeIO
import com.interfun.buz.component.hilt.UserQualifier
import com.interfun.buz.social.db.dao.OfficialAccountDao
import com.interfun.buz.social.db.entity.BuzOfficialAccountExtra
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.sync.Mutex
import javax.inject.Inject

internal class OfficialAccountLocalDataSourceImpl @Inject constructor(
    @UserQualifier private val officialAccountDao: OfficialAccountDao,
    @UserQualifier private val userScope: CoroutineScope
) : OfficialAccountLocalDataSource {
    
    private val officialAccountExtraLruCache = LruCache<Long, BuzOfficialAccountExtra>(1000)
    private val officialAccountExtraUpdateFlow = MutableSharedFlow<Long>()
    private val officialAccountMutex = Mutex()

    override suspend fun getOfficialAccountExtra(userId: Long): BuzOfficialAccountExtra? {
        return officialAccountExtraLruCache[userId] ?: queryOfficialAccountExtraFromDb(userId)
    }

    override fun getOfficialAccountExtraFlow(userId: Long): Flow<BuzOfficialAccountExtra?> =
        officialAccountExtraUpdateFlow.onSubscription { emit(userId) }
            .filter { it == userId }
            .mapLatest { getOfficialAccountExtra(userId) }
            .distinctUntilChanged()
            .flowOn(Dispatchers.Default)


    override suspend fun updateOfficialAccountExtra(extra: BuzOfficialAccountExtra): BuzOfficialAccountExtra {
        return awaitInScopeIO(userScope) {
            officialAccountMutex.withReentrantLock {
                officialAccountDao.updateExtra(extra)
                officialAccountExtraLruCache.put(extra.userId, extra)
            }
            officialAccountExtraUpdateFlow.emit(extra.userId)
            return@awaitInScopeIO extra
        }
    }

    override suspend fun replaceAllMyOfficialAccounts(ids: List<Long>) {
        awaitInScopeIO(userScope) {
            officialAccountDao.replaceAllMyOfficialAccountIds(ids)
        }
    }

    override suspend fun getAllMyOfficialAccountIds(): List<Long> {
        return awaitInScopeIO(userScope) {
            officialAccountDao.getAllMyOfficialAccountIds()
        }
    }

    private suspend fun queryOfficialAccountExtraFromDb(userId: Long): BuzOfficialAccountExtra? {
        return awaitInScopeIO(userScope) {
            officialAccountMutex.withReentrantLock {
                officialAccountDao.queryOfficialExtra(userId)?.apply {
                    officialAccountExtraLruCache.put(userId, this)
                }
            }
        }
    }

}