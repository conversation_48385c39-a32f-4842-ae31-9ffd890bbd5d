package com.interfun.buz.social.repo

import androidx.annotation.Keep
import com.buz.idl.user.bean.UserInfo
import com.interfun.buz.base.ktx.and
import com.interfun.buz.base.ktx.log
import com.interfun.buz.base.ktx.logError
import com.interfun.buz.common.bean.push.PushOP
import com.interfun.buz.common.utils.fromJson
import com.interfun.buz.component.hilt.UserQualifier
import com.interfun.buz.signal.ISignalManagerPresenter
import com.interfun.buz.social.datasource.UserLocalDataSource
import com.interfun.buz.social.entity.QuietMode
import com.interfun.buz.social.entity.UserOnlineStatus
import com.interfun.buz.social.entity.UserStateInfo
import com.interfun.buz.social.entity.convertStatusToState
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject

internal class UserOnlineStatusRepositoryImpl @Inject constructor(
    @UserQualifier private val userLocalDS: UserLocalDataSource,
    @UserQualifier private val userScope: CoroutineScope,
    private val signalManager: ISignalManagerPresenter
) : UserOnlineStatusRepository {

    private val TAG = "UserOnlineStatusRepository"
    private val userOnlineStatusMap: MutableMap<Long, UserOnlineStatus> = ConcurrentHashMap()
    private val updateFlow = MutableSharedFlow<Set<Long>>()

    override fun init() {
        observeSignal()
    }

    private fun observeSignal() {
        userScope.launch {
            var lastTime = 0L
            signalManager.obtainSignalingFlow()
                .filter { it.topic == PushOP.PushFriendListStatus.op && it.sendTimeStamp >= lastTime }
                .collect { pushData ->
                    try {
                        val statusList = pushData.data.toString()
                            .fromJson<FriendStatusServerInfoList>()?.friendStatusInfoList?.map {
                                UserOnlineStatus(
                                    it.userId,
                                    it.quietMode,
                                    it.walkieTalkieOnlineTime,
                                    it.latestActiveTime
                                )
                            }
                        log(TAG, "friend status update from signal push: list:$statusList")
                        statusList?.let { updateStatusInternal(it) }
                        lastTime = pushData.sendTimeStamp
                    } catch (e: Exception) {
                        logError(TAG, e, "parse push data error:${e.message}")
                    }
                }
        }
    }

    override fun getStatusInfo(userId: Long): UserOnlineStatus? {
        return userOnlineStatusMap[userId]
    }

    override fun getStatusInfoFlow(userId: Long): Flow<UserOnlineStatus?> =
        updateFlow.onSubscription { emit(hashSetOf(userId)) }
            .filter { it.contains(userId) }
            .map { userOnlineStatusMap[userId] }

    override fun getStatusInfoFlow(userIds: List<Long>): Flow<Map<Long, UserOnlineStatus>> = flow {
        val userIdSet = userIds.toHashSet()
        val map = mutableMapOf<Long, UserOnlineStatus>()
        val intersectionUpdateFlow = updateFlow.mapNotNull { updateSet ->
            val intersectionSet = updateSet and userIdSet
            intersectionSet.ifEmpty { null }
        }
        intersectionUpdateFlow.onStart { emit(userIdSet) }
            .collect { changedIdSet ->
                changedIdSet.forEach { userId ->
                    map[userId] = userOnlineStatusMap[userId]
                        ?: UserOnlineStatus(
                            userId = userId,
                            quietMode = QuietMode.Disable.value,
                            onlineTime = 0,
                            offlineTime = 0
                        )
                }
                emit(map.toMap())
            }
    }

    override fun getOnlineTime(userId: Long): Long? {
        return getStatusInfo(userId)?.onlineTime
    }

    override fun isOnline(userId: Long): Boolean {
        return getStatusInfo(userId)?.isOnline ?: false
    }

    override fun isOnlineFlow(userId: Long): Flow<Boolean> =
        updateFlow.onSubscription { emit(hashSetOf(userId)) }
            .filter { it.contains(userId) }
            .map { isOnline(userId) }

    override fun isInQuietMode(userId: Long): Boolean {
        return getStatusInfo(userId)?.isInQuietMode ?: false
    }

    override fun isInQuietModeFlow(userId: Long): Flow<Boolean> =
        updateFlow.onSubscription { emit(hashSetOf(userId)) }
            .filter { it.contains(userId) }
            .map { isInQuietMode(userId) }

    override suspend fun updateStatus(userInfo: UserInfo?) {
        userInfo ?: return
        updateStatusInternal(
            listOfNotNull(
                UserOnlineStatus(
                    userInfo.userId ?: 0,
                    userInfo.quietMode ?: QuietMode.Disable.value,
                    userInfo.walkieTalkieOnlineTime ?: 0
                )
            )
        )
    }

    override suspend fun updateStatus(userInfos: List<UserInfo>) {
        updateStatusInternal(userInfos.map { userInfo ->
            UserOnlineStatus(
                userInfo.userId ?: 0,
                userInfo.quietMode ?: QuietMode.Disable.value,
                userInfo.walkieTalkieOnlineTime ?: 0
            )
        })
    }

    private suspend fun updateStatusInternal(statusList: List<UserOnlineStatus>) {
        val updatedUsers = mutableSetOf<Long>()
        for (status in statusList) {
            val userId = status.userId
            val relation = userLocalDS.getUserRelation(userId)
            val newStatus = if (relation?.isFriend == true) {
                status
            } else {
                null
            }
            val oldStatus = getStatusInfo(userId)
            if (oldStatus != newStatus) {
                if (newStatus == null) {
                    userOnlineStatusMap.remove(userId)
                } else {
                    userOnlineStatusMap[userId] = newStatus
                }
                updatedUsers.add(userId)
            }
        }
        updateFlow.emit(updatedUsers)
    }

    override suspend fun clearStatus(userId: Long) {
        userOnlineStatusMap.remove(userId)
    }

    override fun getUpdateFlow(): Flow<Set<Long>> {
        return updateFlow.asSharedFlow()
    }

    override fun getTotalOnlineFriendNum(): Int {
        return userOnlineStatusMap.values.count { it.isOnline }
    }

    override fun getUserStateInfoFlow(userId: Long): Flow<UserStateInfo?> =
        getStatusInfoFlow(userId).map { it ->
            if (it == null) return@map null
            it.convertStatusToState()
        }


    override fun getUserStateInfoFlow(userIds: List<Long>): Flow<Map<Long, UserStateInfo>> {
        return getStatusInfoFlow(userIds).map { statusMap ->
            statusMap.mapValues { (_, status) ->
                status.convertStatusToState()
            }
        }
    }


    @Keep
    data class FriendStatusServerInfo(
        val userId: Long,
        val quietMode: Int, //用户状态 0-客户端忽略 1-Available 2-Quiet
        val walkieTalkieOnlineTime: Long,
        val latestActiveTime: Long, //最近活跃时间戳（毫秒）离线用户
        val statusChangeType: Int //变更类型 1-上线 2-下线 3-QuiteMode变更
    )

    @Keep
    data class FriendStatusServerInfoList(
        @Keep
        val friendStatusInfoList: List<FriendStatusServerInfo>
    )
}