package com.interfun.buz.social.storage

import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.longPreferencesKey
import androidx.datastore.preferences.core.stringPreferencesKey

object SocialDataStoreKey {
    val aiMarketUserListKey = stringPreferencesKey("aiMarketUserList")
    val hasNewBotInMarket = booleanPreferencesKey("hasNewBotInMarket")
    val lastGetFriendListTimeStamp = longPreferencesKey("lastGetFriendListTimeStamp")

    fun recentlyUsedTranslatorLanguageKey(botId: Long) =
        stringPreferencesKey("recentlyUsedTranslatorLanguage_$botId")
}