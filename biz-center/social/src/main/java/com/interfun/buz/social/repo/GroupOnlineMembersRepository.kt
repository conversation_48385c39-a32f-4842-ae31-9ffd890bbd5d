package com.interfun.buz.social.repo

import com.interfun.buz.common.bean.Resp
import com.interfun.buz.social.db.entity.BuzUser
import com.interfun.buz.social.entity.GroupOnlineMembersInfo
import kotlinx.coroutines.flow.Flow

interface GroupOnlineMembersRepository {
    /**
     * 在线群信息，从缓存获取，如果没有缓存则没有数据，内部不会主动刷新，需要刷新需要自行调用refreshOnlineGroupMembers
     */
    fun getOnlineGroupMembersFromCache(groupIds: List<Long>): Flow<Map<Long, GroupOnlineMembersInfo?>>

    fun getOnlineGroupMembersFromCache(groupId: Long): Flow<GroupOnlineMembersInfo?>

    suspend fun refreshOnlineGroupMembers(groupId: Long) : Resp<GroupOnlineMembersInfo?>
}