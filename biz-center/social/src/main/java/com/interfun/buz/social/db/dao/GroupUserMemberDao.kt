package com.interfun.buz.social.db.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import com.interfun.buz.social.db.entity.GroupBotUserEntity
import com.interfun.buz.social.entity.GroupMemberUserEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface GroupUserMemberDao {
    @Insert(onConflict = OnConflictStrategy.Companion.REPLACE)
    suspend fun insertUserMembers(groupBotUserEntity: List<GroupMemberUserEntity>)

    @Insert(onConflict = OnConflictStrategy.Companion.REPLACE)
    suspend fun insertBotMembers(members: List<GroupBotUserEntity>)

    @Transaction
    suspend fun replaceAllUserMembers(groupId: Long, members: List<GroupMemberUserEntity>) {
        deleteGroupAllUserMembers(groupId)
        insertUserMembers(members)
    }

    @Transaction
    suspend fun replaceAllBotMembers(groupId: Long,members: List<GroupBotUserEntity>){
        deleteGroupAllBotMembers(groupId)
        insertBotMembers(members)
    }

    @Query("DELETE FROM group_bot_table WHERE groupId = :groupId AND botUserId IN (:userIds)")
    suspend fun deleteRobotMembers(groupId: Long,userIds: List<Long>)

    @Query("DELETE FROM group_member_user_table WHERE groupId = :groupId AND userId IN (:userIds)")
    suspend fun deleteUserMembers(groupId: Long,userIds: List<Long>)

    @Query("DELETE FROM group_member_user_table WHERE groupId = :groupId")
    suspend fun deleteGroupAllUserMembers(groupId: Long)

    @Query("DELETE FROM group_bot_table WHERE groupId = :groupId")
    suspend fun deleteGroupAllBotMembers(groupId: Long)

    @Query("SELECT * FROM group_member_user_table WHERE groupId = :groupId ORDER BY timeStamp ASC")
    fun getGroupUserMembers(groupId: Long): List<GroupMemberUserEntity>

    @Query("SELECT * FROM group_member_user_table WHERE groupId = :groupId ORDER BY timeStamp ASC")
    fun getGroupUserMemberFlow(groupId: Long): Flow<List<GroupMemberUserEntity>>

    @Query("SELECT * FROM group_member_user_table WHERE groupId = :groupId ORDER BY timeStamp ASC LIMIT :limitCount")
    fun getGroupUserMemberLimitFlow(
        groupId: Long,
        limitCount: Int
    ): Flow<List<GroupMemberUserEntity>>

    @Query("SELECT * FROM group_bot_table WHERE groupId = :groupId ORDER BY joinGroupTime ASC")
    fun getGroupBotMemberListFlow(groupId: Long): Flow<List<GroupBotUserEntity>>

    @Query("SELECT * FROM group_bot_table WHERE groupId = :groupId ORDER BY joinGroupTime ASC")
    fun getGroupBotMemberList(groupId: Long): List<GroupBotUserEntity>
}