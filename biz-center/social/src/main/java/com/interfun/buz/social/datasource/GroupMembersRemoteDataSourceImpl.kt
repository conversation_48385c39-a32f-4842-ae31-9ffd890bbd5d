package com.interfun.buz.social.datasource

import com.buz.idl.bot.request.RequestBotExitGroup
import com.buz.idl.bot.request.RequestBotJoinGroup
import com.buz.idl.bot.response.ResponseBotGroupChange
import com.buz.idl.bot.service.BuzNetBotService
import com.buz.idl.group.bean.GroupMember
import com.buz.idl.group.request.RequestGetGroupMembers
import com.buz.idl.group.request.RequestGetGroupOnlineMembers
import com.buz.idl.group.request.RequestInviteToJoinGroup
import com.buz.idl.group.request.RequestKickOutGroup
import com.buz.idl.group.response.ResponseGetGroupMembers
import com.buz.idl.group.response.ResponseGetGroupOnlineMembers
import com.buz.idl.group.service.BuzNetGroupService
import com.interfun.buz.common.bean.Resp
import com.interfun.buz.common.database.entity.chat.isBigGroup
import com.interfun.buz.common.ktx.isSuccess
import com.interfun.buz.common.manager.cache.group.GroupInfoCacheManager
import com.interfun.buz.common.utils.PromptUtil
import com.interfun.buz.component.hilt.UserQualifier
import com.interfun.buz.social.entity.InviteUserToGroupResult
import javax.inject.Inject

internal class GroupMembersRemoteDataSourceImpl @Inject constructor(
    @UserQualifier private val groupService: BuzNetGroupService,
    @UserQualifier private val botService: BuzNetBotService
) : GroupMembersRemoteDataSource {

    override suspend fun requestGroupMemberList(
        groupId: Long,
        queryParams: String?
    ): Resp<ResponseGetGroupMembers> {
        val isBigGroup = GroupInfoCacheManager.getGroupInfoBeanById(groupId)?.isBigGroup ?: false
        val serverResp = groupService.getGroupMembers(
            RequestGetGroupMembers(
                groupId = groupId,
                queryType = if (isBigGroup) 1 else 0,
                queryParams = queryParams
            )
        )
        val data = serverResp.data
        PromptUtil.parse(data?.prompt)
        if (serverResp.isSuccess && data != null) {
            return Resp.Success(data, data.prompt)
        }
        return Resp.Error(serverResp.code, serverResp.msg, data?.prompt)
    }

    override suspend fun kickOutGroupMembers(groupId: Long, userIds: List<Long>): Resp<Boolean> {
        val serverResp = groupService.kickOutGroup(
            RequestKickOutGroup(groupId, userIds)
        )
        return if (serverResp.isSuccess) {
            Resp.Success(true, serverResp.data?.prompt)
        } else {
            Resp.Error(serverResp.code, serverResp.msg, serverResp.data?.prompt)
        }
    }

    override suspend fun kickOutGroupBots(groupId: Long, botIds: List<Long>): Resp<List<GroupMember>?> {
        val serverResp = botService.botKickOutGroup(RequestBotExitGroup(botIds, groupId))
        return if (serverResp.isSuccess) {
            Resp.Success(serverResp.data?.botMemberMap?.get(groupId.toString()), serverResp.data?.prompt)
        } else {
            Resp.Error(serverResp.code, serverResp.msg, serverResp.data?.prompt)
        }
    }


    override suspend fun addBotToGroup(
        botUserIds: List<Long>,
        groupIds: List<Long>
    ): Resp<ResponseBotGroupChange?> {
        val resp = botService.botJoinGroup(RequestBotJoinGroup(botUserIds, groupIds))
        return if (resp.isSuccess) {
            Resp.Success(resp.data, resp.data?.prompt)
        } else {
            Resp.Error(resp.code, resp.msg, resp.data?.prompt)
        }
    }

    override suspend fun inviteUserToGroup(groupId: Long, userIds: List<Long>): Resp<InviteUserToGroupResult> {
        val serverResp = groupService.inviteToJoinGroup(RequestInviteToJoinGroup(groupId, userIds))
        return if (serverResp.isSuccess) {
            Resp.Success(InviteUserToGroupResult(serverResp.data?.rejectedMsg), serverResp.data?.prompt)
        } else {
            Resp.Error(serverResp.code, serverResp.msg, serverResp.data?.prompt)
        }
    }

    override suspend fun requestOnlineGroupMembers(groupId: Long): Resp<ResponseGetGroupOnlineMembers?> {
        val serverResp =
            groupService.getGroupOnlineMembers(RequestGetGroupOnlineMembers(groupId, 1))
        return if (serverResp.isSuccess) {
            Resp.Success(serverResp.data)
        } else {
            Resp.Error(serverResp.code, serverResp.msg, serverResp.data?.prompt)
        }
    }
}