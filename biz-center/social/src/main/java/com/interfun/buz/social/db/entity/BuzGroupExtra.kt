package com.interfun.buz.social.db.entity

import android.os.Parcelable
import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Ignore
import com.interfun.buz.common.bean.chat.MuteType
import com.interfun.buz.common.database.entity.chat.GroupUserRole
import com.interfun.buz.common.database.entity.chat.GroupUserStatus
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize

/**
 * ！！！注意 ！！！
 * ！！！注意 ！！！
 * ！！！注意 ！！！
 * 所有字段请都添加defaultValue，方便以后做部分内容更新可以直接使用upsert
 */
@Parcelize
@Entity("buz_group_extra", primaryKeys = ["groupId"])
data class BuzGroupExtra(
    @ColumnInfo(name = "groupId")
    val groupId: Long,
    @ColumnInfo(name = "canInvite", defaultValue = "0")
    val canInvite: Boolean = false,
    @ColumnInfo(name = "canEdit", defaultValue = "0")
    val canEdit: Boolean = true,
    @ColumnInfo(name = "userRole", defaultValue = "3")
    val userRole: Int? = GroupUserRole.Member.value,
    @ColumnInfo(name = "userStatus", defaultValue = "2")
    val userStatus: Int = GroupUserStatus.NotInGroup.value,
    @ColumnInfo(name = "muteMessages", defaultValue = "0")
    val muteMessages: Int? = MuteType.DEFAULT.value, //静音好友消息,1-静音，2-不静音
    @ColumnInfo(name = "muteNotification", defaultValue = "0")
    val muteNotification: Int? = MuteType.DEFAULT.value,//静音好友消息通知,1-静音，2-不静音
    /**
     * ！！！注意 ！！！
     * ！！！注意 ！！！
     * ！！！注意 ！！！
     * 所有字段请都添加defaultValue，方便以后做部分内容更新可以直接使用upsert
     */
) : Parcelable {

    @IgnoredOnParcel
    @Ignore
    val isMuteMessages = muteMessages == MuteType.MUTE.value

    @IgnoredOnParcel
    @Ignore
    val isMuteNotification = muteNotification == MuteType.MUTE.value

    @IgnoredOnParcel
    val isInGroup get() = userStatus == GroupUserStatus.InGroup.value
}