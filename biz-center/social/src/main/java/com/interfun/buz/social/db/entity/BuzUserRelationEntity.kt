package com.interfun.buz.social.db.entity

import android.os.Parcelable
import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Ignore
import com.interfun.buz.common.bean.chat.MuteType
import com.interfun.buz.common.bean.user.BuzUserRelationValue
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize

/**
 * ！！！注意 ！！！
 * ！！！注意 ！！！
 * ！！！注意 ！！！
 * 所有字段请都添加defaultValue，方便以后做部分内容更新可以直接使用upsert
 */
@Parcelize
@Entity(tableName = "buz_user_relation", primaryKeys = ["userId"])
data class BuzUserRelationEntity(
    @ColumnInfo(name = "userId")
    val userId: Long,
    /**
     * 静音好友消息,1-静音，2-不静音,default:[MuteType.DEFAULT] (0)
     */
    @ColumnInfo(name = "muteMessages", defaultValue = "0")
    val muteMessages: Int? = 0,

    /**
     * 静音好友消息通知,1-静音，2-不静音,default:[MuteType.DEFAULT] (0)
     */
    @ColumnInfo(name = "muteNotification", defaultValue = "0")
    val muteNotification: Int? = 0,

    /**
     * 好友关系,default:[BuzUserRelationValue.NO_RELATION] (4)
     */
    @ColumnInfo(name = "relation", defaultValue = "4")
    val relation: Int = BuzUserRelationValue.NO_RELATION.value,

    @ColumnInfo(name = "remark", defaultValue = "")
    val remark: String? = "",

    /**
     * 好友添加时间,default:0
     */
    @ColumnInfo(name = "friendTime", defaultValue = "0")
    val friendTime: Long = 0L,

    @ColumnInfo(name = "isBlocked", defaultValue = "0")
    val isBlocked : Boolean = false,
    /**
     * ！！！注意 ！！！
     * ！！！注意 ！！！
     * ！！！注意 ！！！
     * 所有字段请都添加defaultValue，方便以后做部分内容更新可以直接使用upsert
     */
) : Parcelable {

    @IgnoredOnParcel
    @Ignore
    val isFriend = relation == BuzUserRelationValue.FRIEND.value

    @IgnoredOnParcel
    @Ignore
    val isMe = relation == BuzUserRelationValue.MYSELF.value

    @IgnoredOnParcel
    @Ignore
    val isMuteMessages = muteMessages == MuteType.MUTE.value

    @IgnoredOnParcel
    @Ignore
    val isMuteNotification = muteNotification == MuteType.MUTE.value

    @IgnoredOnParcel
    @Ignore
    val relationEnum = BuzUserRelationValue.fromValue(relation)
}