package com.interfun.buz.social.compat

import com.interfun.buz.common.bean.chat.MuteType
import com.interfun.buz.common.database.entity.LocalMuteInfo
import com.interfun.buz.common.manager.MuteInfoCompat
import com.interfun.buz.social.SocialBizCenterMediator
import com.interfun.buz.social.di.SocialSingletonEntryPoint
import dagger.hilt.android.EntryPointAccessors

@Deprecated("请使用 GroupRepository 或者 UserRepository 代替")
class MuteInfoCompatImpl : MuteInfoCompat {

    private val userRepository
        get() = EntryPointAccessors.fromApplication<SocialSingletonEntryPoint>(
            SocialBizCenterMediator.appContext
        ).userRepository()

    private val groupRepository
        get() = EntryPointAccessors.fromApplication<SocialSingletonEntryPoint>(
            SocialBizCenterMediator.appContext
        ).groupRepository()

    override fun getUserMuteInfo(userId: Long, from: String?): LocalMuteInfo {
        val userRelation = userRepository.getUserRelationFromCacheNotSuspend(userId)
        return LocalMuteInfo(userId, userRelation?.isMuteMessages ?: false, userRelation?.isMuteNotification ?: false)
    }

    override fun getGroupMuteInfo(groupId: Long, from: String?): LocalMuteInfo {
        val groupExtra = groupRepository.getGroupExtraFromCacheNotSuspend(groupId)
        return LocalMuteInfo(groupId, groupExtra?.isMuteMessages?: false, groupExtra?.isMuteNotification?: false)
    }
}