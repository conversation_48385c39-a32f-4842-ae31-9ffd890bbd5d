package com.interfun.buz.social.db.dao

import androidx.room.*
import com.interfun.buz.social.db.entity.*
import com.interfun.buz.social.db.entity.BotExtra.BotStatus

@Dao
interface BotDao {

    @Query("SELECT * FROM botinfo_table WHERE botUserId in (:botUserIds)")
    fun queryBotInfo(botUserIds: List<Long>): List<BotExtra>?

    @Query("SELECT * FROM botinfo_table WHERE botUserId = :botUserId")
    fun queryBotInfo(botUserId: Long): BotExtra?

    @Query("SELECT * FROM botinfo_table")
    fun queryAllBotInfo(): List<BotExtra>?

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertBotInfo(botInfo: BotExtra)

    @Insert(onConflict = OnConflictStrategy.IGNORE)
    fun insertBotInfoOrIgnore(botInfo: BotExtra)

    @TypeConverters(BotInfoConvert::class)
    @Query("update botinfo_table set status = :botStatus where botUserId = :botUserId")
    fun updateBotStatus(botUserId: Long, botStatus: BotStatus) : Int

    @Transaction
    fun insertOrUpdateBotStatus(botUserId: Long, botStatus: BotStatus) {
        val updated = updateBotStatus(botUserId, botStatus) > 0
        if (!updated) {
            insertBotInfoOrIgnore(BotExtra(botUserId = botUserId, status = botStatus))
        }
    }

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertBotInfoList(botInfoList: List<BotExtra>)

    @Query("SELECT * FROM bot_single_setting WHERE botUserId = :botUserId")
    fun queryBotSingleSettings(botUserId: Long): List<BotSingleSettingEntity>

    @Query("SELECT * FROM bot_single_setting WHERE botUserId = :botUserId and `key` = :key")
    fun queryBotSingleSetting(botUserId: Long,key : Int): BotSingleSettingEntity?

    @Query("SELECT * FROM bot_single_setting")
    fun queryAllBotSingleSettings(): List<BotSingleSettingEntity>?

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertBotSingleSetting(botSingleSetting: BotSingleSettingEntity)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertBotSingleSettings(botSingleSettings: List<BotSingleSettingEntity>)

    @Transaction
    fun switchSourceAndTargetLanguage(botUserId: Long) {
        val botExtra = queryBotInfo(botUserId) ?: return
        val settingSourceCode = queryBotSingleSetting(
            botUserId,
            BotSettingKeyMap.sourceLanguage.first,
        )?.value
        if (settingSourceCode.isNullOrEmpty() || settingSourceCode == TranslatorLanguageItem.AUTO_LANGUAGE_CODE) {
            //源语言是auto，不切换
            return
        }
        val settingTargetCode = queryBotSingleSetting(
            botUserId,
            BotSettingKeyMap.targetLanguage.first,
        )?.value
        val languageBean = botExtra.getTranslatorLanguageOrDefault(settingSourceCode,settingTargetCode)
        insertBotSingleSettings(
            listOf(
                BotSingleSettingEntity(
                    botUserId,
                    BotSettingKeyMap.sourceLanguage.first,
                    languageBean.targetLanguage.code
                ),
                BotSingleSettingEntity(
                    botUserId,
                    BotSettingKeyMap.targetLanguage.first,
                    languageBean.sourceLanguage.code
                )
            )
        )
    }

    @Query("DELETE FROM botinfo_table WHERE botUserId = :botUserId")
    fun deleteBotInfo(botUserId: Long)

    @Query("DELETE FROM bot_single_setting WHERE botUserId = :botUserId")
    fun deleteBotSingleSettings(botUserId: Long)
} 