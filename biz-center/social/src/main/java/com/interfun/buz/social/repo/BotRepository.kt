package com.interfun.buz.social.repo

import com.interfun.buz.common.bean.Resp
import com.interfun.buz.social.db.entity.BotExtra
import com.interfun.buz.social.db.entity.BotWholeSettingEntity
import com.interfun.buz.social.db.entity.BotInfo
import com.interfun.buz.social.db.entity.BotInfoAndRelation
import com.interfun.buz.social.entity.AddBotResult
import kotlinx.coroutines.flow.Flow

interface BotRepository {

    suspend fun getBotInfo(botUserId: Long) : BotInfo?
    suspend fun getBotInfoFromCache(botUserId: Long): BotInfo?
    fun getBotInfoCacheFlow(botUserId: Long): Flow<BotInfo?>
    fun getBotInfoFlow(botUserId: Long): Flow<BotInfo>
    suspend fun getBotExtra(botUserId: Long) : BotExtra?
    suspend fun getBotExtraFromCache(botUserId: Long): BotExtra?
    @Deprecated("recommend to use getBotExtraFromCache instead")
    fun getBotExtraFromMemory(botUserId: Long): BotExtra?
    fun getBotExtraCacheFlow(botUserId: Long): Flow<BotExtra?>
    fun getBotExtraFlow(botUserId: Long): Flow<BotExtra>
    fun getBotExtraFlow(botUserIds: List<Long>): Flow<Map<Long, BotExtra>>
    fun getBotExtraFromCacheFlow(botUserIds: List<Long>): Flow<Map<Long, BotExtra>>

    fun loadAllBotExtraFromCache()

    suspend fun getBotInfoAndRelationFromCache(botUserId: Long): BotInfoAndRelation?
    fun getBotInfoAndRelationCacheFlow(botUserId: Long): Flow<BotInfoAndRelation?>
    fun getBotInfoAndRelationFlow(botUserId: Long,forceRefresh: Boolean = false): Flow<BotInfoAndRelation>
    fun syncBotInfoAndRelation(botUserId: Long)

    suspend fun getBotSettingsFromCache(botUserId: Long): BotWholeSettingEntity?
    @Deprecated("recommend to use getBotSettingsFromCache instead")
    fun getBotSettingsFromMemory(botUserId: Long): BotWholeSettingEntity?
    fun getBotSettingsCacheFlow(botUserId: Long): Flow<BotWholeSettingEntity?>
    fun getBotSettingsFlow(botUserId: Long): Flow<BotWholeSettingEntity?>
    fun loadAllBotSettingsFromCache()
    suspend fun updateLocalBotSettings(
        botUserId: Long,
        sourceLanguage: String? = null,
        targetLanguage: String? = null,
    ) : BotWholeSettingEntity?
    suspend fun updateServerBotSettings(
        botUserId: Long,
        languageCode: String? = null,
        voiceStyleId: Long? = null
    ): Resp<BotWholeSettingEntity?>

    suspend fun switchSourceAndTargetLanguage(
        botUserId: Long,
    ) : BotWholeSettingEntity?

    fun getRecentlyUsedTranslatorLanguage(botUserId: Long): Flow<List<String>?>

    fun syncBotSettings(botUserId: Long)
    fun syncBotInfo(botList: List<Long>)
    fun syncBotInfo(botUserId: Long)
    fun syncBotMarket()
    fun getAllMarketBotListCacheFlow(): Flow<List<BotInfo>?>
    fun getAllMarketBotListFlow(forceRefresh : Boolean = false): Flow<List<BotInfo>>

    fun hasNewAiInMarket() : Flow<Boolean>

    suspend fun addBotToChat(botUserId: Long): Resp<AddBotResult?>
    suspend fun addBotToChat(botUserIds : List<Long>): Resp<List<BotInfoAndRelation>?>
    suspend fun clearNewAiInMarketFlag()
} 