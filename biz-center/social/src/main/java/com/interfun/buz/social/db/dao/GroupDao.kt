package com.interfun.buz.social.db.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import com.interfun.buz.social.db.entity.*
import kotlinx.coroutines.flow.Flow

@Dao
interface GroupDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertGroup(buzGroup: BuzGroupTable)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertGroup(buzGroup: List<BuzGroupTable>)

    @Query("SELECT * FROM buz_group WHERE groupId = :groupId")
    fun queryGroup(groupId: Long): BuzGroupTable?

    @Query("SELECT * FROM buz_group WHERE groupId IN (:groupIds)")
    fun queryGroup(groupIds: List<Long>): List<BuzGroupTable>?

    @Query("SELECT * FROM buz_group INNER JOIN buz_group_extra ON buz_group.groupId = buz_group_extra.groupId WHERE buz_group_extra.userStatus = 1")
    fun getAllJoinedGroups(): List<GroupTableComposite>

    @Query("SELECT * FROM buz_group INNER JOIN buz_group_extra ON buz_group.groupId = buz_group_extra.groupId WHERE buz_group_extra.userStatus = 1")
    fun getAllJoinedGroupsFlow(): Flow<List<GroupTableComposite>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertGroupExtra(groupExtra: BuzGroupExtra)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertGroupExtra(groupExtraList: List<BuzGroupExtra>)

    @Query("SELECT * FROM buz_group_extra WHERE groupId = :groupId")
    fun queryGroupExtra(groupId: Long): BuzGroupExtra?

    @Query("SELECT * FROM buz_group_extra WHERE groupId IN (:groupIds)")
    fun queryGroupExtra(groupIds: List<Long>): List<BuzGroupExtra>?

    @Query("DELETE FROM buz_group")
    fun clearBuzGroupTable()

    @Query("DELETE FROM buz_group_extra")
    fun clearBuzGroupExtraTable()

    @Transaction
    fun insertGroupAndExtra(composite: GroupTableComposite) {
        insertGroup(composite.buzGroupTable)
        composite.buzGroupExtra?.let {
            insertGroupExtra(it)
        }
    }
    
    @Transaction
    fun insertGroupAndExtra(list: List<GroupTableComposite>) {
        val groupList = list.map { it.buzGroupTable }
        val extraList = list.mapNotNull { it.buzGroupExtra }
        insertGroup(groupList)
        insertGroupExtra(extraList)
    }

    @Query("update buz_group_extra set muteMessages = :muteMessages where groupId = :groupId")
    fun updateMuteMessagesSetting(groupId: Long, muteMessages: Int)

    @Query("update buz_group_extra set muteNotification = :muteNotification where groupId = :groupId")
    fun updateMuteNotificationSetting(groupId: Long, muteNotification: Int)

    @Query("update buz_group_extra set userStatus = 2 where groupId = :groupId")
    fun updateQuitGroup(groupId: Long)

    @Transaction
    fun updateGroupSettings(
        groupId: Long,
        muteMessages: Int? = null,
        muteNotification: Int? = null
    ): BuzGroupExtra? {
        //如果本身没有存在关系表的话不更新，这种场景是有问题的
        muteMessages?.let { updateMuteMessagesSetting(groupId, it) }
        muteNotification?.let { updateMuteNotificationSetting(groupId, it) }
        return queryGroupExtra(groupId)
    }

    @Transaction
    fun clearAllGroupTables() {
        clearBuzGroupTable()
        clearBuzGroupExtraTable()
    }

    @Query("select * from buz_group_un_uploaded_setting")
    fun loadAllUnLoadedSettings(): List<GroupUnUploadedSetting>


    @Query("delete from buz_group_un_uploaded_setting where groupId = :groupId")
    fun deleteGroupUnUploadedSetting(groupId: Long)

    @Query("select * from buz_group_un_uploaded_setting where groupId = :groupId")
    fun queryGroupUnUploadedSettings(groupId: Long): GroupUnUploadedSetting?

    @Query("update buz_group_un_uploaded_setting set muteMessages = :muteMessages where groupId = :groupId")
    fun updateUnUploadedMuteMessagesSetting(groupId: Long, muteMessages: Int?)

    @Query("update buz_group_un_uploaded_setting set muteNotification = :muteNotification where groupId = :groupId")
    fun updateUploadedMuteNotificationSetting(groupId: Long, muteNotification: Int?)

    @Insert(onConflict = OnConflictStrategy.IGNORE)
    fun insertUnUploadedGroupSettingsOrIgnore(setting: GroupUnUploadedSetting)

    @Transaction
    fun insertOrUpdateUnUploadedGroupSettings(
        groupId: Long,
        muteMessages: Int? = null,
        muteNotification: Int? = null,
    ): GroupUnUploadedSetting? {
        val savedSettings = queryGroupUnUploadedSettings(groupId)
        if (savedSettings == null) {
            val newSettings = GroupUnUploadedSetting(
                groupId,
                muteMessages,
                muteNotification
            )
            insertUnUploadedGroupSettingsOrIgnore(newSettings)
            return newSettings
        }
        muteMessages?.let { updateUnUploadedMuteMessagesSetting(groupId, it) }
        muteNotification?.let { updateUploadedMuteNotificationSetting(groupId, it) }
        return queryGroupUnUploadedSettings(groupId)
    }

    @Transaction
    fun setUpLoadUserRelationSettingsNull(
        groupId: Long,
        setMuteMessagesNull: Boolean = false,
        setMuteNotificationNull: Boolean = false,
    ): GroupUnUploadedSetting? {
        if (setMuteMessagesNull) {
            updateUnUploadedMuteMessagesSetting(groupId, null)
        }
        if (setMuteNotificationNull) {
            updateUploadedMuteNotificationSetting(groupId, null)
        }
        return queryGroupUnUploadedSettings(groupId)
    }
}