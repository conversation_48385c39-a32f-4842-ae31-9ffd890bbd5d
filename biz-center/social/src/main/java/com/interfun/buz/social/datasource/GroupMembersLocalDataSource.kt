package com.interfun.buz.social.datasource

import com.interfun.buz.social.db.entity.GroupBotUserEntity
import com.interfun.buz.social.entity.GroupMemberUserEntity
import kotlinx.coroutines.flow.Flow

internal interface GroupMembersLocalDataSource {

    suspend fun insertUserMembers(members: List<GroupMemberUserEntity>)

    suspend fun insertBotMembers(members: List<GroupBotUserEntity>)

    suspend fun replaceAllUserMembers(groupId: Long,newMembers: List<GroupMemberUserEntity>)

    suspend fun replaceAllBots(groupId: Long, newBots: List<GroupBotUserEntity>)

    suspend fun deleteGroupUserMembers(groupId: Long, userIds: List<Long>)

    suspend fun deleteGroupBotMembers(groupId: Long, botIds: List<Long>)

    suspend fun deleteGroupMembersByGroupId(groupId: Long)

    fun getGroupUserMembers(groupId: Long): List<GroupMemberUserEntity>
    fun getGroupUserMembersFlow(groupId: Long, count: Int? = null): Flow<List<GroupMemberUserEntity>>

    fun getGroupBotMembersFlow(groupId: Long): Flow<List<GroupBotUserEntity>>
    suspend fun getGroupBotMembers(groupId: Long): List<GroupBotUserEntity>
}