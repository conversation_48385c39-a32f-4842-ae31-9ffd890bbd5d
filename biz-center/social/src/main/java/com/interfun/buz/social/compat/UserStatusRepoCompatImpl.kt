package com.interfun.buz.social.compat

import com.interfun.buz.common.manager.user.FriendStatusChangeInfo
import com.interfun.buz.common.manager.user.UserOnlineStatusCompat
import com.interfun.buz.common.manager.user.UserStatusRepositoryCompat
import com.interfun.buz.social.SocialBizCenterMediator
import com.interfun.buz.social.di.SocialSingletonEntryPoint
import dagger.hilt.android.EntryPointAccessors
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

@Deprecated("为了兼容旧代码，新代码请使用UserOnlineStatusRepository")
class UserStatusRepoCompatImpl : UserStatusRepositoryCompat {

    @Deprecated("Use hilt to inject a UserRepository in UserComponent instead.")
    val onlineStatusRepository
        get() = EntryPointAccessors.fromApplication<SocialSingletonEntryPoint>(
            SocialBizCenterMediator.appContext
        ).getUserOnlineStatusRepository()

    override val friendStatusChangeFlow: Flow<FriendStatusChangeInfo>
        get() = onlineStatusRepository.getUpdateFlow().map { FriendStatusChangeInfo(it,true) }

    override fun getStatusInfo(userId: Long?): UserOnlineStatusCompat {
        userId ?: return UserOnlineStatusCompat(0, false, false)
        val newStatus = onlineStatusRepository.getStatusInfo(userId)
        return UserOnlineStatusCompat(
            userId,
            newStatus?.isInQuietMode ?: false,
            newStatus?.isOnline ?: false
        )
    }

    override fun getTotalOnlineFriendNum(): Int {
        return onlineStatusRepository.getTotalOnlineFriendNum()
    }

    override fun isOnline(userId: Long?): Boolean {
        return onlineStatusRepository.isOnline(userId?: 0)
    }

    override fun isQuietModeEnable(userId: Long?): Boolean {
        return onlineStatusRepository.isInQuietMode(userId?: 0)
    }
}