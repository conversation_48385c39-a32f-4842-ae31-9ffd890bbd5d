package com.interfun.buz.social.datasource

import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.common.bean.Resp
import com.interfun.buz.common.bean.share.ShareLink
import com.interfun.buz.common.manager.cache.group.GroupInfoCacheManager
import com.interfun.buz.common.utils.getShareGroupLandingPage
import com.interfun.buz.common.utils.getShareUserLandingPage
import kotlinx.coroutines.suspendCancellableCoroutine
import javax.inject.Inject
import kotlin.coroutines.resume

class RemoteShareLinkDataSourceImp @Inject constructor():RemoteShareLinkDataSource {

    override suspend fun generateShareLink(targetId: Long, convType: Int): Resp<ShareLink> {
        return when (convType) {
            1 -> generateUserShareLink(targetId)
            2 -> generateGroupShareLink(targetId)
            else -> throw IllegalArgumentException("Wrong params")
        }
    }

    private suspend fun generateGroupShareLink(targetId: Long):Resp<ShareLink>{
        val groupInfo = GroupInfoCacheManager.getGroupInfoBeanById(targetId)

        return suspendCancellableCoroutine { continuation ->
            val onSuccess:(ShareLink) -> Unit = {
                continuation.resume(Resp.Success(it))
            }
            val onFail:((String?) -> Unit) = {
                continuation.resume(Resp.Error(0,it))
            }
            getShareGroupLandingPage(
                appContext,
                targetId.toString(),
                groupInfo?.groupName?:"",
                groupInfo?.portraitUrl?:"",
                onSuccessCallback = onSuccess,
                onFinalFail = onFail
            )
        }

    }

    private suspend fun generateUserShareLink(targetId: Long): Resp<ShareLink> {
        return suspendCancellableCoroutine { continuation ->
            val onSuccess: (ShareLink) -> Unit = {
                continuation.resume(Resp.Success(it))
            }
            val onFail: ((String?) -> Unit) = {
                continuation.resume(Resp.Error(0, it))
            }
            getShareUserLandingPage(
                context = appContext,
                userId = targetId,
                onSuccessCallback = onSuccess,
                onFinalFail = onFail
            )
        }
    }
}