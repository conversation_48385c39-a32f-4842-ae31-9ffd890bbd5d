package com.interfun.buz.social.repo

import androidx.lifecycle.Lifecycle
import com.buz.idl.group.bean.GroupMember
import com.interfun.buz.base.ktx.collectInScope
import com.buz.idl.user.bean.UserInfo
import com.interfun.buz.base.ktx.logError
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.common.bean.Resp
import com.interfun.buz.common.bean.Resp.Error
import com.interfun.buz.common.bean.Resp.Success
import com.interfun.buz.common.bean.push.PushBusinessType
import com.interfun.buz.common.database.entity.chat.GroupUserRole
import com.interfun.buz.social.db.entity.GroupBotUserEntity
import com.interfun.buz.common.di.NetworkStatus
import com.interfun.buz.common.eventbus.group.GroupMembersChangeEvent
import com.interfun.buz.common.eventbus.wt.GroupBotMemberChangeEvent
import com.interfun.buz.common.ktx.codeRequestSuccess
import com.interfun.buz.common.manager.cache.ai.GroupBotManager
import com.interfun.buz.common.net.dispatcher.DefaultRetryableDispatchInterval
import com.interfun.buz.common.net.dispatcher.Retryable
import com.interfun.buz.common.net.dispatcher.UniqueRequestDispatcher
import com.interfun.buz.component.hilt.GlobalQualifier
import com.interfun.buz.component.hilt.UserQualifier
import com.interfun.buz.signal.ISignalManagerPresenter
import com.interfun.buz.signal.getProtocolDataChangeFlow
import com.interfun.buz.social.datasource.GroupMembersLocalDataSource
import com.interfun.buz.social.datasource.GroupMembersRemoteDataSource
import com.interfun.buz.social.db.entity.BuzUserComposite
import com.interfun.buz.social.entity.GroupMemberUserEntity
import com.interfun.buz.social.entity.InviteUserToGroupResult
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import kotlinx.coroutines.flow.*
import java.util.Collections
import javax.inject.Inject

/**
 * Data class representing a group member's information.
 */
data class GroupMemberInfo(
    val userId: Long,
    val userRole: Int,
    val buzUserComposite: BuzUserComposite?
)

internal class GroupMembersRepositoryImpl @Inject constructor(
    @UserQualifier private val userScope: CoroutineScope,
    @UserQualifier private val userRepository: UserRepository,
    @UserQualifier private val groupRepository: GroupRepository,
    @UserQualifier private val localDs: GroupMembersLocalDataSource,
    @UserQualifier private val remoteDs: GroupMembersRemoteDataSource,
    private val signalRepository : ISignalManagerPresenter,
    @GlobalQualifier private val networkStatus: NetworkStatus,
    @GlobalQualifier private val appLifecycle: Lifecycle,
) : GroupMembersRepository {

    companion object {
        const val TAG = "GroupMembersRepository"
    }

    private val syncGroupMembersDispatcher = UniqueRequestDispatcher<Retryable<Long>>(
        userScope,
        dispatchInterval = DefaultRetryableDispatchInterval(networkStatus, appLifecycle),
        debugTag = "syncGroupMembersDispatcher",
        onUndeliveredElement = null,
        uniqueKey = { it.data })
    { request, _ ->
        val resp = loadGroupMembersFromSever(request.data, null)
        if (resp is Resp.Error && resp.code?.codeRequestSuccess == false) {
            syncGroupMembersInternal(request.data, true)
            false
        } else {
            true
        }
    }

    private val firstTimeRequestSet = Collections.synchronizedSet(mutableSetOf<Long>())

    override fun init() {
        logInfo(TAG, "init")
        signalRepository.getProtocolDataChangeFlow()
            .filter { it.businessType == PushBusinessType.GROUP_MEMBERS_CHANGE.type
                    || it.businessType == PushBusinessType.GROUP_ROB_MEMBER_CHANGE.type }
            .collectInScope(userScope) {
                val groupId = it.businessId?.toLongOrNull() ?: return@collectInScope
                logInfo(TAG,"group members changed from signal,groupId:$groupId,type:${it.businessType}")
                if (it.businessType == PushBusinessType.GROUP_MEMBERS_CHANGE.type){
                    GroupMembersChangeEvent.post(groupId)
                }
                syncGroupMemberList(groupId, forceRequest = true)
            }
    }

    override suspend fun getGroupBotMemberFromCache(groupId: Long): List<GroupMemberInfo> {
        val members = localDs.getGroupBotMembers(groupId)
        val userComposite = userRepository.getUserCompositeFromCache(members.map { it.botUserId })
        return members.mapIndexed { index, botMember ->
            GroupMemberInfo(
                userId = botMember.botUserId,
                userRole = GroupUserRole.Robot.value,
                buzUserComposite = userComposite[index]
            )
        }
    }

    override fun getGroupBotMemberFromCacheFlow(groupId: Long): Flow<List<GroupMemberInfo>> =
        channelFlow {
            localDs.getGroupBotMembersFlow(groupId).collectLatest { memberList ->
                userRepository.getUserCompositeFlow(memberList.map { it.botUserId })
                    .collectLatest { userMap ->
                        val infoList = memberList.map { member ->
                            val userComposite = userMap[member.botUserId]
                            GroupMemberInfo(
                                userId = member.botUserId,
                                userRole = 0,
                                buzUserComposite = userComposite
                            )
                        }
                        send(infoList)
                    }
            }
        }

    override suspend fun getGroupBotMembersFromServer(groupId: Long): Resp<List<GroupMemberInfo>> {
        //机器人不分页
        val resp = loadGroupMembersFromSever(groupId, null)
        return when (resp) {
            is Success -> {
                Resp.Success(resp.data.botMembers)
            }

            is Resp.Error -> {
                Resp.Error(resp.code, resp.msg, resp.prompt)
            }
        }
    }

    override suspend fun getGroupUserMemberEntityFromCache(groupId: Long): List<GroupMemberUserEntity> {
        return localDs.getGroupUserMembers(groupId)
    }

    override fun getGroupUserMemberFromCacheFlow(
        groupId: Long,
        count: Int?
    ): Flow<List<GroupMemberInfo>> = channelFlow {
        localDs.getGroupUserMembersFlow(groupId, count).collectLatest { memberList ->
            userRepository.getUserCompositeFlow(memberList.map { it.userId })
                .collectLatest { userMap ->
                    val infoList = memberList.map { member ->
                        val userComposite = userMap[member.userId]
                        GroupMemberInfo(
                            userId = member.userId,
                            userRole = member.userRole,
                            buzUserComposite = userComposite
                        )
                    }
                    send(infoList)
                }
        }
    }

    override suspend fun deleteGroupUserMemberList(groupId: Long) {
        localDs.deleteGroupMembersByGroupId(groupId)
    }

    override fun syncGroupMemberList(groupId: Long, forceRequest: Boolean) {
        val hasRequested = groupId in firstTimeRequestSet
        logInfo(
            TAG,
            "syncGroupUserMemberList: The group $groupId, has been opened? ${hasRequested}, forceRequest? $forceRequest"
        )
        if (hasRequested && !forceRequest) return // Skip if already requested successfully AND not forced
        syncGroupMembersInternal(groupId)
    }

    private fun syncGroupMembersInternal(groupId: Long, isRetry: Boolean = false) {
        syncGroupMembersDispatcher.request(Retryable(groupId, isRetry))
    }

    override suspend fun kickOutGroupUserMembers(
        groupId: Long,
        userIds: List<Long>
    ): Resp<Boolean> {
        val result = remoteDs.kickOutGroupMembers(groupId, userIds)
        if (result is Success) {
            localDs.deleteGroupUserMembers(groupId, userIds)
            //删除成员后，部分信息，比如成员数会变更，需要重新刷新
            groupRepository.syncGroupCompositeFromServer(groupId)
        }
        return result
    }

    override suspend fun kickOutGroupBotMembers(groupId: Long, botIds: List<Long>): Resp<Boolean> {
        val result = remoteDs.kickOutGroupBots(groupId, botIds)
        return when (result) {
            is Success -> {
                localDs.deleteGroupUserMembers(groupId, botIds)
                replaceAllBotMembers(groupId, result.data ?: emptyList(),true)
                Success(true)
            }

            is Resp.Error -> {
                Resp.Error(result.code, result.msg, result.prompt)
            }
        }
    }

    override suspend fun addBotToGroup(
        botUserIds: List<Long>,
        groupIds: List<Long>
    ): Resp<Boolean> {
        val resp = remoteDs.addBotToGroup(botUserIds, groupIds)
        return when (resp) {
            is Success -> {
                val botMemberMap = resp.data?.botMemberMap
                if (botMemberMap != null) {
                    groupIds.forEach { groupId ->
                        val botMembers = botMemberMap.get(groupId.toString())
                        replaceAllBotMembers(groupId, botMembers ?: emptyList(), true)
                    }
                }
                Success(true)
            }

            is Resp.Error -> {
                Resp.Error(resp.code, resp.msg, resp.prompt)
            }
        }
    }

    override suspend fun inviteUserToGroup(groupId: Long, userIds: List<Long>): Resp<InviteUserToGroupResult> {
        return remoteDs.inviteUserToGroup(groupId, userIds)
    }

    private suspend fun loadGroupMembersFromSever(groupId: Long, queryParams: String?): Resp<LoadGroupMemberListResult> {
        val resp = remoteDs.requestGroupMemberList(groupId, queryParams)
        return when (resp) {
            is Success -> {
                val data = resp.data
                //第一页数据（或者是小群全部数据[不分页]），需要清除所有数据
                val replaceAllExistingMembers = queryParams == null
                val userMembers = updateUserMembers(groupId, data.groupMemberList ?: emptyList(),replaceAllExistingMembers)
                val botMembers = replaceAllBotMembers(groupId, data.botList ?: emptyList(),replaceAllExistingMembers)
                if (queryParams == null) {
                    firstTimeRequestSet.add(groupId)
                }
                val userMemberUserInfos = userRepository.getUserCompositeFromCache(userMembers.map { it.userId })
                val botMemberUserInfos = userRepository.getUserCompositeFromCache(botMembers.map { it.botUserId })
                val userMemberComposites = userMembers.mapIndexed { index, member ->
                    GroupMemberInfo(
                        member.userId,
                        member.userRole,
                        userMemberUserInfos[index]
                    )
                }
                val botMemberComposites = botMembers.mapIndexed { index, member ->
                    GroupMemberInfo(
                        member.botUserId,
                        GroupUserRole.Robot.value,
                        botMemberUserInfos[index]
                    )
                }
                Resp.Success(LoadGroupMemberListResult(groupId, userMemberComposites, botMemberComposites))
            }

            is Resp.Error -> {
                Resp.Error(resp.code, resp.msg, resp.prompt)
            }
        }
    }

    private suspend fun replaceAllBotMembers(
        groupId: Long,
        newBots: List<GroupMember>,
        replaceAllExistingMembers: Boolean
    ): List<GroupBotUserEntity> {
        val botMembers = newBots.mapNotNull {
            val botUserId = it.userInfo.userId ?: return@mapNotNull null
            GroupBotUserEntity(
                groupId = groupId,
                botUserId = botUserId,
                joinGroupTime = it.joinGroupTime ?: 0L
            )
        }
        val users = newBots.map { it.userInfo }
        userRepository.updateUser(users)
        if (replaceAllExistingMembers) {
            localDs.replaceAllBots(groupId, botMembers)
        }else {
            localDs.insertBotMembers(botMembers)
        }
        //兼容旧代码
        GroupBotMemberChangeEvent.post(groupId)
        return botMembers
    }

    private suspend fun updateUserMembers(
        groupId: Long,
        newMembers: List<GroupMember>,
        replaceAllExistingMembers: Boolean
    ) : List<GroupMemberUserEntity> {
        val userMembers = newMembers.mapNotNull {
            val userId = it.userInfo.userId ?: return@mapNotNull null
            GroupMemberUserEntity(
                groupId = groupId,
                userId = userId,
                userRole = it.userRole,
                timeStamp = it.joinGroupTime ?: 0L
            )
        }
        val users = newMembers.map { it.userInfo }
        userRepository.updateUser(users)
        if (replaceAllExistingMembers) {
            localDs.replaceAllUserMembers(groupId, userMembers)
        } else {
            localDs.insertUserMembers(userMembers)
        }
        return userMembers
    }

    class LoadGroupMemberListResult(
        val groupId: Long,
        val userMembers: List<GroupMemberInfo>,
        val botMembers: List<GroupMemberInfo>
    )
}