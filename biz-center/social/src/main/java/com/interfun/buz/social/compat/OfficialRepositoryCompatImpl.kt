package com.interfun.buz.social.compat

import com.interfun.buz.social.SocialBizCenterMediator
import com.interfun.buz.social.di.SocialSingletonEntryPoint
import dagger.hilt.android.EntryPointAccessors

@Deprecated("为了兼容旧代码，新代码别用")
class OfficialRepositoryCompatImpl {
    companion object {
        /**
         * @deprecated Use hilt to inject a officialRepository in UserComponent instead.
         *
         * Don't keep the reference of this object,because it will be change when user has been changed.
         * Should always use [officialRepository] to get the UserRepository.
         */
        @Deprecated("Use hilt to inject a UserRepository in UserComponent instead.")
        val officialRepository
            get() = EntryPointAccessors.fromApplication<SocialSingletonEntryPoint>(
                SocialBizCenterMediator.appContext
            ).getOfficialAccountRepository()
    }
}