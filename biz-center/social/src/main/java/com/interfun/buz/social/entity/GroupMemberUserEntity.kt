package com.interfun.buz.social.entity

import android.os.Parcelable
import androidx.annotation.Keep
import androidx.room.*
import kotlinx.parcelize.Parcelize

/**
 * Design for group's bot list
 */
@Keep
@Parcelize
@Entity(tableName = "group_member_user_table", primaryKeys = ["groupId", "userId"])
data class GroupMemberUserEntity(
    var groupId: Long,
    var userId: Long,
    var userRole: Int,

    @ColumnInfo(defaultValue = "0")
    var timeStamp: Long
) : Parcelable

