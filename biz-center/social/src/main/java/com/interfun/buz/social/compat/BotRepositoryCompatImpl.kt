package com.interfun.buz.social.compat

import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.common.database.entity.UserRelationInfo
import com.interfun.buz.social.db.entity.BotExtra
import com.interfun.buz.common.manager.cache.ai.TranslatorLanguage
import com.interfun.buz.social.SocialBizCenterMediator
import com.interfun.buz.social.di.SocialSingletonEntryPoint
import dagger.hilt.android.EntryPointAccessors
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch

/**
 * only for old logic compatibility
 * !!!! Don't use it in new code !!!
 */
@Deprecated("use BotRepository instead,don't use it in new code.")
class BotRepositoryCompatImpl : BotRepoCompat {

    private val TAG = "BotRepositoryCompat"

    /**
     * @deprecated Use hilt to inject a UserRepository in UserComponent instead.
     *
     * Don't keep the reference of this object,because it will be change when user has been changed.
     * Should always use [botRepository] to get the BotRepository.
     */
    @Deprecated("Use hilt to inject a BotRepository in UserComponent instead.")
    private val botRepository
        get() = EntryPointAccessors.fromApplication<SocialSingletonEntryPoint>(
            SocialBizCenterMediator.appContext
        ).getBotRepository()

    private val userRepository
        get() = EntryPointAccessors.fromApplication<SocialSingletonEntryPoint>(
            SocialBizCenterMediator.appContext
        ).userRepository()

    private val userComponentManager
        get() = EntryPointAccessors.fromApplication<SocialSingletonEntryPoint>(
            SocialBizCenterMediator.appContext
        ).getUserComponentManger()

    init {
        GlobalScope.launch {
            userComponentManager.getUserComponentFlow().collect { userComponent ->
                ensureLoadAiData()
            }
        }
    }

    private fun ensureLoadAiData(){
        logInfo(TAG,"ensureLoadAiData")
        botRepository.loadAllBotExtraFromCache()
        botRepository.loadAllBotSettingsFromCache()
    }

    override fun fetchBotInfoFromMemPurely(robotUserId: Long): BotExtra? {
        return botRepository.getBotExtraFromMemory(robotUserId)
    }

    override suspend fun fetchBotInfo(robotId: Long): BotExtra? {
        return botRepository.getBotExtra(robotId)
    }

    override fun getTranslatorLanguageFromMemoryOrDefault(userInfo: UserRelationInfo?): TranslatorLanguage? {
        val userId = userInfo?.userId ?: return null
        val botSetting = botRepository.getBotSettingsFromMemory(userId)
        return botRepository.getBotExtraFromMemory(userId)?.getTranslatorLanguageOrDefault(botSetting)
    }

    override fun getTranslatorLanguageFromMemoryOrDefault(botUserId: Long): TranslatorLanguage? {
        val user = userRepository.getUserFromCacheUnSuspend(botUserId)
        if (user == null || user.userType != UserRelationInfo.USER_TYPE_ROBOT) return null
        val botSetting = botRepository.getBotSettingsFromMemory(botUserId)
        return botRepository.getBotExtraFromMemory(botUserId)?.getTranslatorLanguageOrDefault(botSetting)
    }
}