package com.interfun.buz.social.compat

import com.interfun.buz.common.database.entity.UserRelationInfo
import com.interfun.buz.social.db.entity.BotExtra
import com.interfun.buz.common.manager.cache.ai.TranslatorLanguage

/**
 * 为了兼容以前common模块里面的ai相关逻辑，
 */
@Deprecated("请不要再使用这个接口，不要新增接口，请使用BotRepository")
interface BotRepoCompat {

    fun fetchBotInfoFromMemPurely(robotUserId: Long): BotExtra?

    suspend fun fetchBotInfo(robotId: Long): BotExtra?


    fun getTranslatorLanguageFromMemoryOrDefault(userInfo: UserRelationInfo?): TranslatorLanguage?

    fun getTranslatorLanguageFromMemoryOrDefault(botUserId: Long): TranslatorLanguage?

}