package com.interfun.buz.social.repo

import com.interfun.buz.common.bean.Resp
import com.interfun.buz.social.entity.GroupMemberUserEntity
import com.interfun.buz.social.entity.InviteUserToGroupResult
import kotlinx.coroutines.flow.Flow

interface GroupMembersRepository {
    fun init()
    suspend fun getGroupBotMemberFromCache(groupId: Long): List<GroupMemberInfo>
    fun getGroupBotMemberFromCacheFlow(groupId: Long): Flow<List<GroupMemberInfo>>
    suspend fun getGroupBotMembersFromServer(groupId: Long): Resp<List<GroupMemberInfo>>
    fun getGroupUserMemberFromCacheFlow(groupId: Long, count: Int? = null): Flow<List<GroupMemberInfo>>
    suspend fun getGroupUserMemberEntityFromCache(groupId: Long): List<GroupMemberUserEntity>
    suspend fun deleteGroupUserMemberList(groupId: Long)

    /**Update using remote data source when entering group chat page of groupId for the first time
     * @param groupId The id of the group whose user member list is to be synchronized
     * @param forceRequest Whether to force the request even if data is already available. True if from signal push, false by default
     * */
    fun syncGroupMemberList(groupId: Long, forceRequest: Boolean = true)

    suspend fun kickOutGroupUserMembers(groupId: Long, userIds: List<Long>): Resp<Boolean>

    suspend fun kickOutGroupBotMembers(groupId: Long, botIds: List<Long>): Resp<Boolean>

    suspend fun addBotToGroup(botUserIds: List<Long>, groupIds: List<Long>): Resp<Boolean>

    suspend fun inviteUserToGroup(groupId: Long, userIds: List<Long>): Resp<InviteUserToGroupResult>
}