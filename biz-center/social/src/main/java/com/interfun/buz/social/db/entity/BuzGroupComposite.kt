package com.interfun.buz.social.db.entity

import android.os.Parcelable
import androidx.room.Embedded
import androidx.room.Relation
import com.interfun.buz.common.bean.chat.MuteType
import com.interfun.buz.common.database.entity.chat.GroupInfoBean
import com.interfun.buz.common.database.entity.chat.GroupUserRole
import com.interfun.buz.common.database.entity.chat.GroupUserStatus
import kotlinx.parcelize.Parcelize

@Parcelize
data class BuzGroupComposite(
    val buzGroup: BuzGroup,
    val buzGroupExtra: BuzGroupExtra?
) : Parcelable {

    @Deprecated("兼容旧代码，新代码别用")
    fun toOldGroupInfoBean() : GroupInfoBean{
        return GroupInfoBean(
            groupId = buzGroup.groupId,
            groupName = buzGroup.groupName,
            portraitUrl = buzGroup.portraitUrl,
            firstFewPortraits = buzGroup.firstFewPortraits,
            memberNum = buzGroup.memberNum,
            maxMemberNum = buzGroup.maxMemberNum,
            groupStatus = buzGroup.groupStatus,
            groupType = buzGroup.groupType,
            canInvite = buzGroupExtra?.canInvite ?: false,
            canEdit = buzGroupExtra?.canEdit ?: false,
            userRole = buzGroupExtra?.userRole ?: GroupUserRole.Member.value,
            userStatus = buzGroupExtra?.userStatus ?: GroupUserStatus.InGroup.value,
            muteMessages = buzGroupExtra?.muteMessages?: MuteType.DEFAULT.value,
            muteNotification = buzGroupExtra?.muteNotification?: MuteType.DEFAULT.value,
            serverPortraitUrl = buzGroup.serverPortraitUrl
        )
    }
}

data class GroupTableComposite(
    @Embedded
    val buzGroupTable: BuzGroupTable,
    @Relation(
        parentColumn = "groupId",
        entityColumn = "groupId"
    )
    val buzGroupExtra: BuzGroupExtra?
)