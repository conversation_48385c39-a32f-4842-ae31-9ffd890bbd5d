package com.interfun.buz.social.datasource

import com.interfun.buz.social.db.entity.BuzUser
import com.interfun.buz.social.db.entity.BuzUserRelationEntity
import com.interfun.buz.social.db.entity.BuzUserComposite
import com.interfun.buz.social.db.entity.UserUnUploadedSetting
import kotlinx.coroutines.flow.Flow

internal interface UserLocalDataSource {
    suspend fun getUser(userId: Long): BuzUser?
    @Deprecated("请使用getUser")
    fun getUserUnSuspend(userId: Long): BuzUser?
    /**
     * @return 返回的数据跟查询的对应index，如果查不到，则对应的位置为null
     */
    suspend fun getUser(userIds: List<Long>): List<BuzUser?>
    fun getUserFlow(userId: Long): Flow<BuzUser?>
    fun getUserFlow(userIds: List<Long>): Flow<Map<Long,BuzUser>>
    suspend fun saveUser(user: BuzUser) : BuzUser
    suspend fun markUserAccountDeleted(userId: Long) : BuzUser?
    suspend fun saveUser(user: List<BuzUser>) : List<BuzUser>
    suspend fun getUserRelation(userId: Long): BuzUserRelationEntity?
    @Deprecated("为了兼容旧代码，新代码别用")
    fun getUserRelationNotSuspend(userId: Long): BuzUserRelationEntity?
    suspend fun updateRelation(userId: Long,userRelation: Int): BuzUserRelationEntity?
    suspend fun updateRelation(userRelationList: List<Pair<Long,Int>>) : List<BuzUserRelationEntity>?
    suspend fun replaceAllBlockList(newBlockList : List<Long>)
    /**
     * @return 返回的数据跟查询的对应index，如果查不到，则对应的位置为null
     */
    suspend fun getUserRelation(userIds: List<Long>): List<BuzUserRelationEntity?>?
    suspend fun getFriendsSize(): Int
    suspend fun getAllFriends(): List<BuzUserComposite>
    suspend fun getAllFriendIds(): List<Long>
    fun getAllFriendIdsFlow(): Flow<List<Long>>
    fun getBlockedUserIdsFlow(): Flow<List<Long>>
    suspend fun getAllOfficialFriends() : List<BuzUserComposite>
    suspend fun getLastRequestFriendTimestamp(): Long
    suspend fun updateLastRequestFriendTimestamp(timestamp: Long)
    @Deprecated("为了兼容旧代码，新代码别用")
    fun getAllFriendsNotSuspend(): List<BuzUserComposite>
    fun getUserRelationFlow(userId: Long): Flow<BuzUserRelationEntity?>
    fun getUserRelationFlow(userIds: List<Long>): Flow<Map<Long,BuzUserRelationEntity>>
    suspend fun saveUserRelation(relationInfo: BuzUserRelationEntity) : BuzUserRelationEntity
    suspend fun saveUserRelation(relationInfoList: List<BuzUserRelationEntity>) : List<BuzUserRelationEntity>
    suspend fun getUserComposite(userId: Long): BuzUserComposite?
    @Deprecated("为了兼容旧代码，新代码别用")
    fun getUserCompositeNotSuspend(userId: Long): BuzUserComposite?
    @Deprecated("为了兼容旧代码，新代码别用")
    fun getUserCompositeFromMemory(userId: Long): BuzUserComposite?
    suspend fun updateUserRelationSettings(
        userId: Long,
        muteMessages: Int? = null,
        muteNotification: Int? = null,
        remark: String? = null
    ): BuzUserRelationEntity?

    suspend fun insertOrUpdateUnUploadedUserRelationSettings(
        userId: Long,
        muteMessages: Int? = null,
        muteNotification: Int? = null,
        remark: String? = null
    )

    suspend fun deleteUserUnUploadedSettingsIfMatched(
        userId: Long,
        updatedMuteMessages: Int? = null,
        updatedMuteNotification: Int? = null,
        updatedRemark: String? = null
    )

    suspend fun getUserUnUploadedSettings(userId: Long): UserUnUploadedSetting?

    suspend fun getAllUserUnUploadedSettings(): List<UserUnUploadedSetting>?

    /**
     * @return 返回的数据跟查询的对应index，如果查不到，则对应的位置为null
     */
    suspend fun getUserComposite(userIds: List<Long>): List<BuzUserComposite?>
    fun getUserCompositeFlow(userId: Long): Flow<BuzUserComposite?>
    fun getUserCompositeFlow(userIds: List<Long>): Flow<Map<Long,BuzUserComposite>>
    suspend fun saveUserComposite(userComposite: BuzUserComposite) : BuzUserComposite
    suspend fun saveUserComposite(userComposites: List<BuzUserComposite>) : List<BuzUserComposite>
    suspend fun clearBuzUserTable()
}