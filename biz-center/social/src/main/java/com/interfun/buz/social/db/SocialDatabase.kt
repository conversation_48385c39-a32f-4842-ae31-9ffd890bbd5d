package com.interfun.buz.social.db

import com.interfun.buz.social.db.dao.BotDao
import com.interfun.buz.social.db.dao.GroupDao
import com.interfun.buz.social.db.dao.GroupUserMemberDao
import com.interfun.buz.social.db.dao.OfficialAccountDao
import com.interfun.buz.social.db.dao.ShareLinkDao
import com.interfun.buz.social.db.dao.UserDao

interface SocialDatabase {
    val isDataBaseOpen: Boolean
    fun getUserDao(): UserDao
    fun getGroupDao(): GroupDao
    fun getGroupUserMemberDao(): GroupUserMemberDao
    fun getShareLinkDao(): ShareLinkDao
    fun getOfficialDao(): OfficialAccountDao
    fun getBotDao(): BotDao
}