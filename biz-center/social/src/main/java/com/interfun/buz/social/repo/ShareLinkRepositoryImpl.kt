package com.interfun.buz.social.repo

import com.buz.idl.common.request.RequestEvaluateContactInfoChange
import com.buz.idl.common.service.BuzNetCommonServiceClient
import com.interfun.buz.common.bean.Resp
import com.interfun.buz.common.net.withConfig
import com.interfun.buz.component.hilt.UserQualifier
import com.interfun.buz.social.datasource.LocalShareLinkDataSource
import com.interfun.buz.social.datasource.RemoteShareLinkDataSource
import com.interfun.buz.social.entity.ShareLinkEntity
import com.lizhi.component.basetool.ntp.NtpTime
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.withContext
import javax.inject.Inject

@UserQualifier
class ShareLinkRepositoryImpl @Inject constructor(
    @UserQualifier private val localDs: LocalShareLinkDataSource,
    @UserQualifier private val remoteDs: RemoteShareLinkDataSource,
    @UserQualifier private val userScope: CoroutineScope
) : ShareLinkRepository {

    private val serviceClient: BuzNetCommonServiceClient by lazy {
        BuzNetCommonServiceClient().withConfig()
    }


    override fun init() {

    }

    override suspend fun getShareLink(
        targetId: Long,
        convType: Int,
    ): Resp<ShareLinkEntity?> {
        val localData = localDs.getShareLink(targetId, convType)
        if (localData != null && localData.expiredTime > NtpTime.nowForce()) {
            return Resp.Success(localData)
        } else {
            val generateResp = remoteDs.generateShareLink(targetId, convType)
            if (generateResp is Resp.Success && generateResp.data != null) {
                val newData = ShareLinkEntity(
                    targetId = targetId,
                    convType = ShareLinkEntity.toShareLinkTypeHobbiesImpl(convType),
                    expiredTime = generateResp.data.expireTime,
                    link = generateResp.data.link
                )
                localDs.insert(newData)
                return Resp.Success(newData)
            } else if (generateResp is Resp.Error) {
                return Resp.Error(generateResp.code, generateResp.msg)
            } else {
                return Resp.Error(-1, "Unknown error")
            }
        }
    }

    override suspend fun reportShareContactMsgClick(
        id: Long,
        contactType: Int,
        conversationType: IM5ConversationType,
        serverMsgId: Long,
        traceId: String
    ) {
        withContext(userScope.coroutineContext) {
            serviceClient.evaluateContactInfoChange(
                RequestEvaluateContactInfoChange(
                    id = id,
                    contactType = contactType,
                    conversationType = conversationType.value,
                    serverMsgId = serverMsgId,
                    traceId = traceId
                )
            )
        }
    }

}