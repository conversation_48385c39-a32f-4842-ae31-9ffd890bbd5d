package com.interfun.buz.social

import android.content.Context
import com.interfun.buz.common.manager.MuteInfoManager
import com.interfun.buz.common.manager.OnLogout
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.social.compat.AiInfoDataHelper
import com.interfun.buz.common.manager.cache.ai.GroupBotDataHelper
import com.interfun.buz.common.manager.cache.group.GroupInfoCacheManager
import com.interfun.buz.common.manager.cache.user.UserRelationCacheManager
import com.interfun.buz.common.manager.user.FriendStatusManager
import com.interfun.buz.component.hilt.UserComponentManager
import com.interfun.buz.component.hilt.GlobalQualifier
import com.interfun.buz.social.compat.*
import com.interfun.buz.social.di.SocialEntryPoint
import dagger.hilt.EntryPoints
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class SocialBizCenterMediator @Inject constructor(
    private val userComponentManager: UserComponentManager,
    @GlobalQualifier private val globalScope: CoroutineScope
) {
    companion object {
        @Volatile
        internal lateinit var appContext : Context
    }

    fun init(appContext : Context) {
        Companion.appContext = appContext
        globalScope.launch {
            userComponentManager.getUserComponentFlow().collect { userComponent ->
                EntryPoints.get(userComponent, SocialEntryPoint::class.java).getSocialBizCenterUserEntry().init()
            }
        }

        AiInfoDataHelper.delegate = BotRepositoryCompatImpl()
        FriendStatusManager.delegate = UserStatusRepoCompatImpl()
        UserRelationCacheManager.delegate = UserRepositoryCompatImpl()
        MuteInfoManager.delegate = MuteInfoCompatImpl()
        GroupInfoCacheManager.delegate = GroupRepositoryCompatImpl()
        GroupBotDataHelper.delegate = GroupBotMemberRepoCompatImpl()
    }
}