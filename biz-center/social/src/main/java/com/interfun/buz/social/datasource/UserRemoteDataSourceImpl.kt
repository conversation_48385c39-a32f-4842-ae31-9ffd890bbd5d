package com.interfun.buz.social.datasource

import com.buz.idl.common.request.RequestReportInterviewInviteResult
import com.buz.idl.common.service.BuzNetCommonService
import com.buz.idl.user.bean.UserInfo
import com.buz.idl.user.bean.UserRelation
import com.buz.idl.user.bean.UserRelationInfo
import com.buz.idl.user.request.*
import com.buz.idl.user.response.ResponseGetFriendList
import com.buz.idl.user.response.ResponseGetUserInfo
import com.buz.idl.user.service.BuzNetUserService
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.common.bean.Resp
import com.interfun.buz.common.ktx.isSuccess
import com.interfun.buz.component.hilt.UserQualifier
import com.interfun.buz.social.db.entity.BuzUserComposite
import com.interfun.buz.social.di.SocialUserQualifier
import javax.inject.Inject

internal class UserRemoteDataSourceImpl @Inject constructor(
    @UserQualifier private val userNetUserService: BuzNetUserService,
    @SocialUserQualifier private val commonNetService: BuzNetCommonService,
) : UserRemoteDataSource {

    private val TAG = "UserRemoteDataSource"

    override suspend fun requestUserComposite(userId: Long): Resp<ResponseGetUserInfo?> {
        val serverResp = userNetUserService.getUserInfo(RequestGetUserInfo(userId, null))
        return if (serverResp.isSuccess) {
            Resp.Success(serverResp.data, serverResp.data?.prompt)
        } else {
            Resp.Error(serverResp.code, serverResp.msg, serverResp.data?.prompt)
        }
    }

    override suspend fun requestUserCompositeBatch(userIds: List<Long>): Resp<List<UserRelationInfo>?> {
        val serverResp =
            userNetUserService.getUserRelationInfoBatch(RequestGetUserRelationInfoBatch(userIds))
        return if (serverResp.isSuccess) {
            Resp.Success(serverResp.data?.userRelationInfoList, serverResp.data?.prompt)
        } else {
            Resp.Error(serverResp.code, serverResp.msg, serverResp.data?.prompt)
        }
    }

    override suspend fun getUserInfo(userId: Long): Resp<UserInfo?> {
        val serverResp = userNetUserService.getUserInfoSingle(RequestGetUserInfoSingle(userId))
        return if (serverResp.isSuccess) {
            Resp.Success(serverResp.data?.userInfo, serverResp.data?.prompt)
        } else {
            Resp.Error(serverResp.code, serverResp.msg, serverResp.data?.prompt)
        }
    }

    override suspend fun requestUserInfoBatch(userIds: List<Long>): Resp<List<UserInfo>?> {
        val serverResp = userNetUserService.getUserInfoList(RequestGetUserInfoList(userIds))
        return if (serverResp.isSuccess) {
            Resp.Success(serverResp.data?.userInfoList, serverResp.data?.prompt)
        } else {
            Resp.Error(serverResp.code, serverResp.msg, serverResp.data?.prompt)
        }
    }

    override suspend fun requestUserRelation(userId: Long): Resp<UserRelation?> {
        val serverResp = userNetUserService.getUserRelation(RequestGetUserRelation(userId))
        return if (serverResp.isSuccess) {
            Resp.Success(serverResp.data?.userRelation, serverResp.data?.prompt)
        } else {
            Resp.Error(serverResp.code, serverResp.msg, serverResp.data?.prompt)
        }
    }

    override suspend fun requestUserRelationBatch(userIds: List<Long>): Resp<List<UserRelation>?> {
        val serverResp =
            userNetUserService.getUserRelationBatch(RequestGetUserRelationBatch(userIds))
        return if (serverResp.isSuccess) {
            Resp.Success(serverResp.data?.userRelationList, serverResp.data?.prompt)
        } else {
            Resp.Error(serverResp.code, serverResp.msg, serverResp.data?.prompt)
        }
    }

    override suspend fun getFriendList(timestamp: Long): Resp<ResponseGetFriendList?> {
        val serverResp = userNetUserService.getFriendList(RequestGetFriendList(timestamp))
        return if (serverResp.isSuccess) {
            Resp.Success(serverResp.data, null)
        } else {
            Resp.Error(serverResp.code, serverResp.msg, null)
        }
    }

    override suspend fun getBlockList(): Resp<List<UserInfo>> {
        val serverResp = userNetUserService.getBlackList(RequestGetBlackList())
        return if (serverResp.isSuccess) {
            Resp.Success(serverResp.data?.blackUserList ?: emptyList(), null)
        } else {
            Resp.Error(serverResp.code, serverResp.msg, null)
        }
    }

    override suspend fun addFriend(
        userId: Long,
        source: Int,
        businessId: String?
    ): Resp<UserRelationInfo> {
        logInfo(TAG,"addFriend: userId:$userId, source:$source, businessId:$businessId")
        val serverResp = userNetUserService.addFriend(RequestAddFriend(userId, source, businessId))
        return if (serverResp.isSuccess) {
            val newRelation = serverResp.data?.userRelationInfo
            if (newRelation != null) {
                Resp.Success(newRelation, serverResp.data?.prompt)
            } else {
                Resp.Error(
                    serverResp.code,
                    serverResp.msg,
                    serverResp.data?.prompt
                )
            }
        } else {
            Resp.Error(serverResp.code, serverResp.msg, serverResp.data?.prompt)
        }
    }

    override suspend fun addBuzAIAsFriend(): Resp<UserRelationInfo> {
        val serverResp = userNetUserService.addAIFriend(RequestAddAIFriend())
        return if (serverResp.isSuccess) {
            val newRelation = serverResp.data?.userRelationInfo
            if (newRelation != null) {
                Resp.Success(newRelation, serverResp.data?.prompt)
            } else {
                Resp.Error(
                    serverResp.code,
                    serverResp.msg,
                    serverResp.data?.prompt
                )
            }
        } else {
            Resp.Error(serverResp.code, serverResp.msg, serverResp.data?.prompt)
        }
    }

    override suspend fun deleteFriend(userId: Long): Resp<UserRelation> {
        logInfo(TAG,"deleteFriend: userId:$userId")
        val serverResp = userNetUserService.deleteFriend(RequestDeleteFriend(userId))
        return if (serverResp.isSuccess) {
            val data = serverResp.data?.userRelation
            if (data!= null) {
                Resp.Success(data, serverResp.data?.prompt)
            } else {
                Resp.Error(serverResp.code, serverResp.msg, serverResp.data?.prompt)
            }
        } else {
            Resp.Error(serverResp.code, serverResp.msg, serverResp.data?.prompt)
        }
    }

    override suspend fun blockUser(userId: Long): Resp<UserRelation> {
        logInfo(TAG,"blockUser: userId:$userId")
        //type 1: block  2: unblock
        val serverResp =
            userNetUserService.operateBlackList(RequestOperateBlackList(arrayListOf(userId), 1))
        return if (serverResp.isSuccess) {
            val data = serverResp.data?.userRelationList?.firstOrNull()
            if (data != null) {
                Resp.Success(data, serverResp.data?.prompt)
            } else {
                Resp.Error(serverResp.code, serverResp.msg, serverResp.data?.prompt)
            }
        } else {
            Resp.Error(serverResp.code, serverResp.msg, serverResp.data?.prompt)
        }
    }

    override suspend fun unblockUser(userId: Long): Resp<UserRelation> {
        logInfo(TAG,"unblockUser: userId:$userId")
        //type 1: block  2: unblock
        val serverResp =
            userNetUserService.operateBlackList(RequestOperateBlackList(arrayListOf(userId), 2))
        return if (serverResp.isSuccess) {
            val data = serverResp.data?.userRelationList?.firstOrNull()
            if (data != null) {
                Resp.Success(data, serverResp.data?.prompt)
            } else {
                Resp.Error(serverResp.code, serverResp.msg, serverResp.data?.prompt)
            }
        } else {
            Resp.Error(serverResp.code, serverResp.msg, serverResp.data?.prompt)
        }
    }

    override suspend fun reportUser(userId: Long): Resp<Boolean> {
        logInfo(TAG,"reportUser: userId:$userId")
        val serverResp = userNetUserService.reportUser(RequestReportUser(userId))
        return if (serverResp.isSuccess) {
            Resp.Success(true, serverResp.data?.prompt)
        } else {
            Resp.Error(serverResp.code, serverResp.msg, serverResp.data?.prompt)
        }
    }

    override suspend fun agreeFriendApply(userId: Long): Resp<UserRelation> {
        return processFriendApply(userId, 1)
    }

    override suspend fun refuseFriendApply(userId: Long): Resp<UserRelation> {
        return processFriendApply(userId, 2)
    }

    override suspend fun agreeAddResearchAccount(): Resp<UserRelationInfo?> {
        return handleResearchAccountRequest(1)
    }

    override suspend fun refuseAddResearchAccount(): Resp<UserRelationInfo?> {
        return handleResearchAccountRequest(2)
    }

    /**
     * @param type 1: agree  2: refuse
     */
    private suspend fun handleResearchAccountRequest(type: Int): Resp<UserRelationInfo?> {
        val serverResp =
            commonNetService.reportInterviewInviteResult(RequestReportInterviewInviteResult(type))
        return if (serverResp.isSuccess) {
            Resp.Success(serverResp.data?.userRelationInfo, serverResp.data?.prompt)
        } else {
            Resp.Error(serverResp.code, serverResp.msg, serverResp.data?.prompt)
        }
    }

    private suspend fun processFriendApply(userId: Long, type: Int): Resp<UserRelation> {
        logInfo(TAG,"processFriendApply: userId:$userId")
        val serverResp =
            userNetUserService.processFriendApply(RequestProcessFriendApply(userId, type))
        return if (serverResp.isSuccess) {
            val data = serverResp.data?.userRelation
            if (data != null) {
                Resp.Success(
                    data,
                    serverResp.data?.prompt
                )
            } else {
                Resp.Error(serverResp.code, serverResp.msg, serverResp.data?.prompt)
            }
        } else {
            Resp.Error(serverResp.code, serverResp.msg, serverResp.data?.prompt)
        }
    }

    override suspend fun updateUserSetting(
        userId: Long,
        muteMessages: Int?,
        muteNotification: Int?,
        remark: String?
    ): Resp<Boolean> {
        logInfo(TAG,"updateUserSetting: userId:$userId, muteMessages:$muteMessages, muteNotification:$muteNotification, remark:$remark")
        val serverResp = userNetUserService.updateFriendInfo(
            RequestUpdateFriendInfo(
                userId,
                remark = remark,
                muteMessages = muteMessages,
                muteNotification = muteNotification
            )
        )
        return if (serverResp.isSuccess) {
            Resp.Success(true, serverResp.data?.prompt)
        } else {
            Resp.Error(serverResp.code, serverResp.msg, serverResp.data?.prompt)
        }
    }
}