package com.interfun.buz.social.db.migration

import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase

class SocialMigration_23_24 : Migration(23, 24) {
    override fun migrate(db: SupportSQLiteDatabase) {
        //建新用户表，关系表
        db.execSQL("CREATE TABLE IF NOT EXISTS `buz_user` (`userId` INTEGER NOT NULL, `userName` TEXT DEFAULT '', `firstName` TEXT DEFAULT '', `lastName` TEXT DEFAULT '', `portrait` TEXT DEFAULT '', `registerTime` INTEGER DEFAULT 0, `buzId` TEXT DEFAULT '', `userType` INTEGER NOT NULL DEFAULT 0, `userStatus` INTEGER NOT NULL DEFAULT 0, `isAccountDeleted` INTEGER NOT NULL DEFAULT 0, PRIMARY KEY(`userId`))")
        db.execSQL("CREATE TABLE IF NOT EXISTS `buz_user_relation` (`userId` INTEGER NOT NULL, `muteMessages` INTEGER DEFAULT 0, `muteNotification` INTEGER DEFAULT 0, `relation` INTEGER NOT NULL DEFAULT 4, `remark` TEXT DEFAULT '', `friendTime` INTEGER NOT NULL DEFAULT 0, `isBlocked` INTEGER NOT NULL DEFAULT 0, PRIMARY KEY(`userId`))")
        //从旧用户合并表迁移数据到新表
        db.execSQL("INSERT INTO buz_user (`userId`, `userName`, `firstName`, `lastName`,`portrait`,`registerTime`,`buzId`,`userType`,`userStatus`) SELECT `userId`, `userName`, `firstName`, `lastName`,`portrait`,`registerTime`,`buzId`,`userType`,`userStatus` FROM user_relation")
        //从旧用户关系表迁移数据到新表
        db.execSQL("INSERT INTO buz_user_relation (`userId`, `muteMessages`, `muteNotification`, `relation`, `remark`, `friendTime`) SELECT `userId`, `muteMessages`, `muteNotification`, `relation`, `remark`, `friendTime` FROM user_relation")
        //从旧黑名单表迁移数据到新表
        db.execSQL("UPDATE buz_user_relation set isBlocked = 1 where userId in (select userId from black_list)")
        //新建群信息表，关系表
        db.execSQL("CREATE TABLE IF NOT EXISTS `buz_group` (`groupId` INTEGER NOT NULL, `serverGroupName` TEXT DEFAULT '', `displayName` TEXT DEFAULT '', `portraitUrl` TEXT DEFAULT '', `serverPortraitUrl` TEXT DEFAULT '', `memberNum` INTEGER NOT NULL DEFAULT 0, `maxMemberNum` INTEGER NOT NULL DEFAULT 0, `groupStatus` INTEGER NOT NULL DEFAULT 1, `groupType` INTEGER NOT NULL DEFAULT 1, `firstFewUsers` TEXT DEFAULT NULL,`oldFirstFewPortraits` TEXT DEFAULT NULL, PRIMARY KEY(`groupId`))")
        db.execSQL("CREATE TABLE IF NOT EXISTS `buz_group_extra` (`groupId` INTEGER NOT NULL, `canInvite` INTEGER NOT NULL DEFAULT 0, `canEdit` INTEGER NOT NULL DEFAULT 0, `userRole` INTEGER DEFAULT 3, `userStatus` INTEGER NOT NULL DEFAULT 2, `muteMessages` INTEGER DEFAULT 0, `muteNotification` INTEGER DEFAULT 0, PRIMARY KEY(`groupId`))")
        //从旧群信息表迁移数据到新表
        db.execSQL("INSERT INTO buz_group (`groupId`,`displayName`,`portraitUrl`,`memberNum`,`maxMemberNum`,`groupStatus`,`groupType`,`serverPortraitUrl`,`oldFirstFewPortraits`) SELECT groupId,groupName,portraitUrl,memberNum,maxMemberNum,groupStatus,groupType,serverPortraitUrl,firstFewPortraits FROM group_info")
        //从旧群关系表迁移数据到新表
        db.execSQL("INSERT INTO buz_group_extra (`groupId`,`canInvite`,`canEdit`,`userRole`,`userStatus`,`muteMessages`,`muteNotification`) SELECT groupId,canInvite,canEdit,userRole,userStatus,muteMessages,muteNotification FROM group_info")

        db.execSQL("DROP VIEW ContactUserInfo")
        db.execSQL("DROP TABLE `user_relation`")
        db.execSQL("DROP TABLE `black_list`")
        db.execSQL("DROP TABLE `local_mute_info`")
        db.execSQL("DROP TABLE `group_info`")
        db.execSQL("DROP TABLE `official_account_info`")
        //最近使用的翻译机器人语音，跟产品说了，这个可以不迁移，不影响
        db.execSQL("DROP TABLE `botinfo_recent_language_use_table`")
        db.execSQL("ALTER TABLE `botinfo_table` ADD COLUMN `status` INTEGER NOT NULL DEFAULT 0")
        db.execSQL("ALTER TABLE `group_bot_table` ADD COLUMN `joinGroupTime` INTEGER NOT NULL DEFAULT 0")

        db.execSQL("CREATE TABLE IF NOT EXISTS `buz_official_account_extra` (`userId` INTEGER NOT NULL, `description` TEXT DEFAULT '', `shortDescription` TEXT DEFAULT '', PRIMARY KEY(`userId`))")

        db.execSQL("CREATE TABLE IF NOT EXISTS `buz_user_un_uploaded_setting` (`userId` INTEGER NOT NULL, `muteMessages` INTEGER, `muteNotification` INTEGER, `remark` TEXT, PRIMARY KEY(`userId`))")
        db.execSQL("CREATE TABLE IF NOT EXISTS `buz_group_un_uploaded_setting` (`groupId` INTEGER NOT NULL, `muteMessages` INTEGER, `muteNotification` INTEGER, PRIMARY KEY(`groupId`))")
        db.execSQL("CREATE TABLE IF NOT EXISTS `_new_botinfo_table` (`botUserId` INTEGER NOT NULL, `description` TEXT NOT NULL, `topics` TEXT NOT NULL, `options` TEXT, `shortDescription` TEXT, `status` INTEGER NOT NULL DEFAULT 0, `BotUIConfigWrapper_showTopic` INTEGER, `BotUIConfigWrapper_showLanguage` INTEGER, `BotUIConfigWrapper_showVoiceStyle` INTEGER, `BotUIConfigWrapper_useRemotePortrait` INTEGER, `BotUIConfigWrapper_showTranslation` INTEGER, `BotUIConfigWrapper_showImageButton` INTEGER, `BotUIConfigWrapper_showJoinGroup` INTEGER, `BotUIConfigWrapper_showPrivateChatMsgFeedbackEntrance` INTEGER, `BotDescriptionLink_displayName` TEXT, `BotDescriptionLink_type` INTEGER, `BotDescriptionLink_scheme` TEXT, `BotDescriptionLink_extraData` TEXT, PRIMARY KEY(`botUserId`))")
        db.execSQL(
            "INSERT INTO `_new_botinfo_table` (`botUserId`,`description`,`topics`,`options`,`shortDescription`,`BotUIConfigWrapper_showTopic`,`BotUIConfigWrapper_showLanguage`,`BotUIConfigWrapper_showVoiceStyle`,`BotUIConfigWrapper_useRemotePortrait`,`BotUIConfigWrapper_showTranslation`,`BotUIConfigWrapper_showImageButton`,`BotUIConfigWrapper_showJoinGroup`,`BotUIConfigWrapper_showPrivateChatMsgFeedbackEntrance`,`BotDescriptionLink_displayName`,`BotDescriptionLink_type`,`BotDescriptionLink_scheme`,`BotDescriptionLink_extraData`) SELECT `botUserId`,`description`,`topics`,`options`,`shortDescription`,`BotUIConfigWrapper_showTopic`,`BotUIConfigWrapper_showLanguage`,`BotUIConfigWrapper_showVoiceStyle`,`BotUIConfigWrapper_useRemotePortrait`,`BotUIConfigWrapper_showTranslation`,`BotUIConfigWrapper_showImageButton`,`BotUIConfigWrapper_showJoinGroup`,`BotUIConfigWrapper_showPrivateChatMsgFeedbackEntrance`,`BotDescriptionLink_displayName`,`BotDescriptionLink_type`,`BotDescriptionLink_scheme`,`BotDescriptionLink_extraData` FROM `botinfo_table`"
        )
        db.execSQL("DROP TABLE `botinfo_table`")
        db.execSQL("ALTER TABLE `_new_botinfo_table` RENAME TO `botinfo_table`")
        db.execSQL("CREATE TABLE IF NOT EXISTS `my_official_account` (`userId` INTEGER NOT NULL, `index` INTEGER NOT NULL, PRIMARY KEY(`userId`))")
    }
}