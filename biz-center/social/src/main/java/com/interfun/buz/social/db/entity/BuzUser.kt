package com.interfun.buz.social.db.entity

import android.os.Parcelable
import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Ignore
import com.interfun.buz.base.ktx.takeIfNotNullAndNotEmpty
import com.interfun.buz.common.database.entity.UserStatus
import com.interfun.buz.common.widget.portrait.group.UserPortrait
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize

/**
 * ！！！注意 ！！！
 * ！！！注意 ！！！
 * ！！！注意 ！！！
 * 所有字段请都添加defaultValue，方便以后做部分内容更新可以直接使用upsert
 */
@Parcelize
@Entity(tableName = "buz_user", primaryKeys = ["userId"])
data class BuzUser(
    @ColumnInfo(name = "userId")
    val userId: Long,
    //格式化后的手机号
    @ColumnInfo(name = "userName", defaultValue = "")
    val userName: String? = "",
    //好友用户注册的firstName
    @ColumnInfo(name = "firstName", defaultValue = "")
    val firstName: String? = "",
    @ColumnInfo(name = "lastName", defaultValue = "")
    val lastName: String? = "",//好友用户注册的lastName
    @ColumnInfo(name = "portrait", defaultValue = "")
    val portrait: String? = "",//用户头像
    @ColumnInfo(name = "registerTime", defaultValue = "0")
    val registerTime: Long? = 0L,
    @ColumnInfo(name = "buzId", defaultValue = "")
    val buzId: String? = "",//用户buzId
    //userType .0 indicate normal user and 1 means this is a robot
    @ColumnInfo(name = "userType", defaultValue = USER_TYPE_NORMAL_USER.toString())
    val userType: Int = USER_TYPE_NORMAL_USER,
    @ColumnInfo(name = "userStatus", defaultValue = UserStatus.STATUS_NORMAL.toString())
    val userStatus: Int = UserStatus.STATUS_NORMAL,
    @ColumnInfo(name = "isAccountDeleted", defaultValue = "0")
    val isAccountDeleted: Boolean = false
    /**
     * ！！！注意 ！！！
     * ！！！注意 ！！！
     * ！！！注意 ！！！
     * 所有字段请都添加defaultValue，方便以后做部分内容更新可以直接使用upsert
     */
) : Parcelable {

    companion object {
        const val USER_TYPE_NORMAL_USER = 0
        const val USER_TYPE_ROBOT = 1
        //官方账号
        const val USER_TYPE_BUZ_RESEARCH = 2
        const val USER_TYPE_BUZ_OFFICIAL = 3
    }

    @IgnoredOnParcel
    val isOfficial get() = userType == USER_TYPE_BUZ_RESEARCH || userType == USER_TYPE_BUZ_OFFICIAL

    @IgnoredOnParcel
    val isOfficialAccount get() = userType == USER_TYPE_BUZ_OFFICIAL

    @IgnoredOnParcel
    val isResearchAccount get() = userType == USER_TYPE_BUZ_RESEARCH

    @IgnoredOnParcel
    val isRobot get() = userType == USER_TYPE_ROBOT

    @IgnoredOnParcel
    val isNormalUser get() = userType == USER_TYPE_NORMAL_USER


    fun fullNickName(relationInfo: BuzUserRelationEntity?): String {
        return relationInfo?.remark.takeIfNotNullAndNotEmpty()
            ?: this.userName.takeIfNotNullAndNotEmpty()
            ?: this.buzId.takeIfNotNullAndNotEmpty()
            ?: "${this.userId}"
    }

    val realFullName: String
        get() = listOfNotNull(firstName, lastName).joinToString(" ")

    fun firstNickName(relationInfo: BuzUserRelationEntity?): String {
        return relationInfo?.remark.takeIfNotNullAndNotEmpty()
            ?: this.firstName.takeIfNotNullAndNotEmpty()
            ?: this.lastName.takeIfNotNullAndNotEmpty()
            ?: this.userName.takeIfNotNullAndNotEmpty()
            ?: this.buzId.takeIfNotNullAndNotEmpty()
            ?: "${this.userId}"
    }

    fun toUserPortrait(): UserPortrait {
        return UserPortrait(userId, portrait)
    }
}