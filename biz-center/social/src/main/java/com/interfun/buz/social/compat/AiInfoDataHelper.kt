package com.interfun.buz.social.compat

import com.interfun.buz.common.database.entity.UserRelationInfo
import com.interfun.buz.social.db.entity.BotExtra
import com.interfun.buz.common.manager.cache.ai.TranslatorLanguage

/**
 *
 */
@Deprecated("不要再使用这个了，已经废弃,请使用 BotRepository 替代")
object AiInfoDataHelper : BotRepoCompat {

    lateinit var delegate: BotRepoCompat

    override fun fetchBotInfoFromMemPurely(robotUserId: Long): BotExtra? {
        return delegate.fetchBotInfoFromMemPurely(robotUserId)
    }

    override suspend fun fetchBotInfo(
        robotId: Long
    ): BotExtra? {
        return delegate.fetchBotInfo(robotId)
    }

    override fun getTranslatorLanguageFromMemoryOrDefault(userInfo: UserRelationInfo?): TranslatorLanguage? {
        return delegate.getTranslatorLanguageFromMemoryOrDefault(userInfo)
    }

    override fun getTranslatorLanguageFromMemoryOrDefault(botUserId: Long): TranslatorLanguage? {
        return delegate.getTranslatorLanguageFromMemoryOrDefault(botUserId)
    }
}


