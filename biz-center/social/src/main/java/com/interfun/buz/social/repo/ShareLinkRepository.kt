package com.interfun.buz.social.repo

import com.interfun.buz.common.bean.Resp
import com.interfun.buz.social.entity.ShareLinkEntity
import com.lizhi.im5.sdk.conversation.IM5ConversationType

interface ShareLinkRepository {
    fun init()
    /**
     * @param convType 请查看ShareLinkEntity的convType
     * @param timeOutDay 超时天数
     */
   suspend fun  getShareLink(targetId:Long,convType:Int): Resp<ShareLinkEntity?>

    /**
     * 点击分享卡片的时候上报，触发编辑消息更新卡片
     */
    suspend fun reportShareContactMsgClick(
        id: Long,
        contactType: Int,
        conversationType: IM5ConversationType,
        serverMsgId: Long,
        traceId: String
    )
}