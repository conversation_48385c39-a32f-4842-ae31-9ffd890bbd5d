package com.interfun.buz.translator.datasource

import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import com.interfun.buz.common.utils.fromJson
import com.interfun.buz.common.utils.language.LanguageManager
import com.interfun.buz.common.utils.language.LanguageProvider
import com.interfun.buz.common.utils.language.language_en
import com.interfun.buz.common.utils.toJson
import com.interfun.buz.component.hilt.UserQualifier
import com.interfun.buz.translator.di.TranslatorUserQualifier
import com.interfun.buz.translator.entity.AutoTranslationSelectedLanguage
import com.interfun.buz.translator.entity.TranslateLanguageBean
import com.interfun.buz.translator.storage.TranslatorDataStoreKey
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.mapNotNull
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import javax.inject.Inject

internal class AutoTranslationLocalDataSourceImpl @Inject constructor(
    @UserQualifier private val userScope: CoroutineScope,
    @TranslatorUserQualifier private val userDataStore: DataStore<Preferences>
): AutoTranslationLocalDataSource {
    private val translationCountMap = MutableStateFlow<Map<Long, Int>>(emptyMap())

    private val autoTranslateLanguage = TranslatorDataStoreKey.autoTranslateLanguage
    private val autoTranslateLanguageList = TranslatorDataStoreKey.autoTranslateLanguageList
    private val lastUsedAutoTranslateLanguageList = TranslatorDataStoreKey.lastUsedAutoTranslateLanguageList

    //------------------- 计算自动翻译居中提示语 -------------------//
    override fun observeTotalChatTranslation(conversationId: Long): Flow<Int> {
        return translationCountMap.map { map ->
            map[conversationId] ?: 0
        }.distinctUntilChanged()
    }

    override suspend fun getTotalChatTranslation(conversationId: Long): Int {
        return translationCountMap.value[conversationId] ?: 0
    }

    override suspend fun updateTotalChatTranslation(conversationId: Long, value: Int) {
        translationCountMap.update { currentMap ->
            val currentValue = currentMap[conversationId] ?: 0
            currentMap.toMutableMap().apply {
                put(conversationId, currentValue + value)
            }
        }
    }

    override suspend fun resetTotalChatTranslation(conversationId: Long) {
        translationCountMap.update { currentMap ->
            currentMap.toMutableMap().apply {
                put(conversationId, 0)
            }
        }
    }

    override suspend fun resetAllTotalChatTranslation() {
        translationCountMap.update { emptyMap() }
    }

    //------------------- 获取自动翻译优化语言 -------------------//
    override fun observeSelectedCode(): StateFlow<String> {
        return userDataStore.data.flatMapLatest { preferences ->
            val selectedCode = preferences[autoTranslateLanguage]
            if (selectedCode.isNullOrEmpty()) {
                //getReportLanguageCode返回服务端对应的 code,比如繁体服务端是 zh-hant但是我们本地是 zh-tw,
                val appLanguage = LanguageManager.getTranslationLanguageCode(LanguageManager.getLocaleLanguageCode())
                flow {
                    updateSelectedAutoTranslationLanguage(appLanguage)
                    emit(appLanguage)
                }
            } else {
                flowOf(selectedCode)
            }
        }.stateIn(userScope, SharingStarted.Eagerly, LanguageProvider.DEFAULT_LANGUAGE.code)
    }

    override fun observeAutoTranslationSelectedLanguage(): StateFlow<TranslateLanguageBean> {
        return userDataStore.data.map { preferences ->
            preferences[autoTranslateLanguageList]
                .fromJson<List<TranslateLanguageBean>>().orEmpty()
        }.combine(observeSelectedCode()) { list, selectedCode ->
            val hiltCode = list.firstOrNull { it.languageCode == selectedCode }
            hiltCode ?: list.firstOrNull { it.languageCode == language_en.code }
            ?: list.firstOrNull()
        }.mapNotNull { it }.stateIn(userScope, SharingStarted.Eagerly, TranslateLanguageBean(LanguageProvider.DEFAULT_LANGUAGE.code, LanguageProvider.DEFAULT_LANGUAGE.language))
    }

    override fun observeAutoTranslationLanguageList(): StateFlow<List<AutoTranslationSelectedLanguage>> {
        return userDataStore.data.map { preferences ->
            preferences[autoTranslateLanguageList]
                .fromJson<List<TranslateLanguageBean>>().orEmpty()
        }.combine(observeSelectedCode()) { list, selectedCode ->
            list.map { item ->
                AutoTranslationSelectedLanguage(
                    data = TranslateLanguageBean(item.languageCode, item.displayText),
                    isSelected = item.languageCode == selectedCode
                )
            }
        }.stateIn(userScope, SharingStarted.Eagerly, emptyList())
    }

    override fun observeLastUsedAutoTranslationLanguageList(): StateFlow<List<AutoTranslationSelectedLanguage>> {
        return userDataStore.data.map { preferences ->
            preferences[lastUsedAutoTranslateLanguageList]
                .fromJson<List<TranslateLanguageBean>>().orEmpty()
        }.combine(observeSelectedCode()) { list, selectedCode ->
            list.map { item ->
                AutoTranslationSelectedLanguage(
                    data = TranslateLanguageBean(item.languageCode, item.displayText),
                    isSelected = item.languageCode == selectedCode
                )
            }
        }.stateIn(userScope, SharingStarted.Eagerly, emptyList())
    }

    override suspend fun getSelectedCode(): String {
        val preferences = userDataStore.data.first()
        val selectedCode = preferences[autoTranslateLanguage]
        if (selectedCode.isNullOrEmpty()) {
            val appLanguage = LanguageManager.getLocaleLanguageCode()
            updateSelectedAutoTranslationLanguage(appLanguage)
            return appLanguage
        }
        return selectedCode
    }

    override suspend fun getAutoTranslationLanguageList(): List<AutoTranslationSelectedLanguage> {
        val selectedCode = getSelectedCode()
        val preferences = userDataStore.data.first()
        return preferences[autoTranslateLanguageList].fromJson<List<TranslateLanguageBean>>()
            ?.mapNotNull { item ->
                AutoTranslationSelectedLanguage(
                    data = TranslateLanguageBean(item.languageCode, item.displayText),
                    isSelected = item.languageCode == selectedCode
                )
            }.orEmpty()
    }

    override suspend fun updateAutoTranslationLanguageList(languageList: List<TranslateLanguageBean>) {
        userDataStore.edit { preferences ->
            preferences[autoTranslateLanguageList] = languageList.toJson()
        }
    }

    override suspend fun getLastUsedAutoTranslationLanguageList(): List<AutoTranslationSelectedLanguage> {
        val selectedCode = getSelectedCode()
        val preferences = userDataStore.data.first()
        return preferences[lastUsedAutoTranslateLanguageList].fromJson<List<TranslateLanguageBean>>()
            ?.mapNotNull { item ->
                AutoTranslationSelectedLanguage(
                    data = TranslateLanguageBean(item.languageCode, item.displayText),
                    isSelected = item.languageCode == selectedCode
                )
            }.orEmpty()
    }

    override suspend fun updateLastUsedAutoTranslationLanguageList(languageList: List<TranslateLanguageBean>) {
        userDataStore.edit { preferences ->
            preferences[lastUsedAutoTranslateLanguageList] = languageList.toJson()
        }
    }

    override suspend fun getAutoTranslationSelectedLanguage(): TranslateLanguageBean? {
        val preferences = userDataStore.data.first()
        return preferences[autoTranslateLanguageList]
            .fromJson<List<TranslateLanguageBean>>()
            ?.firstOrNull { it.languageCode == getSelectedCode() }
            ?.let { TranslateLanguageBean(it.languageCode, it.displayText) }
    }

    override suspend fun updateSelectedAutoTranslationLanguage(languageCode: String) {
        userDataStore.edit { preferences ->
            preferences[autoTranslateLanguage] = languageCode
        }
    }
}