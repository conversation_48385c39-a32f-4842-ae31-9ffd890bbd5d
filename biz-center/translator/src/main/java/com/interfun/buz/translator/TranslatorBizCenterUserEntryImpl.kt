package com.interfun.buz.translator

import com.interfun.buz.base.ktx.log
import com.interfun.buz.component.hilt.UserQualifier
import com.interfun.buz.translator.repo.AutoTranslationSettingRepository
import javax.inject.Inject

internal class TranslatorBizCenterUserEntryImpl @Inject constructor(
    @UserQualifier val autoTranslationSettingRepository: AutoTranslationSettingRepository,
) : TranslatorBizCenterUserEntry {
    companion object {
        private const val TAG = "SocialBizCenterUserEntry"
    }

    override fun init() {
        log(TAG, "init")
        autoTranslationSettingRepository.init()
    }
}