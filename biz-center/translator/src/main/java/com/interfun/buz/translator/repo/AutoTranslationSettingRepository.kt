package com.interfun.buz.translator.repo

import com.interfun.buz.common.bean.Resp
import com.interfun.buz.translator.entity.AutoTranslationSelectedLanguage
import com.interfun.buz.translator.entity.TranslateLanguageBean
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.StateFlow

interface AutoTranslationSettingRepository {
    fun init()
    //------------------- 计算自动翻译居中提示语 -------------------//
    fun observeTotalChatTranslation(conversationId: Long): Flow<Int>
    suspend fun getTotalChatTranslation(conversationId: Long): Int
    suspend fun updateTotalChatTranslation(conversationId: Long, value: Int)
    suspend fun resetTotalChatTranslation(conversationId: Long)

    //------------------- 获取自动翻译优化语言 -------------------//
    fun observeSelectedCode(): StateFlow<String>
    fun observeAutoTranslationSelectedLanguage(): StateFlow<TranslateLanguageBean>
    fun observeAutoTranslationLanguageList(): StateFlow<List<AutoTranslationSelectedLanguage>>
    fun observeLastUsedAutoTranslationLanguageList(): StateFlow<List<AutoTranslationSelectedLanguage>>

    suspend fun getSelectedCode(): String
    suspend fun requestAutoTranslationLanguageList(): Resp<List<AutoTranslationSelectedLanguage>?>
    suspend fun getAutoTranslationLanguageList(): List<AutoTranslationSelectedLanguage>
    suspend fun getLastUsedAutoTranslationLanguageList(): List<AutoTranslationSelectedLanguage>
    suspend fun insertLastUsedAutoTranslationLanguageList(languageItem: TranslateLanguageBean)
    suspend fun getAutoTranslationSelectedLanguage(): TranslateLanguageBean?
    suspend fun updateSelectedAutoTranslationLanguage(languageCode: String)
}