package com.interfun.buz.translator.di

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.PreferenceDataStoreFactory
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.preferencesDataStoreFile
import com.interfun.buz.component.hilt.UserAuth
import com.interfun.buz.component.hilt.UserComponent
import com.interfun.buz.component.hilt.UserQualifier
import com.interfun.buz.component.hilt.UserScope
import com.interfun.buz.translator.TranslatorBizCenterUserEntry
import com.interfun.buz.translator.TranslatorBizCenterUserEntryImpl
import com.interfun.buz.translator.datasource.AutoTranslationLocalDataSource
import com.interfun.buz.translator.datasource.AutoTranslationLocalDataSourceImpl
import com.interfun.buz.translator.datasource.AutoTranslationRemoteDataSource
import com.interfun.buz.translator.datasource.AutoTranslationRemoteDataSourceImpl
import com.interfun.buz.translator.repo.AutoTranslationSettingRepository
import com.interfun.buz.translator.repo.AutoTranslationSettingRepositoryImpl
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.EntryPoint
import dagger.hilt.EntryPoints
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent

@EntryPoint
@InstallIn(UserComponent::class)
internal interface TranslatorEntryPoint {
    @UserQualifier
    fun getTranslatorBizCenterUserEntry(): TranslatorBizCenterUserEntry

    @UserQualifier
    fun autoTranslationSettingRepository(): AutoTranslationSettingRepository
}

@Module
@InstallIn(UserComponent::class)
internal abstract class TranslatorUserModule {
    companion object{
        @Provides
        @TranslatorUserQualifier
        @UserScope
        fun provideTranslatorDatastore(
            @ApplicationContext appContext: Context,
            @UserQualifier userAuth: UserAuth
        ): DataStore<Preferences> {
            return PreferenceDataStoreFactory.create(scope = userAuth.userScope) {
                appContext.preferencesDataStoreFile("translator_preferences_${userAuth.userId}")
            }
        }
    }

    @Binds
    @UserQualifier
    abstract fun provideTranslatorEntry(entry: TranslatorBizCenterUserEntryImpl): TranslatorBizCenterUserEntry

    @Binds
    @UserQualifier
    @UserScope
    abstract fun provideTranslationLocalDataSource(translationLocalDataSource: AutoTranslationLocalDataSourceImpl): AutoTranslationLocalDataSource

    @Binds
    @UserQualifier
    @UserScope
    abstract fun provideTranslationRemoteDataSource(translationRemoteDataSource: AutoTranslationRemoteDataSourceImpl): AutoTranslationRemoteDataSource

    @Binds
    @UserQualifier
    @UserScope
    abstract fun provideTranslationSettingRepository(translationSettingRepositoryImpl: AutoTranslationSettingRepositoryImpl): AutoTranslationSettingRepository
}

@Module
@InstallIn(SingletonComponent::class)
internal object TranslatorModule {
    @Provides
    fun provideAutoTranslationSettingRepository(userComponent: UserComponent): AutoTranslationSettingRepository {
        return EntryPoints.get(userComponent, TranslatorEntryPoint::class.java).autoTranslationSettingRepository()
    }
}