package com.interfun.buz.translator.repo

import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.common.bean.Resp
import com.interfun.buz.common.bean.Resp.Error
import com.interfun.buz.common.bean.Resp.Success
import com.interfun.buz.common.constants.CommonMMKV
import com.interfun.buz.common.manager.CommonFlowManager
import com.interfun.buz.common.utils.parse
import com.interfun.buz.component.hilt.GlobalQualifier
import com.interfun.buz.component.hilt.UserQualifier
import com.interfun.buz.translator.datasource.AutoTranslationLocalDataSource
import com.interfun.buz.translator.datasource.AutoTranslationRemoteDataSource
import com.interfun.buz.translator.entity.AutoTranslationSelectedLanguage
import com.interfun.buz.translator.entity.TranslateLanguageBean
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

internal class AutoTranslationSettingRepositoryImpl @Inject constructor(
    @UserQualifier private val translationLocalDS: AutoTranslationLocalDataSource,
    @UserQualifier private val translationRemoteDS: AutoTranslationRemoteDataSource,
    @GlobalQualifier private val globalScope: CoroutineScope
) : AutoTranslationSettingRepository {

    companion object {
        private const val TAG = "AutoTranslationSettingRepository"
    }

    override fun init() {
        globalScope.launch {
            CommonFlowManager.walkieTalkieOnClosedFlow.collect {
                logDebug(TAG, "resetAllTotalChatTranslation")
                translationLocalDS.resetAllTotalChatTranslation()
            }
        }
    }

    //------------------- 计算自动翻译居中提示语 -------------------//
    override fun observeTotalChatTranslation(conversationId: Long): Flow<Int> {
        logDebug(TAG, "observeTotalChatTranslation")
        return translationLocalDS.observeTotalChatTranslation(conversationId)
    }

    override suspend fun getTotalChatTranslation(conversationId: Long): Int {
        logDebug(TAG, "getTotalChatTranslation")
        return translationLocalDS.getTotalChatTranslation(conversationId)
    }

    override suspend fun updateTotalChatTranslation(conversationId: Long, value: Int) {
        logDebug(TAG, "updateTotalChatTranslation: ${translationLocalDS.getTotalChatTranslation(conversationId)} + $value")
        translationLocalDS.updateTotalChatTranslation(conversationId, value)
    }

    override suspend fun resetTotalChatTranslation(conversationId: Long) {
        logDebug(TAG, "resetTotalChatTranslation")
        translationLocalDS.resetTotalChatTranslation(conversationId)
    }


    //------------------- 获取自动翻译优化语言 -------------------//
    override fun observeSelectedCode(): StateFlow<String> {
        logDebug(TAG, "observeSelectedCode")
        return translationLocalDS.observeSelectedCode()
    }

    override fun observeAutoTranslationSelectedLanguage(): StateFlow<TranslateLanguageBean> {
        logDebug(TAG, "observeAutoTranslationSelectedLanguage")
        return translationLocalDS.observeAutoTranslationSelectedLanguage()
    }

    override fun observeAutoTranslationLanguageList(): StateFlow<List<AutoTranslationSelectedLanguage>> {
        logDebug(TAG, "observeAutoTranslationLanguageList")
        return translationLocalDS.observeAutoTranslationLanguageList()
    }

    override fun observeLastUsedAutoTranslationLanguageList(): StateFlow<List<AutoTranslationSelectedLanguage>> {
        logDebug(TAG, "observeLastUsedAutoTranslationLanguageList")
        return translationLocalDS.observeLastUsedAutoTranslationLanguageList()
    }

    override suspend fun getSelectedCode(): String {
        return translationLocalDS.getSelectedCode()
    }

    override suspend fun requestAutoTranslationLanguageList(): Resp<List<AutoTranslationSelectedLanguage>?> {
        logDebug(TAG, "requestAutoTranslationLanguage")

        val resp = translationRemoteDS.requestAutoTranslationLanguageList()
        resp.prompt?.parse()

        return when (resp) {
            is Success -> {
                val list = resp.data?.mapNotNull { item ->
                    val code = item.languageCode
                    val name = item.displayName
                    if (code != null && name != null) {
                        val translateLanguageBean = TranslateLanguageBean(code, name)
                        AutoTranslationSelectedLanguage(
                            data = translateLanguageBean,
                            isSelected = code == getSelectedCode()
                        )
                    } else null
                }

                updateAutoTranslationLanguageList(list?.map { it.data }.orEmpty())
                Success(list)
            }

            is Error -> Error(resp.code, resp.msg, resp.prompt)
        }
    }

    override suspend fun getAutoTranslationLanguageList(): List<AutoTranslationSelectedLanguage> {
        logDebug(TAG, "getAutoTranslationLanguageList")
        return translationLocalDS.getAutoTranslationLanguageList().ifEmpty {
            (requestAutoTranslationLanguageList() as? Success)?.data.orEmpty()
        }
    }

    private suspend fun updateAutoTranslationLanguageList(languageList: List<TranslateLanguageBean>) {
        logDebug(TAG, "updateAutoTranslationLanguageList")
        translationLocalDS.updateAutoTranslationLanguageList(languageList)
    }

    override suspend fun getLastUsedAutoTranslationLanguageList(): List<AutoTranslationSelectedLanguage> {
        logDebug(TAG, "getLastUsedAutoTranslationLanguageList")
        return translationLocalDS.getLastUsedAutoTranslationLanguageList().ifEmpty {
            (requestAutoTranslationLanguageList() as? Success)?.data.orEmpty()
        }
    }

    override suspend fun insertLastUsedAutoTranslationLanguageList(languageItem: TranslateLanguageBean) {
        logDebug(TAG, "insertLastUsedAutoTranslationLanguageList")

        val currentList = translationLocalDS.getLastUsedAutoTranslationLanguageList()
            .map { it.data }
            .toMutableList()

        // Remove existing entry with same languageCode (if exists)
        currentList.removeAll { it.languageCode == languageItem.languageCode }

        // Add new item to the front
        currentList.add(0, languageItem)

        // Trim to max 4 items
        val updatedList = currentList.take(4)

        translationLocalDS.updateLastUsedAutoTranslationLanguageList(updatedList)
    }

    override suspend fun getAutoTranslationSelectedLanguage(): TranslateLanguageBean? {
        logDebug(TAG, "getAutoTranslationSelectedLanguage")
        return translationLocalDS.getAutoTranslationSelectedLanguage()
    }

    override suspend fun updateSelectedAutoTranslationLanguage(languageCode: String) {
        logDebug(TAG, "updateSelectedAutoTranslationLanguage: $languageCode")
        CommonMMKV.translateTargetLanguage = languageCode
        translationLocalDS.updateSelectedAutoTranslationLanguage(languageCode)
    }
}