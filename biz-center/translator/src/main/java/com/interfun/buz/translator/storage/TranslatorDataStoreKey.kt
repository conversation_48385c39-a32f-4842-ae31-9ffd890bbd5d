package com.interfun.buz.translator.storage

import androidx.datastore.preferences.core.stringPreferencesKey

object TranslatorDataStoreKey {
    val autoTranslateLanguageList = stringPreferencesKey("autoTranslateTargetLanguageList")
    val lastUsedAutoTranslateLanguageList = stringPreferencesKey("lastUsedAutoTranslateLanguageList")
    val autoTranslateLanguage = stringPreferencesKey("autoTranslateTargetLanguage")
}