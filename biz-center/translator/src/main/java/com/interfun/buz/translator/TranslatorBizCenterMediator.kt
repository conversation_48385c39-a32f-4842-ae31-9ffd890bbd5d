package com.interfun.buz.translator

import android.content.Context
import com.interfun.buz.component.hilt.UserComponentManager
import com.interfun.buz.component.hilt.GlobalQualifier
import com.interfun.buz.translator.di.TranslatorEntryPoint
import dagger.hilt.EntryPoints
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class TranslatorBizCenterMediator @Inject constructor(
    private val userComponentManager: UserComponentManager,
    @GlobalQualifier private val globalScope: CoroutineScope
) {
    companion object {
        @Volatile
        internal lateinit var appContext : Context
    }

    fun init(appContext : Context) {
        Companion.appContext = appContext
        globalScope.launch {
            userComponentManager.getUserComponentFlow().collect { userComponent ->
                EntryPoints.get(userComponent, TranslatorEntryPoint::class.java).getTranslatorBizCenterUserEntry().init()
            }
        }
    }
}