package com.interfun.buz.translator.datasource

import com.buz.idl.bot.bean.BotTranslateLanguage
import com.buz.idl.bot.request.RequestGetTranslateLanguageList
import com.buz.idl.bot.service.BuzNetBotService
import com.interfun.buz.common.bean.Resp
import com.interfun.buz.common.constants.CommonMMKV
import com.interfun.buz.common.ktx.isSuccess
import com.interfun.buz.common.utils.toJson
import com.interfun.buz.component.hilt.UserQualifier
import javax.inject.Inject

internal class AutoTranslationRemoteDataSourceImpl @Inject constructor(
    @UserQualifier private val botService: BuzNetBotService,
) : AutoTranslationRemoteDataSource {
    override suspend fun requestAutoTranslationLanguageList(): Resp<List<BotTranslateLanguage>?> {
        val resp = botService.getTranslateLanguageList(RequestGetTranslateLanguageList())
        return if (resp.isSuccess) {
            val data = resp.data
            if (data != null) {
                CommonMMKV.translateTargetLanguageList = data.translateTargetLanguage?.toJson()
                Resp.Success(data = data.translateTargetLanguage, prompt = data.prompt)
            } else {
                Resp.Success(data = null, prompt = null)
            }
        } else {
            Resp.Error(resp.code, resp.msg, resp.data?.prompt)
        }
    }
}