package com.interfun.buz.domain.voiceemoji_im.ktx

import com.buz.idl.voicemoji.bean.Voicemoji
import com.interfun.buz.biz.center.voicemoji.model.collection.CollectionType
import com.interfun.buz.biz.center.voicemoji.model.collection.VECollectionData
import com.interfun.buz.biz.center.voicemoji.model.collection.VECollectionDataType
import com.interfun.buz.biz.center.voicemoji.model.collection.VECollectionDataType.VoiceEmojiCollection
import com.interfun.buz.biz.center.voicemoji.model.collection.VECollectionDataType.VoiceGifCollection
import com.interfun.buz.biz.center.voicemoji.model.voiceemoji.VoiceEmojiCategory
import com.interfun.buz.biz.center.voicemoji.model.voiceemoji.VoiceEmojiCategoryStyle
import com.interfun.buz.biz.center.voicemoji.model.voiceemoji.convert
import com.interfun.buz.biz.center.voicemoji.model.voicegif.VoiceGifEntity
import com.interfun.buz.biz.center.voicemoji.model.voiceemoji.VoiceEmojiCategoryType
import com.lizhi.component.itnet.base.ext.gson


fun VECollectionData.toVECollectionDataType(): VECollectionDataType? {
    return kotlin.runCatching {
        when (type) {
            CollectionType.VoiceEmoji -> {
                VoiceEmojiCollection(
                    collectionInfo = this,
                    entity = gson.fromJson(data, Voicemoji::class.java).convert(
                        category = VoiceEmojiCategory(
                            name = "",
                            type = VoiceEmojiCategoryType.Unknown.type,
                            style = VoiceEmojiCategoryStyle.NO_BADGE.value
                        )
                    )
                )
            }
            else -> {
                VoiceGifEntity.parse(data)?.let {
                    VoiceGifCollection(
                        collectionInfo = this,
                        entity = it
                    )
                }
            }
        }
    }.getOrNull()
}