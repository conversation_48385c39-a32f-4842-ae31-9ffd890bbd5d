package com.interfun.buz.domain.voiceemoji_im.manager

import com.interfun.buz.biz.center.voicemoji.repository.voicegif.VoiceGifRepository
import com.interfun.buz.im.message.BuzVoiceGifMsg
import com.lizhi.im5.sdk.message.IMessage

object VoiceGifAgent {

    const val TAG = "VoiceGifAgent"
    fun updateVoiceGifRemoteUrl(msg: IMessage) {
        if (msg.content is BuzVoiceGifMsg) {
            val voiceGifMsg = msg.content as BuzVoiceGifMsg
            val remoteUrl = voiceGifMsg.remoteUrl
            val id = voiceGifMsg.id
            if (remoteUrl.isNullOrEmpty()) {
            } else {
                // 有remoteUrl，直接发送消息
                VoiceGifRepository.updateVoiceGifUsageOfRemoteUrl(id, remoteUrl)
            }
        }

    }

}