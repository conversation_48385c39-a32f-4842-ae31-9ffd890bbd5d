package com.interfun.buz.domain.voiceemoji_im.ktx

import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.biz.center.voicemoji.model.voiceemoji.VoiceEmojiCategory
import com.interfun.buz.biz.center.voicemoji.model.voiceemoji.VoiceEmojiEntity
import com.interfun.buz.common.bean.chat.MentionedUser
import com.interfun.buz.im.message.WTVoiceEmojiImgMsg
import com.interfun.buz.im.message.WTVoiceEmojiMsg
import com.interfun.buz.im.util.IMBaseParser
import org.json.JSONObject

/**
 * Author: ChenYouSheng
 * Date: 2024/12/18
 * Email: <EMAIL>
 * Desc: 迁移到domain中
 */

fun WTVoiceEmojiMsg.convert(): VoiceEmojiEntity {
    return VoiceEmojiEntity(
        emojiId,
        emojiSuperscript,
        emojiIcon,
        emojiAnimationUrl,
        emojiVoiceUrl,
        emojiNewAnimationUrl,
        emojiNewVoiceUrl,
        emojiAnimationType,
        emojiType,
        VoiceEmojiCategory(emojiCategory, emojiCategoryType.type),
        emojiDescription
    )
}
