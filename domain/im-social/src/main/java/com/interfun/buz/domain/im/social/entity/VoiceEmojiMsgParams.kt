package com.interfun.buz.domain.im.social.entity

import com.interfun.buz.im.message.ImVoiceEmojiCategoryType
import com.lizhi.im5.sdk.conversation.IM5ConversationType

class VoiceEmojiMsgParams(
    val targetId: Long,
    val convType: IM5ConversationType,
    val emojiNewAnimationUrl: String,
    val emojiNewVoiceUrl: String,
    val emojiAnimationType: Int,
    val emojiAnimationUrl: String,
    val emojiVoiceUrl: String,
    val emojiIcon: String,
    val emojiId: Long,
    val emojiSuperscript: String,
    val emojiCategory: String,
    val emojiCategoryType: ImVoiceEmojiCategoryType,
    val emojiType: Int,
    val emojiDescription: String,
    val commonMsgParams: CommonMsgParams? = null
)