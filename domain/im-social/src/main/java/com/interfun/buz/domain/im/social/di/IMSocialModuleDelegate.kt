package com.interfun.buz.domain.im.social.di

import com.interfun.buz.component.hilt.UserComponent
import com.interfun.buz.domain.im.social.repository.SendIMRepository
import dagger.Module
import dagger.Provides
import dagger.hilt.EntryPoints
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
class IMSocialModuleDelegate {

    @Provides
    fun delegateSendIMRepository(userComponent: UserComponent): SendIMRepository {
        return EntryPoints.get(userComponent, IMSocialEntryPoint::class.java).getSendIMRepository()
    }
}