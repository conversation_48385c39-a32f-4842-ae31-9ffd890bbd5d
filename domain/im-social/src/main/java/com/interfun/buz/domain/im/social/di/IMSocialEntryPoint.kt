package com.interfun.buz.domain.im.social.di

import com.interfun.buz.component.hilt.UserComponent
import com.interfun.buz.component.hilt.UserQualifier
import com.interfun.buz.domain.im.social.IMSocialDomainUserEntry
import com.interfun.buz.domain.im.social.repository.SendIMRepository
import dagger.hilt.EntryPoint
import dagger.hilt.InstallIn

@EntryPoint
@InstallIn(UserComponent::class)
internal interface IMSocialEntryPoint {
    @UserQualifier fun getSendIMRepository(): SendIMRepository
    @UserQualifier fun getIMSocialDomainUserEntry(): IMSocialDomainUserEntry
}