package com.interfun.buz.domain.im.social.entity

import com.interfun.buz.common.bean.chat.MentionedUser
import com.interfun.buz.im.entity.IMMessageContentExtra
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import org.json.JSONObject

class LocationMsgParams(
    val targetId: String,
    val sendType: IM5ConversationType,
    val locType: String,
    val lat: Double,
    val lon: Double,
    val locationName: String,
    val locationAddress: String,
    val commonMsgParams: CommonMsgParams? = null
)