package com.interfun.buz.domain.im.social.repository

import com.interfun.buz.common.bean.chat.MentionedUser
import com.interfun.buz.domain.im.social.entity.*
import com.interfun.buz.im.entity.*
import com.lizhi.im5.sdk.base.LocalFileOption
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import com.lizhi.im5.sdk.message.IMessage
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.CoroutineStart
import kotlinx.coroutines.flow.Flow
import kotlin.coroutines.CoroutineContext
import kotlin.coroutines.EmptyCoroutineContext

interface SendIMRepository {

    fun init()

    fun launchInUserScope(
        context: CoroutineContext = EmptyCoroutineContext,
        start: CoroutineStart = CoroutineStart.DEFAULT,
        block: suspend CoroutineScope.() -> Unit
    )

    suspend fun sendTextMessage(
        textMsgParams: TextMsgParams
    ): SendMsgResult

    suspend fun sendVoiceMessage(
        voiceMsgParams: VoiceMsgParams
    ): SendMsgResult

    enum class PreDeliveryType(val serverType: Int) {
        Normal(0),
        ASR(1),
        AsrAndVF(2),
    }

    suspend fun previewVoiceFilterMessage(
        voiceMsgParams: VoiceMsgParams,
        voiceFilterData: VoiceFilterInfo,
        preDeliveryType: PreDeliveryType? = PreDeliveryType.Normal,
        localFileOption: LocalFileOption = LocalFileOption.Copy
    ): VoiceFilterPreviewingItem

    suspend fun sendVoiceFilterMessage(
        voiceMsgParams: VoiceMsgParams,
        voiceFilterData: VoiceFilterInfo
    ): SendMsgResult

    suspend fun sendVoiceFilterMessage(
        previewId: String
    ): SendMsgResult

    suspend fun cancelPreviewVoiceFilter(previewId: String)

    suspend fun sendImageMsg(
        imageMsgParams: ImageMsgParams
    ): SendMsgResult

    /**
     * 发送视频消息，会做完“准备”，“压缩”，“发送”三个流程
     * https://vocalbeats.sg.larksuite.com/wiki/SJviwalxlikn5QklpsPley69gQh
     */
    suspend fun sendVideoMsg(
        videoMsgParams: VideoMsgParams,
        onCompressFinished: (VideoCompressResult) -> Unit = {},
    ): SendVideoResult

    suspend fun sendLocationMsg(
        locationMsgParams: LocationMsgParams
    ): SendMsgResult

    suspend fun sendVoiceEmojiMsg(
        msgParams: VoiceEmojiMsgParams
    ): SendMsgResult

    suspend fun sendVoiceGifMsg(
        msgParams: VoiceGifMsgParams
    ): SendMsgResult

    suspend fun sendFileMsg(
        fileMsgParams: FileMsgParams
    ): SendMsgResult?

    suspend fun sendFileMsgFlow(
        fileMsgParams: FileMsgParams
    ): Flow<SendMsgState>

    suspend fun sendFileMsgFlow(
        fileMsgParams: List<FileMsgParams>
    ): List<Flow<SendMsgState>>

    suspend fun sendShareContactMsgFlow(
        shareContactMsgParams: ShareContactParams
    ): Flow<SendMsgState>

    suspend fun forwardMessage(forwardMsgParams: ForwardMsgParams): SendMsgResult

    /**
     * 用户暂停压缩，或者压缩失败，可以重新调用这个完成“压缩”跟“发送”两个步骤
     */
    suspend fun compressVideoAndSend(
        preparedMsg: IMessage,
        onCompressFinished: (VideoCompressResult) -> Unit = {}
    ): SendVideoResult

    suspend fun createContentExtra(
        originalContentExtra: IMMessageContentExtra?,
        targetId: Long,
        conversationType: IM5ConversationType,
        mentionedMap: Collection<MentionedUser>? = null,
    ): IMMessageContentExtra

    suspend fun createServerExtra(
        originalServerExtra: ServerExtra?,
        targetId: Long,
        conversationType: IM5ConversationType,
        mentionedMap: Collection<MentionedUser>? = null
    ): ServerExtra?

    suspend fun createStyleExtra(
        originalStyleExtra: ContentStyleExtra?,
        targetId: Long,
        conversationType: IM5ConversationType,
        mentionedMap: Collection<MentionedUser>? = null
    ): ContentStyleExtra?
}