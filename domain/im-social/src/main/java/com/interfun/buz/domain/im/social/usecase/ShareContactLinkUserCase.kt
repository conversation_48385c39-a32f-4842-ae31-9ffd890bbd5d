package com.interfun.buz.domain.im.social.usecase

import android.content.Context
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.base.ktx.copyToClipboard
import com.interfun.buz.base.ktx.toast
import com.interfun.buz.common.R
import com.interfun.buz.common.bean.Resp
import com.interfun.buz.common.ktx.shareChooser
import com.interfun.buz.common.ktx.toastNetworkErrorTips
import com.interfun.buz.common.utils.shareTextBySystemDialog
import com.interfun.buz.domain.im.social.entity.ShareItemBean
import com.interfun.buz.domain.im.social.entity.ShareType
import com.interfun.buz.domain.im.social.entity.platformName
import com.interfun.buz.social.repo.ShareLinkRepository
import javax.inject.Inject


/**
 * Author: ChenYouSheng
 * Date: 2025/7/7
 * Email: <EMAIL>
 * Desc: 分享群资料和个人资料页的链接到第三方平台
 */
class ShareContactLinkUserCase @Inject constructor(private val shareLinkRepository: ShareLinkRepository) {

    sealed interface ShareLinkResult {
        data class Success(val shareType: ShareType, val link: String) : ShareLinkResult
        data class Error(val shareType: ShareType) : ShareLinkResult
    }

    /**
     * 把群/个人生成的链接分享到第三方平台
     */
    suspend operator fun invoke(
        context: Context,
        targetId: Long,
        convType: Int,
        shareItem: ShareItemBean
    ): ShareLinkResult {
        val link =
            (shareLinkRepository.getShareLink(targetId, convType) as? Resp.Success)?.data?.link
        if (link.isNullOrEmpty()) {
            toastNetworkErrorTips()
            return ShareLinkResult.Error(shareItem.type)
        }

        return when (shareItem.type) {
            ShareType.SYSTEM_SHARE -> {
                shareTextBySystemDialog(context, link)
                ShareLinkResult.Success(shareItem.type, link)
            }

            ShareType.COPY_LINK -> {
                link.copyToClipboard()
                toast(R.string.ftue_v3_linkCopied.asString())
                ShareLinkResult.Success(shareItem.type, link)
            }

            else -> {
                val success = when (shareItem.type) {
                    ShareType.MESSAGE -> {
                        shareChooser.sms.shareText(context, link)
                    }

                    ShareType.INSTAGRAM -> {
                        shareChooser.instagram.chatDestination.shareText(context, link)
                    }

                    ShareType.MESSENGER -> {
                        shareChooser.messenger.shareText(context, link)
                    }

                    ShareType.SNAPCHAT -> {
                        shareChooser.snapchat.shareText(context, link)
                    }

                    ShareType.WHATSAPP -> {
                        shareChooser.whatsapp.shareText(context, link)
                    }

                    ShareType.TELEGRAM -> {
                        shareChooser.telegram.shareTextAndUrl(context, link, null)
                    }

                    ShareType.LINE -> {
                        shareChooser.line.shareText(context, link)
                    }

                    else -> false
                }
                if (success) {
                    toast(R.string.shared_live_place_successfully.asString())
                    ShareLinkResult.Success(shareItem.type, link)
                } else {
                    toast(
                        String.format(
                            R.string.contacts_share_failed_tips.asString(),
                            shareItem.type.platformName
                        )
                    )
                    ShareLinkResult.Error(shareItem.type)
                }
            }
        }
    }
}