package com.interfun.buz.domain.im.social.usecase

import com.interfun.buz.common.utils.ClientTracker
import com.interfun.buz.domain.im.social.entity.CommonMsgParams
import com.interfun.buz.domain.im.social.entity.ContactType
import com.interfun.buz.domain.im.social.entity.ShareContactParams
import com.interfun.buz.domain.im.social.entity.UserType
import com.interfun.buz.domain.im.social.repository.SendIMRepository
import com.interfun.buz.im.entity.EventTrackExtra
import com.interfun.buz.im.entity.SendMsgState
import com.interfun.buz.social.repo.GroupRepository
import com.interfun.buz.social.repo.UserRepository
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject


/**
 * Author: ChenYouSheng
 * Date: 2025/7/7
 * Email: <EMAIL>
 * Desc: 将好友名片分享到群或者个人聊天
 */
class ShareContactCardUserCase @Inject constructor(
    private val sendIMRepository: SendIMRepository,
    private val groupRepository: GroupRepository,
    private val userRepository: UserRepository
) {

    /**
     * @param fromId:将fromId代表的用户或者群分享到targetItem
     * @param isGroupContact: true: 分享的名片是群名片，false: 分享的名片是个人名片
     * @param targetId: 分享的目标（群/个人）id
     * @param convType: 分享的目标（群/个人）的会话类型
     */
    suspend operator fun invoke(
        fromId: Long,
        isGroupContact: Boolean,
        targetId: Long,
        convType: IM5ConversationType
    ): Flow<SendMsgState> {
        val commonMsgParams = CommonMsgParams(
            replyId = null,
            eventTrackExtra = EventTrackExtra(),
            traceId = ClientTracker.generateTraceId()
        )

        val shareContactMsgParams = if (isGroupContact) {
            val buzGroup = groupRepository.getGroupCompositeFromCache(fromId)?.buzGroup
            ShareContactParams(
                id = buzGroup?.groupId ?: 0L,
                name = buzGroup?.groupName ?: "",
                portrait = buzGroup?.serverPortraitUrl ?: "",
                groupMemberCount = buzGroup?.memberNum ?: 0,
                userType = UserType.Unknown,
                buzId = "",
                targetId = targetId.toString(),
                contactType = ContactType.Group,
                convType = convType,
                commonMsgParams = commonMsgParams
            )
        } else {
            val userComposite = userRepository.getUserCompositeFromCache(fromId)
            ShareContactParams(
                id = userComposite?.user?.userId ?: 0L,
                name = userComposite?.user?.realFullName ?: "",
                portrait = userComposite?.user?.portrait ?: "",
                groupMemberCount = 0,
                userType = when {
                    userComposite?.user?.isRobot == true -> UserType.AIBot
                    userComposite?.user?.isOfficialAccount == true -> UserType.Official
                    userComposite?.user?.isResearchAccount == true -> UserType.UserResearch
                    else -> UserType.Normal
                },
                buzId = userComposite?.user?.buzId ?: "",
                targetId = targetId.toString(),
                convType = convType,
                contactType = ContactType.User,
                commonMsgParams = commonMsgParams
            )
        }

        return sendIMRepository.sendShareContactMsgFlow(shareContactMsgParams)
    }

}