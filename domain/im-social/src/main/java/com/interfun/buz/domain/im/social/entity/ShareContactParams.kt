package com.interfun.buz.domain.im.social.entity

import com.lizhi.im5.sdk.conversation.IM5ConversationType


data class ShareContactParams(
    val id: Long,// userId 或 groupId
    val name: String,// 全名，普通用户包括 firstName 和 lastName，群名或AI名
    val portrait: String, // 为空则使用默认头像兜底
    val userType: UserType,  // 用户类型：0 - 普通，1 - 机器人，2 - 用研，3 - 官方
    val groupMemberCount: Int,  // 群成员人数，members 本地进行国际化处理
    val buzId: String,  // 用户可自定义的唯一标识
    val contactType: ContactType, // 名片类型：1个人用户，2群组
    val targetId: String,
    val convType: IM5ConversationType,
    val commonMsgParams: CommonMsgParams? = null,
)


// 名片类型：1个人用户，2群组
sealed class ContactType(val type: Int) {
    data object User : ContactType(1)
    data object Group : ContactType(2)
}

// 用户类型：0 - 普通，1 - 机器人，2 - 用研，3 - 官方
sealed class UserType(val type: Int) {
    data object Normal : UserType(0)
    data object AIBot : UserType(1)
    data object UserResearch : UserType(2)
    data object Official : UserType(3)
    data object Unknown : UserType(-1)
}