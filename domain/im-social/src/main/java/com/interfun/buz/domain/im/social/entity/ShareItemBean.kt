package com.interfun.buz.domain.im.social.entity

import androidx.annotation.ColorRes

sealed class ShareItemBean(open val type: ShareType, open val shareText: String) {
    data class NormalShareItemBean(
        val iconText: String,
        @ColorRes val iconColor: Int,
        @ColorRes val backgroundColor: Int,
        override val shareText: String,
        override val type: ShareType
    ) : ShareItemBean(type,shareText)

    data class ThirdPartyShareItemBean(
        val iconRes: Int,
        override val shareText: String,
        override val type: ShareType,
    ) : ShareItemBean(type,shareText)
}
