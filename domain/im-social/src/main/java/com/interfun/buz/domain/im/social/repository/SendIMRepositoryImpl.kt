package com.interfun.buz.domain.im.social.repository

import android.content.Context
import android.webkit.MimeTypeMap
import androidx.annotation.Keep
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.bean.album.MediaExtraInfo
import com.interfun.buz.common.bean.chat.MentionedUser
import com.interfun.buz.common.bean.push.extra.BasePushExtra
import com.interfun.buz.common.bean.push.extra.GroupPushExtra
import com.interfun.buz.common.bean.push.extra.PrivateChatPushExtra
import com.interfun.buz.common.constants.TrackConstant
import com.interfun.buz.common.manager.ABTestManager
import com.interfun.buz.common.manager.AppConfigRequestManager
import com.interfun.buz.common.manager.GlobalEventManager
import com.interfun.buz.common.manager.TempImageManager
import com.interfun.buz.common.manager.cache.ai.TranslatorLanguage
import com.interfun.buz.common.manager.chat.WTGuidanceManager
import com.interfun.buz.common.utils.BuzTracker
import com.interfun.buz.common.utils.createVideoThumbnail
import com.interfun.buz.common.utils.getVideoSize
import com.interfun.buz.component.hilt.UserQualifier
import com.interfun.buz.domain.im.social.R
import com.interfun.buz.domain.im.social.entity.*
import com.interfun.buz.domain.im.social.repository.SendIMRepository.PreDeliveryType
import com.interfun.buz.domain.im.social.utils.IMSocialTracker
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import com.interfun.buz.im.IMAgent
import com.interfun.buz.im.constants.IMErrorCode
import com.interfun.buz.im.entity.*
import com.interfun.buz.im.entity.SendFileMsgState.*
import com.interfun.buz.im.ktx.localExtraModel
import com.interfun.buz.im.message.*
import com.interfun.buz.im.repo.CopyResult
import com.interfun.buz.im.repo.IMFileUploadRepository
import com.interfun.buz.media.video.compressor.ICompressorCallBack
import com.interfun.buz.media.video.compressor.ICompressorCallBack.FailureReason
import com.interfun.buz.media.video.compressor.SendVideoMsgCompressHelper
import com.interfun.buz.social.repo.BotRepository
import com.interfun.buz.social.repo.GroupRepository
import com.interfun.buz.social.repo.UserRepository
import com.lizhi.component.tekiapm.TekiApm
import com.lizhi.im5.sdk.base.LocalFileOption
import com.lizhi.im5.sdk.base.LocalFileOption.Move
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import com.lizhi.im5.sdk.conversation.IM5ConversationType.GROUP
import com.lizhi.im5.sdk.conversation.IM5ConversationType.PRIVATE
import com.lizhi.im5.sdk.message.IM5Message
import com.lizhi.im5.sdk.message.IMessage
import com.lizhi.im5.sdk.message.model.IM5VideoMessage
import com.lizhi.im5.sdk.utils.MediaFileType.FILE
import com.yibasan.lizhifm.sdk.platformtools.Const
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.*
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.consumeAsFlow
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.produceIn
import kotlinx.coroutines.selects.select
import okhttp3.internal.toLongOrDefault
import java.io.File
import java.util.TimeZone
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

internal class SendIMRepositoryImpl @Inject constructor(
    @UserQualifier val userRepository: UserRepository,
    @UserQualifier val imFileUploadRepository: IMFileUploadRepository,
    @UserQualifier val botRepository: BotRepository,
    @UserQualifier val groupRepository: GroupRepository,
    @UserQualifier val userScope: CoroutineScope,
    @ApplicationContext val appContext: Context,

    val imSocialTracker: IMSocialTracker,
) : SendIMRepository {

    private val imRepository = IMAgent

    companion object {
        const val TAG = "SendIMRepository"
    }

    override fun init() {
        userScope.launchIO {
            IMAgent.msgSendStateFlow.collect { sendStateEvent ->
                if (sendStateEvent is IMSendStateEvent.OnError) {
                    if (IMErrorCode.isVideoNotCompressed(
                            sendStateEvent.errorType, sendStateEvent.errorCode
                        )
                    ) {
                        logInfo(TAG, "compress video from im error")
                        userScope.launch {
                            compressVideoAndSend(sendStateEvent.msg)
                        }
                    }
                }
            }
        }
    }

    override fun launchInUserScope(
        context: CoroutineContext, start: CoroutineStart, block: suspend CoroutineScope.() -> Unit
    ) {
        userScope.launch(context, start, block)
    }

    override suspend fun sendTextMessage(
        textMsgParams: TextMsgParams
    ): SendMsgResult = awaitInScopeIO(userScope) {
        logInfo(TAG, "sendTextMessage,targetId:${textMsgParams.targetId},convType:${textMsgParams.convType}")
        val targetIdLong = textMsgParams.targetId.toLongOrDefault(0L)
        val textMessage = BuzTextMsg().apply {
            this.text = textMsgParams.content
            this.textMentioned = textMsgParams.mentionedText
            this.mentionedMaps = textMsgParams.mentionedMap
            val contentExtra = IMMessageContentExtra(
                eventTrackExtra = textMsgParams.commonMsgParams?.eventTrackExtra,
                contentStyleExtra = ContentStyleExtra(
                    isTopic = textMsgParams.isTopic,
                    hyperlinkMetadataExtra = textMsgParams.hyperlinkMetadataExtra
                ),
                isTopic = textMsgParams.isTopic,
                serverExtra = ServerExtra(isTopic = textMsgParams.isTopic)
            )
            val extra = createContentExtra(
                contentExtra,
                targetIdLong,
                textMsgParams.convType,
                textMsgParams.mentionedMap?.values
            )
            this.extra = extra.toJson()
        }
        val enableEncrypt = isEnableEncrypt(textMsgParams.convType, textMsgParams.targetId)
        val message = IM5Message.obtain(textMsgParams.targetId, textMsgParams.convType, textMessage)
        val pushExtra = createPushExtra(
            targetIdLong, textMsgParams.convType, message.msgType
        ).toJsonObject()
        imRepository.sendMessageSync(
            message,
            pushExtra,
            enableEncrypt,
            textMsgParams.commonMsgParams.replyId,
            traceId = textMsgParams.commonMsgParams.traceId,
        )
    }

    override suspend fun sendVoiceMessage(voiceMsgParams: VoiceMsgParams): SendMsgResult =
        awaitInScopeIO(userScope) {
            logInfo(TAG, "sendVoiceMessage,targetId:${voiceMsgParams.targetId},convType:${voiceMsgParams.convType}")
            val message = createVoiceMessage(voiceMsgParams)
            val pushExtra = createPushExtra(
                voiceMsgParams.targetId.toLongOrDefault(0L),
                voiceMsgParams.convType,
                message.msgType
            ).toJsonObject()

            val result = imRepository.sendMessageSync(
                message,
                pushExtra,
                replyId = voiceMsgParams.commonMsgParams?.replyId,
                traceId = voiceMsgParams.commonMsgParams?.traceId,
            )

            // 发送完成后清理音频文件
            cleanupAudioFileAfterSend(voiceMsgParams.path, result)

            return@awaitInScopeIO result
        }

    override suspend fun previewVoiceFilterMessage(
        voiceMsgParams: VoiceMsgParams,
        voiceFilterData: VoiceFilterInfo,
        preDeliveryType: PreDeliveryType?,
        localFileOption: LocalFileOption
    ): VoiceFilterPreviewingItem = awaitInScopeIO(userScope) {
        logInfo(TAG, "previewVoiceFilterMessage,targetId:${voiceMsgParams.targetId},convType:${voiceMsgParams.convType}")
        val message = createVoiceMessage(voiceMsgParams, voiceFilterData,preDeliveryType,localFileOption=localFileOption)
        val pushExtra = createPushExtra(
            voiceMsgParams.targetId.toLongOrDefault(0L),
            voiceMsgParams.convType,
            message.msgType,
            voiceFilterData.filterId
        ).toJsonObject()
        val previewId = imRepository.previewVoiceFilterMessage(
            message,
            pushExtra,
            replyId = voiceMsgParams.commonMsgParams?.replyId,
            traceId = voiceMsgParams.commonMsgParams?.traceId,
        )
        val item = VoiceFilterPreviewingItem(message, previewId)
        return@awaitInScopeIO item
    }

    override suspend fun sendVoiceFilterMessage(
        voiceMsgParams: VoiceMsgParams, voiceFilterData: VoiceFilterInfo
    ): SendMsgResult = awaitInScopeIO(userScope) {
        logInfo(TAG, "sendVoiceFilterMessage,targetId:${voiceMsgParams.targetId},convType:${voiceMsgParams.convType}")
        val message = createVoiceMessage(voiceMsgParams, voiceFilterData)
        val pushExtra = createPushExtra(
            voiceMsgParams.targetId.toLongOrDefault(0L),
            voiceMsgParams.convType,
            message.msgType,
            voiceFilterData.filterId
        ).toJsonObject()

        val result = imRepository.sendVoiceFilterMessage(
            message,
            pushExtra,
            replyId = voiceMsgParams.commonMsgParams?.replyId,
            traceId = voiceMsgParams.commonMsgParams?.traceId,
        )

        // 发送完成后清理音频文件
        cleanupAudioFileAfterSend(voiceMsgParams.path, result)

        return@awaitInScopeIO result
    }

    override suspend fun sendVoiceFilterMessage(previewId: String): SendMsgResult =
        awaitInScopeIO(userScope) {
            logInfo(TAG, "sendVoiceFilterMessage,previewId:$previewId")
            return@awaitInScopeIO imRepository.sendVoiceFilterMessage(previewId)
        }

    override suspend fun cancelPreviewVoiceFilter(previewId: String) = awaitInScopeIO(userScope) {
        logInfo(TAG, "cancelPreviewVoiceFilter,previewId:$previewId")
        imRepository.cancelPreviewVoiceFilter(previewId)
    }

    private suspend fun createVoiceMessage(
        voiceMsgParams: VoiceMsgParams,
        voiceFilterData: VoiceFilterInfo? = null,
        preDeliveryType: PreDeliveryType?=null,
        localFileOption:LocalFileOption = LocalFileOption.Move
    ): IM5Message {
        val targetIdLong = voiceMsgParams.targetId.toLongOrDefault(0L)
        val isRobot = isSendToRobot(
            targetIdLong, voiceMsgParams.convType, voiceMsgParams.mentionedUsers
        ) == true
        val useVoiceTextMsg = if (voiceMsgParams.convType == GROUP) {
            AppConfigRequestManager.enableAIGroupTextVoiceMsg && isRobot
        } else {
            isRobot
        }
        val content = if (useVoiceTextMsg) {
            VoiceTextMsgNew.obtain(
                voiceMsgParams.path,
                voiceMsgParams.duration,
                voiceMsgParams.mentionedUsers,
                true,
                voiceFilterData
            )
        } else {
            BuzVoiceMsg.obtain(
                voiceMsgParams.path,
                voiceMsgParams.duration,
                voiceMsgParams.mentionedUsers,
                voiceFilterData
            )
        }
        content.isEnableBase64 = true
        // 发送后移除音频文件
        content.setLocalFileOption(localFileOption)
        val isGuidanceMsg = isGuideMsg(targetIdLong, voiceMsgParams.convType)
        val contentExtra = IMMessageContentExtra(
            serverExtra = ServerExtra(guidanceMsg = isGuidanceMsg, preDeliveryType = preDeliveryType?.serverType),
            eventTrackExtra = voiceMsgParams.commonMsgParams?.eventTrackExtra,
        )
        val extra = createContentExtra(
            contentExtra, targetIdLong, voiceMsgParams.convType, voiceMsgParams.mentionedUsers
        )
        content.extra = extra.toJson()
        val message = IM5Message.obtain(voiceMsgParams.targetId, voiceMsgParams.convType, content)
        return message
    }

    private suspend fun isGuideMsg(targetId: Long, convType: IM5ConversationType): Boolean? {
        val isInGuidanceThirdStep =
            WTGuidanceManager.isInGuideMode() && WTGuidanceManager.isStepPTT()
        val isGuidanceMsg = if (isInGuidanceThirdStep && convType == PRIVATE) {
            val userInfo = userRepository.getUserFromCache(targetId)
            userInfo?.isOfficial
        } else {
            null
        }
        return isGuidanceMsg
    }

    override suspend fun sendImageMsg(
        imageMsgParams: ImageMsgParams
    ): SendMsgResult = awaitInScopeIO(userScope) {
        logInfo(
            TAG,
            "sendImageMsg,targetId:${imageMsgParams.targetId},convType:${imageMsgParams.convType},imagePath:${imageMsgParams.imagePath}, imageWidth:${imageMsgParams.imageWidth}, imageHeight:${imageMsgParams.imageHeight}"
        )
        val mimeType = MimeTypeMap.getFileExtensionFromUrl(imageMsgParams.imagePath)
        val isLongPic = Math.max(
            imageMsgParams.imageHeight.toFloat() / imageMsgParams.imageWidth,
            imageMsgParams.imageWidth.toFloat() / imageMsgParams.imageHeight
        ) > 5
        val targetIdLong = imageMsgParams.targetId.toLongOrDefault(0L)
        val imageMessage = BuzImageMessage.obtain(imageMsgParams.imagePath).apply {
            this.setLocalFileOption(imageMsgParams.localFileOption)
            this.imageWidth = imageMsgParams.imageWidth
            this.imageHeight = imageMsgParams.imageHeight
            this.resizeWidth =
                if (imageMsgParams.isSendOriginImage) 0 else if (isLongPic) 3000 else 1920
            this.resizeHeight =
                if (imageMsgParams.isSendOriginImage) 0 else if (isLongPic) 3000 else 1920
            this.compressionQuality = 90
            val contentExtra =
                IMMessageContentExtra(eventTrackExtra = imageMsgParams.commonMsgParams?.eventTrackExtra)
            this.extra =
                createContentExtra(contentExtra, targetIdLong, imageMsgParams.convType).toJson()
            this.contentType = when (mimeType) {
                "png" -> "image/png"
                "gif" -> "image/gif"
                else -> "image/jpeg"
            }
            this.orientation = imageMsgParams.orientation
        }
        val message =
            IM5Message.obtain(imageMsgParams.targetId, imageMsgParams.convType, imageMessage)
        val pushExtra = createPushExtra(
            targetIdLong, imageMsgParams.convType, message.msgType
        ).toJsonObject()
        imRepository.sendMessageSync(
            message,
            pushExtra,
            replyId = imageMsgParams.commonMsgParams?.replyId,
            traceId = imageMsgParams.commonMsgParams?.traceId,
        )
    }

    override suspend fun sendVideoMsg(
        videoMsgParams: VideoMsgParams, onCompressFinished: (VideoCompressResult) -> Unit
    ): SendVideoResult = awaitInScopeIO(userScope) {
        logInfo(TAG, "sendVideoMsg,targetId:${videoMsgParams.targetId},convType:${videoMsgParams.convType}")
        val prepareResult = prepareVideoMsg(videoMsgParams)
        if (prepareResult !is SendMsgResult.SendMsgSuccess) {
            logInfo(TAG, "prepareVideoMessage failed:${prepareResult}")
            return@awaitInScopeIO SendVideoResult.PreparedResult(prepareResult)
        }
        compressVideoAndSend(prepareResult.msg, onCompressFinished)
    }

    override suspend fun sendLocationMsg(locationMsgParams: LocationMsgParams): SendMsgResult =
        awaitInScopeIO(userScope) {
            logInfo(TAG, "sendLocationMsg,targetId:${locationMsgParams.targetId},convType:${locationMsgParams.sendType}")
            val locationMsg = BuzLocationMessage.obtainJson(
                locationMsgParams.lat,
                locationMsgParams.lon,
                locationMsgParams.locationName,
                locationMsgParams.locationAddress,
                locationMsgParams.locType
            )
            val contentExtra =
                IMMessageContentExtra(eventTrackExtra = locationMsgParams.commonMsgParams?.eventTrackExtra)
            locationMsg.extra = createContentExtra(
                contentExtra,
                locationMsgParams.targetId.toLongOrDefault(0L),
                locationMsgParams.sendType
            ).toJson()
            val message = IM5Message.obtain(
                locationMsgParams.targetId,
                locationMsgParams.sendType,
                locationMsg
            )
            val pushExtra = createPushExtra(
                locationMsgParams.targetId.toLongOrDefault(0L),
                locationMsgParams.sendType,
                message.msgType
            ).toJsonObject()
            imRepository.sendMessageSync(
                message,
                pushExtra,
                replyId = locationMsgParams.commonMsgParams?.replyId,
                traceId = locationMsgParams.commonMsgParams?.traceId,
            )
        }

    override suspend fun sendVoiceEmojiMsg(msgParams: VoiceEmojiMsgParams): SendMsgResult =
        awaitInScopeIO(userScope) {
            logInfo(TAG, "sendVoiceEmojiMsg,targetId:${msgParams.targetId},convType:${msgParams.convType},emojiId=${msgParams.emojiId}")
            val msgContent = if (msgParams.emojiType == IMVoiceEmojiType.Image.value) {
                WTVoiceEmojiImgMsg()
            } else {
                WTVoiceEmojiMsg()
            }
            msgContent.emojiNewVoiceUrl = msgParams.emojiNewVoiceUrl
            msgContent.emojiNewAnimationUrl = msgParams.emojiNewAnimationUrl
            msgContent.emojiAnimationType = msgParams.emojiAnimationType
            msgContent.emojiAnimationUrl = msgParams.emojiAnimationUrl
            msgContent.emojiVoiceUrl = msgParams.emojiVoiceUrl
            msgContent.emojiId = msgParams.emojiId
            msgContent.emojiSuperscript = msgParams.emojiSuperscript
            msgContent.emojiIcon = msgParams.emojiIcon
            msgContent.emojiCategory = msgParams.emojiCategory
            msgContent.emojiCategoryType = msgParams.emojiCategoryType
            msgContent.emojiType = msgParams.emojiType
            msgContent.emojiDescription = msgParams.emojiDescription
            msgContent.remoteUrl = msgContent.getAudioUrl()
            val isGuidanceMsg = isGuideMsg(msgParams.targetId, msgParams.convType)
            val contentExtra = IMMessageContentExtra(
                serverExtra = ServerExtra(guidanceMsg = isGuidanceMsg),
                eventTrackExtra = msgParams.commonMsgParams?.eventTrackExtra,
            )
            val extra = createContentExtra(
                contentExtra, msgParams.targetId, msgParams.convType
            )
            msgContent.extra = extra.toJson()
            val message =
                IM5Message.obtain(msgParams.targetId.toString(), msgParams.convType, msgContent)
            val pushExtra = createPushExtra(
                msgParams.targetId, msgParams.convType, message.msgType
            ).toJsonObject()
            return@awaitInScopeIO imRepository.sendMessageSync(
                message,
                pushExtra,
                replyId = msgParams.commonMsgParams?.replyId,
                traceId = msgParams.commonMsgParams?.traceId,
                isSendMediaMessage = false
            )
        }

    override suspend fun sendVoiceGifMsg(msgParams: VoiceGifMsgParams): SendMsgResult =
        awaitInScopeIO(userScope) {
            logInfo(TAG, "sendVoiceGifMsg,targetId:${msgParams.targetId},convType:${msgParams.convType},gifId=${msgParams.id}")
            val msgContent = BuzVoiceGifMsg.obtain(
                msgParams.id,
                msgParams.videoUrl,
                msgParams.animationUrl,
                msgParams.thumbnailUrl,
                msgParams.smallThumbnailUrl,
                msgParams.duration,
                msgParams.width,
                msgParams.height,
                msgParams.remoteUrl,
                null
            )
            val isGuidanceMsg = isGuideMsg(msgParams.targetId, msgParams.convType)
            val contentExtra = IMMessageContentExtra(
                serverExtra = ServerExtra(guidanceMsg = isGuidanceMsg),
                eventTrackExtra = msgParams.commonMsgParams?.eventTrackExtra,
            )
            val extra = createContentExtra(
                contentExtra, msgParams.targetId, msgParams.convType
            )
            msgContent.extra = extra.toJson()
            val message =
                IM5Message.obtain(msgParams.targetId.toString(), msgParams.convType, msgContent)
            val pushExtra = createPushExtra(
                msgParams.targetId, msgParams.convType, message.msgType
            ).toJsonObject()
            val sendPreprocessMsg = if (msgContent.remoteUrl.isNullOrEmpty()) {
                logInfo(TAG,"sendVoiceGif by Preprocess")
                true
            } else {
                logInfo(TAG,"sendVoiceGif by Normal")
                false
            }
            if (sendPreprocessMsg) {
                imRepository.sendPreprocessMessageSync(
                    message,
                    pushExtra,
                    replyId = msgParams.commonMsgParams?.replyId,
                    traceId = msgParams.commonMsgParams?.traceId,
                )
            } else {
                imRepository.sendMessageSync(
                    message,
                    pushExtra,
                    replyId = msgParams.commonMsgParams?.replyId,
                    traceId = msgParams.commonMsgParams?.traceId,
                    isSendMediaMessage = false
                )
            }
        }

    override suspend fun sendFileMsg(fileMsgParams: FileMsgParams): SendMsgResult? =
        awaitInScopeIO(userScope) {
            sendFileMsgFlow(fileMsgParams).firstOrNull { it is SendMsgResult } as? SendMsgResult
        }

    override suspend fun sendFileMsgFlow(fileMsgParams: FileMsgParams): Flow<SendMsgState> =
        awaitInScopeIO(userScope) {
            val channel = Channel<SendMsgState>(capacity = Channel.UNLIMITED)
            val filePath = fileMsgParams.filePath
            var localPath : String? = ""
            if (filePath is FilePath.UriPath) {
                channel.trySend(CopyFileStart(filePath.uri))
                logInfo(TAG, "sendFileMsgFlow copy file start: targetId=${fileMsgParams.targetId},convType=${fileMsgParams.convType},uri=${filePath}")
                val copyDeferred = async {
                    imFileUploadRepository.singleCopySync(filePath.uri)
                }
                val closeAppChannel = GlobalEventManager.onServiceDestroyStateFlow.filter { it }.produceIn(this)
                val result = try {
                    select<SendFileMsgState> {
                        copyDeferred.onAwait { copyResult ->
                            if (!isActive) return@onAwait CopyFileError(
                                uri = filePath.uri, errorMsg = "copy job canceled"
                            )
                            return@onAwait when (copyResult) {
                                is CopyResult.CopyError -> {
                                    CopyFileError(uri = filePath.uri, errorMsg = copyResult.errorMsg)
                                }

                                is CopyResult.CopySuccess -> {
                                    CopyFileEnd(uri = filePath.uri, localPath = copyResult.path)
                                }
                            }
                        }

                        closeAppChannel.onReceiveCatching { result ->
                            if (result.isClosed) {
                                logInfo(TAG, "sendFileMsgFlow closeAppChannel is closed before receiving")
                                copyDeferred.cancel("closeAppChannel is closed")
                                CopyFileError(uri = filePath.uri, errorMsg = "user quit the app (channel closed)")
                            } else {
                                logInfo(TAG, "sendFileMsgFlow closeAppChannel is received")
                                copyDeferred.cancel("user quit the app")
                                CopyFileError(uri = filePath.uri, errorMsg = "user quit the app")
                            }
                        }
                    }
                } finally {
                    closeAppChannel.cancel()
                }
                channel.trySend(result)

                if (result is CopyFileEnd) {
                    logInfo(TAG, "sendFileMsgFlow copy success: $result")
                    localPath = result.localPath
                } else if (result is CopyFileError) {
                    logError(TAG, "sendFileMsgFlow copy error: $result")
                    imFileUploadRepository.cleanUploadFileByUri(uri = result.uri)
                    return@awaitInScopeIO channel.consumeAsFlow()
                }
            } else if (filePath is FilePath.LocalPath) {
                localPath = filePath.localPath
            }
            logInfo(
                TAG,
                "sendFileMsgFlow start send: targetId=${fileMsgParams.targetId}" +
                        ",convType=${fileMsgParams.convType},filePath=${filePath},localPath=${localPath}"
            )
            val targetIdLong = fileMsgParams.targetId.toLongOrDefault(0L)
            val convType = fileMsgParams.convType
            val fileMsg = BuzFileMessage.obtain(
                filePath = localPath ?: "",
                fileName = fileMsgParams.fileName,
                mimeType = fileMsgParams.mimeType,
                fileExtension = fileMsgParams.fileExtension,
                fileSize = fileMsgParams.fileSize
            ).apply {
                val contentExtra =
                    IMMessageContentExtra(eventTrackExtra = fileMsgParams.commonMsgParams?.eventTrackExtra)
                this.extra =
                    createContentExtra(contentExtra, targetIdLong, convType).toJson()
                this.mediaFileType = FILE
                this.setLocalFileOption(Move)
            }
            val message =
                IM5Message.obtain(fileMsgParams.targetId, fileMsgParams.convType, fileMsg)
            val pushExtra = createPushExtra(
                targetId = targetIdLong,
                convType = fileMsgParams.convType,
                msgType = message.msgType,
                fileName = fileMsgParams.fileName
            ).toJsonObject()

            imRepository.sendMessageFlow(
                message = message,
                pushPayloadExtra = pushExtra,
                replyId = fileMsgParams.commonMsgParams?.replyId,
                traceId = fileMsgParams.commonMsgParams?.traceId,
                channel = channel
            )
        }

    override suspend fun sendFileMsgFlow(fileMsgParams: List<FileMsgParams>): List<Flow<SendMsgState>> {
        return fileMsgParams.map { sendFileMsgFlow(it) }
    }

    override suspend fun sendShareContactMsgFlow(shareContactMsgParams: ShareContactParams): Flow<SendMsgState> =
        awaitInScopeIO(userScope) {
            val channel = Channel<SendMsgState>(capacity = Channel.UNLIMITED)
            val targetIdLong = shareContactMsgParams.targetId.toLongOrDefault(0L)
            val convType = shareContactMsgParams.convType
            val msgContent = BuzShareContactMessage.obtain(
                id = shareContactMsgParams.id,
                name = shareContactMsgParams.name,
                portrait = shareContactMsgParams.portrait,
                userType = shareContactMsgParams.userType.type,
                groupMemberCount = shareContactMsgParams.groupMemberCount,
                buzId = shareContactMsgParams.buzId,
                contactType = shareContactMsgParams.contactType.type
            ).apply {
                val contentExtra =
                    IMMessageContentExtra(eventTrackExtra = shareContactMsgParams.commonMsgParams?.eventTrackExtra)
                this.extra = createContentExtra(
                    originalContentExtra = contentExtra,
                    targetId = targetIdLong,
                    conversationType = convType
                ).toJson()
            }
            val message = IM5Message.obtain(
                shareContactMsgParams.targetId,
                shareContactMsgParams.convType,
                msgContent
            )
            val pushExtra = createPushExtra(
                targetId = targetIdLong,
                convType = shareContactMsgParams.convType,
                msgType = message.msgType,
            ).toJsonObject()

            imRepository.sendMessageFlow(
                message = message,
                pushPayloadExtra = pushExtra,
                replyId = shareContactMsgParams.commonMsgParams?.replyId,
                traceId = shareContactMsgParams.commonMsgParams?.traceId,
                channel = channel
            )
        }

    override suspend fun forwardMessage(forwardMsgParams: ForwardMsgParams): SendMsgResult =
        awaitInScopeIO(userScope) {
            logInfo(TAG, "forwardMessage,targetId:${forwardMsgParams.targetId},convType:${forwardMsgParams.targetConvType},msgId:${forwardMsgParams.message.msgId}")
            val pushExtra = createPushExtra(
                forwardMsgParams.targetId,
                forwardMsgParams.targetConvType,
                forwardMsgParams.message.msgType
            ).toJsonObject()
            return@awaitInScopeIO imRepository.forwardMessageSync(
                forwardMsgParams.message,
                forwardMsgParams.targetConvType,
                forwardMsgParams.targetId.toString(),
                pushExtra
            )
        }


    override suspend fun compressVideoAndSend(
        preparedMsg: IMessage, onCompressFinished: (VideoCompressResult) -> Unit
    ): SendVideoResult = awaitInScopeIO(userScope) {
        logInfo(TAG, "compressVideoAndSend(),targetId=${preparedMsg.targetId},convType=${preparedMsg.conversationType},msgId=${preparedMsg.msgId}")
        val compressResult = compressVideo(preparedMsg)
        onCompressFinished(compressResult)
        sendCompressedVideo(preparedMsg, compressResult)
    }

    private suspend fun prepareVideoMsg(videoMsgParams: VideoMsgParams): SendMsgResult =
        awaitInScopeIO(userScope) {
            logInfo(TAG, "prepareVideoMsg(),targetId=${videoMsgParams.targetId},convType=${videoMsgParams.convType}")
            val mediaExtraInfo = getMediaExtraInfo(
                videoMsgParams.coverPath,
                videoMsgParams.orgVideoPath,
            )
            val thumbImage = mediaExtraInfo.getVideoThumbnail()
            val videoWidth =
                mediaExtraInfo.getWidth().takeIf { it > 0 } ?: videoMsgParams.videoWidth
            val videoHeight =
                mediaExtraInfo.getHeight().takeIf { it > 0 } ?: videoMsgParams.videoHeight
            val coverPathFile = File(thumbImage ?: "")
            val orgVideoPathFile = File(videoMsgParams.orgVideoPath)
            var coverPathFileExists = false
            if (thumbImage.notEmpty()) {
                coverPathFileExists = coverPathFile.exists()
                assert(coverPathFileExists)
            }
            var orgVideoPathFileExists = false
            if (videoMsgParams.orgVideoPath.notEmpty()) {
                orgVideoPathFileExists = orgVideoPathFile.exists()
                assert(orgVideoPathFileExists)
            }
            BuzTracker.onTechTrack {
                put(TrackConstant.KEY_EXCLUSIVE_ID, "TT2024060203")
                put(TrackConstant.KEY_EVENT_NAME, "SendVideoCheck")
                put(TrackConstant.KEY_CONTENT_1, thumbImage ?: "")
                put(TrackConstant.KEY_CONTENT_2, videoMsgParams.orgVideoPath)
                put(TrackConstant.KEY_CONTENT_3, "${if (coverPathFileExists) 1 else 0}")
                put(TrackConstant.KEY_CONTENT_4, "${if (orgVideoPathFileExists) 1 else 0}")
            }

            logInfo(
                TAG,
                "prepareVideoMessage() called with: targetId = ${videoMsgParams.targetId}, coverPath = $thumbImage, orgVideoPath = ${videoMsgParams.orgVideoPath}, videoWidth = $videoWidth, videoHeight = $videoHeight, videoDuration = ${videoMsgParams.videoDuration}, sendType = ${videoMsgParams.convType}, coverPathFileExists = $coverPathFileExists , orgVideoPathFileExists = $orgVideoPathFileExists"
            )

            val videoMessageContent = BuzVideoMsg.obtain(
                thumbImage ?: "",
                videoMsgParams.orgVideoPath,
                videoWidth,
                videoHeight,
                videoMsgParams.videoDuration
            )
            // 让IM直接移动视频封面图到IM缓存目录
            videoMessageContent.setLocalFileOption(LocalFileOption.Move)
            val message = IM5Message.obtain(
                videoMsgParams.targetId, videoMsgParams.convType, videoMessageContent
            )
            val pushExtra = createPushExtra(
                videoMsgParams.targetId.toLongOrDefault(0L),
                videoMsgParams.convType,
                message.msgType
            ).toJsonObject()
            val prepareResult = imRepository.prepareVideoMsgSync(
                message,
                pushExtra,
                replyId = videoMsgParams.commonMsgParams?.replyId,
                traceId = videoMsgParams.commonMsgParams?.traceId,
            )
            logInfo(TAG, "prepareVideoMsgSync result: $prepareResult")
            return@awaitInScopeIO prepareResult
        }


    private suspend fun sendCompressedVideo(
        message: IMessage, compressResult: VideoCompressResult
    ): SendVideoResult = awaitInScopeIO(userScope) {
        logInfo(TAG, "sendCompressedVideo(),targetId=${message.targetId},convType=${message.conversationType},msgId=${message.msgId}")
        val msgContent = message.content as IM5VideoMessage
        logCompressResult(msgContent, compressResult)
        imSocialTracker.postCompressMediaResult(
            videoLength = msgContent.duration,
            compressDuration = compressResult.compressTime,
            isSuccess = compressResult.isSuccess,
            failReason = compressResult.failedReason?.reportFailedReason ?: ""
        )
        if (!compressResult.isSuccess) {
            logInfo(TAG, "compressVideo failed:${compressResult}")
            imRepository.prepareVideoMsgFailed(
                message.msgId,
                message.conversationType,
                compressResult.failedReason?.reportFailedReason ?: ""
            )
            if (compressResult.failedReason?.userToastReason?.isNotEmpty() == true) {
                toast(compressResult.failedReason.userToastReason)
            }
            return@awaitInScopeIO SendVideoResult.CompressResult(compressResult)
        }
        val outFile = File(compressResult.outputFile)
        val outFileExists = outFile.exists()
        assert(outFileExists)
        val preparedMsgContent = message.content as IM5VideoMessage
        assert(compressResult.outputFile == preparedMsgContent.localPath)
        if (compressResult.outputFile != preparedMsgContent.localPath || !outFileExists) {
            logInfo(
                TAG,
                "outputFile!=videoMessage.localPath outputFile=[${compressResult.outputFile}] videoMessage=[${preparedMsgContent.localPath}] outFileExists ${outFileExists}"
            )
            toast(R.string.chat_failed_to_send_message)
            //数据量化上报
            BuzTracker.onTechTrack {
                put(TrackConstant.KEY_EXCLUSIVE_ID, "TT2024060201")
                put(TrackConstant.KEY_EVENT_NAME, "CompressPathNotEqualError")
                put(TrackConstant.KEY_CONTENT_1, "${compressResult.outputFile}")
                put(TrackConstant.KEY_CONTENT_2, "${preparedMsgContent.localPath}")
                put(TrackConstant.KEY_CONTENT_3, "${message.msgId}")
            }
            imRepository.prepareVideoMsgFailed(
                message.msgId,
                message.conversationType,
                compressResult.failedReason?.reportFailedReason ?: ""
            )
            return@awaitInScopeIO SendVideoResult.CompressResult(compressResult)
        }
        val parseFromJson = message.localExtraModel()
        parseFromJson.compressVideoDone = true
        message.localExtra = parseFromJson.toString()
        val sendResult = imRepository.sendPreparedVideoMsg(message.msgId, message.conversationType)
        return@awaitInScopeIO SendVideoResult.SendResult(sendResult)
    }

    private fun logCompressResult(
        videoMessage: IM5VideoMessage, compressResult: VideoCompressResult
    ) {
        val outputFile = compressResult.outputFile
        val originInput = compressResult.originInput
        try {
            val orgVideoPath = videoMessage?.orgVideoPath ?: ""
            val localPath = videoMessage?.localPath ?: ""
            val subLocalPath = videoMessage?.subLocalPath ?: ""
            val orgVideoPathExists =
                if (orgVideoPath.isNotEmpty()) File(orgVideoPath).exists() else "path为空"
            val localPathExists =
                if (localPath.isNotEmpty()) File(localPath).exists() else "path为空"
            val subLocalPathExists =
                if (subLocalPath.isNotEmpty()) File(subLocalPath).exists() else "path为空"
            val outputFileExists =
                if (outputFile.isNotEmpty()) File(outputFile).exists() else "path为空"
            val originInputExists =
                if (originInput.isNotEmpty()) File(originInput).exists() else "path为空"

            logInfo(
                TAG,
                "CompressVideo imInfo orgVideoPath=$orgVideoPath " + "orgVideoPathExists=$orgVideoPathExists " + "localPath=$localPath " + "localPathExists=$localPathExists " + "subLocalPath=$subLocalPath " + "subLocalPathExists=$subLocalPathExists " + "outputFile=$outputFile " + "outputFileExists=$outputFileExists " + "originInput=$originInput " + "originInputExists=$originInputExists"
            )
        } catch (e: Exception) {
            logError(TAG, e)
        }
    }

    private suspend fun compressVideo(message: IMessage): VideoCompressResult {
        val videoMessage = message.content as IM5VideoMessage
        val inputFile = videoMessage.orgVideoPath
        if (inputFile.isNullOrEmpty()) {
            logInfo(TAG, "inputFile isNullOrEmpty")
            TekiApm.reportException(RuntimeException("inputFile isNullOrEmpty"))
            return VideoCompressResult(
                isSuccess = false,
                originInput = "",
                outputFile = videoMessage?.localPath ?: "",
                compressTime = 0,
                failedReason = FailureReason("inputFile isNullOrEmpty"),
                attachInfo = null
            )
        }
        return withContext(Dispatchers.Main) {
            suspendCancellableCoroutine {
                SendVideoMsgCompressHelper.execCompressVideo(inputFile,
                    videoMessage.localPath,
                    SendVideoMsgCompressHelper.messageCompressId(message),
                    callBack = object : ICompressorCallBack {
                        override fun onCompleted(
                            isSuccess: Boolean,
                            originInput: String,
                            outputFile: String,
                            compressTime: Long,
                            failedReason: FailureReason?,
                            attachInfo: Any?
                        ) {
                            it.resumeWith(
                                Result.success(
                                    VideoCompressResult(
                                        isSuccess,
                                        originInput,
                                        outputFile,
                                        compressTime,
                                        failedReason,
                                        attachInfo
                                    )
                                )
                            )
                        }
                    })
            }
        }
    }

    private suspend fun getMediaExtraInfo(
        coverPath: String?,
        videoPath: String,
    ): MediaExtraInfo {
        val mediaExtraInfo = getVideoSize(appContext, videoPath)
        var thumbBitmapPath = coverPath
        if (thumbBitmapPath.isNullOrEmpty()) {
            thumbBitmapPath = createVideoThumbnail(videoPath)?.let { thumbBitmap ->
                TempImageManager.saveBitmapInCache(thumbBitmap)
            }
            logInfo(TAG, "createVideoThumb: from create")
        } else {
            logInfo(TAG, "createVideoThumb: from cache")
        }
        logInfo(TAG, "createVideoThumb: ${thumbBitmapPath.isNullOrEmpty()}")
        mediaExtraInfo.setVideoThumbnail(thumbBitmapPath)
        return mediaExtraInfo
    }

    private suspend fun createPushExtra(
        targetId: Long, convType: IM5ConversationType, msgType: Int, filterId: Long? = null,
        fileName: String? = null
    ): BasePushExtra {
        return if (convType == IM5ConversationType.PRIVATE) {
            PrivateChatPushExtra(msgType, filterId = filterId, fileName = fileName)
        } else {
            val groupInfo = groupRepository.getGroupCompositeFromCache(targetId)
            GroupPushExtra(
                msgType,
                name = groupInfo?.buzGroup?.serverGroupName ?: "",
                portrait = groupInfo?.buzGroup?.portraitUrl ?: "",
                serverPortrait = groupInfo?.buzGroup?.serverPortraitUrl ?: "",
                groupId = targetId,
                filterId = filterId,
                fileName = fileName
            )
        }
    }

    private suspend fun isEnableEncrypt(
        conversationType: IM5ConversationType, targetId: String
    ): Boolean {
        if (!ABTestManager.getE2EESendEnable()) {
            return false
        }
        val isTargetRobot = when (conversationType) {
            IM5ConversationType.PRIVATE -> userRepository.getUserFromCache(
                targetId.toLongOrDefault(
                    0L
                )
            )?.isRobot != true

            else -> false
        }
        return !isTargetRobot
    }

    override suspend fun createContentExtra(
        originalContentExtra: IMMessageContentExtra?,
        targetId: Long,
        conversationType: IM5ConversationType,
        mentionedMap: Collection<MentionedUser>?,
    ): IMMessageContentExtra {
        return IMMessageContentExtra(
            eventTrackExtra = originalContentExtra?.eventTrackExtra,
            serverExtra = createServerExtra(
                originalContentExtra?.serverExtra, targetId, conversationType, mentionedMap
            ),
            contentStyleExtra = createStyleExtra(
                originalContentExtra?.contentStyleExtra, targetId, conversationType, mentionedMap
            ),
            isTopic = originalContentExtra?.isTopic,
        )
    }

    override suspend fun createServerExtra(
        originalServerExtra: ServerExtra?,
        targetId: Long,
        conversationType: IM5ConversationType,
        mentionedMap: Collection<MentionedUser>?
    ): ServerExtra? {
        val isSendToRobot = isSendToRobot(targetId, conversationType, mentionedMap)
        val translatorLanguage = takeIf(isSendToRobot) {
            when (conversationType) {
                IM5ConversationType.PRIVATE -> {
                    getTranslatorLanguageOrNull(targetId)
                }

                else -> null
            }
        }
        val sourceLanguage =
            originalServerExtra?.sourceLanguage ?: translatorLanguage?.sourceLanguage?.code
        val targetLanguage =
            originalServerExtra?.targetLanguage ?: translatorLanguage?.targetLanguage?.code
        val timeZoneId =
            originalServerExtra?.timeZoneId ?: takeIf(isSendToRobot) { TimeZone.getDefault().id }
        val deviceType =
            originalServerExtra?.deviceType ?: takeIf(isSendToRobot) { Const.deviceType }
        val clientVersion =
            originalServerExtra?.clientVersion ?: takeIf(isSendToRobot) { "${Const.clientVersion}" }
        val result = originalServerExtra ?: ServerExtra()
        //子字段全空时不填充本extra进入消息体
        return result.copy(
            sourceLanguage = sourceLanguage,
            targetLanguage = targetLanguage,
            timeZoneId = timeZoneId,
            deviceType = deviceType,
            clientVersion = clientVersion
        ).takeIf { it != ServerExtra.EMPTY }
    }

    override suspend fun createStyleExtra(
        originalStyleExtra: ContentStyleExtra?,
        targetId: Long,
        conversationType: IM5ConversationType,
        mentionedMap: Collection<MentionedUser>?
    ): ContentStyleExtra? {
        val isSendToBot = originalStyleExtra?.isSendToBot ?: isSendToRobot(
            targetId, conversationType, mentionedMap
        )
        val result = originalStyleExtra ?: ContentStyleExtra()
        //子字段全空时不填充本extra进入消息体
        return result.copy(
            isTopic = originalStyleExtra?.isTopic,
            isSendToBot = isSendToBot.takeIf { it == true },
            hyperlinkMetadataExtra = originalStyleExtra?.hyperlinkMetadataExtra
        ).takeIf { it != ContentStyleExtra.EMPTY }
    }

    private suspend fun getTranslatorLanguageOrNull(targetIdLong: Long): TranslatorLanguage? {
        val isRobot = userRepository.getUserFromCache(targetIdLong)?.isRobot == true
        if (!isRobot) {
            return null
        }
        val botExtra = botRepository.getBotExtraFromCache(targetIdLong)
        val isTranslator = botExtra?.botUIConfig?.showTranslation == true
        return if (isTranslator) {
            val botSetting = botRepository.getBotSettingsFromCache(targetIdLong)
            botExtra?.getTranslatorLanguageOrDefault(botSetting)
        } else {
            null
        }
    }

    private suspend fun isSendToRobot(
        targetId: Long,
        conversationType: IM5ConversationType,
        mentionedList: Collection<MentionedUser>?
    ): Boolean? {
        return when (conversationType) {
            IM5ConversationType.PRIVATE -> userRepository.getUserFromCache(targetId)?.isRobot
            IM5ConversationType.GROUP -> {
                var hasRobot: Boolean? = null
                if (mentionedList != null) {
                    for (mentionedUser in mentionedList) {
                        val user = userRepository.getUserFromCache(mentionedUser.userId)
                        if (user?.isRobot == true) {
                            hasRobot = true
                            break
                        }
                    }
                }
                hasRobot
            }

            else -> false
        }
    }

    private suspend fun <T> takeIf(predicate: Boolean?, block: suspend () -> T): T? {
        return if (predicate == true) {
            block()
        } else {
            null
        }
    }

    /**
     * 简化的音频文件删除函数
     * 在子线程中执行文件删除操作，不返回结果，确保不会崩溃
     *
     * @param filePath 要删除的文件路径
     */
    private fun deleteAudioFile(filePath: String?) {
        userScope.launch(Dispatchers.IO) {
            try {
                if (!filePath.isNullOrEmpty()) {
                    val file = java.io.File(filePath)
                    if (file.exists()) {
                        file.delete()
                        logInfo(TAG, "deleteAudioFile: deleted file: $filePath")
                    }
                }
            } catch (e: Exception) {
                logError(TAG, e, "deleteAudioFile: failed to delete file: $filePath")
            }
        }
    }

    /**
     * 发送完成后清理音频文件
     *
     * @param audioFilePath 音频文件路径
     * @param sendResult 发送结果
     */
    private fun cleanupAudioFileAfterSend(audioFilePath: String, sendResult: SendMsgResult) {
        when (sendResult) {
            is SendMsgResult.SendMsgSuccess -> {
                // 发送成功，删除临时音频文件
                deleteAudioFile(audioFilePath)
            }
            is SendMsgResult.SendMsgFailed -> {
                // 发送失败，根据错误类型决定是否删除文件
                val errorInfo = sendResult.error
                when {
                    // 网络错误，保留文件以便重试
                    errorInfo.errorCode == -1 || errorInfo.errorCode == -6 -> {
                        logInfo(TAG, "cleanupAudioFileAfterSend: keeping file due to network/server error, path: $audioFilePath")
                    }
                    else -> {
                        // 其他错误（包括用户取消），删除文件
                        deleteAudioFile(audioFilePath)
                    }
                }
            }
        }
    }
}