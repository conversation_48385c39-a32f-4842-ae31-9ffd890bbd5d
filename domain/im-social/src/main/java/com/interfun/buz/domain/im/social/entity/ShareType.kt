package com.interfun.buz.domain.im.social.entity

/**
 * Author: <PERSON><PERSON><PERSON><PERSON>hen<PERSON>
 * Date: 2025/7/7
 * Email: chenyoush<PERSON>@vocalbeats.com
 * Desc:
 */
enum class ShareType(val type:Int){
    SYSTEM_SHARE(-1000),
    QR_CODE(-1001),
    COPY_LINK(-1002),
    DOWN_LOAD(-1003),
    // Third-party app
    LINE(1),
    INSTAGRAM(2),
    WHATSAPP(3),
    TELEGRAM(4),
    MESSENGER(5),
    MESSAGE(6),
    TIKTOK(7),
    X(8),
    DISCORD(9),
    VIBER(10),
    SNAPCHAT(11),
    FACEBOOK(12);

    companion object{
        fun getShareTypeList(list:List<Int>):List<ShareType>{
            val result = mutableListOf<ShareType>()
            list.forEach {
                ShareType.entries.firstOrNull { shareType -> shareType.type == it }
                    ?.let { item -> result.add(item) }
            }
            return result
        }
    }
}

val ShareType.platformName: String
    get() = when (this) {
        ShareType.SYSTEM_SHARE -> "share"
        ShareType.COPY_LINK -> "copyLink"
        ShareType.MESSAGE -> "message"
        ShareType.INSTAGRAM -> "instagram"
        ShareType.MESSENGER -> "messenger"
        ShareType.SNAPCHAT -> "snapchat"
        ShareType.WHATSAPP -> "whatsapp"
        ShareType.TELEGRAM -> "telegram"
        ShareType.LINE -> "line"
        ShareType.QR_CODE -> "qrcode"
        ShareType.DOWN_LOAD -> "download"
        ShareType.TIKTOK -> "tiktok"
        ShareType.X -> "x"
        ShareType.DISCORD -> "discord"
        ShareType.VIBER -> "viber"
        ShareType.FACEBOOK -> "facebook"
    }
