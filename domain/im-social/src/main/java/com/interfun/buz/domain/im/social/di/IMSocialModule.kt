package com.interfun.buz.domain.im.social.di

import com.interfun.buz.component.hilt.UserComponent
import com.interfun.buz.component.hilt.UserQualifier
import com.interfun.buz.component.hilt.UserScope
import com.interfun.buz.domain.im.social.IMSocialDomainUserEntry
import com.interfun.buz.domain.im.social.IMSocialDomainUserEntryImpl
import com.interfun.buz.domain.im.social.repository.SendIMRepository
import com.interfun.buz.domain.im.social.repository.SendIMRepositoryImpl
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn

@Module
@InstallIn(UserComponent::class)
internal abstract class IMSocialModule {
    @Binds
    @UserScope
    @UserQualifier
    abstract fun bindSendIMRepository(impl: SendIMRepositoryImpl): SendIMRepository

    @Binds
    @UserScope
    @UserQualifier
    abstract fun bindIMSocialEntry(impl: IMSocialDomainUserEntryImpl): IMSocialDomainUserEntry
}