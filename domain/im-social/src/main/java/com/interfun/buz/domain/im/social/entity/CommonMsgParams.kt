package com.interfun.buz.domain.im.social.entity

import com.interfun.buz.common.utils.ClientTracker
import com.interfun.buz.im.entity.EventTrackExtra
import com.interfun.buz.im.entity.IMMessageContentExtra

/**
 * 存放一些通用的消息参数
 * @param contentExtra 默认会自己补全一些所需基本参数，业务特别的参数自行传递即可，里面会做合并
 * @param traceId 如果不传的话，最后会自动生成一个id
 */
data class CommonMsgParams(
    val replyId: Long? = null,
    val eventTrackExtra: EventTrackExtra? = null,
    val traceId: String? = ClientTracker.generateTraceId(),
)