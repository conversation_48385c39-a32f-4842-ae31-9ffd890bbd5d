package com.interfun.buz.domain.im.social.entity

import com.interfun.buz.common.bean.chat.MentionedUser
import com.interfun.buz.im.entity.HyperlinkMetadataExtra
import com.lizhi.im5.sdk.conversation.IM5ConversationType

data class TextMsgParams(
    val targetId: String,
    val convType: IM5ConversationType,
    val content: String,
    val mentionedText: String? = null,
    val mentionedMap: Map<String, MentionedUser>? = null,
    val hyperlinkMetadataExtra: HyperlinkMetadataExtra? = null,
    val isTopic: Boolean? = null,
    val commonMsgParams: CommonMsgParams = CommonMsgParams(),
)