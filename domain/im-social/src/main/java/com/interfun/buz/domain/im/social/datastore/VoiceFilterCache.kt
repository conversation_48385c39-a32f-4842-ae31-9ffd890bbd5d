package com.interfun.buz.domain.im.social.datastore

import com.interfun.buz.component.hilt.UserScope
import com.interfun.buz.domain.im.social.entity.VoiceFilterPreviewingItem
import javax.inject.Inject

@UserScope
class VoiceFilterCache @Inject constructor() {
    private val map = mutableMapOf<String, VoiceFilterPreviewingItem>()

    @Synchronized
    fun savePreviewingItem(audioPath: String, item: VoiceFilterPreviewingItem) {
        map.put(audioPath, item)
    }

    @Synchronized
    fun getPreviewingItem(audioPath: String): VoiceFilterPreviewingItem? {
        return map.get(audioPath)
    }

    @Synchronized
    fun removePreviewingItem(audioPath: String) {
        map.remove(audioPath)
    }
}