package com.interfun.buz.domain.im.social

import com.interfun.buz.base.ktx.log
import com.interfun.buz.component.hilt.UserQualifier
import com.interfun.buz.domain.im.social.repository.SendIMRepository
import javax.inject.Inject

internal class IMSocialDomainUserEntryImpl @Inject constructor(
    @UserQualifier private val sendIMRepository: SendIMRepository,
) : IMSocialDomainUserEntry {
    companion object {
        private const val TAG = "IMSocialDomainUserEntryImpl"
    }

    override fun init() {
        log(TAG,"init")
        sendIMRepository.init()
    }
}