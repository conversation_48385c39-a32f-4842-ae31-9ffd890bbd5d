package com.interfun.buz.domain.im.social.entity

import android.net.Uri
import com.lizhi.im5.sdk.conversation.IM5ConversationType

sealed interface FilePath {
    data class LocalPath(val localPath: String) : FilePath {
        override fun toString(): String {
            return localPath
        }
    }

    data class UriPath(val uri: Uri) : FilePath {
        override fun toString(): String {
            return uri.toString()
        }
    }
}

data class FileMsgParams(
    val filePath: FilePath,
    val fileName: String,
    val fileSize: Long,
    val mimeType: String,
    val fileExtension: String,
    val targetId: String,
    val convType: IM5ConversationType,
    val commonMsgParams: CommonMsgParams? = null,
)