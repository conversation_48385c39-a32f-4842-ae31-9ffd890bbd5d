package com.interfun.buz.domain.im.social.utils

import com.interfun.buz.common.constants.TrackConstant
import com.interfun.buz.common.utils.BuzTracker
import javax.inject.Inject

class IMSocialTracker @Inject constructor() {
    /**
     * 上报视频压缩结果
     */
    fun postCompressMediaResult(videoLength: Long, compressDuration: Long, isSuccess: <PERSON>olean, failReason: String?) {
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2024032504")
            put(TrackConstant.KEY_RESULT_TYPE, "compress_media_result")
            put(TrackConstant.KEY_PAGE_TYPE, "聊天页")
            put(TrackConstant.KEY_CONTENT_ID, videoLength.toString())
            put(TrackConstant.KEY_BUSINESS_NUM, compressDuration)
            put(TrackConstant.KEY_IS_SUCCESS, if (isSuccess) "success" else "fail")
            put(TrackConstant.KEY_FAIL_REASON, failReason.toString())
        }
    }
}