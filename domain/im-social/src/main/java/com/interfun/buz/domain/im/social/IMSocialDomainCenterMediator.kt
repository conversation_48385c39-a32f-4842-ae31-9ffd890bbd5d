package com.interfun.buz.domain.im.social

import com.interfun.buz.component.hilt.GlobalQualifier
import com.interfun.buz.component.hilt.UserComponentManager
import com.interfun.buz.domain.im.social.di.IMSocialEntryPoint
import dagger.hilt.EntryPoints
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class IMSocialDomainCenterMediator @Inject constructor(
    private val userComponentManager: UserComponentManager,
    @GlobalQualifier private val globalScope: CoroutineScope
) {

    fun init() {
        globalScope.launch {
            userComponentManager.getUserComponentFlow().collect { userComponent ->
                EntryPoints.get(userComponent, IMSocialEntryPoint::class.java).getIMSocialDomainUserEntry().init()
            }
        }
    }
}