package com.interfun.buz.domain.im.social.entity

import com.interfun.buz.common.widget.portrait.group.BuzPortrait
import com.interfun.buz.common.widget.portrait.group.GroupPortrait
import com.interfun.buz.common.widget.portrait.group.UserPortrait
import com.interfun.buz.social.db.entity.BuzGroupComposite
import com.interfun.buz.social.db.entity.BuzUserComposite
import com.lizhi.im5.sdk.message.IMessage

sealed interface Conversation {
    val convId: Long
    val unReadCount: Int
    val mentionMeCount: Int
    val lastMessage: IMessage?
}

data class UserConversation(
    override val convId: Long,
    override val unReadCount: Int,
    override val mentionMeCount: Int,
    override val lastMessage: IMessage?,
    val userComposite: BuzUserComposite?,
) : Conversation

data class GroupConversation(
    override val convId: Long,
    override val unReadCount: Int,
    override val mentionMeCount: Int,
    override val lastMessage: IMessage?,
    val groupComposite: BuzGroupComposite?,
) : Conversation

val Conversation.isUser get() = this is UserConversation
val Conversation.isGroup get() = this is GroupConversation

/**
 * if null, means user info not loaded yet
 */
val Conversation.isRobot: Boolean?
    get() {
        if (this !is UserConversation) {
            return false
        }
        if (userComposite == null) {
            return null
        }
        return userComposite.user.isRobot
    }

val Conversation.isOfficial: Boolean?
    get() {
        if (this !is UserConversation) {
            return false
        }
        if (userComposite == null) {
            return null
        }
        return userComposite.user.isOfficial
    }

val Conversation.displayName: String
    get() {
        return when (this) {
            is UserConversation -> userComposite?.firstNickName ?: "$convId"
            is GroupConversation -> groupComposite?.buzGroup?.groupName ?: "$convId"
        }
    }

val Conversation.portrait: BuzPortrait
    get() {
        return when (this) {
            is UserConversation -> userComposite?.user?.toUserPortrait() ?: UserPortrait(
                convId,
                null
            )

            is GroupConversation -> groupComposite?.buzGroup?.toGroupPortrait() ?: GroupPortrait(
                convId,
                null,
                null
            )
        }
    }
