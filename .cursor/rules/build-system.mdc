---
description: 
globs: 
alwaysApply: true
---
# Buz Android 构建系统

## 主要构建文件

- [build.gradle](mdc:build.gradle) - 项目级构建文件，定义全局配置和依赖
- [config.gradle](mdc:config.gradle) - 集中管理依赖版本号和构建配置
- [build_config.gradle](mdc:build_config.gradle) - 定义构建变体和编译选项
- [settings.gradle](mdc:settings.gradle) - 定义项目包含的所有模块

## 构建类型和变体

项目支持多种构建类型和变体，包括：
- debug - 开发调试版本
- release - 发布版本
- benchmark - 性能测试版本

## 依赖管理

项目通过[config.gradle](mdc:config.gradle)集中管理所有依赖版本，确保跨模块依赖一致性。

主要使用的技术栈：
1. Kotlin 1.9.24
2. AndroidX组件
3. Jetpack组件 (Room, ViewModel, Compose等)
4. Dagger Hilt 依赖注入
5. 自研组件 (通过maven私服)

## 多渠道打包

项目使用VasDolly实现多渠道打包，渠道配置在[app/app_channel.txt](mdc:app/app_channel.txt)。

## CI/CD

项目使用GitLab CI/CD进行持续集成和部署，配置在[.gitlab-ci.yml](mdc:.gitlab-ci.yml)。

## 编译命令

执行编译命令 `./gradlew app:assembleDebug`

## 编译问题解决

常见问题及解决方法：
1. Room生成问题: 运行 `./gradlew clean app:assembleDebug --no-build-cache`
2. Compose性能分析: 运行 `./gradlew clean --no-build-cache -Pandroidx.enableComposeCompilerMetrics=true -Pandroidx.enableComposeCompilerReports=true app:assembleDebug`
3. Git LFS问题: 运行 `brew install git-lfs && git lfs install && git lfs pull`
