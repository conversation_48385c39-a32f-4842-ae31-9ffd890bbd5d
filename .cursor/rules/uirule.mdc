1. 所有在
`common/src/main/res/values/iconfont.xml`出现的字符都需要使用`IconFontText`(位于common-compose/src/main/java/com/interfun/buz/compose/components/IconFont.kt)作为UI组件
2.  CheckBox 类型的使用common-compose/src/main/java/com/interfun/buz/compose/components/CheckBox.kt 下的UI组件
3.  Switch类似的使用common-compose/src/main/java/com/interfun/buz/compose/components/CommonSwitch.kt 下的UI组件
4.  文本输入框如果没有指定请使用common-compose/src/main/java/com/interfun/buz/compose/components/CommonTextField.kt 下的UI组件
5.  对于标题组件可以参考 common-compose/src/main/java/com/interfun/buz/compose/components/CommonTitleBar.kt 下的UI 组件,他左侧是返回键中间是标题,右侧是一个按钮
6.  对于loading 可以参阅 CommonLoading.kt 下的组件
7.  对于加载对话框请使用 common-compose/src/main/java/com/interfun/buz/compose/components/CommonLoadingDialog.kt 下的组件，支持配置是否可关闭、背景变暗等参数
7.  对于文本类型请读取 figma中相关字体大小映射到common-compose/src/main/java/com/interfun/buz/compose/styles/TextStyles.kt 下的 syles给 Text的style 参数
8.  如果要显示群头像或者用户头像请参阅common-compose/src/main/java/com/interfun/buz/compose/components/PortraitImage.kt 下的组件
9.  对于搜索框 参阅 common-compose/src/main/java/com/interfun/buz/compose/components/InputField.ktcommon-compose/src/main/java/com/interfun/buz/compose/components/InputField.kt 和 common-compose/src/main/java/com/interfun/buz/compose/components/CommonTextField.kt
10. 对于需要显示返回键的样式请使用 common-compose/src/main/java/com/interfun/buz/compose/components/IconFontBack.kt 下的组件
11. 对于音量等 slider 可以参看common-compose/src/main/java/com/interfun/buz/compose/components/CommonVolumeSlider.kt 下的组件
12. 对于按钮组件一定要参阅 common-compose/src/main/java/com/interfun/buz/compose/components/CommonButton.kt 下的组件,并根据 figma中的信息传入合适的CommonButtonType类型,请注意挑选正确的重载
13. 对于简单的弹窗可以参阅 common-compose/src/main/java/com/interfun/buz/compose/components/CommonAlertDialog.kt 下的组件
14. 编写 Compose 界面一定要写Preview,并且在第一行调用com.interfun.buz.compose.components.PreviewUtilsKt#InitPreview 
15. 编写半弹层请使用androidx.compose.material3.BottomSheetScaffold.kt下的BottomSheetScaffold函数,如果需要sheetDragHandle可以传入CommonSheetDragHandle
16. 如果编写拖拽条可以比如放入BottomSheetScaffold的sheetDragHandle传入CommonSheetDragHandle
17. 涉及到图片的请从 figma 下载图片放入指定资源文件夹,svg不要下载.
18. 如果检测到一个在界面顶部居中的、横向拉长的圆角矩形，通常表示可拖动的 Sheet 手柄（Drag Handle）， 可复用组件 CommonSheetDragHandle。
20. 设置颜色的时候一定要读取common/src/main/res/values/colors_new.xml和common/src/main/res/values/colors_new.xml 查看是否有已经定义好的颜色。如果定义好了请直接使用。
21. 使用 CommonLoadingDialog 组件的最佳实践：
    - 默认情况下对话框不可关闭（dismissOnBackPress=false, dismissOnClickOutside=false），适用于必须等待完成的操作
    - 对于可选的加载操作，可设置 dismissOnBackPress=true 和 dismissOnClickOutside=true 允许用户取消
    - 使用 dimAmount=0.0f 可移除背景变暗效果，适用于不希望阻挡后面界面的场景
    - 遵循项目颜色规范，优先使用 colors_new.xml 中定义的颜色