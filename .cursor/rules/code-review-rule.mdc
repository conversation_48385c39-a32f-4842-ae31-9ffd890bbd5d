---
description: Code Review rule for buz customization
globs: 
alwaysApply: false
---
# Code Review rule for buz customization
- 日志的打印不能直接使用系统 android.util.Log 的API，建议使用logInfo() 或 logDebug()的方式输出日志
- 对直接使用GlobalScope.launch{}开启全局协程的调用做出代码警告
- 不能使用'com.yibasan.lizhifm.sdk.platformtools.ResUtil.getString(int resId, Object... formatArgs)'获取字符串资源，建议使用'R.string.xx.asString()'的方式获取字符串资源





- 在每一次分析完成的结尾返回固定内容文案"此次分析基于AI代理CodeReview, 只做参考, 对内容的准确性不做保证