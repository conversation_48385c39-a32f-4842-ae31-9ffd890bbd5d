---
description: 
globs: 
alwaysApply: true
---

## 行为定义
1. 当我说"编译","编译项目","编译执行","检查编译错误"等相关指令的时候,你需要执行后面的命令去构建项目. `./gradlew app:assembleDebug`
2. 代码加载网络图片的时候请使用`coil`库
3. 代码风格尽量使用Kotlin语言以及kotlin的flow和协程作为响应式逻辑处理
4. 当你需要编写 UI 的时候可以查看java/com/interfun/buz/compose/components/目录下是否有合适的对应 UI 组件.
5. 如果在 Compose 中申请 Android 动态权限请使用rememberPermissionState,rememberLauncherForActivityResult相关类
6. 新增代码需要丰富的注释,尤其注重可读性
7. 新增代码需要注意是否要考虑混淆问题,如果需要考虑请加上@Keep注解
8. 我们的文案放入 common 模块的strings.xml,注意多语言放入不同的文件夹,默认语言为英语放入 value 文件夹.value-zh是中文文件夹...
9. 代码中涉及到的 UI 部分尽量不要使用高斯模糊代码
10. UI 请尽量使用 Compose 
11. 新建Activity 或者 Fragment 请使用 Compose作为 UI 编写框架.如果新建 Activity使用请继承com.interfun.buz.compose.base.BaseComposeActivity
12. 如果新建 Fragment 请继承com.interfun.buz.compose.base.BaseComposeFragment.
13. 所有新增的文案颜色,请放入 common模块对应的 Android 规范文件中.注意文案需要考虑多语言国际化问题.
14. 新增 Compose 一定要写对应的@Preview 方便查看.在 preview 的第一行请调用 [PreviewUtils.kt](mdc:common-compose/src/main/java/com/interfun/buz/compose/components/PreviewUtils.kt) 的`InitPreview`函数

