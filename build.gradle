
buildscript {
    apply from: "https://gitlab.lizhi.fm/component_android/Gradle_Config/raw/master/common-ci.gradle"
    ext.kotlin_version = "1.9.24"
    repositories {
        gradlePluginPortal()
        google()
        mavenCentral()  // Maven Central repository
        //公司私有服务器
        maven {
            url 'http://maven.lizhi.fm:8081/nexus/content/repositories/releases/'
            allowInsecureProtocol = true
        }
        maven {
            url 'http://maven.lizhi.fm:8081/nexus/content/repositories/snapshots/'
            allowInsecureProtocol = true
        }
        maven {
            url 'http://maven.lizhi.fm:8081/nexus/content/groups/android_public/'
            allowInsecureProtocol = true
        }
        maven {
            url 'http://maven.lizhi.fm:8081/nexus/content/repositories/android_uploadpub/'
            allowInsecureProtocol = true
        }
        maven { url = uri("https://storage.googleapis.com/r8-releases/raw") }
        maven {url 'https://developer.huawei.com/repo/'}
    }
    dependencies {
        classpath 'com.huawei.agconnect:agcp:1.9.1.301'
        classpath 'com.android.tools.build:gradle:8.8.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath "org.jetbrains.kotlin:kotlin-serialization:$kotlin_version"
        //litchi-pods插件
        classpath "com.lizhi.component.pods:litchi-pods-plugin:1.6.2"
        // Google Services plugin
        classpath "com.google.gms:google-services:4.3.15"
        // TekiAPM 插件
        classpath "com.lizhi.component.plugin:stump-v8:1.1.1"
        classpath "com.lizhi.component.plugin:tekiapm:1.0.4-plugin"
        //图片检查插件
        classpath "com.huanliao.tiya.bitmapcheck:bitmap-checker-plugin:1.4.15-Release"
        //https://github.com/JailedBird/ArouterGradlePlugin?tab=readme-ov-file
        classpath("io.github.JailedBird:arouter-gradle-plugin:1.0.1")
        classpath "com.google.android.libraries.mapsplatform.secrets-gradle-plugin:secrets-gradle-plugin:2.0.1"
        //VasDolly多渠道打包
        classpath "com.tencent.vasdolly:plugin:3.0.6"
        //classpath 'com.meituan.android.walle:plugin:1.1.7'
        classpath 'com.google.dagger:hilt-android-gradle-plugin:2.52'

    }
}
plugins {
    id 'com.google.devtools.ksp' version '1.9.24-1.0.20' apply(false)
}
apply from: "config.gradle"

allprojects {
    repositories {
        google()
        mavenCentral()  // Maven Central repository
        //公司私有服务器
        maven {
            url 'http://maven.lizhi.fm:8081/nexus/content/repositories/releases/'
            allowInsecureProtocol = true
        }
        maven {
            url 'http://maven.lizhi.fm:8081/nexus/content/repositories/snapshots/'
            allowInsecureProtocol = true
        }
        maven {
            url 'http://maven.lizhi.fm:8081/nexus/content/groups/android_public/'
            allowInsecureProtocol = true
        }
        maven {
            url 'http://maven.lizhi.fm:8081/nexus/content/repositories/android_uploadpub/'
            allowInsecureProtocol = true
        }
        maven { url "https://jitpack.io" }
        maven { url = uri("https://storage.googleapis.com/r8-releases/raw") }
        maven {url 'https://developer.huawei.com/repo/'}
        maven {url "https://maven.lokalise.com"}
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}