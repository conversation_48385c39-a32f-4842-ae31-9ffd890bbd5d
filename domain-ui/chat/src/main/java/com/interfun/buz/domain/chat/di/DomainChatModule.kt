package com.interfun.buz.domain.chat.di

import com.interfun.buz.component.hilt.UserComponent
import com.interfun.buz.component.hilt.UserQualifier
import com.interfun.buz.domain.chat.repo.MorePanelRepository
import com.interfun.buz.domain.chat.repo.MorePanelRepositoryImpl
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.EntryPoint
import dagger.hilt.EntryPoints
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@EntryPoint
@InstallIn(UserComponent::class)
internal interface DomainChatEntryPoint {

    @UserQualifier
    fun getMorePanelRepository(): MorePanelRepository
}

@EntryPoint
@InstallIn(SingletonComponent::class)
internal interface DomainChatSingletonEntryPoint {
    fun getMorePanelRepository(): MorePanelRepository
}

@Module
@InstallIn(UserComponent::class)
internal abstract class DomainChatUserModule {

    @Binds
    @UserQualifier
    abstract fun provideMorePanelRepository(morePanelRepositoryImpl: MorePanelRepositoryImpl): MorePanelRepository
}

@Module
@InstallIn(SingletonComponent::class)
internal object DomainChatModule {
    @Provides
    fun provideMorePanelRepository(userComponent: UserComponent): MorePanelRepository {
        return EntryPoints.get(userComponent, DomainChatEntryPoint::class.java).getMorePanelRepository()
    }
}

