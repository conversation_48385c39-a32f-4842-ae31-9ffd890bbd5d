package com.interfun.buz.domain.chat.viewmodel

import android.content.Intent
import android.net.Uri
import android.provider.OpenableColumns
import android.webkit.MimeTypeMap
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.manager.AppConfigRequestManager
import com.interfun.buz.common.utils.ClientTracker
import com.interfun.buz.common.utils.StorageUtil
import com.interfun.buz.common.utils.StorageUtil.toAccurateFileSize
import com.interfun.buz.domain.chat.R
import com.interfun.buz.domain.chat.entity.FileMetadata
import com.interfun.buz.domain.chat.entity.FileSendFrom
import com.interfun.buz.domain.chat.entity.FileSendFrom.EXTERNAL
import com.interfun.buz.domain.im.social.entity.CommonMsgParams
import com.interfun.buz.domain.im.social.entity.FileMsgParams
import com.interfun.buz.domain.im.social.entity.FilePath
import com.interfun.buz.domain.im.social.repository.SendIMRepository
import com.interfun.buz.im.entity.EventTrackExtra
import com.interfun.buz.im.entity.SendFileMsgState
import com.interfun.buz.im.entity.SendMsgResult
import com.interfun.buz.im.entity.SendMsgState
import com.interfun.buz.im.message.BuzFileMessage
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import com.lizhi.im5.sdk.message.IMessage
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.merge
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.concurrent.atomic.AtomicInteger
import javax.inject.Inject

enum class NetworkType {
    WIFI, MOBILE, NONE
}

sealed class ProcessingResult {
    /**
     * Represents the successful processing result of selected files
     * @param validSelectedFileList List of successfully selected files with their metadata
     * @param fileName Name of the file if user selects only one file. Only used when [validSelectedFileList]'s size is 1
     * @param showConfirmToSendDialog Whether to show a confirmation dialog before sending. True when there is only one selected file, false otherwise
     * @param containEmptyFile Indicates if any of the user-selected files has file size equal to 0
     * @param containLargeFile Indicates if any of the user-selected files exceed the maximum allowed size
     * @param totalValidSelectedFileSize The total size of selectedFiles in bytes
     */
    data class Success(
        val validSelectedFileList: List<FileMetadata>,
        val fileName: String? = null,
        val showConfirmToSendDialog: Boolean,
        val containEmptyFile: Boolean,
        val containLargeFile: Boolean,
        val totalValidSelectedFileSize: Long
    ) : ProcessingResult()

    data class InvalidFile(val uri: Uri) : ProcessingResult()
    data class ExceedMaxFileCount(val count: Int) : ProcessingResult()
    data object InsufficientStorage : ProcessingResult()
}

@HiltViewModel
class FileSelectViewModel @Inject constructor(
    val sendIMRepository: SendIMRepository,
    private val savedStateHandle: SavedStateHandle,
) : ViewModel() {

    companion object {
        private const val TAG = "FileSelectViewModel"
        private const val KEY_TARGET_ID = "file_send_target_id"
        private const val KEY_CONV_TYPE = "file_send_conv_type"
        private const val KEY_REPLY_ID = "file_send_reply_id"
    }

    private val _uploadingFlow = MutableStateFlow<Boolean>(false)
    val uploadingFlow = _uploadingFlow.asStateFlow()

    private val _fileProcessingResult = MutableSharedFlow<ProcessingResult>()
    val fileProcessingResult = _fileProcessingResult.asSharedFlow()


    fun setConversationInfo(targetId: Long, convType: IM5ConversationType, replyId: Long? = null) {
        savedStateHandle[KEY_TARGET_ID] = targetId
        savedStateHandle[KEY_CONV_TYPE] = convType
        savedStateHandle[KEY_REPLY_ID] = replyId
    }

    fun getTargetId(): Long {
        return savedStateHandle.get<Long>(KEY_TARGET_ID) ?: -1L
    }

    fun getConvType(): IM5ConversationType {
        val convTypeValue = savedStateHandle.get<IM5ConversationType>(KEY_CONV_TYPE) ?: IM5ConversationType.PRIVATE
        return convTypeValue
    }

    fun getReplyId(): Long? {
        return savedStateHandle.get<Long>(KEY_REPLY_ID)
    }

    fun handleSelectedFiles(data: Intent) {
        val fileUris = extractUrisFromIntent(data)
        if (fileUris.isEmpty()) return

        viewModelScope.launch {
            processFiles(fileUris)
        }
    }

    /** Extract file metadata from the intent data when sharing files from external applications */
    suspend fun getFileMetadataList(data: Intent): Pair<ProcessingResult, List<FileMetadata>> =
        withContext(Dispatchers.IO) {
            val fileUris = extractUrisFromIntent(data)
            val result = processFilesOnBackground(fileUris)
            val metadataList = when (result) {
                is ProcessingResult.Success -> result.validSelectedFileList
                else -> emptyList()
            }
            return@withContext result to metadataList
        }

    /** Start uploading files from external applications */
    fun startUploadExternal(
        fileMetadataList: List<FileMetadata>,
        targetId: Long? = null,
        convType: IM5ConversationType? = null,
    ) {
        startUploadAndSendFiles(
            validSelectedFiles = fileMetadataList,
            targetId = targetId,
            convType = convType,
            sendFrom = EXTERNAL
        )
    }

    private fun extractUrisFromIntent(data: Intent): List<Uri> {
        val fileUris = mutableListOf<Uri>()

        when {
            data.clipData != null -> {
                val clipData = data.clipData!!
                val numberOfSelectedFiles = clipData.itemCount
                for (i in 0 until numberOfSelectedFiles) {
                    val uri = clipData.getItemAt(i).uri
                    fileUris.add(uri)
                }
            }

            data.data != null -> {
                fileUris.add(data.data!!)
            }
        }

        return fileUris
    }

    private suspend fun processFiles(fileUris: List<Uri>) {
        val processingResult = withContext(Dispatchers.IO) {
            processFilesOnBackground(fileUris)
        }
        _fileProcessingResult.emit(processingResult)
    }

    private fun processFilesOnBackground(fileUris: List<Uri>): ProcessingResult {
        if (fileUris.size > AppConfigRequestManager.maxFileCount) {
            return ProcessingResult.ExceedMaxFileCount(fileUris.size)
        }

        val validSelectedFileList = mutableListOf<FileMetadata>()
        val largeFilesList = mutableListOf<FileMetadata>()
        val emptyFilesList = mutableListOf<FileMetadata>()
        var totalSize = 0L
        var displayFileName: String? = null

        for (uri in fileUris) {
            val fileInfo = extractFileMetadata(uri)
                ?: return ProcessingResult.InvalidFile(uri)

            if (fileUris.size == 1) {
                displayFileName = fileInfo.displayName
            }
            if (fileInfo.size == 0L) {
                // record empty file if there is any
                emptyFilesList.add(fileInfo)
            } else if (fileInfo.size > AppConfigRequestManager.maxSingleFileSize * 1024L * 1024L) {
                // record large file (exceed config limit) if there is any
                largeFilesList.add(fileInfo)
            } else {
                totalSize += fileInfo.size
                validSelectedFileList.add(fileInfo)
            }
            logInfo(TAG, buildFileInfoLog(fileInfo))
        }

        if (totalSize > StorageUtil.getDeviceAvailableSize()) {
            logInfo(TAG, "Total size of selected files (bytes): $totalSize, " +
                    "Total size of selected files: ${totalSize.toAccurateFileSize()}, " +
                    "device available size: ${StorageUtil.getDeviceAvailableSize().toAccurateFileSize()}")
            return ProcessingResult.InsufficientStorage
        }

        return ProcessingResult.Success(
            validSelectedFileList = validSelectedFileList,
            fileName = if (fileUris.size == 1) displayFileName else null,
            showConfirmToSendDialog = fileUris.size == 1,
            containLargeFile = largeFilesList.isNotEmpty(),
            containEmptyFile = emptyFilesList.isNotEmpty(),
            totalValidSelectedFileSize = totalSize
        )
    }

    private fun extractFileMetadata(uri: Uri): FileMetadata? {
        return try {
            val cursor = contentResolver.query(uri, null, null, null, null)
            cursor?.use { c ->
                if (c.moveToFirst()) {
                    // Get file name
                    val displayName = getFileNameFromUri(uri)

                    // Get file size
                    val sizeIndex = c.getColumnIndex(OpenableColumns.SIZE)
                    val size = if (sizeIndex != -1 && !c.isNull(sizeIndex)) {
                        c.getLong(sizeIndex)
                    } else {
                        getFileSizeFromStream(uri)
                    }

                    // Get file extension
                    val extension = uri.fileExtension ?: ""

                    // Get MIME type
                    val mimeType = contentResolver.getType(uri)
                        ?: getMimeTypeFromExtension(extension)
                        ?: "application/octet-stream"

                    FileMetadata(
                        uri = uri,
                        displayName = displayName,
                        size = size,
                        extension = extension,
                        mimeType = mimeType
                    )
                } else {
                    null
                }
            }
        } catch (e: Exception) {
            logError(TAG, "Error extracting file metadata for uri: $uri", e)
            null
        }
    }

    /**
     * Extracts the display name of a file from its URI
     *
     * @param uri The URI of the file
     * @return The display name of the file, or a fallback name if extraction fails
     */
    private fun getFileNameFromUri(uri: Uri): String {
        // 1. Query content provider for display name
        try {
            val cursor = contentResolver.query(uri, null, null, null, null)
            cursor?.use {
                val nameIndex = it.getColumnIndex(OpenableColumns.DISPLAY_NAME)
                if (it.moveToFirst() && nameIndex != -1) {
                    val displayName = it.getString(nameIndex)
                    if (!displayName.isNullOrEmpty()) {
                        return displayName
                    }
                }
            }
        } catch (e: Exception) {
            logError(TAG, "Error querying display name for uri: $uri", e)
        }
        
        // 2. Parse from URI path
        try {
            val pathSegment = uri.lastPathSegment
            if (!pathSegment.isNullOrEmpty()) {
                val fileName = pathSegment.substringAfterLast('/')
                if (fileName.isNotEmpty()) {
                    return fileName
                }
            }
        } catch (e: Exception) {
            logError(TAG, "Error parsing path segment for uri: $uri", e)
        }
        
        // 3. Fallback: Generate a file name with timestamp
        return "temp_file_${System.currentTimeMillis()}"
    }

    private fun getFileSizeFromStream(uri: Uri): Long {
        return try {
            contentResolver.openInputStream(uri)?.use { inputStream ->
                inputStream.available().toLong()
            } ?: 0L
        } catch (e: Exception) {
            logError(TAG, "Error getting file size from stream", e)
            0L
        }
    }

    private fun getMimeTypeFromExtension(extension: String): String? {
        return if (extension.isNotEmpty()) {
            MimeTypeMap.getSingleton().getMimeTypeFromExtension(extension.lowercase())
        } else {
            null
        }
    }

    fun startUploadAndSendFiles(
        validSelectedFiles: List<FileMetadata>,
        targetId: Long? = null,
        convType: IM5ConversationType? = null,
        replyId: Long? = null,
        sendFrom: FileSendFrom
    ) {
        val finalTargetId = targetId ?: getTargetId()
        val finalConvType = convType ?: getConvType()
        val finalReplyId = replyId ?: getReplyId()
        val pendingSendList = validSelectedFiles.map { metadata ->
            FileMsgParams(
                filePath = FilePath.UriPath(metadata.uri),
                fileName = metadata.displayName,
                fileSize = metadata.size,
                mimeType = metadata.mimeType,
                fileExtension = metadata.extension,
                targetId = finalTargetId.toString(),
                convType = finalConvType,
                commonMsgParams = CommonMsgParams(
                    replyId = finalReplyId,
                    eventTrackExtra = EventTrackExtra(sendFrom = sendFrom.value),
                    traceId = ClientTracker.generateTraceId(if (validSelectedFiles.size > 1){ validSelectedFiles.indexOf(metadata) } else null)
                )
            )
        }

        viewModelScope.launch {
            startSendFileAndDismissLoading(pendingSendList)
        }
    }

    private suspend fun startSendFileAndDismissLoading(pendingSendList: List<FileMsgParams>) {
        val startCount = AtomicInteger(pendingSendList.size)
        val isLoading = _uploadingFlow.value == true
        logInfo(
            TAG,
            "startSendFileAndDismissLoading startCount: $startCount, isLoading=${isLoading}"
        )
        val resultListFlow = sendIMRepository.sendFileMsgFlow(pendingSendList)
        // 合并所有 Flow，统一 collect
        merge(*resultListFlow.map { singleFlow ->
            singleFlow.filter {
                it is SendFileMsgState.CopyFileEnd || it is SendFileMsgState.CopyFileError
            }
        }.toTypedArray()).collect { state ->

            if (startCount.decrementAndGet() == 0) {
                _uploadingFlow.value = false
            }

            when (state) {
                is SendFileMsgState.CopyFileError -> {
                    toast(R.string.air_unknow_exception.asString())
                }
                else -> {
                    // nothing
                }
            }

            logInfo(TAG, "startSendFileAndDismissLoading startCount: ${startCount.get()}, uri=$state")
        }
    }

    private suspend fun startSendFile(pendingSendList: List<FileMsgParams>) {
        val currentSendingList = pendingSendList.toMutableList()
        val dismissLoadingBySendResult = { msg: IMessage? ->
            val fileMsg = msg?.content as? BuzFileMessage
            currentSendingList.removeIf {
                it.filePath.toString() == fileMsg?.localPath
            }
            if (currentSendingList.isEmpty()) {
                _uploadingFlow.emitInScope(viewModelScope, false)
            }
        }
        val dismissLoadingByParamsError = { copyError: SendFileMsgState.CopyFileError ->
            currentSendingList.removeIf {
                it.filePath.toString() == copyError.uri.toString()
            }
            if (currentSendingList.isEmpty()) {
                _uploadingFlow.emitInScope(viewModelScope, false)
            }
        }
        val resultListFlow = sendIMRepository.sendFileMsgFlow(pendingSendList)
        resultListFlow.forEach {
            it.collect { state ->
                when (state) {
                    is SendMsgState.SendMsgCancel -> {
                        dismissLoadingBySendResult.invoke(state.msg)
                    }

                    is SendFileMsgState.CopyFileError -> {
                        dismissLoadingByParamsError.invoke(state)
                    }

                    is SendMsgResult.SendMsgFailed -> {
                        dismissLoadingBySendResult.invoke(state.msg)
                    }

                    is SendMsgResult.SendMsgSuccess -> {
                        dismissLoadingBySendResult.invoke(state.msg)
                    }

                    is SendMsgState.SendMsgAttached -> {
                        dismissLoadingBySendResult.invoke(state.msg)
                    }

                    else -> {

                    }
                }
            }
        }
    }


    fun startsUploadingFiles() {
        viewModelScope.launch {
            _uploadingFlow.emit(true)
        }
    }

    private fun buildFileInfoLog(fileInfo: FileMetadata): String {
        return "\nuri: ${fileInfo.uri}" +
                "\ndisplayName: ${fileInfo.displayName}" +
                "\nsize: ${fileInfo.size}, ${fileInfo.size.toAccurateFileSize()}" +
                "\nextension: ${fileInfo.extension}" +
                "\nmimeType: ${fileInfo.mimeType}"
    }
}