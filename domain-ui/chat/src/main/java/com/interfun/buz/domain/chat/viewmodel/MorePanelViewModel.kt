package com.interfun.buz.domain.chat.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.interfun.buz.biz.center.voicefilter.repository.VoiceFilterDataRepository
import com.interfun.buz.common.bean.LoadingState
import com.interfun.buz.domain.chat.repo.ChatMorePanelAction
import com.interfun.buz.domain.chat.repo.MorePanelRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import javax.inject.Inject

data class MorePanelOptionUiState(
    val title: String,
    val icon: Int,
    val type: ChatMorePanelAction,
    val needsLoading: Boolean = false,
    val isLoading : Boolean = false
)

@HiltViewModel
class MorePanelViewModel @Inject constructor(
    private val morePanelRepository: MorePanelRepository,
    private val voiceFilterRepository: VoiceFilterDataRepository,
) : ViewModel() {

    companion object {
        private const val TAG = "MorePanelViewModel"
    }

    fun morePanelOptionListFlow(targetId: Long, isGroup: Boolean) = combine(
        voiceFilterRepository.voiceFilterListLoadStatusFlow.map { it is LoadingState.Loading },
        flow { emit(morePanelRepository.getOptionsList(isGroup, targetId)) }
    ) { isFilterDataLoading, optionList ->
        optionList.map { optionData ->
            MorePanelOptionUiState(
                title = optionData.title,
                icon = optionData.icon,
                type = optionData.type,
                isLoading = optionData.type is ChatMorePanelAction.VoiceFilter && isFilterDataLoading
            )
        }
    }.stateIn(viewModelScope, SharingStarted.Eagerly, emptyList())

    private val _openFromChatPage = MutableStateFlow(false)
    val openFromChatPage: StateFlow<Boolean> = _openFromChatPage.asStateFlow()

    val showMoreButtonRedDotStateFlow = morePanelRepository.showMoreButtonRedDotStateFlow

    /**
     * 通知聊天页多功能面板的开关状态
     */
    fun updateChatPageOpenStatus(open: Boolean) {
        _openFromChatPage.value = open
    }

}