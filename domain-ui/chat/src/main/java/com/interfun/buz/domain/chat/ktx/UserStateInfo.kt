package com.interfun.buz.domain.chat.ktx

import android.widget.TextView
import com.interfun.buz.base.ktx.asColor
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.base.ktx.gone
import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.base.ktx.setTextWithAutoGoneIfEmpty
import com.interfun.buz.base.ktx.visible
import com.interfun.buz.common.R
import com.interfun.buz.social.entity.OfflineState
import com.interfun.buz.social.entity.OnlineState
import com.interfun.buz.social.entity.UserStateInfo

/**
 * Author: ChenYouSheng
 * Date: 2025/7/31
 * Email: <EMAIL>
 * Desc:
 */

// 设置用户状态
fun UserStateInfo?.updateUserState(tvUserState: TextView, defaultText: String?) {
    logDebug("updateUserState", "userStateInfo=$this")

    tvUserState.setTextColor(R.color.text_white_secondary.asColor())
    tvUserState.gone()

    when (this) {
        null, is OfflineState.MoreThan48Hour -> {
            if (defaultText.isNullOrEmpty().not()) {
                tvUserState.visible()
                tvUserState.setTextWithAutoGoneIfEmpty(defaultText)
            }
        }

        is OnlineState.AutoPlay -> {
            tvUserState.visible()
            tvUserState.setTextColor(R.color.color_text_highlight_default.asColor())
            tvUserState.text = R.string.user_state_online_auto_play.asString()
        }

        is OnlineState.Quiet -> {
            tvUserState.visible()
            tvUserState.setTextColor(R.color.color_foreground_dnd_default.asColor())
            tvUserState.text = R.string.user_state_online_quiet.asString()
        }

        is OfflineState.WithinADay -> {
            tvUserState.visible()
            tvUserState.text = R.string.user_state_offline_within_a_day.asString()
        }

        is OfflineState.LessThan60Minute -> {
            tvUserState.visible()
            tvUserState.text =
                if (minutes == 1) R.string.user_state_offline_within_one_minute.asString()
                else String.format(
                    R.string.user_state_offline_within_60_minute.asString(),
                    minutes
                )
        }

        is OfflineState.LessThan8Hour -> {
            tvUserState.visible()
            tvUserState.text =
                if (hours == 1) R.string.user_state_offline_within_one_hour.asString()
                else String.format(
                    R.string.user_state_offline_within_8_hour.asString(),
                    hours
                )
        }

        is OfflineState.Yesterday -> {
            tvUserState.visible()
            tvUserState.text =
                R.string.user_state_offline_yesterday.asString()
        }

    }
}