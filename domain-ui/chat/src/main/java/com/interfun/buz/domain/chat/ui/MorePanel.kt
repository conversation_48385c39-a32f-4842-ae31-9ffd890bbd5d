package com.interfun.buz.domain.chat.ui

import androidx.compose.animation.AnimatedContent
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.interfun.buz.compose.components.CommonLoadingLottie
import com.interfun.buz.compose.components.IconFontText
import com.interfun.buz.compose.ktx.VerticalSpace
import com.interfun.buz.compose.ktx.asColor
import com.interfun.buz.compose.modifier.debouncedClickable
import com.interfun.buz.compose.styles.TextStyles
import com.interfun.buz.domain.chat.R
import com.interfun.buz.domain.chat.repo.ChatMorePanelAction
import com.interfun.buz.domain.chat.repo.ChatMorePanelAction.VideoCall
import com.interfun.buz.domain.chat.viewmodel.MorePanelOptionUiState

data class MorePanelConfig(
    val column: Int,
    val rowGap: Dp,
    val bgColor: Int,
    val minPanelHeight: Dp = 0.dp
)

private val itemHeight = 86.dp

@Composable
fun MorePanel(
    modifier: Modifier = Modifier,
    showVFEntryRedDot: () -> Boolean,
    optionList: () -> List<MorePanelOptionUiState>,
    morePanelConfig: MorePanelConfig,
    onChatPanelAction: (ChatMorePanelAction) -> Unit
) {
    LazyVerticalGrid(
        modifier = modifier
            .heightIn(min = morePanelConfig.minPanelHeight)
            .background(color = morePanelConfig.bgColor.asColor()),
        columns = GridCells.Fixed(morePanelConfig.column),
        verticalArrangement = Arrangement.spacedBy(morePanelConfig.rowGap),
        horizontalArrangement = Arrangement.spacedBy(0.dp),
    ) {
        items(optionList()) { option ->
            MorePanelItem(
                backgroundColor = morePanelConfig.bgColor.asColor(),
                uiState = option,
                showLoading = option.isLoading,
                showVFEntryRedDot = showVFEntryRedDot()
            ) { type ->
                onChatPanelAction(type)
            }
        }
    }
}

@Composable
private fun MorePanelItem(
    modifier: Modifier = Modifier,
    backgroundColor: Color,
    uiState: MorePanelOptionUiState,
    showLoading: Boolean = false,
    showVFEntryRedDot: Boolean,
    optionClick: (ChatMorePanelAction) -> Unit = { },
) {
    Column(
        modifier = modifier
            .height(itemHeight)
            .background(
                color = backgroundColor,
                shape = RoundedCornerShape(20.dp)
            )
            .clip(RoundedCornerShape(20.dp))
            .debouncedClickable { optionClick(uiState.type) },
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        VerticalSpace(10.dp)
        Box {
            AnimatedContent(targetState = showLoading) { startLoading ->
                if (startLoading) {
                    CommonLoadingLottie(
                        modifier = Modifier
                            .size(30.dp)
                            .align(Alignment.Center),
                        color = R.color.color_text_white_important
                    )
                } else {
                    IconFontText(
                        modifier = Modifier.align(Alignment.Center),
                        iconRes = uiState.icon,
                        iconSize = 30.dp,
                        iconColor = R.color.color_text_white_important.asColor()
                    )
                }
            }

            if (uiState.type == ChatMorePanelAction.VoiceFilter && showVFEntryRedDot) {
                Box(
                    modifier = Modifier
                        .size(12.dp)
                        .offset(x = 4.dp, y = (-4).dp)
                        .border(width = 3.dp, color = backgroundColor, shape = CircleShape)
                        .padding(2.dp)
                        .background(
                            color = colorResource(R.color.color_foreground_consequential_default),
                            shape = CircleShape
                        )
                        .align(Alignment.TopEnd)
                )
            }
        }
        VerticalSpace(12.dp)
        Text(
            modifier = modifier,
            text = uiState.title,
            style = TextStyles.labelSmall(),
            maxLines = 2,
            overflow = TextOverflow.Ellipsis,
            textAlign = TextAlign.Center,
            color = R.color.color_text_white_secondary.asColor()
        )
    }
}

@Preview(widthDp = 350, showBackground = true)
@Composable
fun PreviewMorePanel(modifier: Modifier = Modifier) {
    LazyVerticalGrid(
        columns = GridCells.Fixed(4),
        verticalArrangement = Arrangement.spacedBy(4.dp),
        horizontalArrangement = Arrangement.spacedBy(0.dp)
    ) {
        items(8) {
            PreviewMorePanelItem()
        }
    }
}

@Preview(showBackground = true)
@Composable
fun PreviewMorePanelItem(modifier: Modifier = Modifier) {
    MorePanelItem(
        modifier = modifier,
        backgroundColor = R.color.color_background_4_default.asColor(),
        uiState = MorePanelOptionUiState(
            title = "Voice Filter",
            icon = R.string.ic_wt_speaking,
            type = VideoCall,
        ),
        showVFEntryRedDot = true,
        optionClick = {}
    )
}