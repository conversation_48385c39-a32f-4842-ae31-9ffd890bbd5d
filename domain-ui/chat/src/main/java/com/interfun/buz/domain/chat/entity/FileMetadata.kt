package com.interfun.buz.domain.chat.entity

import android.net.Uri
import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class FileMetadata(
    val uri: Uri,
    val displayName: String,
    val size: Long,
    val extension: String,
    val mimeType: String
) : Parcelable

enum class FileSendFrom(val value: Int) {
    HOME(0), // 首页
    CHAT(1), // 聊天页
    EXTERNAL(2); // 外部应用
}