package com.interfun.buz.domain.chat.block

import androidx.fragment.app.viewModels
import androidx.viewbinding.ViewBinding
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.base.BaseFragment
import com.interfun.buz.common.base.binding.BaseBindingBlock
import com.interfun.buz.common.manager.AppConfigRequestManager
import com.interfun.buz.common.utils.StorageUtil.toAccurateFileSize
import com.interfun.buz.common.utils.StorageUtil.toReadableFileSize
import com.interfun.buz.common.widget.button.CommonButton
import com.interfun.buz.common.widget.dialog.CommonAlertDialog
import com.interfun.buz.domain.chat.R
import com.interfun.buz.domain.chat.entity.FileMetadata
import com.interfun.buz.domain.chat.entity.FileSendFrom
import com.interfun.buz.domain.chat.viewmodel.FileSelectViewModel
import com.interfun.buz.domain.chat.viewmodel.NetworkType
import com.interfun.buz.domain.chat.viewmodel.NetworkType.MOBILE
import com.interfun.buz.domain.chat.viewmodel.NetworkType.NONE
import com.interfun.buz.domain.chat.viewmodel.NetworkType.WIFI
import com.interfun.buz.domain.chat.viewmodel.ProcessingResult.*
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import com.yibasan.lizhifm.sdk.platformtools.ResUtil

/**
 * <AUTHOR> Siong
 * @date 2025/6/9
 * @desc
 */
abstract class BaseFileBlock<T : ViewBinding>(
    val TAG: String,
    private val fragment: BaseFragment,
    binding: T,
) : BaseBindingBlock<T>(binding) {

    abstract fun getTargetId(): Long
    abstract fun getConvType(): IM5ConversationType
    abstract fun getReplyId(): Long?
    abstract val fileSendFrom: FileSendFrom

    private val fileSelectViewModel by fragment.viewModels<FileSelectViewModel>()

    private val maxFileCount get() = AppConfigRequestManager.maxFileCount
    private val totalFileReminderSize get() = AppConfigRequestManager.totalFileReminderSize
    private val maxSingleFileSize get() = AppConfigRequestManager.maxSingleFileSize

    override fun initData() {
        super.initData()
        fileSelectViewModel.fileProcessingResult.collectIn(fragment.viewLifecycleOwner) { result ->
            logInfo(TAG, "fileProcessingResult: ServerConfigMaxFileCount=${maxFileCount} ," +
                        "ServerConfigTotalFileReminderSize=${totalFileReminderSize}MB ," +
                        "ServerConfigMaxSingleFileSize=${maxSingleFileSize}MB, " +
                        "Select result=$result")
            when (result) {
                is Success -> {
                    val validSelectedFiles = result.validSelectedFileList
                    val validSelectedTotalSize = result.totalValidSelectedFileSize
                    val exceedTotalFileReminderSize = validSelectedTotalSize > totalFileReminderSize * 1024L * 1024L
                    logInfo(TAG, buildSummaryLog(validSelectedFiles, validSelectedTotalSize))

                    if (result.showConfirmToSendDialog && !exceedTotalFileReminderSize && null != result.fileName) {
                        // Show confirmation dialog if there is only one file to send
                        showConfirmToSendDialog(
                            fileName = result.fileName,
                            validSelectedFiles = validSelectedFiles,
                            sendCallback = { noFileToSend ->
                                if (noFileToSend) {
                                    toast(R.string.file_deselected_toast)
                                }
                            }
                        )
                        return@collectIn
                    }
                    if (result.containEmptyFile || result.containLargeFile) {
                        toast(R.string.file_deselected_toast)
                    }
                    if (validSelectedFiles.isEmpty()) return@collectIn
                    if (exceedTotalFileReminderSize) {
                        // Show confirmation dialog if total file size exceeds 150MB
                        showLargeFileSizeConfirmationDialog(
                            fileName = result.fileName,
                            validSelectedTotalSize = validSelectedTotalSize,
                            validSelectedFiles = validSelectedFiles
                        )
                    } else {
                        fileSelectViewModel.startUploadAndSendFiles(
                            validSelectedFiles = validSelectedFiles,
                            targetId = getTargetId(),
                            convType = getConvType(),
                            replyId = getReplyId(),
                            sendFrom = fileSendFrom
                        )
                    }
                }

                is InvalidFile -> {
                    logInfo(TAG, "Invalid file: ${result.uri}")
                }

                is InsufficientStorage -> {
                    toast(R.string.send_file_insufficient_storage.asString())
                }

                is ExceedMaxFileCount -> {
                    logInfo(TAG, "user selected=${result.count}, max count=$maxFileCount")
                    toast(context.getString(R.string.file_exceed_count_limit, maxFileCount.toString()))
                }
            }
        }

        fileSelectViewModel.uploadingFlow.collectIn(fragment.viewLifecycleOwner) {
            logInfo(TAG, "Uploading files: $it")
            if (it) {
                fragment.showDataLoading(
                    cancelable = false,
                    showDimBackground = true,
                    message = R.string.notification_channel_pending_send_file.asString()
                )
            } else {
                fragment.hideDataLoading()
            }
        }
    }

    private fun showConfirmToSendDialog(
        fileName: String,
        validSelectedFiles: List<FileMetadata>,
        sendCallback: (Boolean) -> Unit = {}
    ) {
        showDialog(
            title = ResUtil.getString(R.string.confirm_to_send_single_file, fileName),
            validSelectedFiles = validSelectedFiles,
            sendCallback = sendCallback
        )
    }

    private fun showLargeFileSizeConfirmationDialog(
        fileName: String? = null,
        validSelectedTotalSize: Long,
        validSelectedFiles: List<FileMetadata>
    ) {
        val networkType = getCurrentNetworkType()
        val dialogDesc = when (networkType) {
            WIFI,
            NONE -> {
                ResUtil.getString(
                    R.string.send_files_with_wifi_dialog_content,
                    validSelectedTotalSize.toReadableFileSize()
                )
            }

            MOBILE -> {
                ResUtil.getString(
                    R.string.send_files_with_data_dialog_content,
                    validSelectedTotalSize.toReadableFileSize()
                )
            }
        }
        showDialog(
            title = if (null != fileName) ResUtil.getString(
                R.string.confirm_to_send_single_file,
                fileName
            ) else null,
            description = dialogDesc,
            validSelectedFiles = validSelectedFiles,
        )
    }

    private fun showDialog(
        title: String? = null,
        description: String? = null,
        validSelectedFiles: List<FileMetadata>,
        sendCallback: (Boolean) -> Unit = {}
    ) {
        CommonAlertDialog(
            context = fragment.requireContext,
            title = title,
            tips = description,
            positiveText = R.string.live_place_share_send.asString(),
            negativeText = R.string.cancel.asString(),
            positiveButtonType = CommonButton.TYPE_SECONDARY_MEDIUM,
            positiveCallback = {
                it.dismiss()
                if (validSelectedFiles.isEmpty()) {
                    sendCallback.invoke(true)
                } else {
                    fileSelectViewModel.startsUploadingFiles()
                    fileSelectViewModel.setConversationInfo(getTargetId(), getConvType(), getReplyId())
                    fileSelectViewModel.startUploadAndSendFiles(
                        validSelectedFiles = validSelectedFiles,
                        sendFrom = fileSendFrom
                    )
                }
            },
            negativeCallback = {
                it.dismiss()
            }
        ).show()
    }

    private fun getCurrentNetworkType(): NetworkType {
        return when {
            isWifiConnected -> WIFI
            isMobileData -> MOBILE
            else -> NONE
        }
    }

    private fun buildSummaryLog(files: List<FileMetadata>, totalSize: Long): String {
        return "\nSelected ${files.size} files, total size: $totalSize, accurate total size: ${totalSize.toAccurateFileSize()}"
    }
}