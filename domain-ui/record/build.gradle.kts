plugins {
    id("com.android.library")
    id("org.jetbrains.kotlin.android")
}
apply {
    from("${rootDir}/base_config.gradle")
}

android {
    namespace = "com.interfun.buz.domain.record"
}

dependencies {
    implementation(project(":common"))
    implementation(project(":common-compose"))
    implementation(project(":im"))
    implementation(project(":biz-center:social"))
    api(project(":core:widget_record"))
    api(project(":biz-center:voicefilter"))
    api(project(":biz-center:voicerecord"))
    api(project(":biz-center:user"))
    implementation(project(":domain:im-social"))
}