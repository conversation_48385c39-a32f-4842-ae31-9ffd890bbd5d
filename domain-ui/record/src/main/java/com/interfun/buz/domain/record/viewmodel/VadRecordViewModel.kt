package com.interfun.buz.domain.record.viewmodel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.audio.AudioManagerHelper
import com.interfun.buz.common.constants.CommonMMKV
import com.interfun.buz.common.constants.RecordingConstant
import com.interfun.buz.common.constants.TrackConstant
import com.interfun.buz.common.ktx.getStringDefault
import com.interfun.buz.common.manager.AppConfigRequestManager
import com.interfun.buz.common.manager.UserManager
import com.interfun.buz.common.utils.BuzTracker
import com.lizhi.component.tekiapm.TekiApm
import com.yibasan.lizhifm.record.simplerecord.VadRecordEngine
import com.yibasan.lizhifm.rtcdorime.xapm.Metrics
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.collectLatest

class VadRecordSegment(
    val durationMs: Int,
    val aacFilePath: String,
    val endType: VadRecordEngine.VadEndType,
    val audioProcessMetrics: Metrics?
)

enum class VoiceState {
    INIT,
    PAUSE,
    RESUME,
    END
}

class VadRecordViewModel : ViewModel() {

    companion object {
        const val TAG = "VadRecordViewModel"
    }

    @Volatile
    private var vadRecordEngine: VadRecordEngine? = null
    val isEngineNull: Boolean get() = vadRecordEngine == null

    private var lastVadActiveTime = Long.MAX_VALUE
    private var durationJob: Job? = null
    val recordDurationFlow = MutableStateFlow<Long?>(null)

    //volume,from 0 to 1
    val volumeFlow: MutableSharedFlow<Float> = MutableSharedFlow()

    //Emitted when vad finished a segment.It means that user has completed in one-time saying.
    val vadRecordSegmentFlow: MutableSharedFlow<VadRecordSegment> = MutableSharedFlow()
    val vadRecordErrorFlow: MutableSharedFlow<Int> = MutableSharedFlow()

    private var lastVadRecordSegment: VadRecordSegment? = null

    /**
     * 标记被SDK内部算法检测到空音录制，注意这不是Google提供静机制
     * onRecordingConfigChanged才是google官方提供的判断机制
     */
    var selfReportSilent = false
        private set

    /**
     * 标记录音的错误码
     */
    var lastErrorCode: Int = RecordingConstant.NORMAL
        private set

    //True if has detected voice and vad is still recording.
    val vadDetectedLiveData = object : MutableLiveData<Boolean>(false) {
        override fun setValue(value: Boolean) {
            val originValue = this.value
            super.setValue(value)
            if (originValue != value) {
                onVadDetectedChanged(value)
            }
        }
    }

    //Emitted when real-time voice state have changed during detected time.
    //It should be noted that VoiceState.PAUSE will be emitted after voice pausing for 500ms.
    val voiceStateFlow = MutableStateFlow<VoiceState>(VoiceState.INIT)


    fun startRecord(
        maxDuration: Int,
        silentTime: Int,
        storePath: String,
        enableVad: Boolean = true,
        rSamplingRate: Int = CommonMMKV.rSamplingRate,
        rBitRate: Int = CommonMMKV.rBitRate,
        traceId: String? = "",
        isPrivateChat: Boolean,
        pageBusinessId: String,
    ) {

        logLineInfo(
            TAG,
            LogLine.VOICE_MSG_RECORD,
            "startRecord maxDuration=$maxDuration,silentTime=$silentTime,storePath=$storePath,enableVad=$enableVad rSamplingRate=$rSamplingRate,rBitRate=$rBitRate"
        )
        lastErrorCode = RecordingConstant.NORMAL
        vadDetectedLiveData.postIfDifferent(false)
        val initEngineStartTime = System.currentTimeMillis()

        initRecordEngine(
            maxDuration,
            silentTime,
            storePath,
            rSamplingRate,
            rBitRate,
            enableVad,
            traceId,
            isPrivateChat,
            pageBusinessId
        )
        val initEngineCost = System.currentTimeMillis() - initEngineStartTime
        val startRecordTime = System.currentTimeMillis()
        GlobalScope.launchIO {
            try {
                AudioManagerHelper.muteChange(false)
            }catch (e:Exception){
                TekiApm.reportException(e)
            }
        }
        val startRecord = vadRecordEngine?.startRecord()
        logLineInfo(TAG, LogLine.VOICE_MSG_RECORD,"start startRecord Result: ${startRecord == 0}")
        RecordingConstant.startRecordRet(startRecord?:0)
        RecordingConstant.tekTT2024071701("1")
        val startRecordCost = System.currentTimeMillis() - startRecordTime
        BuzTracker.onTechTrack {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "TT2024051101")
            put(TrackConstant.KEY_CONTENT_1, initEngineCost + startRecordCost)
            put(TrackConstant.KEY_CONTENT_2, initEngineCost)
            put(TrackConstant.KEY_CONTENT_3, startRecordCost)
        }
    }


    fun stopRecord() {
        logLineInfo(TAG, LogLine.VOICE_MSG_RECORD, "stopRecord")
        // 频繁快速触发start和stopRecord会导致录音组件内部crash，这里先try住
        doInTryCatch(TAG) {
            vadRecordEngine?.stopRecord()
        }
        vadDetectedLiveData.postIfDifferent(false)
        viewModelScope.launch {
            lastVadActiveTime = Long.MAX_VALUE
            voiceStateFlow.emit(VoiceState.INIT)
        }
    }

    fun cancelRecord() {
        logLineInfo(TAG, LogLine.VOICE_MSG_RECORD,"cancelRecord")
        vadRecordEngine?.cancelRecord()
        destroyRecord()
        vadDetectedLiveData.postIfDifferent(false)
        viewModelScope.launch {
            lastVadActiveTime = Long.MAX_VALUE
            voiceStateFlow.emit(VoiceState.INIT)
        }
    }

    private fun onVadDetectedChanged(value: Boolean) {
        if (!value) {
            durationJob?.cancel()
            recordDurationFlow.emitInScope(viewModelScope, null)
        } else {
            durationJob?.cancel()
            durationJob = viewModelScope.launch {
                var times = 0L
                recordDurationFlow.emit(times * 1000)
                while (isActive) {
                    delay(1000L)
                    times++
                    recordDurationFlow.emit(times * 1000)
                }
            }
        }
    }

    private fun initRecordEngine(
        maxDuration: Int,
        silentTime: Int,
        storePath: String,
        rSamplingRate: Int,
        rBitRate: Int,
        enableVad: Boolean = true,
        traceId: String? = "",
        isPrivateChat: Boolean,
        pageBusinessId: String,
    ) {
        destroyRecord()
        if (vadRecordEngine == null) {
            logLineInfo(TAG, LogLine.VOICE_MSG_RECORD,"initRecordEngine,use old vadRecordEngine=${vadRecordEngine}")
            vadRecordEngine = VadRecordEngine()
            initLoginStateObserve()
        }

        vadRecordEngine?.setSleepRecordEngineListener(null)
        vadRecordEngine?.cancelRecord()
        vadRecordEngine?.enableVad(enableVad)

        vadRecordEngine?.setSleepRecordEngineListener(
            object : VadRecordEngine.VadRecordEngineListener {
                override fun onRecordCurrentVolume(vol: Float) {
//                    logInfo(TAG, "onRecordCurrentVolume ${vol}")
                    viewModelScope.launch { volumeFlow.emit(vol) }
                }

                override fun onRecordCurrentVadState(vad: Int) {
//                    logInfo(TAG, "onRecordCurrentVadState ${vad}")
                    <EMAIL>(vad)
                }

                override fun onRecordOneVadStarted() {
                    logLineInfo(TAG, LogLine.VOICE_MSG_RECORD,"onRecordOneVadStarted")
                    vadDetectedLiveData.postIfDifferent(true)
                    viewModelScope.launch {
                        lastVadActiveTime = System.currentTimeMillis()
                        voiceStateFlow.emit(VoiceState.INIT)
                    }
                }

                override fun onRecordOneVadSegment(
                    audioDuration: Int,
                    aacFilePath: String,
                    endType: VadRecordEngine.VadEndType
                ) {
                    logLineInfo(
                        TAG,
                        LogLine.VOICE_MSG_RECORD,
                        "onRecordOneVadSegment audioDuration ${audioDuration} aacFilePath ${aacFilePath} endType ${endType}"
                    )
                    lastVadRecordSegment?.apply {
                        if (this.durationMs == audioDuration && this.aacFilePath == aacFilePath && this.endType.name == endType.name) {
                            log(
                                TAG,
                                "onRecordOneVadSegment 重复触发 dur:$audioDuration,file:$aacFilePath,type:$endType"
                            )
                            return
                        }
                    }
                    val audioProcessMetrics = vadRecordEngine?.audioProcessMetrics
                    lastVadRecordSegment = VadRecordSegment(audioDuration, aacFilePath, endType,audioProcessMetrics)
//                    println(audioProcessMetrics)
                    <EMAIL>(
                        audioDuration,
                        aacFilePath,
                        endType,audioProcessMetrics
                    )
                    viewModelScope.launch {
                        destroyRecord()
                    }
                }

                override fun onRecordingConfigChanged(isClientSilenced: Boolean) {
                    logLineInfo(TAG, LogLine.VOICE_MSG_RECORD,"onRecordingConfigChanged  isClientSilenced ${isClientSilenced}")
                    if (isClientSilenced) {
                        RecordingConstant.systemReportSilent()
                        viewModelScope.launch {
                            lastErrorCode = RecordingConstant.ERRO_CLIENTSILENCED
                            vadRecordErrorFlow.emit(RecordingConstant.ERRO_CLIENTSILENCED)
                        }
                    }
                }


                override fun onError(code: Int, msg: String?) {
                    logLineError(TAG, LogLine.VOICE_MSG_RECORD,"onError code=$code,msg=$msg")
                    log(TAG, "onRecordPermissionProhibited")
                    viewModelScope.launch {
                        lastErrorCode = code
                        vadRecordErrorFlow.emit(code)
                    }
                }

                override fun onWarn(code: Int, msg: String?) {
                    logLineError(TAG, LogLine.VOICE_MSG_RECORD,"onWarn code=$code,msg=$msg")
                    if (code == RecordingConstant.WARN_SILENT) {
                        selfReportSilent = true
                        RecordingConstant.sdkSelfSilentCheck(vadRecordEngine?.checkClientSlienced()?:0)
                        viewModelScope.launch {
                            lastErrorCode = RecordingConstant.WARN_SILENT
                            if (AppConfigRequestManager.enableVadCheck)
                                vadRecordErrorFlow.emit(RecordingConstant.WARN_SILENT)
                        }
                        RecordingConstant.onSilenceWarnStart(
                            traceId = traceId.getStringDefault(),
                            isPrivateChat = isPrivateChat,
                            pageBusinessId = pageBusinessId
                        )
                    }
                }

                override fun onFullSilentDetected(isSilentAudio: Boolean) {
                    logLineInfo(TAG, LogLine.VOICE_MSG_RECORD,"onFullSilentDetected isSilentAudio $isSilentAudio")
                    RecordingConstant.onFullSilenceDetected(
                        traceId = traceId.getStringDefault(),
                        isPrivateChat = isPrivateChat,
                        pageBusinessId = pageBusinessId,
                        isClientSilenced = isSilentAudio
                    )
                }
            })
        selfReportSilent = false

        val initCode = vadRecordEngine?.initRecord(
            maxDuration,
            silentTime,
            storePath,
            rSamplingRate,
            rBitRate,AppConfigRequestManager.enableAudioNoiseReductionFallback
        )
        RecordingConstant.initErrorCode(initCode?:0)
        //确保资源释放
//        vadRecordEngine?.stopRecord()

        log(
            TAG,
            "initRecordEngine,create new vadRecordEngine=${vadRecordEngine} iniCode = ${initCode}"
        )
    }

    private fun initLoginStateObserve(){
        launchIO {
            UserManager.isUserLogin.collectLatest {
                if (!it){
                    logInfo(TAG,"initLoginStateObserve: $it")
                    cancelRecord()
                }
            }
        }
    }

    private fun onRecordCurrentVadState(state: Int) {
        val currentTime = System.currentTimeMillis()
        if (state == 0 && voiceStateFlow.value != VoiceState.PAUSE
            && voiceStateFlow.value != VoiceState.END && currentTime - lastVadActiveTime > 500L
        ) {
            viewModelScope.launch {
                voiceStateFlow.emit(VoiceState.PAUSE)
            }
        }
        if (state == 1) {
            if (voiceStateFlow.value == VoiceState.INIT) {
                lastVadActiveTime = currentTime
            }
            if (voiceStateFlow.value == VoiceState.PAUSE) {
                viewModelScope.launch {
                    lastVadActiveTime = currentTime
                    voiceStateFlow.emit(VoiceState.RESUME)
                }
            }
        }
    }

    private fun onRecordOneVadSegment(
        audioDuration: Int,
        aacFilePath: String,
        endType: VadRecordEngine.VadEndType,
        audioProcessMetrics: Metrics?
    ) {
        viewModelScope.launch {
            lastVadActiveTime = Long.MAX_VALUE
            voiceStateFlow.emit(VoiceState.END)
        }
        log(TAG, "onRecordOneVadSegment dur:$audioDuration,file:$aacFilePath,type:$endType")
        vadDetectedLiveData.postIfDifferent(false)
        viewModelScope.launch {
            vadRecordSegmentFlow.emit(
                VadRecordSegment(
                    audioDuration,
                    aacFilePath,
                    endType,
                    audioProcessMetrics
                )
            )
        }
    }


    override fun onCleared() {
        super.onCleared()
        log(TAG, "onCleared releaseRecord start")
        destroyRecord()
    }

    fun destroyRecord() {
        logLineInfo(TAG, LogLine.VOICE_MSG_RECORD, "destroyRecord")
        vadRecordEngine?.setSleepRecordEngineListener(null)
        vadRecordEngine?.release()
        vadRecordEngine = null
    }
}