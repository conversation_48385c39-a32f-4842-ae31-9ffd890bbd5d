package com.interfun.buz.domain.record.helper

import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * <AUTHOR>
 * @date 2025/5/14
 * @desc 提供录音状态的单例
 */
object RecordStatusHelper {

    /**
     * 是否已经按下
     * 可能此时还未真正开始录音，但一些临界情况需要这种判断，比如同时按下录音和其他按钮时，拦截其他按钮的点击
     */
    private var _isPressingFlow = MutableStateFlow(false)
    val isPressingFlow = _isPressingFlow.asStateFlow()
    val isPressing get() = isPressingFlow.value

    /**
     * 是否正在录音
     */
    private var _isRecordingFlow = MutableStateFlow(false)
    val isRecordingFlow = _isRecordingFlow.asStateFlow()
    val isRecording get() = isRecordingFlow.value

    /**
     * 是否正在锁定录音，锁定时isRecordingFlow为ture，而isPressingFlow为false
     */
    private var _isLockingFlow = MutableStateFlow(false)
    var isLockingFlow = _isLockingFlow.asStateFlow()
    val isLocking get() = isLockingFlow.value

    /**
     * 如果希望在录音状态下拦截一些操作，那么使用 isRecording 或者 isPressingOrLocking 都行
     * 但isPressingOrLocking 覆盖了pressDown -> recording 这个过程，适合处理一些点击事件的边界case
     * 比如刚按下录音就快速按了其他按钮打开其他页面，此时马上要进入但还未真正进入recording状态，不应跳转其他页面
     */
    val isPressingOrLocking get() = isPressing || isLocking

    fun setRecordingStatus(isRecording: Boolean) {
        _isRecordingFlow.value = isRecording
    }

    fun setPressingStatus(isPressing: Boolean) {
        _isPressingFlow.value = isPressing
    }

    fun setLockingStatus(isLocking: Boolean) {
        _isLockingFlow.value = isLocking
    }

    fun reset() {
        _isPressingFlow.value = false
        _isRecordingFlow.value = false
        _isLockingFlow.value = false
    }
}