package com.interfun.buz.domain.record.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.interfun.buz.base.ktx.LogLine
import com.interfun.buz.base.ktx.emitInScope
import com.interfun.buz.base.ktx.launchIO
import com.interfun.buz.base.ktx.logLineInfo
import com.interfun.buz.biz.center.voicefilter.repository.VoiceFilterDataRepository
import com.interfun.buz.common.bean.chat.MentionedUser
import com.interfun.buz.common.constants.CommonMMKV
import com.interfun.buz.common.constants.RecordingConstant
import com.interfun.buz.common.ktx.uid
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.manager.chat.WTGuidanceManager
import com.interfun.buz.common.utils.RtpTracker
import com.interfun.buz.domain.im.social.entity.CommonMsgParams
import com.interfun.buz.domain.im.social.entity.VoiceMsgParams
import com.interfun.buz.domain.im.social.repository.SendIMRepository
import com.interfun.buz.domain.record.entity.RecordPreviewMsgParam
import com.interfun.buz.domain.record.helper.TraceUtils
import com.interfun.buz.im.entity.EventTrackExtra
import com.interfun.buz.im.entity.FilterIng
import com.interfun.buz.im.entity.IMSendFrom
import com.interfun.buz.im.entity.VoiceFilterInfo
import com.interfun.buz.social.repo.UserRepository
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import javax.inject.Inject

/**
 * <AUTHOR>
 * @date 2025/5/14
 * @desc
 */
@HiltViewModel
class SendRecordViewModel @Inject constructor(
    private val userRepository: UserRepository,
    private val sendIMRepository: SendIMRepository,
    private val voiceFilterRepository: VoiceFilterDataRepository,
) : ViewModel() {

    companion object {
        const val TAG = "SendRecordViewModel"
    }

    private val _openPreviewRecordFlow = MutableSharedFlow<RecordPreviewMsgParam?>()
    val openPreviewRecordFlow = _openPreviewRecordFlow.asSharedFlow()



    fun onSegmentReceived(
        segment: VadRecordSegment,
        targetId: Long?,
        convType: IM5ConversationType?,
        traceId: String?,
        pageType: IMSendFrom,
        replyId: Long? = null,
        isPreview: Boolean = false,
        isPreviewNotAutoPlay: Boolean = false,
        selfReportSilent: Boolean = false,
        voiceFilterId: Long? = null,
        mentionedUsers: List<MentionedUser>? = null
    ) = launchIO {
        if (convType == null || targetId == null) {
            trackRtpResult(
                targetId = targetId,
                convType = convType,
                pageType = pageType,
                traceId = traceId,
                isSuccess = false,
                errorCode = 10000
            )
            return@launchIO
        }

        RecordingConstant.selfReportSilent(traceId ?: "", selfReportSilent)
        TraceUtils.voiceFileInfo(
            source = when (pageType) {
                IMSendFrom.HOME -> "1"
                IMSendFrom.CHAT_LIST -> "3"
                IMSendFrom.OVERLAY -> "2"
            },
            traceId = traceId ?: "",
            duration = segment.durationMs.toLong(),
            path = segment.aacFilePath
        )
        WTGuidanceManager.setStartRecordInGuidancePTT(true)

        if (isPreview) {
            // logInfo(TAG,"onSegmentReceived: on UserPreview")
            val param = RecordPreviewMsgParam(
                targetId = targetId.toString(),
                aacPath = segment.aacFilePath,
                convType = convType,
                duration = segment.durationMs,
                mentionedUsers = mentionedUsers,
                traceId = traceId,
                replyId = replyId,
                voiceFilterId = voiceFilterId,
                isPreviewNotAutoPlay = isPreviewNotAutoPlay
            )
            _openPreviewRecordFlow.emitInScope(viewModelScope, param)
            CommonMMKV.hadShowPreviewAvailableTip = true
            return@launchIO
        }

        sendRecordMessage(
            segment = segment,
            targetId = targetId,
            convType = convType,
            traceId = traceId,
            sendFrom = pageType,
            replyId = replyId,
            voiceFilterId = voiceFilterId,
            mentionedUsers = mentionedUsers
        )
        logLineInfo(
            TAG,
            LogLine.VOICE_MSG_RECORD,
            "Vad Record End, send record voice message, targetId = $targetId, convType = $convType, voiceFilterId = $voiceFilterId, traceId = $traceId"
        )
        trackRtpResult(
            targetId = targetId,
            convType = convType,
            pageType = pageType,
            traceId = traceId,
            isSuccess = true
        )
    }

    suspend fun sendRecordMessage(
        segment: VadRecordSegment,
        targetId: Long,
        convType: IM5ConversationType,
        traceId: String?,
        sendFrom: IMSendFrom,
        replyId: Long? = null,
        voiceFilterId: Long? = null,
        mentionedUsers: List<MentionedUser>? = null,
    ) {
        val voiceMsgParams = VoiceMsgParams(
            targetId = targetId.toString(),
            path = segment.aacFilePath,
            convType = convType,
            duration = segment.durationMs,
            commonMsgParams = CommonMsgParams(
                traceId = traceId,
                replyId = replyId,
                eventTrackExtra = EventTrackExtra(sendFrom = sendFrom.code)
            ),
            mentionedUsers = mentionedUsers
        )
        if (voiceFilterId != null) {
            val voiceFilterInfo = voiceFilterRepository.getCachedVoiceFilterInfoById(voiceFilterId)
            sendIMRepository.sendVoiceFilterMessage(
                voiceMsgParams,
                VoiceFilterInfo(
                    filterId = voiceFilterId,
                    originalUrl = null,
                    filterState = FilterIng,
                    videoTemplateUrl = voiceFilterInfo?.videoTemplate?.videoTemplateUrl,
                    videoTemplateMd5 = voiceFilterInfo?.videoTemplate?.videoTemplateMd5,
                    bubbleStyle = voiceFilterInfo?.itemConfig?.bubbleStyle ?: 0,
                    suitColor1 = voiceFilterInfo?.backgroundColor,
                    suitColor2 = voiceFilterInfo?.microphoneColor
                )
            )
        } else {
            sendIMRepository.sendVoiceMessage(voiceMsgParams)
        }
    }

    fun trackRtpResult(
        targetId: Long?,
        convType: IM5ConversationType?,
        pageType: IMSendFrom,
        traceId: String?,
        isSuccess: Boolean,
        errorCode: Int? = null,
        isUserCanceled: Boolean = false
    ) {
        RtpTracker.onEventVoiceRecordResult(
            traceId = traceId,
            streamId = "0",
            senderId = UserSessionManager.uid.toString(),
            targetId = targetId?.toString() ?: "",
            source = if (convType == IM5ConversationType.GROUP) 1 else 0,
            useRtp = false,
            isSuccess = isSuccess,
            errorCode = errorCode,
            from = pageType.code,
            isCanceled = isUserCanceled
        )
    }

    suspend fun isRobot(
        targetId: Long,
        convType: IM5ConversationType?
    ) = if (convType == IM5ConversationType.PRIVATE) {
        userRepository.getUserComposite(targetId)?.user?.isRobot == true
    } else {
        false
    }

    fun closePreview() {
        _openPreviewRecordFlow.emitInScope(viewModelScope, null)
    }
}