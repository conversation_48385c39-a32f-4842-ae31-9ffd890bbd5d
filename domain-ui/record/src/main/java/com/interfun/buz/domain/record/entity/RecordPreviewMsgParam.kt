package com.interfun.buz.domain.record.entity

import android.os.Parcelable
import com.interfun.buz.common.bean.chat.MentionedUser
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import kotlinx.android.parcel.Parcelize

@Parcelize
class RecordPreviewMsgParam(
    val targetId:String,
    val aacPath:String,
    val convType:IM5ConversationType,
    val duration:Int,
    val mentionedUsers: List<MentionedUser>?,
    val traceId:String?,
    val replyId: Long? = null,
    val voiceFilterId: Long? = null,
    val isPreviewNotAutoPlay: Boolean = false
): Parcelable