package com.interfun.buz.domain.record.ui

import android.os.Bundle
import androidx.compose.foundation.layout.Column
import androidx.compose.runtime.Composable
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import com.interfun.buz.common.constants.CommonMMKV
import com.interfun.buz.compose.components.CommonButtonType
import com.interfun.buz.compose.components.dialog.BaseComposeDialogFragment
import com.interfun.buz.compose.components.dialog.CommonGuideDialogContent
import com.interfun.buz.compose.ktx.VerticalSpace
import com.interfun.buz.compose.ktx.asString
import com.interfun.buz.domain.record.R
import com.interfun.buz.domain.record.viewmodel.VoiceFilterViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.delay

@AndroidEntryPoint
class VoiceFilterIntroduceDialog: BaseComposeDialogFragment() {

    companion object {
        const val TAG = "VoiceFilterIntroduceDialog"

        suspend fun tryToShowDialog(fragmentManger: FragmentManager){
            if (CommonMMKV.hadShowVFIntroduceDialog) return
            // 等待 VoiceFilterList 动画出现后再弹窗
            delay(300)
            VoiceFilterIntroduceDialog().show(fragmentManger, TAG)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        CommonMMKV.hadShowVFIntroduceDialog = true
    }

    @Composable
    override fun DialogContent() {
        Column {
            CommonGuideDialogContent(
                lottieId = R.raw.home_voice_filter_guidance,
                lottieRatio = 1.824047f,
                title = R.string.add_fun_vf.asString(),
                description = R.string.voice_effects_surprise_friends.asString(),
                positiveText = R.string.try_it_now.asString(),
                positiveBtnType = CommonButtonType.PRIMARY_MEDIUM,
                onClickClose = { dismiss() },
                onClickPositive = { dismiss() },
                onClickNegative = { dismiss() }
            )
            VerticalSpace(dp = 20.dp)
        }
    }

    override fun dismiss() {
        super.dismiss()
    }

    @Preview
    @Composable
    fun PreviewDialog(){
        DialogContent()
    }
}