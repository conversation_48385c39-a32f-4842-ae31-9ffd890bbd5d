package com.interfun.buz.domain.record.manager


import android.annotation.SuppressLint
import android.media.AudioAttributes
import android.media.AudioManager
import android.net.Uri
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.manager.BuzAudioFocusManager
import com.interfun.buz.common.manager.FocusRequestConfig
import com.interfun.buz.common.manager.userLifecycleScope
import com.lizhi.component.tekiplayer.MediaItem
import com.lizhi.component.tekiplayer.PlayEventListener
import com.lizhi.component.tekiplayer.TekiPlayer
import com.lizhi.component.tekiplayer.controller.PlayerState
import kotlinx.coroutines.Job
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.cancelAndJoin
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.io.File

object VoicePreviewPlayer {
    const val TAG = "VoicePreviewPlayer"
    private val scope = MainScope()
    private var lastJob: Job? = null
    private val _isPlaying = MutableStateFlow(false)
    val isPlaying:StateFlow<Boolean> = _isPlaying.asStateFlow()
    private val _playerState = MutableStateFlow(PlayerState.STATE_IDLE)
    val playerState:StateFlow<Int> = _playerState.asStateFlow()
    private val _onPlayErrorMsg = MutableSharedFlow<Pair<Int,String>>()
    val onPlayErrorMsg:SharedFlow<Pair<Int,String>> = _onPlayErrorMsg.asSharedFlow()

    @SuppressLint("StaticFieldLeak")
    private val audioFocusManager = BuzAudioFocusManager(
        application, TAG, FocusRequestConfig(
            focusGain = AudioManager.AUDIOFOCUS_GAIN_TRANSIENT_MAY_DUCK,
            contentType = AudioAttributes.CONTENT_TYPE_MUSIC,
            usage = AudioAttributes.USAGE_MEDIA,
            willPauseWhenDucked = true
        ) { focus ->
            handleAudioFocusChange(focus)
        }
    )

    private val audioPlayerLazy = lazy {
        TekiPlayer.Builder(appContext).build().apply {
            addPlayEventListener(playEventListener)
            pauseWhenAudioFocusLost = false
            autoHandleAudioFocus = false
        }
    }
    private val audioPlayer by audioPlayerLazy
    private val playEventListener = object : PlayEventListener {

        override fun onBufferedPositionUpdate(index: Int, item: MediaItem?, bufferPosition: Long) {
        }

        override fun onError(errCode: Int, message: String) {
            logInfo(TAG, "PlayEventListener onError errcode $errCode message $message")
            _onPlayErrorMsg.emitInScope(userLifecycleScope,errCode to message)
            stopAudioInternal()
        }

        override fun onPlayListFinished() {
            logInfo(TAG, "onPlayListFinished")
            stopAudioInternal()
        }

        override fun onPlayListUpdate() {
        }

        override fun onPlayZeroItem(item: MediaItem?) {
        }

        override fun onPlaybackChange(
            index: Int,
            item: MediaItem?,
            lastPosition: Long,
            reason: Int
        ) {
            logInfo(
                TAG,
                "onPlaybackChange index=$index reason=$reason,tag:${item?.tag},url:${item?.uri?.toString()}"
            )
        }

        override fun onPlaybackRemoveOnList() {
            logInfo(TAG, "onPlaybackRemoveOnList")
        }

        override fun onPlaybackStateChange(status: Int) {
            _playerState.emitInScope(userLifecycleScope,status)
            logInfo(TAG, "onPlaybackStateChange -- play state:$status")
        }

        override fun onPlayedPositionUpdate(index: Int, item: MediaItem?, position: Long) {
        }
    }

    fun getPlayingItem():MediaItem?{
        return audioPlayer.getCurrentMediaItem()
    }

    fun getCurrentPlayPosition():Long{
        return audioPlayer.getPosition()
    }


    fun play(path:String,duration:Long) : Boolean {
        logInfo(TAG,"play : path = $path, duration = $duration")
        val uri = if (File(path).exists()) {
            Uri.fromFile(File(path))
        }else{
            path.asUri()
        }

        scope.launch {
            _isPlaying.emit(true)
            lastJob?.cancelAndJoin()
            lastJob = coroutineContext[Job]
            audioPlayer.clear()


            audioPlayer.addMediaItem(
                MediaItem.Builder()
                    .setUri(uri)
                    .setTag(path)
                    .setSpecifiedDuration(duration)
                    .build()
            )
            audioPlayer.prepare()
            withIOContext {
                audioFocusManager.request()
            }
            audioPlayer.play()
        }
        return true
    }

    fun stopPlay(){
        stopAudioInternal()
    }

    private fun stopAudioInternal() {
        scope.launch {
            if (audioPlayerLazy.isInitialized()) {
                logInfo(TAG,"stopAudioInternal")
                audioPlayer.stop()
                audioPlayer.clear()
                audioFocusManager.abandon()
            }
            _isPlaying.emit(false)
        }
    }

    private fun handleAudioFocusChange(focus: Int) {
        logInfo(TAG, "handleAudioFocusChange focus=${focus}")
        when (focus) {
            AudioManager.AUDIOFOCUS_LOSS -> stopAudioInternal()
            AudioManager.AUDIOFOCUS_GAIN -> {}
            AudioManager.AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK -> stopAudioInternal()
            AudioManager.AUDIOFOCUS_LOSS_TRANSIENT -> stopAudioInternal()
        }
    }
}