package com.interfun.buz.domain.record.block

import android.media.AudioAttributes
import android.media.AudioManager
import android.widget.Toast
import androidx.annotation.CallSuper
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.viewModelScope
import androidx.viewbinding.ViewBinding
import com.interfun.buz.base.ktx.*
import com.interfun.buz.base.utils.VibratorUtil
import com.interfun.buz.biz.center.voicefilter.tracker.VoiceFilterTracker
import com.interfun.buz.biz.center.voicerecord.tracker.RecordTracker
import com.interfun.buz.common.base.binding.BaseBindingBlock
import com.interfun.buz.common.bean.LoadingState
import com.interfun.buz.common.bean.chat.MentionedUser
import com.interfun.buz.common.constants.CommonConstant
import com.interfun.buz.common.constants.CommonMMKV
import com.interfun.buz.common.constants.RecordingConstant
import com.interfun.buz.common.ktx.toastRegularWarning
import com.interfun.buz.common.ktx.toastSolidWarning
import com.interfun.buz.common.ktx.uid
import com.interfun.buz.common.manager.*
import com.interfun.buz.common.utils.ClientTracker
import com.interfun.buz.common.utils.RingtonePlayer
import com.interfun.buz.common.utils.RtpTracker
import com.interfun.buz.domain.record.R
import com.interfun.buz.domain.record.entity.RecordInfo
import com.interfun.buz.domain.record.entity.RecordStatusType
import com.interfun.buz.domain.record.entity.ReleaseReason
import com.interfun.buz.domain.record.helper.RecordStatusHelper
import com.interfun.buz.domain.record.helper.TraceUtils
import com.interfun.buz.domain.record.viewmodel.*
import com.interfun.buz.im.entity.IMSendFrom
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import com.yibasan.lizhifm.record.simplerecord.VadRecordEngine
import com.yibasan.lizhifm.sdk.platformtools.ResUtil
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.combine
import java.io.File

/**
 * <AUTHOR>
 * @date 2025/5/13
 * @desc
 */
abstract class BaseRecordBlock<T: ViewBinding>(
    val TAG: String,
    private val fragment: Fragment,
    binding: T,
): BaseBindingBlock<T>(binding) {

    companion object{
        const val MIN_PRESS_DURATION = 100L
        const val MIN_RECORD_DURATION = 500L
    }

    abstract val pageType: IMSendFrom
    abstract fun getTargetId(): Long?
    abstract fun getConvType(): IM5ConversationType?

    val vadRecordViewModel by fragment.viewModels<VadRecordViewModel>()
    val recordViewModel by fragment.viewModels<RecordVoiceViewModel>()
    val sendRecordViewModel by fragment.viewModels<SendRecordViewModel>()
    val voiceFilterViewModel by fragment.viewModels<VoiceFilterViewModel>()

    private val audioFocusManager = BuzAudioFocusManager(
        context = application,
        debugTag = TAG + "AudioFocus",
        config = FocusRequestConfig(
            focusGain = AudioManager.AUDIOFOCUS_GAIN_TRANSIENT_EXCLUSIVE,
            contentType = AudioAttributes.CONTENT_TYPE_SPEECH
        )
    )

    private val isAttachedToWindow
        get() = fragment.isAdded && !fragment.isDetached && !fragment.isRemoving

    private var handlePressDownJob: Job? = null
    private var tryToStartRecordJob: Job? = null
    private var recordJob: Job? = null
    private val recordDir by lazy { File(application.filesDir, "record") }
    private var lastReleaseReason = ReleaseReason.NORMAL
    private var lastIsLocking = false
    private var recordTarget: RecordInfo? = null

    // 开始录制的回调
    @CallSuper
    open fun onStartRecord(recordingTargetId: Long? = null) {
        RecordStatusHelper.setRecordingStatus(true)
    }

    // 结束录制的回调
    @CallSuper
    open fun onStopRecord(reason: ReleaseReason?) {
        RecordStatusHelper.setRecordingStatus(false)
    }

    @CallSuper
    override fun initData() {
        super.initData()
        initPressStateObserver()
        initVadRecordDurationObserver()
        initVadRecordSegmentObserver()
        initVadRecordErrorObserver()
        initVoiceFilterOpenStatusObserver()
        initVoiceFilterScrollingObserver()
    }

    private fun initPressStateObserver() {
        recordViewModel.recordStatusSharedFlow.collectIn(fragment.viewLifecycleOwner) { pressState ->
            if (!isAttachedToWindow) return@collectIn
            if (pressState is RecordStatusType.Pressed) {
                RecordStatusHelper.setPressingStatus(true)
                handlePressDown()
                lastIsLocking = false
            } else if (pressState is RecordStatusType.Locked) {
                // 进入锁定录音状态，保持按钮按下状态，但允许用户手指离开
                // 此时不需要停止录音，继续保持录音状态
                RecordStatusHelper.setPressingStatus(false)
                RecordStatusHelper.setLockingStatus(true)
                lastIsLocking = true
                trackWhenLocking()
            } else if (pressState is RecordStatusType.Release) {
                RecordStatusHelper.setPressingStatus(false)
                RecordStatusHelper.setLockingStatus(false)
                lastReleaseReason = pressState.reason
                handlePressRelease(pressState.reason)
            }
        }
    }

    // 在按下录音按钮时，可能有一些特殊的状态需要挂起等待，比如首页列表还在惯性滑动过程中，需要先等待滑动停止再开始录音
    open suspend fun waitTillRecordEnable(): Boolean = true

    // 录制第一步：等待按钮按下
    // 但在这一步前已经在RecordVoiceViewModel.checkEnableAndToastWhenDisable()中检查过很多条件了
    private fun handlePressDown() {
        logInfo(TAG, "handlePressDown handlePressDownJob.isActive=${handlePressDownJob?.isActive}")
        handlePressDownJob?.cancel()
        handlePressDownJob = fragment.viewLifecycleScope.launch {
            if (waitTillRecordEnable()) {
                if (isAttachedToWindow) {
                    tryToStartRecord()
                }
            }
        }
    }

    // 录制第二步：按下按钮后尝试执行录制逻辑
    private fun tryToStartRecord() {
        trackWhenPress()
        // 交由继承类再做一些特定业务场景的条件判断拦截
        val isRecordEnable = checkRecordEnable()
        logLineInfo(TAG, LogLine.VOICE_MSG_RECORD,"tryToStartRecord isRecordEnable: $isRecordEnable")
        if (!isRecordEnable) return
        tryToStartRecordJob?.cancel()
        var needShowTooShortToast = true
        tryToStartRecordJob = vadRecordViewModel.launch {
            // delay 100 用于防止快速连点，过滤无效操作
            delay(MIN_PRESS_DURATION)
            needShowTooShortToast = false
            if (AppConfigRequestManager.enableRecordingSoundEffects) {
                withTimeoutOrNull(500) {
                    RingtoneManager.playAndAwait(RingtoneManager.AUDIO_WALKIE_TALKIE_BUTTON_PRESS)
                }
            }
            logLineInfo(TAG, LogLine.VOICE_MSG_RECORD, "tryToStartRecord isBtnPressing: ${RecordStatusHelper.isPressingOrLocking}")
            if (isActive && RecordStatusHelper.isPressingOrLocking) {
                onStartRecordForReal()
            } else {
                onStartRecordFailed()
            }
        }.also { it ->
            it.invokeOnCompletion {
                if (needShowTooShortToast) {
                    showTooShortToast()
                    logLineInfo(TAG, LogLine.VOICE_MSG_RECORD,"tryToStartRecord canceled bcz too short")
                }
            }
        }
    }

    open fun checkRecordEnable(): Boolean = true

    // 录制第三步：真正开始录制，调用vadRecordViewModel.startRecord()
    private suspend fun onStartRecordForReal(){
        logLineInfo(TAG, LogLine.VOICE_MSG_RECORD,"onStartRecordForReal targetId: ${getTargetId()}, convType: ${getConvType()}")
        val targetId = getTargetId() ?: return
        val convType = getConvType() ?: return
        onStartRecord(recordingTargetId = targetId)
        val recordInfo = RecordInfo(
            targetId = targetId,
            isGroup = convType == IM5ConversationType.GROUP,
            voiceFilterId = voiceFilterViewModel.getCurrentValuableSelectVoiceFilterId(),
            traceId = ClientTracker.generateTraceId(),
            mentionedUsers = getMentionedUsers()
        ).apply {
            recordTarget = this
        }
        recordViewModel.onStartRecording(recordInfo)
        VibratorUtil.vibratorMaxAmplitude(from = "$TAG start record")
        audioFocusManager.requestAsync()
        if (!recordDir.exists()) {
            recordDir.mkdirs()
        }
        val currentFilterId = recordInfo.voiceFilterId
        val isRobot = sendRecordViewModel.isRobot(targetId, convType)
        logLineInfo(TAG,
            logLine = LogLine.VOICE_MSG_RECORD,
            "vadRecordViewModel.startRecord, targetId = $targetId, convType = $convType, isRobot = $isRobot, currentFilterId = $currentFilterId, traceId = ${recordInfo.traceId}"
        )
        vadRecordViewModel.startRecord(
            maxDuration = CommonConstant.MAX_RECORD_DURATION * 1000,
            silentTime = 2500,
            storePath = recordDir.absolutePath,
            enableVad = false,
            rSamplingRate = if (isRobot) CommonMMKV.rAISamplingRate else CommonMMKV.rSamplingRate,
            rBitRate = if (isRobot) CommonMMKV.rAIBitRate else CommonMMKV.rBitRate,
            traceId = recordInfo.traceId,
            isPrivateChat = convType == IM5ConversationType.PRIVATE,
            pageBusinessId = targetId.toString()
        )
        RtpTracker.onEventRtpRecordStart(
            streamId = 0L,
            senderId = UserSessionManager.uid,
            targetId = targetId,
            isGroup = convType == IM5ConversationType.GROUP,
            useRtp = false,
            traceId = recordInfo.traceId,
            from = pageType.code,
            isVoiceFilter = currentFilterId.isNotNull(),
        )
    }

    // 尝试录制失败
    private fun onStartRecordFailed(){
        logLineError(TAG, LogLine.VOICE_MSG_RECORD,"onStartRecordFailed")
        fragment.viewLifecycleScope.launchMain {
            if (vadRecordViewModel.lastErrorCode == RecordingConstant.ERRO_CLIENTSILENCED) {
                toastSolidWarning(R.string.mic_already_in_use_tip)
                logLineWarn(TAG, LogLine.VOICE_MSG_RECORD,"toast: Mic already in use, close other apps and try again.")
            } else {
                showTooShortToast()
                logLineWarn(TAG, LogLine.VOICE_MSG_RECORD,"toast: Tap and Hold to Talk")
            }
        }
        onStopRecord(reason = null)
    }

    @CallSuper
    private fun handlePressRelease(reason: ReleaseReason) {
        logLineInfo(TAG, LogLine.VOICE_MSG_RECORD,"handlePressRelease reason: $reason")
        handlePressDownJob?.cancel()
        tryToStartRecordJob?.cancel()
        recordJob?.cancel()
        onStopRecordForReal(reason)
        trackWhenRelease(reason)
    }

    @CallSuper
    private fun onStopRecordForReal(reason: ReleaseReason) {
        if (reason != ReleaseReason.NORMAL && reason != ReleaseReason.USER_PREVIEW) {
            //这种方式停止不会产生aac文件
            onCancelRecord(reason)
        } else {
            //产生aac文件并上传
            vadRecordViewModel.stopRecord()
        }
        onStopRecord(reason)
        recordViewModel.onStopRecording()
        VibratorUtil.vibratorMaxAmplitude(from = "$TAG stop record")
        audioFocusManager.abandon()
        when (reason) {
            ReleaseReason.NORMAL -> {
                if (AppConfigRequestManager.enableRecordingSoundEffects) {
                    RingtoneManager.play(RingtoneManager.AUDIO_WALKIE_TALKIE_BUTTON_RELEASE)
                }
            }
            ReleaseReason.USER_PREVIEW -> {}
            else -> {
                RingtonePlayer.getInstance().play(RingtonePlayer.TYPE_CHAT_SEND_VOICE_CANCEL)
            }
        }
    }

    @CallSuper
    open fun onCancelRecord(reason: ReleaseReason){
        vadRecordViewModel.cancelRecord()
        val isUserCanceled = reason == ReleaseReason.USER_CANCEL
        if (isUserCanceled) {
            toastRegularWarning(R.string.message_canceled)
        } else {
            val tips = when (reason) {
                ReleaseReason.SILENT -> R.string.mic_already_in_use_tip.asString()
                ReleaseReason.APP_BACKGROUND,
                ReleaseReason.ON_DISPOSE,
                ReleaseReason.ON_BACK_PRESS -> R.string.message_canceled.asString()
                else -> R.string.voice_recording_error.asString()
            }
            toastSolidWarning(tips)
        }
        trackRtpResult(
            isSuccess = false,
            errorCode = vadRecordViewModel.lastErrorCode,
            isUserCanceled = isUserCanceled
        )
    }

    private fun initVadRecordDurationObserver() {
        vadRecordViewModel.vadDetectedLiveData.observe(fragment.viewLifecycleOwner) { duration ->
            recordViewModel.updateLastStartRecordTime()
        }
    }

    // 监听 Vad 录制结果
    private fun initVadRecordSegmentObserver() {
        vadRecordViewModel.vadRecordSegmentFlow.collect(fragment.viewLifecycleOwner) { segment ->
            logLineInfo(
                TAG,
                LogLine.VOICE_MSG_RECORD,
                "receive segment, " +
                    "segment.durationMs: ${segment.durationMs}, " +
                    "segment.endType: ${segment.endType}, " +
                    "isBtnPressing: ${RecordStatusHelper.isPressingOrLocking}"
            )
            if (segment.durationMs <= MIN_RECORD_DURATION) {
                showTooShortToast()
                vadRecordViewModel.viewModelScope.launch(Dispatchers.IO) {
                    File(segment.aacFilePath).delete()
                }
                trackRtpResult(
                    isSuccess = false,
                    errorCode = 1
                )
                logLineInfo(
                    TAG,
                    LogLine.VOICE_MSG_RECORD,
                    "return bzc too short record segment duration: ${segment.durationMs}"
                )
                return@collect
            }
            var isPreview = lastReleaseReason == ReleaseReason.USER_PREVIEW
            var isPreviewNotAutoPlay = false
            if (segment.endType == VadRecordEngine.VadEndType.Vad_End_OverMaxDuration) {
                toast(
                    ResUtil.getString(
                        R.string.wt_record_max_time_limit_tip,
                        CommonConstant.MAX_RECORD_DURATION
                    )
                )
                if (lastIsLocking) {
                    recordViewModel.onRecordRelease(ReleaseReason.USER_PREVIEW)
                    isPreview = true
                    isPreviewNotAutoPlay = true
                    withTimeoutOrNull(500) {
                        RingtoneManager.playAndAwait(RingtoneManager.AUDIO_WALKIE_TALKIE_BUTTON_LOCKING)
                    }
                } else {
                    recordViewModel.onRecordRelease(ReleaseReason.NORMAL)
                }
            }
            val traceId = recordTarget?.traceId
            TraceUtils.reportReduceNoiseData(traceId.toString(), segment.audioProcessMetrics)
            onSegmentReceived(segment, isPreview, isPreviewNotAutoPlay)
        }
    }

    open val replyId: Long? = null
    open fun getMentionedUsers(): List<MentionedUser>? = null

    // 拿到录制片段，发送IM语音消息
    @CallSuper
    open fun onSegmentReceived(
        segment: VadRecordSegment,
        isPreview: Boolean,
        isPreviewNotAutoPlay: Boolean
    ) {
        val recordTarget = recordTarget
        if (recordTarget == null) {
            toast(R.string.error_try_again)
            if (isDebug){
                throw IllegalStateException("recordTarget is null")
            }
            logLineError(TAG, LogLine.VOICE_MSG_RECORD, "onSegmentReceived recordTarget is null")
            return
        }
        logLineInfo(
            TAG,
            LogLine.VOICE_MSG_RECORD,
            "onSegmentReceived traceId: ${recordTarget.traceId}, " +
            "replyId: $replyId, selfReportSilent: ${vadRecordViewModel.selfReportSilent}"
        )
        sendRecordViewModel.onSegmentReceived(
            segment = segment,
            targetId = recordTarget.targetId,
            convType = if (recordTarget.isGroup) IM5ConversationType.GROUP else IM5ConversationType.PRIVATE,
            traceId = recordTarget.traceId,
            pageType = pageType,
            replyId = replyId,
            isPreview = isPreview,
            isPreviewNotAutoPlay = isPreviewNotAutoPlay,
            selfReportSilent = vadRecordViewModel.selfReportSilent,
            voiceFilterId = recordTarget.voiceFilterId,
            mentionedUsers = recordTarget.mentionedUsers
        )
    }

    private fun initVadRecordErrorObserver() {
        vadRecordViewModel.vadRecordErrorFlow.collectIn(fragment.viewLifecycleOwner) { errorCode ->
            logLineWarn(TAG, LogLine.VOICE_MSG_RECORD,"vadRecordErrorFlow errorCode: $errorCode")
            //触发录音器静默行为。但是不会主动取消录音因此需要区分逻辑
            if (RecordingConstant.ERRO_CLIENTSILENCED == errorCode || RecordingConstant.WARN_SILENT == errorCode) {
                val reason = if (RecordingConstant.ERRO_CLIENTSILENCED == errorCode) {
                    ReleaseReason.SILENT
                } else {
                    ReleaseReason.WARN_SILENT
                }
                recordViewModel.onRecordRelease(reason)
            }
            val tips = if (RecordingConstant.ERRO_CLIENTSILENCED == errorCode) {
                R.string.mic_already_in_use_tip
            } else if (RecordingConstant.WARN_SILENT == errorCode) {
                R.string.voice_recording_error
            } else {
                R.string.record_failure_tip
            }
            toastSolidWarning(tips)
            trackRtpResult(isSuccess = false, errorCode = errorCode)
        }
    }

    private fun trackRtpResult(
        isSuccess: Boolean,
        errorCode: Int,
        isUserCanceled: Boolean = false,
    ) {
        val recordTarget = recordTarget
        sendRecordViewModel.trackRtpResult(
            targetId = recordTarget?.targetId ?: -9999L,
            convType = if (recordTarget?.isGroup == true) IM5ConversationType.GROUP else IM5ConversationType.PRIVATE,
            pageType = pageType,
            traceId = recordTarget?.traceId,
            isSuccess = isSuccess,
            errorCode = errorCode,
            isUserCanceled = isUserCanceled,
        )
    }

    private fun initVoiceFilterOpenStatusObserver() {
        combine(
            voiceFilterViewModel.hasClickedSwitchVoiceFilterModeFlow,
            voiceFilterViewModel.voiceFilterListLoadStatusFlow
        ) { (hasClicked, clickFrom), loadStatus ->
            if (hasClicked && (loadStatus == LoadingState.Success || loadStatus is LoadingState.Failed)) {
                clickFrom to loadStatus
            } else {
                null
            }
        }.collectLatestIn(fragment.viewLifecycleOwner) { it ->
            if (!fragment.atLeastResumed) return@collectLatestIn
            val (clickFrom, loadStatus) = it ?: return@collectLatestIn
            VoiceFilterTracker.onResultBackOpenVoiceFilter(
                isGroup = getConvType() == IM5ConversationType.GROUP,
                targetId = getTargetId() ?: 0L,
                source = clickFrom ?: "",
                isSuccess = loadStatus == LoadingState.Success,
                rCode = (loadStatus as? LoadingState.Failed)?.rCode
            )
        }
    }

    private fun initVoiceFilterScrollingObserver() {
        voiceFilterViewModel.isVoiceFilterScrollingStateFlow.collectLatestIn(fragment.viewLifecycleOwner) {
            if (it) {
                voiceFilterViewModel.stopFilterSoundUrl()
            }
        }
    }

    private fun trackWhenPress(){
        if (voiceFilterViewModel.isInVoiceFilterMode()) {
            VoiceFilterTracker.onClickVoiceFilterRecord(
                isGroup = getConvType() == IM5ConversationType.GROUP,
                targetId = getTargetId() ?: 0L,
                pageType = when (pageType) {
                    IMSendFrom.HOME -> "homepage"
                    IMSendFrom.CHAT_LIST -> "chat_history"
                    IMSendFrom.OVERLAY -> "overlay"
                },
                filterId = voiceFilterViewModel.getCurrentSelectVoiceFilterId()
            )
        }
    }

    private fun trackWhenRelease(reason: ReleaseReason) {
        val pageType = when (pageType) {
            IMSendFrom.HOME -> "homepage"
            IMSendFrom.CHAT_LIST -> "chat_history"
            IMSendFrom.OVERLAY -> "overlay"
        }
        val type = when (reason) {
            ReleaseReason.USER_CANCEL -> VoiceFilterTracker.TYPE_CANCEL
            ReleaseReason.USER_PREVIEW -> VoiceFilterTracker.TYPE_PREVIEW
            else -> null
        }
        type?.let {
            VoiceFilterTracker.onClickRecordCancelOrPreview(
                isGroup = getConvType() == IM5ConversationType.GROUP,
                targetId = getTargetId() ?: 0L,
                type = it,
                pageType = pageType,
                isVoiceFilterMode = voiceFilterViewModel.isInVoiceFilterMode(),
                filterId = voiceFilterViewModel.getCurrentSelectVoiceFilterId()
            )
        }
        when (reason) {
            ReleaseReason.NORMAL -> RecordTracker.TYPE_SEND
            ReleaseReason.USER_CANCEL -> RecordTracker.TYPE_CANCEL
            ReleaseReason.USER_PREVIEW -> RecordTracker.TYPE_PREVIEW
            else -> null
        }?.let {
            RecordTracker.onReleaseWhenLocking(
                isGroup = getConvType() == IM5ConversationType.GROUP,
                targetId = getTargetId() ?: 0L,
                pageType = pageType,
                type = it,
                isVoiceFilterMode = voiceFilterViewModel.isInVoiceFilterMode(),
                filterId = voiceFilterViewModel.getCurrentSelectVoiceFilterId()
            )
        }
    }

    private fun trackWhenLocking() {
        RecordTracker.onLockingModeResult(
            isGroup = getConvType() == IM5ConversationType.GROUP,
            targetId = getTargetId() ?: 0L,
            pageType = when (pageType) {
                IMSendFrom.HOME -> "homepage"
                IMSendFrom.CHAT_LIST -> "chat_history"
                IMSendFrom.OVERLAY -> "overlay"
            },
            isVoiceFilterMode = voiceFilterViewModel.isInVoiceFilterMode(),
            filterId = voiceFilterViewModel.getCurrentSelectVoiceFilterId()
        )
    }

    private fun showTooShortToast() {
        toastSolidWarning(
            message = R.string.chat_tap_and_hold_to_speak,
            duration = Toast.LENGTH_SHORT
        )
    }

    override fun onPause() {
        super.onPause()
        handleOnPause()
    }

    // 录制中应用退去后台后，停止录制
    open fun handleOnPause() {
        logInfo(TAG, "handleOnPause")
        if (RecordStatusHelper.isPressingOrLocking) {
            recordViewModel.onRecordRelease(reason = ReleaseReason.APP_BACKGROUND)
        }
    }
    
}