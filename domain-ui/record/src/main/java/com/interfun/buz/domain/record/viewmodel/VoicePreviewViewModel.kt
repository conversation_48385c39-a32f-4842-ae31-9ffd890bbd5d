package com.interfun.buz.domain.record.viewmodel

import android.app.Application
import android.view.View.MeasureSpec
import android.view.ViewGroup
import androidx.compose.ui.graphics.toArgb
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.interfun.buz.base.ktx.*
import com.interfun.buz.biz.center.voicefilter.repository.VoiceFilterDataRepository
import com.interfun.buz.biz.center.voicefilter.tracker.VoiceFilterTracker
import com.interfun.buz.common.bean.user.BuzUserRelationValue
import com.interfun.buz.common.constants.CommonMMKV
import com.interfun.buz.common.database.entity.chat.GroupUserStatus
import com.interfun.buz.common.di.standard.IVFVideoGeneration
import com.interfun.buz.common.manager.cache.group.GroupInfoCacheManager
import com.interfun.buz.common.manager.cache.user.UserRelationCacheManager
import com.interfun.buz.common.tracker.trackerString
import com.interfun.buz.core.widget_record.state.VoiceFilterUiData
import com.interfun.buz.core.widget_record.state.VoiceFilterUiData.PreviewType
import com.interfun.buz.core.widget_record.state.bubbleStyle
import com.interfun.buz.core.widget_record.state.toVoiceFilterUiData
import com.interfun.buz.core.widget_record.ui.VoicePreviewAction
import com.interfun.buz.core.widget_record.ui.VoicePreviewAction.*
import com.interfun.buz.core.widget_record.ui.VoiceFilterPreviewState
import com.interfun.buz.domain.im.social.entity.CommonMsgParams
import com.interfun.buz.domain.im.social.entity.ShareItemBean
import com.interfun.buz.domain.im.social.entity.ShareType
import com.interfun.buz.domain.im.social.entity.VoiceMsgParams
import com.interfun.buz.domain.im.social.repository.SendIMRepository
import com.interfun.buz.domain.im.social.repository.SendIMRepository.PreDeliveryType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import com.interfun.buz.domain.record.R
import com.interfun.buz.domain.record.entity.RecordPreviewMsgParam
import com.interfun.buz.domain.record.manager.VoicePreviewPlayer
import com.interfun.buz.domain.record.trace.TraceUtils
import com.interfun.buz.domain.social.components.VoiceFilterMessagePreview
import com.interfun.buz.domain.social.components.VoiceFilterMessagePreviewType
import com.interfun.buz.im.IMAgent
import com.interfun.buz.im.entity.*
import com.interfun.buz.im.ktx.asrText
import com.interfun.buz.im.ktx.getVoiceDuration
import com.interfun.buz.im.track.IMTracker
import com.interfun.buz.social.repo.GroupRepository
import com.interfun.buz.social.repo.UserRepository
import com.interfun.buz.user.repository.UserSettingRepository
import com.lizhi.component.tekiplayer.controller.PlayerState
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import com.lizhi.im5.sdk.message.model.IM5VoiceMessage
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import java.io.File
import javax.inject.Inject

/**
 * 分享事件数据类
 * 用于通过 Flow 通知 Dialog 执行分享操作
 */
data class ShareEvent(
    val shareType: ShareItemBean,
    val videoFile: File
)

@HiltViewModel
class VoicePreviewViewModel @Inject constructor(
    private val voiceFilterRepository: VoiceFilterDataRepository,
    private val sendIMRepository: SendIMRepository,
    private val userRepository: UserRepository,
    private val groupRepository: GroupRepository,
    private val application: Application,
    private val vfVideoGeneration: IVFVideoGeneration,
    private val userSettingRepo: UserSettingRepository
) : ViewModel() {
    private val TAG = "VoicePreviewViewModel_test"

    /**
     * 语音预览来源类型枚举
     * 用于区分不同的调用场景，以便应用对应的动画效果和UI调整
     */
    enum class SourceType(val value: Int) {
        /** 来自首页场景 */
        HOME(0),

        /** 来自聊天列表场景 */
        CHAT_LIST(1);

        companion object {
            /**
             * 通过值获取对应的枚举
             */
            fun fromValue(value: Int): SourceType {
                return values().find { it.value == value } ?: HOME
            }
        }
    }

    private lateinit var voiceIMParams: RecordPreviewMsgParam
    private lateinit var pageType: IMSendFrom

    private val _previewAction = MutableSharedFlow<VoicePreviewAction>()
    val previewAction: SharedFlow<VoicePreviewAction> = _previewAction.asSharedFlow()

    // 内部状态管理，用于构建统一的VoiceFilterPreviewState
    private val _duration = MutableStateFlow(0).also { flow ->
//        logInfo(TAG, "StateFlow initialized: _duration = ${flow.value}")
    }
    private val _offsetY = MutableStateFlow(0).also { flow ->
//        logInfo(TAG, "StateFlow initialized: _offsetY = ${flow.value}")
    }
    private val _voiceFilter = MutableStateFlow<VoiceFilterUiData?>(null).also { flow ->
//        logInfo(TAG, "StateFlow initialized: _voiceFilter = ${flow.value}")
    }
    private val _isPlaying = MutableStateFlow(true).also { flow ->
//        logInfo(TAG, "StateFlow initialized: _isPlaying = ${flow.value}")
    }
    private val _isOnSendAnim = MutableStateFlow(false).also { flow ->
//        logInfo(TAG, "StateFlow initialized: _isOnSendAnim = ${flow.value}")
    }
    private val _filterState = MutableStateFlow(FilterSuccess).also { flow ->
//        logInfo(TAG, "StateFlow initialized: _filterState = ${flow.value}")
    }
    private val _playProgress = MutableStateFlow(0f).also { flow ->
//        logInfo(TAG, "StateFlow initialized: _playProgress = ${flow.value}")
    }
    private val _notClickCancelOp = MutableStateFlow(true).also { flow ->
//        logInfo(TAG, "StateFlow initialized: _notClickCancelOp = ${flow.value}")
    }
    private val _sendAnimTranslationY = MutableStateFlow(0).also { flow ->
//        logInfo(TAG, "StateFlow initialized: _sendAnimTranslationY = ${flow.value}")
    }

    // 分享事件流，用于通知 Dialog 执行分享操作
    private val _shareEventFlow = MutableSharedFlow<ShareEvent>()
    val shareEventFlow: SharedFlow<ShareEvent> = _shareEventFlow.asSharedFlow()

    /**
     * 播放进度状态流
     * 暴露给外部组件监听播放进度，实现最小化重组
     */
    val playProgress: StateFlow<Float> = _playProgress.asStateFlow()

    val filterId: Long? get() = _voiceFilter.value?.filterId

    private val isPrivate: Boolean get() = voiceIMParams.convType == IM5ConversationType.PRIVATE
    private val previewIdFlow = MutableStateFlow<String?>(null)
    private val msgIdFlow = MutableStateFlow<String?>(null)

    private val audioUrlStateFlow = MutableStateFlow<String?>(null).also { flow ->
        viewModelScope.launch {
            flow.collect { url ->
                if (url != null && !voiceIMParams.isPreviewNotAutoPlay) playVoice()
            }
        }
    }
    private val _audioASRStateFlow = MutableStateFlow<String?>(null)
    val audioASRStateFlow = _audioASRStateFlow.asStateFlow()

    private val _showSendTipFlow = MutableStateFlow<String>("")
    val showSendTipFlow = _showSendTipFlow.asStateFlow()

    // 响应式参数流，用于状态转换
    private val _asrTextFlow = MutableStateFlow<String?>(null)
    private val _sourceTypeFlow = MutableStateFlow(SourceType.HOME) // 使用枚举类型，默认为HOME
    private val _errorMessageFlow = MutableStateFlow<String>("")

    val isQuietModeEnable = userSettingRepo.getQuiteMode().map { it.enable }
        .stateIn(viewModelScope, SharingStarted.WhileSubscribed(5000L), false)


    /**
     * 响应式的语音滤镜预览状态流
     * 自动响应内部状态变化，实现真正的MVVM响应式架构
     *
     * StateFlow 参数顺序：
     * [0] _duration - 语音时长
     * [1] _offsetY - Y轴偏移量
     * [2] _voiceFilter - 语音滤镜数据
     * [3] _isPlaying - 是否正在播放
     * [4] _isOnSendAnim - 是否正在执行发送动画
     * [5] _filterState - 滤镜状态
     * [6] _playProgress - 播放进度
     * [7] _notClickCancelOp - 是否未点击取消操作
     * [8] _sendAnimTranslationY - 发送动画Y轴偏移量
     * [9] _asrTextFlow - ASR识别文本
     * [10] _sourceTypeFlow - 来源类型
     */
    val voiceFilterPreviewState: StateFlow<VoiceFilterPreviewState> = combine(
        _duration,
        _offsetY,
        _voiceFilter,
        _isPlaying,
        _isOnSendAnim,
        _filterState,
        _playProgress,
        _notClickCancelOp,
        _sendAnimTranslationY,
        _asrTextFlow,
        _sourceTypeFlow,
        _errorMessageFlow
    ) { flows ->
        // 通过数组索引访问各个状态值
        val duration = flows[0] as Int                    // _duration
        val offsetY = flows[1] as Int                     // _offsetY
        val voiceFilter = flows[2] as VoiceFilterUiData?  // _voiceFilter
        val isPlaying = flows[3] as Boolean               // _isPlaying
        val isOnSendAnim = flows[4] as Boolean            // _isOnSendAnim
        val filterState = flows[5] as Int                 // _filterState
        val playProgress = flows[6] as Float              // _playProgress
        val notClickCancelOp = flows[7] as Boolean        // _notClickCancelOp
        val sendAnimTranslationY = flows[8] as Int        // _sendAnimTranslationY
        val asrText = flows[9] as String?                 // _asrTextFlow
        val sourceType = flows[10] as SourceType         // _sourceTypeFlow
        val errorMessage = flows[11] as String            // _errorMessageFlow

        logInfo(
            TAG, "voiceFilterPreviewState combine triggered - " +
                    "duration=$duration, offsetY=$offsetY, voiceFilter=${voiceFilter?.filterId}, " +
                    "isPlaying=$isPlaying, isOnSendAnim=$isOnSendAnim, filterState=$filterState, " +
                    "playProgress=$playProgress, notClickCancelOp=$notClickCancelOp, " +
                    "sendAnimTranslationY=$sendAnimTranslationY, asrText=$asrText, sourceType=$sourceType, " +
                    "errorMessage=$errorMessage"
        )

        val newState = mapToUnifiedVoiceFilterPreviewState(
            duration, offsetY, voiceFilter, isPlaying, isOnSendAnim, filterState,
            playProgress, notClickCancelOp, sendAnimTranslationY, asrText, sourceType, errorMessage
        )
//        logInfo(
//            TAG, "voiceFilterPreviewState updated: ${newState::class.simpleName}, " +
//                    "filterState=$filterState, isPlaying=$isPlaying, asrText=$asrText, sourceType=$sourceType"
//        )
        newState
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = VoiceFilterPreviewState.Loading(
            duration = 0, // 初始值为0，会在数据加载后更新
            isOnSendAnim = false,
            sendAnimTranslationY = 0,
            isPlaying = false
        )
    )

    //    private val _action = MutableSharedFlow<VoicePreviewAction>()
//    val action: SharedFlow<VoicePreviewAction> = _action.asSharedFlow()
//
//    fun onAction(action: VoicePreviewAction) {
//        _action.emitInScope(viewModelScope, action)
//    }
    fun initPreviewUiState(
        params: RecordPreviewMsgParam,
        pageType: IMSendFrom,
        offsetY: Int? = null,
        sendAnimTranslationY: Int? = null,
        sourceType: SourceType = SourceType.HOME, // 改为枚举类型，默认为HOME
    ) {
        logInfo(
            TAG, "initData : params = $params, pageType = $pageType, " +
                    "offsetY = $offsetY, sendAnimTranslationY = $sendAnimTranslationY, " +
                    "voiceFilterId = ${params.voiceFilterId}, sourceType = $sourceType"
        )
        this.voiceIMParams = params
        this.pageType = pageType

        // 设置来源类型，用于状态转换
        _sourceTypeFlow.value = sourceType

        // 同步ASR文本流
        _asrTextFlow.value = _audioASRStateFlow.value

        // 监听ASR状态变化并同步到状态流
        viewModelScope.launch {
            _audioASRStateFlow.collect { asrText ->
                _asrTextFlow.value = asrText
            }
        }

        // 初始化内部状态
        logInfo(
            TAG,
            "initPreviewUiState - updating _duration: ${_duration.value} -> ${params.duration}"
        )
        _duration.value = params.duration

        logInfo(TAG, "initPreviewUiState - updating _offsetY: ${_offsetY.value} -> ${offsetY ?: 0}")
        _offsetY.value = offsetY ?: 0

        logInfo(
            TAG,
            "initPreviewUiState - updating _isPlaying: ${_isPlaying.value} -> ${!params.isPreviewNotAutoPlay}"
        )
        _isPlaying.value = !params.isPreviewNotAutoPlay

        logInfo(
            TAG,
            "initPreviewUiState - updating _sendAnimTranslationY: ${_sendAnimTranslationY.value} -> ${sendAnimTranslationY ?: 0}"
        )
        _sendAnimTranslationY.value = sendAnimTranslationY ?: 0

        observePlayingProgress()
        observePlayErrorMsg()

        val targetId = params.targetId.toLongOrNull()
        if (targetId != null && params.convType == IM5ConversationType.PRIVATE) {
            userRepository.getUserCompositeFlow(targetId).onEach { userMap ->
                val displayName = application.getString(
                    R.string.ve_preview_send_to_xxx,
                    userMap.user.firstNickName(userMap.relationInfo)
                )
                _showSendTipFlow.emit(displayName)
            }.launchIn(viewModelScope)
        } else if (targetId != null && params.convType == IM5ConversationType.GROUP) {
            groupRepository.getGroupFromCacheFlow(targetId).onEach { groupInfo ->
                val displayName = application.getString(
                    R.string.ve_preview_send_to_xxx,
                    groupInfo?.groupName
                )
                _showSendTipFlow.emit(displayName)
            }.launchIn(viewModelScope)
        }
        // update normal voice message url & play
        if (null == params.voiceFilterId) {
            audioUrlStateFlow.value = params.aacPath
            return
        }
        viewModelScope.launchIO {
            val loadedVoiceFilter = voiceFilterRepository
                .getCachedVoiceFilterInfoById(params.voiceFilterId)
                ?.toVoiceFilterUiData()

            logInfo(TAG, "initData: loadedVoiceFilter = $loadedVoiceFilter")
            logInfo(
                TAG,
                "initPreviewUiState - updating _voiceFilter: ${_voiceFilter.value?.filterId} -> ${loadedVoiceFilter?.filterId}"
            )
            _voiceFilter.value = loadedVoiceFilter

            if (loadedVoiceFilter != null) {
                observeFilterState()
                filterVoice()
            }
        }


    }

    fun onPreviewAction(): (VoicePreviewAction) -> Unit = { action ->
        when (action) {
            CancelAction -> onCancelAction()
            PlayOrFilterAction -> onPlayOrFilterAction()
            SendAction -> onSendAction()
            SendActionAnimFinish -> onSendActionAnimFinish()
        }
    }

    private fun onPlayOrFilterAction() {
        when (_filterState.value) {
            FilterIng -> return
            FilterFail -> {
                filterId?.let {
                    filterVoice()
                    VoiceFilterTracker.onVoiceFilterPreviewDialogButtonClick(
                        isPrivate = isPrivate,
                        targetId = voiceIMParams.targetId,
                        pageType = pageType.toTrackString(),
                        filterId = filterId ?: 0L,
                        buttonString = "retry",
                        campaignId = "${_voiceFilter.value?.campaignConfig?.id ?: ""}"
                    )
                }
                return
            }

            FilterSuccess -> {
                if (_isPlaying.value == false) {
                    playVoice()
                    observePlayingProgress()
                    VoiceFilterTracker.onVoiceFilterPreviewDialogButtonClick(
                        isPrivate = isPrivate,
                        targetId = voiceIMParams.targetId,
                        pageType = pageType.toTrackString(),
                        filterId = filterId ?: 0L,
                        buttonString = "replay",
                        campaignId = "${_voiceFilter.value?.campaignConfig?.id ?: ""}"
                    )
                } else {
                    stopPlay()
                    VoiceFilterTracker.onVoiceFilterPreviewDialogButtonClick(
                        isPrivate = isPrivate,
                        targetId = voiceIMParams.targetId,
                        pageType = pageType.toTrackString(),
                        filterId = filterId ?: 0L,
                        buttonString = "stop",
                        campaignId = "${_voiceFilter.value?.campaignConfig?.id ?: ""}"
                    )
                }
            }
        }
    }

    private fun observePlayingProgress() = launchIO {
        VoicePreviewPlayer.playerState.collect {
            if (it != PlayerState.STATE_PLAYING && it != PlayerState.STATE_BUFFERING) {
                logInfo(
                    TAG,
                    "observePlayingProgress - updating _playProgress: ${_playProgress.value} -> 0f (PlayerState: $it)"
                )
                _playProgress.value = 0f
                return@collect
            }
            if (_duration.value == 0) {
                return@collect
            }

            val currentPlayItem = VoicePreviewPlayer.getPlayingItem()
            logInfo(
                TAG,
                "observePlayingProgress:$it  isPlay = ${_isPlaying.value} tag = ${currentPlayItem?.tag}"
            )
            if (it == PlayerState.STATE_PLAYING && currentPlayItem?.tag == audioUrlStateFlow.value) {
                logInfo(
                    TAG,
                    "observePlayingProgress - updating _isPlaying: ${_isPlaying.value} -> true (PlayerState.STATE_PLAYING)"
                )
                _isPlaying.value = true
                while (_isPlaying.value && isActive) {
                    delay(50)
                    if (VoicePreviewPlayer.getPlayingItem()?.tag == audioUrlStateFlow.value && VoicePreviewPlayer.playerState.value == PlayerState.STATE_PLAYING) {
                        _playProgress.value = VoicePreviewPlayer.getCurrentPlayPosition()
                            .toFloat() / _duration.value // Use duration
                    } else {
                        onPlayFinish()
                        return@collect
                    }
                }
                _playProgress.value = 0f
            }
        }
    }

    private fun observePlayErrorMsg() = launchIO {
        VoicePreviewPlayer.onPlayErrorMsg.collect {
            val currentItem = VoicePreviewPlayer.getPlayingItem()
            if (currentItem?.tag != audioUrlStateFlow.value) return@collect
            toast(R.string.unable_to_load)
            onPlayFinish()
        }
    }

    private var clickCancel = false
    private var clickSend = false
    private fun onCancelAction() {
        clickCancel = true
        viewModelScope.launch {
            previewIdFlow.value?.let { sendIMRepository.cancelPreviewVoiceFilter(it) }
        }

        // 取消预览时清理音频文件
        cleanupAudioFileOnCancel()
        IMTracker.onChatSendVoiceResult(
            targetId = voiceIMParams.targetId,
            convType = voiceIMParams.convType,
            duration = voiceIMParams.duration,
            traceId = voiceIMParams.traceId,
            mentionedUsers = voiceIMParams.mentionedUsers,
            filterId,
            null,
            null,
            if (isQuietModeEnable.value) 1 else 0,
            "0",
            true
        )

        stopPlay()
        _previewAction.emitInScope(this.viewModelScope, VoicePreviewAction.CancelAction)
        logInfo(
            TAG,
            "onCancelAction - updating _notClickCancelOp: ${_notClickCancelOp.value} -> false"
        )
        _notClickCancelOp.value = false
        VoiceFilterTracker.onVoiceFilterPreviewDialogButtonClick(
            isPrivate = isPrivate,
            targetId = voiceIMParams.targetId,
            pageType = pageType.toTrackString(),
            filterId = filterId ?: 0L,
            buttonString = "cancel", campaignId = "${_voiceFilter.value?.campaignConfig?.id ?: ""}"

        )
    }

    private fun onSendAction() {
        clickSend = true
        logInfo(TAG, "onSendAction - method called")
        val isConvAvailable = checkConvStatusBeforeSend()
        if (!isConvAvailable) return
        stopPlay()
        logInfo(TAG, "onSendAction - updating _isOnSendAnim: ${_isOnSendAnim.value} -> true")
        _isOnSendAnim.value = true

        viewModelScope.launch(Dispatchers.IO) {
            launch {
                sendMessage()
            }
            _previewAction.emit(SendAction)
            VoiceFilterTracker.onVoiceFilterPreviewDialogButtonClick(
                isPrivate = isPrivate,
                targetId = voiceIMParams.targetId,
                pageType = pageType.toTrackString(),
                filterId = filterId ?: 0L,
                buttonString = "send",
                campaignId = "${_voiceFilter.value?.campaignConfig?.id ?: ""}"
            ) // filterId derived from uiState
        }
    }

    private fun onSendActionAnimFinish() {
        _previewAction.emitInScope(viewModelScope, VoicePreviewAction.SendActionAnimFinish)
    }

    private fun stopPlay() {
        logInfo(TAG, "stopPlay - updating _isPlaying: ${_isPlaying.value} -> false")
        _isPlaying.value = false
        VoicePreviewPlayer.stopPlay()
    }

    private fun playVoice() {
        audioUrlStateFlow.value?.let { url ->
            VoicePreviewPlayer.play(url, _duration.value.toLong())
        }
    }

    private fun onPlayFinish() {
        logInfo(
            TAG,
            "onPlayFinish - updating _isPlaying: ${_isPlaying.value} -> false, _playProgress: ${_playProgress.value} -> 0f"
        )
        _isPlaying.value = false
        _playProgress.value = 0f
    }

    private fun filterVoice() {
        if (_filterState.value == FilterIng) return
        logInfo(TAG, "filterVoice - updating _filterState: ${_filterState.value} -> $FilterIng")
        _filterState.value = FilterIng

        viewModelScope.launch {
            val voiceMsgParams = VoiceMsgParams(
                targetId = voiceIMParams.targetId,
                path = voiceIMParams.aacPath,
                convType = voiceIMParams.convType,
                duration = voiceIMParams.duration,
                commonMsgParams = CommonMsgParams(
                    traceId = voiceIMParams.traceId,
                    replyId = voiceIMParams.replyId,
                    eventTrackExtra = EventTrackExtra(sendFrom = pageType.code)
                ),
                mentionedUsers = voiceIMParams.mentionedUsers,
            )

            val preDeliveryType: PreDeliveryType =
                determinePreDeliveryType(voiceIMParams.voiceFilterId)
            val voiceFilterInfo = if (voiceIMParams.voiceFilterId != null) {
                voiceFilterRepository.getCachedVoiceFilterInfoById(voiceIMParams.voiceFilterId!!)
            } else null

            val previewResult = sendIMRepository.previewVoiceFilterMessage(
                voiceMsgParams,
                VoiceFilterInfo(
                    filterId = voiceIMParams.voiceFilterId,
                    originalUrl = null,
                    filterState = FilterIng,
                    videoTemplateUrl = voiceFilterInfo?.videoTemplate?.videoTemplateUrl,
                    videoTemplateMd5 = voiceFilterInfo?.videoTemplate?.videoTemplateMd5,
                    bubbleStyle = voiceFilterInfo?.itemConfig?.bubbleStyle ?: 0,
                    suitColor1 = voiceFilterInfo?.backgroundColor,
                    suitColor2 = voiceFilterInfo?.microphoneColor
                ),
                preDeliveryType = preDeliveryType
            )

            msgIdFlow.value = previewResult.message.localMsgId
            previewIdFlow.value = previewResult.previewId
        }
    }

    private suspend fun determinePreDeliveryType(filterId: Long?): PreDeliveryType {
        if (filterId == null) return PreDeliveryType.Normal

        val filterData = voiceFilterRepository
            .getCachedVoiceFilterInfoById(filterId)
            ?.toVoiceFilterUiData()

        return if (filterData?.itemConfig?.previewType == PreviewType.PREVIEW.originDef) {
            PreDeliveryType.AsrAndVF
        } else {
            PreDeliveryType.Normal
        }
    }

    private fun observeFilterState() {
        launch {
            IMAgent.msgVoiceFilterFlow.collect {messageVoiceFilterData->
                logInfo(
                    TAG,
                    "observeFilterState: ${messageVoiceFilterData.state}, error: ${messageVoiceFilterData.error} msgId: ${msgIdFlow.value}"
                )
                if (messageVoiceFilterData.message.localMsgId != msgIdFlow.value) return@collect

                logInfo(
                    TAG,
                    "observeFilterState - updating _filterState: ${_filterState.value} -> ${messageVoiceFilterData.state}"
                )
                _filterState.value = messageVoiceFilterData.state
                logInfo(
                    TAG,
                    "observeFilterState: ${messageVoiceFilterData.state}, errorType: ${messageVoiceFilterData.error?.errorType}, errorCode: ${messageVoiceFilterData.error?.errorCode}"
                )

                if (messageVoiceFilterData.state == FilterFail) {
                    // 设置错误信息到状态流
                    val errorMessage = if (messageVoiceFilterData.error?.errorType == VoiceFilterAIErrorType) {
                        val errorMsg = messageVoiceFilterData.error?.errorMsg

                        when (messageVoiceFilterData.error?.errorCode) {
                            VoiceFilterUserCancel -> {
                                // 用户取消，不显示错误信息
                                return@collect
                            }
                            NoVoiceFilterCode -> {
                                // 滤镜已过期
                                if (errorMsg.isNullOrEmpty()) {
                                    application.getString(R.string.voice_flter_expired)
                                }else{
                                    errorMsg
                                }
                            }
                            else -> {
                                if (errorMsg.isNullOrEmpty()) {
                                    application.getString(R.string.filter_loading_failed)
                                }else{
                                    errorMsg
                                }
                            }
                        }
                    } else {
                        // 通用错误
                        application.getString(R.string.filter_loading_failed)
                    }

                    _errorMessageFlow.value = errorMessage
//                    toast(errorMessage)
                    return@collect
                }
                // update voice filter audio url & play
                if (messageVoiceFilterData.state == FilterSuccess) {
                    (messageVoiceFilterData.message.content as? IM5VoiceMessage).apply {
                        logInfo(TAG, "previewResult: remoteUrl = ${this?.remoteUrl}  duration = ${this?.duration}")

                        val remoteUrlBackup = this?.remoteUrl
                        if (!remoteUrlBackup.isNullOrEmpty()) {
                            audioUrlStateFlow.value = remoteUrlBackup
                        }

                        val asrTextBackup = this?.asrText
                        _audioASRStateFlow.value = asrTextBackup
                        _asrTextFlow.value = asrTextBackup // 同步到状态转换流

                        val durationBackup = this?.duration
                        _duration.value = durationBackup ?: 0
                    }


                }
            }
        }
    }

    private suspend fun sendMessage() {
        val voiceMsgParams = VoiceMsgParams(
            targetId = voiceIMParams.targetId,
            path = voiceIMParams.aacPath,
            convType = voiceIMParams.convType,
            duration = voiceIMParams.duration,
            mentionedUsers = voiceIMParams.mentionedUsers,
            commonMsgParams = CommonMsgParams(
                traceId = voiceIMParams.traceId,
                replyId = voiceIMParams.replyId,
                eventTrackExtra = EventTrackExtra(sendFrom = pageType.code)
            ),
        )
        val filterId = voiceIMParams.voiceFilterId
        if (null == filterId) {
            sendIMRepository.sendVoiceMessage(voiceMsgParams)
            return
        }
        val previewId = previewIdFlow.value
        if (previewId == null) {
            val voiceFilterInfo = voiceFilterRepository.getCachedVoiceFilterInfoById(filterId)
            sendIMRepository.sendVoiceFilterMessage(
                voiceMsgParams,
                VoiceFilterInfo(
                    filterId = filterId,
                    originalUrl = null,
                    filterState = FilterIng, // initial state is filtering
                    videoTemplateUrl = voiceFilterInfo?.videoTemplate?.videoTemplateUrl,
                    videoTemplateMd5 = voiceFilterInfo?.videoTemplate?.videoTemplateMd5,
                    bubbleStyle = voiceFilterInfo?.itemConfig?.bubbleStyle ?: 0,
                    suitColor1 = voiceFilterInfo?.backgroundColor,
                    suitColor2 = voiceFilterInfo?.microphoneColor
                )
            )
        } else {
            sendIMRepository.sendVoiceFilterMessage(previewId)
        }
    }


    private fun checkConvStatusBeforeSend(): Boolean {
        val convId = voiceIMParams.targetId.toLongOrNull() ?: return false
        if (voiceIMParams.convType == IM5ConversationType.PRIVATE) {
            val friend =
                UserRelationCacheManager.getUserRelationInfoByUid(voiceIMParams.targetId.toLong())
            val isConversationAvailable =
                friend?.serverRelation == BuzUserRelationValue.FRIEND.value
            if (!isConversationAvailable) {
                toast(R.string.chat_add_friend_first.asString())
            }
            return isConversationAvailable
        }

        if (voiceIMParams.convType == IM5ConversationType.GROUP) {
            val groupInfo = GroupInfoCacheManager.getGroupInfoBeanById(convId)
            val isConversationAvailable = groupInfo?.userStatus == GroupUserStatus.InGroup.value
            if (!isConversationAvailable) {
                toast(R.string.chat_be_removed_tip)
            }
            return isConversationAvailable
        }
        return false
    }

    override fun onCleared() {
        super.onCleared()
        sendIMRepository.launchInUserScope {
            previewIdFlow.value?.let {
                sendIMRepository.cancelPreviewVoiceFilter(it)
            }
        }

        // ViewModel 清理时清理音频文件
        cleanupAudioFileOnCancel()
        VoicePreviewPlayer.stopPlay()
        // 清理分享相关资源
        clearPendingShareRequest()
    }

    private fun IMSendFrom.toTrackString() = when (this) {
        IMSendFrom.HOME -> "homepage"
        IMSendFrom.CHAT_LIST -> "chat_history"
        IMSendFrom.OVERLAY -> "overlay"
    }

    /**
     * 将内部状态映射为统一的 VoiceFilterPreviewState
     * 统一状态管理，支持不同来源的动画效果和传统预览界面的兼容
     *
     * @param duration 语音时长
     * @param offsetY Y轴偏移量
     * @param voiceFilter 语音滤镜数据
     * @param isPlaying 是否正在播放
     * @param isOnSendAnim 是否正在执行发送动画
     * @param filterState 滤镜状态
     * @param playProgress 播放进度
     * @param notClickCancelOp 是否未点击取消操作
     * @param sendAnimTranslationY 发送动画Y轴偏移量
     * @param asrText ASR识别文本
     * @param sourceType 来源类型，用于调整动画效果
     * @param errorMessage 错误信息
     * @return 统一的语音滤镜预览状态
     */
    private fun mapToUnifiedVoiceFilterPreviewState(
        duration: Int,
        offsetY: Int,
        voiceFilter: VoiceFilterUiData?,
        isPlaying: Boolean,
        isOnSendAnim: Boolean,
        filterState: Int,
        playProgress: Float,
        notClickCancelOp: Boolean,
        sendAnimTranslationY: Int,
        asrText: String?,
        sourceType: SourceType,
        errorMessage: String,
    ): VoiceFilterPreviewState {
        return when (filterState) {
            FilterIng -> {
                // 正在处理语音滤镜 - 加载状态
                VoiceFilterPreviewState.Loading(
                    duration = duration, // 传递正确的语音时长
                    isOnSendAnim = isOnSendAnim,
                    sendAnimTranslationY = adjustAnimationForSource(
                        sendAnimTranslationY,
                        sourceType
                    ),
                    voiceFilter = voiceFilter,
                    offsetY = offsetY,
                    notClickCancelOp = notClickCancelOp,
                    isPlaying = isPlaying
                )

            }

            FilterSuccess -> {
                // 处理成功 - 完成状态
                VoiceFilterPreviewState.Completed(
                    duration = duration,
                    filterName = voiceFilter?.filterName ?: "",
                    bubbleStyle = voiceFilter?.bubbleStyle ?: 0,
                    asr = asrText ?: "",
                    isOnSendAnim = isOnSendAnim,
                    sendAnimTranslationY = adjustAnimationForSource(
                        sendAnimTranslationY,
                        sourceType
                    ),
                    backgroundColor = voiceFilter?.backgroundColor?.toArgb()
                        ?: com.interfun.buz.core.widget_record.R.color.brand_90.asColor(),
                    microphoneColor = voiceFilter?.microphoneColor?.toArgb()
                        ?: com.interfun.buz.core.widget_record.R.color.color_text_black_primary.asColor(),
                    voiceFilter = voiceFilter,
                    filterState = filterState,
                    offsetY = offsetY,
                    notClickCancelOp = notClickCancelOp,
                    isPlaying = isPlaying
                )
            }

            FilterFail -> {
                // 处理失败时也显示为完成状态，但不播放
                VoiceFilterPreviewState.Completed(
                    duration = duration,
                    filterName = voiceFilter?.filterName ?: "",
                    asr = asrText ?: "",
                    bubbleStyle = voiceFilter?.bubbleStyle ?: 0,
                    isOnSendAnim = isOnSendAnim,
                    sendAnimTranslationY = adjustAnimationForSource(
                        sendAnimTranslationY,
                        sourceType
                    ),
                    backgroundColor = voiceFilter?.backgroundColor?.toArgb()
                        ?: com.interfun.buz.core.widget_record.R.color.brand_90.asColor(),
                    microphoneColor = voiceFilter?.microphoneColor?.toArgb()
                        ?: com.interfun.buz.core.widget_record.R.color.color_text_black_primary.asColor(),
                    voiceFilter = voiceFilter,
                    filterState = filterState,
                    offsetY = offsetY,
                    notClickCancelOp = notClickCancelOp,
                    isPlaying = false, // 失败时不播放
                    errorMessage = errorMessage // 添加错误信息
                )
            }

            else -> {
                // 默认为加载状态
                VoiceFilterPreviewState.Loading(
                    duration = duration, // 传递正确的语音时长
                    isOnSendAnim = isOnSendAnim,
                    sendAnimTranslationY = adjustAnimationForSource(
                        sendAnimTranslationY,
                        sourceType
                    ),
                    voiceFilter = voiceFilter,
                    offsetY = offsetY,
                    notClickCancelOp = notClickCancelOp,
                    isPlaying = isPlaying
                )
            }
        }
    }

    /**
     * 根据来源调整动画效果
     */
    private fun adjustAnimationForSource(
        originalTranslationY: Int,
        sourceType: SourceType,
    ): Int {
        return when (sourceType) {
            SourceType.HOME -> originalTranslationY // 首页来源
            SourceType.CHAT_LIST -> originalTranslationY  // 聊天列表来源 - 聊天列表需要额外偏移
        }
    }


    data class ShareMediaUIState(val state: State = State.NONE) {
        enum class State {
            NONE,
            LOADING,
        }
    }

    private val _shareUiState = MutableStateFlow<ShareMediaUIState>(ShareMediaUIState())
    val shareUiState = _shareUiState.asStateFlow()

    // 用于存储待处理的分享请求
    private var pendingShareRequest: ShareItemBean? = null

    // 用于取消分享状态监听的 Job
    private var shareStateObserverJob: Job? = null

    /**
     * 预览分享到外部平台
     * 当过滤状态为 FilterIng 时，会显示 loading 状态并等待过滤完成
     * 当过滤状态变化时，会自动重新执行分享逻辑
     */
    fun onPreviewShareOuter(bean: ShareItemBean) =
        viewModelScope.launch {
            val currentFilterState = _filterState.value

            // 取消之前的状态监听
            shareStateObserverJob?.cancel()

            // 存储当前分享请求
            pendingShareRequest = bean

            when (currentFilterState) {
                FilterIng -> {
                    // 过滤进行中，显示 loading 状态并开始监听状态变化
                    _shareUiState.emit(ShareMediaUIState(state = ShareMediaUIState.State.LOADING))
                    startObservingFilterStateForShare()
                }

                FilterFail -> {
                    shareFail()
                }

                FilterSuccess -> {
                    // 过滤成功，执行分享逻辑
                    executeShare(bean)
                }
            }
        }

    private suspend fun shareFail() {
        // 过滤失败，清除 loading 状态并提示失败
        _shareUiState.emit(ShareMediaUIState(state = ShareMediaUIState.State.NONE))
        toast(R.string.share_fail)
        clearPendingShareRequest()
    }

    /**
     * 开始监听过滤状态变化，用于自动重新执行分享
     */
    private fun startObservingFilterStateForShare() {
        shareStateObserverJob = viewModelScope.launch {
            _filterState.collect { filterState ->
                logInfo(
                    TAG,
                    "startObservingFilterStateForShare: filterState changed to $filterState"
                )
                val request = pendingShareRequest
                if (request != null) {
                    when (filterState) {
                        FilterFail -> {
                            shareFail()
                        }

                        FilterSuccess -> {
                            // 过滤成功，执行分享逻辑
                            executeShare(request)
                            clearPendingShareRequest()
                        }
                        // FilterIng 状态继续等待
                    }
                }
            }
        }
    }


    private fun reportVideoResult(isSuccess: Boolean, rCode: String?, stopReason: String?) {
        com.interfun.buz.domain.record.helper.TraceUtils.resultBackRB2025061901(
            chatType = voiceIMParams.convType.trackerString,
            chatId = voiceIMParams.targetId.toString(),
            source = "voice_filter_preview",
            eventId = "${_voiceFilter.value?.campaignConfig?.id ?: ""}",
            voiceFilterId = "${voiceIMParams.voiceFilterId ?: ""}",
            isSuccess = isSuccess,
            stopReason = stopReason,
            failReason = rCode
        )
    }

    /**
     * 执行实际的分享逻辑
     */
    private suspend fun executeShare(
        shareType: ShareItemBean,
    ) {
        _shareUiState.emit(ShareMediaUIState(state = ShareMediaUIState.State.LOADING))
        try {
            // 模板信息
            val currentVoiceFilter = _voiceFilter.value
            val videoTemplateUrl = currentVoiceFilter?.videoTemplate?.videoTemplateUrl
            val videoTemplateMd5 = currentVoiceFilter?.videoTemplate?.videoTemplateMd5
            if (videoTemplateMd5.isNullOrEmpty() || videoTemplateUrl.isNullOrEmpty()) {
                _shareUiState.emit(ShareMediaUIState(state = ShareMediaUIState.State.NONE))
                showFailureToast(shareType.type.type)
                return
            }
            val audioUrl = audioUrlStateFlow.value
            if (audioUrl.isNullOrEmpty()) {
                _shareUiState.emit(ShareMediaUIState(state = ShareMediaUIState.State.NONE))
                showFailureToast(shareType.type.type)
                return
            }
            val voiceFilterMessagePreview = VoiceFilterMessagePreview(application)
            voiceFilterMessagePreview.initPreview(
                VoiceFilterMessagePreviewType.EXPORT_VIDEO,
                _duration.value.voiceTimeStr(),
                currentVoiceFilter.filterName,
                currentVoiceFilter.bubbleStyle,
                _audioASRStateFlow.value,
                currentVoiceFilter.backgroundColor.toArgb(),
                currentVoiceFilter.microphoneColor.toArgb()
            )
            voiceFilterMessagePreview.autoMeasure()
            voiceFilterMessagePreview.waitForImageLoad()
            val file = vfVideoGeneration.generateVideo(
                videoTemplateUrl,
                videoTemplateMd5,
                _duration.value,
                audioUrl,
                voiceFilterMessagePreview
            )
            if (!file.exists() || file.length() <= 0) {
                showFailureToast(shareType.type.type)
                return
            }
            reportVideoResult(true, null, null)
            handleShareResult(shareType, file)
        } catch (e: Exception) {
            logError(TAG, "executeShare failed", e)
            handleShareException(shareType.type.type, e)
        } finally {
            _shareUiState.emit(ShareMediaUIState(state = ShareMediaUIState.State.NONE))
        }
    }

    /**
     * 处理分享结果
     */
    private suspend fun handleShareResult(shareType: ShareItemBean, file: File) {
        // 统一通过 ShareFlow 通知 UI 层处理分享操作（包括下载）
        _shareEventFlow.emit(ShareEvent(shareType, file))
        logInfo(TAG, "generateVideo success: ${file.absolutePath}")
    }

    /**
     * 显示失败提示 Toast
     */
    private fun showFailureToast(shareType: Int) {
        val messageRes = if (shareType == ShareType.DOWN_LOAD.type) {
            R.string.failed_to_save
        } else {
            R.string.share_fail
        }
        toast(messageRes)
    }



    /**
     * 处理分享异常
     */
    private fun handleShareException(shareType: Int, exception: Exception) {
        if (exception is CancellationException) {
            showCancellationToast(shareType)
        } else {
            showFailureToast(shareType)
        }

        val stopReason = if (exception is CancellationException) {
            when {
                clickCancel -> "cancel"
                clickSend -> "send"
                else -> "others"
            }
        } else {
            "others"
        }

        reportVideoResult(false, exception.message ?: "", stopReason = stopReason)
    }

    /**
     * 显示取消操作提示 Toast
     */
    private fun showCancellationToast(shareType: Int) {
        val messageRes = if (shareType == ShareType.DOWN_LOAD.type) {
            R.string.download_canceled
        } else {
            R.string.share_canceled
        }
        toast(messageRes)
    }

    fun onResultShareVoiceFilterVideo(shareType: ShareType, isSuccess: Boolean, failReason: String?){
        TraceUtils.onResultShareVoiceFilterVideo(
            voiceIMParams.convType == IM5ConversationType.PRIVATE,
            voiceIMParams.targetId,
            "voice_filter_preview",
            isSuccess,
            shareType,
            voiceIMParams.voiceFilterId,
            failReason = failReason,
            eventId = "${_voiceFilter.value?.campaignConfig?.id ?: ""}"
        )
    }

    /**
     * 清除待处理的分享请求
     */
    private fun clearPendingShareRequest() {
        pendingShareRequest = null
        shareStateObserverJob?.cancel()
        shareStateObserverJob = null
    }

    /**
     * 简化的音频文件删除函数
     * 在子线程中执行文件删除操作，不返回结果，确保不会崩溃
     *
     * @param filePath 要删除的文件路径
     */
    private fun deleteAudioFile(filePath: String?) {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                if (!filePath.isNullOrEmpty()) {
                    val file = java.io.File(filePath)
                    if (file.exists()) {
                        file.delete()
                        logInfo(TAG, "deleteAudioFile: deleted file: $filePath")
                    }
                }
            } catch (e: Exception) {
                logError(TAG, e, "deleteAudioFile: failed to delete file: $filePath")
            }
        }
    }

    /**
     * 取消预览时清理音频文件
     */
    private fun cleanupAudioFileOnCancel() {
        deleteAudioFile(voiceIMParams.aacPath)
    }
}