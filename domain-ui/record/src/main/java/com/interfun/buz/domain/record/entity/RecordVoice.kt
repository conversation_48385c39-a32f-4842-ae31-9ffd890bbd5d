package com.interfun.buz.domain.record.entity

import com.interfun.buz.core.widget_record.state.VoiceFilterUiData

/**
 * <AUTHOR>
 * @date 2024/11/14
 * @desc
 */
sealed interface RecordStatusType {
    object Default : RecordStatusType
    data class Pressed(val voiceFilter: VoiceFilterUiData? = null) : RecordStatusType
    data class Locked(val voiceFilter: VoiceFilterUiData? = null) : RecordStatusType
    data class Release(
        val reason: ReleaseReason = ReleaseReason.NORMAL,
        val voiceFilter: VoiceFilterUiData? = null,
    ) : RecordStatusType
}

enum class RecordBtnEnableType {
    Enable,
    DisableInAddBtn,
    DisableUnSpeakable,
    DisableIsPlaying,
    DisableIsWTOff,
    DisableIdle,
}

enum class RecordBgType{
    Normal,
    Robot,
    AddressBot,
}

enum class ReleaseReason {
    NORMAL,
    USER_CANCEL,
    USER_PREVIEW,
    // 上面三个是用户正常操作，下面的都是异常操作
    ON_BACK_PRESS,
    SILENT,
    WARN_SILENT,
    APP_BACKGROUND, // 录制中退到后台
    ON_DISPOSE,
}