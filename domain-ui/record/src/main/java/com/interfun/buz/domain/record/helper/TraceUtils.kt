package com.interfun.buz.domain.record.helper

import android.text.SpannableString
import android.text.style.URLSpan
import android.text.util.Linkify
import com.interfun.buz.common.constants.TrackConstant
import com.interfun.buz.common.ktx.trackString
import com.interfun.buz.common.utils.BuzTracker
import com.yibasan.lizhifm.lzlogan.Logz
import com.yibasan.lizhifm.rtcdorime.xapm.Metrics
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import java.io.File
import java.net.URI

object TraceUtils {
    /**
     * @param source 1:首页 2:悬浮窗 3:历史聊天
     */
    fun voiceFileInfo(source: String, traceId: String, duration: Long, path: String) {
        GlobalScope.launch {
            val file = File(path)
            val fileLength = if (file.exists()) {
                file.length()
            } else {
                -1
            }
            BuzTracker.onTechTrack {
                put(TrackConstant.KEY_EXCLUSIVE_ID, "TT2024041801")
                put(TrackConstant.KEY_EVENT_NAME, "SentVoiceFileInfo")
                put(TrackConstant.KEY_CONTENT_1, traceId)
                put(TrackConstant.KEY_CONTENT_2, source)
                put(TrackConstant.KEY_NUMBER_1, fileLength)
                put(TrackConstant.KEY_NUMBER_2, duration)
            }
        }
    }

    /**
     * @param source 1:首页 2:悬浮窗 3:历史聊天
     */
    fun receiveVoiceFileInfo(
        source: String,
        traceId: String,
        duration: Long,
        path: String
    ) {
        GlobalScope.launch {
            //不探测网络文件
            if (path.startsWith("http", true)) {
                return@launch
            }
            //非内嵌消息uri返回
            if (!path.startsWith("file:", true)) {
                return@launch
            }

            try {
                val file = File(URI(path))
                val fileLength = if (file.exists()) {
                    file.length()
                } else {
                    -1
                }
                BuzTracker.onTechTrack {
                    put(TrackConstant.KEY_EXCLUSIVE_ID, "TT2024041802")
                    put(TrackConstant.KEY_EVENT_NAME, "ReceviceVoiceFileInfo")
                    put(TrackConstant.KEY_CONTENT_1, traceId)
                    put(TrackConstant.KEY_CONTENT_2, source)
                    put(TrackConstant.KEY_NUMBER_1, fileLength)
                    put(TrackConstant.KEY_NUMBER_2, duration)
                }
            } catch (e: Exception) {
                Logz.Companion.e(e)
            }
        }
    }

    fun reportMsgContentLink(msg: String) {
        try {
            val spanBuilder = SpannableString(msg)
            Linkify.addLinks(spanBuilder, Linkify.WEB_URLS)
            val urlSpans = spanBuilder.getSpans(0, spanBuilder.length, URLSpan::class.java)
            val hasLink = urlSpans.isNotEmpty()
            BuzTracker.onTechTrack {
                put(TrackConstant.KEY_EXCLUSIVE_ID, "TT2024061701")
                put(TrackConstant.KEY_EVENT_NAME, "reportMsgContentLink")
                put(TrackConstant.KEY_CONTENT_1, if (hasLink) "1" else "0")
            }
        } catch (e: Exception) {
            Logz.Companion.e(e)
        }
    }

    fun reportReduceNoiseData(traceId: String, audioProcessMetrics: Metrics?) {
        if (audioProcessMetrics == null) {
            return
        }
        BuzTracker.onTechTrack {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "TT2025052701")
            put(TrackConstant.KEY_EVENT_NAME, "NoiseReduction")
            put(TrackConstant.KEY_CONTENT_1, traceId)
            put(TrackConstant.KEY_CONTENT_2, audioProcessMetrics.captureVolumeDb)
            put(TrackConstant.KEY_CONTENT_3, audioProcessMetrics.captureBackgroundNoiseDb)
            put(TrackConstant.KEY_CONTENT_4, audioProcessMetrics.processedVolumeDb)
            put(TrackConstant.KEY_CONTENT_5, audioProcessMetrics.processedBackgroundNoiseDb)
        }
    }

    /**
     * @param source
     * source const of:
     * voice_filter_preview
     * export_voice
     * @param chatType
     * chat type const of:
     * private:private chat
     * group:group chat
     */
    fun resultBackRB2025061901(
        chatType: String,
        chatId: String,
        source: String,
        voiceFilterId: String,
        isSuccess: Boolean,
        eventId: String? = null,
        stopReason: String? = null,
        failReason: String? = null
    ) {

        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2025061901")
            put(TrackConstant.KEY_RESULT_TYPE, "voice_filter_video_generation_result")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, chatType)
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, chatId)
            put(TrackConstant.KEY_SOURCE, source)
            if (eventId.isNullOrEmpty().not()) {
                put(TrackConstant.KEY_CONTENT_ID, eventId!!)
            }
            put(TrackConstant.KEY_IS_SUCCESS, isSuccess.trackString)
            if (stopReason.isNullOrEmpty().not()) {
                put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, stopReason!!)
            }
            put(TrackConstant.KEY_ELEMENT_BUSINESS_ID, voiceFilterId)
            if (failReason.isNullOrEmpty().not()) {
                put(TrackConstant.KEY_FAIL_REASON, failReason!!)
            }
        }
    }
}