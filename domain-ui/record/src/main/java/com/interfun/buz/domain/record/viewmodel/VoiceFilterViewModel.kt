package com.interfun.buz.domain.record.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.buz.idl.bot.bean.VoiceFilterTabItem
import com.interfun.buz.base.ktx.*
import com.interfun.buz.biz.center.voicefilter.repository.VoiceFilterDataRepository
import com.interfun.buz.biz.center.voicefilter.repository.VoiceFilterPlayRepository
import com.interfun.buz.biz.center.voicefilter.repository.VoiceFilterStatusRepository
import com.interfun.buz.common.bean.LoadingState
import com.interfun.buz.common.constants.CommonMMKV
import com.interfun.buz.common.manager.AppConfigRequestManager
import com.interfun.buz.compose.components.TabOption
import com.interfun.buz.core.widget_record.state.*
import com.interfun.buz.domain.record.entity.OpenVoiceFilterType
import com.interfun.buz.user.repository.UserSettingRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.collections.immutable.persistentListOf
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class VoiceFilterViewModel @Inject constructor(
    private val dataRepo: VoiceFilterDataRepository,
    private val statusRepo: VoiceFilterStatusRepository,
    private val playRepo: VoiceFilterPlayRepository,
    private val userSettingRepo: UserSettingRepository
) : ViewModel() {

    companion object {
        const val TAG = "VoiceFilterViewModel"
    }



    /**-----------------⬇️ 获取滤镜Data ⬇️️------------------**/

    // map出全部Tab+对应的滤镜列表的数据
    private val combineTabListFlow = dataRepo.getVoiceFilterOriginData().map {
        logInfo(TAG, "tabUiDataMapFlow tabSize: ${it.voiceFilterTabList.size} listSize: ${it.voiceFilterList.size}")
        val tabPairList = it.voiceFilterTabList.mapNotNull { tab ->
            if (tab.type != null && tab.typeName != null) {
                tab.type!! to tab.typeName!!
            } else {
                null
            }
        }
        val allVoiceFilterList = it.voiceFilterList
        if (tabPairList.isEmpty()) {
            // 当tabList为空时，返回只包含一个默认VoiceFilterTabUiData的列表
            listOf(
                VoiceFilterTabUiData.default().copy(
                    voiceFilterList = mutableListOf<VoiceFilterUiData>().apply {
                        add(VoiceFilterUiData.NoFilter)
                        addAll(allVoiceFilterList.mapNotNull { it.toVoiceFilterUiData() })
                    }
                )
            )
        } else {
            tabPairList.mapNotNull { (tabType, tabName) ->
                val voiceFilterList = mutableListOf<VoiceFilterUiData>().apply {
                    add(VoiceFilterUiData.NoFilter)
                }

                // 如果是All tab，则返回所有滤镜
                if (tabType == TabOption.allOptionTab.type) {
                    allVoiceFilterList.mapNotNull {
                        it.toVoiceFilterUiData()
                    }.let {
                        voiceFilterList.addAll(it)
                    }
                } else {
                    // 筛选出type等于当前tabType的滤镜
                    val filteredUiDataList = allVoiceFilterList.filter {
                        it.type == tabType
                    }.mapNotNull {
                        it.toVoiceFilterUiData()
                    }

                    if (filteredUiDataList.isEmpty()) {
                        return@mapNotNull null
                    }
                    voiceFilterList.addAll(filteredUiDataList)
                }

                VoiceFilterTabUiData(
                    tabType = tabType,
                    tabName = tabName,
                    voiceFilterList = voiceFilterList,
                )
            }
        }
    }

    // 根据当前选中tab和滤镜 提供ui所需的uiState
    val voiceFilterUiStateFlow = combine(
        combineTabListFlow,
        statusRepo.currentSelectTabTypeFlow,
        statusRepo.tabSelectedVoiceFilterIdMapFlow,
    ) { tabList, currentSelectTabType, tabSelectedVoiceFilterIdMap ->
        val allTabOptionList = tabList.map { TabOption(it.tabType, it.tabName) }
        if (currentSelectTabType == null) {
            val firstTabType = tabList.firstOrNull()?.tabType ?: TabOption.allOptionTab.type
            updateSelectTabType(firstTabType)
            logInfo(TAG, "initFirstTabType: $firstTabType")
            return@combine VoiceFilterUiState.Empty
        }
        val selectedTab = tabList.firstOrNull { it.tabType == currentSelectTabType }
        val selectedVoiceFilterId = tabSelectedVoiceFilterIdMap[currentSelectTabType]
        logInfo(
            TAG, "voiceFilterUiStateFlow " +
                "tabList.size: ${tabList.size} " +
                "currentSelectTabType: $currentSelectTabType " +
                "selectedVoiceFilterId: $selectedVoiceFilterId "
        )
        VoiceFilterUiState(
            isShowTabs = allTabOptionList.size > 1,
            tabOptionList = allTabOptionList,
            currentSelectTabType = currentSelectTabType,
            currentSelectVoiceFilter = selectedTab?.voiceFilterList?.firstOrNull { it.filterId == selectedVoiceFilterId },
            voiceFilterList = selectedTab?.voiceFilterList ?: listOf(VoiceFilterUiData.NoFilter)
        )
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.Companion.Eagerly,
        initialValue = VoiceFilterUiState.Empty
    )

    val voiceFilterListLoadStatusFlow = dataRepo.voiceFilterListLoadStatusFlow
    val isVoiceFilterLoadingFlow = dataRepo.voiceFilterListLoadStatusFlow.map {
        it == LoadingState.Loading
    }

    suspend fun syncFromServer() {
        dataRepo.syncVoiceFilterList()
    }

    suspend fun getVoiceFilterInfoById(voiceFilterId: Long) =
        dataRepo.getCachedVoiceFilterInfoById(voiceFilterId)

    suspend fun getVoiceFilterTabs(): List<VoiceFilterTabItem> {
        return dataRepo.getVoiceFilterOriginData()
            .map { it.voiceFilterTabList }
            .firstOrNull() ?: emptyList()
    }

    /**-----------------⬆️️ 获取滤镜Data ⬆️------------------**/








    /**-----------------⬇️ 切换滤镜模式开关 ⬇️------------------**/

    val isVoiceFilterModeStateFlow = statusRepo.isVoiceFilterModeStateFlow

    // 这个 Flow 受数据驱动，当其为 ture 时代表已经准备好可以切换到 VF 模式了
    // 但实际需求是还需要等待 more 面板关闭后才真正开始展示 VF，所以这里只抛出去给面板调用处让其关闭面板先
    val readyToSwitchVoiceFilterModeFlow = combine(
        dataRepo.getVoiceFilterOriginData(),
        statusRepo.hasClickedSwitchVoiceFilterModeFlow
    ) { data, (hasClickedEntry, clickedFrom) ->
        logInfo(
            TAG,
            "${this.hashCode()}-switchVoiceFilterModeFlow, data list.size: ${data.voiceFilterList.size} " +
                "hasClickedEntry: $hasClickedEntry, clickedFrom: $clickedFrom"
        )
        (data.voiceFilterList.isNotEmpty()) && hasClickedEntry
    }

    // 等待之前的动画结束，比如之前的 panel 先关闭，再切换到滤镜模式
    private val _waitTillPreviousPageClosedFlow = MutableStateFlow(false)
    val waitTillPreviousAnimFinishedFlow = _waitTillPreviousPageClosedFlow.asStateFlow()

    init {
        combine(
            readyToSwitchVoiceFilterModeFlow,
            waitTillPreviousAnimFinishedFlow
        ) { switchTo, waitTillPreviousPageClosed ->
            logInfo(
                TAG, "${this.hashCode()}-updateVoiceFilterMode, switchTo: $switchTo " +
                    "waitTillPreviousPageClosed: $waitTillPreviousPageClosed"
            )
            switchTo && !waitTillPreviousPageClosed
        }.collectLatestIn(viewModelScope) {
            logInfo(TAG, "${this.hashCode()}-updateVoiceFilterMode: $it")
            statusRepo.updateVoiceFilterMode(it)
        }
    }

    fun isInVoiceFilterMode() = isVoiceFilterModeStateFlow.value

    fun updateWaitTillPreviousPageClosed(wait: Boolean) {
        _waitTillPreviousPageClosedFlow.emitInScope(viewModelScope, wait)
    }

    fun onClickOpenVoiceFilterMode(from: OpenVoiceFilterType) = launch {
        statusRepo.updateHasClickedSwitchVoiceFilterMode(true, from.source)
        dataRepo.syncVoiceFilterList()
    }

    fun onClickCloseVoiceFilterMode() = launch {
        statusRepo.updateHasClickedSwitchVoiceFilterMode(false, null)
    }

    /**-----------------⬆️️ 切换滤镜模式开关 ⬆️------------------**/







    /**-----------------⬇️ 当前选中滤镜或Tab ⬇️------------------**/

    val currentSelectTabTypeFlow = voiceFilterUiStateFlow.map {
        it.currentSelectTabType
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.Companion.Eagerly,
        initialValue = TabOption.allOptionTab.type
    )

    val currentSelectVoiceFilterIdFlow = voiceFilterUiStateFlow.map {
        it.currentSelectVoiceFilter?.filterId
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.Companion.Eagerly,
        initialValue = null
    )

    val currentValuableSelectVoiceFilterIdFlow = combine(
        isVoiceFilterModeStateFlow,
        voiceFilterUiStateFlow.map { it.currentSelectVoiceFilter?.filterId },
    ) { isInVoiceFilterMode, currentSelectVoiceFilterId ->
        currentSelectVoiceFilterId?.takeIf {
            isInVoiceFilterMode && it != VoiceFilterUiData.NO_FILTER_ID
        }
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.Companion.Eagerly,
        initialValue = null
    )

    val currentSelectVoiceFilterUiDataFlow = voiceFilterUiStateFlow.map {
        it.currentSelectVoiceFilter
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.Companion.Eagerly,
        initialValue = null
    )

    val hasClickedSwitchVoiceFilterModeFlow = statusRepo.hasClickedSwitchVoiceFilterModeFlow

    init {
        viewModelScope.launch {
            // 初始化时 currentSelectVoiceFilterIdFlow 的值为null，此时默认应该指向列表中第0个的NoFilter
            // 但当list数据加载出来后，要去读取第一个有campaignConfig信息的滤镜然后再指向它
            combine(
                isVoiceFilterModeStateFlow,
                voiceFilterUiStateFlow,
            ) { inVoiceFilterMode, uiState ->
                if (!inVoiceFilterMode) return@combine null
                var shouldChooseFirstCampaignFilterId: Long? = null
                if (uiState.currentSelectVoiceFilter == null) {
                    if (uiState.currentSelectTabType != TabOption.allOptionTab.type) {
                        shouldChooseFirstCampaignFilterId =
                            uiState.voiceFilterList.firstOrNull {
                                it.campaignConfig != null
                            }?.filterId
                    }
                }
                return@combine if (shouldChooseFirstCampaignFilterId != null) {
                    uiState.currentSelectTabType to shouldChooseFirstCampaignFilterId
                } else {
                    null
                }
            }.filterNotNull().collect { (tabType, shouldChooseFirstCampaignFilterId) ->
                logInfo(TAG, "initFirstSelectionFilterId: $shouldChooseFirstCampaignFilterId tabType: $tabType")
                statusRepo.updateSelectTabType(tabType)
                statusRepo.updateTabSelectedVoiceFilterId(
                    tabType = tabType,
                    voiceFilterId = shouldChooseFirstCampaignFilterId
                )
            }
        }
    }

    fun updateSelectTabType(type: Int) = viewModelScope.launch {
        logInfo(TAG, "updateSelectTabType:type = $type")
        statusRepo.updateSelectTabType(tabType = type)
    }

    fun updateSelectVoiceFilter(tabType: Int, voiceFilterId: Long) = viewModelScope.launch {
        // 为当前tab更新选中的滤镜，如果没有tab则使用默认tab(-1)
        statusRepo.updateTabSelectedVoiceFilterId(tabType, voiceFilterId)
        if (voiceFilterId != VoiceFilterUiData.NO_FILTER_ID) {
            statusRepo.updateUserIsScrolled(true)
        }
    }

    fun getCurrentSelectTabType() = currentSelectTabTypeFlow.value

    /**
     * 注意：当处于非滤镜模式下时，通过此方法仍能获取到之前选中的滤镜ID，并且NoFilter对应的-1也会返回
     * 如果你需要判断只有在滤镜模式下才返回有意义的滤镜ID，那么请使用 getValuableCurrentSelectVoiceFilterId()
     * **/
    fun getCurrentSelectVoiceFilterId() =
        currentSelectVoiceFilterIdFlow.value ?: VoiceFilterUiData.NO_FILTER_ID

    /**
     * 只有在滤镜模式下才并且不是NoFilter时才返回有意义的滤镜ID
     * **/
    fun getCurrentValuableSelectVoiceFilterId() = currentValuableSelectVoiceFilterIdFlow.value

    fun getCurrentSelectVoiceFilterUiData() =
        currentSelectVoiceFilterUiDataFlow.value ?: VoiceFilterUiData.NoFilter

    /**-----------------⬇️️ 当前选中滤镜或Tab ⬇️------------------**/







    /**-----------------⬇️️ 其它杂项 ⬇️------------------**/

    val isVoiceFilterScrollingStateFlow = statusRepo.isVoiceFilterScrollingStateFlow

    private val _voiceFilterLatestTimestampList = dataRepo
        .getVFLatestTimestamp()
        .stateIn(
            viewModelScope,
            SharingStarted.Eagerly,
            persistentListOf()
        )
    val voiceFilterShowBadgeMapFlow: StateFlow<Map<Long, Boolean>> = combine(
        _voiceFilterLatestTimestampList,
        combineTabListFlow
    ) { timestampList, uiDataMap ->
        val voiceFilterList = uiDataMap.flatMap { it.voiceFilterList }
        val latestUpdatedTimestamp = voiceFilterList.maxOfOrNull { it.latestTimestamp }
        voiceFilterList.associate {
            val showBadge = it.hasUpdated(timestampList, latestUpdatedTimestamp)
            it.filterId to showBadge
        }
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.Companion.Eagerly,
        initialValue = emptyMap<Long, Boolean>()
    )

    val isVoiceFilterPreviewPlayingFlow = playRepo.isVoiceFilterPreviewPlayingFlow

    /**
     * 红点状态：用于多功能面板滤镜选项，和录音面板右下角滤镜入口按钮
     * 执行 updateVFEntryRedDotStatus，这里 flow 的状态就会变更
     */
    val showRedDOtOnVFEntry = getVFLatestTabTimestamp().map {
        logInfo(TAG, "showRedDOtOnVFEntry? local=${CommonMMKV.voiceFilterLatestTabNotifyTimestamp}, remote=${AppConfigRequestManager.voiceFilterLatestTimestamp}")
        val local = CommonMMKV.voiceFilterLatestTabNotifyTimestamp
        local > 0 && AppConfigRequestManager.voiceFilterLatestTimestamp > local
    }

    fun stopFilterSoundUrl() = viewModelScope.launch {
        playRepo.stopFilterSound()
    }

    val isQuietModeEnable = userSettingRepo.getQuiteMode().map { it.enable }
        .stateIn(viewModelScope, SharingStarted.WhileSubscribed(5000L), false)

    fun previewVoiceFilter(from: Int) = launchIO {
        if (isQuietModeEnable.value) return@launchIO
        getCurrentSelectVoiceFilterUiData().filterSoundUrl?.let {
            if (playRepo.isVoiceFilterPreviewPlayingFlow.value) {
                playRepo.stopFilterSound()
            } else {
                playRepo.playFilterSound(it, from)
            }
        }
    }

    /**
     * 更新滤镜滚动状态
     */
    fun updateVoiceFilterScrolling(isScrolling: Boolean) = viewModelScope.launch {
        statusRepo.updateVoiceFilterScrolling(isScrolling)
    }

    /**
     * 更新+号入口红点状态
     */
    fun updateVFLatestEntryForMoreButtonTimestamp() = launch {
        dataRepo.updateVFLatestEntryForMoreButtonTimestamp()
    }

    /**
     * 更新语音滤镜入口红点状态
     */
    fun updateVFEntryRedDotStatus() = launch {
        dataRepo.updateVFEntryRedDotStatus()
    }

    fun updateVFLatestTimestamp(filterId: Long?, latestTimestamp: Int?) {
        if (null != filterId && filterId != VoiceFilterUiData.NO_FILTER_ID && latestTimestamp != null) {
            viewModelScope.launch {
                dataRepo.updateVFLatestTimestamp(
                    filterId = filterId,
                    timestamp = latestTimestamp
                )
//            _voiceFilterLatestTimestampList.emit(voiceFilterRepository.getVFLatestTimestamp())
            }
        }
    }

    fun getVFLatestEntryTimestamp(): StateFlow<Int> {
        return dataRepo.getVFLatestEntryNotifyTimestamp()
    }

    fun getVFLatestTabTimestamp(): StateFlow<Int> {
        return dataRepo.getVFLatestTabTimestamp()
    }
}