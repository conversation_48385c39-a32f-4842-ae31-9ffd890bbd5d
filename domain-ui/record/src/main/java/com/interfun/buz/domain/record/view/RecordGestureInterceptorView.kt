package com.interfun.buz.domain.record.view

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import com.interfun.buz.domain.record.helper.RecordStatusHelper

/**
 * <AUTHOR>
 * @date 2025/5/22
 * @desc
 */
class RecordGestureInterceptorView@JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        // 只在按下的时候拦截，而不是判断isRecording（因为可能是locking状态）
        if (RecordStatusHelper.isRecording && RecordStatusHelper.isPressing) return true
        return super.dispatchTouchEvent(ev)
    }
}