package com.interfun.buz.domain.record.ui

import android.content.pm.PackageManager
import android.graphics.drawable.ColorDrawable
import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.layout.LayoutCoordinates
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.positionOnScreen
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.fragment.app.viewModels
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.lifecycleScope
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.R
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.constants.CommonMMKV
import com.interfun.buz.common.manager.AppConfigRequestManager
import com.interfun.buz.common.service.ChatService
import com.interfun.buz.common.utils.saveVideoInGallery
import com.interfun.buz.compose.components.CommonLoadingDialog
import com.interfun.buz.compose.components.dialog.BaseComposeDialogFragment
import com.interfun.buz.compose.ktx.VerticalSpace
import com.interfun.buz.compose.ktx.pxToDp
import com.interfun.buz.compose.styles.TextStyles
import com.interfun.buz.core.widget_record.state.VoiceFilterUiData.PreviewType
import com.interfun.buz.core.widget_record.ui.*
import com.interfun.buz.core.widget_record.ui.VoicePreviewSource.HOME
import com.interfun.buz.domain.im.social.entity.ShareType
import com.interfun.buz.domain.record.callback.VoicePreviewCallback
import com.interfun.buz.domain.record.entity.RecordPreviewMsgParam
import com.interfun.buz.domain.record.viewmodel.ShareEvent
import com.interfun.buz.domain.record.viewmodel.VoicePreviewViewModel
import com.interfun.buz.domain.record.viewmodel.VoicePreviewViewModel.ShareMediaUIState
import com.interfun.buz.domain.record.viewmodel.VoicePreviewViewModel.SourceType
import com.interfun.buz.domain.social.helper.ShareHelper
import com.interfun.buz.im.entity.FilterIng
import com.interfun.buz.im.entity.IMSendFrom
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.collections.immutable.persistentListOf
import kotlinx.coroutines.launch

//
//.background(
//brush = Brush.verticalGradient(
//colors = listOf(
//colorResource(R.color.black_70), // 70% alpha
//colorResource(R.color.black_96)  // 96% alpha
//)
//)
//)
@Composable
private fun Modifier.backgroundWithAlpha(): Modifier {
    return this.then(Modifier.background(
        brush = Brush.verticalGradient(
            colors = listOf(
                colorResource(R.color.black_70), // 70% alpha
                colorResource(R.color.black_96)  // 96% alpha
            )
        )
    ))
}

/**
 * 通用语音滤镜预览对话框
 * 支持多种来源场景，提供统一的预览和分享功能
 */
@AndroidEntryPoint
class PreviewVoiceFilterNewlyDialog : BaseComposeDialogFragment() {
    private val TAG = "PreviewVoiceFilterNewlyDialog"
    private val viewModel by viewModels<VoicePreviewViewModel>()

    private val fromSource: SourceType by lazy {
        SourceType.fromValue(
            requireArguments().getInt(KEY_SOURCE_TYPE, SourceType.HOME.value)
        )
    }

    // 回调接口，用于直接回调而不是通过 ViewModel
    private var callback: VoicePreviewCallback? = null

    // 待处理的下载事件
    private var pendingDownloadEvent: ShareEvent? = null

    // 权限申请器
    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        pendingDownloadEvent?.let { shareEvent ->
            if (isGranted) {
                // 权限已授予，执行下载
                executeDownload(shareEvent)
            } else {
                // 权限被拒绝，判断拒绝类型并显示相应提示
                handlePermissionDenied()
                logInfo(TAG, "Storage permission denied")
                viewModel.onResultShareVoiceFilterVideo(ShareType.DOWN_LOAD, false, "Permission denied")
            }
            pendingDownloadEvent = null
        }
    }

    companion object {
        const val KEY_VOICE_IM_PARAMS = "KEY_VOICE_IM_PARAMS"
        const val KEY_GRAVITY_OFFSET = "KEY_GRAVITY_OFFSET"
        const val KEY_TRANSLATION_Y = "KEY_TRANSLATION_Y"
        const val KEY_SOURCE_TYPE = "KEY_SOURCE_TYPE"

        /**
         * 创建新实例
         * @param params 录音预览消息参数
         * @param offsetY Y轴偏移量
         * @param sendAnimTranslationY 发送动画Y轴偏移量
         * @param sourceType 来源类型，使用 SourceType 枚举
         * @param callback 回调接口，用于处理预览操作
         */
        fun newInstance(
            params: RecordPreviewMsgParam,
            offsetY: Int = 90.buzDp,
            sendAnimTranslationY: Int = 0,
            sourceType: SourceType = SourceType.HOME,
            callback: VoicePreviewCallback? = null,
        ): PreviewVoiceFilterNewlyDialog {
            val bundle = Bundle()
            bundle.putParcelable(KEY_VOICE_IM_PARAMS, params)
            bundle.putInt(KEY_GRAVITY_OFFSET, offsetY)
            bundle.putInt(KEY_TRANSLATION_Y, sendAnimTranslationY)
            bundle.putInt(KEY_SOURCE_TYPE, sourceType.value)
            val dialog = PreviewVoiceFilterNewlyDialog()
            dialog.arguments = bundle
            dialog.callback = callback
            return dialog
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
         routerServices<ChatService>().value?.setChatPreviewVF(true)

    }

    override fun onDestroy() {
        super.onDestroy()
        routerServices<ChatService>().value?.setChatPreviewVF(false)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        val voiceIMParams = arguments?.getParcelable(KEY_VOICE_IM_PARAMS) as RecordPreviewMsgParam?
        val offsetY = arguments?.getInt(KEY_GRAVITY_OFFSET)
        val sendAnimTranslationY = arguments?.getInt(KEY_TRANSLATION_Y)
        val sourceType = fromSource

        if (voiceIMParams != null) {
            // 根据来源类型确定页面类型
            val pageType = when (sourceType) {
                SourceType.HOME -> IMSendFrom.HOME
                SourceType.CHAT_LIST -> IMSendFrom.CHAT_LIST
            }

            viewModel.initPreviewUiState(
                params = voiceIMParams,
                pageType = pageType,
                offsetY = offsetY,
                sendAnimTranslationY = sendAnimTranslationY,
                sourceType = sourceType
            )
        }

        logInfo(
            TAG,
            "onCreateView: sourceType = $sourceType, viewModel = $viewModel"
        )
        CommonMMKV.vfHadShowPreviewAvailableToolTip = true
        return super.onCreateView(inflater, container, savedInstanceState)
    }

    @Composable
    override fun DialogContent() {
        // 使用统一的VoiceFilterPreviewState进行状态管理
        val voiceFilterPreviewState =
            viewModel.voiceFilterPreviewState.collectAsStateWithLifecycle()
        val sendTitleTipState = viewModel.showSendTipFlow.collectAsStateWithLifecycle()
        val shareUiState = viewModel.shareUiState.collectAsStateWithLifecycle()
        val playProgressState = viewModel.playProgress.collectAsStateWithLifecycle()
        val sourceType = fromSource


        if (shareUiState.value.state == ShareMediaUIState.State.LOADING) {
            CommonLoadingDialog(
                text = stringResource(R.string.video_processing),
                onDismissRequest = { },
                dimAmount = 0f,
                dismissOnClickOutside = false,
                allowTouchPassThrough = true,
            )
        }

        // 根据预览类型选择不同的UI实现
        val useTraditionalPreview = when (val state = voiceFilterPreviewState.value) {
            is VoiceFilterPreviewState.Loading -> state.voiceFilter?.itemConfig?.previewType != PreviewType.PREVIEW.originDef
            is VoiceFilterPreviewState.Completed -> state.voiceFilter?.itemConfig?.previewType != PreviewType.PREVIEW.originDef
        }

        if (useTraditionalPreview) {
            // 使用传统的预览界面，但基于VoiceFilterPreviewState
            VoicePreviewScreenWithUnifiedState(
                voiceFilterPreviewState = voiceFilterPreviewState,
                voicePreviewAction = viewModel.onPreviewAction(),
                sendTitleTipState = sendTitleTipState,
                sourceType = sourceType,
                playProgress = { playProgressState.value } // 传统预览界面暂时使用固定值，可根据需要从ViewModel获取
            )
        } else {
            // 使用新的语音滤镜预览界面
            VoiceFilterPreviewActivityScreen(
                viewModel = viewModel,
                sourceType = sourceType
            )
        }
    }


    override fun onStart() {
        super.onStart()

        // 确保所有来源都设置背景不变暗
        dialog?.window?.let { window ->
            // 设置宽度和高度
            val layoutParams = WindowManager.LayoutParams()
            layoutParams.copyFrom(window.attributes)
            window.attributes = layoutParams
            window.setDimAmount(0f) // 设置背景不变暗
            // 添加以下代码确保移除黑色背景
            window.clearFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND)
            window.setBackgroundDrawable(ColorDrawable(android.graphics.Color.TRANSPARENT))
        }
    }


    override fun initData() {
        viewModel.previewAction.collectIn(this) { previewAction ->
            logInfo(TAG, "previewAction = $previewAction")
            if (previewAction == VoicePreviewAction.SendActionAnimFinish) {
                dismiss()
            }
            // 直接调用回调接口而不是通过 ViewModel
            callback?.onVoicePreviewAction(previewAction)
        }

        // 使用新的响应式状态流监听对话框显示状态
        viewModel.voiceFilterPreviewState.collectIn(this.viewLifecycleOwner) { state ->
            // 直接从基类获取共有属性
            val showDialog = state.notClickCancelOp
            logInfo(TAG, "initData: showDialog = $showDialog")
            if (!showDialog) {
                dismiss()
            }
        }

        // 监听分享事件流，处理分享操作
        viewModel.shareEventFlow.collectIn(this) { shareEvent ->
            logInfo(TAG, "shareEventFlow: shareType = ${shareEvent.shareType.type}, file = ${shareEvent.videoFile.absolutePath}")
            activity?.let { activity ->
                viewLifecycleOwner.lifecycleScope.launch {
                    if (shareEvent.shareType.type.type == ShareType.DOWN_LOAD.type) {
                        // 处理下载逻辑，需要权限检查
                        handleDownloadWithPermission(shareEvent, activity)
                    } else {
                        // 处理其他分享逻辑
                        ShareHelper.shareVideoByFile(activity, shareEvent.shareType.type, shareEvent.videoFile.absolutePath, false)
                        viewModel.onResultShareVoiceFilterVideo(shareEvent.shareType.type, true, null)
                    }
                }
            }
        }
    }

    /**
     * 处理权限被拒绝的情况
     * 根据拒绝类型显示不同的提示消息
     */
    private fun handlePermissionDenied() {
        activity?.let { activity ->
            // 检查是否为永久拒绝（用户勾选了"不再询问"）
            val shouldShowRationale = ActivityCompat.shouldShowRequestPermissionRationale(
                activity,
                android.Manifest.permission.WRITE_EXTERNAL_STORAGE
            )
            if (shouldShowRationale) {
                // 临时拒绝权限：用户拒绝了权限但没有勾选"不再询问"
                // 显示下载失败提示
                toast(R.string.failed_to_save)
                logInfo(TAG, "Storage permission temporarily denied")
            } else {
                // 永久拒绝权限：用户勾选了"不再询问"或首次申请被拒绝
                // 显示需要去设置中手动开启权限的提示
                toast(R.string.chat_record_permission_denied_toast)
                logInfo(TAG, "Storage permission permanently denied")
            }
        }
    }

    /**
     * 处理带权限检查的下载逻辑
     */
    private suspend fun handleDownloadWithPermission(
        shareEvent: ShareEvent,
        activity: android.app.Activity
    ) {
        // 检查Android版本，Android 10以下需要权限
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q) {
            // 检查是否已有权限
            if (ContextCompat.checkSelfPermission(activity, android.Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
                // 权限未授予，需要申请权限
                logInfo(TAG, "WRITE_EXTERNAL_STORAGE permission not granted, requesting permission")
                pendingDownloadEvent = shareEvent
                requestPermissionLauncher.launch(android.Manifest.permission.WRITE_EXTERNAL_STORAGE)
                return
            }
        }

        // 有权限或不需要权限，直接执行下载
        executeDownload(shareEvent)
    }

    /**
     * 执行实际的下载操作
     */
    private fun executeDownload(shareEvent: ShareEvent) {
        saveVideoInGallery(
            requireContext(),
            shareEvent.videoFile,
            onSuccess = {
                logInfo(TAG, "Download success: ${shareEvent.videoFile.absolutePath}")
                toastIconFontMsg(
                    iconFont = R.string.ic_correct_solid.asString(),
                    message =R.string.air_save_ve_success.asString(),
                    iconFontColor = R.color.text_white_important.asColor(),
                    textColor = R.color.color_text_white_primary.asColor(),
                    style = IconToastStyle.ICON_TOP_TEXT_BOTTOM
                )
                viewModel.onResultShareVoiceFilterVideo(ShareType.DOWN_LOAD, true, null)
            },
            onFail = {
                toast(R.string.failed_to_save)
                logInfo(TAG, "Download failed: ${shareEvent.videoFile.absolutePath}")
                viewModel.onResultShareVoiceFilterVideo(ShareType.DOWN_LOAD, false, null)
            }
        )
    }

    /**
     * 设置回调接口
     * 用于内存重建场景下重新设置回调
     */
    fun setCallback(callback: VoicePreviewCallback?) {
        this.callback = callback
    }
}


/**
 * 新版语音滤镜预览界面
 * 使用统一的 VoiceFilterPreviewScreen 组件，并在此处处理动画逻辑
 */
@Composable
internal fun VoiceFilterPreviewActivityScreen(
    modifier: Modifier = Modifier,
    viewModel: VoicePreviewViewModel,
    sourceType: SourceType,
) {
    val voiceFilterPreviewState = viewModel.voiceFilterPreviewState.collectAsStateWithLifecycle()
    val sendTitleTipState = viewModel.showSendTipFlow.collectAsStateWithLifecycle()
    val shareUiState = viewModel.shareUiState.collectAsStateWithLifecycle()
    // 获取状态值
    val state = voiceFilterPreviewState.value
    val isOnSendAnim = state.isOnSendAnim
    val sendAnimTranslationY = state.sendAnimTranslationY
    val density = LocalDensity.current

    // 根据来源类型调整目标位置
    val targetTranslationY = when (sourceType) {
        SourceType.HOME -> sendAnimTranslationY
        SourceType.CHAT_LIST -> sendAnimTranslationY + 50
    }

    // 根据来源类型调整动画参数
    val animationDuration = when (sourceType) {
        SourceType.HOME -> 200
        SourceType.CHAT_LIST -> 200
    }

    // 用于存储组件的屏幕位置信息
    var componentScreenPosition by remember { mutableStateOf<LayoutCoordinates?>(null) }

    // 计算动画偏移量（基于组件中心点到目标位置的距离）
    val animatedTranslationY = remember(componentScreenPosition, targetTranslationY, density) {
        derivedStateOf {
            componentScreenPosition?.let { coordinates ->
                val componentCenterY = coordinates.positionOnScreen().y + coordinates.size.height / 2f
                val targetY = targetTranslationY.toFloat()
                val offsetDistance = targetY - componentCenterY
                with(density) { offsetDistance.toDp() }
            } ?: 0.dp
        }
    }

    val animatedOffset by animateFloatAsState(
        targetValue = if (isOnSendAnim) 1f else 0f,
        animationSpec = tween(
            durationMillis = if (isOnSendAnim) animationDuration else 1,
            easing = { fraction -> fraction * fraction }
        ),
        finishedListener = {
            if (isOnSendAnim) {
                // 直接调用 VoiceFilterPreviewAction.SendActionAnimFinish
                val voicePreviewAction = VoicePreviewAction.SendActionAnimFinish
                viewModel.onPreviewAction().invoke(voicePreviewAction)
            }
        },
        label = "send_animation"
    )

    // 为CHAT_LIST来源添加scale缩放动画
    val scaleAnimation by animateFloatAsState(
        targetValue = if (isOnSendAnim && sourceType == SourceType.CHAT_LIST) 0f else 1f,
        animationSpec = tween(
            durationMillis = if (isOnSendAnim) animationDuration else 1,
            easing = { fraction -> fraction * fraction }
        ),
        label = "scale_animation"
    )

    Box(
        modifier = modifier
            .fillMaxHeight()
            .backgroundWithAlpha()
            .widthIn(max = 400.dp)
            .fillMaxWidth()
            .padding(horizontal = 20.dp)
            .padding(bottom = state.offsetY.pxToDp())
        ,
        contentAlignment = Alignment.BottomCenter
    ) {
        // 显示发送提示标题
        ShowVFStyleTitle(
            titleLambda = { sendTitleTipState.value },
            modifier = Modifier
                .align(Alignment.TopCenter)
        )

        VoiceFilterPreviewScreen(
            modifier = Modifier
                .let { screenModifier ->
                    // 根据来源类型应用不同的动画效果
                    when (sourceType) {
                        SourceType.CHAT_LIST -> {
                            // CHAT_LIST使用scale缩放到0的动画
                            screenModifier.scale(scaleAnimation)
                        }

                        SourceType.HOME -> {
                            // 使用精确的位移动画，基于组件中心点到目标位置的计算
                            val offsetY = animatedTranslationY.value * animatedOffset
                            screenModifier
                                .offset(y = offsetY)
                                .scale(0.2f.coerceAtLeast(1 - (3f * animatedOffset)))
                        }
                    }
                }
                .onGloballyPositioned { coordinates ->
                    // 存储组件的屏幕位置信息，用于计算动画偏移
                    componentScreenPosition = coordinates
                },
            state = state,
            shareTypeList = persistentListOf(ShareType.DOWN_LOAD, ShareType.SYSTEM_SHARE).addAll(
                ShareType.getShareTypeList(AppConfigRequestManager.sharePlatformList)
            ),
            isShareLoading = shareUiState.value.state == ShareMediaUIState.State.LOADING,
            onAction = { action ->
                // 将 VoiceFilterPreviewAction 映射到 VoicePreviewAction
                val voicePreviewAction = when (action) {
                    VoiceFilterPreviewAction.Cancel -> VoicePreviewAction.CancelAction
                    VoiceFilterPreviewAction.Replay -> VoicePreviewAction.PlayOrFilterAction
                    VoiceFilterPreviewAction.Send -> VoicePreviewAction.SendAction
                    VoiceFilterPreviewAction.SendActionAnimFinish -> VoicePreviewAction.SendActionAnimFinish
                }
                viewModel.onPreviewAction().invoke(voicePreviewAction)
            },
            onShareAction = { shareItemBean ->
                viewModel.onPreviewShareOuter(shareItemBean)
            },
            videoPreviewTemplateUrl = state.voiceFilter?.videoTemplate?.videoPreviewTemplateUrl,
        )
    }
}

/**
 * 基于统一状态的传统语音预览界面
 * 使用VoiceFilterPreviewState进行状态管理，兼容原有的预览逻辑
 */
@Composable
internal fun VoicePreviewScreenWithUnifiedState(
    voiceFilterPreviewState: State<VoiceFilterPreviewState>,
    voicePreviewAction: (VoicePreviewAction) -> Unit,
    sendTitleTipState: State<String>,
    sourceType: SourceType,
    playProgress: () -> Float = { 0f },
) {
    val state = voiceFilterPreviewState.value

    // 直接从基类获取共有属性
    val offsetY = state.offsetY

    Box(
        modifier = Modifier
            .fillMaxSize()
            .backgroundWithAlpha()
            .padding(bottom = offsetY.pxToDp()),
        contentAlignment = Alignment.BottomCenter
    ) {
        ShowVFStyleTitle(
            titleLambda = { sendTitleTipState.value },
            modifier = Modifier
                .align(Alignment.TopCenter)
                .padding(horizontal = 20.dp)
        )

        CommonPreviewContentWithUnifiedState(
            modifier = Modifier.align(Alignment.BottomCenter),
            state = state,
            action = voicePreviewAction,
            sourceType = sourceType,
            playProgress = playProgress // 传统预览界面暂时使用固定值，可根据需要从ViewModel获取
        )
    }
}


/**
 * 基于统一状态的内容区域组件
 * 使用VoiceFilterPreviewState进行状态管理，支持不同来源的动画效果和样式调整
 */
@Composable
private fun CommonPreviewContentWithUnifiedState(
    modifier: Modifier = Modifier,
    state: VoiceFilterPreviewState,
    action: (VoicePreviewAction) -> Unit,
    sourceType: SourceType,
    playProgress: () -> Float = { 0f },
) {
    // 直接从基类获取共有属性
    val isOnSendAnim = state.isOnSendAnim
    val sendAnimTranslationY = state.sendAnimTranslationY
    val progress = playProgress()
    val voiceFilter = state.voiceFilter
    val density = LocalDensity.current

    val targetTranslationY = when (sourceType) {
        SourceType.HOME -> sendAnimTranslationY
        SourceType.CHAT_LIST -> sendAnimTranslationY
    }

    // 根据来源调整动画参数
    val animationDuration = when (sourceType) {
        SourceType.HOME -> 200
        SourceType.CHAT_LIST -> 200 // 聊天列表动画稍快
    }

    // 用于存储组件的屏幕位置信息
    var componentScreenPosition by remember { mutableStateOf<LayoutCoordinates?>(null) }

    // 计算动画偏移量（基于组件中心点到目标位置的距离）
    val animatedTranslationY = remember(componentScreenPosition, targetTranslationY, density) {
        derivedStateOf {
            componentScreenPosition?.let { coordinates ->
                val componentCenterY = coordinates.positionOnScreen().y + coordinates.size.height / 2f
                val targetY = targetTranslationY.toFloat()
                val offsetDistance = targetY - componentCenterY
                with(density) { offsetDistance.toDp() }
            } ?: 0.dp
        }
    }

    // 根据来源类型选择不同的动画效果
    val animatedOffset by animateFloatAsState(
        targetValue = if (isOnSendAnim) 1f else 0f,
        animationSpec = tween(
            durationMillis = if (isOnSendAnim) animationDuration else 1,
            easing = { fraction -> fraction * fraction }
        ),
        finishedListener = {
            if (isOnSendAnim) {
                action.invoke(VoicePreviewAction.SendActionAnimFinish)
            }
        },
        label = "send_animation"
    )

    // 为CHAT_LIST来源添加scale缩放动画
    val scaleAnimation by animateFloatAsState(
        targetValue = if (isOnSendAnim && sourceType == SourceType.CHAT_LIST) 0f else 1f,
        animationSpec = tween(
            durationMillis = if (isOnSendAnim) animationDuration else 1,
            easing = { fraction -> fraction * fraction }
        ),
        label = "scale_animation"
    )

    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 20.dp)
            .let { modifier ->
                // 根据来源类型应用不同的动画效果
                when (sourceType) {
                    SourceType.CHAT_LIST -> {
                        // CHAT_LIST来源使用scale缩放到0的动画
                        modifier.scale(scaleAnimation)
                    }

                    SourceType.HOME -> {
                        // 使用精确的位移动画，基于组件中心点到目标位置的计算
                        val offsetY = animatedTranslationY.value * animatedOffset

                        modifier
                            .offset(y = offsetY)
                            .scale(0.2f.coerceAtLeast(1 - (3f * animatedOffset)))
                    }
                }
            }
            .clip(RoundedCornerShape(20.dp))
            .background(colorResource(id = R.color.color_background_4_default))
            .padding(horizontal = 30.dp)
            .onGloballyPositioned { coordinates ->
                // 存储组件的屏幕位置信息，用于计算动画偏移
                componentScreenPosition = coordinates
                // if (!isOnSendAnim) onGetPreviewHeight.invoke(coordinates.size.height)
            }
    ) {
        VerticalSpace(dp = 24.dp)

        // 根据状态类型显示不同内容
        when (state) {
            is VoiceFilterPreviewState.Loading -> {
                VoicePreviewContent(
                    previewSource = HOME,
                    filterState = FilterIng,
                    isPlaying = false,
                    voiceDuration = state.duration, // 从Loading状态中获取正确的时长
                    progress = progress,
                    voiceFilter = voiceFilter,
                    action = action
                )
            }

            is VoiceFilterPreviewState.Completed -> {
                VoicePreviewContent(
                    previewSource = HOME,
                    filterState = state.filterState,
                    isPlaying = state.isPlaying,
                    voiceDuration = state.duration,
                    progress = progress,
                    voiceFilter = voiceFilter,
                    action = action
                )
            }
        }

        VerticalSpace(dp = 30.dp)
    }
}

/**
 * 显示语音滤镜样式标题
 * 从原有代码中提取的通用组件
 */
@Composable
private fun ShowVFStyleTitle(
    titleLambda: () -> String,
    modifier: Modifier = Modifier,
) {
    // 这里应该实现标题显示逻辑
    // 由于原代码中没有提供具体实现，这里留空
    // 实际使用时需要根据具体需求实现
    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(dimensionResource(id = R.dimen.title_bar_height))
    ) {
        Text(
            style = TextStyles.titleMedium(),
            text = titleLambda.invoke(),
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            color = colorResource(R.color.color_foreground_neutral_important_default),
            modifier = Modifier
                .align(Alignment.Center)

        )
    }
}