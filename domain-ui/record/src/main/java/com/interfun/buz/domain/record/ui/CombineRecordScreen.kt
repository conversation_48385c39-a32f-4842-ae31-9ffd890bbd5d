package com.interfun.buz.domain.record.ui

import android.annotation.SuppressLint
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.gestures.awaitEachGesture
import androidx.compose.foundation.layout.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.platform.LocalViewConfiguration
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.interfun.buz.base.ktx.DefaultCallback
import com.interfun.buz.base.ktx.isNotNull
import com.interfun.buz.base.ktx.resumedActivity
import com.interfun.buz.base.utils.VibratorUtil
import com.interfun.buz.biz.center.voicefilter.repository.VoiceFilterPlayRepository
import com.interfun.buz.common.manager.ActionInfoHandler.ActionType
import com.interfun.buz.common.manager.router.RouterManager
import com.interfun.buz.compose.components.BackHandler
import com.interfun.buz.compose.ktx.rememberMutableBoolean
import com.interfun.buz.compose.ktx.rememberMutableFloat
import com.interfun.buz.compose.modifier.debouncedClickable
import com.interfun.buz.compose.utils.RecomposeCountHelper
import com.interfun.buz.core.widget_record.entity.LocalRecordUIConfig
import com.interfun.buz.core.widget_record.entity.RecordOperateStatus
import com.interfun.buz.core.widget_record.entity.RecordUIConfig
import com.interfun.buz.core.widget_record.state.VoiceFilterUiData
import com.interfun.buz.core.widget_record.ui.InnerVoiceFilterEntry
import com.interfun.buz.core.widget_record.ui.RecordScreen
import com.interfun.buz.core.widget_record.ui.VoiceFilterScreen
import com.interfun.buz.core.widget_record.ui.action.RecordScreenAction
import com.interfun.buz.core.widget_record.ui.action.RecordScreenAction.*
import com.interfun.buz.domain.record.entity.RecordBgType
import com.interfun.buz.domain.record.entity.ReleaseReason
import com.interfun.buz.domain.record.helper.RecordGestureDetectorHelper
import com.interfun.buz.domain.record.viewmodel.RecordVoiceViewModel
import com.interfun.buz.domain.record.viewmodel.VoiceFilterViewModel
import kotlinx.coroutines.flow.collectLatest
import org.json.JSONObject

/**
 * <AUTHOR>
 * @date 2024/12/10
 * @desc
 */
@SuppressLint("UnusedBoxWithConstraintsScope")
@Composable
fun CombineRecordScreen(
    modifier: Modifier = Modifier,
    onActions: (RecordScreenAction) -> Unit,
    showVFEntryRedDot: () -> Boolean = { false },
    isInterceptGestureLambda: () -> Boolean = { false },
    config: RecordUIConfig = RecordUIConfig(),
    recordViewModel: RecordVoiceViewModel = hiltViewModel(),
    voiceFilterViewModel: VoiceFilterViewModel = hiltViewModel()
) {
    SideEffect {
        RecomposeCountHelper.increment("CombineRecordScreen")
    }
    val recordBgType by recordViewModel.recordBgStateFlow.collectAsStateWithLifecycle()
    val lastStartRecordTime by recordViewModel.lastStartRecordTime.collectAsStateWithLifecycle()
    val isRecording by recordViewModel.isRecordingFlow.collectAsStateWithLifecycle()
    val currentPressArea by recordViewModel.currentPressAreaFlow.collectAsStateWithLifecycle()

    val isInVoiceFilterMode by voiceFilterViewModel.isVoiceFilterModeStateFlow.collectAsStateWithLifecycle()
    val voiceFilterUiState by voiceFilterViewModel.voiceFilterUiStateFlow.collectAsStateWithLifecycle()
    val showBadgeMap by voiceFilterViewModel.voiceFilterShowBadgeMapFlow.collectAsStateWithLifecycle()
    val isVoiceFilterPreviewing by voiceFilterViewModel.isVoiceFilterPreviewPlayingFlow.collectAsStateWithLifecycle()

    var recordOperateStatus by remember { mutableStateOf(RecordOperateStatus()) }
    var onPressDownCallback by remember { mutableStateOf<(DefaultCallback)>({}) }

    val isShowAIBg by remember {
        derivedStateOf {
            recordBgType == RecordBgType.Robot || recordBgType == RecordBgType.AddressBot
        }
    }

    LaunchedEffect(Unit) {
        recordViewModel.recordOperateStatusFlow.collectLatest {
            recordOperateStatus = it
        }
    }

    val onActions = getRecordActions(onActions, recordViewModel, voiceFilterViewModel)
    val touchSlop = LocalViewConfiguration.current.touchSlop
    val localLayoutDirection = LocalLayoutDirection.current
    var lockGuideProgress by rememberMutableFloat()
    val gestureDetector = remember {
        RecordGestureDetectorHelper(
            touchSlop = touchSlop,
            recordTransitionYWhenShowTabs = config.recordTransitionYWhenShowTabs,
            getAreaLambda = { type -> recordViewModel.getArea(type) },
            isRtlLambda = { localLayoutDirection == LayoutDirection.Rtl },
            isShowVFTabs = { voiceFilterUiState.isShowTabs },
            isRecordingLambda = { isRecording },
            isLockingLambda = { recordOperateStatus.isLocking },
            isVoiceFilterModeLambda = { isInVoiceFilterMode },
            isVoiceFilterScrollingLambda = { voiceFilterViewModel.isVoiceFilterScrollingStateFlow.value },
            currentVoiceFilterLambda = { voiceFilterUiState.currentSelectVoiceFilter },
            checkEnableAndToastWhenDisableLambda = {
                recordViewModel.checkEnableAndToastWhenDisable(
                    isRecordEnable = recordOperateStatus.isEnable,
                    onRequestRecordPermission = {
                        onActions.invoke(OnRequestRecordPermission)
                    }
                )
            },
            onPressDown = {
                onPressDownCallback.invoke()
                recordViewModel.onRecordPressDown()
                voiceFilterViewModel.stopFilterSoundUrl()
            },
            onLockRecord = {
                recordViewModel.onRecordLocked()
            },
            onPressUp = { reason ->
                recordViewModel.onRecordRelease(reason)
            },
            onPressAreaChange = { type ->
                recordViewModel.updateCurrentPressArea(type)
            },
            onLockGuideProgressChange = {
                lockGuideProgress = it
            }
        ).create()
    }

    DisposableEffect(Unit) {
        onDispose {
            if (isRecording) {
                // 录音过程面板被异常关闭，取消录制
                recordViewModel.onRecordRelease(ReleaseReason.ON_DISPOSE)
            }
        }
    }

    // 录音过程中拦截返回操作
    BackHandler(enabledLambda = { isRecording }) {
        if (recordOperateStatus.isLocking) {
            recordViewModel.onRecordRelease(reason = ReleaseReason.ON_BACK_PRESS)
        }
    }

    // 生成普通模式和滤镜模式切换过程中的动画进度
    var switchToVFProgress by rememberMutableFloat()
    var hasSwitchToVFModeFinished by rememberMutableBoolean()
    SwitchToVFModeAnim(
        isInVoiceFilterModeLambda = { isInVoiceFilterMode },
        onSwitchProgressChange = { switchToVFProgress = it },
        onSwitchFinished = { hasSwitchToVFModeFinished = it }
    )

    CompositionLocalProvider(LocalRecordUIConfig provides config) {
        BoxWithConstraints(
            modifier = modifier
                .fillMaxSize()
                .pointerInput(Unit) {
                    awaitEachGesture(gestureDetector)
                }
        ) {
            VoiceFilterScreen(
                voiceFilterUiStateLambda = { voiceFilterUiState },
                isShowAIBgLambda = { isShowAIBg },
                isInVoiceFilterModeLambda = { isInVoiceFilterMode },
                isVoiceFilterPreviewingLambda = { isVoiceFilterPreviewing },
                isInterceptGestureLambda = isInterceptGestureLambda,
                hasSwitchToVFModeFinishedLambda = { hasSwitchToVFModeFinished },
                switchToVFProgressLambda = { switchToVFProgress },
                recordOperateStatusLambda = { recordOperateStatus },
                showBadgeMapLambda = { showBadgeMap },
                voiceFilterAction = onActions,
                onSetPressDownCallback = { onPressDownCallback = it },
            )

            RecordScreen(
                isRecording = { isRecording },
                isShowAIBg = { isShowAIBg },
                isShowTabs = { voiceFilterUiState.isShowTabs },
                isInterceptGestureLambda = isInterceptGestureLambda,
                isInVoiceFilterMode = { isInVoiceFilterMode },
                switchToVFProgressLambda = { switchToVFProgress },
                currentPressAreaLambda = { currentPressArea },
                recordOperateStatusLambda = { recordOperateStatus },
                lastStartRecordTimeLambda = { lastStartRecordTime },
                lockGuideProgressLambda = { lockGuideProgress },
                currentVoiceFilter = { voiceFilterUiState.currentSelectVoiceFilter },
                getAreaLambda = { type -> recordViewModel.getArea(type) },
                onAreaUpdate = { type, area -> recordViewModel.updateArea(type, area) }
            )

            InnerVoiceFilterEntry(
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .padding(end = 20.dp),
                isInVoiceFilterMode = { isInVoiceFilterMode },
                isPressingOrLockingLambda = { recordOperateStatus.isPressingOrLocking },
                showRedDotLambda = showVFEntryRedDot,
                onClick = { onActions.invoke(OnClickVoiceFilterEntry) }
            )

            NotEnabledInterceptor(
                modifier = Modifier.fillMaxSize(),
                recordOperateStatusLambda = { recordOperateStatus },
                isInVoiceFilterModeLambda = { isInVoiceFilterMode },
                onClick = { onActions.invoke(OnClickRecordWhenNotEnabled) }
            )
        }
    }
}

@Composable
private fun SwitchToVFModeAnim(
    isInVoiceFilterModeLambda: () -> Boolean,
    onSwitchProgressChange: (Float) -> Unit,
    onSwitchFinished: (Boolean) -> Unit
) {
    val switchVoiceFilterProgress by animateFloatAsState(
        targetValue = if (isInVoiceFilterModeLambda()) 1f else 0f,
        animationSpec = tween(250),
        finishedListener = { progress ->
            if (progress == 1f) {
                onSwitchFinished(true)
            } else if (progress == 0f) {
                onSwitchFinished(false)
            }
        }
    )
    LaunchedEffect(switchVoiceFilterProgress) {
        onSwitchProgressChange(switchVoiceFilterProgress)
    }
}

@Composable
private fun NotEnabledInterceptor(
    modifier: Modifier = Modifier,
    recordOperateStatusLambda: () -> RecordOperateStatus,
    isInVoiceFilterModeLambda: () -> Boolean,
    onClick: () -> Unit,
) {
    if (!recordOperateStatusLambda().isEnable) {
        // 在录音不可用状态下，拦截所有区域的触摸事件、点击录音按钮区域会触发OnClickRecordWhenNotEnabled事件
        Box(
            modifier = modifier
                .fillMaxSize()
                .debouncedClickable(onClick = {})
        ) {
            val config = LocalRecordUIConfig.current
            val heightPercent = if (isInVoiceFilterModeLambda()) {
                //这里不那么麻烦1比1还原位置了，放大一点覆盖录音按钮的区域即可
                config.recordBtnHeightPercent * config.voiceFilterUnRecordBtnSizeScale * 1.2f
            } else {
                config.recordBtnHeightPercent * 1.05f
            }

            Box(
                modifier = Modifier
                    .fillMaxHeight(fraction = heightPercent)
                    .aspectRatio(1f)
                    .align(Alignment.Center)
                    .debouncedClickable(onClick = onClick)
            )
        }
    }
}

private fun getRecordActions(
    onActions: (RecordScreenAction) -> Unit,
    recordViewModel: RecordVoiceViewModel,
    voiceFilterViewModel: VoiceFilterViewModel,
): (RecordScreenAction) -> Unit = { action ->
    when (action) {
        OnClickPreviewVoiceFilter -> {
            voiceFilterViewModel.previewVoiceFilter(VoiceFilterPlayRepository.FROM_CLICK)
        }

        OnClickQuit -> {
            voiceFilterViewModel.onClickCloseVoiceFilterMode()
        }

        OnClickRecordWhenNotEnabled -> {
            recordViewModel.onClickRecordWhenNotEnabled()
        }

        is OnClickClear -> {
            voiceFilterViewModel.updateSelectVoiceFilter(action.tabType, VoiceFilterUiData.NO_FILTER_ID)
        }

        is OnSelectVoiceFilter -> {
            voiceFilterViewModel.updateSelectVoiceFilter(action.tabType, action.voiceFilter.filterId)
            voiceFilterViewModel.updateVFLatestTimestamp(
                filterId = action.voiceFilter.filterId,
                latestTimestamp = action.voiceFilter.latestTimestamp
            )
            voiceFilterViewModel.previewVoiceFilter(VoiceFilterPlayRepository.FROM_SCROLL)
        }

        is OnSelectVoiceFilterTab -> {
            voiceFilterViewModel.updateSelectTabType(action.tab.type)
            VibratorUtil.vibrator(from = "OnSelectVoiceFilterTab")
        }

        is OnClickCampaignEntry -> {
            val actionInfo = action.action
            if (actionInfo.isNotNull() && actionInfo!!.type == ActionType.ROUTE.type && actionInfo.router.isNotNull()) {
                val router = actionInfo.router
                if (resumedActivity != null) {
                    val routerInfo = RouterManager.create(router.scheme, JSONObject(router.extraData))
                    RouterManager.handle(resumedActivity!!, routerInfo)
                }
            }
        }

        is OnVoiceFilterScrollingChanged -> {
            voiceFilterViewModel.updateVoiceFilterScrolling(action.isScrolling)
        }

        else -> {}
    }
    onActions.invoke(action)
}
