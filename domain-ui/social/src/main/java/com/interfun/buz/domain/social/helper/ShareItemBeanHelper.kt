package com.interfun.buz.domain.social.helper

import android.annotation.SuppressLint
import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.common.ktx.shareChooser
import com.interfun.buz.domain.im.social.entity.ShareItemBean
import com.interfun.buz.domain.im.social.entity.ShareType
import com.interfun.buz.domain.social.R
import com.interfun.buz.domain.social.bean.PackageName
import com.yibasan.lizhifm.sdk.platformtools.MobileUtils.isAppInstalled

internal fun getShareItemBeanList(shareTypeList:List<ShareType>):List<ShareItemBean>{
    val list = mutableListOf<ShareItemBean>()
    shareTypeList.forEach {
        when(it){
            ShareType.SYSTEM_SHARE -> {
                list.add(getSystemShareItemBean())
            }
            ShareType.QR_CODE -> {
                list.add(getQRCodeShareItemBean())
            }
            ShareType.COPY_LINK -> {
                list.add(getCopyLinkShareItemBean())
            }
            ShareType.DOWN_LOAD -> {
                list.add(getDownloadShareItemBean())
            }
            ShareType.LINE -> {
                if (shareChooser.line.isAppInstalled(appContext)) {
                    list.add(getLineShareItemBean())
                }
            }
            ShareType.INSTAGRAM -> {
                if (shareChooser.instagram.isAppInstalled(appContext)) {
                    list.add(getInstagramShareItemBean())
                }
            }
            ShareType.WHATSAPP -> {
                if (shareChooser.whatsapp.isAppInstalled(appContext)){
                    list.add(getWhatsAppShareItemBean())
                }
            }
            ShareType.TELEGRAM -> {
                if (shareChooser.telegram.isAppInstalled(appContext)){
                    list.add(getTelegramShareItemBean())
                }
            }
            ShareType.MESSENGER -> {
                if (shareChooser.messenger.isAppInstalled(appContext)){
                    list.add(getMessengerShareItemBean())
                }
            }
            ShareType.MESSAGE -> {
                list.add(getMessageShareItemBean())
            }
            ShareType.SNAPCHAT -> {
                if (shareChooser.snapchat.isAppInstalled(appContext)){
                    list.add(getSnapchatShareItemBean())
                }
            }
            ShareType.TIKTOK -> {
                if (shareChooser.tiktok.isAppInstalled(appContext)){
                    list.add(getTikTokShareItemBean())
                }
            }
            ShareType.X -> {
                if (isAppInstalled(PackageName.X_PACKAGE_NAME)) {
                    list.add(getXShareItemBean())
                }
            }
            ShareType.FACEBOOK -> {
                if (isAppInstalled(PackageName.FACEBOOK_PACKAGE_NAME)) {
                    list.add(getFacebookShareItemBean())
                }
            }
            else ->{}
        }
    }
    return list
}

@SuppressLint("ResourceAsColor")
private fun getSystemShareItemBean(): ShareItemBean.NormalShareItemBean {
    return ShareItemBean.NormalShareItemBean(
        shareText = R.string.share.asString(),
        iconText = R.string.ic_share.asString(),
        iconColor = R.color.color_text_black_primary,
        backgroundColor = R.color.color_background_highlight_1_default,
        type = ShareType.SYSTEM_SHARE
    )
}

@SuppressLint("ResourceAsColor")
private fun getQRCodeShareItemBean(): ShareItemBean.NormalShareItemBean {
    return ShareItemBean.NormalShareItemBean(
        shareText = R.string.common_qrcode.asString(),
        iconText = R.string.ic_qrcode.asString(),
        iconColor = R.color.color_text_black_primary,
        backgroundColor = R.color.color_background_light_default,
        type = ShareType.QR_CODE
    )
}
private fun getDownloadShareItemBean(): ShareItemBean.NormalShareItemBean {
    return ShareItemBean.NormalShareItemBean(
        shareText = R.string.download.asString(),
        iconText = R.string.ic_download.asString(),
        iconColor = R.color.color_text_black_primary,
        backgroundColor = R.color.color_background_light_default,
        type = ShareType.DOWN_LOAD
    )
}

@SuppressLint("ResourceAsColor")
private fun getCopyLinkShareItemBean(): ShareItemBean.NormalShareItemBean {
    return ShareItemBean.NormalShareItemBean(
        shareText = R.string.ftue_v3_copylink.asString(),
        iconText = R.string.ic_link.asString(),
        iconColor = R.color.color_text_black_primary,
        backgroundColor = R.color.color_background_light_default,
        type = ShareType.COPY_LINK
    )
}

private fun getLineShareItemBean(): ShareItemBean.ThirdPartyShareItemBean {
    return ShareItemBean.ThirdPartyShareItemBean(
        iconRes = R.drawable.contact_pic_line,
        shareText = R.string.common_line.asString(),
        type = ShareType.LINE
    )
}

private fun getInstagramShareItemBean(): ShareItemBean.ThirdPartyShareItemBean {
    return ShareItemBean.ThirdPartyShareItemBean(
        iconRes = R.drawable.contact_pic_instagram,
        shareText = R.string.common_instagram.asString(),
        type = ShareType.INSTAGRAM
    )
}

private fun getTelegramShareItemBean(): ShareItemBean.ThirdPartyShareItemBean {
    return ShareItemBean.ThirdPartyShareItemBean(
        iconRes = R.drawable.contact_pic_telegram,
        shareText = R.string.common_telegram.asString(),
        type = ShareType.TELEGRAM
    )
}

private fun getMessengerShareItemBean(): ShareItemBean.ThirdPartyShareItemBean {
    return ShareItemBean.ThirdPartyShareItemBean(
        iconRes = R.drawable.contact_pic_messenger,
        shareText = R.string.common_messenger.asString(),
        type = ShareType.MESSENGER
    )
}

private fun getMessageShareItemBean(): ShareItemBean.ThirdPartyShareItemBean {
    return ShareItemBean.ThirdPartyShareItemBean(
        iconRes = R.drawable.contact_pic_message,
        shareText = R.string.common_message.asString(),
        type = ShareType.MESSAGE
    )
}

private fun getSnapchatShareItemBean(): ShareItemBean.ThirdPartyShareItemBean {
    return ShareItemBean.ThirdPartyShareItemBean(
        iconRes = R.drawable.contact_pic_snapchat,
        shareText = R.string.common_snapchat.asString(),
        type = ShareType.SNAPCHAT
    )
}

private fun getWhatsAppShareItemBean(): ShareItemBean.ThirdPartyShareItemBean {
    return ShareItemBean.ThirdPartyShareItemBean(
        iconRes = R.drawable.contact_pic_whatsapp,
        shareText = R.string.common_whatsapp.asString(),
        type = ShareType.WHATSAPP
    )
}

private fun getTikTokShareItemBean(): ShareItemBean.ThirdPartyShareItemBean {
    return ShareItemBean.ThirdPartyShareItemBean(
        iconRes = R.drawable.contact_pic_tiktok,
        shareText = R.string.common_tiktok.asString(),
        type = ShareType.TIKTOK
    )
}

private fun getXShareItemBean(): ShareItemBean.ThirdPartyShareItemBean {
    return ShareItemBean.ThirdPartyShareItemBean(
        iconRes = R.drawable.contact_pic_x,
        shareText = R.string.common_X.asString(),
        type = ShareType.X
    )
}

private fun getFacebookShareItemBean(): ShareItemBean.ThirdPartyShareItemBean {
    return ShareItemBean.ThirdPartyShareItemBean(
        iconRes = R.drawable.common_ic_facebook,
        shareText = R.string.common_facebook.asString(),
        type = ShareType.FACEBOOK
    )
}

private fun getDiscordShareItemBean(): ShareItemBean.ThirdPartyShareItemBean {
    return ShareItemBean.ThirdPartyShareItemBean(
        iconRes = R.drawable.contact_pic_discord,
        shareText = R.string.common_discord.asString(),
        type = ShareType.DISCORD
    )
}

private fun getViberShareItemBean(): ShareItemBean.ThirdPartyShareItemBean {
    return ShareItemBean.ThirdPartyShareItemBean(
        iconRes = R.drawable.contact_pic_viber,
        shareText = R.string.common_viber.asString(),
        type = ShareType.VIBER
    )
}


