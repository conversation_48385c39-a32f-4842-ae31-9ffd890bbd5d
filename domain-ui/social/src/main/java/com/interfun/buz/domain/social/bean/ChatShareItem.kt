package com.interfun.buz.domain.social.bean

import com.interfun.buz.base.ktx.asString
import com.interfun.buz.common.R
import com.interfun.buz.domain.im.social.entity.UserType

/**
 * Author: ChenYouSheng
 * Date: 2025/7/4
 * Email: chenyo<PERSON><PERSON>@vocalbeats.com
 * Desc:
 */
sealed interface ChatShareItem {

    val shareState: ShareState

    data class GroupShareItem(
        val groupId: Long,
        val groupName: String,
        val groupPortrait: String,
        val firstFewPortraits: List<String?>,
        val groupMemberCount: Int,
        override val shareState: ShareState = ShareState.Normal
    ) : ChatShareItem

    data class UserShareItem(
        val userId: Long,
        val userName: String,
        val displayName: String,
        val realFullName: String, // 发送出去需要用firstName+lastName
        val userPortrait: String,
        val userType: UserType,
        val buzId: String,
        override val shareState: ShareState = ShareState.Normal
    ) : ChatShareItem
}


sealed interface ShareState {
    object Normal : ShareState
    object Loading : ShareState
    object Sent : ShareState
}

fun ChatShareItem.UserShareItem.displayBuzId(): String {
    return if (buzId.isNotEmpty()) R.string.common_symbol_at.asString() + buzId else ""
}

fun ChatShareItem.targetId(): Long {
    return when (this) {
        is ChatShareItem.GroupShareItem -> groupId
        is ChatShareItem.UserShareItem -> userId
    }
}

fun ChatShareItem.isTheSame(other: ChatShareItem): Boolean {
    return when (this) {
        is ChatShareItem.GroupShareItem -> {
            other is ChatShareItem.GroupShareItem && groupId == other.groupId
        }

        is ChatShareItem.UserShareItem -> {
            other is ChatShareItem.UserShareItem && userId == other.userId
        }
    }
}

fun ChatShareItem.copyState(state: ShareState): ChatShareItem {
    return when (this) {
        is ChatShareItem.GroupShareItem -> {
            copy(shareState = state)
        }

        is ChatShareItem.UserShareItem -> {
            copy(shareState = state)
        }
    }
}