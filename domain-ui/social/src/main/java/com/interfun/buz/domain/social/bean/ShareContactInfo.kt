package com.interfun.buz.domain.social.bean

import android.os.Parcelable
import com.interfun.buz.common.constants.ShareContactSource
import kotlinx.parcelize.Parcelize

/**
 * Author: ChenYouSheng
 * Date: 2025/7/7
 * Email: chen<PERSON><PERSON><PERSON>@vocalbeats.com
 * Desc:
 */
@Parcelize
data class ShareContactInfo(
    val id: Long, // groupId or userId
    val type: TargetType,
    val replyMsgId: Long? = null,
    val source: ShareContactSource
) : Parcelable {

    companion object {
        @Parcelize
        sealed class TargetType : Parcelable {
            @Parcelize
            object Group : TargetType()

            @Parcelize
            object User : TargetType()
        }
    }
}