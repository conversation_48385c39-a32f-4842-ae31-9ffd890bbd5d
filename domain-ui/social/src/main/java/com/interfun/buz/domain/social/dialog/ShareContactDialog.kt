package com.interfun.buz.domain.social.dialog

import android.os.Bundle
import androidx.compose.runtime.Composable
import androidx.lifecycle.viewmodel.compose.viewModel
import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.common.constants.RouterParamKey
import com.interfun.buz.compose.components.dialog.BaseComposeDialogFragment
import com.interfun.buz.domain.social.bean.ShareContactInfo
import com.interfun.buz.domain.social.components.ShareContactScreen
import com.interfun.buz.domain.social.viewmodel.ShareContactViewModel
import dagger.hilt.android.AndroidEntryPoint

/**
 * Author: ChenYouSheng
 * Date: 2025/7/3
 * Email: <EMAIL>
 * Desc: 从群资料页/好友资料页入口，将当前群或者好友分享到第三方或者会话聊天页
 */
@AndroidEntryPoint
class ShareContactDialog : BaseComposeDialogFragment() {

    companion object {
        const val TAG = "ShareContactDialog"

        fun newInstance(info: ShareContactInfo): ShareContactDialog {
            return ShareContactDialog().apply {
                arguments = Bundle().apply {
                    putParcelable(RouterParamKey.ShareContact.KEY_SHARE_INFO, info)
                }
            }
        }
    }

    @Composable
    override fun DialogContent() {
        ShareContactScreen(onDismissRequest = { dismissAllowingStateLoss() })
    }
}