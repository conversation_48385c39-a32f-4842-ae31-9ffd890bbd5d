package com.interfun.buz.domain.social.helper

import android.content.Context
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.ktx.shareChooser
import com.interfun.buz.common.service.IBuzSharedService
import com.interfun.buz.common.service.Result
import com.interfun.buz.common.utils.shareTextBySystemDialog
import com.interfun.buz.domain.im.social.entity.ShareType
import com.interfun.buz.domain.social.R
import com.interfun.buz.domain.social.bean.PackageName
import com.yibasan.lizhifm.sdk.platformtools.MobileUtils.isAppInstalled

object ShareHelper {
    fun shareText(context: Context, shareType: ShareType, msg: String) {
        when (shareType) {
            ShareType.SYSTEM_SHARE -> {
                shareTextBySystemDialog(context, msg)
            }

            ShareType.QR_CODE -> {
                //Todo ShareQRCodeActivity 把这个activity里面的参数减少一些，只传id就行，目前还没有用到，先不实现
            }

            ShareType.COPY_LINK -> {
                msg.copyToClipboard()
                toast(R.string.ftue_v3_linkCopied.asString())
            }

            ShareType.MESSAGE -> shareChooser.sms.shareText(context, msg)
                .handleResult("Message")

            ShareType.INSTAGRAM -> shareChooser.instagram.chatDestination.shareText(context, msg)
                .handleResult("Instagram")

            ShareType.MESSENGER -> shareChooser.messenger.shareText(context, msg)
                .handleResult("Messenger")

            ShareType.SNAPCHAT -> shareChooser.snapchat.shareText(context, msg)
                .handleResult("Snapchat")

            ShareType.TELEGRAM -> shareChooser.telegram.shareTextAndUrl(context, msg, null)
                .handleResult("Telegram")

            ShareType.LINE -> shareChooser.line.shareText(context, msg)
                .handleResult("Line")

            ShareType.WHATSAPP -> shareChooser.whatsapp.shareText(context, msg)
                .handleResult("WhatsApp")

            else -> {}
        }
    }

    suspend fun shareVideo(context: Context, shareType: ShareType, videoUrl: String, excludeSelf: Boolean = true) {
        val activity = context.activity ?: return
        val shareLink = obtainShareLink(shareType)
        routerServices<IBuzSharedService>().value?.shareVideoFromUrl(
            activity = activity,
            url = videoUrl,
            shareToApp = shareLink,
            excludeSelf = excludeSelf
        )
    }
    suspend fun shareVideoByFile(context: Context, shareType: ShareType, videoPath: String, excludeSelf: Boolean = true): Result? {
        val activity = context.activity ?: return null
        val shareLink = obtainShareLink(shareType)
        return routerServices<IBuzSharedService>().value?.shareMedia(
            activity = activity,
            mediaFilePath = videoPath,
            mimeTypes = "video/*",
            shareToApp = shareLink,
            excludeSelf = excludeSelf
        )
    }

    suspend fun shareImage(context: Context, shareType: ShareType, imageUrl: String, excludeSelf: Boolean = true) {
        val activity = context.activity ?: return
        val shareLink = obtainShareLink(shareType)
        routerServices<IBuzSharedService>().value?.shareImageFromUrl(
            activity = activity,
            url = imageUrl,
            shareToApp = shareLink,
            excludeSelf = excludeSelf
        )
    }

    suspend fun shareImageByFile(context: Context, shareType: ShareType, imagePath: String, excludeSelf: Boolean = true): Result? {
        val activity = context.activity ?: return null
        val shareLink = obtainShareLink(shareType)
        return routerServices<IBuzSharedService>().value?.shareMedia(
            activity = activity,
            mediaFilePath = imagePath,
            mimeTypes = "image/*",
            shareToApp = shareLink,
            excludeSelf = excludeSelf
        )
    }

    private fun Boolean.handleResult(platform: String) {
        if (this.not()) {
            toast(appStringContext.getString(R.string.contacts_share_failed_tips, platform))
        }
    }
}

private fun obtainShareLink(shareType: ShareType): String? {
    val shareLink = when (shareType) {
        ShareType.TIKTOK -> {
            when {
                isAppInstalled(PackageName.TIKTOK_T_PACKAGE_NAME) -> PackageName.TIKTOK_T_PACKAGE_NAME
                isAppInstalled(PackageName.TIKTOK_M_PACKAGE_NAME) -> PackageName.TIKTOK_M_PACKAGE_NAME
                else -> null
            }
        }
        ShareType.INSTAGRAM -> PackageName.INSTAGRAM_PACKAGE_NAME
        ShareType.X -> PackageName.X_PACKAGE_NAME
        ShareType.FACEBOOK -> PackageName.FACEBOOK_PACKAGE_NAME
        ShareType.LINE -> PackageName.LINE_PACKAGE_NAME
        ShareType.WHATSAPP -> {
            when {
                isAppInstalled(PackageName.WHATSAPP_PACKAGE_NAME) -> PackageName.WHATSAPP_PACKAGE_NAME
                isAppInstalled(PackageName.WHATSAPP_BUSINESS_PACKAGE_NAME) -> PackageName.WHATSAPP_BUSINESS_PACKAGE_NAME
                else -> null
            }
        }
        ShareType.MESSENGER -> PackageName.MESSENGER_PACKAGE_NAME
        ShareType.TELEGRAM -> PackageName.TELEGRAM_PACKAGE_NAME
        ShareType.MESSAGE -> PackageName.getMessagePackageName()
        ShareType.DISCORD -> PackageName.DISCORD_PACKAGE_NAME
        ShareType.VIBER -> PackageName.VIBER_PACKAGE_NAME
        ShareType.SNAPCHAT -> PackageName.SNAPCHAT_PACKAGE_NAME
        else -> null
    }
    return shareLink
}