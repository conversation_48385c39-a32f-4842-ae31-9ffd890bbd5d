package com.interfun.buz.domain.social.dialog

import android.os.Bundle
import androidx.compose.runtime.Composable
import com.interfun.buz.common.constants.RouterParamKey
import com.interfun.buz.compose.components.dialog.BaseComposeDialogFragment
import com.interfun.buz.domain.social.bean.ShareContactInfo
import com.interfun.buz.domain.social.components.ShareFriendScreen
import dagger.hilt.android.AndroidEntryPoint

/**
 * Author: ChenYouSheng
 * Date: 2025/7/9
 * Email: <EMAIL>
 * Desc: 将好友名片分享到个人或者群中
 */
@AndroidEntryPoint
class ShareFriendDialog : BaseComposeDialogFragment() {

    companion object {
        const val TAG = "ShareFriendDialog"

        fun newInstance(info: ShareContactInfo): ShareFriendDialog {
            return ShareFriendDialog().apply {
                arguments = Bundle().apply {
                    putParcelable(RouterParamKey.ShareContact.KEY_SHARE_INFO, info)
                }
            }
        }
    }

    @Composable
    override fun DialogContent() {
        ShareFriendScreen(onDismissRequest = {
            dismissAllowingStateLoss()
        })
    }
}