package com.interfun.buz.domain.social.components

import android.annotation.SuppressLint
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.interfun.buz.compose.components.IconFontText
import com.interfun.buz.compose.components.InitPreview
import com.interfun.buz.compose.ktx.HorizontalSpace
import com.interfun.buz.compose.ktx.VerticalSpace
import com.interfun.buz.compose.ktx.asColor
import com.interfun.buz.compose.ktx.asDimension
import com.interfun.buz.compose.ktx.asString
import com.interfun.buz.compose.styles.TextStyles
import com.interfun.buz.domain.im.social.entity.ShareItemBean
import com.interfun.buz.domain.im.social.entity.ShareType
import com.interfun.buz.domain.social.R
import com.interfun.buz.domain.social.helper.getShareItemBeanList
import kotlinx.collections.immutable.PersistentList
import kotlinx.collections.immutable.persistentListOf

@Deprecated(message = "请使用HorizontalShareListScreenNew，时间不够就没移动过去")
@Composable
fun HorizontalShareListScreen(
    list: List<ShareType>,
    contentPadding: Dp = R.dimen.default_share_item_icon_padding.asDimension(),
    onItemClick: (ShareItemBean) -> Unit
) {
    LazyRow(contentPadding = PaddingValues(horizontal = contentPadding)) {
        getShareItemBeanList(list).forEach {
            item {
                HorizontalShareItem(
                    item = it,
                    onItemClick = onItemClick
                )
            }
        }
    }
}
@Composable
fun HorizontalShareListScreenNew(
    list: PersistentList<ShareType>,
    modifier: Modifier= Modifier,
    addBorder: Boolean = true,
    contentPadding: Dp = R.dimen.default_share_item_icon_padding.asDimension(),
    onItemClick: (ShareItemBean) -> Unit
) {
    LazyRow(modifier = modifier,contentPadding = PaddingValues(horizontal = contentPadding), horizontalArrangement = Arrangement.Start) {
        val shareItemBeanList = getShareItemBeanList(list)
        items(shareItemBeanList.size, key={shareItemBeanList[it].type}) { itemIndex->
            HorizontalShareItem(shareItemBeanList[itemIndex], addBorder, onItemClick)
        }
    }
}
@Preview
@Composable
fun HorizontalShareListScreenNew(){
    InitPreview()
    HorizontalShareListScreenNew(modifier = Modifier
        .fillMaxWidth()
        .background(Color.Red),list = persistentListOf(ShareType.SYSTEM_SHARE,ShareType.TELEGRAM,ShareType.WHATSAPP,ShareType.DOWN_LOAD),onItemClick = {})
}


@Composable
fun VerticalShareListScreen(list: List<ShareType>, onItemClick: (ShareItemBean) -> Unit) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .wrapContentHeight()
    ) {
        getShareItemBeanList(list).forEach {
            VerticalShareItem(it, onItemClick)
        }
    }
}

@Composable
private fun HorizontalShareItem(
    item: ShareItemBean,
    addBorder: Boolean = true,
    onItemClick: (ShareItemBean) -> Unit
) {
    Column(
        Modifier
            .clickable { onItemClick.invoke(item) },
        horizontalAlignment = Alignment.CenterHorizontally
    ) {

        HorizontalShareItemIcon(item, this, addBorder)
        VerticalSpace(10.dp)
        Text(
            modifier = Modifier
                .wrapContentWidth()
                .wrapContentHeight()
                .align(Alignment.CenterHorizontally),
            text = item.shareText,
            color = R.color.color_text_white_secondary.asColor(),
            style = TextStyles.labelSmall(),
        )
    }
}

@Composable
private fun Modifier.showBorder(showBorder: Boolean):Modifier{

   return this.then(
        if (showBorder) {
            Modifier.border(1.dp, R.color.color_outline_1_default.asColor(), CircleShape)
        }else{
            Modifier
        }
    )

}
@Composable
private fun HorizontalShareItemIcon(item: ShareItemBean, scope: ColumnScope, addBorder: Boolean) {
    val borderWidth = if (addBorder) 1.dp else 0.dp
    scope.apply {
        when(item){
            is ShareItemBean.ThirdPartyShareItemBean -> {
                Image(
                    modifier = Modifier
                        .padding(horizontal = 10.dp)
                        .showBorder(addBorder)
                        .size(48.dp)
                        .clip(CircleShape),
                    painter = painterResource(id = item.iconRes),
                    contentDescription = "",
                )
            }

            is ShareItemBean.NormalShareItemBean -> {
                Box(
                    modifier = Modifier
                        .padding(horizontal = 10.dp)
                        .showBorder(addBorder)
                        .size(48.dp)
                        .background(color = item.backgroundColor.asColor(), shape = CircleShape),
                    contentAlignment = Alignment.Center
                ) {
                    IconFontText(
                        modifier = Modifier,
                        iconText = item.iconText,
                        iconColor = item.iconColor.asColor(),
                        iconSize = 20.dp
                    )
                }
            }
        }
    }
}

@Composable
private fun VerticalShareItem(
    item: ShareItemBean,
    onItemClick: (ShareItemBean) -> Unit
) {
    Row (
        Modifier
            .fillMaxWidth()
            .wrapContentHeight()
            .padding(horizontal = 20.dp, vertical = 8.dp)
            .clickable { onItemClick.invoke(item) },
        verticalAlignment = Alignment.CenterVertically
    ) {
        VerticalShareItemIcon(item,this)
        HorizontalSpace(16.dp)
        Text(
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight()
                .weight(1f),
            text = item.shareText,
            color = R.color.color_text_white_primary.asColor(),
            style = TextStyles.labelLarge(),
            textAlign = TextAlign.Start
        )

        HorizontalSpace(16.dp)

        IconFontText(
            iconText = R.string.ic_share.asString(),
            iconColor = R.color.color_text_white_primary.asColor(),
            iconSize = 16.dp
        )    }
}

@Composable
private fun VerticalShareItemIcon(item: ShareItemBean,scope: RowScope){
    scope.apply {
        when(item){
            is ShareItemBean.NormalShareItemBean -> {
                Box(
                    modifier = Modifier
                        .size(48.dp)
                        .border(1.dp, R.color.color_outline_1_default.asColor(), CircleShape)
                        .padding(1.dp)
                        .background(color = item.backgroundColor.asColor(), shape = CircleShape),
                    contentAlignment = Alignment.Center
                ) {
                    IconFontText(
                        modifier = Modifier,
                        iconText = item.iconText,
                        iconColor = item.iconColor.asColor(),
                        iconSize = 20.dp
                    )
                }
            }

            is ShareItemBean.ThirdPartyShareItemBean -> {
                Image(
                    modifier = Modifier
                        .size(48.dp)
                        .border(1.dp, R.color.color_outline_1_default.asColor(), CircleShape)
                        .padding(1.dp)
                        .clip(CircleShape),
                    painter = painterResource(id = item.iconRes),
                    contentDescription = "",
                )
            }

        }
    }
}

@SuppressLint("ResourceAsColor")
@Preview
@Composable
fun PreviewHorizontalShareItem() {
    val normalItem = ShareItemBean.NormalShareItemBean(
        R.string.ic_share.asString(),
        R.color.color_000000,
        R.color.basic_primary,
        R.string.share.asString(),
        ShareType.SYSTEM_SHARE
    )

    HorizontalShareItem(normalItem) { }
}

@SuppressLint("ResourceAsColor")
@Preview
@Composable
fun PreviewVerticalShareItem() {
    val normalItem = ShareItemBean.NormalShareItemBean(
        R.string.ic_share.asString(),
        R.color.color_000000,
        R.color.basic_primary,
        R.string.share.asString(),
        ShareType.SYSTEM_SHARE
    )

    VerticalShareItem(normalItem) { }
}


