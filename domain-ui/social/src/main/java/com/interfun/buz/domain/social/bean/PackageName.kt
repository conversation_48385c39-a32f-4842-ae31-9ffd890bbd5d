package com.interfun.buz.domain.social.bean

import android.content.Context
import android.provider.Telephony
import com.interfun.buz.base.ktx.appContext

object PackageName {
    const val TIKTOK_M_PACKAGE_NAME = "com.zhiliaoapp.musically"       // TikTok Global (old)
    const val TIKTOK_T_PACKAGE_NAME = "com.ss.android.ugc.trill"       // TikTok Global (newer)
    const val INSTAGRAM_PACKAGE_NAME = "com.instagram.android"         // Instagram
    const val X_PACKAGE_NAME = "com.twitter.android"                   // X (Twitter)
    const val FACEBOOK_PACKAGE_NAME = "com.facebook.katana"            // Facebook
    const val MESSENGER_PACKAGE_NAME = "com.facebook.orca"             // Messenger
    const val LINE_PACKAGE_NAME = "jp.naver.line.android"              // LINE
    const val WHATSAPP_PACKAGE_NAME = "com.whatsapp"                   // WhatsApp (Main)
    const val WHATSAPP_BUSINESS_PACKAGE_NAME = "com.whatsapp.w4b"      // WhatsApp (Business)
    const val TELEGRAM_PACKAGE_NAME = "org.telegram.messenger"         // Telegram
    const val DISCORD_PACKAGE_NAME = "com.discord"                     // Discord
    const val VIBER_PACKAGE_NAME = "com.viber.voip"                    // Viber
    const val SNAPCHAT_PACKAGE_NAME = "com.snapchat.android"           // Snapchat

    fun getMessagePackageName(context: Context = appContext): String? {
        return Telephony.Sms.getDefaultSmsPackage(context)
    }
}