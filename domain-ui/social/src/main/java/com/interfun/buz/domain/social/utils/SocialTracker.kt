package com.interfun.buz.domain.social.utils

import com.interfun.buz.base.ktx.topActivity
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.constants.ShareContactSource
import com.interfun.buz.common.constants.TrackConstant
import com.interfun.buz.common.ktx.getBooleanDefault
import com.interfun.buz.common.service.ChatService
import com.interfun.buz.common.utils.BuzTracker
import com.interfun.buz.domain.im.social.entity.ContactType
import com.interfun.buz.domain.im.social.entity.UserType
import com.lizhi.component.basetool.ntp.NtpTime
import com.lizhi.im5.sdk.conversation.IM5ConversationType

/**
 * Author: ChenYouSheng
 * Date: 2025/7/16
 * Email: <EMAIL>
 * Desc:
 */
object SocialTracker {

    /**
     * when user click share in the contact seletion page after select the contact
     */
    fun onClickAC2025071002(
        isPrivate: <PERSON><PERSON><PERSON>, targetId: Long, source: ShareContactSource
    ) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2025071002")
            put(TrackConstant.KEY_TITLE, "chat")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "share_contact_inapp")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, if (isPrivate) "private" else "group")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, "$targetId")
            put(
                TrackConstant.KEY_SOURCE,
                if (source == ShareContactSource.Home) "homepage" else "chat_history"
            )
        }
    }

    /**
     * when user click share contact / share group in other's profile or group profile
     */
    fun onClickAC2025071003(
        targetId: Long,
        source: ShareContactSource
    ) {
        val pageType = when (source) {
            ShareContactSource.UserProfile -> "private"
            ShareContactSource.GroupProfile -> "group"
            ShareContactSource.AiProfile -> "robot"
            ShareContactSource.OfficialProfile -> "OA"
            else -> ""
        }
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2025071003")
            put(TrackConstant.KEY_TITLE, "profile_page")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "share_profile_contact")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, pageType)
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, "$targetId")
        }
    }

    /**
     * when user click share selection to chats in the share to page
     */
    fun onClickAC2025071004(
        targetId: Long,
        source: ShareContactSource
    ) {
        val pageType = when (source) {
            ShareContactSource.UserProfile -> "private"
            ShareContactSource.GroupProfile -> "group"
            ShareContactSource.AiProfile -> "robot"
            ShareContactSource.OfficialProfile -> "OA"
            else -> ""
        }
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2025071004")
            put(TrackConstant.KEY_TITLE, "chat")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "share_contact_to_chat")
            put(TrackConstant.KEY_PAGE_TYPE, "contact_share_to_page")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, pageType)
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, "$targetId")
        }
    }

    /**
     * when user click the contact card in chat
     */
    fun onClickAC2025071005(
        targetId: Long,
        userType: Int,
        contactType: Int,
        convType: IM5ConversationType
    ) {
        val content = if (contactType == ContactType.Group.type) {
            "group"
        } else {
            when (userType) {
                UserType.Normal.type -> "private"
                UserType.AIBot.type -> "robot"
                UserType.Official.type, UserType.UserResearch.type -> "OA"
                else -> ""
            }
        }
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2025071005")
            put(TrackConstant.KEY_TITLE, "chat")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "check_contact_message")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(
                TrackConstant.KEY_PAGE_BUSINESS_TYPE,
                if (convType == IM5ConversationType.PRIVATE) "private" else "group"
            )
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, "$targetId")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT, content)
        }
    }


    /**
     * when user click share selection to external applpication in the share to page
     */
    fun onClickAC2025071006(
        targetId: Long,
        source: ShareContactSource,
        platformName: String
    ) {
        val pageType = when (source) {
            ShareContactSource.UserProfile -> "private"
            ShareContactSource.GroupProfile -> "group"
            ShareContactSource.AiProfile -> "robot"
            ShareContactSource.OfficialProfile -> "OA"
            else -> ""
        }
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2025071006")
            put(TrackConstant.KEY_TITLE, "chat")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "share_contact_to_external")
            put(TrackConstant.KEY_PAGE_TYPE, "contact_share_to_page")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, pageType)
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, "$targetId")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT, platformName)
        }
    }

    /**
     * when the contact share to page is being exposed to user
     */
    fun onPageExposeAVS2025071001(
        targetId: Long,
        source: ShareContactSource
    ) {
        val pageType = when (source) {
            ShareContactSource.UserProfile -> "private"
            ShareContactSource.GroupProfile -> "group"
            ShareContactSource.AiProfile -> "robot"
            ShareContactSource.OfficialProfile -> "OA"
            else -> ""
        }
        BuzTracker.onPageViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AVS2025071001")
            put(TrackConstant.KEY_TITLE, "chat")
            put(TrackConstant.KEY_PAGE_TYPE, "contact_share_to_page")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, pageType)
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, "$targetId")
        }
    }

    /**
     * when user send contact
     */
    fun onResultRB2025071001(
        isPrivate: Boolean,
        targetId: String,
        traceId: String,
        startTime: String,
        isCancel: Boolean = false,
        errorType: Int? = null,
        errorCode: Int? = null
    ) {
        val isInHome =
            routerServices<ChatService>().value?.isChatHomeActivity(topActivity).getBooleanDefault()
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2025071001")
            put(TrackConstant.KEY_RESULT_TYPE, "send_contact")
            put(TrackConstant.KEY_PAGE_TYPE, "contact")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, if (isPrivate) "private" else "group")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, targetId)
            put(TrackConstant.KEY_SOURCE, if (isInHome) "homepage" else "chat_history")
            put(TrackConstant.KEY_CONTENT_ID, traceId)
            put(
                TrackConstant.KEY_ELEMENT_BUSINESS_TYPE,
                if (errorType != null) "$errorType" else ""
            )
            if (isCancel) {
                put(TrackConstant.KEY_FAIL_REASON, "cancel")
                put(TrackConstant.KEY_IS_SUCCESS, "fail")
            } else if (errorCode != null) {
                put(TrackConstant.KEY_FAIL_REASON, "$errorCode")
                put(TrackConstant.KEY_IS_SUCCESS, "fail")
            } else {
                put(TrackConstant.KEY_FAIL_REASON, "")
                put(TrackConstant.KEY_IS_SUCCESS, "success")
            }
            put(TrackConstant.KEY_LOG_TIME, startTime)
            put(TrackConstant.KEY_BUSINESS_NUM, NtpTime.nowForce())
        }
    }
}