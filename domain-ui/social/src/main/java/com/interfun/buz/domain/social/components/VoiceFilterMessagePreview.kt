package com.interfun.buz.domain.social.components

import android.content.Context
import android.graphics.Paint
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.viewinterop.AndroidView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.text.buildSpannedString
import coil.imageLoader
import coil.target.Target
import com.interfun.buz.base.ktx.appendSpace
import com.interfun.buz.base.ktx.asColor
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.base.ktx.backgroundColor
import com.interfun.buz.base.ktx.dp
import com.interfun.buz.base.ktx.dpFloat
import com.interfun.buz.base.ktx.gone
import com.interfun.buz.base.ktx.iconFontAlign
import com.interfun.buz.base.ktx.layoutMargin
import com.interfun.buz.base.ktx.layoutMarginEnd
import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.base.ktx.logError
import com.interfun.buz.base.ktx.typeface
import com.interfun.buz.base.ktx.visible
import com.interfun.buz.common.ktx.FontUtil
import com.interfun.buz.common.ktx.portrait
import com.interfun.buz.common.ktx.setStyleBodyLarge
import com.interfun.buz.common.ktx.setStyleTitleMedium
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.widget.portrait.PortraitUtil
import com.interfun.buz.common.widget.portrait.group.GroupPortraitConstants
import com.interfun.buz.compose.components.InitPreview
import com.interfun.buz.domain.social.R
import com.interfun.buz.domain.social.databinding.ChatVoiceFilterMessagePreviewBinding
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.withTimeoutOrNull

/**
 * 图片加载状态枚举
 */
enum class ImageLoadState {
    /** 未开始加载 */
    INITIAL,

    /** 加载中 */
    LOADING,

    /** 加载成功 */
    SUCCESS,

    /** 加载失败 */
    ERROR
}

/**
 * 图片加载状态监听器接口
 */
interface ImageLoadStateListener {
    /**
     * 图片加载状态变化回调
     * @param state 当前加载状态
     * @param url 图片URL
     * @param error 错误信息（仅在ERROR状态时有值）
     */
    fun onImageLoadStateChanged(state: ImageLoadState, url: String?, error: Throwable? = null)
}

class VoiceFilterMessagePreview @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
) : ConstraintLayout(context, attrs, defStyleAttr) {

    companion object {
        private const val TAG = "VoiceFilterMessagePreview"

        /** 头像尺寸 34dp */
        private const val PORTRAIT_SIZE_DP = 34

        /** 默认超时时间 10秒 */
        private const val DEFAULT_TIMEOUT_MS = 10_000L
    }

    private val lottiePlayingVoiceMsg = "lottie/voice_msg_playing.json"
    private val binding =
        ChatVoiceFilterMessagePreviewBinding.inflate(LayoutInflater.from(context), this)

    /** 当前图片加载状态 */
    private var currentImageLoadState: ImageLoadState = ImageLoadState.INITIAL

    /** 图片加载状态监听器 */
    private var imageLoadStateListener: ImageLoadStateListener? = null

    /** 用于协程等待的 CompletableDeferred */
    private var imageLoadDeferred: CompletableDeferred<ImageLoadState>? = null

    init {
        binding.animPlaying.apply {
            setLoop(true)
            setCenterCropScaleType()
            loadAnim(lottiePlayingVoiceMsg) {
                setFrame(0)
                stopAnim()
            }
        }
    }

    fun initPreview(
        previewType: VoiceFilterMessagePreviewType,
        recordDuration: String,
        filterName: String?,
        bubbleStyle: Int,
        asrText: String? = null,
        backgroundColor: Int?,
        microphoneColor: Int?,
    ) {
        setBubbleStyle(bubbleStyle, backgroundColor, microphoneColor)
        setPortrait()
        setTime(recordDuration)
        setVoiceFilterName(filterName)
        setAsrText(asrText)
        setPreviewType(previewType)
    }

    private fun setPreviewType(previewType: VoiceFilterMessagePreviewType) {
        binding.apply {
            when (previewType) {
                VoiceFilterMessagePreviewType.EXPORT_VIDEO -> {
                    (clMessage.layoutParams as ConstraintLayout.LayoutParams).apply {
                        topToTop = binding.anchorVideoExport.id
                        marginEnd = EXPORT_VIDEO_MARGIN_BETWEEN
                        matchConstraintMinWidth = 552
                        matchConstraintMaxWidth = 751
                        bottomToBottom = binding.anchorVideoExport.id
                        marginStart = EXPORT_VIDEO_LEFT_MIN_MARGIN
                        clMessage.layoutParams = this
                    }

                    (vPortraitStroke.layoutParams as ConstraintLayout.LayoutParams).apply {
                        height = 121
                        width = 121
                        marginEnd = EXPORT_VIDEO_RIGHT_MIN_MARGIN
                        vPortraitStroke.setRadius(121 / 2f)
                        vPortraitStroke.layoutParams = this
                    }

                    (userPortraitIV.layoutParams as ConstraintLayout.LayoutParams).apply {
                        this.height = 98
                        this.width = 98
                        val marginSize = 0
                        bottomMargin = marginSize
                        topMargin = marginSize
                        marginStart = marginSize
                        marginEnd = marginSize
                        userPortraitIV.layoutParams = this
                    }
                    tvVoiceAsr.maxLines = 8

                    // 实现智能布局逻辑：容器宽度1080px，高度1920px
                    // 约束条件：clMessage距离左边≥144px，vPortraitStroke距离右边≥23px
                    // 目标：在55%位置（594px）放置组合中心点，userPortraitIV相对vPortraitStroke中心距离4dp
//                    applySmartLayout()

                }

                VoiceFilterMessagePreviewType.PREVIEW_VIDEO -> {
                    clMessage.layoutMarginEnd(8.dp)
                    tvVoiceAsr.maxLines = 5
                }


            }
        }
    }

    /**
     * 设置用户头像
     * 从 UserSessionManager 获取头像URL，使用 Coil 加载到 ImageView 中
     * 支持状态监听和协程等待，适用于离屏渲染场景
     */
    private fun setPortrait() {
        val portraitUrl = UserSessionManager.portrait
        logDebug(TAG, "setPortrait: portraitUrl = $portraitUrl")

        if (portraitUrl.isNullOrEmpty()) {
            // 头像URL为空，设置为加载失败状态
            updateImageLoadState(
                ImageLoadState.ERROR,
                portraitUrl,
                IllegalArgumentException("Portrait URL is null or empty")
            )
            return
        }

        // 设置加载中状态
        updateImageLoadState(ImageLoadState.LOADING, portraitUrl)

        // 创建自定义 Target，适用于离屏渲染场景
        val customTarget = object : Target {
            override fun onStart(placeholder: Drawable?) {
                logDebug(TAG, "Portrait loading started: $portraitUrl")
                // onStart 在这里不需要重复设置 LOADING 状态，因为上面已经设置了
            }

            override fun onSuccess(result: Drawable) {
                logDebug(TAG, "Portrait loading success: $portraitUrl")
                // 手动设置 ImageView 的图片
                binding.userPortraitIV.setImageDrawable(result)
                // 更新状态为成功
                updateImageLoadState(ImageLoadState.SUCCESS, portraitUrl)
            }

            override fun onError(error: Drawable?) {
                logError(TAG, "Portrait loading error: $portraitUrl")
                // 设置默认头像（如果有错误占位图）
                error?.let { binding.userPortraitIV.setImageDrawable(it) }
                // 更新状态为失败
                updateImageLoadState(
                    ImageLoadState.ERROR,
                    portraitUrl,
                    Exception("Image load failed")
                )
            }
        }

        // 构建图片请求，使用自定义 Target，并确保应用圆形变换
        val request = PortraitUtil.buildImageRequest(
            context = context,
            url = portraitUrl,
            size = PORTRAIT_SIZE_DP.dp,
            target = customTarget
        ).newBuilder()
            // 强制应用圆形变换，确保在离屏渲染场景下也能正确显示圆形头像
            .transformations(GroupPortraitConstants.circleCropTransformation)
            // 禁用硬件加速以确保变换正确应用
            .allowHardware(false)
            .listener(
                onStart = { request ->
                    logDebug(TAG, "ImageRequest onStart: ${request.data}")
                },
                onSuccess = { request, result ->
                    logDebug(TAG, "ImageRequest onSuccess: ${request.data}")
                },
                onError = { request, result ->
                    logError(
                        TAG,
                        "ImageRequest onError: ${request.data}, error: ${result.throwable.message}"
                    )
                    // 在 listener 的 onError 中也更新状态，确保错误被捕获
                    updateImageLoadState(ImageLoadState.ERROR, portraitUrl, result.throwable)
                }
            )
            .build()

        // 执行图片加载请求
        context.imageLoader.enqueue(request)
    }

    private fun setTime(time: String) {
        binding.tvTime.text = time
    }

    private fun setAsrText(asrText: String?) {
        if (asrText.isNullOrEmpty()) {
            binding.tvVoiceAsr.gone()
            binding.vAsrBackground.gone()
            binding.spacingBottom.gone()
            return
        }
        binding.tvVoiceAsr.text = asrText
        binding.tvVoiceAsr.visible()
        binding.vAsrBackground.visible()
        binding.spacingBottom.visible()
    }

    private fun setVoiceFilterName(voiceFilter: String?) {
        if (voiceFilter.isNullOrEmpty()) {
            binding.tvVoiceFilter.gone()
            return
        }
        binding.tvVoiceFilter.text = buildSpannedString {
            iconFontAlign {
                typeface(FontUtil.fontIcon!!) {
                    append(R.string.ic_wt_speaking.asString())
                }
            }
            appendSpace(1.dp)
            append(voiceFilter)
        }
        binding.tvVoiceFilter.visible()
    }

    private fun setBubbleStyle(
        bubbleStyle: Int,
        voiceFilterBackground: Int?,
        voiceFilterTextColor: Int?
    ) {
        val isFeatured = bubbleStyle == 1

        val backgroundColor = when {
            isFeatured && voiceFilterBackground != null -> voiceFilterBackground
            else -> R.color.color_foreground_highlight_default.asColor()
        }

        val viewBackground = when {
            isFeatured -> R.color.alpha_black_10
            else -> R.color.brand_90
        }.asColor()

        val asrBackground = when {
            isFeatured -> R.color.alpha_black_10
            else -> R.color.brand_90
        }.asColor()

        val asrTextColor = when {
            isFeatured -> R.color.color_text_white_important
            else -> R.color.color_text_black_primary
        }.asColor()

        val voiceTextColor = when {
            isFeatured && voiceFilterTextColor != null -> voiceFilterTextColor
            else -> R.color.color_text_black_primary.asColor()
        }

        val portraitStrokeColor = when {
            isFeatured && voiceFilterTextColor != null -> voiceFilterTextColor
            else -> R.color.transparent.asColor()
        }

        val strokeWidth = when {
            isFeatured -> 2.dpFloat
            else -> 0f
        }

        val portraitMargin = when {
            isFeatured -> 4.dp
            else -> 0.dp
        }

        binding.apply {
            userPortraitIV.layoutMargin(portraitMargin)
            vPortraitStroke.backgroundColor(portraitStrokeColor)
            clMessage.backgroundColor(backgroundColor)
            vAsrBackground.backgroundColor(asrBackground)
            tvVoiceFilter.backgroundColor(viewBackground)
            tvVoiceFilter.setTextColor(voiceTextColor)
            tvTime.setTextColor(voiceTextColor)
            animPlaying.setColor(voiceTextColor)
            tvVoiceAsr.apply {
                if (isFeatured) setStyleTitleMedium() else setStyleBodyLarge()
                setTextColor(asrTextColor)
                setStrokeWidth(strokeWidth)
                setStrokeType(Paint.Join.ROUND)
                setStrokeColor(voiceTextColor)
                setOuterStroke(true)
            }
        }
    }

    /**
     * 更新图片加载状态
     * @param newState 新的加载状态
     * @param url 图片URL
     * @param error 错误信息（可选）
     */
    private fun updateImageLoadState(
        newState: ImageLoadState,
        url: String?,
        error: Throwable? = null,
    ) {
        if (currentImageLoadState != newState) {
            currentImageLoadState = newState

            // 通知监听器
            imageLoadStateListener?.onImageLoadStateChanged(newState, url, error)

            // 完成协程等待
            imageLoadDeferred?.complete(newState)

            logDebug(TAG, "Image load state updated: $newState, url: $url")
        }
    }

    /**
     * 设置图片加载状态监听器
     * @param listener 状态监听器
     */
    fun setImageLoadStateListener(listener: ImageLoadStateListener?) {
        this.imageLoadStateListener = listener
    }

    /**
     * 获取当前图片加载状态
     * @return 当前加载状态
     */
    fun getCurrentImageLoadState(): ImageLoadState {
        return currentImageLoadState
    }

    /**
     * 等待图片加载完成的挂起函数
     * @param timeoutMs 超时时间（毫秒），默认10秒
     * @return 最终的加载状态，超时返回null
     */
    suspend fun waitForImageLoad(timeoutMs: Long = DEFAULT_TIMEOUT_MS): ImageLoadState? {
        // 如果已经加载完成（成功或失败），直接返回当前状态
        if (currentImageLoadState == ImageLoadState.SUCCESS || currentImageLoadState == ImageLoadState.ERROR) {
            return currentImageLoadState
        }

        // 创建新的 CompletableDeferred 用于等待
        imageLoadDeferred = CompletableDeferred()

        return try {
            withTimeoutOrNull(timeoutMs) {
                imageLoadDeferred?.await()
            }
        } catch (e: Exception) {
            logError(TAG, "waitForImageLoad error: ${e.message}")
            null
        } finally {
            imageLoadDeferred = null
        }
    }

    var isPlaying = false

    /**
     * 开始播放动画
     * 用于响应VoiceFilterPreviewState.isPlaying为true时的状态变化
     */
    fun startAnimation() {
        if (isPlaying) {
            return
        }
        binding.animPlaying.playAnim()
        isPlaying = true
    }

    /**
     * 停止播放动画
     * 用于响应VoiceFilterPreviewState.isPlaying为false时的状态变化
     */
    fun stopAnimation() {
        isPlaying = false
        binding.animPlaying.apply {
            stopAnim()
            setFrame(0) // 重置到第一帧
        }
    }

    override fun onDetachedFromWindow() {
        binding.animPlaying.stopAnim()
        // 清理资源
        imageLoadStateListener = null
        imageLoadDeferred?.cancel()
        imageLoadDeferred = null
        super.onDetachedFromWindow()
    }

    private val DEFAULT_VIDEO_WIDTH = 1080
    private val DEFAULT_VIDEO_HEIGHT = 1920
    private val EXPORT_VIDEO_LEFT_MIN_MARGIN = 144
    private val EXPORT_VIDEO_RIGHT_MIN_MARGIN = 23
    private val EXPORT_VIDEO_MARGIN_BETWEEN = 40

    fun autoMeasure() {
        layoutParams = ViewGroup.LayoutParams(
            DEFAULT_VIDEO_WIDTH,
            DEFAULT_VIDEO_HEIGHT
        )
        measure(
            MeasureSpec.makeMeasureSpec(DEFAULT_VIDEO_WIDTH, MeasureSpec.EXACTLY),
            MeasureSpec.makeMeasureSpec(DEFAULT_VIDEO_HEIGHT, MeasureSpec.EXACTLY)
        )
        layout(0, 0, measuredWidth, measuredHeight)
    }
}

enum class VoiceFilterMessagePreviewType {
    EXPORT_VIDEO,
    PREVIEW_VIDEO
}

@Composable
@Preview
private fun previewVoiceFilterMessagePreview() {
    InitPreview()
    Box(
        modifier = Modifier
            .background(androidx.compose.ui.graphics.Color.Red)
            .fillMaxSize()
    ) {
        AndroidView(factory = { context ->
            VoiceFilterMessagePreview(context).apply {
                initPreview(
                    VoiceFilterMessagePreviewType.EXPORT_VIDEO,
                    "00:00",
                    "滤镜名称",
                    0,
                    "ASR文本ASR文ASR文本ASR文ASR文本ASR文ASR文本ASR文ASR文本ASR文ASR文本ASR文ASR文本ASR文ASR文本ASR文ASR文本ASR文ASR文本ASR文ASR文本ASR文ASR文本ASR文ASR文本ASR文ASR文本ASR文ASR文本ASR文",
                    null,
                    null
                )
                autoMeasure()
            }
        }, update = {

        })
    }
}