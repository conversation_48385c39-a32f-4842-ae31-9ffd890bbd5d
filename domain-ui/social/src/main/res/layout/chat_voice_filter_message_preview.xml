<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">



    <androidx.legacy.widget.Space
        android:id="@+id/anchorVideoExport"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.557" />


    <com.interfun.buz.base.widget.round.RoundConstraintLayout
        android:id="@+id/clMessage"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="14dp"
        android:background="@color/color_foreground_highlight_default"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toStartOf="@id/vPortraitStroke"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_min="92dp"
        app:round_bottom_left_radius="17dp"
        app:round_bottom_right_radius="4dp"
        app:round_top_left_radius="17dp"
        app:round_top_right_radius="17dp">

        <com.interfun.buz.base.widget.round.RoundTextView
            android:id="@+id/tvVoiceFilter"
            style="@style/text_label_small"
            android:layout_width="wrap_content"
            android:layout_height="20dp"
            android:layout_marginHorizontal="7dp"
            android:background="@color/alpha_black_10"
            android:gravity="center_vertical"
            android:paddingStart="4dp"
            android:paddingEnd="6dp"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="@id/tvTime"
            app:layout_constraintEnd_toStartOf="@id/tvTime"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/tvTime"
            app:round_radius="12dp"
            tools:text="関西弁"
            tools:textColor="#0E4892" />

        <TextView
            android:id="@+id/tvTime"
            style="@style/text_body_medium"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="4dp"
            android:layout_marginVertical="8dp"
            android:gravity="center_vertical|end"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toTopOf="@id/vAsrBackground"
            app:layout_constraintEnd_toStartOf="@id/animPlaying"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toEndOf="@+id/voiceFilter"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed"
            tools:text="0:06"
            tools:textColor="#0E4892" />

        <com.interfun.buz.base.widget.view.animContainer.AnimContainerView
            android:id="@+id/animPlaying"
            android:layout_width="14dp"
            android:layout_height="14dp"
            android:layout_marginEnd="12dp"
            app:layout_constraintBottom_toBottomOf="@id/tvTime"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintTop_toTopOf="@id/tvTime"
            tools:textColor="#0E4892" />

        <com.interfun.buz.base.widget.round.RoundView
            android:id="@+id/vAsrBackground"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginHorizontal="4dp"
            android:background="@color/alpha_black_10"
            app:layout_constraintBottom_toBottomOf="@id/tvVoiceAsr"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvTime"
            app:layout_constraintVertical_chainStyle="packed"
            app:round_bottom_left_radius="13dp"
            app:round_bottom_right_radius="2dp"
            app:round_top_left_radius="13dp"
            app:round_top_right_radius="13dp" />

        <com.interfun.buz.base.widget.view.StrokeTextView
            android:id="@+id/tvVoiceAsr"
            style="@style/text_title_medium"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="14"
            android:paddingHorizontal="10dp"
            android:paddingVertical="6dp"
            android:textColor="@color/color_text_white_important"
            app:layout_constraintBottom_toBottomOf="@id/vAsrBackground"
            app:layout_constraintEnd_toEndOf="@id/vAsrBackground"
            app:layout_constraintHeight_min="34dp"
            app:layout_constraintStart_toStartOf="@id/vAsrBackground"
            app:layout_constraintTop_toTopOf="@id/vAsrBackground"
            app:layout_constraintVertical_chainStyle="packed"
            app:layout_constraintWidth_min="84dp"
            app:strokeType="ROUND"
            app:strokeWidth="2dp"
            tools:strokeColor="#0E4892"
            tools:text="うちうちうちうちうちうちうち" />

        <View
            android:id="@+id/spacingBottom"
            android:layout_width="0dp"
            android:layout_height="4dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/vAsrBackground" />
    </com.interfun.buz.base.widget.round.RoundConstraintLayout>

    <com.interfun.buz.base.widget.round.RoundView
        android:id="@+id/vPortraitStroke"
        android:layout_width="42dp"
        android:layout_height="42dp"
        app:layout_constrainedWidth="true"
        android:background="@color/transparent"
        app:layout_constraintBottom_toBottomOf="@id/clMessage"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/clMessage"
        app:round_radius="21dp" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/userPortraitIV"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="4dp"
        app:layout_constraintBottom_toBottomOf="@id/vPortraitStroke"
        app:layout_constraintEnd_toEndOf="@id/vPortraitStroke"
        app:layout_constraintStart_toStartOf="@id/vPortraitStroke"
        app:layout_constraintTop_toTopOf="@id/vPortraitStroke"
        tools:background="@drawable/common_user_default_portrait_round" />

</merge>