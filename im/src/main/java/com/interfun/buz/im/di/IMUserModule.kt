package com.interfun.buz.im.di

import com.interfun.buz.component.hilt.UserComponent
import com.interfun.buz.component.hilt.UserQualifier
import com.interfun.buz.component.hilt.UserScope
import com.interfun.buz.im.IMUserEntry
import com.interfun.buz.im.IMUserEntryImpl
import com.interfun.buz.im.repo.IMFileDownloadRepository
import com.interfun.buz.im.repo.IMFileDownloadRepositoryImpl
import com.interfun.buz.im.repo.IMFileUploadRepository
import com.interfun.buz.im.repo.IMFileUploadRepositoryImpl
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn

/**
 * Author: ChenYouSheng
 * Date: 2025/6/6
 * Email: <EMAIL>
 * Desc:
 */
@Module
@InstallIn(UserComponent::class)
internal abstract class IMUserModule {

    @Binds
    @UserQualifier
    @UserScope
    abstract fun bindIMFileUploadRepository(imFileUploadRepository: IMFileUploadRepositoryImpl): IMFileUploadRepository

    @Binds
    @UserScope
    @UserQualifier
    abstract fun bindIMFileDownloadRepository(imFileDownloadRepository: IMFileDownloadRepositoryImpl): IMFileDownloadRepository

    @Binds
    @UserQualifier
    @UserScope
    abstract fun bindIMUserEntry(impl: IMUserEntryImpl): IMUserEntry
}