package com.interfun.buz.im

import android.content.Context
import android.os.SystemClock
import android.util.LruCache
import androidx.lifecycle.AtomicReference
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ProcessLifecycleOwner
import com.buz.idl.login.bean.ImDeviceParams
import com.interfun.buz.base.ktx.LogLine
import com.interfun.buz.base.ktx.OneParamCallback
import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.base.ktx.asMillisecond
import com.interfun.buz.base.ktx.doOnLifecycle
import com.interfun.buz.base.ktx.emitInScope
import com.interfun.buz.base.ktx.isAppInForeground
import com.interfun.buz.base.ktx.isDebug
import com.interfun.buz.base.ktx.isNetworkAvailable
import com.interfun.buz.base.ktx.isNotNull
import com.interfun.buz.base.ktx.isReleaseLogBuildType
import com.interfun.buz.base.ktx.launch
import com.interfun.buz.base.ktx.log
import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.base.ktx.logError
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.base.ktx.logLineError
import com.interfun.buz.base.ktx.logLineInfo
import com.interfun.buz.base.ktx.logLineWarn
import com.interfun.buz.base.ktx.logWarn
import com.interfun.buz.base.ktx.mmkv
import com.interfun.buz.base.ktx.toSafeLong
import com.interfun.buz.base.ktx.toast
import com.interfun.buz.base.manager.retry.RetryManager
import com.interfun.buz.common.R
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.constants.CommonMMKV
import com.interfun.buz.common.constants.appId
import com.interfun.buz.common.constants.channelId
import com.interfun.buz.common.constants.deviceId
import com.interfun.buz.common.eventbus.im5.IMConnectStateChangedEvent
import com.interfun.buz.common.eventbus.im5.IMConnectSuccessEvent
import com.interfun.buz.common.eventbus.im5.IMServerStateChangeEvent
import com.interfun.buz.common.ktx.isMe
import com.interfun.buz.common.ktx.sessionKey
import com.interfun.buz.common.ktx.uid
import com.interfun.buz.common.manager.ABTestManager
import com.interfun.buz.common.manager.AppConfigRequestManager
import com.interfun.buz.common.manager.UserLifecycle
import com.interfun.buz.common.manager.UserManager
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.manager.cache.config.AppInitMMKV
import com.interfun.buz.common.manager.userLifecycleScope
import com.interfun.buz.common.net.SessionKeyManager
import com.interfun.buz.im.constants.*
import com.interfun.buz.im.coroutine.ExitWaitingException
import com.interfun.buz.im.coroutine.checkOwnership
import com.interfun.buz.im.entity.*
import com.interfun.buz.im.ktx.*
import com.interfun.buz.im.message.*
import com.interfun.buz.im.model.IMResult
import com.interfun.buz.im.signal.InnerIMSignalManager
import com.interfun.buz.im.signal.PushAgentManager
import com.interfun.buz.im.track.IMTracker
import com.interfun.buz.im.viewmodel.IMViewModel
import com.interfun.buz.onair.standard.IGlobalOnAirController
import com.interfun.buz.signal.ISignalManagerPresenter
import com.lizhi.component.basetool.env.AppEnvironment
import com.lizhi.component.basetool.env.Environments
import com.lizhi.component.basetool.ntp.NtpTime
import com.lizhi.component.cloudconfig.CloudConfig
import com.lizhi.fm.e2ee.model.E2EEConfigure
import com.lizhi.fm.e2ee.model.ServerEnv
import com.lizhi.im5.sdk.IM5Client
import com.lizhi.im5.sdk.auth.AuthCallback
import com.lizhi.im5.sdk.auth.AuthResult
import com.lizhi.im5.sdk.auth.AuthStatus
import com.lizhi.im5.sdk.auth.login.BizUserInfo
import com.lizhi.im5.sdk.auth.login.IM5LoginInfo
import com.lizhi.im5.sdk.base.*
import com.lizhi.im5.sdk.conversation.IConversation
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import com.lizhi.im5.sdk.conversation.IM5ConversationUpdateTimeParam
import com.lizhi.im5.sdk.core.IM5Configure
import com.lizhi.im5.sdk.e2ee.E2EEBridgeConfig
import com.lizhi.im5.sdk.e2ee.E2EEBrigdeFactory
import com.lizhi.im5.sdk.group.HistoryResult
import com.lizhi.im5.sdk.group.UnreadData
import com.lizhi.im5.sdk.message.*
import com.lizhi.im5.sdk.message.model.IM5MsgContent
import com.lizhi.im5.sdk.message.model.IM5VideoMessage
import com.lizhi.im5.sdk.profile.UserInfo
import com.lizhi.im5.sdk.report.IM5ReportUtils
import com.lizhi.im5.sdk.report.ReportEvent
import com.yibasan.lizhifm.lzlogan.Logz
import kotlinx.coroutines.*
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.*
import okhttp3.internal.toLongOrDefault
import java.util.LinkedList
import java.util.UUID
import java.util.concurrent.TimeUnit
import kotlin.collections.set
import kotlin.coroutines.Continuation

internal class IMManager {

    companion object {
        private const val TAG = "IMAgentManager"
        private const val EVENT_IM5_CLIENT_FILE_UPLOAD = ReportEvent.EVENT_IM5_CLIENT_FILE_UPLOAD
        private const val EVENT_IM5_CLIENT_MESSAGE_SEND = ReportEvent.EVENT_IM5_CLIENT_MESSAGE_SEND
    }
    @Volatile
    private var requestTokenJob: Job? = null

    @Volatile
    private var requestTokenUid = 0L
    private var retryLoginImJob: Job? = null
    private var userId: Long = 0
    private val imViewModel = IMViewModel()
    private val globalSendListLock = Any()
    private var onNetworkChangeCallbackList: MutableList<OneParamCallback<Boolean>>? = null
    private val runnableMap = LinkedHashMap<String, java.lang.Runnable>()
    private val imAuthStatus get() = IM5Client.getInstance().currentAuthStatus
    private val isIMLoggedIn get() = imAuthStatus == AuthStatus.LOGINED
    val authStatusFlow = MutableStateFlow<AuthStatus>(IM5Client.getInstance().currentAuthStatus)

    @Volatile
    private var isIMInitialized = false

    @Volatile
    private var pendingLogin = false
    val localMsgInsertedFlow = MutableSharedFlow<IMessage>()

    @Volatile
    var imLongLinkConnectStatus: IM5ConnectStatus? = null
        private set
    private val _msgSendStateFlow = MutableSharedFlow<IMSendStateEvent>()
    val msgSendStateFlow: Flow<IMSendStateEvent> = _msgSendStateFlow
    private val _onlineMsgSendStateFlow = MutableSharedFlow<IMSendStateEvent>()
    val onlineMsgSendStateFlow: Flow<IMSendStateEvent> = _onlineMsgSendStateFlow
    private val _msgReceivedFlow = MutableSharedFlow<IMMessageReceivedData>()
    val msgReceivedFlow: Flow<IMMessageReceivedData> = _msgReceivedFlow
    private val _onlineMsgReceivedFlow = MutableSharedFlow<IMMessageReceivedData>()
    val onlineMsgReceivedFlow: Flow<IMMessageReceivedData> = _onlineMsgReceivedFlow
    private val globalScope = CoroutineScope(SupervisorJob() + Dispatchers.Main.immediate)

    private val _msgVoiceFilterFlow = MutableSharedFlow<IMMessageVoiceFilterData>()
    val msgVoiceFilterFlow: Flow<IMMessageVoiceFilterData> = _msgVoiceFilterFlow

    private val _msgEndLoadingFlow = MutableSharedFlow<Int>()
    val msgEndLoadingFlow:Flow<Int> = _msgEndLoadingFlow

    //防止多个地方同时删除导致太慢
    private val conversationDeleteFlow = MutableSharedFlow<ImConversationIdentity>(extraBufferCapacity = 3)
    private val _conversationChangedFlow = MutableSharedFlow<IConversation>()
    @Deprecated("use conversationListChangedFlow instead")
    val conversationChangedFlow: Flow<IConversation> = _conversationChangedFlow
    //跟conversationChangedFlow是一样的，包含全部的conversation更新，但是上面是一个个，如果量多的话性能不是很好，更重要是有一些地方拿列表处理更方便，
    //而且这里每个地方创建一个channelFlow也好一点，不会因为消费慢阻塞
    private val imConvChangeFlow: Flow<List<IConversation>> = callbackFlow<List<IConversation>> {
        val callback = object : IM5Observer<List<IConversation>> {
            override fun onEvent(conversationList: List<IConversation>?) {
                logInfo(TAG, "initConversationChangedCallback conversationDid changed")
                conversationList?.let {
                    trySend(conversationList)
                }
            }

            override fun onError(errType: Int, errCode: Int, errMsg: String?) {}
        }
        IM5Client.getInstance().addConversationsObserver(callback)
        awaitClose {
            IM5Client.getInstance().removeConversationsObserver(callback)
        }
    }.buffer(capacity = Channel.UNLIMITED)
    val conversationListChangedFlow =
        merge(imConvChangeFlow, conversationDeleteFlow.map { convIdentity ->
            getConversationSync(convIdentity.convType, convIdentity.targetId)?.let { listOf(it) }
                ?: emptyList()
        })

    /**
     * 用来通知消息localExtra变化的flow，仅通知变更实际，使用时需要重新获取
     * @see IMessage.localExtraModel
     * @see IMManager.getLocalExtraModel
     */
    private val _msgLocalExtraUpdateFlow = MutableSharedFlow<IMMessageLocalExtraUpdateKey>()
    val msgLocalExtraUpdateFlow:Flow<IMMessageLocalExtraUpdateKey> = _msgLocalExtraUpdateFlow

    private val localExtraCache = LruCache<String, IMMessageLocalExtra>(1024)

    private var useUnifyLogin: Boolean = false


    internal data class LoginTimeCorrect(
        /**
         * 登录时间戳,毫秒
         */
        val loginTime: Long,
        /**
         * 写入[loginTime]时当前系统启动时间
         */
        val loginTimeSysLock: Long,
        /**
         * 标记[loginTime]是否使用 NTP 时间,如果时那么为 true
         */
        val loginTimeUseNtp: Boolean,

        /**
         * 首次loading结束的时间,ntp时间优先
         */
        var loadingEndTime: Long = Long.MAX_VALUE
    )

    private fun updateEndLoadingNtpTime() {
        loginTimeCorrect.get()?.apply {
            if (loadingEndTime == Long.MAX_VALUE) {
                logInfo(TAG, "updateEndLoadingNtpTime isAppInForeground:$isAppInForeground")
                loadingEndTime = NtpTime.nowForce()
                if (isAppInForeground) {
                    return
                }
                // 读取是未读消息总数
                IM5Client.getInstance().getTotalUnreadCount(object : IM5Observer<Int> {
                    override fun onEvent(unreadCount: Int?) {
                        logInfo(
                            TAG,
                            "getTotalUnreadCount unreadCount:$unreadCount isAppInForeground:$isAppInForeground"
                        )
                        unreadCount?.let {
                            if (isAppInForeground || unreadCount <= 0) {
                                return
                            }
                            globalScope.launch {
                                _msgEndLoadingFlow.emit(unreadCount)
                            }
                        }
                    }
                    override fun onError(errType: Int, errCode: Int, errMsg: String?) {
                    }
                })
            }
        }
    }

    private val loginTimeCorrect: AtomicReference<LoginTimeCorrect?> = AtomicReference(null)


    private val mConnectListener = object : IM5Observer<AuthResult> {
        override fun onEvent(t: AuthResult) {
            logLineWarn(TAG, logLine = LogLine.LOGIN, "im mConnectListener onEvent status = ${t.authStatus}")
            authStatusFlow.value = t.authStatus
            IMConnectStateChangedEvent.post(t.authStatus)
            when (t.authStatus) {
                AuthStatus.LOGINING -> {
                    //正在登录中
                }

                AuthStatus.LOGINED -> {
                    //已成功登陆
                    IMConnectSuccessEvent.post()
                    PushAgentManager.setAlias(UserSessionManager.uid.toString(), "IM LOGINED")
                }

                AuthStatus.TOKEN_INVALID,
                AuthStatus.SESSION_INVALID -> {
                    if (useUnifyLogin) {
                        logLineWarn(TAG, logLine = LogLine.LOGIN, "onEvent user was kick-out caused by: IM AuthStatus.SESSION_INVALID")
                        if (UserManager.logout()) {
                            toast(R.string.login_expired)
                        }
                    }
                }

                AuthStatus.SESSION_TIMEOUT -> {
                    logInfo(TAG, "SESSION_TIMEOUT useUnifyLogin:$useUnifyLogin, session:${t.session}, is equal:${t.session==UserSessionManager.sessionKey}")
                    if (useUnifyLogin && t.session == UserSessionManager.sessionKey) {
                        GlobalScope.launch {
                            SessionKeyManager.refreshSessionKey("IM")
                        }
                    }
                }
                AuthStatus.KICKOUT -> {
                    //被T下线
                    logLineInfo(TAG, logLine = LogLine.LOGIN, "onEvent user was kick-out caused by: IM AuthStatus.KICK-OUT")
                    CommonMMKV.isLoginOutByMulDevice = true
                    UserManager.logout()
                }


                else -> {}
            }
            onAuthStatusChanged(t.authStatus)
        }

        override fun onError(errType: Int, errCode: Int, errMsg: String?) {
            logLineError(
                TAG,
                logLine = LogLine.LOGIN,
                "mConnectListener onError errType = $errType, errCode = $errCode, errMsg = $errMsg"
            )
        }
    }

    private val mServiceStatusObserver = IM5ServiceStatusObserver {
        IMServerStateChangeEvent.post(serverStatus = it)
        if (it == IM5ServiceStatus.END_LOADING) {
            Logz.tag(TAG).d("endLoading")
            updateEndLoadingNtpTime()
        }
    }


    fun filterRoamMessage(message: IMessage): Boolean {
        var loginTimeData = loginTimeCorrect.get()
        /**
         * 算法思想:
         * 背景:
         *      产品期望不播放比较老旧的语音消息,否则可能在群拉历史的情况会"停不下来"播放语音
         *      如果调用 login()函数的时候使用的是本地时间,而不是被校准过的 NTP 时间.假设用户修改过时间
         *      且设置成一个很老旧的时间点,将会永远导致无法播放语音.所以我们最好得到一个自纠正机制.
         * 自纠正机制算法:
         *      (1)每次写入[com.interfun.buz.chat.wt.manager.WTMessageManager#loginTimeCorrect]
         *      时,记录当前是否使用 NTP时间 和当前开机时间.
         *      (2) 收到消息的时候,判断之前记录是否使用了 NTP 时间.如果是那么直接判断和 IM 消息的创建时间差,
         *          如果在阈值内那么播放.
         *      (3) 收到消息的时候,如果没有使用 NTP 时间.那么再次获取一次当前NTP时间,我们称为[新NTP时间].如果成功获取那么进入自纠正逻辑.
         *      (4) 在自纠正逻辑中再次获取一次当前开机时间.用当前开机时间减去之前记录的开机时间得到[时间差]
         *      (5) 当前最新获取的 [新NTP时间]减去[时间差]得到精准的开机时间点[correctLoginTime].然后更新回[com.interfun.buz.chat.wt.manager.WTMessageManager#loginTimeCorrect]
         *
         */
        if (AppConfigRequestManager.enableCorrectMsgTime && loginTimeData?.loginTimeUseNtp == false) {//判断是否使用了 NTP 时间
            //进入if 表示之前没有使用 NTP 时间
            //[新NTP时间]
            val newNtpTime = NtpTime.now()
            if (newNtpTime != null) {//成功获取了 NTP 时间
                //获取当前开机时间
                val elapsedRealtime = SystemClock.elapsedRealtime()
                //得到一个[时间差]
                val diffTime = elapsedRealtime - loginTimeData.loginTimeSysLock
                //得到一个精准的开机时间点
                val correctLoginTime = newNtpTime - diffTime
                //兜底校验正确性,防止系统自己有bug
                if (correctLoginTime > 0 && diffTime > 0) {
                    val loginTimeCorrectNewly =
                        LoginTimeCorrect(correctLoginTime, SystemClock.elapsedRealtime(), true)
                    //通过 CAS 写入回去.
                    // 并发处理逻辑:
                    //              (1) 如果 logout 先调用那么 CAS 会失败
                    //              (2) 如果 logout 后调用那么 CAS 先成功,当前消息会播放,但是后续消息会直接跳过
                    //              (3) 如果 login 先调用那么 CAS 会失败
                    //              (4) 如果 login 后调用那么没有副作用.
                    val logTimeSetRet =
                        this.loginTimeCorrect.compareAndSet(
                            loginTimeData,
                            loginTimeCorrectNewly
                        )
                    //如果操作成功回写局部变量,让其判断更严谨.
                    if (logTimeSetRet) {
                        loginTimeData = loginTimeCorrectNewly
                    }
                }
            }
        }
        if (loginTimeData == null) {
            logInfo(TAG, "filterRoamMessage filter. item continue. loginTime is null ")
            return true
        }


        if (loginTimeData.loadingEndTime > message.createTime) {
            val timeInterval = loginTimeData.loginTime - message.createTime
            logInfo(TAG, "filterRoamMessage filter, time gap: $timeInterval")
            if (timeInterval > TimeUnit.SECONDS.toMillis(AppConfigRequestManager.msgAutoPlayTime.toLong())) {
                logInfo(TAG, "filterRoamMessage filter, first loading:gap > 2min")
                return true
            }
        }
        return false
    }

    private fun getIMConfigure(): IM5Configure {
        var appKey = IM5_APP_KEY_PRODUCTION
        var serverEnv = com.lizhi.im5.netadapter.base.ServerEnv.PRODUCT_US
        if (isDebug || isReleaseLogBuildType) {
            if (AppEnvironment.TOWER == Environments.getEnv(appContext)) {
                appKey = IM5_APP_KEY_PRODUCT_DOCKER
                serverEnv = com.lizhi.im5.netadapter.base.ServerEnv.PRODUCT_DOCKER
            }else if (AppEnvironment.PRE == Environments.getEnv(appContext)){
                appKey = IM5_APP_KEY_PRODUCTION
            }
        }
        Logz.tag(TAG).i("initIm,AppKey = $appKey,isEnableIMAutoRetry:${AppInitMMKV.isEnableIMAutoRetry},maxIMAutoRetryMillis:${AppInitMMKV.maxIMAutoRetryMillis}")
        return IM5Configure.Builder()
            .setAppKey(appKey)
            .setDisableGroupSync(false)
            .setServerEnv(serverEnv)
            .setAppHost(IM5_APP_HOST)
            .setAlarmDisable(true)
            .setNeedMsgTraceId(true)
            .setContainRecalledMsg(true)
            .setEnableBase64ForMediaContent(true)
            .setAutoLoadRefMessage(false)
            .setEnableResetPrivateConversations(true)
            .setEnableAutoResend(AppInitMMKV.isEnableIMAutoRetry)
            .setAutoResendTimeInterval(AppInitMMKV.maxIMAutoRetryMillis)
            .build()
    }
    /**
     * 初始化
     */
    fun init(context: Context) {
        logInfo(TAG, "im init start")
        CloudConfig.init(appContext, appId, deviceId, channelId)
        // 因为 IM 内部信令 post 就不管了.所以可以考虑initIMSignal就注册监听?
        // 此时业务可能没有完成登录,信令的eventbus乱飞会丢失带来隐形问题(系统鉴权统一!!!!!!)
        // 综上:暂时放入业务登录后监听.
//        SignalManagerPresenter.initIMSignal()
        IM5Client.init(context, getIMConfigure(),
            Runnable {
                logInfo(TAG, "im init finished")
                isIMInitialized = true
                if (userId > 0 && pendingLogin && isIMLoggedIn.not()) {
                    requestTokenAndConnect(userId, false, true)
                    pendingLogin = false
                }
                registerMessageType()
            }
        )
        GlobalScope.launch(Dispatchers.Main) {
            ProcessLifecycleOwner.get().doOnLifecycle(onStart = {
                if (userId > 0 && isIMLoggedIn.not()) {
                    requestTokenAndConnect(userId, false, true)
                }
            })
        }
        setConnectionStatusListener(mConnectListener)
        addServiceStatusObserver(mServiceStatusObserver)
        IM5Client.getInstance().setOnNetworkChangeObserver(object : IM5Observer<Boolean> {
            override fun onEvent(networkValid: Boolean) {
                logError(TAG, "IM OnNetworkChangeObserver networkValid ${networkValid}")
                if (networkValid) {
                    requestTokenAndConnect(userId, false, true)
                }
            }

            override fun onError(p0: Int, p1: Int, p2: String?) {
            }
        })

        initMessageNotifyCallback()
        initSendMsgCallback()
        initConversationChangedCallback()
        initIM5ReportListener()
    }

    /**
     * 后续消息注册请移步到此添加
     */
    private fun registerMessageType() {
        IM5Client.getInstance().registerMsgType(BuzTextMsg::class.java)
        IM5Client.getInstance().registerMsgType(BuzImageMessage::class.java)
        IM5Client.getInstance().registerMsgType(BuzVoiceMsg::class.java)
        IM5Client.getInstance().registerMsgType(CommandMsg::class.java)
        IM5Client.getInstance().registerMsgType(WTVoiceMsg::class.java)
        IM5Client.getInstance().registerMsgType(VoiceTextMsg::class.java)
        IM5Client.getInstance().registerMsgType(VoiceTextMsgNew::class.java)
        IM5Client.getInstance().registerMsgType(WTVoiceEmojiMsg::class.java)
        IM5Client.getInstance().registerMsgType(MediaTextMsg::class.java)
        IM5Client.getInstance().registerMsgType(MediaTextMsgNew::class.java)
        IM5Client.getInstance().registerMsgType(BuzLocationMessage::class.java)
        IM5Client.getInstance().registerMsgType(OnAirDanmakuMessage::class.java)
        IM5Client.getInstance().registerMsgType(LiveplaceDanmakuMessage::class.java)
        IM5Client.getInstance().registerMsgType(BuzVideoMsg::class.java)
        IM5Client.getInstance().registerMsgType(WTVoiceEmojiImgMsg::class.java)
        IM5Client.getInstance().registerMsgType(SoundBoardEmojiMsg::class.java)
        IM5Client.getInstance().registerMsgType(OnAirGiftMsg::class.java)
        IM5Client.getInstance().registerMsgType(BuzVoiceGifMsg::class.java)
        IM5Client.getInstance().registerMsgType(BuzLivePlaceShareMessage::class.java)
        IM5Client.getInstance().registerMsgType(RealTimeCallInviteMsg::class.java)
        IM5Client.getInstance().registerMsgType(BuzFileMessage::class.java)
        IM5Client.getInstance().registerMsgType(BuzShareContactMessage::class.java)
    }

    private fun initMessageNotifyCallback() {
        // callbackFlow的缓存默认是64个，这里发消息应该够用,这里可以把流按顺序发送
        val syncFlow: Flow<Pair<IMMessageReceivedData, Boolean>> = callbackFlow {
            IM5Client.getInstance().addMessageNotifyObserver { type, msgList ->
                logInfo(TAG, "IM message received from addMessageNotifyObserver type:$type, message list size = ${msgList.size}")
                val messageList = msgList.toMutableList()
                if (type == IM5NotifyType.NewMsg) {
                    // 过滤漫游消息
                    val roamMessageList = messageList.filter { filterRoamMessage(it) }
                    if (roamMessageList.isNotEmpty()) {
                        messageList.removeAll(roamMessageList)
                        trySend(IMMessageReceivedData(BuzNotifyType.RoamMsg, roamMessageList) to false)
                    }

                    // todo: 新消息，并且符合自动asr条件
                }
                var onlineList: MutableList<IMessage>? = null
                messageList.forEach {
                    if (it.isOnlineMessage) {
                        if (onlineList == null) {
                            onlineList = arrayListOf()
                        }
                        onlineList!!.add(it)
                    }
                }
                val normalList =
                    if (onlineList != null) messageList.filter { !it.isOnlineMessage } else messageList
                // TODO:  目前IM那边返回的asr编辑和翻译编辑都是走这个方法来的。这个需要在后面版本升级IMSDK去改这个参数的命名。
                if (type == IM5NotifyType.BizEditMSG) {
                    val asrMsgList = normalList.filter {
                        it.content.isASREdit
                    }
                    val translateMsgList = normalList.filter {
                        it.content.isTranslateEdit
                    }
                    val detectLanguageCodeMsgList = normalList.filter {
                        it.content.isDetectTranslateCodeEdit
                    }
                    val onlineAsrMsgList = onlineList?.filter {
                        it.content.isASREdit
                    }
                    val onlineTranslateMsgList = onlineList?.filter {
                        it.content.isTranslateEdit
                    }
                    val onlineDetectLanguageCodeMsgList = onlineList?.filter {
                        it.content.isDetectTranslateCodeEdit
                    }
                    if (asrMsgList.isNotEmpty()) {
                        trySend(IMMessageReceivedData(BuzNotifyType.AsrEditMSG, asrMsgList) to false)
                    }
                    onlineAsrMsgList?.let {
                        trySend(IMMessageReceivedData(BuzNotifyType.AsrEditMSG, it) to true)
                    }
                    if (translateMsgList.isNotEmpty()) {
                        trySend(IMMessageReceivedData(BuzNotifyType.TranslateEditMSG, translateMsgList) to false)
                    }
                    onlineTranslateMsgList?.let {
                        trySend(IMMessageReceivedData(BuzNotifyType.TranslateEditMSG, onlineTranslateMsgList) to true)
                    }
                    detectLanguageCodeMsgList?.let {
                        trySend(IMMessageReceivedData(BuzNotifyType.DetectLanguageCodeEditMSG, detectLanguageCodeMsgList) to false)
                    }
                    onlineDetectLanguageCodeMsgList?.let {
                        trySend(IMMessageReceivedData(BuzNotifyType.DetectLanguageCodeEditMSG, onlineDetectLanguageCodeMsgList) to true)
                    }
                } else {
                    trySend(IMMessageReceivedData(type.toBuzNotifyType(), normalList) to false)

                    onlineList?.let {
                        trySend(IMMessageReceivedData(type.toBuzNotifyType(), it) to true)
                    }
                }
            }

            awaitClose {
                logInfo(TAG, "recevie message callback flow is close")
            }
        }
        globalScope.launch {
            syncFlow.collect { (receivedData, isOnlineMessage) ->
                if (isOnlineMessage) {
                    _onlineMsgReceivedFlow.emit(receivedData)
                } else {
                    _msgReceivedFlow.emit(receivedData)
                }
            }
        }
    }

    private fun initSendMsgCallback() {
        // callbackFlow的缓存默认是64个，这里发消息应该够用,这里可以把流按顺序发送
        val syncFlow: Flow<Pair<IMSendStateEvent,Boolean>> = callbackFlow {
            IM5Client.getInstance().addSendMsgObserver(object : MediaMessageCallback {
                override fun onAttached(messageInfo: IMessage) {
                    logInfo(TAG, "sendMessage:onAttached,msgId:${messageInfo.msgId},state:${messageInfo.status},buzState:${messageInfo.buzSendingState}")
                    trySend(IMSendStateEvent.OnAttach(messageInfo) to messageInfo.isOnlineMessage)
                }

                override fun onSuccess(messageInfo: IMessage) {
                    logInfo(TAG, "sendMessage:onSuccess,msgId:${messageInfo.msgId},state:${messageInfo.status},buzState:${messageInfo.buzSendingState}")
                    trySend(IMSendStateEvent.OnSuccess(messageInfo) to messageInfo.isOnlineMessage)
                }

                override fun onError(message: IMessage, errorType: Int, errorCode: Int, errorMsg: String?) {
                    logInfo(TAG, "sendMessage:onError,msgId:${message.msgId},state:${message.status},buzState:${message.buzSendingState},errorType:$errorType,errorCode:$errorCode,errorMsg:$errorMsg")
                    trySend(IMSendStateEvent.OnError(message, errorType, errorCode, errorMsg) to message.isOnlineMessage)
                }

                override fun onProgress(message: IMessage, totalSize: Long, currentSize: Long) {
                    trySend(IMSendStateEvent.OnProgress(message, totalSize, currentSize) to message.isOnlineMessage)
                }

                override fun onCanceled(message: IMessage) {
                    logInfo(TAG, "sendMessage:onCanceled,msgId:${message.msgId},state:${message.status},buzState:${message.buzSendingState}")
                    trySend(IMSendStateEvent.OnCanceled(message) to message.isOnlineMessage)
                }
            })

            awaitClose {
                logInfo(TAG, "send callback flow is close")
            }
        }

        globalScope.launch {
            syncFlow.collect { (event, isOnlineMessage) ->
                if (isOnlineMessage) {
                    _onlineMsgSendStateFlow.emit(event)
                } else {
                    _msgSendStateFlow.emit(event)
                }
            }
        }
    }

    private fun initConversationChangedCallback() {
        // callbackFlow的缓存默认是64个，这里发消息应该够用,这里可以把流按顺序发送
        val syncFlow: Flow<List<IConversation>> = callbackFlow {
            IM5Client.getInstance().addConversationsObserver(object : IM5Observer<List<IConversation>> {
                override fun onEvent(conversationList: List<IConversation>?) {
                    logInfo(TAG, "initConversationChangedCallback conversationDid changed")
                    conversationList?.let {
                        trySend(conversationList)
                    }
                }

                override fun onError(errType: Int, errCode: Int, errMsg: String?) {}
            })

            awaitClose {
                logInfo(TAG, "conversation callback flow is close")
            }
        }

        globalScope.launch {
            syncFlow.collect {
                it.forEach {  conv ->
                    _conversationChangedFlow.emit(conv)
                }
            }
        }
    }

    fun addNetworkChangeListener(
        lifecycleOwner: LifecycleOwner,
        callback: OneParamCallback<Boolean>
    ) {
        val list = onNetworkChangeCallbackList ?: LinkedList()
        onNetworkChangeCallbackList = list
        lifecycleOwner.doOnLifecycle(onDestroy = {
            onNetworkChangeCallbackList?.remove(callback)
        })
        list.add(callback)
    }

    fun connect() {
        if (isIMLoggedIn.not()) {
            return
        }
        log(TAG, "Connect IM")
        IM5Client.getInstance().connect()
    }


    /**
     * 设置连接状态监听，断开触发重连时用到
     */
    private fun setConnectionStatusListener(listener: IM5Observer<AuthResult>) {
        IM5Client.getInstance().addAuthStatusObserver(listener)
        // TODO: vx 现在换成了websocket，这里将不再回调
        IM5Client.getInstance().addConnectStatusChangeObserver { oldStatus, newStatus ->
            imLongLinkConnectStatus = newStatus
            logWarn(TAG, "IM ConnectStatusChange oldStatus:$oldStatus,newStatus:$newStatus")
        }
    }

    fun connectStatusFlow() = callbackFlow<IM5ConnectStatus> {
        val callBack: IM5ConnectStatusChangedCallback = object : IM5ConnectStatusChangedCallback {
            override fun onConnectStatusDidChanged(
                oldStatus: IM5ConnectStatus,
                newStatus: IM5ConnectStatus
            ) {
                //个人感觉没必要挂起
                launch {
                    send(newStatus);
                }
            }
        }
        IM5Client.getInstance().addConnectStatusChangeObserver(callBack)
//        IM5Client.getInstance().addConnectStatusChangeObserver { oldStatus, newStatus ->
//            imLongLinkConnectStatus = newStatus
//            logWarn(TAG, "IM ConnectStatusChange oldStatus:$oldStatus,newStatus:$newStatus")
//        }
        val imLongLinkConnectStatusBackup = imLongLinkConnectStatus
        if (imLongLinkConnectStatusBackup != null) {
            trySend(imLongLinkConnectStatusBackup)
        }
        //测试使用
//        launch {
//            delay(1000*60)
//            send(IM5ConnectStatus.Disconnected)
//        }

        awaitClose {
            IM5Client.getInstance().removeConnectStatusChangeObserver(callBack)
        }
    }

    /**
     * 监听IM服务链接状态和同步消息状态, 状态有:[com.lizhi.im5.sdk.base.IM5ServiceStatus]
     */
    fun addServiceStatusObserver(observer: IM5ServiceStatusObserver) {
        IM5Client.getInstance().addServiceStatusObserver(observer)
    }

    fun removeServiceStatusObserver(observer: IM5ServiceStatusObserver) {
        IM5Client.getInstance().removeServiceStatusObserver(observer)
    }

    fun isLoading(): Boolean = IM5Client.getInstance().isLoading

    fun isServerDisconnected(): Boolean = IM5Client.getInstance().isServerDisconnected

    private fun startRetryTask() {
        if (retryLoginImJob?.isActive == true || useUnifyLogin) {
            Logz.tag(TAG).w("startRetryTask ignore,isActive:${retryLoginImJob?.isActive}, useUnifyLogin:$useUnifyLogin")
            return
        }
        retryLoginImJob = GlobalScope.launch {
            RetryManager().retry {
                if (useUnifyLogin) {
                    return@retry true
                }
                when (imAuthStatus) {
                    null,
                    AuthStatus.UNLOGIN,
                    AuthStatus.LOGIN_ERROR,
                    AuthStatus.MAINTENANCING -> {
                        requestTokenAndConnect(userId, false, false)
                        return@retry false
                    }

                    AuthStatus.TOKEN_INVALID -> {
                        requestTokenAndConnect(userId, true, false)
                        return@retry false
                    }

                    AuthStatus.SESSION_INVALID -> {
                        requestTokenAndConnect(userId, false, false)
                        return@retry false
                    }

                    AuthStatus.LOGINING -> {
                        return@retry false
                    }

                    AuthStatus.LOGINED,
                    AuthStatus.KICKOUT,
                    AuthStatus.LOGOUTED,
                    AuthStatus.LOGOUT_ERROR,
                    AuthStatus.SESSION_TIMEOUT,
                    AuthStatus.FROZEN -> {
                        //don't need to retry
                        return@retry true
                    }
                }
            }
        }
    }

    fun onSessionRefresh(userId: Long, imUin: Long, session: String) {
        Logz.tag(TAG).i("onSessionRefresh userId = $userId, useUnifyLogin:$useUnifyLogin")
        if (useUnifyLogin && this.userId == userId) {
            IM5Client.getInstance().onBizSessionRefresh(BizUserInfo(userId.toString(), session, imUin))
        }
    }
    fun onUserLogin(userId: Long, useUnifyLogin: Boolean, imUin: Long, session: String) {
        this.useUnifyLogin = useUnifyLogin
        onUserLoginInternal(userId, imUin, session)
    }

    private fun onUserLoginInternal(userId: Long, imUin: Long, session: String) {
        Logz.tag(TAG).i("onUserLogin userId = $userId, useUnifyLogin:$useUnifyLogin")

        val ntpTime = NtpTime.now()
        loginTimeCorrect.updateAndGet { operand ->
            LoginTimeCorrect(
                ntpTime ?: System.currentTimeMillis(),
                SystemClock.elapsedRealtime(),
                ntpTime != null
            )
        }

        localExtraCache.evictAll()
        this.userId = userId
        if (useUnifyLogin) {
            if (userId != 0L) {
                val bizUserInfo = BizUserInfo(userId.toString(), session, imUin)
                IM5Client.getInstance().onBizLogin(bizUserInfo)
            }
        } else {
            requestTokenAndConnect(userId, false, true)
            startRetryTask()
        }
    }

    fun onUserLogout(callback: AuthCallback? = null) {
        Logz.tag(TAG).i("onUserLogout useUnifyLogin:$useUnifyLogin")
        loginTimeCorrect.updateAndGet { operand ->
            null
        }

        if (useUnifyLogin) {
            IM5Client.getInstance().onBizLogout()
        } else {
            IM5Client.getInstance().logout(callback)
        }
        E2EEBrigdeFactory.e2eeBridgeInstance.releaseE2EE()
        userId = 0L
        requestTokenUid = 0
        cancelRetryJobs()
        clearAllDelayTask()
        localExtraCache.evictAll()
    }

    suspend fun getConversationList(
        convType: IM5ConversationType? = null,
        count: Int = 0,
        timestamp: Long = 0,
    ): IMFetchResult<List<IConversation>?> {
        waitForLogin()
        return suspendCancellableCoroutine { cont ->
            IM5Client.getInstance()
                .getConversationList(timestamp, count, convType, createFetchImResultCallback(cont))
        }
    }

    private fun <T> createFetchImResultCallback(cont: Continuation<IMFetchResult<T>>): IM5Observer<T> {
        return object : IM5Observer<T> {
            override fun onEvent(t: T) {
                cont.resumeWith(Result.success(IMFetchResult.Success(t)))
            }

            override fun onError(errType: Int, errCode: Int, errMsg: String?) {
                cont.resumeWith(Result.success(IMFetchResult.Error(IMErrorInfo(errType, errCode, errMsg))))
            }
        }
    }

    fun getConversationList(
        lifecycleOwner: LifecycleOwner?,
        convType: IM5ConversationType? = null,
        count: Int = 0,
        timestamp: Long = 0,
        observer: IM5Observer<MutableList<IConversation>>?
    ): String? {
        val uuid = runOrDelay(lifecycleOwner, "getConversationList") {
            IM5Client.getInstance().getConversationList(timestamp, count, convType, observer)
        }
        return uuid
    }

    suspend fun deleteConversation(
        convType: IM5ConversationType,
        targetId: String,
    ): IMFetchResult<Boolean> {
        waitForLogin()
        val result = suspendCancellableCoroutine { cont ->
            IM5Client.getInstance()
                .deleteConversation(convType, targetId, createFetchImResultCallback(cont))
        }
        if (result is IMFetchResult.Success) {
            conversationDeleteFlow.emit(ImConversationIdentity(targetId, convType))
        }
        return result
    }

    /**
     * Load history by both local and remote for specified arguments.
     */
    fun loadHistory(
        lifecycleOwner: LifecycleOwner?,
        convType: IM5ConversationType,
        targetId: String,
        count: Int = 20,
        msgId: Long = 0,
        before: Boolean = true,
        localObserver: HistoryObserver<MutableList<IMessage>>?,
        observer: HistoryObserver<MutableList<IMessage>>?,
    ): String? {
        return runOrDelay(lifecycleOwner, "loadHistory") {
            IM5Client.getInstance().loadHistory(
                convType,
                targetId,
                before,
                msgId,
                count,
                localObserver,
                observer
            )
        }
    }

    /**
     * Load group history by both local and remote for specified arguments.
     * And HistoryResult distinguish between old and new messages
     */
    fun loadGroupHistory(
        lifecycleOwner: LifecycleOwner?,
        convType: IM5ConversationType,
        targetId: String,
        count: Int = 20,
        msgId: Long = 0,
        before: Boolean = true,
        forceRequestRemote: Boolean = false,
        localObserver: HistoryObserver<MutableList<IMessage>>?,
        observer: HistoryObserver<HistoryResult>
    ): String? {
        return runOrDelay(lifecycleOwner, "loadGroupHistory") {
            IM5Client.getInstance().loadGroupHistory(
                convType,
                targetId,
                before,
                msgId,
                count,
                forceRequestRemote,
                localObserver,
                observer
            )
        }
    }

    fun getLocalHistoryMessages(
        lifecycleOwner: LifecycleOwner?,
        convType: IM5ConversationType,
        targetId: String,
        count: Int = 40,
        msgId: Long = 0,
        before: Boolean = true,
        observer: IM5Observer<MutableList<IMessage>>?
    ): String? {
        return runOrDelay(lifecycleOwner, "getLocalHistoryMessages") {
            IM5Client.getInstance()
                .getLocalHistoryMessages(convType, targetId, before, msgId, count, observer)
        }
    }

    suspend fun loadUnreadHistoryMessage(
        convType: IM5ConversationType,
        targetId: String,
        count: Int
    ): IMResult<UnreadData> {
        return runOrDelayWithCont("getLocalHistoryMessages") { cont ->
            IM5Client.getInstance().loadUnreadHistoryMessage(
                convType,
                targetId,
                count,
                object : IM5Observer<UnreadData> {
                    override fun onEvent(t: UnreadData?) {
                        if (t == null) {
                            cont.resumeWith(
                                Result.success(
                                    IMResult.Error(
                                        errorType = IMResult.Error.ERROR_TYPE_UNKNOWN,
                                        errorCode = IMResult.Error.ERROR_CODE_UNKNOWN,
                                        errorMsg = IMResult.Error.ERROR_MSG_UNKNOWN
                                    )
                                )
                            )
                        } else {
                            cont.resumeWith(Result.success(IMResult.Success(t)))
                        }
                    }

                    override fun onError(errType: Int, errCode: Int, errMsg: String?) {
                        cont.resumeWith(
                            Result.success(
                                IMResult.Error(
                                    errorType = errType,
                                    errorCode = errCode,
                                    errorMsg = errMsg
                                )
                            )
                        )
                    }
                })
        }
    }

    /**
     * get history message according to the types
     */
    fun getLocalHistoryMessagesByTypes(
        convType: IM5ConversationType,
        targetId: String,
        msgTypes: List<Int>,
        count: Int = 100,
        msgId: Long = 0,
        before: Boolean = true,
        observer: IM5Observer<MutableList<IMessage>>?
    ): String? {
        return runOrDelay(null, "getLocalHistoryMessagesByTypes") {
            IM5Client.getInstance()
                .getLocalHistoryMessages(
                    convType,
                    targetId,
                    msgId,
                    msgTypes,
                    count,
                    before,
                    observer
                )
        }
    }


    fun getRemoteHistoryMessages(
        lifecycleOwner: LifecycleOwner?,
        convType: IM5ConversationType,
        targetId: String,
        count: Int = 40,
        msgId: Long = 0,
        observer: IM5Observer<MutableList<IMessage>>?
    ): String? {
        return runOrDelay(lifecycleOwner, "getRemoteHistoryMessages") {
            IM5Client.getInstance()
                .getRemoteHistoryMessages(convType, targetId, msgId, count, false, observer)
        }
    }

    /**
     * enter Conversation
     * Will clear the unread data
     */
    fun enterConversation(
        targetId: String,
        type: IM5ConversationType = IM5ConversationType.PRIVATE
    ) {
        IM5Client.getInstance().enterConversation(type, targetId)
    }

    /**
     * leave Conversation
     */
    fun leaveConversation(
        targetId: String,
        type: IM5ConversationType = IM5ConversationType.PRIVATE
    ) {
        IM5Client.getInstance().leaveConversation(type, targetId)
    }

    fun getUnreadCount(
        lifecycleOwner: LifecycleOwner?,
        targetIds: Array<out String>,
        observer: IM5Observer<Int>
    ): String? {
        return runOrDelay(lifecycleOwner, "getUnreadCount") {
            IM5Client.getInstance().getUnreadCount(targetIds, observer)
        }
    }

    suspend fun getConversationSync(
        convType: IM5ConversationType,
        targetId: String
    ): IConversation? {
        return runOrDelaySync("getConversation") {
            suspendCancellableCoroutine { cont ->
                IM5Client.getInstance()
                    .getConversation(convType, targetId, object : IM5Observer<IConversation> {
                        override fun onEvent(t: IConversation?) {
                            cont.resumeWith(Result.success(t))
                        }

                        override fun onError(errType: Int, errCode: Int, errMsg: String?) {
                            cont.resumeWith(Result.success(null))
                        }
                    })
            }
        }
    }

    suspend fun sendPreprocessMessageSync(message: IM5Message): SendMsgResult {
        if (message.targetId.toLongOrDefault(0) <= 0) {
            return SendMsgResult.SendMsgFailed(null, IMErrorInfo(99999, 99999, "targetId is empty"))
        }
        return runOrDelayWithCont("sendVoiceFilterMessage") { cont ->
            IM5Client.getInstance()
                .sendPreprocessMessage(message, createSendMessageSyncCallbackNew(cont))
        }
    }

    suspend fun sendMessageSync(message: IMessage): SendMsgResult {
        if (message.targetId.toLongOrDefault(0) <= 0) {
            return SendMsgResult.SendMsgFailed(null, IMErrorInfo(99999, 99999, "targetId is empty"))
        }
        return runOrDelayWithCont("sendMessageSync") { cont ->
            IM5Client.getInstance()
                .sendMessage(message, 0, createSendMessageSyncCallbackNew(cont))
        }
    }


    suspend fun forwardMessageSync(
        convType: IM5ConversationType,
        msgId: Long,
        targetConvType: IM5ConversationType,
        targetId: String,
        msgTraceId: String?,
        pushContent: String?,
        pushPayload: String?,
        userInfo: UserInfo?,
    ): SendMsgResult {
        return runOrDelayWithCont("forwardMessageSync") { cont ->
            IM5Client.getInstance()
                .forwardMessage(
                    convType,
                    msgId,
                    targetConvType,
                    targetId,
                    msgTraceId,
                    pushContent,
                    pushPayload,
                    userInfo,
                    createSendMessageSyncCallbackNew(cont)
                )
        }
    }

    suspend fun sendMediaMessageSync(message: IMessage): SendMsgResult {
        if (message.targetId.toLongOrDefault(0) <= 0) {
            return SendMsgResult.SendMsgFailed(null, IMErrorInfo(99999, 99999, "targetId is empty"))
        }
        return runOrDelayWithCont("sendMediaMessageSync") { cont ->
            IM5Client.getInstance()
                .sendMediaMessage(message, createSendMessageSyncCallbackNew(cont))
        }
    }

    suspend fun prepareVideoMessageSync(message: IM5Message): SendMsgResult {
        if (message.targetId.toLongOrDefault(0) <= 0) {
            return SendMsgResult.SendMsgFailed(null, IMErrorInfo(99999, 99999, "targetId is empty"))
        }
        return runOrDelayWithCont("prepareMessageSync") { cont ->
            IM5Client.getInstance()
                .prepareVideoMessage(message, createPrepareMessageSyncCallback(cont))
        }
    }

    /**
     * @param reason 预留参数，准备视频消息处理失败原因，可用于下次重发是获取使用()，目前压缩失败不处理原因，后续做剪辑等操作可能使用
     * @see IM5VideoMessage.getReason 获取处理失败原因
     */
    suspend fun prepareVideoMessageFailed(
        msgId: Long,
        convType: IM5ConversationType,
        reason: String,
    ): SendMsgResult {
        return runOrDelayWithCont("prepareVideoMessageFailed") { cont ->
            IM5Client.getInstance()
                .prepareVideoMessageFailed(
                    convType,
                    msgId,
                    reason,
                    createPrepareMessageSyncCallback(cont)
                )
        }
    }

    suspend fun sendPreparedVideoMessage(
        msgId: Long,
        convType: IM5ConversationType,
    ): SendMsgResult {
        return runOrDelayWithCont("prepareMessageSync") { cont ->
            IM5Client.getInstance()
                .sendPreparedVideoMessage(
                    convType,
                    msgId,
                    createSendMessageSyncCallbackNew(cont)
                )
        }
    }

    suspend fun sendVoiceFilterMessage(
        message: IM5Message
    ) : SendMsgResult {
        if (message.targetId.toLongOrDefault(0) <= 0) {
            return SendMsgResult.SendMsgFailed(null, IMErrorInfo(99999, 99999, "targetId is empty"))
        }
        return runOrDelayWithCont("sendVoiceFilterMessage") { cont ->
            IM5Client.getInstance()
                .sendVoiceFilterMessage(message, createSendMessageSyncCallbackNew(cont))
        }
    }

    suspend fun sendVoiceFilterMessage(
        previewId: String
    ) : SendMsgResult {
        return runOrDelayWithCont("sendVoiceFilterMessage") { cont ->
            IM5Client.getInstance()
                .sendVoiceFilterMessage(previewId, createSendMessageSyncCallbackNew(cont))
        }
    }

    suspend fun sendPrivateOnlineMessage(message: IM5Message): IMErrorInfo? {
        return runOrDelaySync("sendPrivateOnlineMessage") {
            suspendCancellableCoroutine { cont ->
                IM5Client.getInstance()
                    .sendOnlyOnlineMessageForChannel(message, createSendMessageSyncCallback(cont))
            }
        }
    }

//    suspend fun sendGroupOnlineMessage(
//        message: IM5Message,
//        targetList: List<String>?
//    ): IMErrorInfo? {
//        return runOrDelaySync("sendGroupOnlineMessage") {
//            suspendCancellableCoroutine { cont ->
//                IM5Client.getInstance()
//                    .sendOnlyOnlineMessageForChannel(
//                        message,
//                        targetList,
//                        createSendMessageSyncCallback(cont)
//                    )
//            }
//        }
//    }

    fun insertLocalMessage(
        lifecycleOwner: LifecycleOwner?,
        messageContent: IM5MsgContent,
        fromId: String,
        targetId: String,
        convType: IM5ConversationType,
        createTime: Long,
        observer: IM5Observer<IMessage>? = null
    ): String? {
        return runOrDelay(lifecycleOwner, "insertLocalMessage") {
            IM5Client.getInstance().insertLocalMessage(
                messageContent,
                fromId,
                targetId,
                convType,
                createTime,
                object : IM5Observer<IMessage> {
                    override fun onEvent(msg: IMessage?) {
                        observer?.onEvent(msg)
                        if (msg != null) {
                            GlobalScope.launch(start = CoroutineStart.UNDISPATCHED) {
                                localMsgInsertedFlow.emit(msg)
                            }
                        }
                    }

                    override fun onError(errorType: Int, errorCode: Int, errorMsg: String?) {
                        observer?.onError(errorType, errorCode, errorMsg)
                    }
                }
            )
        }
    }

    fun resendMessage(
        lifecycleOwner: LifecycleOwner?,
        convType: IM5ConversationType,
        msgId: Long,
        msgTraceId: String? = null,
        callback: MessageCallback?
    ): String? {
        return runOrDelay(lifecycleOwner, "resendMessage") {
            IM5Client.getInstance().resendMessage(convType, msgId, msgTraceId, callback)
        }
    }

    fun getRangeRemoteHistoryMessage(
        lifecycleOwner: LifecycleOwner,
        convType: IM5ConversationType,
        targetId: String,
        svrMsgId: String,
        beforeCount: Int = 25,
        afterCount: Int = 25,
        observer: IM5Observer<MutableList<IMessage>>?
    ): String? {
        return runOrDelay(lifecycleOwner, "getRangeHistoryMessage") {
            IM5Client.getInstance().getRangeHistoryMessage(
                convType,
                targetId,
                svrMsgId,
                beforeCount,
                afterCount,
                observer
            )
        }
    }

    fun getRangeLocalHistoryMessage(
        lifecycleOwner: LifecycleOwner,
        convType: IM5ConversationType,
        targetId: String,
        msgId: Long,
        beforeCount: Int = 25,
        afterCount: Int = 25,
        observer: IM5Observer<MutableList<IMessage>>?
    ): String? {
        return runOrDelay(lifecycleOwner, "getRangeHistoryMessage") {
            IM5Client.getInstance().getRangeLocalHistoryMessage(
                convType,
                targetId,
                msgId,
                beforeCount,
                afterCount,
                observer
            )
        }
    }

    fun getMessageForServerMsgId(
        lifecycleOwner: LifecycleOwner?,
        convType: IM5ConversationType,
        servMsgId: Long,
        callback: IM5Observer<IMessage?>
    ): String? {
        return runOrDelay(lifecycleOwner, "getMessageForServerMsgId") {
            IM5Client.getInstance().getMessageForServerMsgId(convType, servMsgId, callback)
        }
    }

    fun getMessage(
        lifecycleOwner: LifecycleOwner?,
        convType: IM5ConversationType,
        messageId: Long,
        callback: IM5Observer<IMessage?>
    ): String? {
        return runOrDelay(lifecycleOwner, "getMessage") {
            IM5Client.getInstance().getMessage(convType, messageId, callback)
        }
    }

    suspend fun getLocalMessages(
        convType: IM5ConversationType,
        messageIdList: List<Long>,
    ): List<IMessage>? {
        return runOrDelayWithCont("getLocalMessages") { cont ->
            IM5Client.getInstance()
                .getLocalMessages(convType, messageIdList, object : IM5Observer<List<IMessage>?> {
                    override fun onEvent(list: List<IMessage>?) {
                        cont.resumeWith(Result.success(list))
                    }

                    override fun onError(errType: Int, errCode: Int, errMsg: String?) {
                        cont.resumeWith(Result.success(null))
                    }
                })
        }
    }

    fun deleteMessages(
        lifecycleOwner: LifecycleOwner?,
        convType: IM5ConversationType,
        targetId: String,
        deleteRemote: Boolean,
        msgIds: LongArray,
        msgDeletedCallback: MsgDeletedCallback
    ): String? {
        return runOrDelay(lifecycleOwner, "deleteMessages") {
            IM5Client.getInstance()
                .deleteMessages(convType, targetId, msgIds, deleteRemote, msgDeletedCallback)
        }
    }

    fun clearMessages(
        lifecycleOwner: LifecycleOwner?,
        convType: IM5ConversationType,
        targetId: String,
        deleteRemote: Boolean,
        msgDeletedCallback: MsgDeletedCallback
    ): String? {
        return runOrDelay(lifecycleOwner, "clearMessages") {
            IM5Client.getInstance().clearMessages(
                convType, targetId,
                Long.MAX_VALUE,
                deleteRemote,
                msgDeletedCallback
            )
        }
    }

    suspend fun updateLocalExtraField(
        message: IMessage,
        updateFieldExtra: IMMessageLocalExtra
    ) {
        runOrDelaySync("updateLocalExtraField") {
            suspendCancellableCoroutine { continuation ->
                IM5Client.getInstance().updateLocalExtraField(message.conversationType, message.msgId, updateFieldExtra.toMap(), object : IM5Observer<IMessage> {
                    override fun onEvent(message: IMessage?) {
                        message?.let {
                            // sdk更新成功，再次更新缓存数据
                            localExtraCache.put(message.localExtraKey, IMMessageLocalExtra.parseFromJson(message.localExtra))
                            globalScope.launch {
                                _msgLocalExtraUpdateFlow.emit(
                                    IMMessageLocalExtraUpdateKey(
                                        message.msgId,
                                        message.conversationType.value
                                    )
                                )
                            }
                        }
                        continuation.resumeWith(Result.success(Unit))
                    }

                    override fun onError(errType: Int, errCode: Int, errMsg: String?) {
                        continuation.resumeWith(Result.success(Unit))
                    }
                })
            }
        }
    }

    /**
     * 通过有线获取缓存，取得最新的localExtra
     */
    fun getLocalExtraModel(message: IMessage): IMMessageLocalExtra {
        return localExtraCache.get(message.localExtraKey) ?: IMMessageLocalExtra.parseFromJson(message.localExtra)
    }


    fun setConvLocalExtra(
        lifecycleOwner: LifecycleOwner?,
        convType: IM5ConversationType,
        targetId: String,
        localExtra: String
    ): String? {
        return runOrDelay(lifecycleOwner, "setConvLocalExtra") {
            IM5Client.getInstance().setConvLocalExtra(convType, targetId, localExtra, null)
        }
    }

    fun setPlayedMessage(
        lifecycleOwner: LifecycleOwner?,
        convType: IM5ConversationType,
        targetId: String,
        msgId: String
    ): String? {
        return runOrDelay(lifecycleOwner, "setPlayedMessage") {
            log(TAG, "setPlayedMessage executed.")
            IM5Client.getInstance().setPlayedMessage(convType, targetId, msgId)
        }
    }

    fun getLastReadMessage(
        lifecycleOwner: LifecycleOwner?,
        convType: IM5ConversationType,
        targetId: String,
        observer: IM5Observer<IMessage?>
    ): String? {
        return runOrDelay(lifecycleOwner, "getLastReadMessage") {
            log(TAG, "getLastReadMessage executed.")
            IM5Client.getInstance().getLastReadMessage(convType, targetId, observer)
        }
    }

    fun reloadMessage(
        lifecycleOwner: LifecycleOwner?,
        msgId: Long,
        convType: IM5ConversationType,
        callback: IM5Observer<IM5Message?>
    ): String? {
        return runOrDelay(lifecycleOwner, "reloadMessage") {
            IM5Client.getInstance().reloadMessage(msgId, convType, callback)
        }
    }

    fun checkPrivateSyncResult(
        lifecycleOwner: LifecycleOwner?,
        commCallback: CommCallback
    ): String? {
        return runOrDelay(lifecycleOwner, "checkPrivateSyncResult") {
            IM5Client.getInstance().checkPrivateSyncResult(commCallback)
        }
    }

    /**
     * 用于App进程在但是IM长链断开的场景下 IM转离线推送到达，这时候IM长链推送不下来，调用该方法主动去同步指定serMsgId的消息
     */
    fun onPushMessageReceived(serMsgId: Long, iM5ConversationType: IM5ConversationType): Boolean {
        log(
            TAG,
            "onPushMessageReceived executed. serMsgId = $serMsgId and iM5ConversationType = $iM5ConversationType"
        )
        return IM5Client.getInstance().onPushMessageReceived(serMsgId, iM5ConversationType)
    }

    /**
     * 查询消息是否显示过通知栏
     */
    fun isMessageDisplayed(serMsgId: Long, iM5ConversationType: IM5ConversationType): Boolean {
        log(
            TAG,
            "isMessageDisplayed executed. serMsgId = $serMsgId and iM5ConversationType = $iM5ConversationType"
        )
        return IM5Client.getInstance().isMessageDisplayed(serMsgId, iM5ConversationType)
    }

    fun cancel(uuid: String) {
        synchronized(runnableMap) {
            log(TAG, "cancelTask,uuid:$uuid")
            runnableMap.remove(uuid)
        }
    }

    fun getImDeviceParams() :ImDeviceParams{
        return IM5Client.getInstance().deviceParams.let {
            ImDeviceParams(it.deviceId, it.deviceType, it.clientVersion)
        }
    }

    fun onAuthStatusChanged(status: AuthStatus) {
        if (status == AuthStatus.LOGINED) {
            runAllDelayTasks()
        } else {
            startRetryTask()
        }
    }

    fun addReaction(
        lifecycleOwner: LifecycleOwner?,
        msgId: Long,
        convType: IM5ConversationType,
        reaction: Reaction,
        callback: IM5Observer<IMessage?>
    ): String? {
        return runOrDelay(lifecycleOwner, "addReaction") {
            IM5Client.getInstance().addReaction(convType, msgId, reaction, callback)
        }
    }

    fun removeReaction(
        lifecycleOwner: LifecycleOwner?,
        msgId: Long,
        convType: IM5ConversationType,
        reaction: Reaction,
        callback: IM5Observer<IMessage?>
    ): String? {
        return runOrDelay(lifecycleOwner, "removeReaction") {
            IM5Client.getInstance().removeReaction(convType, msgId, reaction, callback)
        }
    }

    fun updateReaction(
        lifecycleOwner: LifecycleOwner?,
        msgId: Long,
        convType: IM5ConversationType,
        oldReaction: Reaction,
        newReaction: Reaction,
        callback: IM5Observer<IMessage?>
    ): String? {
        return runOrDelay(lifecycleOwner, "updateReaction") {
            IM5Client.getInstance()
                .updateReaction(convType, msgId, oldReaction, newReaction, callback)
        }
    }

    private fun runAllDelayTasks() {
        synchronized(runnableMap) {
            runnableMap.forEach {
                log(TAG, "runDelayTask uuid:${it.key}")
                it.value.run()
            }
            clearAllDelayTask()
        }
    }

    private fun clearAllDelayTask() {
        synchronized(runnableMap) {
            runnableMap.clear()
        }
    }

    private fun removeAtOnDestroy(lifecycleOwner: LifecycleOwner?, uuid: String?) {
        GlobalScope.launch(Dispatchers.Main) {
            uuid?.let { lifecycleOwner?.doOnLifecycle(onDestroy = { <EMAIL>(uuid) }) }
        }
    }

    /**
     * 请使用waitForLogin
     */
    @Deprecated("use waitForLogin instead", ReplaceWith("waitForLogin()"))
    private suspend fun <T> runOrDelaySync(
        debugTag: String?,
        externalUUID: String? = null,
        block: suspend () -> T
    ): T {
        return suspendCancellableCoroutine<T> { cont ->
            runOrDelay(null, debugTag, externalUUID) {
                CoroutineScope(cont.context).launch {
                    cont.resumeWith(Result.success(block.invoke()))
                }
            }
        }
    }

    /**
     * 请使用waitForLogin
     */
    @Deprecated("use waitForLogin instead", ReplaceWith("waitForLogin()"))
    private suspend fun <T> runOrDelayWithCont(
        debugTag: String?,
        block: (Continuation<T>) -> Unit
    ): T {
        return suspendCancellableCoroutine { cont ->
            val uuid = runOrDelay(null, debugTag) {
                block.invoke(cont)
            }
            if (uuid != null) {
                cont.invokeOnCancellation { cancel(uuid) }
            }
        }
    }

    private suspend fun waitForLogin() {
        if (isIMLoggedIn) {
            return
        }
        val collector = object : FlowCollector<AuthStatus> {
            override suspend fun emit(value: AuthStatus) {
                if (value == AuthStatus.LOGINED) {
                    throw ExitWaitingException(this)
                }
            }
        }
        try {
            authStatusFlow.collect(collector)
        } catch (exitWaitingException: ExitWaitingException) {
            exitWaitingException.checkOwnership(collector)
        }
    }

    /**
     * @return uuid that can use for canceling the runnable.
     */
    @Deprecated("use waitForLogin instead", ReplaceWith("waitForLogin()"))
    private fun runOrDelay(
        lifecycleOwner: LifecycleOwner?,
        debugTag: String? = null,
        externalUUID: String? = null,
        block: () -> Unit
    ): String? {
        if (lifecycleOwner?.lifecycle?.currentState == Lifecycle.State.DESTROYED) {
            return null
        }
        val uuid = if (isIMLoggedIn) {
            block.invoke()
            null
        } else {
            synchronized(runnableMap) {
                // 先从外部获取UUID。没有才自己生成。因为有的地方我们无法返回到这个UUID出去。需要在外面生成传进来。
                val uuid = externalUUID ?: UUID.randomUUID().toString()
                runnableMap[uuid] = createRunnable(block)
                uuid
            }
        }
        removeAtOnDestroy(lifecycleOwner, uuid)
        debugTag?.let {
            log(
                TAG,
                "$debugTag,if run delay? ${uuid != null} ,uuid : $uuid"
            )
        }
        return uuid
    }

    private fun createRunnable(block: () -> Unit): java.lang.Runnable {
        return Runnable(block)
    }

    private fun cancelRetryJobs() {
        requestTokenJob?.cancel()
        retryLoginImJob?.cancel()
    }

    /**
     * @param forceRequestToken Set to true to disallow the token cached in device, false otherwise
     */
    private fun requestTokenAndConnect(
        userId: Long,
        forceRequestToken: Boolean,
        ignoreNetworkState: Boolean
    ) {
        Logz.tag(TAG).i("requestTokenAndConnect userId = $userId")
        if (userId == 0L || useUnifyLogin) {
            Logz.tag(TAG).w("requestTokenAndConnect ignore,userId:$userId, useUnifyLogin:$useUnifyLogin")
            return
        }
        if (requestTokenJob?.isActive == true) {
            if (userId != requestTokenUid) {
                requestTokenJob?.cancel()
            } else {
                //already have a request,do not need to do it again
                return
            }
        }


        val loginTask = {
            requestTokenJob = imViewModel.launch {
                val token = if (forceRequestToken) {
                    requestToken()
                } else {
                    //Getting the token saved in device first if exist otherwise request a new one
                    getSavedToken(userId) ?: requestToken()
                }
                Logz.tag(TAG)
                    .i("requestTokenAndConnect requestTokenJob result,isActive:$isActive,tokenNotEmpty:${token?.isNotEmpty()}")
                IMTracker.onIMTokenResult(token?.isNotEmpty()?.not() ?: true)
                if (isActive && token?.isNotEmpty() == true) {
                    savedToken(userId, token)
                    loginIm(token, userId, ignoreNetworkState)
                }
            }
        }

        if (IM5Client.getInstance().currentAuthStatus == AuthStatus.LOGINING
            || IM5Client.getInstance().currentAuthStatus == AuthStatus.LOGINED
        ) {
            if (requestTokenUid != userId) {
                val callback = object : AuthCallback {
                    override fun onSuccess() {
                        loginTask.invoke()
                    }

                    override fun onFail(p0: Int, p1: Int, p2: String?) {}
                }
                onUserLogout(callback)
            }
            return
        }
        Logz.tag(TAG).i("requestTokenAndConnect requestTokenJob launch")
        requestTokenUid = userId

        loginTask.invoke()

    }

    private fun checkLoginIM(ignoreNetworkState: Boolean): Boolean {
        if (isLogining) {
            logInfo(TAG, "loginIm.Already logging/logged,return.imAuthStatus:$imAuthStatus")
            return false
        }
        if (isIMInitialized.not()) {
            logInfo(TAG, "loginIm.im is not initialized,return")
            pendingLogin = true
            return false
        }
        if (ignoreNetworkState.not() && isNetworkAvailable.not()) {
            //network error,don't login
            logInfo(TAG, "loginIm networkUnavailable,return")
            IMTracker.onIMLoginInterceptWithNotNet()
            return false
        }
        return true
    }

    private var isLogining = false
    private fun loginIm(token: String, userId: Long, ignoreNetworkState: Boolean) {
        if (useUnifyLogin) {
            Logz.tag(TAG).w("loginIm ignore because useUnifyLogin is true")
            return
        }
        val loginIM: () -> Unit = {
            if (checkLoginIM(ignoreNetworkState)) {
                val info = IM5LoginInfo.Builder().apply {
                    setToken(token)
                    setAccid(userId.toString())
                }.build()
                logInfo(
                    TAG,
                    "loginIm.IM login start,token:${info.token},userId:${info.accid} e2ee:${ABTestManager.getE2EEInitEnable()}"
                )
                IMTracker.onIMLoginStart()
                isLogining = true
                IM5Client.getInstance().login(info, object : AuthCallback {
                    override fun onSuccess() {
                        logInfo(TAG, "IM login succeed")
                        IMTracker.onIMLoggedResult(true, null)
                        isLogining = false
                    }

                    override fun onFail(errorType: Int, errorCode: Int, errorMsg: String?) {
                        logError(
                            TAG,
                            "im login failed.errorType:${errorType},errorCode:${errorType},errorMsg:${errorMsg}"
                        )
                        IMTracker.onIMLoggedResult(
                            false,
                            IMErrorInfo(errorType, errorCode, errorMsg)
                        )
                        isLogining = false
                    }
                })
            }
        }

        if (ABTestManager.FORCE_CLOSE_E2EE) {
            loginIM()
        } else {
            resetE2EEStatus()
            E2EEBrigdeFactory.e2eeBridgeInstance.initE2EEBride(
                E2EEBridgeConfig(
                    e2eeConfigure,
                    ABTestManager.getE2EEInitEnable()
                )
            ) {
                logInfo(
                    TAG, "E2EE init over,userId:$userId,appkey:${getE2EEAppKey()}, " +
                            "serverEnv:${ServerEnv.PRODUCT_US},deviceId:${deviceId}"
                )
                loginIM()
            }
        }
    }

    /**
     * 重置端到端相关状态
     */
    private fun resetE2EEStatus() {
        ABTestManager.resetE2EEABTest()
        e2eeConfigure = E2EEConfigure(
            appkey = getE2EEAppKey(),
            serverEnv = ServerEnv.PRODUCT_US,
            userId = userId.toString(),
            deviceId = deviceId,
            enableGroupE2EE = false, // 禁用群聊加密
            enableAutoSyncSK = false
        )
    }


    /**
     * E2EE相关配置
     */
    private var e2eeConfigure = E2EEConfigure(
        appkey = getE2EEAppKey(),
        serverEnv = ServerEnv.PRODUCT_US,
        userId = userId.toString(),
        deviceId = deviceId,
        enableGroupE2EE = false, // 禁用群聊加密
        enableAutoSyncSK = false
    )


    private fun getSavedToken(userId: Long): String? {
        return mmkv.getString("imtoken_${userId}", null).takeIf { !it.isNullOrEmpty() }
    }

    private fun savedToken(userId: Long, token: String) {
        mmkv.putString("imtoken_${userId}", token)
    }

    private suspend fun requestToken(): String? {
        return imViewModel.getImToken()
    }

    private fun doOnDestroy(lifecycleOwner: LifecycleOwner?, block: () -> Unit) {
        MainScope().launch(Dispatchers.Main) {
            lifecycleOwner?.doOnLifecycle(
                onDestroy = block
            )
        }
    }

    /**
     * return a temp token for test
     */
    private fun getTestToken(): String {
        return "6e4e9da2dbbd43ac8f78a01429d7c7c7"
    }

    private fun getE2EEAppKey(): String {
        var appKey = E2EE_APP_KEY_PRODUCTION
        if (isDebug || isReleaseLogBuildType) {
            if (AppEnvironment.TOWER == Environments.getEnv(appContext)) {
                appKey = E2EE_APP_KEY_DEVELOP
            } else if (AppEnvironment.PRE == Environments.getEnv(appContext)) {
                appKey = E2EE_APP_KEY_PRODUCTION
            }
        }
        Logz.tag(TAG).d("getE2EEAppKey AppKey = $appKey")
        return appKey
    }

    /**
     * @param reason 预留参数，准备视频消息处理失败原因，可用于下次重发是获取使用()，目前压缩失败不处理原因，后续做剪辑等操作可能使用
     * @see IM5VideoMessage.getReason 获取处理失败原因
     */
    fun prepareVideoMessageFailed(
        lifecycleOwner: LifecycleOwner?, message: IMessage, reason: String,
        observer: IM5Observer<IM5Message>? = null
    ) {
        runOrDelay(lifecycleOwner, "prepareVideoMessageFailed") {
            IM5Client.getInstance().prepareVideoMessageFailed(
                message.conversationType,
                message.msgId,
                reason,
                observer
            )
        }
    }

    suspend fun cancelSendingMessage(
        convType: IM5ConversationType,
        msgId: Long
    ): Pair<IM5Message?, IMErrorInfo?> {
        return runOrDelaySync("cancelSendingMessage") {
            suspendCancellableCoroutine { cont ->
                IM5Client.getInstance()
                    .cancelSendingMessage(convType, msgId, object : IM5Observer<IM5Message> {
                        override fun onEvent(msg: IM5Message?) {
                            cont.resumeWith(Result.Companion.success(msg to null))
                        }
                        override fun onError(errType: Int, errCode: Int, errMsg: String?) {
                            cont.resumeWith(Result.Companion.success(null to IMErrorInfo(errType, errCode, errMsg)))
                        }
                    })
            }
        }
    }


    private fun initIM5ReportListener() {
        val eventKeys = listOf(EVENT_IM5_CLIENT_FILE_UPLOAD, EVENT_IM5_CLIENT_MESSAGE_SEND)
        IM5Client.getInstance().setIM5ReportListener(eventKeys, object : IM5ReportEventListener {
            override fun onEventReport(key: String?, data: MutableMap<String, Any>?) {
                logDebug(TAG, "onEventReport:$key")
                if (key == null || data == null || eventKeys.contains(key).not()) return
                when (key) {
                    EVENT_IM5_CLIENT_FILE_UPLOAD -> {
                        logDebug(TAG, "setIM5ReportListener key = $key, data = $data")
                        val isVideo = (data["contentType"] as String).startsWith("video")
                        val uploadDuration = data["costTime"] as Long
                        val traceID = data["msgTraceId"] as? String
                        val mediaUploadSize = data["fileSize"] as Long
                        val isSuccess = data["result"] as String == "success"
                        val failReason = data["errorMsg"] as? String
                        IMTracker.onResultMediaUpload(
                            isVideo,
                            uploadDuration,
                            traceID,
                            mediaUploadSize / 1024,
                            isSuccess,
                            failReason
                        )
                    }

                    EVENT_IM5_CLIENT_MESSAGE_SEND -> {
                        logMessageResendResult(data)
                    }
                }
            }
        })
    }

    private fun logMessageResendResult(data: MutableMap<String, Any>?) {
        if (data == null) {
            return
        }
        val autoSend = data["autoSend"] as? Int ?: 0
        if (autoSend != 1) {
            //not auto resend,return
            return
        }
        val convType = data["convType"] as? Int ?: 0

        val targetId = data["targetId"] as? String ?: ""
        val traceId = data["msgTraceId"] as? String ?: ""
        val reqTime = data["reqTime"] as? Long ?: 0
        val endTime = IM5ReportUtils.getNTPTime()
        val errorType = data["errorType"] as? Int ?: 0
        val errorCode = data["errorCode"] as? Int ?: 0
        val errorDesc = data["errorDesc"] as? String
        val msgType = data["msgType"] as Int
        val rcode = data["rcode"] as Int? ?: 0

        val convStr = when (convType) {
            1 -> if (targetId.toSafeLong().isMe()) "self" else "private"
            3 -> "group"
            else -> "errorType"
        }
        IMTracker.onResultMsgAutoResend(
            rcode == 0,
            convStr, msgType, targetId, traceId, reqTime, endTime, errorType, errorCode, errorDesc
        )
    }

    fun getMediaCache(userLifecycleOwner: UserLifecycle, cacheType: Int, callback: (Long) -> Unit) {
        if (isIMLoggedIn.not()) {
            callback.invoke(0)
            return
        }
        runOrDelay(userLifecycleOwner, "getMediaCache") {
            IM5Client.getInstance().getCache(cacheType, object : IM5Observer<Long> {
                override fun onEvent(size: Long) {
                    callback.invoke(size)
                }

                override fun onError(p0: Int, p1: Int, p2: String?) {
                    callback.invoke(0)
                }
            })
        }
    }

    fun getCacheByTargetId(
        userLifecycleOwner: UserLifecycle,
        targetId: String,
        convType: IM5ConversationType,
        cacheType: Int, callback: (Long) -> Unit
    ) {
        val result = runOrDelay(userLifecycleOwner, "getCacheByTargetId") {
            IM5Client.getInstance()
                .getCacheByTargetId(convType, targetId, cacheType, object : IM5Observer<Long> {
                    override fun onEvent(size: Long) {
                        callback.invoke(size)
                    }

                    override fun onError(p0: Int, p1: Int, p2: String?) {
                        callback.invoke(0)
                    }
                })
        }
        if (result.isNotNull()) {
            callback.invoke(0)
        }
    }

    /**
     * 删除当前用户所有的缓存
     */
    fun removeCache(userLifecycleOwner: UserLifecycle, cacheType: Int) {
        removeCache(userLifecycleOwner, Long.MAX_VALUE, cacheType)
    }

    /**
     * @param beforeTimeStamp 设置Long.MAX，表示删除所有，判断文件修改时间小于这个时间的
     */
    fun removeCache(userLifecycleOwner: UserLifecycle, beforeTimeStamp: Long, cacheType: Int) {
        runOrDelay(userLifecycleOwner, "removeCache") {
            IM5Client.getInstance().removeCache(cacheType, beforeTimeStamp, object : CommCallback {
                override fun onSuccess() {
                }

                override fun onFail(p0: Int, p1: Int, p2: String?) {
                }
            })
        }
    }


    fun removeCacheByTargetId(
        userLifecycleOwner: UserLifecycle,
        targetId: String,
        convType: IM5ConversationType,
        cacheType: Int
    ) {
        runOrDelay(userLifecycleOwner, "removeCacheByTargetId") {
            IM5Client.getInstance()
                .removeCacheByTargetId(convType, targetId, cacheType, object : CommCallback {
                    override fun onSuccess() {
                    }

                    override fun onFail(p0: Int, p1: Int, p2: String?) {
                    }
                })
        }
    }

    fun recallMessage(
        lifecycleOwner: LifecycleOwner?,
        convType: IM5ConversationType,
        msgId: Long,
        pushContent: String,
        pushPayLoad: String,
        isKeepOriginalContent: Boolean,
        observer: IM5Observer<IMessage>? = null
    ): String? {
        return runOrDelay(lifecycleOwner, "recallMessage") {
            log(TAG, "recallMessage executed.")
            IM5Client.getInstance().recallMessage(
                convType,
                msgId,
                pushContent,
                pushPayLoad,
                isKeepOriginalContent,
                observer
            )
        }
    }

    fun forwardMessage(
        lifecycleOwner: LifecycleOwner?,
        convType: IM5ConversationType,
        msgId: Long,
        targetConvType: IM5ConversationType,
        targetId: String,
        msgTraceId: String?,
        pushContent: String,
        pushPayLoad: String,
        userInfo: UserInfo,
        callback: MessageCallback?
    ): String? {
        return runOrDelay(lifecycleOwner, "forwardMessage") {
            log(TAG, "forwardMessage executed.")
            IM5Client.getInstance().forwardMessage(
                convType,
                msgId,
                targetConvType,
                targetId,
                msgTraceId,
                pushContent,
                pushPayLoad,
                userInfo,
                callback
            )
        }
    }

    fun getLocalMessages(
        lifecycleOwner: LifecycleOwner?,
        convType: IM5ConversationType,
        messageIdList: List<Long>,
        callback: IM5Observer<List<IMessage>?>
    ): String? {
        return runOrDelay(lifecycleOwner, "getMessage") {
            IM5Client.getInstance().getLocalMessages(convType, messageIdList, callback)
        }
    }

    fun getSendingMessageInfoByMsgTypes(
        lifecycleOwner: LifecycleOwner?,
        msgTypes: List<Int>,
        callback: IM5Observer<IM5SendingMessageInfo>
    ): String? {
        val uuid = runOrDelay(lifecycleOwner, "getUploadingFileInfo") {
            IM5Client.getInstance().getSendingMessageInfoByMsgTypes(msgTypes, callback)
        }
        return uuid
    }

    private fun createPrepareMessageSyncCallback(cont: Continuation<SendMsgResult>): IM5Observer<IM5Message> {
        return object : IM5Observer<IM5Message> {
            override fun onEvent(newMsg: IM5Message?) {
                cont.resumeWith(Result.success(SendMsgResult.SendMsgSuccess(newMsg!!)))
            }

            override fun onError(errType: Int, errCode: Int, errMsg: String?) {
                cont.resumeWith(
                    Result.success(
                        SendMsgResult.SendMsgFailed(
                            null,
                            IMErrorInfo(
                                errType,
                                errCode,
                                errMsg
                            )
                        )
                    )
                )
            }
        }
    }

    private fun createSendMessageSyncCallbackNew(cont: Continuation<SendMsgResult>): PreprocessMessageCallback {
        return object : PreprocessMessageCallback {


            override fun onAttached(messageInfo: IMessage?) {
            }

            override fun onSuccess(messageInfo: IMessage) {
                cont.resumeWith(Result.success(SendMsgResult.SendMsgSuccess(messageInfo)))
            }

            override fun onError(
                message: IMessage?,
                errorType: Int,
                errorCode: Int,
                errorMsg: String?
            ) {
                val sendResult = SendMsgResult.SendMsgFailed(
                    message,
                    IMErrorInfo(
                        errorType,
                        errorCode,
                        errorMsg
                    )
                )
                cont.resumeWith(Result.success(sendResult))
            }

            override fun onMessageProcessing(message: IM5Message) {

            }

            override fun onProgress(message: IMessage?, totalSize: Long, currentSize: Long) {

            }

            override fun onCanceled(message: IMessage?) {

            }
        }
    }

    private fun createSendMessageSyncCallback(cont: Continuation<IMErrorInfo?>): MessageCallback {
        return object : MessageCallback {
            override fun onAttached(messageInfo: IMessage?) {
            }

            override fun onSuccess(messageInfo: IMessage?) {
                cont.resumeWith(Result.success(null))
            }

            override fun onError(
                message: IMessage?,
                errorType: Int,
                errorCode: Int,
                errorMsg: String?
            ) {
                cont.resumeWith(
                    Result.success(
                        IMErrorInfo(
                            errorType,
                            errorCode,
                            errorMsg
                        )
                    )
                )
            }
        }
    }

    private val iMSignalManagerRef by lazy {
        AtomicReference<ISignalManagerPresenter>(
            InnerIMSignalManager(IM5Client.getInstance().imSignalManager)
        )
    }

    fun obtainISignalManagerPresenter(): ISignalManagerPresenter {
        return iMSignalManagerRef.get()
    }

    suspend fun previewVoiceFilter(message: IM5Message): String? {
        return runOrDelayWithCont("previewVoiceFilter") { cont ->
            logDebug("VoiceFilter", "start previewVoiceFilter")
            val previewId = IM5Client.getInstance().previewVoiceFilter(message, object : VoiceFilterCallback {
                override fun onProgress(
                    message: IMessage,
                    totalSize: Long,
                    currentSize: Long
                ) {
                    logDebug("VoiceFilter", "onProgress $totalSize $currentSize")
                }

                override fun onVoiceFilterError(
                    errorType: Int,
                    errorCode: Int,
                    errorMsg: String
                ) {
                    logDebug("VoiceFilter", "onVoiceFilterError $errorCode $errorMsg")
                    _msgVoiceFilterFlow.emitInScope(
                        userLifecycleScope,
                        IMMessageVoiceFilterData(
                            FilterFail,
                            message,
                            VoiceFilterError(errorType, errorCode, errorMsg)
                        )
                    )
                    IMTracker.voiceFilterFailConvertResult(
                        isPrivate = message.isPrivateMessage,
                        filterId = message.getVoiceFilterId ?: 0L,
                        duration = message.getVoiceDurationSecond().asMillisecond().value,
                        code = errorCode,
                        isSuccess = false
                    )
                }

                override fun onVoiceFilterSuccess(message: IM5Message) {
                    logDebug("VoiceFilter", "onVoiceFilterSuccess")
                    routerServices<IGlobalOnAirController>()
                    _msgVoiceFilterFlow.emitInScope(
                        userLifecycleScope,
                        IMMessageVoiceFilterData(FilterSuccess, message, null)
                    )
                    IMTracker.voiceFilterFailConvertResult(
                        isPrivate = message.isPrivateMessage,
                        filterId = message.getVoiceFilterId ?: 0L,
                        duration = message.getVoiceDurationSecond().asMillisecond().value,
                        code = null,
                        isSuccess = true
                    )
                }

                override fun onVoiceFiltering(message: IM5Message) {
                    logDebug("VoiceFilter", "onVoiceFiltering")
                    _msgVoiceFilterFlow.emitInScope(
                        userLifecycleScope,
                        IMMessageVoiceFilterData(FilterIng, message, null)
                    )
                }

            })
            cont.resumeWith(Result.success(previewId))
        }
    }

    fun cancelPreviewVoiceFilter(previewId: String) {
        IM5Client.getInstance().cancelPreviewVoiceFilter(previewId)
    }


    suspend fun sendPreprocessMessage(message: IM5Message, callback: PreprocessMessageCallback?) {
        if (message.getConvTargetIdLong() <= 0) {
            return
        }
        return runOrDelaySync("sendVoiceFilterMessage") {
            IM5Client.getInstance().sendPreprocessMessage(message, callback)
        }
    }

    suspend fun sendMessage(message: IM5Message, callback: MessageCallback?) {
        if (message.getConvTargetIdLong() <= 0) {
            return
        }
        return runOrDelaySync("sendMessage") {
            IM5Client.getInstance().sendMessage(message, callback)
        }
    }

    suspend fun sendMediaMessage(message: IM5Message, callback: MediaMessageCallback?) {
        if (message.getConvTargetIdLong() <= 0) {
            return
        }
        return runOrDelaySync("sendMediaMessage") {
            IM5Client.getInstance().sendMediaMessage(message, callback)
        }
    }


    suspend fun updateConversationTime(param: IM5ConversationUpdateTimeParam) {
        waitForLogin()
        IM5Client.getInstance().updateConversationTime(param)
    }

    suspend fun updateMultipleConversationsTime(paramList: List<IM5ConversationUpdateTimeParam>) {
        return runOrDelaySync("updateMultipleConversationsTime") {
            IM5Client.getInstance().updateMultipleConversationsTime(paramList)
        }
    }

    fun enterChannel(channelId: String, heartInterval: Long) {
        IM5Client.getInstance().enterChannel(channelId, heartInterval)

    }

    fun leaveChannel(channelId: String) {
        IM5Client.getInstance().leaveChannel(channelId)

    }

    fun pauseUploadMediaMessageByConversation(
        lifecycle: UserLifecycle,
        targetId: String,
        convType: IM5ConversationType
    ) {
        runOrDelay(lifecycle, "pauseUploadMediaMessageByConversation") {
            IM5Client.getInstance().pauseUploadMediaMessageByConversation(convType, targetId)
        }
    }

    fun startSceneHeartBeat(scene: Int, identifier: String?, timeInterval: Long) {
        logInfo(TAG,"startSceneHeartBeat scene = $scene identifier = $identifier timeInterval = $timeInterval")
        IM5Client.getInstance().startSceneHeartBeat(scene, identifier, timeInterval)
    }

    fun stopSceneHeartBeat(scene: Int) {
        logInfo(TAG,"stopSceneHeartBeat scene = $scene ")
        IM5Client.getInstance().stopSceneHeartBeat(scene)
    }

}