package com.interfun.buz.im.repo

import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.webkit.MimeTypeMap
import androidx.core.content.FileProvider
import com.interfun.buz.base.ktx.*
import com.interfun.buz.base.manager.retry.IntervalStrategy
import com.interfun.buz.base.manager.retry.RetryManager
import com.interfun.buz.common.R
import com.interfun.buz.common.database.entity.MediaCacheType
import com.interfun.buz.common.di.NetworkSwitchType
import com.interfun.buz.common.ktx.uid
import com.interfun.buz.common.manager.GlobalEventManager
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.manager.storage.BuzMediaRecordManager
import com.interfun.buz.component.hilt.GlobalQualifier
import com.interfun.buz.component.hilt.UserQualifier
import com.interfun.buz.download.BuzDownLoadManager
import com.interfun.buz.download.IDownloadManager
import com.interfun.buz.download.bean.DownloadCancelInfo
import com.interfun.buz.download.bean.DownloadProgressChange
import com.interfun.buz.download.bean.DownloadStateInfo
import com.interfun.buz.download.bean.DownloadStatus
import com.interfun.buz.download.db.entity.BuzDownloadCacheEntity
import com.interfun.buz.im.repo.DownloadNotificationModel.StopDownload
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import java.io.File
import javax.inject.Inject

/**
 * Author: ChenYouSheng
 * Date: 2025/6/6
 * Email: <EMAIL>
 * Desc:
 */
class IMFileDownloadRepositoryImpl @Inject constructor(
    @UserQualifier private val userScope: CoroutineScope,
    @GlobalQualifier private val networkSwitchType: NetworkSwitchType,
    @ApplicationContext private val appContext: Context
) : IMFileDownloadRepository {

    companion object {
        private const val TAG = "IMFileDownloadRepositoryImpl"
        private const val DOWNLOAD_HOME = "buz_im_file_download"
    }

    override var allowDownloadWithMobileData: Boolean = false
        get() {
            return if (isWifiConnected) true else field
        }

    private val userDownloadManager: IDownloadManager by lazy {
        BuzDownLoadManager(
            appContext = appContext,
            downloadScope = userScope,
            storagePathName = DOWNLOAD_HOME,
            logger = object : IDownloadManager.Logger {
                override fun logI(tag: String, msg: String) {
                    logInfo(tag, msg)
                }

                override fun logE(tag: String, e: Throwable?, msg: String) {
                    logError(tag, e, msg)
                }
            }
        )
    }

    private val _notificationFlow = MutableSharedFlow<DownloadNotificationModel>()

    override val progressFlow: SharedFlow<DownloadProgressChange>
        get() = userDownloadManager.progressFlow

    override val stateFlow: SharedFlow<DownloadStateInfo>
        get() = userDownloadManager.stateFlow

    override val notificationFlow: SharedFlow<DownloadNotificationModel> = _notificationFlow.asSharedFlow()

    init {
        logInfo(TAG, "IMFileDownloadRepository init==> isMobileData:${isMobileData}, " +
                "isWifiConnected:${isWifiConnected}")

        userScope.launch {
            GlobalEventManager.onServiceDestroyStateFlow.collect { isDestroy ->
                if (isDestroy) {
                    userDownloadManager.cancelAll()
                    allowDownloadWithMobileData = false
                }
            }
        }
    }

    private fun insertFileCacheRecord(
        fileName: String,
        remoteUrl: String,
        serMsgId: String,
        conversationType: IM5ConversationType,
        targetId: String,
        fromId: String
    ) {
        BuzMediaRecordManager.insertOrUpdateMediaCacheRecord(
            userId = UserSessionManager.uid,
            mediaUrl = remoteUrl,
            mediaIndex = userDownloadManager.generateTaskId(url = remoteUrl, fileName = fileName),
            mediaType = MediaCacheType.File,
            serMsgId = serMsgId,
            conversationType = conversationType,
            targetId = targetId,
            fromId = fromId,
        )
    }

    override suspend fun openFile(
        context: Context,
        localPath: String?,
        remoteUrl: String?,
        fileName: String,
        serMsgId: String,
        conversationType: IM5ConversationType,
        targetId: String,
        fromId: String
    ): FileOpenResult {
        return withContext(Dispatchers.IO) {
            try {
                when {
                    // Sent file message with localPath, or remoteUrl (forwarded file message)
                    !localPath.isNullOrEmpty() -> {
                        val localFile = File(localPath)
                        if (localFile.exists() && localFile.length() > 0) {
                            val result = openFileWithPath(context, localPath)
                            if (result == FileOpenResult.Success) {
                                insertFileCacheRecord(
                                    fileName = fileName,
                                    remoteUrl = remoteUrl ?: "",
                                    serMsgId = serMsgId,
                                    conversationType = conversationType,
                                    targetId = targetId,
                                    fromId = fromId
                                )
                            }
                            result
                        } else if (!remoteUrl.isNullOrEmpty()) {
                            openDownloadedFile(
                                context = context,
                                url = remoteUrl,
                                fileName = fileName,
                                serMsgId = serMsgId,
                                conversationType = conversationType,
                                targetId = targetId,
                                fromId = fromId
                            )
                        } else {
                            FileOpenResult.FileNotFound
                        }
                    }
                    // Received file message with remoteUrl
                    !remoteUrl.isNullOrEmpty() -> {
                        openDownloadedFile(
                            context = context,
                            url = remoteUrl,
                            fileName = fileName,
                            serMsgId = serMsgId,
                            conversationType = conversationType,
                            targetId = targetId,
                            fromId = fromId
                        )
                    }

                    else -> {
                        FileOpenResult.FileNotFound
                    }
                }
            } catch (e: Exception) {
                logError(TAG, "Error opening file", e)
                FileOpenResult.Error(e)
            }
        }
    }

    override suspend fun queryAllDownloadCache(): List<BuzDownloadCacheEntity> {
        return userDownloadManager.queryAllDownloadCache()
    }

    override suspend fun removeCacheList(cacheList: List<BuzDownloadCacheEntity>) {
        userDownloadManager.removeCacheList(cacheList)
    }

    private fun openFileWithPath(context: Context, filePath: String): FileOpenResult {
        return try {
            val file = File(filePath)
            if (!file.exists() || file.length() == 0L) {
                return FileOpenResult.FileNotFound
            }

            val uri = FileProvider.getUriForFile(
                context,
                "${context.applicationContext.packageName}.fileprovider",
                file
            )
            val fileExt = uri.fileExtension ?: file.extension
            val mimeType = uri.mimeType ?: getMimeTypeFromExtension(fileExt)

            if (mimeType == "application/vnd.android.package-archive" || fileExt == "apk") {
                return FileOpenResult.IsApk
            }

            val intent = Intent(Intent.ACTION_VIEW).apply {
                setDataAndType(uri, mimeType)
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            }
            context.startActivity(intent)
            FileOpenResult.Success
        } catch (e: ActivityNotFoundException) {
            logDebug(TAG, "No app found to open file: $filePath")
            FileOpenResult.NoSupportedApp
        } catch (e: Exception) {
            logError(TAG, "Error opening file with path: $filePath", e)
            FileOpenResult.Error(e)
        }
    }

    private suspend fun openDownloadedFile(
        context: Context,
        url: String,
        fileName: String,
        serMsgId: String,
        conversationType: IM5ConversationType,
        targetId: String,
        fromId: String
    ): FileOpenResult {
        val downloadedFile = getDownloadFile(url = url, fileName = fileName)
        if (downloadedFile?.exists() != true || downloadedFile.length() == 0L) {
            return FileOpenResult.FileNotFound
        }
        val result = openFileWithPath(context, downloadedFile.absolutePath)
        if (result is FileOpenResult.Success) {
            insertFileCacheRecord(
                fileName = fileName,
                remoteUrl = url,
                serMsgId = serMsgId,
                conversationType = conversationType,
                targetId = targetId,
                fromId = fromId
            )
        }
        return result
    }

    private fun getMimeTypeFromExtension(extension: String): String {
        return MimeTypeMap.getSingleton().getMimeTypeFromExtension(extension.lowercase())
            ?: "application/octet-stream"
    }

    override suspend fun deleteFile(url: String, fileName: String): Boolean {
        return withContext(userScope.coroutineContext) {
            userDownloadManager.deleteFile(
                url = url,
                fileName = fileName
            )
        }
    }

    override suspend fun getAllDownloadFileSize(): Long {
        return withContext(userScope.coroutineContext) {
            userDownloadManager.getAllDownloadFileSize()
        }
    }

    override fun download(
        url: String,
        fileName: String,
        serverFileSize: Long,
        conversationType: IM5ConversationType,
        serMsgId: String,
        targetId: String,
        fromId: String,
        source: String
    ) {
        userScope.launch {
            if (isMobileData && !isWifiConnected) {
                // 仅流量下下载才表示用户同意了使用流量下载
                allowDownloadWithMobileData = true
            }
            BuzMediaRecordManager.insertOrUpdateMediaCacheRecord(
                userId = UserSessionManager.uid,
                mediaUrl = url,
                mediaIndex = userDownloadManager.generateTaskId(url = url, fileName = fileName),
                mediaType = MediaCacheType.File,
                serMsgId = serMsgId,
                conversationType = conversationType,
                targetId = targetId,
                fromId = fromId,
            )
            userDownloadManager.pendingDownload(
                url = url,
                fileName = fileName,
                serverFileSize = serverFileSize,
                source = source
            )
        }
    }

    override suspend fun getDownloadStateInfo(
        url: String,
        fileName: String?,
        checkDatabase: Boolean
    ): DownloadStateInfo {
        return withContext(userScope.coroutineContext) {
            userDownloadManager.getDownloadStateInfo(
                url = url,
                fileName = fileName,
                checkDataBase = checkDatabase
            )
        }
    }

    override suspend fun pause(url: String, fileName: String?) {
        withContext(userScope.coroutineContext) {
            userDownloadManager.pause(url = url, fileName = fileName)
        }
    }

    override suspend fun resume(url: String, fileName: String?) {
        withContext(userScope.coroutineContext) {
            userDownloadManager.resume(url = url, fileName = fileName)
        }
    }

    override suspend fun getDownloadFile(url: String, fileName: String?): File? {
        return withContext(userScope.coroutineContext) {
            userDownloadManager.getDownloadFile(url, fileName)
        }
    }

    override suspend fun getDownloadFileSize(url: String, fileName: String?): Long {
        return withContext(userScope.coroutineContext) {
            userDownloadManager.getDownloadFileSize(url, fileName)
        }
    }

    override suspend fun getAllDownloadFailureCache(): List<BuzDownloadCacheEntity> {
        return withContext(userScope.coroutineContext) {
            userDownloadManager.getAllDownloadFailureCache()
        }
    }

    override suspend fun resumeHistoryDownloading() {
        allowDownloadWithMobileData = false
        // 重启App进入首页后，展示暂停下载通知或者恢复下载
        showPauseNotificationOrResumeDownloadWhenRestart()
        // 网络切换暂停下载
        pauseDownloadWhenChangeNetwork()
    }

    private suspend fun showPauseNotificationOrResumeDownloadWhenRestart() {
        val downloadCacheList = queryAllDownloadCache()
        val startOrPendingCacheList = downloadCacheList.filter {
            it.status == DownloadStatus.STARTED.value || it.status == DownloadStatus.PENDING.value
        }
        logInfo(TAG, "resumeHistoryDownloading startOrPendingCacheList:${startOrPendingCacheList.size}")
        if (startOrPendingCacheList.isEmpty()) {
            return
        }
        // 重启后，如果是cancel 和 failure的 就不恢复下载， 如果是start和pending的就恢复(例如app crash 导致了有些task处于start 或者pending状态)
        if (isWifiConnected) {
            startOrPendingCacheList.forEach {
                userDownloadManager.pendingDownload(
                    url = it.remoteUrl,
                    fileName = it.fileName,
                    serverFileSize = it.totalLength
                )
            }
            logInfo(TAG, "resumeHistoryDownloading start pendingDownload in wifi")
        }
    }

    private var resumeErrorDownloadJob: Job? = null

    private suspend fun pauseDownloadWhenChangeNetwork() {
        networkSwitchType.flow.collect { netEvent ->
            tryResumeTheNetInterruptDownloadTask(netEvent)
            tryShowPauseDownloadNotification(netEvent)
        }
    }

    private fun tryShowPauseDownloadNotification(netEvent: NetworkSwitchEvent) {
        userScope.launch {
            if (netEvent is NetworkSwitchEvent.WifiToCellular && !allowDownloadWithMobileData) {
                // 当WiFi切换到移动网络时，如果之前没有同意过使用流量下载，则下载暂停
                val tip = R.string.notification_current_download_stopped_by_net_change.asString()
                val cancelInfoList = userDownloadManager.cancelAll()
                // 排除暂停个数，失败的不用排出，因为断网导致的就是正常下载的
                val count = cancelInfoList.count { it !is DownloadCancelInfo.PauseInfo }
                if (count > 0) {
                    _notificationFlow.emit(StopDownload(tip))
                }
                logInfo(TAG, "WifiToCellular, no allow download, cancel all download tasks:$count")
            }
            // 断网时，停止所有下载任务并弹窗通知用户
            else if (netEvent is NetworkSwitchEvent.Disconnected) {
                val tip = R.string.notification_current_download_stopped_by_net_error.asString()
                val cancelInfoList = userDownloadManager.cancelAll()
                // 排除用户手动暂停的个数，失败的不用排出，因为断网导致的就是正常下载的
                val count = cancelInfoList.count { it !is DownloadCancelInfo.PauseInfo }
                if (count > 0) {
                    _notificationFlow.emit(StopDownload(tip))
                }
                logInfo(TAG, "Disconnected, cancel all download tasks:$count, emit:$_notificationFlow")
            }
            logInfo(
                TAG, "netEvent:${netEvent}, " +
                        "allowDownloadWithMobileData:${allowDownloadWithMobileData}"
            )
        }
    }

    private fun tryResumeTheNetInterruptDownloadTask(netEvent: NetworkSwitchEvent) {
        resumeErrorDownloadJob?.cancel()
        if (netEvent is NetworkSwitchEvent.WifiToCellular && allowDownloadWithMobileData) {
            logInfo(TAG, "WifiToCellular, allow download, resume all download error tasks")
            userScope.launch {
                userDownloadManager.resumeFailureTask()
            }
        } else if (netEvent is NetworkSwitchEvent.CellularToWifi) {
            // 仅流量切换Wi-Fi+流量，中途会断网，然后再过渡到Wi-Fi，此时需要重试中断的请求，最多重试10s
            logInfo(TAG, "CellularToWifi, resume all download error tasks")
            resumeErrorDownloadJob = userScope.launch {
                val retryStrategy = object : IntervalStrategy {
                    override fun interval(time: Int): Long {
                        return 1000 // 每隔1s重试一次
                    }

                    override fun reset() {}
                }
                RetryManager().retryImmediately(intervalStrategy = retryStrategy) { time->
                    logDebug(TAG, "resumeFailureTask retryInterval: $time")
                    if (time > 10) {
                        return@retryImmediately true // 重试10秒结束
                    }
                    return@retryImmediately userDownloadManager.resumeFailureTask() > 0
                }
            }
        }
    }

}