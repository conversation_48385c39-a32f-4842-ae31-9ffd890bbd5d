package com.interfun.buz.im.repo

import com.interfun.buz.im.IMAgent
import com.interfun.buz.im.ktx.BuzSendingState
import com.interfun.buz.im.util.IMMsgIdentity
import kotlinx.coroutines.flow.*
import javax.inject.Inject

internal class MessageSendStateRepositoryImpl @Inject constructor() : MessageSendStateRepository {

    private val updateObservedMessage = MutableStateFlow<Set<IMMsgIdentity>>(emptySet())

    override val msgSendStateFlow: Flow<Map<IMMsgIdentity, BuzSendingState>> = channelFlow {
        updateObservedMessage.collectLatest { msgIdentities ->
            IMAgent.getMessageSendStateFlow(msgIdentities).collect { sendStateMap ->
                send(sendStateMap)
            }
        }
    }

    override fun updateMessages(msgIdentities: Set<IMMsgIdentity>) {
        updateObservedMessage.value = msgIdentities
    }
}