package com.interfun.buz.im.track

import android.content.Context
import android.media.AudioDeviceInfo
import android.media.AudioManager
import android.os.Build
import android.os.Build.VERSION_CODES
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.constants.CommonMMKV
import com.interfun.buz.common.constants.RouterParamKey
import com.interfun.buz.common.constants.TrackConstant
import com.interfun.buz.common.ktx.*
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.manager.cache.user.UserRelationCacheManager
import com.interfun.buz.common.manager.user.FriendStatusManager
import com.interfun.buz.common.manager.userLifecycleScope
import com.interfun.buz.common.service.ChatService
import com.interfun.buz.common.service.IVoiceEmojiService
import com.interfun.buz.common.tracker.trackerIMString
import com.interfun.buz.common.utils.BuzTracker
import com.interfun.buz.common.utils.logAFAndFireBaseEvent
import com.interfun.buz.im.IMErrorInfo
import com.interfun.buz.im.entity.IMMessageContentExtra
import com.interfun.buz.im.entity.IMSendFrom
import com.interfun.buz.im.entity.IMType
import com.interfun.buz.common.bean.chat.MentionedUser
import com.interfun.buz.common.manager.cache.user.UserSettingManager
import com.interfun.buz.im.ktx.*
import com.interfun.buz.im.message.BuzFileMessage
import com.interfun.buz.im.message.BuzLocationMessage
import com.interfun.buz.im.message.BuzTextMsg
import com.interfun.buz.im.message.ImVoiceEmojiCategoryType
import com.interfun.buz.im.message.VoiceTextMsg
import com.interfun.buz.im.message.WTVoiceEmojiMsg
import com.interfun.buz.im.message.WTVoiceMsg
import com.lizhi.component.basetool.common.AppStateWatcher
import com.lizhi.component.basetool.ntp.NtpTime
import com.lizhi.component.itnet.base.isDozeMode
import com.lizhi.component.itnet.base.isLightDozeMode
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import com.lizhi.im5.sdk.conversation.IM5ConversationType.PRIVATE
import com.lizhi.im5.sdk.message.IMessage
import com.lizhi.im5.sdk.message.MsgReferenceStatus
import com.lizhi.im5.sdk.message.model.IM5VoiceMessage
import com.lizhi.im5.sdk.utils.IM5MsgUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import okhttp3.internal.toLongOrDefault

object IMTracker {


    fun onIMLoginInterceptWithNotNet() {
        BuzTracker.onEvent("CLIENT_IM_LOGIN_NOT_NET") {
            put("isInForeground", isAppInForeground)
            val time = System.currentTimeMillis() - (UserSessionManager.loginTime
                ?: System.currentTimeMillis())
            put("timeBetweenLocalLogin", time.toString())
            val isDozeMode = isDozeMode()
            val isLightDozeMode = isLightDozeMode()
            put("isDozeMode", isDozeMode)
            put("isLightDozeMode", isLightDozeMode)
            put("isNetworkValid", isNetworkAvailable)
        }
    }

    fun onIMLoginStart() {
        BuzTracker.onEvent("CLIENT_IM_LOGIN_START") {
            put("isInForeground", isAppInForeground)
            val time = System.currentTimeMillis() - (UserSessionManager.loginTime
                ?: System.currentTimeMillis())
            put("timeBetweenLocalLogin", time.toString())
            val isDozeMode = isDozeMode()
            val isLightDozeMode = isLightDozeMode()
            put("isDozeMode", isDozeMode)
            put("isLightDozeMode", isLightDozeMode)
            put("isNetworkValid", isNetworkAvailable)
        }
    }

    fun onIMLoggedResult(isSuccess: Boolean, imErrorInfo: IMErrorInfo?) {
        BuzTracker.onEvent("CLIENT_IM_LOGIN_RESULT") {
            put("isInForeground", isAppInForeground)
            val time = System.currentTimeMillis() - (UserSessionManager.loginTime
                ?: System.currentTimeMillis())
            put("timeBetweenLocalLogin", time.toString())
            val isDozeMode = isDozeMode()
            val isLightDozeMode = isLightDozeMode()
            put("isDozeMode", isDozeMode)
            put("isLightDozeMode", isLightDozeMode)
            put("isNetworkValid", isNetworkAvailable)
            put("isSuccess", isSuccess)
            imErrorInfo?.let {
                put("errorType", it.errorType)
                put("errorCode", it.errorCode)
                put("errorMsg", it.errorMsg ?: "")
            }
        }
    }

    fun onIMTokenResult(isTokenEmpty: Boolean) {
        val time = System.currentTimeMillis() - (UserSessionManager.loginTime
            ?: System.currentTimeMillis())
        val isAppInForeground = isAppInForeground
        (userLifecycleScope ?: GlobalScope).launchIO {
            isNetworkAvailable.let {
                BuzTracker.onEvent("CLIENT_REQUEST_TOKEN_RESULT") {
                    put("timeBetweenLocalLogin", time.toString())
                    put("isEmpty", isTokenEmpty)
                    val isDozeMode = isDozeMode()
                    val isLightDozeMode = isLightDozeMode()
                    put("isDozeMode", isDozeMode)
                    put("isLightDozeMode", isLightDozeMode)
                    put("isNetworkValid", it)
                    put("isInForeground", isAppInForeground)
                }
            }
        }
    }

    /** 结果反馈-聊天页-发送消息结果 */
    fun onChatSendMsgResult(targetId: String, convType: IM5ConversationType, rCode: Int) {
        if (convType != PRIVATE && convType != IM5ConversationType.GROUP) {
            return
        }
        val isGroup = convType == IM5ConversationType.GROUP
        BuzTracker.onResult {
            val id = isGroup.getIfTrueOrFalse("RB2022091404", "RB2022091403")
            put(TrackConstant.KEY_EXCLUSIVE_ID, id)
            val type = isGroup.getIfTrueOrFalse("send_group_message", "send_private_message")
            put(TrackConstant.KEY_RESULT_TYPE, type)
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, targetId)
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            val isSuccess = (rCode == CODE_SUCCESS).trackString
            put(TrackConstant.KEY_IS_SUCCESS, isSuccess)
            put(TrackConstant.KEY_FAIL_REASON, rCode.toString())
        }
    }

    /** 结果反馈-聊天页-发送对讲机语音结果 */
    suspend fun onWTSendMsgResult(
        message: IMessage,
        rCode: Int,
        wtOnlineFriendCount: Int,
        isCancel: Boolean
    ) = withContext(Dispatchers.IO) {
        val convType = message.conversationType
        if (convType != PRIVATE && convType != IM5ConversationType.GROUP) {
            return@withContext
        }
        val traceId = message.msgTraceId
        val targetId = message.getConversationId()
        val isGroup = convType == IM5ConversationType.GROUP
        val isOnline = if (isGroup) false else FriendStatusManager.isOnline(targetId.toLongOrDefault(0L))
        val isRobot = if (isGroup) false else {
            val userInfo =
                UserRelationCacheManager.getUserRelationInfoByUid(targetId.toLongOrDefault(0L))
            userInfo?.isRobot == true
        }
        val msgContent = message.content
        val mentionedUser = when (msgContent) {
            is VoiceTextMsg -> msgContent.mentionedUsers
            is WTVoiceMsg -> msgContent.mentionedUsers
            is BuzTextMsg -> msgContent.mentionedUsers
            else -> null
        }
        val durationMs = when (msgContent) {
            is IM5VoiceMessage -> msgContent.duration
            else -> 0
        }
        val sendFrom = IMMessageContentExtra.parseFromJson(message.content.extra).eventTrackExtra?.sendFrom
        val source = if (sendFrom == IMSendFrom.OVERLAY.code) {
            "overlay"
        } else {
            null
        }
        val elementBusinessType = if (isBluetoothRecording()) "using_bluetooth_record" else "not_using_bluetooth_record"
        val pageBusinessType = message.trackerIMString()
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2022102803")
            put(TrackConstant.KEY_RESULT_TYPE, "send_walkie_talkie_voice")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, targetId)
            put(TrackConstant.KEY_PAGE_TYPE, "chat")

            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, pageBusinessType)
            val isSuccess = (rCode == CODE_SUCCESS).trackString
            put(TrackConstant.KEY_IS_SUCCESS, isSuccess)
            if (isCancel) {
                put(TrackConstant.KEY_FAIL_REASON, "cancel")
            } else {
                put(TrackConstant.KEY_FAIL_REASON, rCode.toString())
            }
            put(TrackConstant.KEY_BUSINESS_ID,if (message.hasVoiceFilter) "Y" else "N")
            put(TrackConstant.KEY_BUSINESS_NUM, wtOnlineFriendCount)
            put(TrackConstant.KEY_PAGE_STATUS, if (isOnline) "1" else "0")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE,elementBusinessType)
            source?.let { put(TrackConstant.KEY_SOURCE, source) }
            if (isGroup.not()) {
                val isQuiet = FriendStatusManager.isQuietModeEnable(targetId.toLongOrNull())
                val type = if (isQuiet) "quiet" else "available"
                put(TrackConstant.KEY_BUSINESS_TYPE, type)
            }
            put(TrackConstant.KEY_CONTENT_NAME, durationMs.toString())
            put(TrackConstant.KEY_LOG_TIME, NtpTime.nowForce().toString())
            put(TrackConstant.KEY_ELEMENT_BUSINESS_ID,(message.getVoiceFilterId?:0).toString())
            traceId?.let {
                put(TrackConstant.KEY_CONTENT_ID, it)
            }
            put(TrackConstant.KEY_MENU, getMenu(mentionedUser))
            message.getVoiceFilterId?.let { put(TrackConstant.KEY_ELEMENT_BUSINESS_ID, it) }
        }

        val needLog = rCode == CODE_SUCCESS
        val afEvent = "af_chat"
        val firebaseEvent = "fb_chat"
        logAFAndFireBaseEvent(afEvent, firebaseEvent, needLog)

    }

    private fun isBluetoothRecording():Boolean{
        val audioManager = appContext.getSystemService(Context.AUDIO_SERVICE) as AudioManager
        if (Build.VERSION.SDK_INT > VERSION_CODES.M ){
            val audioDevices = audioManager.getDevices(AudioManager.GET_DEVICES_INPUTS)
            for (device in audioDevices) {
                if (device.type == AudioDeviceInfo.TYPE_BLUETOOTH_SCO || device.type == AudioDeviceInfo.TYPE_BLUETOOTH_A2DP) {
                    return true
                }
            }
        }
        return false
    }

    private fun getMenu(mentionedUser: List<MentionedUser>?): String {
        return if (mentionedUser != null) {
            val first = mentionedUser.firstOrNull()
            first?.userId?.toString() ?: ""
        } else ""
    }

    /**
     * 图片消息发送结果打点
     * @param  contentName: 0 拍照, 1 相册图片
     * @param  quietMode: 0:available 1:quiet
     * @param beginSendTime the ntp time before sending
     */
    fun onChatSendImageMsgResult(
        message: IMessage,
        errorType: Int?,
        errorCode: Int?,
        quietMode: Int,
        beginSendTime: String,
        isCancel: Boolean = false,
    ) = GlobalScope.launch {
        val pageBusinessType = message.trackerIMString()
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2023060901")
            put(TrackConstant.KEY_RESULT_TYPE, "send_photo")
            put(TrackConstant.KEY_PAGE_TYPE, "photo")
            put(TrackConstant.KEY_BUSINESS_NUM,NtpTime.nowForce())
            put(
                TrackConstant.KEY_PAGE_BUSINESS_TYPE,
                pageBusinessType
            )
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, message.targetId)

            val chatService = routerServices<ChatService>().value
            val isInHome = chatService?.isChatHomeActivity(topActivity).getBooleanDefault()
            put(TrackConstant.KEY_SOURCE, if(isInHome) "0" else "1")

            val isFromCamera = IMMessageContentExtra.parseFromJson(message.content.extra).eventTrackExtra?.let {
                it.contentName?.let { contentName ->
                    if (contentName == RouterParamKey.Album.ALBUM_SOURCE_TAKE_PHOTO_TYPE.toString())
                        0
                    else
                        1
                }
            }.getIntDefault(1)
            put(TrackConstant.KEY_CONTENT_NAME, isFromCamera)


            if (message.isGroup) {
                put(TrackConstant.KEY_BUSINESS_TYPE, "1")
            } else {
                put(
                    TrackConstant.KEY_BUSINESS_TYPE,
                    if (FriendStatusManager.isOnline(message.targetId.toSafeLong())) "1" else "0"
                )
            }
            put(TrackConstant.KEY_PAGE_STATUS, "$quietMode")
            put(
                TrackConstant.KEY_ELEMENT_BUSINESS_TYPE,
                if (errorType != null) "$errorType" else ""
            )
            if (isCancel){
                put(TrackConstant.KEY_FAIL_REASON, "cancel")
                put(TrackConstant.KEY_IS_SUCCESS, "fail")
            }else if (errorCode != null) {
                put(TrackConstant.KEY_FAIL_REASON, "$errorCode")
                put(TrackConstant.KEY_IS_SUCCESS, "fail")
            } else {
                put(TrackConstant.KEY_FAIL_REASON, "")
                put(TrackConstant.KEY_IS_SUCCESS, "success")
            }
            put(TrackConstant.KEY_LOG_TIME, beginSendTime)
            if (message.msgTraceId != null) {
                put(TrackConstant.KEY_CONTENT_ID, message.msgTraceId)
            }
        }
    }

    /**
     * 图片消息发送结果打点
     * @param  contentName: 0 拍照, 1 相册图片
     * @param  quietMode: 0:available 1:quiet
     * @param beginSendTime the ntp time before sending
     */
    fun onChatSendLocationResult(
        message: IMessage,
        errorCode: Int?,
        isCancel: Boolean = false,
    ) = GlobalScope.launch{
        val pageBusinessType = message.trackerIMString(diffRobot = false)
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2024062504")
            put(TrackConstant.KEY_RESULT_TYPE, "send_location")
            put(TrackConstant.KEY_BUSINESS_NUM,NtpTime.nowForce())
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(
                TrackConstant.KEY_PAGE_BUSINESS_TYPE,
                pageBusinessType
            )
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, message.targetId)
            if (message.msgTraceId != null) {
                put(TrackConstant.KEY_CONTENT_ID, message.msgTraceId)
            }
            if (isCancel){
                put(TrackConstant.KEY_FAIL_REASON, "cancel")
                put(TrackConstant.KEY_IS_SUCCESS, "fail")
            }else if (errorCode != null) {
                put(TrackConstant.KEY_FAIL_REASON, "$errorCode")
                put(TrackConstant.KEY_IS_SUCCESS, "fail")
            } else {
                put(TrackConstant.KEY_FAIL_REASON, "")
                put(TrackConstant.KEY_IS_SUCCESS, "success")
            }
            val buzLocationMessage = message.content as BuzLocationMessage
            put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, buzLocationMessage.locationType)
        }
    }

    /**
     * 文本消息发送结果打点
     * @param  quietMode: 0:available 1:quiet
     * @param beginSendTime the ntp time before sending
     */
    fun onChatSendTextMsgResult(
        message: IMessage,
        errorType: Int?,
        errorCode: Int?,
        quietMode: Int,
        beginSendTime: String,
        mentionedUser: List<MentionedUser>?,
        isCancel: Boolean = false,
    ) = GlobalScope.launch {
        val isOnline =
            if (message.isPrivate) FriendStatusManager.isOnline(message.targetId.toSafeLong()) else true
        val robot = message.getConversationId().toLongOrNull().isRobot()
        val pageBusinessType = message.trackerIMString()
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2023060902")
            put(TrackConstant.KEY_RESULT_TYPE, "send_word")
            put(TrackConstant.KEY_PAGE_TYPE, "word")
            put(TrackConstant.KEY_BUSINESS_NUM,NtpTime.nowForce())
            put(
                TrackConstant.KEY_PAGE_BUSINESS_TYPE,
                pageBusinessType
            )
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, message.targetId)
            put(TrackConstant.KEY_BUSINESS_TYPE, if (isOnline) "1" else "0")
            put(TrackConstant.KEY_PAGE_STATUS, "$quietMode")
            IMMessageContentExtra.parseFromJson(message.content.extra).eventTrackExtra?.apply {
                put(TrackConstant.KEY_SOURCE, "$contentId")
                put(TrackConstant.KEY_CONTENT_NAME, contentName.getStringDefault())
            }
            put(
                TrackConstant.KEY_ELEMENT_BUSINESS_TYPE,
                if (errorType != null) "$errorType" else ""
            )
            if (isCancel){
                put(TrackConstant.KEY_FAIL_REASON, "cancel")
                put(TrackConstant.KEY_IS_SUCCESS, "fail")
            } else if (errorCode != null) {
                put(TrackConstant.KEY_FAIL_REASON, "$errorCode")
                put(TrackConstant.KEY_IS_SUCCESS, "fail")
            } else {
                put(TrackConstant.KEY_FAIL_REASON, "")
                put(TrackConstant.KEY_IS_SUCCESS, "success")
            }
            put(TrackConstant.KEY_LOG_TIME, beginSendTime)
            if (message.msgTraceId != null) {
                put(TrackConstant.KEY_CONTENT_ID, message.msgTraceId)
            }
            put(TrackConstant.KEY_MENU, getMenu(mentionedUser))
        }
    }

    /**
     * 视频消息发送结果打点
     */
    fun onChatSendVideoMsgResult(
        message: IMessage,
        isQuietMode: Boolean,
        beginSendTime: String,
        errorType: Int? = null,
        errorCode: Int? = null,
        isCancel: Boolean = false,
    ) = GlobalScope.launch{
        val targetId = message.targetId.toSafeLong()
        val isOnline = if (message.isPrivate) FriendStatusManager.isOnline(targetId) else true
        val chatService = routerServices<ChatService>().value
        val isFromCamera = IMMessageContentExtra.parseFromJson(message.extra).eventTrackExtra?.let {
            it.contentName?.let { contentName ->
                if (contentName == RouterParamKey.Album.ALBUM_SOURCE_TAKE_PHOTO_TYPE.toString())
                    0
                else
                    1
            }
        }.getIntDefault(1)
        val isInHome = chatService?.isChatHomeActivity(topActivity).getBooleanDefault()
        val pageBusinessType = message.trackerIMString(diffRobot = false)

        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2024032505")
            put(TrackConstant.KEY_RESULT_TYPE, "send_video")
            put(TrackConstant.KEY_PAGE_TYPE, "聊天页")
            put(TrackConstant.KEY_BUSINESS_NUM,NtpTime.nowForce())
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, pageBusinessType)
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, targetId)
            put(TrackConstant.KEY_PAGE_STATUS, if (isQuietMode) "1" else "0")
            put(TrackConstant.KEY_BUSINESS_TYPE, if (isOnline) "1" else "0")
            put(TrackConstant.KEY_SOURCE, if (isInHome) "0" else "1")
            put(TrackConstant.KEY_CONTENT_NAME, isFromCamera)
            put(TrackConstant.KEY_CONTENT_ID, message.msgTraceId)
            put(TrackConstant.KEY_LOG_TIME, beginSendTime)
            if (isCancel) {
                put(TrackConstant.KEY_FAIL_REASON, "cancel")
                put(TrackConstant.KEY_IS_SUCCESS, "fail")
            }else if (errorCode != null && errorType != null) {
                put(TrackConstant.KEY_IS_SUCCESS, "fail")
                put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, errorType.toString())
                put(TrackConstant.KEY_FAIL_REASON, errorCode.toString())
            } else {
                put(TrackConstant.KEY_IS_SUCCESS, "success")
            }

        }
    }

    fun clickAC2023092101(hasNewAIFlag: Boolean, friendSize: Int) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2023092101")
            put(TrackConstant.KEY_TITLE, "首页")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "机器人列表_首页入口")
            put(TrackConstant.KEY_PAGE_TYPE, "home")
            put(
                TrackConstant.KEY_ELEMENT_BUSINESS_TYPE,
                if (hasNewAIFlag) "have_dot" else "no_dot"
            )
            //说明:当前好友数e
            put(TrackConstant.KEY_BUSINESS_NUM, friendSize)
        }
    }

    /**
     * 消息全链路中埋点，新消息的接收结果埋点
     */
    fun trackNewMessageReceived(msgList: List<IMessage>) {
        GlobalScope.launch(Dispatchers.IO) {
            logDebug(
                "IMAgent",
                "trackNewMessageReceived received new message size = ${msgList.size}"
            )
            msgList.forEach {
                if (it.isReceive.not()) {
                    return@forEach
                }
                if (it.isTextMessage.not() &&
                    it.isImageMessage.not() &&
                    it.isVoiceMsg.not() &&
                    it.isVideoMessage.not() &&
                    it.isLocationMsg.not() &&
                    it.isMediaTextMsg.not() &&
                    it.isFileMessage.not() &&
                    it.isShareContactMessage.not()
                ) {
                    return@forEach
                }
                var isOnline = true
                val conversationId = it.getConversationId().toLongOrNull() ?: 0L
                if (it.isPrivate) {
                    isOnline = FriendStatusManager.isOnline(conversationId)
                }
                val targetId = it.getConversationId().toLongOrNull() ?: 0L
                val isOfficial = if (it.isPrivate) targetId.isOfficial() else false
                if (it.isPrivate && !isOfficial) {
                    isOnline = FriendStatusManager.isOnline(conversationId)
                }
                onReceiveTxtPicMsgResult(
                    msg = it,
                    convType = it.conversationType,
                    pageBusinessId = conversationId.toString(),
                    isOnline = isOnline,
                    traceId = it.msgTraceId,
                    msgInfo = it.businessType
                )
            }
        }
    }


    /**
     * 接收到消息结果点（content_name 表示语音/图片/文字/文件/分享联系人 不同的消息类型）
     */
    private fun onReceiveTxtPicMsgResult(
        msg: IMessage,
        convType: IM5ConversationType,
        pageBusinessId: String,
        isOnline: Boolean,
        traceId: String? = null,
        msgInfo: Int?
    ) {
        GlobalScope.launch{
            val pageBusinessType = msg.trackerIMString(diffSelf = false)
            BuzTracker.onResult {
                put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2023060903")
                put(TrackConstant.KEY_RESULT_TYPE, "chat_result")
                put(TrackConstant.KEY_PAGE_TYPE, "chat")
                put(TrackConstant.KEY_PAGE_BUSINESS_TYPE,pageBusinessType)
                put(TrackConstant.KEY_PAGE_BUSINESS_ID, pageBusinessId)
                put(TrackConstant.KEY_PAGE_STATUS, if (UserSettingManager.isQuietModeEnable) "1" else "0")
                put(TrackConstant.KEY_BUSINESS_TYPE, if (isOnline) "1" else "0")
                put(TrackConstant.KEY_SOURCE, if (AppStateWatcher.isForeground == true) "0" else "1")
                put(TrackConstant.KEY_LOG_TIME, NtpTime.nowForce().toString())
                put(TrackConstant.KEY_BUSINESS_ID, msg.hasVoiceFilter.getIfTrueOrFalse("Y","N"))
                put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT,msg.getVoiceDuration().toString())
                put(TrackConstant.KEY_ELEMENT_BUSINESS_ID,(msg.getVoiceFilterId?:0).toString())
                traceId?.let {
                    put(TrackConstant.KEY_CONTENT_ID, it)
                }
                val contentName = if (msg.isVoiceMsg && !msg.isVoiceMojiMessage && !msg.isVoiceGifMessage) {
                    "0"
                } else if (msg.isImageMessage) {
                    "1"
                } else if (msg.isTextMessage) {
                    "2"
                } else if (msg.isVoiceMojiMessage) {
                    "3"
                } else if (msg.isVideoMessage) {
                    "4"
                } else if (msg.isLocationMsg) {
                    "5"
                } else if (msg.isVoiceGifMessage) {
                    "6"
                } else if (msg.isMediaTextMsg) {
                    "7"
                } else if (msg.isFileMessage)
                    "8"
                else if (msg.isShareContactMessage)
                    "9"
                else {
                    "-1"
                }
                if (msg.isFileMessage) {
                    val extName = (msg.content as? BuzFileMessage)?.extName ?: ""
                    put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, extName)
                }
                put(TrackConstant.KEY_CONTENT_NAME, contentName)
                if (convType == IM5ConversationType.GROUP && msg.fromId.toLongOrNull()
                        ?.isRobot() == true
                ) {
                    put(TrackConstant.KEY_MENU, msg.fromId)
                }
                msg.getVoiceFilterId?.let { put(TrackConstant.KEY_ELEMENT_BUSINESS_ID, it) }
                put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT, msg.getVoiceDuration().toString())
                msgInfo?.let {
                    put(TrackConstant.KEY_BUSINESS_NUM, it)
                }
            }
        }
    }

    suspend fun onChatSendVoiceEmojiResult(
        message: IMessage,
        isSuccess: Boolean,
        errorType: Int?,
        errorCode: Int?,
        traceId: String,
        nptTime: String,
        atRobot: String = "",
        isCancel: Boolean = false,
        wtVoiceEmojiMsg: WTVoiceEmojiMsg,
        ) {
        val conversationType = message.trackerIMString()
        //对方是否在线枚举值
        val isOnline = if (message.isGroup) {
            "1"
        } else {
            if (FriendStatusManager.isOnline(
                    IM5MsgUtils.getConvTargetId(message).toLongOrNull()
                )
            ) {
                "1"
            } else {
                "0"
            }
        }
        val isBlindBox = wtVoiceEmojiMsg.emojiCategoryType == ImVoiceEmojiCategoryType.BlindBox
        val existInCollection = routerServices<IVoiceEmojiService>().value?.existInCollectionList(
            wtVoiceEmojiMsg.emojiId.toString()
        ) ?: false
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2023120602")
            put(TrackConstant.KEY_RESULT_TYPE, "send_voicemoji")
            put(TrackConstant.KEY_PAGE_TYPE, "voicemoji")
            put(TrackConstant.KEY_BUSINESS_NUM, NtpTime.nowForce())
            put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT,if (existInCollection) "Fav" else "Not_Fav")

            //private:私信聊天
            //group:群组聊天
            //robot:AI聊天
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, conversationType)
            //用户ID、群ID、机器人ID
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, IM5MsgUtils.getConvTargetId(message))
            //[非必填]说明:当聊天类型为群聊中@机器人时，上报机器人id,其他聊天类型置空上报
            put(TrackConstant.KEY_MENU, atRobot)
            //用户当前模式枚举值:
            //0:available
            //1:quiet
            put(
                TrackConstant.KEY_PAGE_STATUS,
                if (UserSettingManager.isQuietModeEnable) "1" else "0"
            )
            //对方是否在线枚举值:
            //0:不在线
            //1:在线 (群聊是1)
            put(TrackConstant.KEY_BUSINESS_TYPE, isOnline)
            put(TrackConstant.KEY_BUSINESS_ID, wtVoiceEmojiMsg.emojiId)
            put(TrackConstant.KEY_PHONE_NUMBER, if (isBlindBox) "Y" else "N")
            if (isBlindBox) {
                if (CommonMMKV.recentOpenBlindBoxTime.isNotEmpty()) {
                    val recentOpenBlindBox = CommonMMKV.recentOpenBlindBoxTime.split(":")
                    if (recentOpenBlindBox.size >= 2) {
                        val recentOpenBlindBoxId = recentOpenBlindBox.getOrNull(0).toSafeLong()
                        val recentOpenBlindBoxTime = recentOpenBlindBox.getOrNull(1).toSafeLong()
                        val isSameId = wtVoiceEmojiMsg.emojiId == recentOpenBlindBoxId
                        val lessThan1Min =  System.currentTimeMillis() - recentOpenBlindBoxTime < 60000
                        put(TrackConstant.KEY_SOURCE, if (isSameId && lessThan1Min) "Y" else "N")
                    }
                }
            }
            //traceid消息唯一ID
            put(TrackConstant.KEY_CONTENT_ID, traceId)
            put(TrackConstant.KEY_LOG_TIME, nptTime)
            //枚举值:
            //success
            //fail
            put(TrackConstant.KEY_IS_SUCCESS, if (isSuccess) "success" else "fail")
            //失败原因
            if (isCancel) {
                put(TrackConstant.KEY_FAIL_REASON, "cancel")
            } else {
                put(TrackConstant.KEY_FAIL_REASON, if (errorCode != null) "$errorCode" else "")
            }
            put(
                TrackConstant.KEY_ELEMENT_BUSINESS_TYPE,
                if (errorType != null) "$errorType" else ""
            )
            IMMessageContentExtra.parseFromJson(message.content.extra).eventTrackExtra?.apply {
                put(TrackConstant.KEY_SOURCE, "$contentId")
                put(TrackConstant.KEY_CONTENT_NAME, contentName.getStringDefault())
            }
        }
    }

    /**
     * 语音消息发送结果打点
     */
    fun onChatSendVoiceResult(
        message: IMessage,
        errorType: Int?,
        errorCode: Int?,
        quietMode: Int,
        beginSendTime: String,
        mentionedUser: List<MentionedUser>?,
        isCancel: Boolean = false,
    ) = GlobalScope.launch {
        val isOnline =
            if (message.isPrivate) FriendStatusManager.isOnline(message.targetId.toSafeLong()) else true
        val robot =
            if (message.isPrivate) message.getConversationId().toLongOrNull().isRobot() else false
        val pageBusinessType = message.trackerIMString()
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2023122508")
            put(TrackConstant.KEY_RESULT_TYPE, "send_voice")
            put(TrackConstant.KEY_PAGE_TYPE, "voice")
            put(TrackConstant.KEY_BUSINESS_NUM,NtpTime.nowForce())
            put(
                TrackConstant.KEY_PAGE_BUSINESS_TYPE,
                pageBusinessType
            )
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, message.targetId)
            put(TrackConstant.KEY_BUSINESS_TYPE, if (isOnline) "1" else "0")
            put(TrackConstant.KEY_PAGE_STATUS, "$quietMode")
            put(TrackConstant.KEY_BUSINESS_ID,if(message.hasVoiceFilter) "Y" else "N")
            IMMessageContentExtra.parseFromJson(message.content.extra).eventTrackExtra?.apply {
                put(TrackConstant.KEY_SOURCE, "$contentId")
                put(TrackConstant.KEY_CONTENT_NAME, contentName.getStringDefault())
                //sendFrom?.let { put(TrackConstant.KEY_BUSINESS_NUM, sendFrom) }
            }
            put(
                TrackConstant.KEY_ELEMENT_BUSINESS_TYPE,
                if (errorType != null) "$errorType" else ""
            )
            if (isCancel){
                put(TrackConstant.KEY_FAIL_REASON, "cancel")
                put(TrackConstant.KEY_IS_SUCCESS, "fail")
            } else if (errorCode != null) {
                put(TrackConstant.KEY_FAIL_REASON, "$errorCode")
                put(TrackConstant.KEY_IS_SUCCESS, "fail")
            } else {
                put(TrackConstant.KEY_FAIL_REASON, "")
                put(TrackConstant.KEY_IS_SUCCESS, "success")
            }
            put(TrackConstant.KEY_LOG_TIME, beginSendTime)
            if (message.msgTraceId != null) {
                put(TrackConstant.KEY_CONTENT_ID, message.msgTraceId)
            }
            put(TrackConstant.KEY_MENU, getMenu(mentionedUser))
            put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT,message.getVoiceDuration().toString())
            put(TrackConstant.KEY_ELEMENT_BUSINESS_ID, (message.getVoiceFilterId?:0).toString())
        }
    }

    /**
     * 语音消息发送结果打点
     */
    fun onChatSendVoiceResult(
        targetId:String,
        convType:IM5ConversationType,
        duration:Int,
        traceId:String?,
        mentionedUsers: List<MentionedUser>?,
        filterId: Long?,
        errorType: Int?,
        errorCode: Int?,
        quietMode: Int,
        beginSendTime: String,
        isCancel: Boolean = false,
    ) = GlobalScope.launch {
        val isOnline =
            if (convType == PRIVATE) FriendStatusManager.isOnline(targetId.toSafeLong()) else true
        val pageBusinessType = trackerIMString(targetId.toSafeLong(),convType)
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2023122508")
            put(TrackConstant.KEY_RESULT_TYPE, "send_voice")
            put(TrackConstant.KEY_PAGE_TYPE, "voice")
            put(TrackConstant.KEY_BUSINESS_NUM,NtpTime.nowForce())
            put(
                TrackConstant.KEY_PAGE_BUSINESS_TYPE,
                pageBusinessType
            )
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, targetId)
            put(TrackConstant.KEY_BUSINESS_TYPE, if (isOnline) "1" else "0")
            put(TrackConstant.KEY_PAGE_STATUS, "$quietMode")
            put(TrackConstant.KEY_BUSINESS_ID,if(filterId.isNotNull()) "Y" else "N")
            put(
                TrackConstant.KEY_ELEMENT_BUSINESS_TYPE,
                if (errorType != null) "$errorType" else ""
            )
            if (isCancel){
                put(TrackConstant.KEY_FAIL_REASON, "cancel")
                put(TrackConstant.KEY_IS_SUCCESS, "fail")
            } else if (errorCode != null) {
                put(TrackConstant.KEY_FAIL_REASON, "$errorCode")
                put(TrackConstant.KEY_IS_SUCCESS, "fail")
            } else {
                put(TrackConstant.KEY_FAIL_REASON, "")
                put(TrackConstant.KEY_IS_SUCCESS, "success")
            }
            put(TrackConstant.KEY_LOG_TIME, beginSendTime)
            if (traceId != null) {
                put(TrackConstant.KEY_CONTENT_ID, traceId)
            }
            put(TrackConstant.KEY_MENU, getMenu(mentionedUsers))
            if (duration > 0){
                put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT,duration.toString())
            }
            put(TrackConstant.KEY_ELEMENT_BUSINESS_ID, (filterId?:0).toString())
        }
    }

    /**
     * 媒体上传结果
     * (同一个媒体点击发送后、手动取消后重新上传的情形可报多条数据)
     */
    fun onResultMediaUpload(
        isVideo: Boolean,
        uploadDuration: Long,
        traceID: String?,
        mediaUploadSize: Long,
        isSuccess: Boolean,
        failReason: String? = null
    ) {
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2024032506")
            put(TrackConstant.KEY_RESULT_TYPE, "media_upload_result")
            put(TrackConstant.KEY_PAGE_TYPE, "聊天页")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, if (isVideo) "video" else "image")
            put(TrackConstant.KEY_CONTENT_ID, uploadDuration.toString())
            put(TrackConstant.KEY_CONTENT_NAME, traceID ?: "")
            put(TrackConstant.KEY_BUSINESS_NUM, mediaUploadSize)
            put(TrackConstant.KEY_IS_SUCCESS, if (isSuccess) "success" else "fail")
            failReason?.let { put(TrackConstant.KEY_FAIL_REASON, it) }
        }
    }

    fun onResultMsgAutoResend(
        isSuccess: Boolean,
        convTypeStr: String,
        msgType: Int,
        targetId: String,
        traceId: String,
        startTime: Long,
        endTime: Long,
        errorType: Int?,
        errorCode: Int?,
        errorMsg: String?
    ) {
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2024071701")
            put(TrackConstant.KEY_RESULT_TYPE, "result of resend message")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, convTypeStr)
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, targetId)
            put(TrackConstant.KEY_CONTENT_ID, traceId)
            put(TrackConstant.KEY_LOG_TIME, startTime.toString())
            put(TrackConstant.KEY_BUSINESS_NUM, endTime)
            put(TrackConstant.KEY_IS_SUCCESS, if (isSuccess) "success" else "fail")
            val msgTypeLogStr = when (msgType) {
                IMType.TYPE_TEXT_MSG -> "resend_word_message"
                IMType.TYPE_IMAGE_MSG -> "resend_photo_message"
                IMType.TYPE_VIDEO_MSG -> "resend_video_message"
                IMType.TYPE_LOCATION_MSG -> "resend_location_message"
                IMType.TYPE_VOICE_EMOJI -> "resend_voicemoji_message"
                IMType.TYPE_VOICE_MSG,
                IMType.TYPE_VOICE_TEXT,
                IMType.TYPE_VOICE_TEXT_NEW -> "resend_voice_message"

                else -> ""
            }
            errorType?.let {
                put(TrackConstant.KEY_CONTENT_NAME,it.toString())
            }
            errorCode?.let {
                put(TrackConstant.KEY_FAIL_REASON,it.toString())
            }
            errorMsg?.let {
                put(TrackConstant.KEY_BUSINESS_TYPE, errorMsg)
            }
            put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, msgTypeLogStr)
        }
    }

    /**
     * 转发结果打点（区别于IM总的消息打点）
     */
    fun onResultRB2024062501(message: IMessage, isSuccess: Boolean, resultCode: String? = null) {
        GlobalScope.launch(Dispatchers.IO) {
            val extra = IMMessageContentExtra.parseFromJson(message.content.extra)
            if (extra.eventTrackExtra?.isForwardMsg != true) {
                return@launch
            }
            val targetId = message.getConversationId().toLongOrDefault(0L)
            val convType = message.conversationType
            val businessType = getPageBusinessType(targetId, convType)
            BuzTracker.onResult {
                put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2024062501")
                put(TrackConstant.KEY_RESULT_TYPE, "forward_message_result")
                put(TrackConstant.KEY_PAGE_TYPE, "chat")
                put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, businessType)
                put(TrackConstant.KEY_PAGE_BUSINESS_ID, targetId)

                val elementBusinessType = when (message.msgType) {
                    IMType.TYPE_TEXT_MSG -> "forward_word_message"
                    IMType.TYPE_IMAGE_MSG -> "forward_photo_message"
                    IMType.TYPE_VIDEO_MSG -> "forward_video_message"
                    IMType.TYPE_LOCATION_MSG -> "forward_location_message"
                    else -> "unknown"
                }
                put(TrackConstant.KEY_BUSINESS_TYPE, elementBusinessType)
                put(TrackConstant.KEY_CONTENT_ID, message.msgTraceId)
                if (isSuccess) {
                    put(TrackConstant.KEY_IS_SUCCESS, "success")
                } else {
                    put(TrackConstant.KEY_IS_SUCCESS, "fail")
                    resultCode?.let {
                        put(TrackConstant.KEY_FAIL_REASON, resultCode)
                    }
                }
            }
        }
    }

    private suspend fun getPageBusinessType(targetId: Long, convType: IM5ConversationType): String {
        val isRobot = if (convType == PRIVATE) {
            val userInfo =
                UserRelationCacheManager.getUserRelationInfoByUidSync(targetId)
            userInfo?.isRobot == true
        } else false
        return when (convType) {
            PRIVATE -> if (isRobot) "robot" else "private"
            else -> "group"
        }
    }
    /**
     * 引用结果打点（区别于IM总的消息打点）
     */
    fun onResultRB2024112901(message: IMessage) {
        if (message.msgReferenceStatus != MsgReferenceStatus.MSG_NORMAL) {
            return
        }
        val targetId = message.getConversationId().toLongOrDefault(0L)

        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2024112901")
            put(TrackConstant.KEY_RESULT_TYPE, "result_reply_message")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            val businessType = if (message.conversationType == IM5ConversationType.GROUP) {
                "group"
            } else {
                "private"
            }
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, businessType)
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, targetId)
            put(TrackConstant.KEY_BUSINESS_TYPE, message.referenceMsg.msgType.toString()) // 消息的引用类型
            put(TrackConstant.KEY_BUSINESS_ID, message.msgType.toString()) // 消息的回复类型
            put(TrackConstant.KEY_CONTENT_ID, message.msgTraceId)
        }
    }

    /** 点击OA消息 */
    fun clickOAMessage(isLearnMore:Boolean, business:String) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024112107")
            put(TrackConstant.KEY_TITLE, "oa_chat")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "oa_message_click")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT, if (isLearnMore)"learn_more" else "try_it_out")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, business)
        }
    }

    /** 接收到OA消息 **/
    /** Removed on 3 Jun 2024 (1.67.0) **/
    fun receiveOAMessage(business: Int?) {
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2024112103")
            put(TrackConstant.KEY_RESULT_TYPE, "oa_message_result")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            business?.let {
                put(TrackConstant.KEY_BUSINESS_NUM, it)
            }
        }
    }

    /** 声音滤镜转换失败结果上报 */
    fun voiceFilterFailConvertResult(
        isPrivate: Boolean,
        filterId: Long,
        duration: Long,
        code: Int?,
        isSuccess :Boolean
    ) {
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2024112104")
            put(TrackConstant.KEY_RESULT_TYPE, "voice_filter_convert_result")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, if (isPrivate) "private" else "group")
            put(TrackConstant.KEY_BUSINESS_TYPE, "$filterId")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, "$duration")
            code?.let {
                put(TrackConstant.KEY_FAIL_REASON, "$it")
            }
            put(TrackConstant.KEY_IS_SUCCESS, isSuccess.getIfTrueOrFalse("success","fail"))
        }
    }

    fun onSendVoiceGifResult(
        message: IMessage,
        voiceGifId: String,
        errorCode: Int?,
        ntpTime: String,
        isCancel: Boolean
    ) = GlobalScope.launch {
        val pageBusinessType = trackerIMString(message.targetId.toSafeLong(), message.conversationType)
        val isOnline =
            if (message.isPrivate) FriendStatusManager.isOnline(message.targetId.toSafeLong()) else true
        val existInCollection = routerServices<IVoiceEmojiService>().value?.existInCollectionList(
            voiceGifId
        ) ?: false
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2025011702")
            put(TrackConstant.KEY_RESULT_TYPE, "send_voicegif")
            put(TrackConstant.KEY_PAGE_TYPE, "voice_gif")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, pageBusinessType)
            put(TrackConstant.KEY_BUSINESS_ID, message.getConversationId())
            put(TrackConstant.KEY_PAGE_STATUS, if (UserSettingManager.isQuietModeEnable) "1" else "0")
            put(TrackConstant.KEY_BUSINESS_TYPE, if (isOnline) "1" else "0")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, voiceGifId)
            put(TrackConstant.KEY_CONTENT_ID, message.msgTraceId)
            put(TrackConstant.KEY_LOG_TIME, ntpTime)
            put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT,if (existInCollection) "Fav" else "Not_Fav")
            if (isCancel) {
                put(TrackConstant.KEY_FAIL_REASON, "cancel")
                put(TrackConstant.KEY_IS_SUCCESS, "fail")
            } else if (errorCode != null) {
                put(TrackConstant.KEY_FAIL_REASON, "$errorCode")
                put(TrackConstant.KEY_IS_SUCCESS, "fail")
            } else {
                put(TrackConstant.KEY_FAIL_REASON, "")
                put(TrackConstant.KEY_IS_SUCCESS, "success")
            }
        }
    }

    fun onSendFileMsgResult(
        isPrivate: Boolean,
        conversationId: String,
        isOnline: Boolean,
        traceId: String,
        extension: String,
        startTime: String,
        isCancel: Boolean,
        errorType: Int?,
        errorCode: Int?
    ) {
        val chatService = routerServices<ChatService>().value
        val isInHome = chatService?.isChatHomeActivity(topActivity).getBooleanDefault()
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2025061601")
            put(TrackConstant.KEY_RESULT_TYPE, "send_file")
            put(TrackConstant.KEY_PAGE_TYPE, "file")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, if (isPrivate) "private" else "group")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, conversationId)
            put(TrackConstant.KEY_PAGE_STATUS, if (UserSettingManager.isQuietModeEnable) "1" else "0")
            put(TrackConstant.KEY_BUSINESS_TYPE, if (isOnline) "1" else "0")
            put(TrackConstant.KEY_SOURCE, if (isInHome) "0" else "1")
            put(TrackConstant.KEY_CONTENT_ID, traceId)
            put(TrackConstant.KEY_CONTENT_NAME, extension)
            put(TrackConstant.KEY_LOG_TIME, startTime)
            put(TrackConstant.KEY_BUSINESS_NUM, NtpTime.nowForce())
            put(
                TrackConstant.KEY_ELEMENT_BUSINESS_TYPE,
                if (errorType != null) "$errorType" else ""
            )
            if (isCancel) {
                put(TrackConstant.KEY_FAIL_REASON, "cancel")
                put(TrackConstant.KEY_IS_SUCCESS, "fail")
            } else if (errorCode != null) {
                put(TrackConstant.KEY_FAIL_REASON, "$errorCode")
                put(TrackConstant.KEY_IS_SUCCESS, "fail")
            } else {
                put(TrackConstant.KEY_FAIL_REASON, "")
                put(TrackConstant.KEY_IS_SUCCESS, "success")
            }
        }
    }
}