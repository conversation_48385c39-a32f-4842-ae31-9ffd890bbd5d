package com.interfun.buz.im.util

import android.text.SpannableStringBuilder
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.common.ktx.getDisplayName
import com.interfun.buz.common.ktx.isMe
import com.interfun.buz.common.ktx.userName
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.manager.cache.user.UserRelationCacheManager
import com.interfun.buz.common.bean.chat.MentionedUser
import com.interfun.buz.im.entity.VoiceFilterInfo
import com.interfun.buz.im.entity.translation.TranslateConstant
import com.interfun.buz.im.entity.translation.TranslateInfo
import org.json.JSONArray
import org.json.JSONObject

object IMBaseParser {
    fun getMentionedUsersFromJson(json: JSONObject): List<MentionedUser>? {
        val mentionedUsersJon = json.optJSONArray("mentionedUsers") ?: return null
        if (mentionedUsersJon.length() == 0) {
            return null
        }
        val list = ArrayList<MentionedUser>(mentionedUsersJon.length())
        for (i in 0 until mentionedUsersJon.length()) {
            val userJon = mentionedUsersJon.getJSONObject(i)
            val userId = userJon.optString("userId").toLongOrNull() ?: 0L
            val userName = userJon.optString("userName", "") ?: ""
            val isMentioningAll = if (userJon.has("isMentioningAll")) {
                userJon.optBoolean("isMentioningAll")
            } else {
                null
            }
            val userInfo = MentionedUser(userId, userName, isMentioningAll)
            list.add(userInfo)
        }
        return list
    }

    fun addMentionedUsers(mentionedUsers: List<MentionedUser>?, json: JSONObject) {
        if (mentionedUsers.isNullOrEmpty()) {
            return
        }
        val jsonArray = JSONArray()
        mentionedUsers.forEach { user ->
            val userJson = JSONObject()
            userJson.put("userId", user.userId.toString())
            userJson.put("userName", user.userName)
            user.isMentioningAll?.let { userJson.put("isMentioningAll", it) }
            jsonArray.put(userJson)
        }
        json.put("mentionedUsers", jsonArray)
    }

    fun optTranslateInfo(json: JSONObject): TranslateInfo? {
        if (json.has("translateInfo")) {
            val translate = JSONObject(json.optString("translateInfo"))
            val translateCode =
                if (translate.has("translateCode")) {
                    translate.optInt("translateCode", TranslateConstant.UNKNOWN)
                } else TranslateConstant.UNKNOWN
            val translateText =
                if (translate.has("translateText")) {
                    translate.optString("translateText")
                } else null
            val placeholdersList =
                if (translate.has("replaceholderList")) {
                    val jsonArray = translate.optJSONArray("replaceholderList")
                    val list = mutableListOf<String>()
                    if (jsonArray != null) {
                        for (i in 0 until jsonArray.length()) {
                            list.add(jsonArray.optString(i))
                        }
                    }
                    list
                } else emptyList()
            val mentionIds =
                if (translate.has("mentionIds")) {
                    val jsonArray = translate.optJSONArray("mentionIds")
                    val list = mutableListOf<String>()
                    if (jsonArray != null) {
                        for (i in 0 until jsonArray.length()) {
                            list.add(jsonArray.optString(i))
                        }
                    }
                    list
                } else emptyList()
            val sourceLanguage =
                if (translate.has("sourceLanguage")) {
                    translate.optString("sourceLanguage")
                } else null
            val targetLanguage =
                if (translate.has("targetLanguage")) {
                    translate.optString("targetLanguage")
                } else null

            // There are no placeholdersList & mentionIds fields for versions before the group mention feature, hence just display the translated text returned by server
            // Section 6 in https://vocalbeats.sg.larksuite.com/wiki/BM61wP2bwitvIokiSVVlpa2GgGe?fromScene=spaceOverview
            val displayTranslatedText = if (placeholdersList.isEmpty() || mentionIds.isEmpty()) {
                // OLD version
                translateText
            } else {
                // NEW version: Starting from group mention feature, use placeholdersList to find the mentioned names part in translateText, & replace with @username
                if (translateText == null) "" else {
                    val spannableStringBuilder = SpannableStringBuilder()
                    var i = 0
                    var idIndex = 0

                    while (i < translateText.length) {
                        var replaced = false
                        for (placeholder in placeholdersList) {
                            if (translateText.startsWith(placeholder, i) && idIndex < mentionIds.size) {
                                val uid = mentionIds[idIndex].toLong()
                                val isMyself = uid.isMe()
                                val userInfo = UserRelationCacheManager.getUserRelationInfoByUid(uid)
                                val displayName = when {
                                    userInfo == null -> ""
                                    isMyself -> "@${UserSessionManager.userName}"
                                    else -> "@${userInfo.getDisplayName()}"
                                }
                                spannableStringBuilder.append(displayName)
                                i += placeholder.length
                                idIndex++
                                replaced = true
                                break
                            }
                        }
                        if (!replaced) {
                            spannableStringBuilder.append(translateText[i])
                            i++
                        }
                    }
                    spannableStringBuilder.toString()
                }
            }
            val translateInfo = TranslateInfo(
                translateCode = translateCode,
                translateText = displayTranslatedText,
                sourceLanguage = sourceLanguage,
                targetLanguage = targetLanguage
            )
            return translateInfo
        }
        return null
    }

    fun addTranslateInfo(translateInfo: TranslateInfo?, json: JSONObject) {
        translateInfo?.let {
            val jsonObject = JSONObject().apply {
                put("translateCode", it.translateCode)
                it.translateText?.let {
                    put("translateText", it)
                }
                it.sourceLanguage?.let {
                    put("sourceLanguage", it)
                }
                it.targetLanguage?.let {
                    put("targetLanguage", it)
                }
            }
            json.put("translateInfo", jsonObject)
        }
    }

    fun optVoiceFilterInfo(json: JSONObject): VoiceFilterInfo? {
        if (json.has("filterInfo")) {
            val voiceFilter = JSONObject(json.optString("filterInfo"))
            val filterId : Long?=
                if (voiceFilter.has("filterId")) {
                    voiceFilter.optLong("filterId")
                } else null
            val originalUrl =
                if (voiceFilter.has("originalUrl")) {
                    voiceFilter.optString("originalUrl")
                } else null
            val filterState =
                if (voiceFilter.has("filterState")) {
                    voiceFilter.optInt("filterState")
                } else 1

            val videoTemplateUrl =
                if (voiceFilter.has("videoTemplateUrl")) {
                    voiceFilter.optString("videoTemplateUrl")
                } else null

            val videoTemplateMd5 =
                if (voiceFilter.has("videoTemplateMd5")) {
                    voiceFilter.optString("videoTemplateMd5")
                }else null

            val bubbleStyle =
                if (voiceFilter.has("bubbleStyle")) {
                    voiceFilter.optInt("bubbleStyle")
                } else 0

            val suitColor1 =
                if (voiceFilter.has("suitColor1")) {
                    voiceFilter.optString("suitColor1")
                } else null

            val suitColor2 =
                if (voiceFilter.has("suitColor2")) {
                    voiceFilter.optString("suitColor2")
                } else null

            val voiceFilterInfo = VoiceFilterInfo(
                filterId = filterId,
                originalUrl = originalUrl,
                filterState = filterState,
                videoTemplateUrl = videoTemplateUrl,
                videoTemplateMd5 = videoTemplateMd5,
                bubbleStyle = bubbleStyle,
                suitColor1 = suitColor1,
                suitColor2 = suitColor2
            )
            return voiceFilterInfo
        }
        return null
    }

    fun addVoiceFilterInfo(voiceFilterInfo: VoiceFilterInfo?, json: JSONObject) {
        voiceFilterInfo?.let {
            val jsonObject = JSONObject().apply {
                it.filterId?.let {
                    put("filterId", it)
                }
                it.originalUrl?.let {
                    put("originalUrl", it)
                }
                it.filterState?.let {
                    put("filterState", it)
                }
                if (it.videoTemplateUrl!= null){
                    put("videoTemplateUrl", it.videoTemplateUrl)
                }
                if (it.videoTemplateMd5!= null){
                    put("videoTemplateMd5", it.videoTemplateMd5)
                }

                put("bubbleStyle", it.bubbleStyle)
                if (it.suitColor1 != null){
                    put("suitColor1", it.suitColor1)
                }
                if (it.suitColor2!= null){
                    put("suitColor2", it.suitColor2)
                }
            }
            json.put("filterInfo", jsonObject)
        }
    }
}