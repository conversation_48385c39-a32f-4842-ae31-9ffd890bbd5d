package com.interfun.buz.im.repo

import android.content.ComponentName
import android.content.ServiceConnection
import android.os.IBinder
import android.os.Parcelable
import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.base.ktx.isAppInForeground
import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.base.ktx.logError
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.im.IMAgent
import com.interfun.buz.im.entity.IMSendStateEvent
import com.interfun.buz.im.entity.IMType
import com.interfun.buz.im.ktx.getConvTargetIdLong
import com.interfun.buz.im.ktx.isGroup
import com.interfun.buz.im.message.BuzFileMessage
import com.interfun.buz.im.service.UploadFileForegroundService
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.mapNotNull
import kotlinx.parcelize.Parcelize

@Parcelize
data class FileUploadNotification(
    val count: Int,
    val targetId: Long,
    val servMsgId: String,
    val msgId: Long,
    val fileName: String,
    val isGroup: Boolean
) : Parcelable

/**
 * Author: ChenYouSheng
 * Date: 2025/6/26
 * Email: <EMAIL>
 * Desc: 文件上传通知处理
 */
class IMFileUploadNotificationRepository {

    companion object {
        const val TAG = "IMFileUploadNotificationRepository"
    }

    private var uploadServiceBinder: UploadFileForegroundService.UploadBinder? = null
    private var pendingNotification: FileUploadNotification? = null
    private var isServiceBound = false

    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, binder: IBinder?) {
            logInfo(TAG, "UploadFileForegroundService connected")
            uploadServiceBinder = binder as? UploadFileForegroundService.UploadBinder
            isServiceBound = true

            pendingNotification?.let {
                uploadServiceBinder?.updateNotification(it)
                pendingNotification = null
            }
        }

        override fun onServiceDisconnected(name: ComponentName?) {
            logInfo(TAG, "UploadFileForegroundService disconnected")
            uploadServiceBinder = null
            isServiceBound = false
        }
    }


    private val showUploadNotificationFlow: Flow<FileUploadNotification> = IMAgent.msgSendStateFlow
        .filter {
            val msg = when (it) {
                is IMSendStateEvent.OnSuccess -> it.msg
                is IMSendStateEvent.OnAttach -> it.msg
                is IMSendStateEvent.OnError -> it.msg
                is IMSendStateEvent.OnCanceled -> it.msg
                else -> null
            }
            msg?.msgType == IMType.TYPE_FILE_MSG
        }
        .mapNotNull {
            IMAgent.getUploadingFileInfoSync(null)?.let { (count, firstMessage) ->
                val targetId = firstMessage?.getConvTargetIdLong() ?: 0L
                val servMsgId = firstMessage?.serMsgId ?: ""
                val isGroup = firstMessage?.isGroup == true
                val msgId = firstMessage?.msgId ?: 0L
                val fileName = (firstMessage?.content as? BuzFileMessage)?.name ?: ""
                FileUploadNotification(count, targetId, servMsgId, msgId, fileName, isGroup)
            }
        }


    private fun handleUploadNotification(model: FileUploadNotification) {
        logDebug(TAG, "handleUploadNotification: $model")

        if (model.count == 0) {
            safelyUnbindService()
            UploadFileForegroundService.stopService()
            return
        }

        // 服务未绑定时先启动并绑定
        if (uploadServiceBinder == null && isAppInForeground) {
            UploadFileForegroundService.startService(model)
            UploadFileForegroundService.bindService(serviceConnection)
        }

        // 保存待处理的数据
        pendingNotification = model

        // 若已经绑定成功，立即更新通知，否则会在绑定成功时处理pendingNotification
        uploadServiceBinder?.updateNotification(model)
    }

    private fun safelyUnbindService() {
        if (isServiceBound) {
            try {
                UploadFileForegroundService.unbindService(appContext, serviceConnection)
                logInfo(TAG, "Unbound from UploadFileForegroundService")
            } catch (e: IllegalArgumentException) {
                logError(TAG, "unbindService UploadFileForegroundService error: ${e.message}")
            }
            isServiceBound = false
        }
    }


    suspend fun start() {
        showUploadNotificationFlow.collect { model ->
            handleUploadNotification(model)
        }
    }
}

