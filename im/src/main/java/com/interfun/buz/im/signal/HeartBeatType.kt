package com.interfun.buz.im.signal

object HeartBeatType {
    // 全局心跳
    const val GLOBAL = 1
    const val VOICE_CALL = 2
//    @Deprecated("WT_ONLINE已经下线")
//    const val WT_ONLINE = 3 //对讲机心跳
    const val WT_ONLINE_GROUP_SELECTED = 4 //对讲机模式下选中的群组在线心跳
    const val VOICE_CALL_GROUP_SELECTED = 5 //首页选中群组时监听房间通话状态
    const val T_ON_AIR_CHANNELID = 6 //监听onAir房间心跳
    const val ON_AIR_GROUP_SELECTED = 7 //首页选中群组时监听房间onAir状态
    const val ON_AIR_PREVIEW_DIALOG = 8 //打开on air预览弹窗时监听状态去
    const val LIVE_PLACE_CHANEL = 9 //空间内麦位和信息的实时同步
    const val T_GROUP_CHAT = 10 // 首页会话列表中群voice call、live place状态
    const val T_LIVE_PLACE_PREVIEW = 11 //资料页中时

    fun getRomeTopic(type: Int, data: String?): String? {
        return when (type) {
            WT_ONLINE_GROUP_SELECTED -> "T_GROUP_ONLINE_CHANGE_$data"
            VOICE_CALL -> "T_REAL_TIME_CALL_$data"
            VOICE_CALL_GROUP_SELECTED -> "T_GROUP_CALL_CHANGE_$data"
            T_ON_AIR_CHANNELID -> "T_ON_AIR_$data"
            ON_AIR_GROUP_SELECTED -> "T_GROUP_ON_AIR_CHANGE_$data"
            ON_AIR_PREVIEW_DIALOG -> "T_ON_AIR_PREVIEW_$data"
            LIVE_PLACE_CHANEL -> "T_LIVE_PLACE_$data"
            T_GROUP_CHAT -> "T_GROUP_CHAT_$data"
            T_LIVE_PLACE_PREVIEW -> "T_LIVE_PLACE_PREVIEW_$data"
            else -> null
        }
    }
}