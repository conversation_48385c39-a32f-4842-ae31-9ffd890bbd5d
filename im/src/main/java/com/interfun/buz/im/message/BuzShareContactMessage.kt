package com.interfun.buz.im.message

import com.interfun.buz.im.entity.EventTrackExtra
import com.interfun.buz.im.entity.IMMessageContentExtra
import com.interfun.buz.im.entity.IMType
import com.lizhi.im5.sdk.message.model.IM5MsgContent
import com.lizhi.im5.sdk.message.model.MessageTag
import org.json.JSONObject

/**
 * Author: ChenYouSheng
 * Date: 2025/7/7
 * Email: <EMAIL>
 * Desc: 分享联系人消息
 */
@MessageTag(type = IMType.TYPE_SHARE_CONTACT_MSG, flag = MessageTag.COUNTANDPERSIST)
class BuzShareContactMessage : IM5MsgContent {

    var id: Long = 0L // userId 或 groupId
    var name: String = "" // 全名，普通用户包括 firstName 和 lastName，群名或AI名
    var portrait: String = "" // 为空则使用默认头像兜底
    var userType: Int = -1   // 用户类型：0 - 普通，1 - 机器人，2 - 用研，3 - 官方
    var groupMemberCount: Int = 0   // 群成员人数，members 本地进行国际化处理
    var buzId: String = ""  // 用户可自定义的唯一标识
    var contactType: Int = 0 // 名片类型：1个人用户，2群组

    companion object {
        const val KEY_ID = "id"
        const val KEY_NAME = "name"
        const val KEY_PORTRAIT = "portrait"
        const val KEY_USER_TYPE = "userType"
        const val KEY_GROUP_MEMBER_COUNT = "groupMemberCount"
        const val KEY_BUZ_ID = "buzId"
        const val KEY_CONTACT_TYPE = "contactType"


        fun obtain(
            id: Long,
            name: String,
            portrait: String,
            userType: Int,
            groupMemberCount: Int,
            buzId: String,
            contactType: Int
        ): BuzShareContactMessage {
            return BuzShareContactMessage().apply {
                this.id = id
                this.name = name
                this.portrait = portrait
                this.userType = userType
                this.groupMemberCount = groupMemberCount
                this.buzId = buzId
                this.contactType = contactType
            }
        }
    }

    private var mExtra: String = ""

    override fun encode(): String {
        return JSONObject().apply {
            put(KEY_ID, id)
            put(KEY_NAME, name)
            put(KEY_PORTRAIT, portrait)
            put(KEY_USER_TYPE, userType)
            put(KEY_GROUP_MEMBER_COUNT, groupMemberCount)
            put(KEY_BUZ_ID, buzId)
            put(KEY_CONTACT_TYPE, contactType)
        }.toString()
    }

    override fun decode(jsonString: String): Boolean {
        if (jsonString.isEmpty()) {
            return false
        }
        with(JSONObject(jsonString)) {
            id = optLong(KEY_ID, 0L)
            name = optString(KEY_NAME, "")
            portrait = optString(KEY_PORTRAIT, "")
            userType = optInt(KEY_USER_TYPE, -1)
            groupMemberCount = optInt(KEY_GROUP_MEMBER_COUNT, 0)
            buzId = optString(KEY_BUZ_ID, "")
            contactType = optInt(KEY_CONTACT_TYPE, 0)
        }
        return true
    }

    override fun getDigest(): String {
        return ""
    }

    override fun setExtra(extra: String?) {
        this.mExtra = extra ?: ""
    }

    override fun getExtra(): String {
        return mExtra
    }

    override fun prepareToForward() {
        super.prepareToForward()
        val contentExtra = IMMessageContentExtra.parseFromJson(extra)
        val eventExtra = contentExtra.eventTrackExtra
        val newExtra = if (eventExtra == null) {
            contentExtra.copy(eventTrackExtra = EventTrackExtra(isForwardMsg = true))
        } else {
            contentExtra.copy(eventTrackExtra = eventExtra.copy(isForwardMsg = true))
        }
        this.extra = newExtra.toJson()
    }
}