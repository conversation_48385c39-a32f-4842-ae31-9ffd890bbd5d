package com.interfun.buz.im.di

import com.interfun.buz.component.hilt.UserComponent
import com.interfun.buz.component.hilt.UserQualifier
import com.interfun.buz.im.IMUserEntry
import com.interfun.buz.im.repo.IMFileDownloadRepository
import com.interfun.buz.im.repo.IMFileUploadRepository
import dagger.hilt.EntryPoint
import dagger.hilt.InstallIn


@EntryPoint
@InstallIn(UserComponent::class)
interface IMUserEntryPoint {

    @UserQualifier
    fun fileDownloadRepository(): IMFileDownloadRepository

    @UserQualifier
    fun fileUploadRepository(): IMFileUploadRepository

    @UserQualifier
    fun getIMUserEntry(): IMUserEntry
}


