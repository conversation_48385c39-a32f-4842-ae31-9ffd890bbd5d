package com.interfun.buz.im.util

import com.buz.idl.user.bean.UserInfo
import com.interfun.buz.base.ktx.appName
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.common.bean.push.PushPayload
import com.interfun.buz.common.database.entity.UserRelationInfo
import com.interfun.buz.common.ktx.portrait
import com.interfun.buz.common.ktx.uid
import com.interfun.buz.common.ktx.userName
import com.interfun.buz.common.manager.AppConfigRequestManager
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.manager.router.RouterCreator
import com.interfun.buz.common.utils.language.LanguageManager
import com.interfun.buz.im.R
import com.interfun.buz.im.entity.IMMessageUserExtra
import com.interfun.buz.im.ktx.mentionedUserIdsJsonArray
import com.interfun.buz.im.ktx.mentionedUserIdsList
import com.interfun.buz.im.message.BuzLocationMessage
import com.interfun.buz.im.message.BuzTextMsg
import com.interfun.buz.im.message.BuzVoiceGifMsg
import com.interfun.buz.im.message.WTVoiceEmojiMsg
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import com.lizhi.im5.sdk.message.IMessage
import com.lizhi.im5.sdk.message.model.IM5ImageMessage
import com.lizhi.im5.sdk.message.model.IM5TextMessage
import com.lizhi.im5.sdk.message.model.IM5VideoMessage
import com.lizhi.im5.sdk.message.model.IM5VoiceMessage
import org.json.JSONObject

object IMUtils {
    fun createPayloadV2(): String {
        return JSONObject().put("ver", 2).toString()
    }
}

fun IMessage.setDefaultUserInfo() {
    userInfo = createDefaultUserInfo()
}

fun createDefaultUserInfo() : com.lizhi.im5.sdk.profile.UserInfo {
    val userInfo = com.lizhi.im5.sdk.profile.UserInfo()
    userInfo.apply {
        userId = UserSessionManager.uid.toString()
        portraitURL = UserSessionManager.portrait ?: ""
        nickName = UserSessionManager.userName ?: ""
    }
    return userInfo
}

fun IMessage.setDefaultPushContent() {
    if (AppConfigRequestManager.enableNewOfflinePushFormat) {
        pushContent = ""
        return
    }
    pushContent = generateDefaultPushContent()
}

fun IMessage.generateDefaultPushContent(): String {
    val sendContent = content
    return when (sendContent) {
        is IM5TextMessage -> {
            val textNeedCutOff = sendContent.text.length > 200
            return if(textNeedCutOff) sendContent.text.substring(0, 200) else sendContent.text
        }
        is BuzLocationMessage -> {
            return R.string.location.asString()
        }
        is IM5ImageMessage -> {
            return R.string.chat_push_image_message.asString()
        }
        is WTVoiceEmojiMsg -> {
            if (sendContent.isImageEmoji()) {
                return R.string.ve_voiceemoji_tip_updated.asString()
            }
            return AppConfigRequestManager.obtainVoiceEmojiMsgContent(sendContent.emojiIcon)
        }
        is BuzVoiceGifMsg -> {
            return R.string.voice_gif_tag.asString()
        }
        is IM5VoiceMessage -> {
            if (conversationType == IM5ConversationType.GROUP){
                return AppConfigRequestManager.groupMsgContent
            }else {
                AppConfigRequestManager.singleMsgContent
            }
        }

        is IM5VideoMessage -> {
            return R.string.chat_push_video_message.asString()
        }

        else -> {
            return AppConfigRequestManager.singleMsgContent
        }
    }
}

fun IMessage.setDefaultPushPayload(pushExtra: JSONObject?) {
    if (AppConfigRequestManager.enableNewOfflinePushFormat) {
        pushPayLoad = IMUtils.createPayloadV2()
    } else {
        val mentionedList = this.mentionedUserIdsJsonArray
        val finalPushExtra =
            if (pushExtra == null && mentionedUserIdsList != null) JSONObject() else pushExtra
        mentionedList?.let {
            finalPushExtra?.put("mentionedUsers", it)
        }
        val textMsgContent = (content as? BuzTextMsg)
        textMsgContent?.textMentioned?.let {
            finalPushExtra?.put("textMentioned", it)
        }
        val mentionMap = textMsgContent?.mentionedMaps?.mapValues { (_, value) ->
            value.userName
        }
        mentionMap?.let {
            finalPushExtra?.put("mentionMap", JSONObject(it))
        }
        pushPayLoad =
            PushPayload(
                title = generatePushTitle(),
                pushContent = generateDefaultPushContent(),
                router = generatePushRouter(),
                senderUserInfo = generatePushSenderInfo(),
                type = getPushPayloadType(),
                pushExtra = finalPushExtra
            ).toJson()
    }
}

fun IMessage.getPushPayloadType(): Int {
    return when (conversationType) {
        IM5ConversationType.PRIVATE -> PushPayload.TYPE_PRIVATE
        IM5ConversationType.GROUP -> PushPayload.TYPE_GROUP
        else -> PushPayload.TYPE_DEFAULT
    }
}

fun IMessage.generatePushTitle(): String {
    return when (conversationType) {
        IM5ConversationType.PRIVATE -> {
            userInfo.nickName?:""
        }
        IM5ConversationType.GROUP -> {
            //see GroupPushExtra.title
            ""
        }
        else -> {
            appName
        }
    }
}

fun IMessage.generatePushRouter(): JSONObject {
    return when (conversationType) {
        IM5ConversationType.PRIVATE -> {
            RouterCreator.createPrivateChatRouter(userInfo.userId, targetId)
        }
        IM5ConversationType.GROUP -> {
            RouterCreator.createGroupChatRouter(targetId.toLong())
        }
        else -> {
            JSONObject()
        }
    }
}

fun IMessage.generatePushSenderInfo(): UserInfo? {
    if (userInfo.userId.isNullOrEmpty()) {
        return null
    }
    val extraData = IMMessageUserExtra.parseFromJson(userInfo.extra)

    return UserInfo(
        userInfo.userId.toLong(),
        userInfo.nickName,
        extraData.firstName,
        extraData.lastName,
        userInfo.portraitURL,
        extraData.phone,
        0L,
        null,
        1,
        UserRelationInfo.USER_TYPE_NORMAL_USER,
        LanguageManager.getLocaleLanguageName(),
        extraData.buzId,
        extraData.email,
        extraData.userStatus,
        null,
        null
    )
}





