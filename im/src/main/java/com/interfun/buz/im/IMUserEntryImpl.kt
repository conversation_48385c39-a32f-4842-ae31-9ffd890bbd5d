package com.interfun.buz.im

import com.interfun.buz.base.ktx.log
import com.interfun.buz.component.hilt.UserQualifier
import com.interfun.buz.im.repo.IMFileDownloadRepository
import com.interfun.buz.im.repo.IMFileUploadRepository
import javax.inject.Inject

class IMUserEntryImpl @Inject constructor(
    @UserQualifier private val imFileDownloadRepository: IMFileDownloadRepository,
    @UserQualifier private val imFileUploadRepository: IMFileUploadRepository,
) : IMUserEntry {
    companion object {
        private const val TAG = "IMSocialDomainUserEntryImpl"
    }

    override fun init() {
        log(TAG, "init")
    }
}