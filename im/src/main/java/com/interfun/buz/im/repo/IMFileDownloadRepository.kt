package com.interfun.buz.im.repo

import android.content.Context
import com.interfun.buz.download.bean.DownloadProgressChange
import com.interfun.buz.download.bean.DownloadStateInfo
import com.interfun.buz.download.db.entity.BuzDownloadCacheEntity
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import kotlinx.coroutines.flow.SharedFlow
import java.io.File

/**
 * Author: ChenYouSheng
 * Date: 2025/6/6
 * Email: <EMAIL>
 * Desc:
 */
interface IMFileDownloadRepository {

    /**
     * Whether to allow downloading with mobile data.
     * Caller can set this value to true to allow downloading with mobile data.
     * everytime launch app will reset this value to false.
     */
    var allowDownloadWithMobileData: Boolean

    /**
     * For download progress changes event.
     */
    val progressFlow: SharedFlow<DownloadProgressChange>

    /**
     * For download state change events.
     */
    val stateFlow: SharedFlow<DownloadStateInfo>

    /**
     * For download notification events.
     */
    val notificationFlow: SharedFlow<DownloadNotificationModel>

    /**
     * delete download file
     */
    suspend fun deleteFile(url: String, fileName: String): Boolean


    /**
     * get all download file size
     */
    suspend fun getAllDownloadFileSize(): Long

    /**
     * Download file
     * Mark：before call this method, you should check [allowDownloadWithMobileData] value.
     * and once call this method, [allowDownloadWithMobileData] will be set to true.
     * When call this method, it will insert a record into [BuzMediaCacheEntity] table.
     * @param url download url
     * @param fileName file name
     * @param serverFileSize server file size
     * @param serMsgId server message id
     * @param conversationType conversation type
     * @param targetId target id
     * @param fromId from id
     * @param source source
     */
    fun download(
        url: String,
        fileName: String,
        serverFileSize: Long,
        conversationType: IM5ConversationType,
        serMsgId: String,
        targetId: String,
        fromId: String,
        source: String
    )


    /**
     * get download status
     * @param url download url
     * @param fileName file name
     * @param checkDatabase if true, will check database to get download status, otherwise will only check memory queue.
     * @return download status, if return null, means not found download status.
     */
    suspend fun getDownloadStateInfo(url: String, fileName: String?, checkDatabase: Boolean): DownloadStateInfo

    /**
     * pause download
     */
    suspend fun pause(url: String, fileName: String?)

    /**
     * resume download
     */
    suspend fun resume(url: String, fileName: String?)

    /**
     * get download file
     */
    suspend fun getDownloadFile(url: String, fileName: String?): File?

    /**
     * get download file size
     */
    suspend fun getDownloadFileSize(url: String, fileName: String?): Long

    /**
     * Get all file records of download failures
     */
    suspend fun getAllDownloadFailureCache(): List<BuzDownloadCacheEntity>

    /**
     * Resume file downloading that is canceled by kill the app
     * It will call [getAllDownloadFailureCache] to get all the cache that is not downloaded successfully.
     * If currently is using Wi-Fi and there are previously undownloaded files, it will directly resume the previous download.
     * If currently is using mobile data and there are previously undownloaded files,
     * it will show a notification to remind the user that the current download has been paused.
     */
    suspend fun resumeHistoryDownloading()


    /**
     * Open file with system file viewer or installer
     * @return result indicating success/failure and associated error type
     */
    suspend fun openFile(
        context: Context,
        localPath: String?,
        remoteUrl: String?,
        fileName: String,
        serMsgId: String,
        conversationType: IM5ConversationType,
        targetId: String,
        fromId: String
    ): FileOpenResult

    /**
     * Get all file records of download failures
     */
    suspend fun queryAllDownloadCache(): List<BuzDownloadCacheEntity>

    /**
     * Remove cache list
     */
    suspend fun removeCacheList(cacheList: List<BuzDownloadCacheEntity>)

}

sealed class FileOpenResult {
    /** File opened successfully */
    data object Success : FileOpenResult()
    
    /** File not found (either local file doesn't exist or downloaded file not found) */
    data object FileNotFound : FileOpenResult()
    
    /** No supported app found to open this file type */
    data object NoSupportedApp : FileOpenResult()

    /** This is an APK file */
    data object IsApk : FileOpenResult()
    
    /** Other error occurred during file opening */
    data class Error(val exception: Exception) : FileOpenResult()
}

sealed interface DownloadNotificationModel{
    data class StopDownload(val title: String) : DownloadNotificationModel
    data class StopDownloadTips(val tips: List<String>) : DownloadNotificationModel
}
