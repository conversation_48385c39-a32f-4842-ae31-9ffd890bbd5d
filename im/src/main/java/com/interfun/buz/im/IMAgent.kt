package com.interfun.buz.im

import android.content.Context
import androidx.compose.ui.util.fastForEachReversed
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.buz.idl.login.bean.ImDeviceParams
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.bean.DataUpdate
import com.interfun.buz.common.manager.userLifecycleOwner
import com.interfun.buz.common.manager.userLifecycleScope
import com.interfun.buz.common.service.IMHeartbeatType
import com.interfun.buz.common.utils.BuzTracker
import com.interfun.buz.common.utils.ClientTracker
import com.interfun.buz.im.IMAgent.getConversationList
import com.interfun.buz.im.IMAgent.loadGroupHistory
import com.interfun.buz.im.constants.IMErrorCode
import com.interfun.buz.im.entity.*
import com.interfun.buz.im.ktx.*
import com.interfun.buz.im.message.ClickableCenterNotifyCommand
import com.interfun.buz.im.message.CommandMsg
import com.interfun.buz.im.message.OnAirGiftMsg
import com.interfun.buz.im.message.SoundBoardEmojiMsg
import com.interfun.buz.im.model.IMResult
import com.interfun.buz.im.signal.HeartBeatManager
import com.interfun.buz.im.signal.PushAgentManager
import com.interfun.buz.im.signal.SignalManagerPresenter
import com.interfun.buz.im.track.IMTracker
import com.interfun.buz.im.util.*
import com.interfun.buz.signal.ISignalManagerPresenter
import com.lizhi.im5.sdk.auth.AuthStatus
import com.lizhi.im5.sdk.base.CommCallback
import com.lizhi.im5.sdk.base.IM5Observer
import com.lizhi.im5.sdk.base.IM5ServiceStatusObserver
import com.lizhi.im5.sdk.base.Reaction
import com.lizhi.im5.sdk.conversation.IConversation
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import com.lizhi.im5.sdk.conversation.IM5ConversationType.GROUP
import com.lizhi.im5.sdk.conversation.IM5ConversationType.PRIVATE
import com.lizhi.im5.sdk.conversation.IM5ConversationUpdateTimeParam
import com.lizhi.im5.sdk.group.UnreadData
import com.lizhi.im5.sdk.message.*
import com.lizhi.im5.sdk.message.model.IM5MsgContent
import com.lizhi.im5.sdk.message.model.IM5VideoMessage
import com.lizhi.im5.sdk.message.model.MediaMessageContent
import com.yibasan.lizhifm.lzlogan.Logz
import kotlinx.coroutines.*
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.*
import org.json.JSONObject
import java.util.LinkedList

data class IMErrorInfo(val errorType: Int, val errorCode: Int, val errorMsg: String?) {
    override fun toString(): String {
        return "IMErrorInfo [errorType:$errorType,errorCode:$errorCode,errorMsg:$errorMsg]"
    }
}

class IMProgressInfo(val message: IMessage, val totalSize: Long, val currentSize: Long)

typealias InitialData = List<Pair<IMMsgIdentity, BuzSendingState>>
typealias UpdateData = Pair<IMMsgIdentity, BuzSendingState>

/**
 * Interface of IM
 */
object IMAgent {

    private val mIMManager = IMManager()
    val authStatusFlow = mIMManager.authStatusFlow
    val localMsgInsertedFlow = mIMManager.localMsgInsertedFlow
    val imConnectStatus get() = mIMManager.imLongLinkConnectStatus
    val msgSendStateFlow get() = mIMManager.msgSendStateFlow
    val onlineMsgSendStateFlow get() = mIMManager.onlineMsgSendStateFlow
    val msgReceivedFlow get() = mIMManager.msgReceivedFlow
    val onlineMsgReceivedFlow get() = mIMManager.onlineMsgReceivedFlow
    val msgVoiceFilterFlow get() = mIMManager.msgVoiceFilterFlow
    val msgEndLoadingFlow get() = mIMManager.msgEndLoadingFlow
    val conversationChangedFlow get() = mIMManager.conversationChangedFlow
    val conversationListChangedFlow get() = mIMManager.conversationListChangedFlow


    const val TAG = "IMAgent"

    /**
     * Im init
     */
    fun init(context: Context) {
        mIMManager.init(context)
    }

    fun onSessionRefresh(userId: Long, imUin: Long, session: String) {
        mIMManager.onSessionRefresh(userId, imUin, session)
    }
        /**
     * login im
     */
    fun onUserLogin(userId: Long, useUnifyLogin: Boolean, imUin: Long, session: String) {
        mIMManager.onUserLogin(userId, useUnifyLogin, imUin, session)

        PushAgentManager.setAlias(userId.toString(),"onSessionUserLogin")
        SignalManagerPresenter.obtainInstance().ready()
        HeartBeatManager.start()
    }

    /**
     * logout im
     */
    fun onUserLogout() {
        mIMManager.onUserLogout()
        PushAgentManager.clearAlias()
        SignalManagerPresenter.obtainInstance().unready()
        HeartBeatManager.clear()
    }

    fun connect() {
        mIMManager.connect()
    }

    fun connectStatusFlow() = mIMManager.connectStatusFlow()

    /**
     * 获取当前登陆状态
     */
    fun getCurrentAuthStatus(): AuthStatus {
        return mIMManager.authStatusFlow.value
    }

    fun cancel(uuid: String) {
        mIMManager.cancel(uuid)
    }

    fun getImDeviceParams(): ImDeviceParams {
        return mIMManager.getImDeviceParams();
    }

    fun getMessageSendStateFlow(messages: Set<IMMsgIdentity>): Flow<Map<IMMsgIdentity, BuzSendingState>> =
        channelFlow {
            // 1. 空检查
            if (messages.isEmpty()) {
                send(emptyMap())
                return@channelFlow
            }
            // 2. 创建结果缓存 - 避免重复数据库查询
            val result = mutableMapOf<IMMsgIdentity, BuzSendingState>()

            // 3. 监听实时更新流
            val flow: Flow<DataUpdate<InitialData, UpdateData>> = msgSendStateFlow.map { sendStateEvent ->
                DataUpdate.Update(sendStateEvent.getMsgIdentity() to sendStateEvent.toBuzSendingState())
            }

            // 4. 初始化数据获取
            flow.shareIn(this, SharingStarted.Lazily).onSubscription {
                // 按会话类型分组消息ID
                val privateMessageIds = messages.mapNotNull { it.takeIf { it.convType == PRIVATE }?.msgId }
                val groupMessageIds = messages.mapNotNull { it.takeIf { it.convType == GROUP }?.msgId }

                // 从本地数据库获取消息状态
                val privateMessages = getLocalMessages(PRIVATE, privateMessageIds)
                val groupMessages = getLocalMessages(GROUP, groupMessageIds)

                // 构建初始状态数据
                val state = ArrayList<Pair<IMMsgIdentity,BuzSendingState>>(messages.size)
                privateMessages?.forEach { msg ->
                    state.add(msg.toMsgIdentity() to msg.buzSendingState)
                }
                groupMessages?.forEach { msg ->
                    state.add(msg.toMsgIdentity() to msg.buzSendingState)
                }
                // 发送初始化数据
                emit(DataUpdate.Init(state))
            }.collect { update ->
                when (update) {
                    is DataUpdate.Init -> {
                        // 初始化数据处理
                        val list = update.data
                        list.forEach { (identity, state) ->
                            result[identity] = state
                        }
                        send(result.toMap())
                    }
                    is DataUpdate.Update -> {
                        // 实时更新数据处理
                        val (identity, state) = update.data
                        result[identity] = state
                        send(result.toMap())
                    }
                }
            }
        }

    suspend fun getConversationList(
        convType: IM5ConversationType? = null,
        count: Int = 0,
        timestamp: Long = 0,
    ): IMFetchResult<List<IConversation>?> =
        mIMManager.getConversationList(convType, count, timestamp)

    fun getConversationListFlow(
        convType: IM5ConversationType? = null,
        count: Int = 0,
        timestamp: Long = 0,
    ): Flow<List<IConversation>> = channelFlow {
        val resultList = LinkedList<IConversation>()
        val flow: SharedFlow<DataUpdate<List<IConversation>, List<IConversation>>> =
            conversationListChangedFlow.map { DataUpdate.Update(it) }
                .shareIn(this, SharingStarted.Lazily)
        flow.onSubscription {
            while (currentCoroutineContext().isActive) {
                val result = getConversationList(convType, count, timestamp)
                when (result) {
                    is IMFetchResult.Success -> {
                        if (result.data != null) {
                            emit(DataUpdate.Init(result.data))
                            break
                        }
                    }

                    is IMFetchResult.Error -> {
                        logInfo(TAG, "getConversationListFlow error:${result.error}")
                    }
                }
                //失败一直重试
                delay(3000)
            }
        }.collect { convList ->
            when (convList) {
                is DataUpdate.Init -> {
                    resultList.addAll(convList.data)
                }

                is DataUpdate.Update -> {
                    //反过来，因为后面是每个都添加到0
                    convList.data.fastForEachReversed { newConv ->
                        if (newConv.isDelete) {
                            resultList.removeIf { it.targetId == newConv.targetId }
                        } else {
                            var oldConv: IConversation? = null
                            var oldIndex: Int? = null
                            for ((index, conv) in resultList.withIndex()) {
                                if (conv.targetId == newConv.targetId) {
                                    oldConv = conv
                                    oldIndex = index
                                    break
                                }
                            }
                            if (oldIndex == null || oldConv == null || shouldMoveToFirst(oldConv, newConv)) {
                                oldIndex?.let { resultList.removeAt(oldIndex) }
                                resultList.add(0, newConv)
                            } else {
                                resultList.removeAt(oldIndex)
                                resultList.add(oldIndex, newConv)
                            }
                        }
                    }
                }
            }
            send(resultList.toList())
        }
    }.flowOn(Dispatchers.Default)

    private fun shouldMoveToFirst(oldConv: IConversation, newConv: IConversation): Boolean {
        val newLastMsg : IMessage? = newConv.lastMessage
        val oldLastMsg : IMessage? = oldConv.lastMessage
        //清除历史消息不需要更新（newLastMsg == null）
        val isMsgChange = newLastMsg != null && newLastMsg.msgId != oldLastMsg?.msgId
        val result = newConv.convModifyTime != oldConv.convModifyTime || isMsgChange
        return result
    }

    /**
     * Get conversation list sync.
     * @see getConversationList
     */
    suspend fun getConversationListSync(
        lifecycleOwner: LifecycleOwner?,
        convType: IM5ConversationType? = null,
        count: Int = 0,
        timestamp: Long = 0,
        waitLogin: Boolean = true
    ): Pair<MutableList<IConversation>?, IMErrorInfo?> {
        return suspendCancellableCoroutine { continuation ->
            val uuid = getConversationList(
                lifecycleOwner,
                convType,
                count,
                timestamp,
                onFailed = { error ->
                    Logz.tag(TAG).e(
                        "getConversationList onFailed errorCode = ${error.errorCode}" +
                                ", errorMsg = ${error.errorMsg}" +
                                ", errorType = ${error.errorType}"
                    )
                    if (continuation.isActive) {
                        continuation.resumeWith(Result.success(null to error))
                    }
                },
                onSuccess = {
                    if (continuation.isActive) {
                        continuation.resumeWith(Result.success(it to null))
                    }
                }
            )
            if (waitLogin.not() && uuid != null) {
                mIMManager.cancel(uuid)
                continuation.resumeWith(Result.success(null to IMErrorInfo(100000, 100000, "")))
            }
        }
    }

    /**
     * Get current uploading file info.
     */
    suspend fun getUploadingFileInfoSync(
        lifecycleOwner: LifecycleOwner?,
        waitLogin: Boolean = true
    ): IM5SendingMessageInfo? {
        return suspendCancellableCoroutine { continuation ->
            val uuid = mIMManager.getSendingMessageInfoByMsgTypes(
                lifecycleOwner = lifecycleOwner,
                msgTypes = listOf(IMType.TYPE_FILE_MSG),
                callback = object : IM5Observer<IM5SendingMessageInfo> {
                    override fun onEvent(info: IM5SendingMessageInfo?) {
                        if (continuation.isActive) {
                            continuation.resumeWith(Result.success(info))
                        }
                    }

                    override fun onError(
                        errType: Int,
                        errCode: Int,
                        errMsg: String?
                    ) {
                        if (continuation.isActive) {
                            continuation.resumeWith(Result.success(null))
                        }
                    }

                })
            if (waitLogin.not() && uuid != null) {
                mIMManager.cancel(uuid)
                continuation.resumeWith(Result.success(null))
            }
        }
    }

    /***
     * Get conversation list
     * @param lifecycleOwner
     * @param convType can be null if not used.
     * @param count Conversation count.
     * @param timestamp Get the conversations before this time (less than timeStamp),
     *     where timeStamp is the convModifyTIme of the conversation. 0: Indicates no time limit.
     */
    private fun getConversationList(
        lifecycleOwner: LifecycleOwner?,
        convType: IM5ConversationType? = null,
        count: Int = 0,
        timestamp: Long = 0,
        onFailed: OneParamCallback<IMErrorInfo>? = null,
        onSuccess: OneParamCallback<MutableList<IConversation>>? = null
    ): String? {
        val observer = object : IM5Observer<MutableList<IConversation>> {
            override fun onEvent(list: MutableList<IConversation>) {
                onSuccess?.invoke(list)
            }

            override fun onError(errorType: Int, errorCode: Int, errorMsg: String?) {
                onFailed?.invoke(IMErrorInfo(errorType, errorCode, errorMsg))
            }
        }
        return mIMManager.getConversationList(lifecycleOwner, convType, count, timestamp, observer)
    }

    /**
     * delete conversation
     */
    suspend fun deleteConversationSync(
        convType: IM5ConversationType,
        targetId: String
    ): Pair<Boolean?, IMErrorInfo?> {
        val result = mIMManager.deleteConversation(convType, targetId)
        return when (result) {
            is IMFetchResult.Success -> result.data to null
            is IMFetchResult.Error -> null to result.error
        }
    }

    /**
     * Get historical messages sync.
     * @see getConversationList
     */
    suspend fun getLocalHistoryMessagesSync(
        lifecycleOwner: LifecycleOwner?,
        convType: IM5ConversationType,
        targetId: String,
        count: Int = 40,
        msgId: Long = 0,
        before: Boolean = true
    ): Pair<MutableList<IMessage>?, IMErrorInfo?> {
        return suspendCancellableCoroutine { contination ->
            getLocalHistoryMessages(
                lifecycleOwner,
                convType,
                targetId,
                count,
                msgId,
                before,
                onFailed = { error ->
                    if (contination.isActive) {
                        contination.resumeWith(Result.success(null to error))
                    }
                },
                onSuccess = {
                    if (contination.isActive) {
                        contination.resumeWith(Result.success(it to null))
                    }
                })
        }
    }

    /**
     * get history message according to the types
     */
    suspend fun getLocalHistoryMessagesByTypesAsync(
        convType: IM5ConversationType,
        targetId: String,
        msgTypes: List<Int>,
        count: Int = 100,
        msgId: Long = 0,
        before: Boolean = true
    ): Pair<MutableList<IMessage>?, IMErrorInfo?> {
        return suspendCancellableCoroutine { contination ->
            val uuid = getLocalHistoryMessagesByTypes(
                convType = convType,
                targetId = targetId,
                msgTypes = msgTypes,
                count = count,
                msgId = msgId,
                before = before,
                onFailed = { error ->
                    if (contination.isActive) {
                        contination.resumeWith(Result.success(null to error))
                    }
                },
                onSuccess = {
                    if (contination.isActive) {
                        contination.resumeWith(Result.success(it to null))
                    }
                })
            if (uuid != null) {
                contination.context.job.invokeOnCompletion {
                    mIMManager.cancel(uuid)
                }
            }
        }
    }

    /**
     * Load group history by both local and remote for specified arguments.
     * @param onNewHistory 用于返回所有remote消息中的'新消息'集合，目前业务使用场景：群聊历史'新消息'的接收结果埋点上报
     */
    fun loadGroupHistory(
        lifecycleOwner: LifecycleOwner?,
        targetId: String,
        count: Int = 20,
        msgId: Long = 0,
        before: Boolean = true,
        forceRequestRemote: Boolean = false,
        onFailed: OneParamCallback<IMErrorInfo>? = null,
        onLocalHistory: OneParamCallback<List<IMessage>>? = null,
        onAllHistory: OneParamCallback<List<IMessage>>? = null,
        onNewHistory: OneParamCallback<List<IMessage>>? = null
    ) {
        mIMManager.loadGroupHistory(
            lifecycleOwner,
            GROUP,
            targetId,
            count,
            msgId,
            before,
            forceRequestRemote,
            { list, errType, errCode, errMsg -> onLocalHistory?.invoke(list ?: emptyList()) }
        ) { historyResult, errType, errCode, errMsg ->
            if ((errType != 0 || errCode != 0) && errCode != IMErrorCode.GROUP_USER_NOT_IN_GROUP) {
                onFailed?.invoke(IMErrorInfo(errType, errCode, errMsg))
            } else {
                historyResult?.let {
                    onAllHistory?.invoke(it.msgList ?: emptyList())
                    onNewHistory?.invoke(it.newMsgList ?: emptyList())
                    if (it.newMsgList.isNotNull() && it.newMsgList.isNotEmpty()) {
                        IMTracker.trackNewMessageReceived(it.newMsgList)
                    }
                }
            }
        }
    }

    /**
     * Get historical messages sync.
     * @see loadGroupHistory
     */
    suspend fun loadGroupHistorySync(
        lifecycleOwner: LifecycleOwner?,
        targetId: String,
        count: Int = 20,
        msgId: Long = 0,
        before: Boolean = true,
        forceRequestRemote: Boolean = false,
    ): Pair<List<IMessage>?, IMErrorInfo?> {
        return suspendCancellableCoroutine { continuation ->
            loadGroupHistory(
                lifecycleOwner,
                targetId,
                count,
                msgId,
                before,
                forceRequestRemote,
                onFailed = { error ->
                    if (continuation.isActive) {
                        continuation.resumeWith(Result.success(null to error))
                    }
                },
                onAllHistory = {
                    if (continuation.isActive) {
                        continuation.resumeWith(Result.success(it to null))
                    }
                })
        }
    }

    /**
     * Get historical messages.
     * @param lifecycleOwner If pass,MessageCallback will be remove automatically when LifecycleOwner.lifecycle destroy.
     * @param targetId target
     * @param count Message count.
     * @param msgId Gets historical messages before or after the specified msgId.
     * @param before Ture to get historical messages before [msgId],and false otherwise.
     */
    private fun getLocalHistoryMessages(
        lifecycleOwner: LifecycleOwner?,
        convType: IM5ConversationType,
        targetId: String,
        count: Int = 40,
        msgId: Long = 0,
        before: Boolean = true,
        onFailed: OneParamCallback<IMErrorInfo>? = null,
        onSuccess: OneParamCallback<MutableList<IMessage>>? = null
    ) {
        val observer = object : IM5Observer<MutableList<IMessage>> {
            override fun onEvent(list: MutableList<IMessage>) {
                onSuccess?.invoke(list)
            }

            override fun onError(errorType: Int, errorCode: Int, errorMsg: String?) {
                onFailed?.invoke(IMErrorInfo(errorType, errorCode, errorMsg))
            }
        }
        mIMManager.getLocalHistoryMessages(
                lifecycleOwner,
                convType,
                targetId,
                count,
                msgId,
                before,
                observer
            )
    }

    /**
     * get history message according to the types
     * @param targetId target
     * @param msgTypes msg type
     * @param count Message count.
     * @param msgId Gets historical messages before or after the specified msgId.
     * @param before Ture to get historical messages before [msgId],and false otherwise.
     */
    private fun getLocalHistoryMessagesByTypes(
        convType: IM5ConversationType,
        targetId: String,
        msgTypes: List<Int>,
        count: Int = 100,
        msgId: Long = 0,
        before: Boolean = true,
        onFailed: OneParamCallback<IMErrorInfo>? = null,
        onSuccess: OneParamCallback<MutableList<IMessage>>? = null
    ): String? {
        val observer = object : IM5Observer<MutableList<IMessage>> {
            override fun onEvent(list: MutableList<IMessage>) {
                onSuccess?.invoke(list)
            }

            override fun onError(errorType: Int, errorCode: Int, errorMsg: String?) {
                onFailed?.invoke(IMErrorInfo(errorType, errorCode, errorMsg))
            }
        }
        return mIMManager.getLocalHistoryMessagesByTypes(
            convType = convType,
            targetId = targetId,
            msgTypes = msgTypes,
            count = count,
            msgId = msgId,
            before = before,
            observer = observer
        )
    }

    fun startSceneHeartBeat(scene: IMHeartbeatType, identifier: String?, timeInterval: Long) {
        mIMManager.startSceneHeartBeat(scene.sceneType, identifier, timeInterval)
    }

    fun stopSceneHeartBeat(scene: IMHeartbeatType) {
        mIMManager.stopSceneHeartBeat(scene.sceneType)
    }

    suspend fun getRangeRemoteHistoryMessagesSync(
        lifecycleOwner: LifecycleOwner,
        convType: IM5ConversationType,
        targetId: String,
        svrMsgId: String,
        beforeCount: Int = 25,
        afterCount: Int = 25
    ): Pair<MutableList<IMessage>?, IMErrorInfo?> {
        return suspendCancellableCoroutine { continuation ->
            mIMManager.getRangeRemoteHistoryMessage(
                lifecycleOwner,
                convType,
                targetId,
                svrMsgId,
                beforeCount,
                afterCount,
                object : IM5Observer<MutableList<IMessage>> {
                    override fun onEvent(list: MutableList<IMessage>?) {
                        continuation.resumeWith(Result.success(list to null))
                    }

                    override fun onError(errorType: Int, errorCode: Int, errorMsg: String?) {
                        continuation.resumeWith(
                            Result.success(
                                null to IMErrorInfo(
                                    errorType,
                                    errorCode,
                                    errorMsg
                                )
                            )
                        )
                    }
                })
        }
    }

    suspend fun getRangeLocalHistoryMessagesSync(
        lifecycleOwner: LifecycleOwner,
        convType: IM5ConversationType,
        targetId: String,
        msgId: Long,
        beforeCount: Int = 25,
        afterCount: Int = 25
    ): Pair<MutableList<IMessage>?, IMErrorInfo?> {
        return suspendCancellableCoroutine { contination ->
            mIMManager.getRangeLocalHistoryMessage(
                lifecycleOwner,
                convType,
                targetId,
                msgId,
                beforeCount,
                afterCount,
                object : IM5Observer<MutableList<IMessage>> {
                    override fun onEvent(list: MutableList<IMessage>?) {
                        contination.resumeWith(Result.success(list to null))
                    }

                    override fun onError(errorType: Int, errorCode: Int, errorMsg: String?) {
                        contination.resumeWith(
                            Result.success(
                                null to IMErrorInfo(
                                    errorType,
                                    errorCode,
                                    errorMsg
                                )
                            )
                        )
                    }
                })
        }
    }

    /**
     * Get historical messages.
     * @param lifecycleOwner If pass,MessageCallback will be remove automatically when LifecycleOwner.lifecycle destroy.
     * @param targetId target
     * @param count Message count.
     * @param msgId Gets historical messages before the specified msgId.
     */
    suspend fun getRemoteHistoryMessagesSync(
        lifecycleOwner: LifecycleOwner?,
        convType: IM5ConversationType,
        targetId: String,
        count: Int = 40,
        msgId: Long = 0
    ): Pair<MutableList<IMessage>?, IMErrorInfo?> {
        return suspendCancellableCoroutine { contination ->
            getRemoteHistoryMessages(
                lifecycleOwner,
                convType,
                targetId,
                count,
                msgId,
                onFailed = { error ->
                    contination.resumeWith(Result.success(null to error))
                },
                onSuccess = {
                    contination.resumeWith(Result.success(it to null))
                })
        }
    }

    /**
     * Get historical messages.
     * @param lifecycleOwner If pass,MessageCallback will be remove automatically when LifecycleOwner.lifecycle destroy.
     * @param targetId target
     * @param count Message count.
     * @param msgId Gets historical messages before the specified msgId.
     */
    private fun getRemoteHistoryMessages(
        lifecycleOwner: LifecycleOwner?,
        convType: IM5ConversationType,
        targetId: String,
        count: Int = 40,
        msgId: Long = 0,
        onFailed: OneParamCallback<IMErrorInfo>? = null,
        onSuccess: OneParamCallback<MutableList<IMessage>>? = null
    ) {
        val observer = object : IM5Observer<MutableList<IMessage>> {
            override fun onEvent(list: MutableList<IMessage>) {
                onSuccess?.invoke(list)
            }

            override fun onError(errorType: Int, errorCode: Int, errorMsg: String?) {
                onFailed?.invoke(IMErrorInfo(errorType, errorCode, errorMsg))
            }
        }
        mIMManager.getRemoteHistoryMessages(lifecycleOwner, convType, targetId, count, msgId, observer)
    }

    /**
     * enter Conversation
     * Will clear the unread data
     */
    suspend fun enterConversation(
        targetId: String,
        type: IM5ConversationType = PRIVATE
    ) {
        withContext(Dispatchers.IO) {
            mIMManager.enterConversation(targetId, type)
        }
    }

    /**
     * leave Conversation
     */
    fun leaveConversation(
        targetId: String,
        type: IM5ConversationType
    ) {
        mIMManager.leaveConversation(targetId, type)
    }

    /**
     * Get unread count for specific targets
     */
    suspend fun getUnreadCount(lifecycleOwner: LifecycleOwner?, vararg targetIds: String): Int {
        return suspendCancellableCoroutine<Int> { continuation ->
            mIMManager.getUnreadCount(lifecycleOwner, targetIds, object : IM5Observer<Int> {
                override fun onEvent(count: Int) {
                    continuation.resumeWith(Result.success(count))
                }

                override fun onError(p0: Int, p1: Int, p2: String?) {
                    continuation.resumeWith(Result.success(0))
                }
            })
        }
    }

    suspend fun getConversation(
        convType: IM5ConversationType,
        targetId: String
    ): IConversation? {
        return mIMManager.getConversationSync(convType, targetId)
    }

    suspend fun sendMessageSync(
        message: IM5Message,
        pushPayloadExtra: JSONObject?,
        enableEncrypt: Boolean? = null,
        replyId: Long? = null,
        traceId: String? = ClientTracker.generateTraceId(),
        isSendMediaMessage : Boolean = message.content is MediaMessageContent
    ): SendMsgResult {
        message.setDefaultUserInfo()
        message.setDefaultPushContent()
        message.setDefaultPushPayload(pushPayloadExtra)
        if (replyId != null) {
            message.sendReferenceMsgId = replyId.toLong()
        }
        message.msgTraceId = traceId
        enableEncrypt?.let { message.enableEncrypt(enableEncrypt) }
        return if (isSendMediaMessage) {
            mIMManager.sendMediaMessageSync(message)
        } else {
            mIMManager.sendMessageSync(message)
        }
    }

    suspend fun sendMessageFlow(
        message: IM5Message,
        pushPayloadExtra: JSONObject?,
        enableEncrypt: Boolean? = null,
        replyId: Long? = null,
        traceId: String? = ClientTracker.generateTraceId(),
        isSendMediaMessage: Boolean = message.content is MediaMessageContent,
        channel: Channel<SendMsgState> = Channel<SendMsgState>(capacity = Channel.UNLIMITED)
    ): Flow<SendMsgState> {
        message.setDefaultUserInfo()
        message.setDefaultPushContent()
        message.setDefaultPushPayload(pushPayloadExtra)
        if (replyId != null) {
            message.sendReferenceMsgId = replyId.toLong()
        }
        message.msgTraceId = traceId
        enableEncrypt?.let { message.enableEncrypt(enableEncrypt) }

        val callback = object : MediaMessageCallback {
            override fun onProgress(
                message: IMessage?,
                totalSize: Long,
                currentSize: Long
            ) {
                channel.trySend(
                    SendMsgState.SendMsgProgress(
                        message,
                        totalSize,
                        currentSize
                    )
                )
            }

            override fun onCanceled(message: IMessage?) {
                channel.trySend(SendMsgState.SendMsgCancel(message))
            }

            override fun onAttached(messageInfo: IMessage?) {
                channel.trySend(SendMsgState.SendMsgAttached(messageInfo))
            }

            override fun onSuccess(messageInfo: IMessage) {
                channel.trySend(SendMsgResult.SendMsgSuccess(messageInfo))
            }

            override fun onError(
                message: IMessage?,
                errorType: Int,
                errorCode: Int,
                errorMsg: String?
            ) {
                channel.trySend(
                    SendMsgResult.SendMsgFailed(
                        message,
                        IMErrorInfo(
                            errorType,
                            errorCode,
                            errorMsg
                        )
                    )
                )
            }
        }

        if (isSendMediaMessage) {
            mIMManager.sendMediaMessage(message, callback)
        } else {
            mIMManager.sendMessage(message, callback)
        }
        return channel.consumeAsFlow()
    }

    suspend fun sendPreprocessMessageSync(
        message: IM5Message,
        pushPayloadExtra: JSONObject?,
        enableEncrypt: Boolean? = null,
        replyId: Long? = null,
        traceId: String? = ClientTracker.generateTraceId(),
    ): SendMsgResult {
        if (replyId != null) {
            message.sendReferenceMsgId = replyId.toLong()
        }
        message.msgTraceId = traceId
        enableEncrypt?.let { message.enableEncrypt(enableEncrypt) }
        message.setDefaultUserInfo()
        message.setDefaultPushContent()
        message.setDefaultPushPayload(pushPayloadExtra)
        return mIMManager.sendPreprocessMessageSync(message)
    }

    suspend fun forwardMessageSync(
        message: IMessage,
        targetConvType: IM5ConversationType,
        targetId: String,
        pushPayloadExtra: JSONObject?,
        traceId: String? = ClientTracker.generateTraceId(),
    ): SendMsgResult {
        val messageNew =
            IM5Message.obtain(targetId, message.conversationType, message.content)
        messageNew.setDefaultUserInfo()
        messageNew.setDefaultPushContent()
        messageNew.setDefaultPushPayload(pushPayloadExtra)
        return mIMManager.forwardMessageSync(
            message.conversationType,
            message.msgId,
            targetConvType,
            targetId,
            traceId,
            messageNew.pushContent,
            messageNew.pushPayLoad,
            messageNew.userInfo
        )
    }

    suspend fun previewVoiceFilterMessage(
        message: IM5Message,
        pushPayloadExtra: JSONObject?,
        enableEncrypt: Boolean? = null,
        replyId: Long? = null,
        traceId: String? = ClientTracker.generateTraceId(),
    ): String? {
        if (replyId != null) {
            message.sendReferenceMsgId = replyId.toLong()
        }
        message.msgTraceId = traceId
        enableEncrypt?.let { message.enableEncrypt(enableEncrypt) }
        message.setDefaultUserInfo()
        message.setDefaultPushContent()
        message.setDefaultPushPayload(pushPayloadExtra)
        val previewId = mIMManager.previewVoiceFilter(message)
        return previewId
    }

    suspend fun sendVoiceFilterMessage(
        message: IM5Message,
        pushPayloadExtra: JSONObject?,
        enableEncrypt: Boolean? = null,
        replyId: Long? = null,
        traceId: String? = ClientTracker.generateTraceId(),
    ) : SendMsgResult {
        if (replyId != null) {
            message.sendReferenceMsgId = replyId.toLong()
        }
        message.msgTraceId = traceId
        enableEncrypt?.let { message.enableEncrypt(enableEncrypt) }
        message.setDefaultUserInfo()
        message.setDefaultPushContent()
        message.setDefaultPushPayload(pushPayloadExtra)
        return mIMManager.sendVoiceFilterMessage(message)
    }

    suspend fun sendVoiceFilterMessage(
        previewId: String,
    ) : SendMsgResult {
        return mIMManager.sendVoiceFilterMessage(previewId)
    }

    suspend fun prepareVideoMessageSync(message: IMessage): SendMsgResult {
        return mIMManager.prepareVideoMessageSync(message as IM5Message)
    }

    suspend fun prepareVideoMsgSync(
        message: IM5Message,
        pushPayloadExtra: JSONObject?,
        enableEncrypt: Boolean? = null,
        replyId: Long? = null,
        traceId: String? = ClientTracker.generateTraceId(),
    ): SendMsgResult {
        if (replyId != null) {
            message.sendReferenceMsgId = replyId.toLong()
        }
        message.msgTraceId = traceId
        enableEncrypt?.let { message.enableEncrypt(enableEncrypt) }
        message.setDefaultUserInfo()
        message.setDefaultPushContent()
        message.setDefaultPushPayload(pushPayloadExtra)
        return mIMManager.prepareVideoMessageSync(message)
    }

    suspend fun prepareVideoMsgFailed(
        msgId: Long,
        convType: IM5ConversationType,
        reason: String
    ): SendMsgResult {
        return mIMManager.prepareVideoMessageFailed(msgId, convType, reason)
    }

    suspend fun sendPreparedVideoMsg(
        msgId: Long,
        convType: IM5ConversationType,
    ): SendMsgResult {
        return mIMManager.sendPreparedVideoMessage(msgId, convType)
    }

    fun insertCenteredMsg(
        lifecycleOwner: LifecycleOwner?,
        fromId: String,
        targetId: String,
        convType: IM5ConversationType,
        createTime: Long,
        text: String,
        digest: String,
        action: String = "",
        actionText: String = "",
        actionMaps: Map<String, ClickableCenterNotifyCommand.HighlightInfo>? = null,
        observer: IM5Observer<IMessage>? = null,
        isTranslate: Boolean = false,
    ) {
        val command = ClickableCenterNotifyCommand(
            IMCommandBusinessType.TYPE_CLICKABLE_CENTER_NOTIFY,
            IMCommandSubBusinessType.TYPE_UNKNOWN
        ).apply {
            uiDisplay = true
        }
        command.text = text
        command.digest = digest
        command.action = action
        command.actionText = actionText
        command.actionMaps = actionMaps
        command.isTranslate = isTranslate
        val msg = CommandMsg().apply {
            content = command.encode()
        }
        insertLocalMsg(lifecycleOwner, msg, fromId, targetId, convType, createTime, observer)
    }

    fun insertLocalMsg(
        lifecycleOwner: LifecycleOwner?,
        messageContent: IM5MsgContent,
        fromId: String,
        targetId: String,
        convType: IM5ConversationType,
        createTime: Long,
        observer: IM5Observer<IMessage>? = null
    ) {
        mIMManager.insertLocalMessage(
            lifecycleOwner,
            messageContent,
            fromId,
            targetId,
            convType,
            createTime,
            observer
        )
    }

    fun resendMsg(
        convType: IM5ConversationType,
        msgId: Long,
        msgTraceId: String? = null,
        callback: MessageCallback? = null
    ) {
        mIMManager.resendMessage(userLifecycleOwner, convType, msgId, msgTraceId, callback)
    }

    fun getMessage(
        lifecycleOwner: LifecycleOwner?,
        convType: IM5ConversationType,
        messageId: Long,
        callback: IM5Observer<IMessage?>
    ): String? {
        return mIMManager.getMessage(lifecycleOwner, convType, messageId, callback)
    }

    suspend fun getLocalMessages(
        convType: IM5ConversationType,
        messageIdList: List<Long>,
    ): List<IMessage>? {
        return mIMManager.getLocalMessages(convType, messageIdList)
    }

    suspend fun getMessageSync(
        convType: IM5ConversationType,
        messageId: Long
    ): IMessage? {
        return getMessageSync(null, convType, messageId)
    }

    @Deprecated("use getMessageSync(IM5ConversationType, Long) instead,don't need LifecycleOwner")
    suspend fun getMessageSync(
        lifecycleOwner: LifecycleOwner?,
        convType: IM5ConversationType,
        messageId: Long
    ): IMessage? {
        return suspendCancellableCoroutine { continuation ->
            val uuid =
                getMessage(lifecycleOwner, convType, messageId, object : IM5Observer<IMessage?> {
                    override fun onEvent(msg: IMessage?) {
                        continuation.resumeWith(Result.success(msg))
                    }

                    override fun onError(errorType: Int, errorCode: Int, errorMsg: String?) {
                        log(TAG, "getMessage onError,errorType${errorType},errorCode:${errorCode},errorMsg:${errorMsg}")
                        continuation.resumeWith(Result.success(null))
                    }
                })
            if (uuid != null) {
                continuation.context.job.invokeOnCompletion { mIMManager.cancel(uuid) }
            }
        }
    }

    suspend fun getMessageForServerMsgIdSync(
        lifecycleOwner: LifecycleOwner?,
        convType: IM5ConversationType,
        messageId: Long
    ): IMessage? {
        return suspendCancellableCoroutine { continuation ->
            mIMManager.getMessageForServerMsgId(
                lifecycleOwner,
                convType,
                messageId,
                object : IM5Observer<IMessage?> {
                    override fun onEvent(msg: IMessage?) {
                        continuation.resumeWith(Result.success(msg))
                    }

                    override fun onError(errorType: Int, errorCode: Int, errorMsg: String?) {
                        log(
                            TAG,
                            "getMessage onError,errorType${errorType},errorCode:${errorCode},errorMsg:${errorMsg}"
                        )
                        continuation.resumeWith(Result.success(null))
                    }
                })
        }
    }

    suspend fun deleteLocalMessages(
        convType: IM5ConversationType,
        targetId: String,
        vararg msgId: Long
    ): Boolean {
        return suspendCancellableCoroutine { cont ->
            val uuid = mIMManager
                .deleteMessages(
                    null,
                    convType,
                    targetId,
                    false,
                    msgId,
                    object : MsgDeletedCallback {
                        override fun onLocalResult(result: Boolean) {
                            cont.resumeWith(Result.success(result))
                        }

                        override fun onRemoteResult(
                            errorType: Int,
                            errorCode: Int,
                            errorMsg: String?
                        ) {
                        }
                    })
            cancelWhenComplete(cont.context.job, uuid)
        }
    }

    private fun cancelWhenComplete(job: Job, uuid: String?) {
        if (uuid != null) {
            job.invokeOnCompletion {
                mIMManager.cancel(uuid)
            }
        }
    }

    fun deleteMessages(
        lifecycleOwner: LifecycleOwner?,
        convType: IM5ConversationType,
        targetId: String,
        deleteRemote: Boolean,
        vararg msgId: Long,
        onLocalResult: OneParamCallback<Boolean>? = null,
        onRemoteResult: OneParamCallback<IMErrorInfo>? = null
    ) {
        mIMManager
            .deleteMessages(
                lifecycleOwner,
                convType,
                targetId,
                deleteRemote,
                msgId,
                object : MsgDeletedCallback {
                    override fun onLocalResult(result: Boolean) {
                        onLocalResult?.invoke(result)
                    }

                    override fun onRemoteResult(errorType: Int, errorCode: Int, errorMsg: String?) {
                        onRemoteResult?.invoke(IMErrorInfo(errorType, errorCode, errorMsg))
                    }
                })
    }

    fun clearMessages(
        lifecycleOwner: LifecycleOwner?,
        convType: IM5ConversationType,
        targetId: String,
        deleteRemote: Boolean,
        onLocalResult: OneParamCallback<Boolean>? = null,
        onRemoteResult: OneParamCallback<IMErrorInfo>? = null
    ) {
        mIMManager
            .clearMessages(
                lifecycleOwner,
                convType,
                targetId,
                deleteRemote,
                object : MsgDeletedCallback {
                    override fun onLocalResult(result: Boolean) {
                        onLocalResult?.invoke(result)
                    }

                    override fun onRemoteResult(errorType: Int, errorCode: Int, errorMsg: String?) {
                        onRemoteResult?.invoke(IMErrorInfo(errorType, errorCode, errorMsg))
                    }
                })
    }

    suspend fun clearMessagesWaitLocal(
        convType: IM5ConversationType,
        targetId: String,
        deleteRemote: Boolean,
    ): Boolean {
        return suspendCancellableCoroutine { cont ->
            val uuid = mIMManager
                .clearMessages(
                    null,
                    convType,
                    targetId,
                    deleteRemote,
                    object : MsgDeletedCallback {
                        override fun onLocalResult(result: Boolean) {
                            cont.resumeWith(Result.success(result))
                        }

                        override fun onRemoteResult(
                            errorType: Int,
                            errorCode: Int,
                            errorMsg: String?
                        ) {}
                    })
            if (uuid!= null) {
                cont.context.job.invokeOnCompletion {
                    mIMManager.cancel(uuid)
                }
            }
        }
    }

    private fun doOnDestroy(lifecycleOwner: LifecycleOwner?, block: () -> Unit) {
        lifecycleOwner?.lifecycleScope?.launch(Dispatchers.Main) {
            lifecycleOwner.doOnLifecycle(
                onDestroy = block
            )
        }
    }

    fun getLocalExtraModel(message: IMessage): IMMessageLocalExtra {
        return mIMManager.getLocalExtraModel(message)
    }


    suspend fun updateLocalExtraField(
        message: IMessage,
        updateFieldExtra: IMMessageLocalExtra
    ) {
        mIMManager.updateLocalExtraField(message, updateFieldExtra)
    }

    /**
     * Setting the conversation local extension field overwrites the old data,
     * it will trigger the IM conversation change callback of 'addConversationsObserver'
     */
    fun setConvLocalExtra(
        convType: IM5ConversationType,
        targetId: String,
        localExtra: String
    ) {
        mIMManager.setConvLocalExtra(userLifecycleOwner, convType, targetId, localExtra)
    }

    fun setPlayedMessage(
        convType: IM5ConversationType,
        targetId: String,
        msgId: String
    ) {
        mIMManager.setPlayedMessage(userLifecycleOwner, convType, targetId, msgId)
    }

    private fun reloadMessage(
        msgId: Long,
        convType: IM5ConversationType,
        callback: IM5Observer<IM5Message?>
    ) {
        mIMManager.reloadMessage(userLifecycleOwner, msgId, convType, callback)
    }

    suspend fun checkPrivateSyncResult(lifecycleOwner: LifecycleOwner?): IMErrorInfo? {
        return suspendCancellableCoroutine { continuation ->
            mIMManager.checkPrivateSyncResult(lifecycleOwner, object : CommCallback {
                override fun onSuccess() {
                    continuation.resumeWith(Result.success(null))
                }

                override fun onFail(errorType: Int, errorCode: Int, errorMsg: String?) {
                    continuation.resumeWith(
                        Result.success(IMErrorInfo(errorType, errorCode, errorMsg))
                    )
                }
            })
        }
    }

    fun onPushMessageReceived(serMsgId: Long, iM5ConversationType: IM5ConversationType): Boolean {
        return mIMManager.onPushMessageReceived(serMsgId, iM5ConversationType)
    }

    fun isMessageDisplayed(serMsgId: Long, iM5ConversationType: IM5ConversationType): Boolean {
        return mIMManager.isMessageDisplayed(serMsgId, iM5ConversationType)
    }

    fun checkUrlAvailableOrReload(
        message: IMessage,
        oneParamCallback: OneParamCallback<IMessage?>
    ) {
        if (message.content is MediaMessageContent) {
            val mediaMessageContent = message.content as MediaMessageContent
            val checkPerferredUrl = mediaMessageContent.checkPerferredUrl()
            if (checkPerferredUrl == IM5CheckPerferredUrl.Reload) {
                Logz.tag(TAG)
                    .w("message[msgId:${message.msgId}, url:${mediaMessageContent.url}] checkUrlAvailableOrReload result is need to reload message")
                reloadMessage(
                    message.msgId,
                    message.conversationType,
                    object : IM5Observer<IM5Message?> {
                        override fun onEvent(msg: IM5Message?) {
                            Logz.tag(TAG)
                                .i("message[msgId:${msg?.msgId}, url:${(msg?.content as MediaMessageContent).remoteUrl}] checkUrlAvailableOrReload reload message successful")
                            message.content = msg.content
                            oneParamCallback.invoke(msg)
                        }

                        override fun onError(errorType: Int, errorCode: Int, errorMsg: String?) {
                            oneParamCallback(null)
                        }
                    })
            } else {
                oneParamCallback.invoke(message)
            }
        } else {
            oneParamCallback.invoke(null)
        }
    }

    fun addNetworkChangeListener(
        lifecycleOwner: LifecycleOwner,
        callback: OneParamCallback<Boolean>
    ) {
        mIMManager.addNetworkChangeListener(lifecycleOwner, callback)
    }

    suspend fun getLastReadMessageSync(
        lifecycleOwner: LifecycleOwner?,
        convType: IM5ConversationType,
        targetId: String
    ): IMessage? {
        return suspendCancellableCoroutine { continuation ->
            mIMManager
                .getLastReadMessage(
                    lifecycleOwner,
                    convType,
                    targetId,
                    object : IM5Observer<IMessage?> {
                        override fun onEvent(msg: IMessage?) {
                            log(TAG, "getLastReadMessage return")
                            continuation.resumeWith(Result.success(msg))
                        }

                        override fun onError(errorType: Int, errorCode: Int, errorMsg: String?) {
                            log(TAG, "getLastReadMessage error")
                            continuation.resumeWith(Result.success(null))
                        }
                    })
        }
    }

    /**
     * 监听IM服务链接状态和同步消息状态, 状态有:[com.lizhi.im5.sdk.base.IM5ServiceStatus]
     */
    fun addServiceStatusObserver(observer: IM5ServiceStatusObserver) {
        mIMManager.addServiceStatusObserver(observer)
    }

    fun removeServiceStatusObserver(observer: IM5ServiceStatusObserver) {
        mIMManager.removeServiceStatusObserver(observer)
    }

    /**
     * 是否正在同步消息
     */
    fun isLoading(): Boolean = mIMManager.isLoading()

    fun isRoamMessage(message: IMessage): Boolean = mIMManager.filterRoamMessage(message)

    /**
     * 是否IM服务器端连
     */
    fun isServerDisconnected(): Boolean = mIMManager.isServerDisconnected()

    fun addReaction(
        lifecycleOwner: LifecycleOwner?,
        msgId: Long,
        convType: IM5ConversationType,
        reaction: Reaction,
        callback: IM5Observer<IMessage?>
    ) {
        mIMManager.addReaction(lifecycleOwner, msgId, convType, reaction, callback)
    }

    suspend fun addReactionSync(
        lifecycleOwner: LifecycleOwner?,
        msgId: Long,
        convType: IM5ConversationType,
        reaction: Reaction,
    ): Pair<IMessage?, Int?> {
        return suspendCancellableCoroutine { continuation ->
            addReaction(lifecycleOwner, msgId, convType, reaction, object : IM5Observer<IMessage?> {
                override fun onEvent(msg: IMessage?) {
                    continuation.resumeWith(Result.success(msg to null))
                }

                override fun onError(errorType: Int, errorCode: Int, errorMsg: String?) {
                    log(
                        TAG,
                        "addReactionSync onError,errorType${errorType},errorCode:${errorCode},errorMsg:${errorMsg}"
                    )
                    continuation.resumeWith(Result.success(null to errorCode))
                }
            })
        }
    }

    fun removeReaction(
        lifecycleOwner: LifecycleOwner?,
        msgId: Long,
        convType: IM5ConversationType,
        reaction: Reaction,
        callback: IM5Observer<IMessage?>
    ) {
        mIMManager.removeReaction(lifecycleOwner, msgId, convType, reaction, callback)
    }

    suspend fun removeReactionSync(
        lifecycleOwner: LifecycleOwner?,
        msgId: Long,
        convType: IM5ConversationType,
        reaction: Reaction,
    ): Pair<IMessage?, Int?> {
        return suspendCancellableCoroutine { continuation ->
            removeReaction(
                lifecycleOwner,
                msgId,
                convType,
                reaction,
                object : IM5Observer<IMessage?> {
                    override fun onEvent(msg: IMessage?) {
                        continuation.resumeWith(Result.success(msg to null))
                    }

                    override fun onError(errorType: Int, errorCode: Int, errorMsg: String?) {
                        log(
                            TAG,
                            "removeReactionSync onError,errorType${errorType},errorCode:${errorCode},errorMsg:${errorMsg}"
                        )
                        continuation.resumeWith(Result.success(null to errorCode))
                    }
                })
        }
    }

    fun updateReaction(
        lifecycleOwner: LifecycleOwner?,
        msgId: Long,
        convType: IM5ConversationType,
        oldReaction: Reaction,
        newReaction: Reaction,
        callback: IM5Observer<IMessage?>
    ) {
        mIMManager.updateReaction(
            lifecycleOwner,
            msgId,
            convType,
            oldReaction,
            newReaction,
            callback
        )
    }

    suspend fun updateReactionSync(
        lifecycleOwner: LifecycleOwner?,
        msgId: Long,
        convType: IM5ConversationType,
        oldReaction: Reaction,
        newReaction: Reaction,
    ): Pair<IMessage?, Int?> {
        return suspendCancellableCoroutine { continuation ->
            updateReaction(
                lifecycleOwner,
                msgId,
                convType,
                oldReaction,
                newReaction,
                object : IM5Observer<IMessage?> {
                    override fun onEvent(msg: IMessage?) {
                        continuation.resumeWith(Result.success(msg to null))
                    }

                    override fun onError(errorType: Int, errorCode: Int, errorMsg: String?) {
                        log(
                            TAG,
                            "updateReactionSync onError,errorType${errorType},errorCode:${errorCode},errorMsg:${errorMsg}"
                        )
                        continuation.resumeWith(Result.success(null to errorCode))
                    }
                })
        }
    }

    /**
     * @param reason 预留参数，准备视频消息处理失败原因，可用于下次重发是获取使用()，目前压缩失败不处理原因，后续做剪辑等操作可能使用
     * @see IM5VideoMessage.getReason 获取处理失败原因
     */
    fun prepareVideoMessageFailed(
        message: IMessage, reason: String,
        onSuccess: OneParamCallback<IM5Message?>? = null,
        onError: ThreeParamCallback<Int, Int, String?>? = null
    ) {
        mIMManager.prepareVideoMessageFailed(
            userLifecycleOwner,
            message,
            reason,
            object : IM5Observer<IM5Message> {
                override fun onEvent(message: IM5Message?) {
                    onSuccess?.invoke(message)
                }

                override fun onError(errType: Int, errCode: Int, errMsg: String?) {
                    onError?.invoke(errType, errCode, errMsg)
                }
            })
    }

    suspend fun cancelSendingMessage(
        convType: IM5ConversationType,
        msgId: Long
    ): Pair<IM5Message?, IMErrorInfo?> {
        return mIMManager.cancelSendingMessage(convType, msgId)
    }

    /**
     * 获取IM媒体缓存(用户维度)
     * @param cacheType 缓存类型，IM5CacheType
     */
    suspend fun getMediaCache(cacheType: Int): Long {
        return suspendCancellableCoroutine { continuation ->
            mIMManager.getMediaCache(userLifecycleOwner, cacheType) { size ->
                continuation.resumeWith(Result.success(size))
            }
        }
    }

    /**
     * 获取IM媒体缓存(会话维度)
     * @param cacheType 缓存类型，IM5CacheType
     */
    suspend fun getCacheByTargetId(
        targetId: String,
        convType: IM5ConversationType,
        cacheType: Int
    ): Long {
        return suspendCancellableCoroutine { continuation ->
            mIMManager.getCacheByTargetId(
                userLifecycleOwner,
                targetId,
                convType,
                cacheType
            ) { size ->
                continuation.resumeWith(Result.success(size))
            }
        }
    }

    fun removeCache(cacheType: Int, beforeTimeStamp: Long = Long.MAX_VALUE) {
        mIMManager.removeCache(userLifecycleOwner, beforeTimeStamp, cacheType)
    }

    fun removeCacheByTargetId(targetId: String, convType: IM5ConversationType, cacheType: Int) {
        mIMManager.removeCacheByTargetId(userLifecycleOwner, targetId, convType, cacheType)
    }

    fun pauseUploadMediaMessageByConversation(targetId: String, convType: IM5ConversationType) {
        mIMManager.pauseUploadMediaMessageByConversation(userLifecycleOwner, targetId, convType)
    }

    suspend fun recallMessageSync(
        lifecycleOwner: LifecycleOwner?,
        convType: IM5ConversationType,
        msgId: Long,
        pushContent: String,
        pushPayLoad: String,
        isKeepOriginalContent: Boolean = false
    ): Pair<IMessage?, IMErrorInfo?> {
        return suspendCancellableCoroutine { continuation ->
            mIMManager.recallMessage(
                lifecycleOwner,
                convType,
                msgId,
                pushContent,
                pushPayLoad,
                isKeepOriginalContent,
                object : IM5Observer<IMessage> {
                    override fun onEvent(msg: IMessage?) {
                        continuation.resumeWith(Result.success(msg to null))
                    }

                    override fun onError(errorType: Int, errorCode: Int, errorMsg: String?) {
                        continuation.resumeWith(
                            Result.success(
                                null to IMErrorInfo(
                                    errorType,
                                    errorCode,
                                    errorMsg
                                )
                            )
                        )
                    }
                })
        }
    }

    suspend fun sendOnlineSoundBoardEmoji(
        targetId: String,
        soundBoardId: Long,
        displayType: Int,
        displayValue: String,
        voiceUrl: String,
        superscript: String = "",
    ): IMErrorInfo? {
        val content = SoundBoardEmojiMsg().apply {
            this.soundBoardId = soundBoardId
            this.displayType = displayType
            this.displayValue = displayValue
            this.voiceUrl = voiceUrl
            this.superscript = superscript
        }
        val message = IM5Message.obtain(targetId, IM5ConversationType.CHANNEL, content)
        message.setDefaultUserInfo()
        return mIMManager.sendPrivateOnlineMessage(message)
    }

    suspend fun sendOnlineOnAirGift(
        targetId: String,
        giftList: List<OnAirGiftInfo>
    ): IMErrorInfo? {
        val content = OnAirGiftMsg().apply {
            this.giftList = giftList
        }
        val message = IM5Message.obtain(targetId, IM5ConversationType.CHANNEL, content)
        message.setDefaultUserInfo()
        return  mIMManager.sendPrivateOnlineMessage(message)
    }

    fun obtainISignalManagerPresenter(): ISignalManagerPresenter {
        return mIMManager.obtainISignalManagerPresenter()
    }

    fun cancelPreviewVoiceFilter(previewId: String?) {
        previewId?.let {
            mIMManager.cancelPreviewVoiceFilter(previewId)
        }
    }

    fun updateConversationTime(param: IM5ConversationUpdateTimeParam) {
        userLifecycleScope?.launchIO {
            mIMManager.updateConversationTime(param)
        }
    }

    fun updateMultipleConversationsTime(paramList: List<IM5ConversationUpdateTimeParam>) {
        if (paramList.isEmpty()) return
        userLifecycleScope?.launchIO {
            mIMManager.updateMultipleConversationsTime(paramList)
        }
    }


    suspend fun loadUnreadHistoryMessage(
        convType: IM5ConversationType,
        targetId: String,
        count: Int
    ): IMResult<UnreadData> {
        return mIMManager.loadUnreadHistoryMessage(convType, targetId, count)
    }


    fun leaveChannel(channelId: String) = mIMManager.leaveChannel(channelId)

    fun enterChannel(channelId: String, heartInterval: Long) = mIMManager.enterChannel(channelId,heartInterval)


}