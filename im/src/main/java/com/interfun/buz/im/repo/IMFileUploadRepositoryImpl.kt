package com.interfun.buz.im.repo

import android.content.Intent
import android.net.Uri
import android.provider.OpenableColumns
import com.interfun.buz.base.ktx.*
import com.interfun.buz.base.utils.FileUtil
import kotlinx.coroutines.NonCancellable
import kotlinx.coroutines.ensureActive
import kotlinx.coroutines.isActive
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream
import java.io.OutputStream
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject
import kotlin.coroutines.coroutineContext


/**
 * Author: ChenYouSheng
 * Date: 2025/6/6
 * Email: <EMAIL>
 * Desc:
 */
class IMFileUploadRepositoryImpl @Inject constructor(
) : IMFileUploadRepository {

    companion object {
        private const val TAG = "IMFileUploadRepositoryImpl"
        private const val UPLOAD_DIR_NAME = "buz_upload"
        private const val TEMP_FILE_PREFIX = "temp_file_"
        private const val DEFAULT_BUFFER_SIZE = 512 * 1024
    }

    private val copyTaskMap = ConcurrentHashMap<Uri, String>()

    private val uploadFolder: File
        get() = FileUtil.getFilesDir(appContext, UPLOAD_DIR_NAME, external = false).apply {
            if (!exists()) {
                mkdirs()
            }
        }

    init {
        cleanUploadFiles()
    }

    override suspend fun singleCopySync(uri: Uri): CopyResult {
        val isActive = coroutineContext.isActive
        if (!isActive) {
            logDebug(TAG, "copyJob is not active, skipping copy for uri: $uri")
            return CopyResult.CopyError(uri =uri,  errorMsg = "copyJob is not active")
        }
        val fileName = getFileName(uri)
        try {
            contentResolver.takePersistableUriPermission(
                uri, Intent.FLAG_GRANT_READ_URI_PERMISSION
            )
        } catch (e: SecurityException) {
            logDebug(TAG, "Failed to take persistable permission for uri: $uri")
        }
        logInfo(TAG, "copyUriToCacheFile [START]  =====> fileName=${fileName}, uri=$uri")
        val cacheFile = File(uploadFolder, fileName)

        // 记录正在拷贝的uri 和 缓存文件的路径
        copyTaskMap.put(uri, cacheFile.absolutePath)

        val startTime = System.currentTimeMillis()
        val result = try {
            contentResolver.openInputStream(uri)?.use { inputStream ->
                FileOutputStream(cacheFile).use { outputStream ->
                    inputStream.copyTo(outputStream)
                }
            }
            CopyResult.CopySuccess(uri = uri, path = cacheFile.absolutePath)
        } catch (e: Exception) {
            logError(TAG, e, "copyFileToCacheFile error: ${e.message}")
            // 清理临时文件
            withContext(NonCancellable) {
                val success = cacheFile.delete()
                logInfo(TAG, "cleanUploadFileByUri file=${cacheFile.name} delete=${success}")
            }
            CopyResult.CopyError(uri =uri,  errorMsg = e.message)
        }
        val spendTime = System.currentTimeMillis() - startTime
        logInfo(
            TAG,
            "copyUriToCacheFile [DONE]  =====> Success=${cacheFile.exists()}, path=${result.path}, " +
                    "name=$fileName, spendTime=${spendTime / 1000L}s"
        )
        if (cacheFile.exists().not()) {
            return CopyResult.CopyError(
                uri = uri,
                errorMsg = "file not found",
                path = cacheFile.absolutePath
            )
        }
        return result
    }

    private suspend fun InputStream.copyTo(
        out: OutputStream, bufferSize: Int = DEFAULT_BUFFER_SIZE
    ): Long {
        var bytesCopied: Long = 0
        val buffer = ByteArray(bufferSize)
        var bytes = read(buffer)
        while (bytes >= 0) {
            coroutineContext.ensureActive()
            out.write(buffer, 0, bytes)
            bytesCopied += bytes
            bytes = read(buffer)
        }
        return bytesCopied
    }


    override fun cleanUploadFiles() {
        copyTaskMap.clear()
        uploadFolder.listFiles()?.forEach { file ->
            val success = file.delete()
            logInfo(TAG, "cleanUploadFiles file=${file.name} delete=${success}")
        }
    }

    override fun cleanUploadFileByUri(uri: Uri) {
        copyTaskMap.remove(uri)?.let { path ->
            val file = File(path)
            val success = file.delete()
            logInfo(TAG, "cleanUploadFileByUri file=${file.name} delete=${success}")
        }
    }


    private fun getFileName(uri: Uri): String {
        // Try from content resolver
        try {
            val cursor = contentResolver.query(uri, null, null, null, null)
            cursor?.use {
                val nameIndex = it.getColumnIndex(OpenableColumns.DISPLAY_NAME)
                if (it.moveToFirst() && nameIndex != -1) {
                    val displayName = it.getString(nameIndex)
                    if (!displayName.isNullOrBlank()) {
                        return displayName
                    }
                }
            }
        } catch (e: Exception) {
            logError(TAG, e, "Failed to get file name from content resolver for uri: $uri")
        }
        // Try from uri path
        uri.path?.let { path ->
            val lastSegment = path.substringAfterLast('/', "")
            if (lastSegment.isNotBlank()) {
                return lastSegment
            }
        }
        // Fallback to default name
        return "${TEMP_FILE_PREFIX}${System.currentTimeMillis()}"
    }

}