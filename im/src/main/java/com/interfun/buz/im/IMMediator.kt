package com.interfun.buz.im

import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.component.hilt.GlobalQualifier
import com.interfun.buz.component.hilt.UserComponentManager
import com.interfun.buz.im.di.IMSingletonEntryPoint
import com.interfun.buz.im.di.IMUserEntryPoint
import com.interfun.buz.im.repo.IMFileDownloadRepository
import dagger.hilt.EntryPoints
import dagger.hilt.android.EntryPointAccessors
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class IMMediator @Inject constructor(
    private val userComponentManager: UserComponentManager,
    @GlobalQualifier private val globalScope: CoroutineScope
) {

    companion object {

        /**
         * 用于解决登录前和登录后获取到的IMFileDownloadRepository不一致的问题，
         * 例如[NotificationManager]的init方法内如果直接使用getIMSingletonEntryPoint获取，那么登录前得到的和登录后得到的是不一样的。
         * 因为[NotificationManager]是单例对象，所以init方法只会执行一次。
         */
        private val _fileDownloadRepositoryFlow = MutableStateFlow<IMFileDownloadRepository?>(null)
        val fileDownloadRepositoryFlow: StateFlow<IMFileDownloadRepository?> = _fileDownloadRepositoryFlow.asStateFlow()

        /**
         * 获取IMSingletonEntryPoint，然后可用它间接获取定义在IMSingletonModule的依赖
         */
        fun getIMSingletonEntryPoint(): IMSingletonEntryPoint {
            return EntryPointAccessors.fromApplication<IMSingletonEntryPoint>(appContext)
        }
    }


    fun init() {
        globalScope.launch {
            userComponentManager.getUserComponentFlow().collect { userComponent ->
                EntryPoints.get(userComponent, IMUserEntryPoint::class.java).getIMUserEntry().init()

                val newRepo = EntryPoints.get(userComponent, IMUserEntryPoint::class.java).fileDownloadRepository()
                _fileDownloadRepositoryFlow.value = newRepo
            }
        }
    }
}