package com.interfun.buz.im.di

import com.interfun.buz.im.repo.IMFileDownloadRepository
import com.interfun.buz.im.repo.IMFileUploadRepository
import dagger.hilt.EntryPoint
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@EntryPoint
@InstallIn(SingletonComponent::class)
interface IMSingletonEntryPoint {

    fun fileDownloadRepository(): IMFileDownloadRepository

    fun fileUploadRepository(): IMFileUploadRepository
}