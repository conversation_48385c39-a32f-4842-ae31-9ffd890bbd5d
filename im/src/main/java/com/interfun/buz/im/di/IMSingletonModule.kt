package com.interfun.buz.im.di

import com.interfun.buz.component.hilt.UserComponent
import com.interfun.buz.im.IMAgent
import com.interfun.buz.im.repo.IMFileDownloadRepository
import com.interfun.buz.im.repo.IMFileUploadRepository
import com.interfun.buz.im.repo.MessageSendStateRepository
import com.interfun.buz.im.repo.MessageSendStateRepositoryImpl
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.EntryPoints
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
class IMSingletonModule {

    @Provides
    fun providerIMFileDownloadRepository(userComponent: UserComponent): IMFileDownloadRepository {
        return EntryPoints.get(userComponent, IMUserEntryPoint::class.java).fileDownloadRepository()
    }

    @Provides
    fun providerIMFileUploadRepository(userComponent: UserComponent): IMFileUploadRepository {
        return EntryPoints.get(userComponent, IMUserEntryPoint::class.java).fileUploadRepository()
    }

    @Provides
    fun provideIMAgent(): IMAgent {
        return IMAgent
    }
}

@Module
@InstallIn(SingletonComponent::class)
internal interface IMSingletonModuleInterface {
    @Binds
    fun bindMessageSendStateRepository(impl: MessageSendStateRepositoryImpl): MessageSendStateRepository
}