[versions]

# AGP and tools should be updated together
androidGradlePlugin = "8.7.3"
androidTools = "31.7.3"
kotlin = "2.1.0"
ksp = "2.1.0-1.0.29"
room = "2.6.1"
truth = "1.4.4"

[bundles]

[libraries]

truth = { group = "com.google.truth", name = "truth", version.ref = "truth" }
# Dependencies of the included build-logic
android-gradlePlugin = { group = "com.android.tools.build", name = "gradle", version.ref = "androidGradlePlugin" }
android-tools-common = { group = "com.android.tools", name = "common", version.ref = "androidTools" }
compose-gradlePlugin = { module = "org.jetbrains.kotlin:compose-compiler-gradle-plugin", version.ref = "kotlin" }
kotlin-gradlePlugin = { group = "org.jetbrains.kotlin", name = "kotlin-gradle-plugin", version.ref = "kotlin" }
ksp-gradlePlugin = { group = "com.google.devtools.ksp", name = "com.google.devtools.ksp.gradle.plugin", version.ref = "ksp" }
room-gradlePlugin = { group = "androidx.room", name = "room-gradle-plugin", version.ref = "room" }

[plugins]

# Plugins defined by this project
buz-android-i18n = { id = "buz.android.i18n" }

