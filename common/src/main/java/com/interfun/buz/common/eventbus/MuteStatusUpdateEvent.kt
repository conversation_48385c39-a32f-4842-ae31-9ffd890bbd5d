package com.interfun.buz.common.eventbus

import com.interfun.buz.common.refactor.ShouldDeleteAfterRefactoring
import com.jeremyliao.liveeventbus.LiveEventBus

/**
 * <AUTHOR>
 * @date 2023/4/19
 * @desc mute friend\group messages event
 */
@ShouldDeleteAfterRefactoring("重构完成后没有引用了可以移除")
@Deprecated("新代码请不要使用这个，直接用GroupRepository或者UserRepository拿用户或者群的mute信息flow即可")
class MuteStatusUpdateEvent(val targetId: Long) : BaseEvent() {

    companion object {
        fun post(targetId: Long) {
            LiveEventBus.get(MuteStatusUpdateEvent::class.java)
                .post(MuteStatusUpdateEvent(targetId))
        }
    }
}