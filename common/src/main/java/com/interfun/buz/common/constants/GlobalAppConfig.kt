package com.interfun.buz.common.constants

import android.text.TextUtils
import com.interfun.buz.common.utils.BuzChannelUtil
import com.ishumei.smantifraud.SmAntiFraud
import com.lizhi.component.itnet.myip.data.LocationBean
import com.yibasan.lizhifm.sdk.platformtools.MobileUtils

/** App ID */
const val APP_ID = 87075309
const val appId = "87075309"

/** 业务数据库名 */
const val APP_DATABASE_NAME = "buz_database"
/**
 * Prefix of database name for users.Each of users have an individual database.
 */
const val USER_DATABASE_NAME_PREFIX = "buz_user_database_"

/** 获取渠道id */
val channelId: String by lazy { BuzChannelUtil.getChannelId() }

/**
 * 获取渠道市场对应的key
 */
val channelAppKey: String by lazy { BuzChannelUtil.getMarketKey() }

/** 归因的媒体渠道，通常来源于广告推广，Adjust/AF归因 */
var mediaChannel: String? = null

/** 获取设备id */
val deviceId: String get() = MobileUtils.getDeviceId()

/**
 * 获取数美SDK的设备id
 */
val SMDeviceId: String get() = if(TextUtils.isEmpty(SmAntiFraud.getDeviceId())) "" else SmAntiFraud.getDeviceId()

/** MyIp信息 在网络库初始化时候异步请求进行赋值*/
var appLocationModel: LocationBean? = null