package com.interfun.buz.common.htmlparser

import android.graphics.Color
import android.text.Editable
import android.text.Spanned
import android.text.style.AbsoluteSizeSpan
import android.text.style.ForegroundColorSpan
import com.interfun.buz.base.ktx.asColor
import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.base.ktx.logError
import com.interfun.buz.common.R
import com.interfun.buz.common.ktx.getIntDefault
import com.interfun.buz.common.utils.AutoLinkSpan
import org.xml.sax.Attributes
import java.util.Stack

class CustomerTagHandler(private val linkActionCallback: AutoLinkSpan.LinkActionCallback?) :
    HtmlParser.TagHandler {
    companion object {
        const val TAG = "CustomerTagHandler"
    }

    /**
     * html 标签的开始下标
     */
    private var startIndex: Stack<Int> = Stack()

    /**
     * html的标签的属性值 value，如:<size value='16'></size>
     */
    private var propertyValue: HashMap<Int, Map<String, String>> = HashMap()

    override fun handleTag(
        opening: Boolean,
        tag: String?,
        output: Editable?,
        attributes: Attributes?
    ): Boolean {
        return try {
            if (opening) {
                handlerStartTAG(tag, output, attributes)
            } else {
                handlerEndTAG(tag, output)
            }
        } catch (e: Exception) {
            logError(
                TAG, "handleTag==>tag=${tag},opening=${opening}," +
                        "output=${output},error=${e.message}"
            )
            false
        }
    }

    private fun handlerStartTAG(
        tag: String?,
        output: Editable?,
        attributes: Attributes?
    ): Boolean {
        if (tag.equals("font", ignoreCase = true)
            || tag.equals("a", ignoreCase = true)
        ) {
            val index = output?.length.getIntDefault()
            startIndex.push(index)
            if (null != attributes) {
                val propertyMap = HashMap<String, String>()
                val style = HtmlParser.getValue(attributes, "style")
                val href = HtmlParser.getValue(attributes, "href")
                if (null != style) {
                    val styleMap = getPropertyMap(style)
                    propertyMap.putAll(styleMap)
                }
                if (null != href) {
                    propertyMap["href"] = href
                }

                propertyValue[index] = propertyMap
                logDebug(
                    TAG, "handlerStartTAG===>startIndex:size = ${startIndex.size}," +
                            "propertyValue:size=${propertyValue.size}"
                )
            }
            return true
        }
        return false
    }

    private fun handlerEndTAG(tag: String?, output: Editable?): Boolean {
        logDebug(TAG, "handlerEndTAG===>tag=$tag")
        // font 标签处理
        if (tag.equals("font", ignoreCase = true)) {
            if (propertyValue.isEmpty().not()) {
                val startIndex = startIndex.pop()
                val propertyMap = propertyValue.remove(startIndex)
                if (propertyMap?.containsKey("color") == true) {
                    output?.setSpan(
                        ForegroundColorSpan(Color.parseColor(propertyMap["color"])),
                        startIndex,
                        output.length,
                        Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                }
                if (propertyMap?.containsKey("font-size") == true) {
                    output?.setSpan(
                        propertyMap["font-size"]?.toInt()?.let {
                            AbsoluteSizeSpan(
                                it,
                                true
                            )
                        }, startIndex, output.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                }
            }
            return true
        }

        // a 标签处理
        if (tag.equals("a", ignoreCase = true)) {
            if (propertyValue.isEmpty().not()) {
                val startIndex = startIndex.pop()
                val propertyMap = propertyValue.remove(startIndex)
                val color = propertyMap?.get("color")?.let {
                    Color.parseColor(it)
                } ?: R.color.text_white_secondary
                val url = propertyMap?.get("href")

                if (null != url) {
                    val clickableSpan = AutoLinkSpan(
                        url = url.toString(),
                        color = color,
                        highlightLinkColor = R.color.overlay_white_14.asColor(),
                        useUnderLine = false,
                        linkActionCallback = linkActionCallback
                    )
                    output?.setSpan(
                        clickableSpan,
                        startIndex, output.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                }

                if (propertyMap?.containsKey("font-size") == true) {
                    output?.setSpan(
                        propertyMap["font-size"]?.toInt()?.let {
                            AbsoluteSizeSpan(
                                it,
                                true
                            )
                        }, startIndex, output.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                }
            }
            return true
        }
        return false
    }

    private fun getPropertyMap(propertyValue: String): Map<String, String> {
        val styleMap = propertyValue.split(";").mapNotNull {
            val value = it.split(":")
            if (value.size == 2) {
                value[0] to value[1].replace("px", "").trim()
            } else null
        }.toMap()
        logDebug(TAG, "propertyValue=${propertyValue}")
        return styleMap
    }
}