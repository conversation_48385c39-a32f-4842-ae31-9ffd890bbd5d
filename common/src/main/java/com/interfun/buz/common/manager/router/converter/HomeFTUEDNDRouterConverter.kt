package com.interfun.buz.common.manager.router.converter

import android.os.Bundle
import com.interfun.buz.common.constants.PATH_CHAT_ACTIVITY_HOME
import com.interfun.buz.common.constants.RouterParamKey
import com.interfun.buz.common.constants.RouterSchemes
import org.json.JSONObject

data class DNDEnableRouterArgs(val enable: Boolean)

class HomeFTUEDNDRouterConverter: RouterConverter<DNDEnableRouterArgs> {

    override val scheme: String
        get() = RouterSchemes.Guide.HOME_FTUE_OFFICIAL_DND

    override val path: String
        get() = PATH_CHAT_ACTIVITY_HOME

    override fun convertToBundle(extraData: JSONObject?, result: Bundle): Boolean {
        if (extraData == null) {
            return false
        }
        result.putBoolean(RouterParamKey.ChatHome.KEY_ENABLE_DND,true)
        return true
    }

    override fun convertToExtraData(args: DNDEnableRouterArgs): JSONObject {
        return JSONObject().apply {
            put(RouterParamKey.ChatHome.KEY_ENABLE_DND, args.enable)
        }
    }


}
