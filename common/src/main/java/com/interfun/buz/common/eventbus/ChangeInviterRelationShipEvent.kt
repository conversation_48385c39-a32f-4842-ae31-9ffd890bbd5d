package com.interfun.buz.common.eventbus

import com.interfun.buz.base.ktx.logInfo
import com.jeremyliao.liveeventbus.LiveEventBus

/**
 * @Desc change inviter relationship to be "send request" status
 * @Author:yangmulin
 * @Date: 2023/11/3
 */
class ChangeInviterRelationShipEvent(val inviterId: Long?, val type: Int?,val linkHash: String?) :
    BaseEvent() {

    companion object {
        fun post(inviterId: Long?, type: Int?, linkHash: String? = null) {
            logInfo("ChangeInviterRelationShipEvent", "post inviterId = $inviterId,type = $type,linkHash = $linkHash")
            LiveEventBus.get(ChangeInviterRelationShipEvent::class.java)
                .post(ChangeInviterRelationShipEvent(inviterId, type, linkHash))
        }
    }
}