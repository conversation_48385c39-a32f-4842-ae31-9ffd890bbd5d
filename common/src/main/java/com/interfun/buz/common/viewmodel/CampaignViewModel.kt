package com.interfun.buz.common.viewmodel

import androidx.lifecycle.MutableLiveData
import com.interfun.buz.common.net.withConfig
import androidx.lifecycle.ViewModel
import com.buz.idl.common.bean.ActivityConfig
import com.buz.idl.common.request.RequestGetActivityConfig
import com.buz.idl.common.service.BuzNetCommonServiceClient
import com.interfun.buz.base.ktx.launchIO
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.common.ktx.isSuccess
import com.interfun.buz.common.utils.parse
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import org.libpag.PAGFile

object CampaignViewModel : ViewModel() {
    private val TAG = "CampaignViewModel"
    val activityConfig = MutableLiveData<ActivityConfig?>()
    val activityConfigValue: ActivityConfig?
        get() = activityConfig.value

    /**
     * 指示当前正在显示红点
     */
    val showHomeRed = MutableLiveData<Boolean>()
    private val service by lazy { BuzNetCommonServiceClient().withConfig() }
    private val mutex = Mutex()

    fun getActivityConfig() {
        launchIO {
            //证明之前的请求还在
            if (mutex.isLocked) {
                return@launchIO
            }
            mutex.withLock {
                val request = RequestGetActivityConfig()
                val response = service.getActivityConfig(request)
                logInfo(TAG, "getActivityConfig:response ${response.code}")
                if (!response.isSuccess) {
                    response.data?.prompt?.parse()
                    return@launchIO
                }
                activityConfig.postValue(response.data?.activityConfig)
            }
        }
    }

}
