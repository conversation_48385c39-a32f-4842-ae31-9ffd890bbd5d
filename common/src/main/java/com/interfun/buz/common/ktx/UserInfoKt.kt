package com.interfun.buz.common.ktx

import com.buz.idl.user.bean.OfficialAccountUserInfo
import com.buz.idl.user.bean.UserInfo
import com.buz.idl.user.bean.UserRelation
import com.interfun.buz.common.database.entity.UserRelationInfo
import com.interfun.buz.common.database.entity.UserStatus
import org.json.JSONObject

fun UserInfo.toJsonObject(): JSONObject {
    val json = JSONObject()
    json.putOpt("firstName", firstName)
    json.putOpt("lastName", lastName)
    json.putOpt("userName", userName)
    json.putOpt("portrait", portrait)
    json.putOpt("userId", userId)
    json.putOpt("phone", phone)
    json.putOpt("registerTime", registerTime)
    json.putOpt("walkieTalkieOnlineTime", walkieTalkieOnlineTime)
    json.putOpt("quietMode", quietMode)
    return json
}

/**
 * Simplified userInfo into JsonObject object,
 * only retaining fields with 'userId', 'userName', 'portrait'
 * Usage scenario: for PushPayload user info Simplified
 * [In order to solve the problem of 'PayloadTooLarge', simplify the carrying information of User Info]
 */
fun UserInfo.simpleToJsonObject(needPortrait: Boolean = true): JSONObject {
    return JSONObject().apply {
        putOpt("userId", userId)
        putOpt("userName", userName)
        if (needPortrait){
            putOpt("portrait", portrait)
        }
    }
}

fun UserInfo.toJson(): String {
    return toJsonObject().toString()
}

val UserInfo.shortName: String? get() = firstName.takeIf { !it.isNullOrEmpty() } ?: userName

val UserInfo?.isOnline: Boolean get() = (this?.walkieTalkieOnlineTime ?: 0L) > 0

val UserInfo?.isQuietModeEnable: Boolean get() = (this?.quietMode ?: 0) == 2

object UserInfoKt {
    fun fromJson(jsonObject: JSONObject): UserInfo {
        val userId = jsonObject.optLong("userId")
        val phone = jsonObject.optString("phone")
        val firstName = jsonObject.optString("firstName")
        val lastName = jsonObject.optString("lastName")
        val userName = jsonObject.optString("userName")
        val portrait = jsonObject.optString("portrait")
        val registerTime = jsonObject.optLong("registerTime")
        val walkieTalkieOnlineTime = jsonObject.optLong("walkieTalkieOnlineTime")
        val quietMode = jsonObject.optInt("quietMode")
        val userType = jsonObject.optInt("userType")
        val buzId = jsonObject.optString("buzId")
        val userEmail = jsonObject.optString("email")
        val language = jsonObject.optString("language")
        val userStatus = jsonObject.optInt("userStatus",UserStatus.STATUS_NORMAL)
        val imUin = jsonObject.optLong("imUin")
        val latestActiveTime = jsonObject.optLong("latestActiveTime")
        return UserInfo(
            userId,
            userName,
            firstName,
            lastName,
            portrait,
            phone,
            registerTime,
            walkieTalkieOnlineTime,
            quietMode,
            userType,
            buzId = buzId,
            email = userEmail,
            language = language,
            userStatus = userStatus,
            imUin = imUin,
            latestActiveTime = latestActiveTime
        )
    }

    fun fromJsonOrNull(jsonObject: JSONObject?): UserInfo? {
        return if (jsonObject == null) {
            null
        } else {
            fromJson(jsonObject)
        }
    }
}

/*fun UserInfo.toContactUserInfo(): ContactUserInfo {
    return ContactUserInfo(
        id = 0,
        firstLetter = PinyinUtils.getNameFirstLetter(this.userName ?: ""),
        phone = this.phone ?: "",
        userId = this.userId ?: 0L,
        userName = this.userName,
        firstName = this.firstName,
        userFirstName = this.firstName,
        lastName = this.lastName,
        userLastName = this.lastName,
        portrait = this.portrait,
        registerTime = this.registerTime
    )
}*/

fun UserInfo.toConvertUserRelationInfo(userRelation: UserRelation?): UserRelationInfo {
    return convertUserRelationInfo(this, userRelation)
}

