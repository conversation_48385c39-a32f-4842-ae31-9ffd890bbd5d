package com.interfun.buz.common.constants

import android.os.Parcelable
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.bean.push.PushGoodBye.LogoutReason
import com.interfun.buz.common.ktx.*
import com.interfun.buz.common.manager.user.FriendRequestCount
import com.interfun.buz.common.widget.portrait.group.fetcher.GroupPortraitByInfoFetcher.GroupPortraitDiskCacheKeyMap
import kotlinx.parcelize.Parcelize

/**
 * MMKV常量记录类，请保持规范：以模块名作为开头、避免重名
 * 使用方式：
 * 1. 通过by mmkv...的属性委托方式添加及使用，使用驼峰式命名
 * 2. 添加const val String作为Key，手动调用mmkv.encode\decode，规范为全大写+下划线
 *
 * 注意事项：使用委托属性方式，需要对MMKVOwner的module子类做防混淆的keep
 */
object CommonMMKV : MMKVOwner {

    /**----------------- 常量区域 ------------------**/

    const val COMMON_KEY_RTP_AB_TEST_BY_UID = "COMMON_KEY_RTP_AB_TEST_BY_"

    /**--------------- 委托属性区域 -----------------**/

    var friendRequestCount by userMMKVParcelable<FriendRequestCount>()

    var firstStartAppTime by mmkvLong()

    var isFeedbackDialogShowed by mmkvBool()

    var isBuzResearchDialogShow by mmkvBool()

    var firstCheckBuzResearchDialogTime by mmkvLong()

    var wtSendMsgCount by mmkvInt()

    var showNotificationInThisVersion by mmkvBool(false)

    var showRealTimeCallDialogInThisVersion by mmkvBool(true)

    var showRealTimeCallDialog by mmkvBool(true)

    var showAsrIntroduceDialogState by mmkvInt(-1)

    var isRealTimeCallDialogShowed by mmkvBool(false)

    var checkShowNewFeatureNotification by mmkvBool(false)

    var enableNotificationDialogShowTime by mmkvLong(0L)

    var NotificationProblemDialogShowTime by mmkvLong(0L)

    var NotificationProblemDialogShowTotal by mmkvInt(0)

    var NotificationProblemDialogIsShow by mmkvBool(false)

    var receiveMsgAudioType by mmkvInt(0)

    //true means enable quiet mode. Otherwise disable.
    @Deprecated("use getQuiteMode in userSettingRepository")
    var isQuietModeEnable by userMMKVBool(false)

    var hadChangedQuietMode by mmkvBool(false)

    var notShowAutoBindIPTagDialog by mmkvBool()

    var recentOpenBlindBoxTime by userMMKVString("")

    var notificationAllowSetting by mmkvBool()

    var batteryOptimizeAllowSetting by mmkvBool()

    var notificationProblemTrackerStartUp by mmkvBool(true)

    /**
     * cache the language setting of app
     */
    var appLanguageSetting by mmkvString()

    /**
     * cache the dialog list config
     */
    var appDialogConfig by mmkvString()

    // 首页的通讯录入口需要展示好友请求未读数，点击后消失，但并没有记录为已读，后续进入首页还是会加载出这个数字
    // 将其记录，判断只有数字跟上次不同时，才会重新显示
    var lastHomePageShowRequestCount by userMMKVInt()

    /**
     * check bitmap cache flag
     */
    var isOpenBitmapCheck by mmkvBool()

    var doKitQuickLoginPhone by mmkvString()

    var abTest by userMMKVString()

    var abTestBeforeLogin by mmkvString()

    //count for counting notifications that delay to show because it's fetching data
    var pendingNotificationCount by mmkvInt()

    // https://lizhi2021.feishu.cn/wiki/TtRewpKfXiydWykz6VXcB33En1T
    var isFeedbackEntryDotVisible by userMMKVBool()
    var isFeedbackLogDotVisible by userMMKVBool()

    //记录推出登陆是否由于多设备登陆
    var isLoginOutByMulDevice by mmkvBool()
    var loginOutReason by mmkvInt(LogoutReason.REASON_ON_OWN)
    var loginOutPrompt by mmkvString()

    // https://lizhi2021.feishu.cn/wiki/W2OYwNiQmiU9BXkAHbocxnocnxg
    var isUserRegister by userMMKVBool()
    var isNewUser by mmkvBool() // 当前用户是否是新用户：默认不是新用户，用户通过注册登录的则为新用户，退登重新登录则为非新用户, 注意不区分用户
    var userRegisterTime by userMMKVLong()
    var hasUserRetainTracked by userMMKVInt()

    // Record the ID of the window that has popped up.
    var surveyDialogPopIds by mmkvStringSet()
    var puzzleDialogPopIds by mmkvStringSet()
    var voiceCallDialogPopIds by mmkvStringSet()
    var quickReactDialogPopIds by mmkvStringSet()
    var asrDialogPopIds by mmkvStringSet()
//    var dndGuideDialogPopIds by mmkvStringSet()
    var officialAccountDialogPopIds by userMMKVStringSet()
    var collectBlindBoxDialogPopIds by userMMKVStringSet()
    // 视频通话弹窗
    var videoCallDialogPopIds by userMMKVStringSet()

    var groupPortraitDiskCacheKeyList by userMMKVParcelable<GroupPortraitDiskCacheKeyMap>()
    var rSamplingRate by mmkvInt(32000)
    var rBitRate by mmkvInt(32000)

    //for robot
    var rAISamplingRate by mmkvInt(32000)

    //for robot
    var rAIBitRate by mmkvInt(32000)
    // for ai market guide
    var needShowAiLearnMoreGuide by mmkvBool(true)
    var isAiLearnMoreGuideExposure by mmkvBool(false)

    //for test
    var maxAddressPersonNum by mmkvInt(1)
    //for test
    var sendVoiceTypeTest by mmkvInt(-1)

    var e2eeEnable by userMMKVBool(false)


    var redefineVoiceEmojiCache by mmkvString("")

    var redefineVoiceEmojiBlindBoxCache by userMMKVString("")

    var hasShownAddFriendsGuideDialog by userMMKVBool()

    var hasShownInviteMoreGuideDialog by userMMKVBool()


    /**
     * My Profile->Notification->Auto played message->Sounds
     */
    var vibration : Boolean
        get() = settingONVibrateOpen || settingAPMVibrateOpen
        set(value) {
            settingONVibrateOpen = value
            settingAPMVibrateOpen = value
        }

    /**
     * My Profile->Notification->Auto played message->Sounds
     */
    var settingAPMSoundsOpen by mmkvBool(true)

    /**
     * My Profile->Notification->Auto played message->Vibration
     */
    private var settingAPMVibrateOpen by mmkvBool(true)

    /**
     * My Profile->Notification->Other Notification->Sounds
     */
    var settingONSoundsOpen by mmkvBool(true)

    /**
     * My Profile->Notification->Other Notification->Vibration
     */
    private var settingONVibrateOpen by mmkvBool(true)

    /**
     * My Profile->Notification->SYNC with "Do Not Disturb"
     */
    @Deprecated("use getEnableSyncDND in userSettingRepository")
    var settingSyncDND by mmkvBool(true)

//    var hasSettingSyncDNDInPlayA by mmkvBool(false)

    var settingsSmartTranscription by mmkvBool(true)

    var trackSkipUnreadConvViewTimeLong by mmkvLong(0L)

    var lastClearNotificationChannelVersion by mmkvInt(-1)

    /* New variable for the recording down the buz's version */
    var latestApplicationVersion by mmkvInt(-1)

    var appConfigWithLogin by userMMKVString()

    var appConfigWithoutLogin by mmkvString()


    /**
     * Is user last use back Camera
     */
    var lastUserUseBackCamera by userMMKVBool(true)

    /**
     * My Profile->Setting->MediaDownload->Photos
     */
    var settingAutoDownloadMediaPhoto by userMMKVBool(false)

    var settingAutoDownloadMediaVideo by userMMKVBool(false)

    var lastShownContactsTipsId by userMMKVLong()

    var markReadFailedTipsId by userMMKVLong()

    var lastRequestTipsInfoExtra by userMMKVString()

    var lastPushTipsUpdateTime by userMMKVLong()

    /**
     * My Profile->Setting->Storage->Auto-Remove Cached Files
     */
    var settingAutoRemoveCachePeriod by userMMKVInt(-1)

    // Record the ID for home guide tip
    var homeGuideTipShowed by userMMKVStringSet()

    var showDeleteFroMeTipsDialog by userMMKVBool(true)
    var showDeleteFroEveryOneTipsDialog by userMMKVBool(true)

    var sessionKeyValidPeriod by userMMKVInt()

    var sessionKeyRefreshTime by userMMKVLong()

    // 缓存启动app和用户手动切换静音模式状态
    var cacheLatestQuietModeByUserSwitch by userMMKVBool(false)

    // 首页静音模式引导提示
    var hadShownQuiteModeGuide by mmkvBool(false)
    var latestDNDPlan by mmkvInt()

    // 首页勿扰模式引导弹窗
//    var hadShowChatDNDGuideDialog by mmkvBool(false)
    // 首页勿扰模式引导弹窗是否第一次弹窗
//    var isFirstShowDndGuideDialog by mmkvBool(true)

    var openTranscriptionPreview by userMMKVInt()


    var isBlindBoxInit by userMMKVBool(false)

    // 是否登录进入过
    var hasLoginSuccess by mmkvBool(false)

    var autoJumpPhoneInput by mmkvBool(false)
    // 在登录首页获取过网络AB配置
    var hasRegisterV2AbTestFromServerInLoginMainPage by mmkvBool(false)

    // 是否展示过新首页
    var showNewHomePage by mmkvBool(false)

    // demo ====
    var testPlanB  by mmkvBool(false)

    var testAll by mmkvBool(false)


    var translateTargetLanguage by userMMKVString("")

    var translateTargetLanguageList by mmkvString()

    var currentGuidanceModeStep by mmkvInt(-1)
    var currentUserIsGuidanceTestB by userMMKVBool(false)
    var fixedPagCacheProblemFlag by mmkvBool(false)

    var hadShowReleaseToSendTip by mmkvBool(false)
    var canShowOverlayGuidancePriorityDialogFragment by mmkvBool(false)
    var hasShowOverlayGuidancePriorityDialogFragment by mmkvBool(false)

    var hadShowVFIntroduceDialog by mmkvBool(false) //device
    var hadShowPreviewAvailableTip by mmkvBool(false)
    var hadPressInFilterMode by mmkvBool(false)

    var loginMainCenterAnim by mmkvBool(true)

    var hadShowVoiceFilterNotify by userMMKVBool(false)
    var hadShowUpdateVersionPopId by mmkvStringSet()

    var vfHadShowPressToRecordTooltip by mmkvBool(false)
    var vfHadShowReleaseToSendTooltip by mmkvBool(false)
    var vfHadShowPreviewAvailableToolTip by mmkvBool(false)
    var vfHadShowFindVFHereToolTip by mmkvBool(false)
    var vfHadShowVFGuidanceDialog by mmkvBool(false)
    var vfIsInVoiceFilterMode by mmkvBool(false)
    var hadSendFirstMsgSuccess by mmkvBool(false)

    var hadShownLPIntroduceDialogPrivate by mmkvBool(false)
    var hadShownLPIntroduceDialogGroup by mmkvBool(false)

    var hadShownLPFeedback by mmkvBool(false)
    var hadShownLPTooltips by mmkvBool(false)

    var voiceEmojiLatestCategoryTimestamp by mmkvString() // 获取2级tab红点时间戳
    var voiceEmojiLatestTabNotifyTimestamp by mmkvInt(0) // 获取1级tab红点时间戳
    var voiceEmojiLatestEntryNotifyTimestamp by mmkvInt(0) // 获取入口红点时间戳

    var voiceFilterLatestTimestamp by mmkvString() // 获取2级tab红点时间戳
    var voiceFilterLatestTabNotifyTimestamp by mmkvInt(0) // 获取1级tab红点时间戳
    var voiceFilterLatestEntryNotifyTimestamp by mmkvInt(0) // 获取入口红点时间戳

    var showSearchBannerTips by mmkvBool(true)

    var onlyForTestLoginAB2Google by mmkvBool(false)
    var onlyForTestLoginAB2Input by mmkvBool(false)
    var onlyForTestLoginAB2Main by mmkvBool(false)

    /**
     * 内存泄漏检测开关状态
     */
    var isLeakCanaryEnabled by mmkvBool(true)

    /**
     *具体枚举值请查看LoginType.type
     */
    var loginSource by mmkvInt()

    var neverTimingTriggeredOnce by mmkvBool(false)
}

/**解决无法存储null空值的boolean类型*/
@Parcelize
data class NullableBoolean(private val value: Boolean? = null) : Parcelable {
    // 未设置
    val isNotInitialized get() = value == null

    // 已设置为true
    val isTrue get() = value == true

    // 已设置为false
    val isFalse get() = value == false
}