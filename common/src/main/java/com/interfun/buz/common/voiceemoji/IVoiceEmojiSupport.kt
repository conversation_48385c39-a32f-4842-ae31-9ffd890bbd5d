package com.interfun.buz.common.voiceemoji

/**
 * 只有继承[Activity]继承后才支持在这个界面播放 全屏voiceemoji动画
 */
interface IVoiceEmojiSupport {
    /**
     * 返回界面对应的选中用户ID。只有收到voiceemoji发送人和当前界面[obtainTargetID]相同切[attachCondition]才播放全屏动画
     *
     * 如：
     * [PrivateChatActivity] 就返回聊天目标用户ID
     *
     *  [ChatHomeActivity] 返回[WTViewModel.wtItemChangeLiveData]
     */
    fun obtainTargetID(): Long

    fun attachCondition():Boolean

}