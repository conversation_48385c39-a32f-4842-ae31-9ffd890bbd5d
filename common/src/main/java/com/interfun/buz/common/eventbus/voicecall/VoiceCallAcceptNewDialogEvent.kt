package com.interfun.buz.common.eventbus.voicecall

import com.interfun.buz.common.bean.chat.JumpVoiceCallPageFrom
import com.interfun.buz.common.bean.chat.OnlineChatJumpInfo
import com.interfun.buz.common.eventbus.BaseEvent
import com.jeremyliao.liveeventbus.LiveEventBus

/**
 * @Desc
 * @Author:<EMAIL>
 * @Date: 2024/4/1
 */
class VoiceCallAcceptNewDialogEvent(val onlineChatJumpInfo: OnlineChatJumpInfo,val from: @JumpVoiceCallPageFrom Int): BaseEvent() {

    companion object{
        // Change to 500ms due to failing to intercept voicecall when recording video
        fun post(onlineChatJumpInfo: OnlineChatJumpInfo, from: @JumpVoiceCallPageFrom Int) {
            LiveEventBus.get(VoiceCallAcceptNewDialogEvent::class.java)
                .postDelay(VoiceCallAcceptNewDialogEvent(onlineChatJumpInfo,from), 500)
        }
    }

}