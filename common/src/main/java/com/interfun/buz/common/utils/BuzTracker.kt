package com.interfun.buz.common.utils

import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.base.ktx.log
import com.interfun.buz.common.constants.TrackConstant
import com.interfun.buz.common.manager.cache.user.UserSettingManager
import com.yibasan.lizhi.tracker.LZTracker
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import org.json.JSONObject

/**
 * Author: ChenYouSheng
 * Date: 2021/8/30
 * Email: <EMAIL>
 * Desc: 公共打点工具类, 不要在这里写业务代码, 打点业务在业务模块创建一个Tracker类,常考LiveTracker
 */
object BuzTracker {

    /**
     * 页面曝光
     */
    @JvmStatic fun onPageViewScreen(
        isReportImmediately: Boolean = false,
        init: MutableMap<String, Any>.() -> Unit
    ) {
        onEvent(TrackConstant.EVENT_APP_VIEW_SCREEN, isReportImmediately, init)
    }

    /**
     * 页面曝光，因为onPageViewScreen跟打点的点位名称不一样，每次需要确认是不是打点文档上的那个事件，所以特意加了个新方法
     */
    fun onAppViewScreen(
        isReportImmediately: Boolean = false,
        init: MutableMap<String, Any>.() -> Unit
    ) {
        onPageViewScreen(isReportImmediately, init)
    }

    /**
     * 弹窗页面曝光
     */
    @JvmStatic fun onDialogViewScreen(
        isReportImmediately: Boolean = false,
        init: MutableMap<String, Any>.() -> Unit
    ) {
        onEvent(TrackConstant.EVENT_VIEW_SCREEN, isReportImmediately, init)
    }

    /**
     * 元素曝光
     */
    @JvmStatic fun onElementExposure(
        isReportImmediately: Boolean = false,
        init: MutableMap<String, Any>.() -> Unit
    ) {
        onEvent(TrackConstant.EVENT_ELEMENT_EXPOSURE, isReportImmediately, init)
    }

    /**
     * 内容流曝光
     */
    @JvmStatic fun onContentExposure(
        isReportImmediately: Boolean = false,
        init: MutableMap<String, Any>.() -> Unit
    ) {
        onEvent(TrackConstant.EVENT_CONTENT_EXPOSURE, isReportImmediately, init)
    }

    /**
     * 点击内容流上报
     */
    @JvmStatic fun onContentClick(
        isReportImmediately: Boolean = false,
        init: MutableMap<String, Any>.() -> Unit
    ) {
        onEvent(TrackConstant.EVENT_CONTENT_CLICK, isReportImmediately, init)
    }

    /**
     * 点击上报
     */
    @JvmStatic fun onClick(
        isReportImmediately: Boolean = false,
        init: MutableMap<String, Any>.() -> Unit
    ) {
        onEvent(TrackConstant.EVENT_APP_CLICK, isReportImmediately, init)
    }

    /**
     * 结果反馈
     */
    @JvmStatic fun onResult(
        eventId: String = TrackConstant.EVENT_RESULT_BACK,
        isReportImmediately: Boolean = false,
        init: MutableMap<String, Any>.() -> Unit
    ) {
        onEvent(eventId = eventId, isReportImmediately = isReportImmediately, init = init)
    }

    @JvmStatic fun onEvent(
        eventId: String,
        isReportImmediately: Boolean = false,
        init: MutableMap<String, Any>.() -> Unit
    ) {
        val map = mutableMapOf<String, Any>()
        init(map)
        val json = JSONObject().apply {
            for ((k, v) in map) {
                put(k, v)
            }
        }
        onEventAtLZTracker(eventId, json, isReportImmediately)
    }

    private fun onEventAtLZTracker(eventKey: String,
        params: JSONObject,
        isReportImmediately: Boolean){
        try {
            log("BuzTracker", "onEvent eventKey $eventKey params $params")
            if (isReportImmediately) {
                LZTracker.trackImmediate(eventKey, params)
            } else {
                LZTracker.track(eventKey, params)
            }
        } catch (t: Throwable) {
            t.log("BuzTracker")
        }
    }

    /**
     * 安装后首次激活事件-$AppInstall
     */
    fun trackInstall(){
        LZTracker.trackInstall()
    }

    /**
     * 上报设备评分，score：50分以下可定义为低端机
     */
    fun trackDeviceScore(){
        GlobalScope.launch(Dispatchers.IO){
            val deviceScore = DeviceScoreUtil.calculateDeviceScore(appContext)
            onTechTrack {
                put(TrackConstant.KEY_EXCLUSIVE_ID, "TT2024122701")
                put(TrackConstant.KEY_EVENT_NAME, "DeviceScore")
                put(TrackConstant.KEY_CONTENT_1, deviceScore.cpuFreq)
                put(TrackConstant.KEY_CONTENT_2, deviceScore.memSize)
                put(TrackConstant.KEY_CONTENT_3, deviceScore.diskSize)
                put(TrackConstant.KEY_CONTENT_4, deviceScore.widthPixel)
                put(TrackConstant.KEY_CONTENT_5, deviceScore.heightPixel)
                put(TrackConstant.KEY_NUMBER_1, deviceScore.score)
                put(TrackConstant.KEY_NUMBER_2, deviceScore.version)
            }
        }
    }

    fun trackLogin(uid: String){
        LZTracker.login(uid)
        LZTracker.registerProperties(JSONObject().apply {
            put("userId", uid)
        })
    }

    fun trackLogout(){
        LZTracker.logout()
        LZTracker.registerProperties(JSONObject().apply {
            put("userId", "0")
        })
    }

    /**
     * 离线推送 点击通知栏打点
     * 事件类型：http://buz.pageweb.io/buz-doc/buz%E5%8D%8F%E8%AE%AE/%E5%AE%A2%E6%88%B7%E7%AB%AF%E8%B7%AF%E7%94%B1/Action.html
     */
    fun trackHandlePush(
        type: String,
        imMsgType: Int,
        imConvType: String?,
        convId: String?,
        serMsgId: String?,
        isFromOnlineNotification : Boolean,
    ) {
        CommonTracker.postClickEvent(
            "AC2022121906",
            "公共推送",
            "推送通知",
            "push",
            element_business_type = type,
            element_business_content = imMsgType.toString(),
            page_business_type = imConvType,
            page_business_id = convId ?: "",
            element_business_id = serMsgId ?: "",
            content_name = if( UserSettingManager.isQuietModeEnable) "no_autoplay" else "autoplay",
            logTime = System.currentTimeMillis().toString(),
            source = if (isFromOnlineNotification) "0" else "1",
        )
    }
    fun clickAC2023092102(myFriendList:Int){
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2023092102")
            put(TrackConstant.KEY_TITLE, "首页")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "机器人列表_Add按钮入口")
            put(TrackConstant.KEY_PAGE_TYPE, "home")
            //说明:当前好友数
            put(TrackConstant.KEY_BUSINESS_NUM, myFriendList)
        }
    }

    fun onTechTrack(
        eventId: String = TrackConstant.EVENT_TECH_TRACK,
        isReportImmediately: Boolean = false,
        init: MutableMap<String, Any>.() -> Unit
    ) {
        onEvent(eventId = eventId, isReportImmediately = isReportImmediately, init = init)
    }

    //Not allowed to start service Intent
    //Service.startForeground() not allowed
    fun onForegroundServiceStartFailed(isAppInForeGround: Boolean, e: Throwable, from: String) {
        GlobalScope.launch(Dispatchers.IO) {
            onTechTrack {
                put(TrackConstant.KEY_EXCLUSIVE_ID, "TT2024011701")
                put(TrackConstant.KEY_CONTENT_1, e.message ?: "")
                put(TrackConstant.KEY_CONTENT_2, if (isAppInForeGround) "1" else "0")
                put(TrackConstant.KEY_CONTENT_3, e.stackTraceToString())
                put(TrackConstant.KEY_CONTENT_4, from)
            }
        }
    }

    fun onForegroundServiceNotStated(
        from: String,
        isServiceStated: Boolean,
        isServiceForeground: Boolean
    ) {
        BuzTracker.onTechTrack {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "TT2024010801")
            put(TrackConstant.KEY_EVENT_NAME, "foreground_service_not_exist")
            put(TrackConstant.KEY_CONTENT_1, from)
            put(
                TrackConstant.KEY_CONTENT_2,
                if (isServiceStated) "1" else "0"
            )
            put(
                TrackConstant.KEY_CONTENT_3,
                if (isServiceForeground) "1" else "0"
            )
        }
    }
}