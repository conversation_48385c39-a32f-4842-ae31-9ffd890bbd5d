package com.interfun.buz.common.eventbus.group

import com.buz.idl.group.bean.GroupInfo
import com.interfun.buz.common.eventbus.BaseEvent
import com.interfun.buz.common.refactor.ShouldDeleteAfterRefactoring
import com.jeremyliao.liveeventbus.LiveEventBus

/**
 * @Desc the event for Group info change
 * @Author:<EMAIL>
 * @Date: 2022/7/27
 */
@Deprecated("兼容旧代码，新代码请别使用了")
@ShouldDeleteAfterRefactoring("待所有引用重构完可以删除")
class GroupInfoDidUpdateEvent(val group: GroupInfo): BaseEvent() {

    companion object{
        fun post(groupInfo: GroupInfo){
            LiveEventBus.get(GroupInfoDidUpdateEvent::class.java).post(GroupInfoDidUpdateEvent(groupInfo))
        }
    }

}