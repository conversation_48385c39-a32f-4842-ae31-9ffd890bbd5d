package com.interfun.buz.common.eventbus.group

import com.interfun.buz.common.eventbus.BaseEvent
import com.jeremyliao.liveeventbus.LiveEventBus

class SwitchAutoTranslateEvent(val conversationId: Long) : BaseEvent() {
    companion object {
        fun post(conversationId:Long) {
            LiveEventBus.get(SwitchAutoTranslateEvent::class.java).post(SwitchAutoTranslateEvent(conversationId))
        }
    }
}