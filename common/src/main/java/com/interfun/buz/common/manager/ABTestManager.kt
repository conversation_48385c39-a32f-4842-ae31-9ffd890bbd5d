package com.interfun.buz.common.manager

import com.buz.idl.common.bean.ABTestGroup
import com.buz.idl.common.request.RequestGetABTestGroup
import com.buz.idl.common.service.BuzNetCommonServiceClient
import com.interfun.buz.base.ktx.isNull
import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.common.bean.common.CustomAbTestGroup
import com.interfun.buz.common.constants.CommonMMKV
import com.interfun.buz.common.ktx.getBooleanDefault
import com.interfun.buz.common.ktx.isSuccess
import com.interfun.buz.common.manager.abtest.BaseABTestManager
import com.interfun.buz.common.net.withConfig
import com.interfun.buz.common.utils.ClientTracker
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map

class ABTestInfo(val isFromDisk: Boolean, type: Int, groupTpe: Int) :
    CustomAbTestGroup(type, groupTpe)

/**
 * 端到端加密开关需要一致性，需要多次读取，所以用第一次读取状态存储
 */
class ABTestE2EECache(val e2eeInit: Boolean, val e2eeSend: Boolean)
object ABTestManager : BaseABTestManager("ABTestManager") {

    // 临时关闭E2EE，切记不删除，为了后面方便恢复，保留代码
    const val FORCE_CLOSE_E2EE = true

    //发IM,TYPE_RTP对应的groupType
    const val GROUP_RTP_IM = GROUP_CONTROL

    //发RTP,TYPE_RTP对应的groupType
    const val GROUP_RTP_RTP = GROUP_EXPERIMENTAL

    //RTP实时对讲 已废弃
    @Deprecated(message = "RTP已下线, 保留枚举值定义")
    const val TYPE_RTP = 3

    //消息提示音类型
    const val TYPE_MESSAGE_RINGTONE_TYPE = 4

    //Buz bot
    const val TYPE_BUZ_BOT = 5

    //Quiet Mode (Deprecated, group B is used by default)
    const val TYPE_QUIET_MODE = 6
    const val TYPE_ROBOT_NEW_USER_DLG = 7
    const val TYPE_ROBOT_OLD_USER_DLG = 8

    @Deprecated(message = "原好友上线通知已下线, 新版使用23来实现")
    const val TYPE_ONLINE_NOTIFICATION = 9

    //the ab type for rtp sdk (init only) or (init + rtp usable)
    @Deprecated(message = "RTP已下线, 保留枚举值定义")
    const val TYPE_RTP_SDK_INIT = 10

    //Heartbeat type
//    const val TYPE_HEARTBEAT_TYPE = 11
    const val TYPE_E2EE_INIT = 15

    // send message support e2ee
    const val TYPE_E2EE_SEND = 16

    // e2ee init close
    const val TYPE_E2EE_INIT_CLOSE = 19

    // home list sort and online popup
    const val TYPE_HOME_LIST_SORT_AND_ONLINE_POPUP = 23

    // connect fcm to bigquery
    const val TYPE_FCM_BIGQUERY_LOG = 25

    // A组（默认组）：不再显示翻译相关的功能按钮 / B组（实验组）：显示翻译相关的按钮和功能
    const val TYPE_SHOW_TRANSLATE_FUNCTION = 33

    const val TYPE_REMAIN_NOTIFICATION_AFTER_KILL_APP = 35

    const val TYPE_FTUE_GUIDANCE = 36

    /**
     * 信令
     */
    const val TYPE_SIGNAL = 39

    // 滤镜功能的AB
    const val TYPE_VOICE_FILTER_FUNCTION = 40

    const val TYPE_VF_FTUE_GUIDANCE = 41

    const val TYPE_FTUE_FIRST_PAGE = 42

    const val TYPE_FTUE_CREATE_GROUP = 44
    const val TYPE_ADD_FRIEND_DIALOG_JAPAN = 45
    // 视频压缩模式AB
    const val TYPE_VIDEO_COMPRESS_MODE = 47

    // 好友状态
    const val TYPE_USER_STATE = 48

    const val LOCAL_TYPE_THAILAND_THIRD_PARTY = 20250520

    /**
     * AI 市场需求 区分新老用户请调用[initAIMarket]初始化相关配置
     * type:
     * [TYPE_AI_MARKET_NEW_USER] 新用户
     * [TYPE_AI_MARKET_OLD_USER] 老用户
     * GROUP_TYPE:
     *  [GROUP_TYPE_AI_MARKET_NO_ENTRY] 为对照组，无入口
     *  [GROUP_TYPE_AI_MARKET_HOME]  首页入口
     *  [GROUP_TYPE_AI_MARKET_CONTACT] 联系人列表入口/Addbutton入口
     *
     * 上述标志组合控制以下标志
     * [aiMarketplaceInAddFriend]
     * [aiMarketplaceInContact]
     * [aiMarketplaceInHomePage]
     */
    const val TYPE_AI_MARKET_NEW_USER = 13
    const val TYPE_AI_MARKET_OLD_USER = 14
    const val GROUP_TYPE_AI_MARKET_NO_ENTRY = 1
    const val GROUP_TYPE_AI_MARKET_HOME = 2
    const val GROUP_TYPE_AI_MARKET_CONTACT = 3

    /**
     * Addbutton入口
     * [GROUP_TYPE_AI_MARKET_CONTACT]
     */
    var aiMarketplaceInAddFriend = false
        private set

    /**
     * 联系人列表入口
     * [GROUP_TYPE_AI_MARKET_CONTACT]
     */
    var aiMarketplaceInContact = false
        private set

    /**
     * 首页入口
     * [GROUP_TYPE_AI_MARKET_HOME]
     */
    var aiMarketplaceInHomePage = false
        private set

    @Deprecated(message = "用于新注册添加+邀请好友,已废弃,仅保留PlanC组代码")
    const val TYPE_REGISTER_INVITE = 18

    val isHomeSortPlanB
        get() = get(
            TYPE_HOME_LIST_SORT_AND_ONLINE_POPUP,
            true
        )?.groupType == GROUP_EXPERIMENTAL

    val showTranslateFunction
        get() = get(TYPE_SHOW_TRANSLATE_FUNCTION,true)?.let {
            it.groupType == GROUP_EXPERIMENTAL
        }.getBooleanDefault(true)

    val closeNotificationAfterKill
        get() = get(TYPE_REMAIN_NOTIFICATION_AFTER_KILL_APP,true)?.groupType == GROUP_EXPERIMENTAL

    val showGuidanceFTUE
//        get() = get(TYPE_FTUE_GUIDANCE,true)?.groupType == GROUP_EXPERIMENTAL
       get() = false
    val isVFGuidanceControl
        get() = get(TYPE_VF_FTUE_GUIDANCE,true)?.groupType == GROUP_CONTROL

    val isVFGuidancePlanB
        get() = get(TYPE_VF_FTUE_GUIDANCE,true)?.groupType == GROUP_PLAN_B
    val isVFGuidancePlanC
        get() = get(TYPE_VF_FTUE_GUIDANCE,true)?.groupType == GROUP_PLAN_C

    val isAddFriendDialogJapanExperimental
        get() = get(TYPE_ADD_FRIEND_DIALOG_JAPAN,true)?.groupType == GROUP_EXPERIMENTAL



    val isFTUECreateGroupPlanB
        get() = get(TYPE_FTUE_CREATE_GROUP,true)?.groupType == GROUP_EXPERIMENTAL


    /**
     * 使用信令融合分组
     */
//    val useIMSignal
//        get() = get(TYPE_SIGNAL,true)?.groupType == GROUP_EXPERIMENTAL

    val showVoiceFilter
        get() = get(TYPE_VOICE_FILTER_FUNCTION,true)?.groupType == GROUP_EXPERIMENTAL
    val showVoiceFilterFlow = getAbFlow(TYPE_VOICE_FILTER_FUNCTION,true).map { it == GROUP_EXPERIMENTAL   }

    val useVideoEditCompressMode
        get() = ABTestManager.get(TYPE_VIDEO_COMPRESS_MODE,true)?.groupType == GROUP_EXPERIMENTAL

    /**
     * A组：- 首页和聊天页以及通讯录页，显示好友以及群在线状态描述 + 最近在线描述
     * B组：- 首页和聊天页以及通讯录页，仅展示绿点 + 紫点
     */
    val isUserStatePlanA get() = ABTestManager.get(TYPE_USER_STATE, true)?.groupType == GROUP_PLAN_A

    internal fun onLogin() {
        requestFromDisk()
    }

    @Synchronized
    override fun requestIfFailed() {
        if (requestJob?.isActive != true && abTestFlow.value.isNull() && UserSessionManager.hasSession()) {
            requestAbTestFromServer()
        }
    }

    override suspend fun requestABTest(): Pair<Boolean, List<ABTestGroup>?> {
        val service = BuzNetCommonServiceClient().withConfig()
        val list = arrayListOf(
            TYPE_MESSAGE_RINGTONE_TYPE,
            TYPE_BUZ_BOT,
            TYPE_ROBOT_NEW_USER_DLG,
            TYPE_ROBOT_OLD_USER_DLG,
//            TYPE_HEARTBEAT_TYPE,
            TYPE_AI_MARKET_NEW_USER,
            TYPE_AI_MARKET_OLD_USER,
            TYPE_E2EE_INIT,
            TYPE_E2EE_SEND,
            TYPE_E2EE_INIT_CLOSE,
            TYPE_HOME_LIST_SORT_AND_ONLINE_POPUP,
            TYPE_FCM_BIGQUERY_LOG,
            TYPE_SHOW_TRANSLATE_FUNCTION,
            TYPE_REMAIN_NOTIFICATION_AFTER_KILL_APP,
            TYPE_FTUE_GUIDANCE,
            TYPE_SIGNAL,
            TYPE_VOICE_FILTER_FUNCTION,
            TYPE_VF_FTUE_GUIDANCE,
            TYPE_ADD_FRIEND_DIALOG_JAPAN,
            TYPE_FTUE_CREATE_GROUP,
            TYPE_VIDEO_COMPRESS_MODE,
            TYPE_USER_STATE
        )
        val result = service.getABTestGroupList(RequestGetABTestGroup(list))
        logDebug("testGroupList: ${result.data?.testGroupList}")
        return result.isSuccess to result.data?.testGroupList
    }

    override fun saveCacheString2MMKV(cacheString: String) {
        CommonMMKV.abTest = cacheString
    }

    override fun getCacheString() = CommonMMKV.abTest

    fun getE2EEInitEnable(): Boolean {
        if (FORCE_CLOSE_E2EE) {
            return false
        }
        if (e2eeAbTestCache == null) {
            initE2EEABTest()
        }
        return e2eeAbTestCache?.e2eeInit ?: true
    }


    fun getE2EESendEnable(): Boolean {
        if (FORCE_CLOSE_E2EE) {
            return false
        }
        if (e2eeAbTestCache == null) {
            initE2EEABTest()
        }
        return e2eeAbTestCache?.e2eeSend ?: false
    }

    fun resetE2EEABTest() {
        initE2EEABTest()
    }

    private fun initE2EEABTest() {
        val list = getDiskCache()?.map { ABTestInfo(true, it.type, it.groupType) }
        logInfo(
            TAG,
            "getFromDiskCache ABTestFromDisk:${list?.map { "type:${it.type},group:${it.groupType}" }}"
        )
        /**
         * e2ee sdk init enable need {type=15, groupType=2}
         */
//        val e2eeInit = list?.find { it.type == TYPE_E2EE_INIT}?.groupType == GROUP_EXPERIMENTAL || CommonMMKV.e2eeEnable

        /**
         * e2ee sdk close init enable need {type = 19, groupType = 2}
         */
        val e2eeInit =
            list?.find { it.type == TYPE_E2EE_INIT_CLOSE }?.groupType != GROUP_EXPERIMENTAL

        /**
         * send encrypt im5message, enable need{type=16, groupType=2}
         */
        val e2eeSend =
            (e2eeInit && (list?.find { it.type == TYPE_E2EE_SEND }?.groupType == GROUP_EXPERIMENTAL))
        e2eeAbTestCache = ABTestE2EECache(e2eeInit, e2eeSend)
    }

    private var e2eeAbTestCache: ABTestE2EECache? = null

    internal fun onLogout() {
        requestJob?.cancel()
        requestJob = null
        requestFromDiskJob?.cancel()
        requestFromDiskJob = null
        abTestFlow.value = null
        abTestFlowFromDisk.value = null
    }

    suspend fun initAIMarket() {
        resetAIMarketFlag()
        val abResult = combineFlow.first()
        if (abResult.isNullOrEmpty()) {
            logInfo(TAG, "initAIMarket abResult.isNullOrEmpty()")
            return
        }

        abResult.filter { abTestInfo ->
            abTestInfo.type == TYPE_AI_MARKET_NEW_USER ||
                    abTestInfo.type == TYPE_AI_MARKET_OLD_USER
        }.forEach { abTestInfo ->
            logInfo(TAG, "initAIMarket type: ${abTestInfo.type} groupType: ${abTestInfo.groupType}")
            when (abTestInfo.groupType) {
                //之前已经置空 并且会有重叠组问题
                // type: 13 groupType: 1 和type: 14 groupType: 2会同时出现
//                GROUP_TYPE_AI_MARKET_NO_ENTRY -> {
//                    resetAIMarketFlag()
//                }
                GROUP_TYPE_AI_MARKET_HOME -> {
                    aiMarketplaceInHomePage = true
                }

                GROUP_TYPE_AI_MARKET_CONTACT -> {
                    aiMarketplaceInContact = true
                }
            }
        }
    }

    private fun resetAIMarketFlag() {
        aiMarketplaceInAddFriend = false
        aiMarketplaceInContact = false
        aiMarketplaceInHomePage = false
    }

}
