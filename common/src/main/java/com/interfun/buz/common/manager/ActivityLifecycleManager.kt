package com.interfun.buz.common.manager

import android.app.Activity
import android.app.Application
import android.os.Looper
import androidx.annotation.MainThread
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.BuildConfig
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.service.StartUpService
import curtains.onNextDraw
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.util.LinkedList

object ActivityLifecycleManager {

    private const val TAG = "ActivityLifecycleManager"

    private var createdActivityCount = 0
    private var startedActivityCount = 0
    private var hasCalledOnDrawThisRound = false
    private val entryPointActivityClazz by lazy {
        routerServices<StartUpService>().value?.getEntryPointActivityClass()
    }
    private var onFirstDrawCallbackList = LinkedList<DefaultCallback>()

    private val _allActivityDestroyed = MutableStateFlow(false)
    val allActivityDestroyed = _allActivityDestroyed.asStateFlow()

    fun onApplicationCreate(application: Application) {
        application.doOnActivityLifecycle(
            onActivityCreated = { activity, bundle ->
                createdActivityCount++
                if (_allActivityDestroyed.value) {
                    _allActivityDestroyed.tryEmit(false)
                }
            },
            onActivityStarted = { activity ->
                startedActivityCount++
                if (hasCalledOnDrawThisRound.not() && activity::class.java !== entryPointActivityClazz) {
                    observeNextDraw(activity)
                }
            },
            onActivityStopped = {
                startedActivityCount--
                if (startedActivityCount == 0) {
                    hasCalledOnDrawThisRound = false
                }
            },
            onActivityDestroyed = {
                createdActivityCount--
                if (createdActivityCount <= 0) {
                    _allActivityDestroyed.tryEmit(true)
                }
            })
    }

    private fun observeNextDraw(activity: Activity) {
        val name = activity.simpleName
        activity.window?.onNextDraw {
            logInfo(TAG, "onNextDraw,activity:$name")
            mainThreadHandler.post {
                if (hasCalledOnDrawThisRound.not()) {
                    logInfo(TAG, "call onFirstDrawCallback,activity:$name")
                    onFirstDrawCallbackList.forEach {
                        it.invoke()
                    }
                    hasCalledOnDrawThisRound = true
                }
            }
        }
    }

    @MainThread
    fun doOnNonEntryFirstDrawOnAppResume(block: DefaultCallback) {
        if (BuildConfig.DEBUG) {
            if (Thread.currentThread() != Looper.getMainLooper().thread) {
                throw IllegalAccessException("doOnNonEntryFirstDrawOnAppResume must call on main thread.")
            }
        }
        onFirstDrawCallbackList.add(block)
        if (hasCalledOnDrawThisRound) {
            block.invoke()
        }
    }
}