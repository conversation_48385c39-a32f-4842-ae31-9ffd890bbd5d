package com.interfun.buz.common.interfaces

import com.interfun.buz.common.bean.voicecall.CallRoomUser

/**
 * Author: ChenYouSheng
 * Date: 2025/3/7
 * Email: <EMAIL>
 * Desc:
 */
interface IVideoCallFloatTargetManager {

    fun createTarget(
        members: List<CallRoomUser>, channelType: Int
    ): Pair<Boolean, IVideoCallFloatTarget>?

    fun dispatchTargetChanges(
        newTarget: IVideoCallFloatTarget,
        onStateChange: (TargetChangeType) -> Unit
    )

    fun destroyTarget()
}

sealed interface TargetChangeType {
    data object MyMic : TargetChangeType
    data object MyCamera : TargetChangeType
    data object MySpeaking : TargetChangeType
    data object MyNetState : TargetChangeType
    data object MyJoinState : TargetChangeType
    data object TargetMic : TargetChangeType
    data object TargetCamera : TargetChangeType
    data object TargetSpeaking : TargetChangeType
    data object TargetNetState : TargetChangeType
    data object TargetJoinState : TargetChangeType
    data object All : TargetChangeType

    fun isTargetChange(): Boolean {
        return this is TargetSpeaking || this is TargetCamera || this is TargetMic || this is TargetNetState || this is TargetJoinState || this is All
    }

    fun isMyChange(): Boolean {
        return this is MySpeaking || this is MyCamera || this is MyMic || this is MyNetState || this is MyJoinState || this is All
    }

}