package com.interfun.buz.common.voicecall

import androidx.fragment.app.Fragment
import com.interfun.buz.base.ktx.OneParamCallback
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.base.ktx.toast
import com.interfun.buz.common.R
import com.interfun.buz.common.arouter.startActivityByRouter
import com.interfun.buz.common.bean.chat.JumpStartRealTimeCallEntrance
import com.interfun.buz.common.bean.chat.JumpVoiceCallPageFrom
import com.interfun.buz.common.bean.chat.OnlineChatJumpInfo
import com.interfun.buz.common.bean.chat.OnlineChatJumpType
import com.interfun.buz.common.bean.voicecall.CallConflictState
import com.interfun.buz.common.bean.voicecall.CallConflictState.*
import com.interfun.buz.common.bean.voicecall.CallRoomUser
import com.interfun.buz.common.bean.voicecall.CallType
import com.interfun.buz.common.bean.voicecall.ChannelType
import com.interfun.buz.common.constants.JUMP_INFO
import com.interfun.buz.common.constants.PATH_ACTIVITY_REAL_TIME_CALL
import com.interfun.buz.common.constants.RouterParamKey
import com.interfun.buz.common.manager.voicecall.VoiceCallPortal
import com.interfun.buz.common.utils.CommonTracker
import com.interfun.buz.common.utils.VoiceCallNotificationTracker

class GroupCallStaterImpl(fragment: Fragment) : CallStarter(fragment),
    GroupCallStater {
    private val TAG = "GroupVoiceCallStaterImpl"

    // ---------------------- Show Select Member Dialog ---------------------- //
    override fun showGroupCallSelectMemberDialog(
        groupId: Long,
        callType: @CallType Int,
        actionType: ActionType,
        entrance: @JumpStartRealTimeCallEntrance Int,
        callResult: OneParamCallback<DialogResult>?
    ) {
        val (jumpHelper, dialogHelper) = createShowSelectMemberDialogHelper(
            groupId = groupId,
            callType = callType,
            channelType = ChannelType.TYPE_VOICE_CALL_GROUP,
            entrance = entrance
        )
        doPreCheck(
            targetId = groupId,
            channelType = ChannelType.TYPE_VOICE_CALL_GROUP,
            callType = callType,
            actionType = actionType,
            onResult = { conflictState ->
                callResult?.invoke(
                    DialogResult(
                        fragment = fragment,
                        showDialogHelper = dialogHelper,
                        jumpHelper = jumpHelper,
                        curCallConflictState = conflictState
                    )
                )
            }
        )
    }

    // ---------------------- Join Call ---------------------- //
    override fun joinRealTimeCall(
        groupId: Long,
        channelId: Long,
        callType: @CallType Int,
        source: @JumpVoiceCallPageFrom Int?,
        joinResult: OneParamCallback<CallJoinResult>?
    ) {
        val (callJumper, joinHelper) = createJoinHelper(
            targetId = groupId,
            channelId = channelId,
            callType = callType,
            source = source,
            onCallStarted = { callConflictState ->
                if (callConflictState != NO_CONFLICT) {
                    handleCallErrorAndTrackEvent(
                        groupId = groupId,
                        callType = callType,
                        error = callConflictState,
                        actionType = ActionType.JOIN
                    )
                }
            }
        )
        doPreCheck(
            targetId = groupId,
            channelType = ChannelType.TYPE_VOICE_CALL_GROUP,
            callType = callType,
            actionType = ActionType.JOIN,
            onResult = {ret->
                joinResult?.invoke(CallJoinResult(joinHelper, callJumper,ret))
            }
        )
    }

    override fun startRealTimeCall(
        groupId: Long,
        callType: @CallType Int,
        selectMember: List<CallRoomUser>,
        entrance: @JumpStartRealTimeCallEntrance Int,
        callStartResult: OneParamCallback<CallStartResult>?
    ) {
        val traceId = generateTraceId()
        // 点击前打点
        VoiceCallNotificationTracker.onClickCallEntranceEvent(
            traceId = traceId,
            targetId = groupId,
            channelType = ChannelType.TYPE_VOICE_CALL_GROUP,
            callType = callType,
            entrance = entrance
        )

        val (callJumper, callStater) = createStartHelper(
            userList = selectMember,
            targetId = groupId,
            callType = callType,
            channelType = ChannelType.TYPE_VOICE_CALL_GROUP,
            source = JumpVoiceCallPageFrom.GROUP_ITEM_CALL_CLICK,
            traceId = traceId,
            onCallStarted = { callConflictState ->
                if (callConflictState != NO_CONFLICT) {
                    handleCallErrorAndTrackEvent(
                        groupId = groupId,
                        callType = callType,
                        error = callConflictState,
                        memberSize = selectMember.size,
                        actionType = ActionType.START
                    )
                }
            }
        )
        doPreCheck(
            targetId = groupId,
            channelType = ChannelType.TYPE_VOICE_CALL_GROUP,
            callType = callType,
            userList = selectMember,
            onResult = { conflictState ->
                // 点击后打点
                VoiceCallNotificationTracker.onClickStartRealTimeCallResult(
                    traceId = traceId,
                    callType = callType,
                    channelType = ChannelType.TYPE_VOICE_CALL_GROUP,
                    entrance = entrance,
                    conflictState = conflictState
                )
                // 回调结果
                callStartResult?.invoke(
                    CallStartResult(
                        startHelper = callStater,
                        jumpHelper = callJumper,
                        curCallConflictState = conflictState
                    )
                )
            },
            actionType = ActionType.START
        )
    }

    override fun handlePreCheckResult(
        targetId: Long,
        channelType: @ChannelType Int,
        callType: @CallType Int,
        callConflictState: CallConflictState,
        actionType: ActionType
    ) {
        handleCallErrorAndTrackEvent(
            groupId = targetId,
            callType = callType,
            error = callConflictState,
            actionType =actionType
        )
    }

    /**
     * 检查、处理用户是否已经在通话中
     */
    private fun checkAndHandleIsMinimize(groupId: Long, callType: @CallType Int): Boolean {
        val currentRoom = VoiceCallPortal.currentRoom.value ?: return false

        if (currentRoom.minimizeTargetId != groupId) {
            toast(R.string.leave_system)
            handleCallErrorAndTrackEvent(groupId = groupId, callType = callType, error = ON_CALL, actionType = ActionType.JOIN)
            return true
        }

        startActivityByRouter(
            path = PATH_ACTIVITY_REAL_TIME_CALL,
            buildPostcardBlock = {
                withParcelable(
                    RouterParamKey.Chat.JUMP_INFO,
                    OnlineChatJumpInfo(
                        channelId = currentRoom.roomChannelId,
                        channelType = ChannelType.TYPE_VOICE_CALL_GROUP,
                        callType = callType,
                        targetId = groupId,
                        jumpType = OnlineChatJumpType.reentryFromMinimize
                    )
                )
                withTransition(R.anim.anim_dialog_theme_enter, R.anim.anim_dialog_theme_out)
            }
        )
        return true
    }



    private fun handleCallErrorAndTrackEvent(
        groupId: Long,
        callType: @CallType Int,
        error: CallConflictState,
        memberSize: Int = 0,
        actionType: ActionType
    ) {
        handleRealTimeCallError(error, actionType, groupId, callType)
        trackStartCallEvent(
            targetId = groupId,
            callType = callType,
            conflict = error,
            memberSize = memberSize
        )
    }

    private fun trackStartCallEvent(
        targetId: Long,
        callType: @CallType Int,
        conflict: CallConflictState,
        memberSize: Int
    ) {
        logInfo(
            TAG,
            "trackStartCallEvent:targetId = $targetId, conflict = $conflict, memberSize = $memberSize"
        )
        val failReason = when (conflict) {
            BEING_INVITED -> "ongoing_call"
            ON_CALL -> "realTimeCalling"
            NO_RECORD_PERMISSION -> "no_record_permission"
            NO_CAMERA_PERMISSION -> "no_camera_permission"
            NOT_FRIEND -> "notfriend" //不是好友
            NETWORK_ERROR -> "other" //其他如网络错误
            RECORD_IN_USE -> "systemCalling" //正在系统通话
            CAMERA_IN_USE -> "camera_in_use" //相机正在使用
            else -> return
        }

        CommonTracker.onStartGroupVoiceCallResult(
            targetId = targetId,
            memberCount = memberSize,
            callType = callType,
            0L,
            0L,
            false,
            failReason
        )
    }
}