package com.interfun.buz.common.eventbus.group

import com.interfun.buz.common.eventbus.BaseEvent
import com.jeremyliao.liveeventbus.LiveEventBus

/**
 * @Desc
 * @Author:lvzhen<PERSON><EMAIL>
 * @Date: 2022/8/29
 */
@Deprecated("请不要使用这个，使用GroupMembersRepository获取members的flow")
class GroupMembersChangeEvent(val groupId: Long): BaseEvent() {

    companion object{
        fun post(groupId: Long){
            LiveEventBus.get(GroupMembersChangeEvent::class.java).post(GroupMembersChangeEvent(groupId))
        }
    }
}