package com.interfun.buz.common.manager.cache.user


/**
 * Author: <PERSON><PERSON>ouSheng
 * Date: 2025/7/30
 * Email: chen<PERSON><PERSON><PERSON>@vocalbeats.com
 * Desc:
 */
@Deprecated("请不要再使用这个，请使用 UserSettingRepository 代替，为了兼容旧代码保留")
object UserSettingManager : UserSettingRepoCompat {
    lateinit var delegate: UserSettingRepoCompat

    override val isQuietModeEnable: Boolean
        get() = delegate.isQuietModeEnable
}