package com.interfun.buz.common.eventbus.af

import com.interfun.buz.common.eventbus.BaseEvent
import com.jeremyliao.liveeventbus.LiveEventBus

class CampaignRouteEvent(val route:String,val isFirstLaunch:Boolean = false): BaseEvent() {
    private val TAG = "CampaignRouteEvent"

    companion object{
        fun post(route:String,isFirstLaunch: Boolean = false){
            LiveEventBus.get(CampaignRouteEvent::class.java).post(CampaignRouteEvent(route,isFirstLaunch))
        }
    }


}


