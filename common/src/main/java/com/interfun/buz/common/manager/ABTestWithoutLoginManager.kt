package com.interfun.buz.common.manager

import com.buz.idl.common.bean.ABTestGroup
import com.buz.idl.common.request.RequestGetABTestResultBeforeLogin
import com.buz.idl.common.service.BuzNetCommonServiceClient
import com.interfun.buz.base.ktx.isNull
import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.common.constants.CommonMMKV
import com.interfun.buz.common.ktx.isSuccess
import com.interfun.buz.common.manager.abtest.BaseABTestManager
import com.interfun.buz.common.net.withConfig
import com.interfun.buz.common.utils.CommonTracker
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow

/**
 * <AUTHOR>
 * @date 2023/10/8
 * @desc
 */
object ABTestWithoutLoginManager : BaseABTestManager("ABTestWithoutLoginManager") {

    private var isForceRequest = false

     val requestStateFromServerShareFlow = MutableSharedFlow<Int?>()

    // 新首页AB实验
    const val TYPE_HOME_PAGE_AB = 34

    // 注册登录权限页实验
    const val TYPE_LOGIN_REGISTER_PERMISSION = 37

    // Google主推实验
    const val TYPE_LOGIN_REGISTER_GOOGLE_MAIN = 38

    val isLoginRegisterPermissionPlanB
        get() = ABTestWithoutLoginManager.get(TYPE_LOGIN_REGISTER_PERMISSION,true)?.groupType == GROUP_PLAN_B


    /**首页AB，旧版本是A组(默认A组），新版本是B组，业务使用[LoginMainABTestManager.isShowNewHomePagePlanB]*/
    val isShowNewHomePagePlanB
        get() = (ABTestWithoutLoginManager.get(TYPE_HOME_PAGE_AB, true)?.groupType
            ?: GROUP_PLAN_A) == GROUP_PLAN_B

    var startTime = 0L

    fun start() {
        // 新增的这个方法是为了做初始化。
        logDebug(TAG,"ABTestWithoutLoginManager start")
        startTime = System.currentTimeMillis()
        requestFromDisk()
    }

    fun requestABTestWithoutLogin(isForceRequest: Boolean = false){
        this.isForceRequest = isForceRequest
        requestIfFailed()
        requestFromDisk()
    }

    override suspend fun requestABTest(): Pair<Boolean, List<ABTestGroup>?> {
        val request = RequestGetABTestResultBeforeLogin(
            arrayListOf(
                TYPE_LOGIN_REGISTER_PERMISSION,
                TYPE_LOGIN_REGISTER_GOOGLE_MAIN,
                TYPE_HOME_PAGE_AB
            )
        )
        val needReport =
            ABTestWithoutLoginManager.get(TYPE_LOGIN_REGISTER_GOOGLE_MAIN,true) == null
        logDebug("ABTestWithoutLoginManager","needReport: $needReport")

        val result = BuzNetCommonServiceClient().withConfig().getABTestResultBeforeLogin(request)
        if (needReport && startTime > 0 ) {
            CommonTracker.postABTestTimeCost(System.currentTimeMillis() - startTime)
        }
        requestStateFromServerShareFlow.emit(result.code)
        return result.isSuccess to result.data?.testGroupList
    }

    override fun saveCacheString2MMKV(cacheString: String) {
        CommonMMKV.abTestBeforeLogin = cacheString
    }

    override fun getCacheString() = CommonMMKV.abTestBeforeLogin

    @Synchronized
    override fun requestIfFailed() {
        if (requestJob?.isActive != true && (abTestFlow.value.isNull() || isForceRequest)) {
            isForceRequest = false
            logDebug(TAG, "requestAbTestFromServer start")
            requestAbTestFromServer()
        }
    }

    fun getAbTestFromCacheShareFlow(): SharedFlow<List<ABTestInfo>?> {
        return abTestFlow.asSharedFlow()
    }

    fun getABTestFromDiskFlow(): SharedFlow<List<ABTestInfo>?> {
        return abTestFlowFromDisk.asSharedFlow()
    }
}
