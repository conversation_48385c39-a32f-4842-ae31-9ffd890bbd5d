package com.interfun.buz.common.manager

import com.interfun.buz.common.utils.language.LanguageManager
import com.interfun.buz.common.utils.language.language_de
import com.interfun.buz.common.utils.language.language_en
import com.interfun.buz.common.utils.language.language_es
import com.interfun.buz.common.utils.language.language_fr
import com.interfun.buz.common.utils.language.language_ja
import com.interfun.buz.common.utils.language.language_zh

val OG_SHARE_IMAGE_URL: String
    get() {
        val languageCode = LanguageManager.getLocaleLanguageCode()
        return supportShareUrls[languageCode] ?: defaultShareUrl
    }


private const val SHARE_BUZ_OG_IMAGE_URL = "https://www.buz-app.com/_app/img/og_en.png"
private val defaultShareUrl = SHARE_BUZ_OG_IMAGE_URL

/**
 * 当前支持的语言对应的pag动画
 */
private val supportShareUrls = mutableMapOf(
    language_en.code to defaultShareUrl,
    language_de.code to "https://www.buz-app.com/_app/img/og_de.png",
    language_fr.code to "https://www.buz-app.com/_app/img/og_fr.png",
    language_ja.code to "https://www.buz-app.com/_app/img/og_ja.png",
    language_es.code to "https://www.buz-app.com/_app/img/og_es.png",
    language_zh.code to "https://www.buz-app.com/_app/img/og_zh.png",
)
