package com.interfun.buz.common.eventbus

import com.interfun.buz.base.utils.BusUtil
import com.interfun.buz.common.database.entity.UserRelationInfo

/**
 * @Desc
 * @Author:<EMAIL>
 * @Date: 2022/7/15
 */
@Deprecated("请不要再使用这个，使用UserRepository相关的flow获取实时更新的用户信息")
class QueryUserInfoSuccessEvent(
    val userInfoList: List<UserRelationInfo>,
    val noExistUserList: List<Long>? = null
) : BaseEvent() {
    companion object {
        fun post(userInfoList: List<UserRelationInfo>, noExistUserList: List<Long>? = null) {
            BusUtil.post(QueryUserInfoSuccessEvent(userInfoList, noExistUserList))
        }
    }
}