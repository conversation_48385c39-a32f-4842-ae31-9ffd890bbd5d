package com.interfun.buz.common.manager

import android.os.Handler
import android.os.HandlerThread
import kotlinx.coroutines.*
import kotlin.coroutines.CoroutineContext
import kotlin.coroutines.EmptyCoroutineContext

fun CoroutineScope.launchAsync(
    context: CoroutineContext = EmptyCoroutineContext,
    start: CoroutineStart = CoroutineStart.DEFAULT,
    block: suspend CoroutineScope.() -> Unit
) {
    AsyncCoroutineLauncher.handler.post {
        launch(context, start, block)
    }
}

internal object AsyncCoroutineLauncher {
    val handler by lazy {
        val thread = HandlerThread("AsyncCoroutineLauncher")
        thread.start()
        Handler(thread.looper)
    }
}