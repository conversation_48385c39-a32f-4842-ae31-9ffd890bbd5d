package com.interfun.buz.common.intentconstant

import android.content.Context
import android.content.Intent


object TargetSelectActivityConstant {
    const val KEY_FILTER_ROBOT = "KEY_FILTER_ROBOT"
    const val KEY_FILTER_GROUP = "KEY_FILTER_GROUP"
    const val KEY_FILTER_OFFICE = "KEY_FILTER_OFFICE"
    const val KEY_USER_SELECT_LIST = "KEY_FILTER_USER_SELECT_LIST"

    /**
     * 如果需要群自己扩展
     */
    const val KEY_GROUP_SELECT_LIST = "KEY_GROUP_SELECT_LIST"
    const val KEY_MAX_LIMIT = "KEY_MAX_LIMIT"
    const val RESULT_KEY_USERS = "RESULT_KEY_USERS"
    const val RESULT_KEY_GROUPS = "RESULT_KEY_GROUPS"
    private val intentClass by lazy { Class.forName("com.interfun.buz.chat.targetselect.TargetSelectActivity") }

    fun createIntent(
        content: Context,
        maxLimit: Int,
        curUsersSelectArr: LongArray = longArrayOf(),
        filterRobot: Boolean = true,
        filterOffice: Boolean = true,
        filterGroup: Boolean = true,
    ): Intent {
        val intent = Intent(content, intentClass)
        intent.putExtra(KEY_MAX_LIMIT, maxLimit)
        intent.putExtra(KEY_USER_SELECT_LIST, curUsersSelectArr)
        intent.putExtra(KEY_FILTER_GROUP, filterGroup)
        intent.putExtra(KEY_FILTER_ROBOT, filterRobot)
        intent.putExtra(KEY_FILTER_OFFICE, filterOffice)
        return intent
    }

}