package com.interfun.buz.common.qrcode

import android.Manifest
import com.interfun.buz.common.net.withConfig
import android.graphics.BitmapFactory
import android.graphics.PointF
import android.net.Uri
import android.os.Bundle
import android.text.TextUtils
import android.util.Log
import android.webkit.URLUtil
import androidx.activity.OnBackPressedCallback
import androidx.activity.result.PickVisualMediaRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.result.contract.ActivityResultContracts.PickVisualMedia.ImageOnly
import androidx.lifecycle.lifecycleScope
import coil.load
import com.alibaba.android.arouter.facade.annotation.Route
import com.buz.idl.common.request.RequestExplainQrCodeContent
import com.buz.idl.common.response.ResponseExplainQrCodeContent
import com.buz.idl.common.service.BuzNetCommonServiceClient
import com.google.zxing.BinaryBitmap
import com.google.zxing.DecodeHintType
import com.google.zxing.RGBLuminanceSource
import com.google.zxing.common.HybridBinarizer
import com.google.zxing.qrcode.QRCodeReader
import com.interfun.buz.base.ktx.*
import com.interfun.buz.base.qrcodereaderview.QRCodeReaderView
import com.interfun.buz.base.utils.VibratorUtil
import com.interfun.buz.common.R
import com.interfun.buz.common.base.binding.BaseBindingActivity
import com.interfun.buz.common.constants.*
import com.interfun.buz.common.databinding.CommonQrcodeScanActivityBinding
import com.interfun.buz.common.ktx.*
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.manager.router.RouterManager
import com.interfun.buz.common.utils.*
import com.lizhi.itnet.lthrift.service.ITResponse
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext


/**
 * Author: ChenYouSheng
 * Date: 2023/5/5
 * Email: <EMAIL>
 * Desc: 二维码识别
 */
@Route(path = PATH_COMMON_ACTIVITY_QR_CODE)
@AndroidEntryPoint
class QRCodeScanActivity : BaseBindingActivity<CommonQrcodeScanActivityBinding>(), QRCodeReaderView.OnQRCodeReadListener {

    companion object {
        private const val TAG = "QRCodeActivity"
    }

    val source: Int by lazy {
        intent.getIntExtra(RouterParamKey.Common.KEY_SOURCE, QRCodeScanSource.Contact.value)
    }

    private var isAnalysisSuccess = false
    private var mScannerView: QRCodeReaderView? = null

    private val pickPictureLauncher = pickContentLauncher { handleChooseImage(it) }
    private val photoPicker = registerForActivityResult(ActivityResultContracts.PickVisualMedia()) {
        handleChooseImage(it)
    }

    private val permissionHelper = PermissionHelper(this)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        onOpenScanEventTrack()
    }


    override fun initView() {
        super.initView()
        binding.ivScanFrame.load(R.drawable.ic_scan_frame)
        binding.spaceStatusBar.initStatusBarHeight()
        binding.iftBack.click {
            back()
        }
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                back()
            }
        })
        combineView(binding.iftSelectAlbum, binding.tvSelectAlbum).click {
            openAlbum()
        }
        combineView(binding.tvMyQrcode, binding.iftMyQrCode).click {
            createMyCode()
        }
        checkPermission()
    }

    private fun createMyCode() {
        startShareQRCodeActivity(
            list = listOf(UserSessionManager.portrait),
            name = UserSessionManager.userName,
            buzId = UserSessionManager.buzId,
            type = 1,
            source = ShareQRCodeSource.Scan.value
        )
        CommonTracker.onClickMyQrCode()
    }

    private fun back() {
        finish()
        overridePendingTransition(R.anim.anim_nav_enter_pop, R.anim.anim_nav_exit_pop)
    }

    private fun openAlbum() {
        if (ActivityResultContracts.PickVisualMedia.isPhotoPickerAvailable()) {
            photoPicker.launch(PickVisualMediaRequest(ImageOnly))
        } else {
            pickPictureLauncher.launchForImage()
        }
        CommonTracker.onClickScanAlbum()
    }

    private fun checkPermission() {
        if (isPermissionGranted(Manifest.permission.CAMERA)) {
            initQRCamera()
        } else {
            permissionHelper.request(
                this, false, Manifest.permission.CAMERA
            ) { result ->
                if (result.isAllGranted) {
                    initQRCamera()
                } else {
                    result.resultMap.forEach { (permission, item) ->
                        if (item.isGranted.not()) {
                            if (item.hasSysDialogShown.not()) {
                                showAskCameraPermissionDialog(this@QRCodeScanActivity){
                                    result.resultHelper.toSetting { result ->
                                        if (result.isAllGranted) {
                                            initQRCamera()
                                        }
                                    }
                                    it.dismiss()
                                }
                            }
                        } else {
                            initQRCamera()
                        }
                    }
                }
            }
        }
    }

    private fun initQRCamera() {
        if (mScannerView != null) {
            return
        }
        binding.ivScanFrame.visible()
        mScannerView = QRCodeReaderView(this).apply {
            setQRDecodingEnabled(true)
            setAutofocusInterval(1000L)
            setTorchEnabled(true)
            setBackCamera()
            setOnQRCodeReadListener(this@QRCodeScanActivity)
        }
        binding.qrCameraLayout.addView(mScannerView)
        // start camera
    }


    override fun onCameraOpenFailed() {
    }

    override fun onResume() {
        super.onResume()
        mScannerView?.startCamera("onResume")
    }

    override fun onPause() {
        super.onPause()
        mScannerView?.stopCamera("onPause")
    }

    override fun onQRCodeRead(text: String, points: Array<PointF>) {
        if (isAnalysisSuccess) {
            return
        }
        VibratorUtil.vibrator(this, 200)
        analysis(text)
        isAnalysisSuccess = true
    }

    private fun analysis(text: String) {
        lifecycleScope.launch {
            if (changeUI(text)) {
                Log.d(TAG, "扫描结果:${text}")
                //"扫码成功,开始请求网络".toastDebug()
                doRequestExplainQrCodeContent(text)
            }
        }
    }

    private suspend fun changeUI(text: String): Boolean {
        binding.ivScanFrame.alpha = 0.8f
        delay(1000)
        if (isNetworkAvailable.not()) {
            showTips(
                showLoading = false, showError = true, title = R.string.scan_failed.asString(), description = R.string.network_error.asString()
            )
            CommonTracker.onScanResult(failReason = 10002)
            return false
        }
        if (TextUtils.isEmpty(text) || URLUtil.isNetworkUrl(text).not()) {
            // 不允许为空
            showTips(
                showLoading = false,
                showError = true,
                title = R.string.scan_error.asString(),
                description = R.string.qr_code_not_detected.asString()
            )
            CommonTracker.onScanResult(failReason = 10003)
            return false
        }
        showTips(
            showLoading = true, showError = false, title = R.string.scan_successful.asString(), description = R.string.prepare_scan.asString()
        )
        return true
    }

    /**
     * 在线解析
     */
    private suspend fun doRequestExplainQrCodeContent(text: String) {
        val result: ITResponse<ResponseExplainQrCodeContent> =
            BuzNetCommonServiceClient().withConfig().explainQrCodeContent(request = RequestExplainQrCodeContent(content = text))
        handleNetResult(result)
        handleResultEventTrack(result)
    }

    private fun handleResultEventTrack(result: ITResponse<ResponseExplainQrCodeContent>) {
        val isSuccess = result.data.isNotNull() && result.isSuccess
        if (isSuccess) {
            val explainCode = result.data!!.explainCode
            if (explainCode == 1) {
                val isGroupQrCode = result.data?.action.isTargetSchema(RouterSchemes.Group.GROUP_INFO)
                CommonTracker.onScanResult(pageStatus = if (isGroupQrCode) 0 else 1)
            } else {
                CommonTracker.onScanResult(failReason = explainCode)
            }
        } else {
            CommonTracker.onScanResult(failReason = result.code)
        }
    }

    private fun handleNetResult(result: ITResponse<ResponseExplainQrCodeContent>) {
        val unknownError = {
            showTips(
                showLoading = false,
                showError = true,
                title = R.string.scan_error.asString(),
                description = R.string.qr_code_try_again.asString()
            )
        }
        if (result.data.isNotNull() && result.isSuccess) {
            when (result.data!!.explainCode) {
                // 扫描结果 1、正常 ; 102、用户不在群; 104、扫自己;
                1, 102, 104 -> {
                    val action = result.data?.action
                    action?.let {
                        logInfo(TAG,"处理二维码识别结果:scheme=${it.router.scheme}, extraData=${it.router.extraData}")
                        RouterManager.handle(this, it.router.scheme, it.router.extraData)
                    }
                    back()
                }

                100 -> { // 格式错误
                    showTips(
                        showLoading = false,
                        showError = true,
                        title = R.string.unsupport_qr_code.asString(),
                        description = R.string.scan_buz_qr_code_tip.asString()
                    )
                }

                101 -> { // 已过期
                    showTips(showLoading = false, showError = true, title = R.string.qr_code_expired.asString())
                }

                103 -> { // 邀请人注销账号
                    showTips(showLoading = false, showError = true, title = R.string.qr_code_expired.asString())
                }

                else -> {
                    unknownError()
                }
            }
        } else {
            unknownError()
        }
    }


    private fun showTips(showLoading: Boolean = true, showError: Boolean = false, title: CharSequence = "", description: CharSequence = "") {
        binding.ivScanFrame.alpha = 0.3f
        binding.defaultPageView.setBackgroundColor(R.color.black_80.asColor())
        binding.defaultPageView.show(showLoading = showLoading, showError = showError, title = title, description = description)
        binding.scanGroupUi.gone()
        binding.ivScanFrame.gone()
        mScannerView?.stopCamera("showTips")
    }

    private fun handleChooseImage(uri: Uri?) {
        if (uri == null) return
        lifecycleScope.launch {
            try {
                val bitmap = withContext(Dispatchers.IO) {
                    contentResolver.openInputStream(uri)?.use {
                        BitmapFactory.decodeStream(it)
                    }
                }
                if (bitmap == null) {
                    onQRCodeRead("", emptyArray())
                    return@launch
                }
                val hints = mapOf(Pair(DecodeHintType.CHARACTER_SET, "utf-8"))
                val width = bitmap.width
                val height = bitmap.height
                val pixels = IntArray(width * height)
                val result = withContext(Dispatchers.Default) {
                    bitmap.getPixels(pixels, 0, width, 0, 0, width, height)
                    val source = RGBLuminanceSource(width, height, pixels) //构建RGBLuminanceSource对象
                    QRCodeReader().decode(BinaryBitmap(HybridBinarizer(source)), hints)
                }
                // 处理二维码扫描结果
                if (result != null) {
                    onQRCodeRead(result.text, result.resultPoints.map { PointF(it.x, it.y) }.toTypedArray())
                } else {
                    onQRCodeRead("", emptyArray())
                }
            } catch (e: Exception) {
                e.printStackTrace()
                // 扫描失败
                onQRCodeRead("", emptyArray())
            }
        }
    }


    private fun onOpenScanEventTrack() {
        when (source) {
            QRCodeScanSource.Home.value -> {
                CommonTracker.onClickScan(exclusiveId = "AC2023051709", title = "首页")
            }
            QRCodeScanSource.Contact.value -> {
                CommonTracker.onClickScan(exclusiveId = "AC2023051708", title = "通讯录页")
            }
            QRCodeScanSource.AddFriend.value ->{
                CommonTracker.onClickScan(exclusiveId = "AC2025022702", title = "加好友页")
            }
            QRCodeScanSource.QRCode.value -> {
                CommonTracker.onClickScan(exclusiveId = "AC2023051710", title = "个人二维码")
            }
        }
        CommonTracker.onQrcodeScanExpose(source = source)
    }
}
