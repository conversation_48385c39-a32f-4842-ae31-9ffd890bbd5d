package com.interfun.buz.common.eventbus.contact

import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.base.utils.BusUtil
import com.interfun.buz.common.eventbus.BaseEvent

/**
 * <AUTHOR>
 * @date 2024/6/13
 * @desc
 */
class PushNewContactRegisteredEvent(val from: String) : BaseEvent() {
    companion object {
        fun post(from: String) {
            logInfo("PushNewContactRegisteredEvent", "receive from: $from")
            BusUtil.post(PushNewContactRegisteredEvent(from))
        }
    }
}