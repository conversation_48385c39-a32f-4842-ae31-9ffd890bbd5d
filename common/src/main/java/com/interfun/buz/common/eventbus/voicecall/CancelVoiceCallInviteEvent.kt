package com.interfun.buz.common.eventbus.voicecall

import com.interfun.buz.common.eventbus.BaseEvent
import com.jeremyliao.liveeventbus.LiveEventBus

/**
 * @Desc voice calling cancel event
 * @Author:<EMAIL>
 * @Date: 2024/3/10
 */
class CancelVoiceCallInviteEvent(val channelId: Long, val callType: Int?) : BaseEvent() {

    companion object {
        fun post(channelId: Long, callType: Int? = null) {
            LiveEventBus.get(CancelVoiceCallInviteEvent::class.java).post(
                CancelVoiceCallInviteEvent(channelId, callType)
            )
        }
    }

}