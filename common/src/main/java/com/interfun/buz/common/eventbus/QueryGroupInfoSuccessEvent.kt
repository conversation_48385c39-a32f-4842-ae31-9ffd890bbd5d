package com.interfun.buz.common.eventbus

import com.interfun.buz.common.database.entity.chat.GroupInfoBean
import com.jeremyliao.liveeventbus.LiveEventBus

/**
 * @Desc
 * @Author:lvzhen<PERSON><EMAIL>
 * @Date: 2022/7/15
 */
@Deprecated("兼容旧代码，新代码别用，请使用GroupRepository的getGroupCompositeFlow()等方法")
class QueryGroupInfoSuccessEvent(val groupInfoList: List<GroupInfoBean>, val noExistGroupList: List<Long>? = null): BaseEvent() {

    companion object{
        fun post(groupInfoList: List<GroupInfoBean>, noExistGroupList: List<Long>? = null){
            LiveEventBus.get(QueryGroupInfoSuccessEvent::class.java).post(QueryGroupInfoSuccessEvent(groupInfoList, noExistGroupList))
        }
    }

}