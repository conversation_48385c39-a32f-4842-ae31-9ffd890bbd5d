package com.interfun.buz.common.eventbus

import com.jeremyliao.liveeventbus.LiveEventBus

/**
 * @Desc 通讯录同步完成通知事件
 * @Author:l<PERSON><PERSON><PERSON><PERSON>@lizhi.fm
 * @Date: 2022/7/12
 */
class ContactsSyncCompleteEvent(val result: Boolean): BaseEvent() {

    companion object{
        fun post(result: Boolean){
            LiveEventBus.get(ContactsSyncCompleteEvent::class.java).post(ContactsSyncCompleteEvent(result))
        }
    }

}