package com.interfun.buz.common.interfaces

/**
 * Author: <PERSON><PERSON><PERSON>Sheng
 * Date: 2024/6/26
 * Email: chenyoush<PERSON>@vocalbeats.com
 * Desc:
 */
interface IStorageHelper {

    suspend fun getUserCacheSize(): Long

    suspend fun deleteUserCache()

    suspend fun deleteUserCacheAsync()

    fun autoDelete(day: Int)

    fun deleteSingleMsgCache(userId: Long, targetId: Long, convType: Int, servMsgId: Long)

    /**
     * @param deleteFrom
     */
    fun deleteBatchMsgCache(userId: Long, deleteFrom: DeleteCacheType, targetId: Long, convType: Int)
}

/**
 *  删除类型
 */
enum class DeleteCacheType {
    ClearChat,  // 清除聊天记录
    DeleteFriend, // 删除好友
    ExitGroup, // 退出群聊
    DeleteConv, // 删除会话
    Logout // 注销账号，用的是[deleteUserCache] 和 [deleteUserCacheAsync]
}