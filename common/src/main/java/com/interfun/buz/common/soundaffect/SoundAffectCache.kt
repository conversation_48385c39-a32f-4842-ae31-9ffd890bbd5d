package com.interfun.buz.common.soundaffect

import androidx.media3.common.util.UnstableApi
import com.interfun.buz.common.widget.view.CacheManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

@UnstableApi
object SoundAffectCache {

    private const val TAG = "SoundAffectCache"
    private val cacheManager = CacheManager(TAG,"soundboard")

    suspend fun cache(url: String): String? = withContext(Dispatchers.IO) {
        cacheManager.cache(url)
    }

}

