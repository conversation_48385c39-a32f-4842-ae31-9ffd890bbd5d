package com.interfun.buz.common.constants

class RouterSchemes {
    object Chat{
        const val PRIVATE_CHAT = "chat/private"
        const val GROUP_CHAT = "chat/group"
    }

    object VoiceCall{
        const val ONLINE_PRIVATE_CHAT = "voicecall/onlinePrivateChat"
        const val ONLINE_GROUP_CHAT = "voicecall/onlineGroupChat"
        const val GROUP_MEMBER_SELECT_DIALOG = "voicecall/groupRtcMemberSelectDialog"
        const val GROUP_CALL_EXIST_DIALOG = "voicecall/groupRtcExitDialog"
        const val APPRAISE_DLG = "voicecall/APPRAISE_DLG"

    }

    object LivePlace{
        const val LIVE_PLACE_OPEN = "livePlace/open"
        const val LIVE_PLACE_CREATE = "livePlace/create"
    }

    @Deprecated("unused")
    object OnAir{
        const val ONLINE_PRIVATE_CHAT = "onAir/onlinePrivateChat"
        const val ONLINE_GROUP_CHAT = "onAir/onlineGroupChat"
//        const val ON_AIR_PREVIEW_DIALOG = "onAir/onAirPreview"
        const val ON_AIR_EXCEPTION_DLG = "onAir/OnAirExceptionDlg"
    }

    object AI {
        const val TRANSLATOR_LANG_SETTING = "ai/translatorLangSetting"
        const val AI_BOT_LIST = "ai/botList"
    }

    object Home{
        const val LIST = "home/list"
    }

    object Common{
        const val FEATURE_NOTIFICATION = "common/featureNotification"
        const val ALERT_DIALOG = "common/alert"
        const val WEB_VIEW = "common/webView"
        const val SYS_WEB_VIEW = "system/webView"
        const val LOGIN_NOTIFICATION = "login/notification"
        const val PROMPT_DIALOG_FRAGMENT= "common/prompt_dialog_fragment"
    }

    object Personal{
        const val profile = "personal/profile"
        const val SETTING = "personal/setting"
        const val agreeFriendRequest = "personal/agreeFriendRequest"
        const val receiveFriendRequest = "personal/receiveFriendRequest"
    }

    object Group{
        const val GROUP_INFO = "group/info"
        const val GROUP_EDIT_INFO = "group/edit_info"
        const val INVITED_USER_TO_GROUP_INFO = "group/invite_user_to_group"
    }

    object Contact{
        const val FRIEND_REQUEST = "contact/friendRequestPage"
        const val ADD_FRIEND = "contact/addFriendPage"
        const val RECOMMEND_FRIEND = "contact/recommendFriendPage"
    }

    object Setting{
        const val NOTIFICATION_SETTING = "setting/notificationSetting"
    }

    object Campaign{
        const val CAMPAIGN_WebView = "campaign/webView"
    }

    object Guide {
        const val HOME_VOICE_MUTE = "guide/homeVoiceMute" // 打开首页左上角静音/非静音按钮
        const val HOME_FTUE_ONBOARDING = "guide/onboarding" // 首页新手引导
        const val HOME_FTUE_PTT_SETTING = "guide/pttSetting" // 首页新手引导完后，二次启动收到的官号消息，点击进入到权限设置页面
        const val HOME_FTUE_OFFICIAL_DND = "guide/dnd" // 官号卡片消息点击got it
    }

    companion object{
        /**
         * Check whether the scheme needs to interrupt wt mode
         */
        fun checkWTInterrupt(scheme:String): Boolean{
            return scheme == Chat.PRIVATE_CHAT || scheme == Chat.GROUP_CHAT
        }
    }
}