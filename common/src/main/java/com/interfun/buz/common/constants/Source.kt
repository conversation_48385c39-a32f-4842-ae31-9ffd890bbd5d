package com.interfun.buz.common.constants

/**
 * Author: Chen<PERSON>ouSheng
 * Date: 2023/5/18
 * Email: ch<PERSON><PERSON><PERSON><PERSON>@lizhi.fm
 * Desc: 存放各种页面跳转的source
 */

enum class QRCodeScanSource(val value: Int) {
    Home(1),  // 来自首页
    Contact(2),  // 来自通讯录
    QRCode(3), // 来自二维码页面
    AddFriend(4), // 来自添加好友页面
}

enum class ShareQRCodeSource(val value: Int) {
    UserProfile(1),//来自用户个人主页
    Scan(2), // 来自扫码页面
    AddFriend(3)//来自添加好友页面
}

enum class CreateGroupSource(val value: Int) {
    Contact(1), // 从通讯录页面建群
    FriendProfile(2), // 从好友主页建群
    InviteBotToGroup(3), // 机器人主页建群
    HomeList(4), //首页add 按钮
}

enum class SendMessageSource(val value: Int) {
    Home(0), // 从首页发送消息
    ChatList(1); // 从聊天列表发送的消息
}

// 预览相片的来源
enum class ImagePreviewSource(val value: Int) {
    Camera(0), // 来自相机拍照页
    Album(1); // 来自相册图片
}

enum class AddFriendSource(val value: Int) {
    AIGuideDialog(0), // 添加机器人引导弹窗
    AddFriendPage(1), // 添加好友页面推荐
    ContactHome(2), // 通讯录
    AIRecommendPage(3), // 机器人推荐页
    FromOfficialAccountPopWindow(8); // 来自公众号弹窗
}

enum class AddFriendPageSource(val value: Int) {
    ChatHomeAdd(1), // 首页+号入口
    Contacts(2), // 通讯录入口
    RegisterInviteDialog(3), // 首页邀请弹窗
    MissedBlindBox(4), // 有未领取盲盒，点击邀请好友补卡
    RegisteredFriendTips(5) // 首页右上角已注册好友推荐tips
}

enum class NotificationPageSource(val value: Int) {
    NotificationIntro(1), // 介绍弹窗
    OA(2), // 官号
    Other(3), // 其他
}


enum class UpdateAiSelectedSource(val value: Int) {
    HomePop(1), // 首页悬浮AI列表
    ChatPage(2), // 聊天页AI列表
}

enum class JumpToChatHomeSource(val value: Int) {
    FromInviteBotToGroup(1), // 来自邀请机器人进群（从群主页添加机器人到该群/从机器人主页选择加入的群）
    FromShowMuteGuideTip(101), // 用于展示语音mute引导提示 （从官方号消息路由过来）
    FromShare(2) // 来自三方分享一些图片文本后打开主页
}

enum class StartVoiceCallSource(val value: Int) {
    PrivateChatList(1), // come from private chat list
    GroupChatList(2), // come from group chat list
    UserProfile(3), // come from user profile
    GroupProfile(4), // come from group profile
    HomeKit(5)//home tool box
}

enum class IncomingRealTimeCallSource(val value: Int) {
    Notification(1),
    InAppNotification(2),
    CallKit(3)
}

enum class PhotoSelectSource(val value: Int) {
    Home(1), // 首页弹窗
    ChatEmbed(2), // 聊天页嵌入方式
    ChatDialog(3), // 聊天页弹窗
    Camera(4) // 相机拍照进入相册
}

enum class ShareContactSource(val value: Int) {
    Home(1), // 首页
    Chat(2), // 聊天页
    AiProfile(3), // Ai 个人资料页
    OfficialProfile(4), // 官号个人资料页
    UserProfile(5), // 用户个人资料页
    GroupProfile(6) // 群个人资料页
}

fun StartVoiceCallSource.isCallUser(): Boolean {
    return this == StartVoiceCallSource.PrivateChatList || this == StartVoiceCallSource.UserProfile
}

fun StartVoiceCallSource.isCallGroup(): Boolean {
    return this == StartVoiceCallSource.GroupChatList || this == StartVoiceCallSource.GroupProfile
}

