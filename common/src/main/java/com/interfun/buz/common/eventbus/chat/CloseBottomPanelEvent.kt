package com.interfun.buz.common.eventbus.chat

import com.interfun.buz.base.utils.BusUtil
import com.interfun.buz.common.eventbus.BaseEvent

/**
 * Author: ChenYouSheng
 * Date: 2024/5/7
 * Email: ch<PERSON><PERSON><PERSON><PERSON>@vocalbeats.com
 * Desc: 关闭聊天底部面板
 */
class CloseBottomPanelEvent : BaseEvent() {
    companion object {
        fun post() {
            BusUtil.post(CloseBottomPanelEvent())
        }
    }
}