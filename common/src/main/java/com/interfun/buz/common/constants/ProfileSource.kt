package com.interfun.buz.common.constants

/**
 * use for tracker report
 */
enum class ProfileSource(val source: Int) {
    NEW_FRIEND_NOTIFICATION(1),//好友新注册通知进入个人主页 /*use for UserProfileRouterConverter router*/
    GROUP(2),//群进入个人主页
    OTHER(3),//其他来源
    SHARE_TO_INVITE(4),//邀请链接来源（仅针对share to invite）/*use for UserProfileRouterConverter router*/
    QUICK_ADD(5),//Quick Add
    ALREADY_ON_BUZ(6),//Already on Buz
    PHONE_SEARCH(7),//手机号搜索结果页
    SCAN_QRCODE(8),//通过Buz扫二维码
    SEARCH_USER(9),//精准搜索
    SEARCH_PAGE_SUGGESTION(10),//点击搜索页推荐好友
    CLIP_BOARD(11),//剪切板
    IM_MESSAGE(12),//im 消息
    PUZZLE_GAME(13),//猜谜机器人弹窗
    CHAT_HISTORY_NAME(14), //点击历史聊天页Chat History中的 用户昵称进入
    ON_AIR_TITLE(15), //点击OnAir标题栏头像或名字
    ON_HOME_PAGE(16), //点击首页会话列表
    ON_PUSH(17), //点击推送
    ON_CONTACTS(18), //通讯录页
    ON_INVITE_LP_CARD(19), //live place邀请卡片
    CHAT_HEADER_LP(20), //聊天页顶部空间入口
}