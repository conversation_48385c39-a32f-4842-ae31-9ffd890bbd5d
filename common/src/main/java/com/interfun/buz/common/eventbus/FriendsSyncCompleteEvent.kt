package com.interfun.buz.common.eventbus

import com.interfun.buz.base.utils.BusUtil
import com.interfun.buz.common.refactor.ShouldDeleteAfterRefactoring

/**
 * @Desc
 * @Author:lvzhen<PERSON><EMAIL>
 * @Date: 2022/12/22
 */
@ShouldDeleteAfterRefactoring("这个需要兼容，后续所有依赖重构完可以移除，这个东西会导致很多内容很重")
@Deprecated("不要用这个，如果想监听好友是否有变更，可以使用 UserRepository.getAllFriendIdsFlow")
class FriendsSyncCompleteEvent : BaseEvent(){

    companion object{
        fun post(){
            BusUtil.post(FriendsSyncCompleteEvent())
        }
    }
}