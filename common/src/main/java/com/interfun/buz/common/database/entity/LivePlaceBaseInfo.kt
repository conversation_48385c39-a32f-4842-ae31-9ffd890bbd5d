package com.interfun.buz.common.database.entity

import android.os.Parcelable
import androidx.room.Embedded
import androidx.room.Entity
import androidx.room.Relation
import androidx.room.TypeConverter
import com.interfun.buz.common.database.entity.chat.GroupInfoBean
import com.interfun.buz.onair.standard.LivePlaceVisibleTypeEnum
import kotlinx.parcelize.Parcelize

enum class PlaceType(val serverNum: Int) {
    PRIVATE(1), GROUP(2), UNKNOWN(-1)
}

enum class ExistPlaceType(val serverNum: Int) {
    EXIST(1), NO_EXIST(2), PENDING(3)
}

class LivePlaceConverters {

    // 将 List<String> 转换为 String
    @TypeConverter
    fun fromHobbies(hobbies: PlaceType): Int {
        return hobbies.serverNum
    }

    // 将 String 转换回 List<String>
    @TypeConverter
    fun toHobbies(enumNum: Int): PlaceType {
        return when (enumNum) {
            PlaceType.GROUP.serverNum -> PlaceType.GROUP
            PlaceType.PRIVATE.serverNum -> PlaceType.PRIVATE
            else -> PlaceType.UNKNOWN
        }
    }
}

class LivePlaceVisibleTypeEnumConverters {

    // 将 List<String> 转换为 String
    @TypeConverter
    fun fromHobbies(hobbies: LivePlaceVisibleTypeEnum): Int {
        return hobbies.originNum
    }

    // 将 String 转换回 List<String>
    @TypeConverter
    fun toHobbies(enumNum: Int?): LivePlaceVisibleTypeEnum {
        return when (enumNum) {
            LivePlaceVisibleTypeEnum.EVERYONE.originNum ->LivePlaceVisibleTypeEnum.EVERYONE
            LivePlaceVisibleTypeEnum.ONLY_INVITING_USER.originNum -> LivePlaceVisibleTypeEnum.ONLY_INVITING_USER
            else -> LivePlaceVisibleTypeEnum.EVERYONE
        }
    }
}

class ExistPlaceTypeConverters {

    // 将 List<String> 转换为 String
    @TypeConverter
    fun fromHobbies(hobbies: ExistPlaceType): Int {
        return hobbies.serverNum
    }

    // 将 String 转换回 List<String>
    @TypeConverter
    fun toHobbies(enumNum: Int): ExistPlaceType {
        return when (enumNum) {
            ExistPlaceType.NO_EXIST.serverNum -> ExistPlaceType.NO_EXIST
            ExistPlaceType.EXIST.serverNum -> ExistPlaceType.EXIST
            else -> ExistPlaceType.PENDING
        }
    }
}

@Parcelize
@Entity(tableName = "liveplace_base_info", primaryKeys = ["uid","placeType"])
data class LivePlaceBaseInfo(
    val uid: Long,
    val placeId: Long,
    val topic: String,
    val placeType: PlaceType,
    val bgImgUrl: String,
    val isCustomizeBg: Boolean,
    val bgmUrl: String,
    val bgmName: String,
    val visibleType: LivePlaceVisibleTypeEnum,
    val startNotification: Boolean,
) : Parcelable

@Parcelize
@Entity(tableName = "liveplace_base_exist_info", primaryKeys = ["uid"])
data class LivePlaceBaseExistInfo(
    val uid: Long,
    val placeType: PlaceType,
    val existPlaceType: ExistPlaceType,
) : Parcelable

@Parcelize
@Entity(tableName = "liveplace_last_channel_id", primaryKeys = ["uid"])
data class LivePlaceLastChannelId(
    val uid: Long,
    val channelId: Long
) : Parcelable
