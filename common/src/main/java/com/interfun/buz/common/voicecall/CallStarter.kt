package com.interfun.buz.common.voicecall

import android.content.Context
import android.view.Gravity
import android.widget.Toast
import androidx.fragment.app.Fragment
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.R
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.arouter.startActivityByRouter
import com.interfun.buz.common.bean.chat.JumpStartRealTimeCallEntrance
import com.interfun.buz.common.bean.chat.JumpVoiceCallPageFrom
import com.interfun.buz.common.bean.chat.OnlineChatJumpInfo
import com.interfun.buz.common.bean.chat.OnlineChatJumpType
import com.interfun.buz.common.bean.voicecall.CallConflictState
import com.interfun.buz.common.bean.voicecall.CallRoomUser
import com.interfun.buz.common.bean.voicecall.CallType
import com.interfun.buz.common.bean.voicecall.ChannelType
import com.interfun.buz.common.constants.JUMP_INFO
import com.interfun.buz.common.constants.KEY_CALL_TYPE
import com.interfun.buz.common.constants.PATH_ACTIVITY_REAL_TIME_CALL
import com.interfun.buz.common.constants.RouterParamKey
import com.interfun.buz.common.constants.RouterSchemes
import com.interfun.buz.common.ktx.uid
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.manager.chat.ChannelPendStatusManager
import com.interfun.buz.common.manager.router.RouterManager
import com.interfun.buz.common.manager.voicecall.RealTimeCallStateChecker
import com.interfun.buz.common.manager.voicecall.VoiceCallPortal
import com.interfun.buz.common.service.RealTimeCallService
import com.interfun.buz.common.widget.dialog.CommonAlertDialog
import com.interfun.buz.onair.standard.IGlobalOnAirController
import com.interfun.buz.onair.standard.JoinConflictType
import com.lizhi.component.basetool.algorithm.Md5Util
import com.yibasan.lizhifm.sdk.platformtools.ResUtil
import kotlinx.coroutines.CoroutineScope
import org.json.JSONObject
import kotlin.Long

fun interface CallStarterHelper {
    fun startCall(): CallConflictState
}

fun interface CallJumpHelper {
    /**
     * 具体实现看generateJumpHelper()方法
     * @param jumpType 跳转类型，在[OnlineChatJumpType]中定义
     */
    fun startJump(jumpType: Int?)
}

fun interface SelectMemberDialogHelper {
    fun startShow()
}

fun interface CallJoinHelper {
    fun startJoin(): CallConflictState
}

data class CallStartResult(
    val startHelper: CallStarterHelper,
    val jumpHelper: CallJumpHelper,
    val curCallConflictState: CallConflictState
)

data class DialogResult(
    val fragment: Fragment?,
    val showDialogHelper: SelectMemberDialogHelper,
    val jumpHelper: CallJumpHelper,
    val curCallConflictState: CallConflictState
)

data class CallJoinResult(
    val joinHelper: CallJoinHelper,
    val jumpHelper: CallJumpHelper,
    val curCallConflictState: CallConflictState
)

enum class ActionType {
    JOIN, START
}

interface PrivateCallStater {
    fun startPrivateVoiceCall(
        targetId: Long,
        entrance: @JumpStartRealTimeCallEntrance Int
    ) {
        startRealTimeCall(
            targetId = targetId,
            callType = CallType.TYPE_VOICE,
            entrance = entrance
        ) { callStartResult ->
            if (callStartResult.curCallConflictState == CallConflictState.NO_CONFLICT) {
                val callConflictState = callStartResult.startHelper.startCall()
                if (callConflictState == CallConflictState.NO_CONFLICT) {
                    callStartResult.jumpHelper.startJump(OnlineChatJumpType.callInvitation)
                } else {
                    logInfo(
                        CallStarter.TAG,
                        "startPrivateVoiceCall fail:${callConflictState}",
                        logLine = LogLine.START_RTCAll
                    )
                }
            } else if (callStartResult.curCallConflictState == CallConflictState.ON_CALL_SAME) {
                callStartResult.jumpHelper.startJump(OnlineChatJumpType.reentryFromMinimize)
            }
        }
    }

    fun startPrivateVideoCall(
        targetId: Long,
        entrance: @JumpStartRealTimeCallEntrance Int
    ) {
        startRealTimeCall(
            targetId = targetId,
            callType = CallType.TYPE_VIDEO,
            entrance = entrance
        ) { callStartResult ->
            if (callStartResult.curCallConflictState == CallConflictState.NO_CONFLICT) {
                val callConflictState = callStartResult.startHelper.startCall()
                if (callConflictState == CallConflictState.NO_CONFLICT) {
                    callStartResult.jumpHelper.startJump(OnlineChatJumpType.callInvitation)
                } else {
                    logInfo(
                        CallStarter.TAG,
                        "startPrivateVideoCall fail:${callConflictState}",
                        logLine = LogLine.START_RTCAll
                    )
                }
            } else if (callStartResult.curCallConflictState == CallConflictState.ON_CALL_SAME) {
                callStartResult.jumpHelper.startJump(OnlineChatJumpType.reentryFromMinimize)
            }
        }
    }

    /**
     * 该方法不会立即创建房间，需要自行判断成功，并且调用[CallStarterHelper]#startCall方法可以得到[CallConflictState]，
     * 然后调用 [CallJumpHelper]的startJump进行房间跳转，可参考startPrivateVoiceCall和startPrivateVideoCall
     * */
    fun startRealTimeCall(
        targetId: Long,
        callType: @CallType Int,
        entrance: @JumpStartRealTimeCallEntrance Int,
        callStartResult: OneParamCallback<CallStartResult>? = null
    )
}

interface GroupCallStater {

    fun showGroupCallSelectMemberDialog(
        context: Context,
        groupId: Long,
        callType: @CallType Int,
        actionType: ActionType,
        entrance: @JumpStartRealTimeCallEntrance Int
    ) {
        showGroupCallSelectMemberDialog(
            groupId = groupId,
            callType = callType,
            actionType = actionType,
            entrance = entrance
        ) { result ->
            if (result.curCallConflictState == CallConflictState.ON_CALL_SAME) {
                // 在当前房间，点击直接加入
                result.jumpHelper.startJump(OnlineChatJumpType.reentryFromMinimize)
            } else if (result.curCallConflictState == CallConflictState.NO_CONFLICT) {
                if (result.fragment?.isAdded == true) {
                    result.showDialogHelper.startShow()
                }
            }
        }
    }

    /**该方法不会立即看到选择弹窗，需要调用[SelectMemberDialogHelper]#startShow方法 */
    fun showGroupCallSelectMemberDialog(
        groupId: Long,
        callType: @CallType Int,
        actionType: ActionType,
        entrance: @JumpStartRealTimeCallEntrance Int,
        callResult: OneParamCallback<DialogResult>? = null
    )

    fun startGroupVoiceCall(
        context: Context,
        groupId: Long,
        selectMember: List<CallRoomUser>,
        entrance: @JumpStartRealTimeCallEntrance Int,
        onStartSuccess: (() -> Unit)? = null,
    ) {
        startRealTimeCall(
            groupId = groupId,
            callType = CallType.TYPE_VOICE,
            selectMember = selectMember,
            entrance = entrance
        ) { callResult ->
            if (callResult.curCallConflictState == CallConflictState.NO_CONFLICT) {
                val callConflictState = callResult.startHelper.startCall()
                if (callConflictState == CallConflictState.NO_CONFLICT) {
                    onStartSuccess?.invoke()
                    callResult.jumpHelper.startJump(OnlineChatJumpType.callInvitation)
                } else {
                    logInfo(
                        CallStarter.TAG,
                        "startGroupVoiceCall fail:${callConflictState}",
                        logLine = LogLine.START_RTCAll
                    )
                }
            } else if (callResult.curCallConflictState == CallConflictState.ON_CALL_SAME) {
                onStartSuccess?.invoke()
                callResult.jumpHelper.startJump(OnlineChatJumpType.reentryFromMinimize)
            }
        }
    }

    fun startGroupVideoCall(
        context: Context,
        groupId: Long,
        selectMember: List<CallRoomUser>,
        entrance: @JumpStartRealTimeCallEntrance Int,
        onStartSuccess: (() -> Unit)? = null
    ) {
        startRealTimeCall(
            groupId = groupId,
            callType = CallType.TYPE_VIDEO,
            selectMember = selectMember,
            entrance = entrance
        ) { callResult ->
            if (callResult.curCallConflictState == CallConflictState.NO_CONFLICT) {
                val callConflictState = callResult.startHelper.startCall()
                if (callConflictState == CallConflictState.NO_CONFLICT) {
                    onStartSuccess?.invoke()
                    callResult.jumpHelper.startJump(OnlineChatJumpType.callInvitation)
                } else {
                    logInfo(
                        CallStarter.TAG,
                        "startGroupVideoCall fail:${callConflictState}",
                        logLine = LogLine.START_RTCAll
                    )
                }
            } else if (callResult.curCallConflictState == CallConflictState.ON_CALL_SAME) {
                onStartSuccess?.invoke()
                callResult.jumpHelper.startJump(OnlineChatJumpType.reentryFromMinimize)
            }
        }
    }

    /**
     * 该方法不会立即创建房间，需要自行判断成功，并且调用[CallStarterHelper]#startCall方法可以得到[CallConflictState]，
     * 然后调用 [CallJumpHelper]的startJump进行房间跳转，可参考startGroupVoiceCall和startGroupVideoCall
     * */
    fun startRealTimeCall(
        groupId: Long,
        callType: @CallType Int,
        selectMember: List<CallRoomUser>,
        entrance: @JumpStartRealTimeCallEntrance Int,
        callStartResult: OneParamCallback<CallStartResult>? = null
    )


    fun joinGroupVoiceCall(
        context: Context,
        groupId: Long,
        channelId: Long,
        @CallType callType: Int,
        source: @JumpVoiceCallPageFrom Int? = null,
        callback: ((CallConflictState) -> Unit)? = null
    ) {
        joinRealTimeCall(
            groupId = groupId,
            channelId = channelId,
            callType = callType,
            source = source
        ) { joinResult ->
            val joinAction = {
                if (callType == CallType.TYPE_VOICE) {
                    val callConflictState = joinResult.joinHelper.startJoin()
                    callback?.invoke(callConflictState)
                    if (callConflictState == CallConflictState.NO_CONFLICT) {
                        joinResult.jumpHelper.startJump(OnlineChatJumpType.joinChannel)
                    }
                } else {
                    callback?.invoke(CallConflictState.NO_CONFLICT)
                    com.interfun.buz.common.arouter.NavManager.startOnlineChatActivity(
                        channelId = channelId,
                        channelType = ChannelType.TYPE_VOICE_CALL_GROUP,
                        callType = callType,
                        targetId = groupId,
                        jumpType = OnlineChatJumpType.pendAnswerByHome,
                        jumpFrom = JumpVoiceCallPageFrom.PENDING_PAGE
                    )
                }
            }
            if (joinResult.curCallConflictState == CallConflictState.ON_AIR) {
                val curLP = routerServices<IGlobalOnAirController>().value?.curOnAirContext()
                if (curLP != null) {
                    CommonAlertDialog(
                        context = context,
                        title = R.string.accept_new_call.asString(),
                        tips = R.string.answer_new_call_tip.asString(),
                        positiveText = R.string.call_answer.asString(),
                        negativeText = R.string.cancel.asString(),
                        positiveCallback = {
                            it.dismiss()
                            routerServices<IGlobalOnAirController>().value?.leaveCurOnAirByJoinConflict(
                                JoinConflictType.VC
                            )
                            joinAction()
                        },
                        negativeCallback = {
                            it.dismiss()
                            callback?.invoke(CallConflictState.ON_AIR)
                        }).show()
                }
            } else if (joinResult.curCallConflictState == CallConflictState.ON_CALL) {
                val room = VoiceCallPortal.currentRoom.value
                val tips=if (room?.callType==CallType.TYPE_VIDEO){
                    R.string.rtc_stop_groupvideocall_to_new_call.asString()
                }else{
                    R.string.rtc_stop_groupvoicecall_to_new_call.asString()
                }
                CommonAlertDialog(
                    context = context,
                    title = tips,
                    positiveText = R.string.call_answer.asString(),
                    negativeText = R.string.cancel.asString(),
                    positiveCallback = {
                        it.dismiss()
                        VoiceCallPortal.currentRoom.value?.hangUp{
                            joinAction.invoke()
                        }
                    },
                    negativeCallback = {
                        it.dismiss()
                        callback?.invoke(CallConflictState.ON_CALL)
                    }).show()
            } else if (joinResult.curCallConflictState == CallConflictState.ON_CALL_SAME) {
                joinResult.jumpHelper.startJump(OnlineChatJumpType.reentryFromMinimize)
                callback?.invoke(CallConflictState.NO_CONFLICT)
            } else if (joinResult.curCallConflictState == CallConflictState.NO_CONFLICT) {
                joinAction()
                callback?.invoke(CallConflictState.NO_CONFLICT)
            } else if (joinResult.curCallConflictState == CallConflictState.BEING_INVITED) {
                if (ChannelPendStatusManager.statusFlow.value.second?.targetId == groupId) {
                    joinAction()
                } else {
                    toastIconFontMsg(
                        message = ResUtil.getString(R.string.incoming_call_try_again),
                        textColor = R.color.text_white_main.asColor(),
                        iconFont = R.string.ic_tel.asString(),
                        iconFontColor = R.color.text_white_main.asColor(),
                        gravity = Gravity.CENTER,
                        duration = Toast.LENGTH_SHORT,
                        style = IconToastStyle.ICON_TOP_TEXT_BOTTOM
                    )
                    callback?.invoke(joinResult.curCallConflictState)
                }

            } else {
                callback?.invoke(joinResult.curCallConflictState)
            }
        }
    }


    /**该方法不会立即加入房间，需要自行判断成功，调用[CallJoinHelper]#startJoin方法的可以得到[CallConflictState]，
     * 然后调用 [CallJumpHelper]的startJump进行房间跳转 参考joinGroupVoiceCall和joinGroupVideoCall*/
    fun joinRealTimeCall(
        groupId: Long,
        channelId: Long,
        callType: @CallType Int,
        source: @JumpVoiceCallPageFrom Int? = null,
        joinResult: OneParamCallback<CallJoinResult>? = null
    )
}


abstract class CallStarter(val fragment: Fragment) {
    private val realTimeCallStateChecker = RealTimeCallStateChecker(fragment)
    private val startErrorHandler = CallStartErrorHandler(fragment)
    protected val realTimeCallService = routerServices<RealTimeCallService>().value
    protected val scope: CoroutineScope? get() = realTimeCallService?.getVoiceCallPortalScope()

    companion object {
        const val TAG = "CallStarter"
    }


    /**
     * 发起通话，加入通话前置条件检查
     * @param onResult : 成功和失败都会返回
     * @param onFailure : 失败回调
     * @param onSuccess : 成功回调
     * */
    fun doPreCheck(
        targetId: Long,
        channelType: @ChannelType Int,
        callType: @CallType Int,
        userList: List<CallRoomUser>? = null,
        onResult: ((CallConflictState) -> Unit)? = null,
        onFailure: ((CallConflictState) -> Unit)? = null,
        onSuccess: () -> Unit = {},
        actionType: ActionType
    ) {
        realTimeCallStateChecker.startPreCheckAll(
            targetId = targetId,
            channelType = channelType,
            callType = callType,
            userList = userList,
            actionType = actionType
        ) { callConflictState ->
            logInfo(
                TAG,
                "doPreCheck callConflictState: $callConflictState",
                logLine = LogLine.START_RTCAll
            )
            onResult?.invoke(callConflictState)
            if (callConflictState == CallConflictState.NO_CONFLICT) {
                onSuccess.invoke()
            } else if (onFailure != null) {
                onFailure.invoke(callConflictState)
            } else {
                handlePreCheckResult(targetId, channelType, callType, callConflictState, actionType)
            }
        }
    }

    /**返回真正处理开启通话的实现函数*/
    fun createStartHelper(
        userList: List<CallRoomUser>,
        targetId: Long,
        callType: @CallType Int,
        channelType: @ChannelType Int,
        source: @JumpVoiceCallPageFrom Int? = null,
        traceId: String,
        onCallStarted: ((CallConflictState) -> Unit)? = null
    ): Pair<CallJumpHelper, CallStarterHelper> {
        val callStarterHelper = CallStarterHelper {
            val callConflictState = realTimeCallService?.startRealTimeCall(
                userList = userList,
                channelType = channelType,
                callType = callType,
                groupId = targetId,
                traceId = traceId
            ) ?: CallConflictState.OTHER_CONFLICT
            onCallStarted?.invoke(callConflictState)
            callConflictState
        }
        val callJumpHelper = generateJumpHelper(
            channelType = channelType,
            callType = callType,
            targetId = targetId,
            source = source,
            traceId = traceId,
            inChannelId = null
        )
        return Pair(callJumpHelper, callStarterHelper)
    }


    /**返回真正处理打开群成员列表选择弹窗的实现函数*/
    fun createShowSelectMemberDialogHelper(
        groupId: Long,
        callType: @CallType Int,
        channelType: @ChannelType Int,
        entrance: @JumpStartRealTimeCallEntrance Int,
    ): Pair<CallJumpHelper, SelectMemberDialogHelper> {
        val callJumpHelper =
            generateJumpHelper(
                channelType = channelType,
                callType = callType,
                targetId = groupId,
                inChannelId = null
            )
        val dialogHelper = SelectMemberDialogHelper {
            logInfo(TAG, "getGroupCallDialog: show", logLine = LogLine.START_RTCAll)
            RouterManager.handle(
                fragment.requireActivity(),
                RouterSchemes.VoiceCall.GROUP_MEMBER_SELECT_DIALOG,
                JSONObject(
                    mapOf(
                        RouterParamKey.Group.KEY_GROUP_ID to groupId,
                        RouterParamKey.Group.KEY_CALL_TYPE to callType,
                        RouterParamKey.Group.KEY_FROM_ENTRANCE to entrance
                    )
                ).toString()
            )
        }
        return Pair(callJumpHelper, dialogHelper)
    }

    /**返回真正处理加入实时通话的实现函数*/
    fun createJoinHelper(
        targetId: Long,
        channelId: Long,
        callType: @CallType Int,
        source: @JumpVoiceCallPageFrom Int?,
        onCallStarted: ((CallConflictState) -> Unit)? = null
    ): Pair<CallJumpHelper, CallJoinHelper> {
        val joinHelper = CallJoinHelper {
            val callConflictState = realTimeCallService?.joinRealTimeCall(
                channelId = channelId,
                targetId = targetId,
                channelType = ChannelType.TYPE_VOICE_CALL_GROUP,
                callType = callType,
                source = if (source == JumpVoiceCallPageFrom.GROUP_INVITE_MSG) 3 else 2
            ) ?: CallConflictState.OTHER_CONFLICT
            onCallStarted?.invoke(callConflictState)
            callConflictState
        }
        val jumpHelper = generateJumpHelper(
            channelType = ChannelType.TYPE_VOICE_CALL_GROUP,
            callType = callType,
            targetId = targetId,
            source = source,
            inChannelId = channelId
        )
        return Pair(jumpHelper, joinHelper)
    }

    fun handleRealTimeCallError(
        error: CallConflictState,
        actionType: ActionType,
        targetId: Long,
        callType: @CallType Int
    ) {
        logInfo(
            TAG,
            "handleRealTimeCallError error:${error}, actionType:${actionType}, targetId:${targetId}",
            logLine = LogLine.START_RTCAll
        )
        startErrorHandler.handle(error, actionType, targetId, callType)
    }

    fun generateTraceId(): String {
        // Combine user ID, timestamp
        val data = UserSessionManager.uid.toString() + System.currentTimeMillis()
        return Md5Util.getStringMD5String(data)
    }

    /**前置条件检查失败处理*/
    protected abstract fun handlePreCheckResult(
        targetId: Long,
        channelType: @ChannelType Int,
        callType: @CallType Int,
        callConflictState: CallConflictState,
        actionType: ActionType

    )

    private fun generateJumpHelper(
        channelType: @ChannelType Int,
        callType: @CallType Int,
        targetId: Long,
        source: @JumpVoiceCallPageFrom Int? = null,
        traceId: String? = null,
        inChannelId: Long?
    ): CallJumpHelper = CallJumpHelper { jumpType ->
        val channelId = if (jumpType == OnlineChatJumpType.reentryFromMinimize) {
            val currentRoom = VoiceCallPortal.currentRoom.value
            currentRoom?.roomChannelId
        } else inChannelId
        val jumpInfo = OnlineChatJumpInfo(
            channelId = channelId,
            channelType = channelType,
            callType = callType,
            targetId = targetId,
            jumpType = jumpType ?: OnlineChatJumpType.callInvitation,
            traceId = traceId
        )
        val paramsList = arrayListOf<Pair<String, Any?>>(
            RouterParamKey.Common.JUMP_INFO to jumpInfo
        )
        if (null != source) {
            paramsList.add(RouterParamKey.ChannelInvite.KEY_JUMP_INVITE_PAGE_FROM to source)
        }
        logInfo(
            TAG, "generateJumpHelper: startActivityByRouter targetId:${targetId}, " +
                    "callType:${callType}, channelType:${channelType}, source:${source}" +
                    ", jumpType:${jumpType}, channelId:${channelId}",
            logLine = LogLine.START_RTCAll
        )
        startActivityByRouter(
            path = PATH_ACTIVITY_REAL_TIME_CALL,
            pairs = paramsList.toTypedArray(),
            buildPostcardBlock = {
                withTransition(R.anim.anim_dialog_theme_enter, R.anim.anim_dialog_theme_out)
            })
    }

}