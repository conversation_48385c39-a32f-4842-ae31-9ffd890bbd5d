package com.interfun.buz.common.constants

val RouterParamKey.ICommon.USER_ID get() = "userId"
val RouterParamKey.ICommon.USER_PORTRAIT get() = "userPortrait"
val RouterParamKey.ICommon.USER_NAME get() = "userName"
val RouterParamKey.ICommon.KEY_ROUTER get() = "router"
val RouterParamKey.ICommon.JUMP_INFO get() = "jumpInfo"
val RouterParamKey.ICommon.KEY_SOURCE get() = "source"
val RouterParamKey.ICommon.KEY_CHANNEL_TYPE get() = "channelType"
val RouterParamKey.ICommon.KEY_CALL_TYPE get() = "callType"
val RouterParamKey.ICommon.BUSINESS_ID get() = "businessId"
val RouterParamKey.ICommon.KEY_A_ROUTER_PATH get() = "ARouterPath"
val RouterParamKey.ICommon.KEY_TYPE get() = "type"
val RouterParamKey.ICommon.KEY_LINK_HASH get() = "linkHash"
val RouterParamKey.ICommon.KEY_TRANSITION_NAIM get() = "transition_anim"

class RouterParamKey {

    interface ICommon

    object Common : ICommon {
        const val KEY_URL = "url"
        const val KEY_BUNDLE = "bundle"
        const val KEY_SECOND_ROUTER = "secondRouter"
        const val KEY_IS_FIRST_LAUNCH = "isFirstLaunch"
        const val KEY_IS_FROM_OVERLAY = "fromOverlay"
        const val KEY_EXCLUSIVE_ID = "exclusiveId"
        const val KEY_HOST_DISABLE_ACTIVITY_ANIM = "hideWindowAnim"
        const val KEY_PROMPT_VALUE = "prompt_value"
    }

    object WebView : ICommon {
        /**
         * 是否显示原生的标题栏 默认为false
         */
        const val KEY_SHOW_NATIVE_TITLE_BAR = "titleBar"

        /**
         * 仅在[KEY_SHOW_NATIVE_TITLE_BAR]为true时生效。
         * 默认情况下：原生的标题栏内容会不断动态从webview加载的网址中获取，
         * 但是你传入这个参数时会固化为传入的标题
         */
        const val KEY_FORCE_TITLE = "forceTitle"

        /**
         * 仅在[KEY_SHOW_NATIVE_TITLE_BAR]为true时生效
         * 默认情况下：原生的标题栏内容会不断动态从webview加载的网址中获取，但是加载网页需要一定时间。
         * 在此期间使用次参数的标题栏
         */
        const val KEY_PLACEHOLDER_TITLE = "placeholderTitle"
    }

    object Startup : ICommon {
        const val KEY_LOG_PUSH_CLICK = "logPushClick"
        const val KEY_LOG_PUSH_MODEL = "logPushModel"
        const val KEY_PLATFORM_PUSH_BEAN = "platformPushBean"
        const val KEY_PUSH_PAYLOAD = "pushPayload"
        const val KEY_IS_FROM_ONLINE_NOTIFICATION = "isFromOnlineNotification"
        const val KEY_PUSH_GROUP_ID = "pushGroupId"
        const val KEY_PUSH_DEVICE_ID = "pushDeviceId"
        const val KEY_PUSH_TOKEN = "pushToken"
        const val KEY_PUSH_FROM = "pushFrom"
        const val KEY_PUSH_CHANNEL = "pushChannel"
    }

    object ChatHome : ICommon {
        const val KEY_WT_TARGET_TYPE = "wtTargetType"//1是私聊 2群聊
        const val KEY_WT_TARGET_ID = "wtTargetId" //Used to locate the home page list by targetId
        const val KEY_WT_NEED_CHAT_HISTORY = "wtOpenHistory" //需要顺带打开历史聊天页当
        const val KEY_OPEN_SOURCE = "keyOpenSource"
        const val KEY_USER_INFO = "userInfo"
        const val KEY_OPEN_VE_PANEL = "openVePanel"
        const val KEY_OPEN_MORE_PANEL = "openMorePanel"
        const val KEY_CREATE_LIVE_PLACE = "createLivePlace"
        const val KEY_ENABLE_DND = "enableDND"
    }

    object Chat : ICommon {
        const val KEY_SER_MSG_ID = "serMsgId"
        const val KEY_TARGET_ID = "targetId"
        const val KEY_MESSAGE_ID = "msgId"
        const val KEY_IS_PRIVATE = "isPrivate"
        const val KEY_IS_VIDEO = "isVideo"
        const val KEY_REACTION_OP_USER_ID = "reactionOpUserId"
        const val KEY_MEDIA_ITEM = "MEDIA_ITEM"
        const val KEY_TRANSITION_NAME = "TRANSITION_NAME"
        const val KEY_TRANSITION_PREFIX_NAME = "TRANSITION_PREFIX_NAME"
        const val KEY_MEDIA_PREVIEW_SHOW_HISTORY = "SHOW_HISTORY"
        const val KEY_MSG_ID = "msgId"
        const val KEY_OPEN_VOICE_FILTER = "openVoiceFilter"
        const val KEY_VOICE_FILTER_ID = "voiceFilterId"
    }

    object Contact : ICommon {
        const val TYPE_ADD = 0
        const val TYPE_EDIT = 1
    }

    object Group : ICommon {
        const val KEY_USER_IDS = "KEY_USER_IDS"
        const val KEY_GROUP_INFO = "KEY_GROUP_INFO"
        const val KEY_GROUP_ID = "KEY_GROUP_ID"
        const val KEY_GROUP_NAME = "KEY_GROUP_NAME"
        const val KEY_GROUP_PORTRAIT = "KEY_GROUP_PORTRAIT"
        const val KEY_MEMBER_USER_IDS = "KEY_MEMBER_USER_IDS"
        const val KEY_GROUP_ID_LOWER_CASE = "groupId"
        const val KEY_INVITER_ID_LOWER_CASE = "inviterId"
        const val KEY_CURRENT_MEMBER_NUM_COUNT = "memberNum"
        const val KEY_PAGE_INIT_STATE = "KEY_PAGE_INIT_STATE"
        const val KEY_FROM_ENTRANCE = "KEY_FROM_ENTRANCE"
        const val KEY_IS_FROM_CREATE_GROUP = "KEY_IS_FROM_CREATE_GROUP"
        const val KEY_TO_HISTORY_PAGE_AFTER_CLOSE = "KEY_TO_HISTORY_PAGE_AFTER_CLOSE"
        const val KEY_GROUP_DEFAULT_SELECTED_IDS = "KEY_GROUP_DEFAULT_SELECTED"
        const val KEY_USE_OVERRIDE_PENDING_TRANSITION = "KEY_USE_OVERRIDE_PENDING_TRANSITION"
        const val KEY_BACK_ICON_FONT = "KEY_BACK_ICON_FONT"
        const val KEY_TRACER_SOURCE = "KEY_TRACKER_SOURCE"
    }

    object Alert : ICommon {
        const val KEY_ALERT_TITLE = "title"
        const val KEY_ALERT_CONTENT = "content"
        const val KEY_ALERT_CONFIRM_TEXT = "confirmText"
    }

    object Login : ICommon {
        const val KEY_IS_LOGIN = "KEY_IS_LOGIN"
        const val TYPE_BINDING_PHONE = "TYPE_BINDING_PHONE"
        const val TYPE_BINDING_EMAIL = "TYPE_BINDING_EMAIL"
        const val SOURCE_SETTING = "settings"
        const val SOURCE_GUIDE = "guide_button"
        const val SOURCE_LOGOUT = "logout"
        const val SOURCE_CONTACT_LOGIN = "contact_login"
        const val SOURCE_GG_REGISTER_REQUEST_PERMISSION = "gg_register_request_permission_fragment"
        const val KEY_INPUT_TAB ="KEY_INPUT_TAB"
        const val KEY_PAGE ="page"
        const val EDIT_FULL_NAME_DEFAULT = "edit_full_name_default_value"

    }

    object ShareQRCode : ICommon {
        const val KEY_SHARE_DATA = "KEY_SHARE_DATA"
    }

    object UserProfile : ICommon {
        const val KEY_AFTER_REGISTER = "KEY_AFTER_REGISTER"
        const val KEY_CONTACT_NAME = "KEY_CONTACT_NAME"
        const val KEY_TRACKER_SOURCE = "KEY_TRACKER_SOURCE" //use for tracker report
        const val KEY_TRACKER_AF_LINK_HASH = "KEY_TRACKER_AF_LINK_HASH" //use for tracker report
        const val KEY_IS_FROM_GROUP = "KEY_IS_FROM_GROUP"
        const val KEY_IS_FROM_BLIND_BOX = "KEY_IS_FROM_BLIND_BOX"
        const val KEY_SHOULD_AUTO_ADD_FRIEND = "KEY_SHOULD_AUTO_ADD_FRIEND"
    }

    object User : ICommon {
        const val TYPE_UPDATE_USER_NAME = "TYPE_UPDATE_USER_NAME"
        const val TYPE_UPDATE_BUZ_ID = "TYPE_UPDATE_BUZ_ID"
        const val UPDATE_BUZ_ID_IN_REGISTRATION = 1
        const val UPDATE_BUZ_ID_IN_PROFILE = 2
    }

    object OnAir: ICommon{
        const val TARGET_ID = "targetId"
        const val TYPE = "type"
        const val SOURCE = "source"
    }

    object Feedback : ICommon {
        //返回添加的前缀
        const val KEY_PREFIX = "PREFIX"

        const val FEEDBACK_SOURCE_UN_KNOWN = -1

        //评分弹框，满足条件自动弹出来
        const val FEEDBACK_SOURCE_RATING = 0

        //自己个人资料页（设置页）
        const val FEEDBACK_SOURCE_SETTING = 1

        //邮箱收集（满足使用频率弹窗）
        const val FEEDBACK_SOURCE_COLLECT_EMAIL = 2

        //机器人资料页反馈入口
        const val FEEDBACK_SOURCE_ROBOT = 3

        //AI机器人TOPIC选择页反馈入口
        const val FEEDBACK_SOURCE_AI_TOPIC_FAQ = 4

        const val FEEDBACK_SOURCE_LIVE_PLACE = 5
        //语音通话结束页反馈
        const val FEEDBACK_SOURCE_VOICE_CALL = 6
        //视频通话结束页反馈
        const val FEEDBACK_SOURCE_VIDEO_CALL = 7
        //注册页面反馈
        const val FEEDBACK_SOURCE_REGISTER = 8
        //帮助中心反馈
        const val FEEDBACK_SOURCE_HELP_CENTER = 9
    }

    object AI : ICommon {
        const val KEY_ROBOT_ID = "robotId"

        //未知
        const val AIMARKET_SOURCE_UNKNOWN = -1

        // 评分弹框，满足条件自动弹出来
        const val AiMarket_SOURCE_HOMEPAGE = 0

        // 添加好友处
        const val AiMarket_SOURCE_ADDFRIEND = AiMarket_SOURCE_HOMEPAGE + 1

        // 通讯录
        const val AiMarket_SOURCE_CONTACT = AiMarket_SOURCE_ADDFRIEND + 1

        //其他
        const val AiMarket_SOURCE_OTHER = AiMarket_SOURCE_CONTACT + 1
    }

    object Album : ICommon {
        const val KEY_TARGET_ID = "targetId"
        const val KEY_CONV_TYPE = "convType"
        const val KEY_FULL_SCREEN = "isFullScreen"
        const val KEY_REFERENCE_MSG_ID = "key_reference_msg_id"

        //相册预览: 点击Preview按钮预览所选择的媒体
        const val ALBUM_SOURCE_ALBUM_SELECT_TYPE = 2
        //相册预览：点击媒体选择列表的item进入的大图预览
        const val ALBUM_SOURCE_ALBUM_ALL_TYPE = 3
        //相机预览: 拍完照后跳转预览页
        const val ALBUM_SOURCE_TAKE_PHOTO_TYPE = 1

        //发送源: 选择媒体列表
        const val ALBUM_SEND_SOURCE_PHOTO_SELECT_FRAGMENT = 1
        //发送源: 预览页
        const val ALBUM_SEND_SOURCE_MEDIA_PREVIEW_FRAGMENT = 2
        //发送源: 外部app分享
        const val ALBUM_SEND_SOURCE_EXTERN_SHARED = 3

        //当前位置
        const val KEY_PREVIEW_INDEX = "KEY_PREVIEW_INDEX"

        const val KEY_PREVIEW_DATA = "KEY_PREVIEW_DATA"
        const val KEY_PREVIEW_FOLDER_BUCKET_ID = "KEY_PREVIEW_FOLDER_BUCKET_ID"
        const val KEY_PREVIEW_FOLDER_MEDIA_COUNT = "KEY_PREVIEW_FOLDER_MEDIA_COUNT"

        const val KEY_SEND_MEDIA_CALLBACK = "KEY_SEND_MEDIA_CALLBACK"
        const val KEY_SEND_MEDIA_SELECTED_HD = "KEY_SEND_MEDIA_SELECTED_HD"
        const val KEY_IS_ALBUM_FULL_SCREEN = "KEY_IS_ALBUM_FULL_SCREEN"

    }

    object ChannelInvite: ICommon{
        const val KEY_CHANNEL_ID = "channelId"
        const val KEY_JUMP_TYPE = "jumpType"
        const val KEY_CALLER_USER_ID = "callerUserId"//发起者userId
        const val KEY_GROUP_ID = "groupId"
        const val KEY_TARGET_ID = "targetId"
        const val KEY_SOURCE_FROM_ANSWER = "sourceFromAnswer"
        const val KEY_JUMP_INVITE_PAGE_FROM = "joinVoiceCallPageFrom"
        const val KEY_CHANNEL_TYPE = "channelType"
        const val KEY_CALL_TYPE = "callType"
        const val KEY_REAL_TIME_CALL_PEND_STATE = "realTimeCallPendState"
    }
    object VC: ICommon{
        const val KEY_USER_INFO = "KEY_USER_INFO"
        const val KEY_GROUP_INFO = "KEY_GROUP_INFO"
        const val KEY_ROOM_INFO = "KEY_ROOM_INFO"
        const val KEY_H5_FEEDBACK_SOURCE = "KEY_H5_FEEDBACK_SOURCE"
    }

    object LivePlace: ICommon{
        const val KEY_TARGET_ID = "targetId"
        const val KEY_PLACE_TYPE = "placeType"
        const val KEY_SOURCE = "source"
    }

    object Personal: ICommon{
        const val KEY_TYPE = "type"
    }

    object ShareContact: ICommon{
        const val KEY_SHARE_INFO = "key_share_info"
    }
}