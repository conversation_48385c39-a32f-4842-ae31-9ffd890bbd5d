package com.interfun.buz.common.eventbus

import com.interfun.buz.base.utils.BusUtil

class BackPressEvent():BaseEvent(){
    companion object{
        fun post(key: String){
            BusUtil.post(key,BackPressEvent())
        }
    }
}

class BackPressKey{
    companion object{
        const val GROUP_INFO_DIALOG = "GroupInfoDialog"
        const val PROFILE_DIALOG_FRAGMENT = "ProfileDialogFragment"
    }
}
