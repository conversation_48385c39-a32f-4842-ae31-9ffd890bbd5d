package com.interfun.buz.common.eventbus.voicecall

import android.app.Activity
import com.interfun.buz.common.bean.voicecall.CallType
import com.interfun.buz.common.constants.KEY_TYPE
import com.interfun.buz.common.constants.RouterParamKey
import com.interfun.buz.common.constants.RouterSchemes
import com.interfun.buz.common.manager.router.RouterManager
import org.json.JSONObject

/**
 * Author: ChenYouSheng
 * Date: 2025/3/25
 * Email: <EMAIL>
 * Desc:
 */
class ExcGroupOnCallDialogEvent {

    companion object {
        const val TYPE_JOIN_CALL_FAILURE = 0
        const val TYPE_JOIN_SAME_ROOM = 1

        fun post(
            activity: Activity,
            type: Int? = TYPE_JOIN_CALL_FAILURE,
            channelId: Long? = null,
            targetId: Long? = null,
            callType: @CallType Int? = null
        ) {
            RouterManager.handle(
                activity,
                RouterSchemes.VoiceCall.GROUP_CALL_EXIST_DIALOG,
                JSONObject(
                    mapOf(
                        RouterParamKey.ChannelInvite.KEY_TARGET_ID to targetId,
                        RouterParamKey.ChannelInvite.KEY_CHANNEL_ID to channelId,
                        RouterParamKey.ChannelInvite.KEY_CALL_TYPE to callType,
                        RouterParamKey.ChannelInvite.KEY_TYPE to type
                    )
                ).toString()
            )
        }
    }
}