package com.interfun.buz.common.ktx

import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import com.interfun.buz.base.ktx.DefaultCallback
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.mapNotNull

/**
 * 自动获取 DataStore 中的值，如无则写入默认值，并提供变化监听。
 *
 * @param key DataStore Preferences 的键
 * @param defaultProvider 默认值提供器（suspend，可从磁盘、远端等异步计算）
 */
fun <T> DataStore<Preferences>.getOrSetDefault(
    key: Preferences.Key<T>,
    defaultProvider: suspend () -> T
): Flow<T> = flow {
    val prefs = data.first()
    val existing = prefs[key]

    val value = if (existing != null) {
        existing
    } else {
        val default = defaultProvider()
        edit { it[key] = default }
        default
    }
    // emit original value or default value
    emit(value)

    // emit datastore flow
    val dataFlow = data.mapNotNull { it[key] }.distinctUntilChanged()
    emitAll(dataFlow)
}.flowOn(Dispatchers.IO)


// 默认的数据迁移实现
class DefaultPreferencesMigration(
    private val defaults: Map<Preferences.Key<*>, Any?>,
    private val valueSerializer: ((Preferences.Key<*>, Any) -> (Pair<Preferences.Key<String>, String>)?)? = null,
    private val clearUpCallback: DefaultCallback = {}
) : androidx.datastore.core.DataMigration<Preferences> {

    @Suppress("UNCHECKED_CAST")
    override suspend fun shouldMigrate(currentData: Preferences): Boolean {
        return defaults.any { (key, defaultValue) ->
            val value = currentData.asMap()[key]
            value == null && defaultValue != null
        }
    }

    @Suppress("UNCHECKED_CAST")
    override suspend fun migrate(currentData: Preferences): Preferences {
        val mutablePrefs = currentData.toMutablePreferences()

        defaults.forEach { (key, value) ->
            if (mutablePrefs[key] == null && value != null) {
                @Suppress("UNCHECKED_CAST")
                when (value) {
                    is String -> mutablePrefs[key as Preferences.Key<String>] = value
                    is Boolean -> mutablePrefs[key as Preferences.Key<Boolean>] = value
                    is Int -> mutablePrefs[key as Preferences.Key<Int>] = value
                    is Long -> mutablePrefs[key as Preferences.Key<Long>] = value
                    is Float -> mutablePrefs[key as Preferences.Key<Float>] = value
                    is Double -> mutablePrefs[key as Preferences.Key<Double>] = value
                    else -> {
                        if (valueSerializer != null) {
                            val (stringKey, json) = valueSerializer.invoke(key, value) ?: return@forEach
                            mutablePrefs[stringKey] = json
                        } else {
                            error("Unsupported preference type for key=$key with value=$value")
                        }
                    }
                }
            }
        }
        return mutablePrefs.toPreferences()
    }

    override suspend fun cleanUp() {
        clearUpCallback.invoke()
    }
}
