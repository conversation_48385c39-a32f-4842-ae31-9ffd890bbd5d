package com.interfun.buz.common.manager.realtimecall

import androidx.lifecycle.Lifecycle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.interfun.buz.common.arouter.routerServicesForce
import com.interfun.buz.common.constants.TrackConstant
import com.interfun.buz.common.database.UserDatabase
import com.interfun.buz.common.ktx.getContactFirstName
import com.interfun.buz.common.manager.cache.group.GroupInfoCacheManager
import com.interfun.buz.common.manager.cache.user.UserRelationCacheManager
import com.interfun.buz.common.manager.realtimecall.MinimizeViewModel.UIState
import com.interfun.buz.common.manager.realtimecall.MinimizeViewModel.UIState.*
import com.interfun.buz.common.manager.realtimecall.VoiceCallMinimizeBlock.MinimizeState
import com.interfun.buz.common.manager.realtimecall.VoiceCallMinimizeBlock.MinimizeState.*
import com.interfun.buz.common.service.RealTimeCallService
import com.interfun.buz.common.utils.BuzTracker
import com.interfun.buz.onair.standard.IGlobalOnAirController
import com.interfun.buz.onair.standard.LivePlaceType.GROUP
import com.interfun.buz.onair.standard.LivePlaceType.PRIVATE
import com.interfun.buz.onair.standard.OnAirRoom
import com.interfun.buz.onair.standard.RoomParam
import com.interfun.buz.onair.standard.SeatType
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch

class MinimizeViewModel : ViewModel() {
    companion object {
        const val TAG = "MinimizeViewModel"
        //非常恶心的埋点，需要区分从onAir退出到最小化的来源，每次onAir点击某个来源时赋值，最下滑处理后，或者stop后清空
        //因为有打开新页面，还有关闭当前onAir都会触发，但是又判断不了，所以只能这么搞
        var onAirMinimizeSource: MinimizeLogSource? = null
        var isMinimizing = false
        fun logMinimizeOnAir(channelIdStr: String, convId: String, logSource: MinimizeLogSource) {
            BuzTracker.onClick {
                put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024082006")
                put(TrackConstant.KEY_TITLE, "on_air_page")
                put(TrackConstant.KEY_ELEMENT_CONTENT, "on_air_minimize")
                put(TrackConstant.KEY_PAGE_TYPE, "on_air_call")
                put(TrackConstant.KEY_PAGE_BUSINESS_ID, convId)
                put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT, channelIdStr)
                put(TrackConstant.KEY_SOURCE, logSource.logStr)
            }
        }
    }

    val voiceCallMinimizeState =
        routerServicesForce<RealTimeCallService>().value.getMinimizeState().stateIn(
            viewModelScope,
            SharingStarted.WhileSubscribed(5000L), MinimizeState.Dismiss()
        )
    private val onAirController by routerServicesForce<IGlobalOnAirController>()
    private val uiLifecycle = MutableStateFlow(Lifecycle.State.INITIALIZED)
    private val livePlaceDao get() = UserDatabase.currInstance?.getLivePlaceBaseInfoDao()

    private val livePlaceState = onAirController.getOnAirRoomFlow()
        .flatMapLatest { room ->
            if (room == null) return@flatMapLatest flowOf(null)
            room.obtainOnAirLifecycleFLow().combine(room.obtainMinimizedStateFlow(),{lifecycle, miniziedState ->
                Pair(lifecycle, miniziedState)
            })
                .flatMapLatest { (lifecycle,miniziedState) ->
                    if (room.isActiveExcludeCreateOrPendingOpen() && miniziedState is com.interfun.buz.onair.standard.MinimizedState.Minimized) {
                        combineLivePlaceStateFlow(room)
                    } else {
                        flowOf(null)
                    }
                }
        }
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = null
        )

    private fun combineLivePlaceStateFlow(room: OnAirRoom): Flow<LivePlaceState> {
        val validSeatTypes = setOf(SeatType.OnACall, SeatType.Calling)
        return combine(
            room.obtainSeatsFlow()
                .map { seats -> seats.seatList.count { it.seatType in validSeatTypes } }
                .distinctUntilChanged(),

            (livePlaceDao?.queryOriginLivePlace(
                room.createParam.targetId,
                room.createParam.livePlaceType.originValue
            )?.mapNotNull { it?.topic }?.distinctUntilChanged()
                ?: flowOf(""))
        ) { connectedCount, topic ->
            LivePlaceState(
                param = room.createParam,
                topic = topic.ifEmpty { getName(room.createParam) },
                connectedCount = connectedCount,
                room = room
            )
        }.distinctUntilChanged()
    }

    val uiState: StateFlow<UIState> =
        combine(
            voiceCallMinimizeState,
            livePlaceState,
            uiLifecycle
        ) { voiceCallState, livePlaceState, uiLifecycle ->
            val state = when (voiceCallState) {
                is Dismiss -> {
                    //voice call没有时，取oniar，都没有为空
                    livePlaceState ?: Empty(false)
                }

                is Invited, is Waiting -> {
                    //voice call时被邀请或者等待别人接听时，优先取onair状态，如果onair没有，再取voice call
                    //因为处于onair状态时，依然可以被打电话，所以onair优先级更高
                    livePlaceState ?: VoiceCallState(voiceCallState)
                }

                is VoiceCall -> {
                    //voice call正在通话时，不管onair是什么，voice call第一优先级
                    VoiceCallState(voiceCallState)
                }
            }
            val lifecycle = Lifecycle.State.STARTED
            if (!uiLifecycle.isAtLeast(lifecycle)) {
                //如果界面不可见时，ui消失（以前的逻辑）
                return@combine if (state is Empty) state else Empty(true)
            }
            state
        }.stateIn(viewModelScope, SharingStarted.WhileSubscribed(5000L), Empty(false))

    val isMinimizingFlow = uiState
        .map { !(it is Empty && !it.isPaused) }
        .distinctUntilChanged()
        .stateIn(viewModelScope, SharingStarted.WhileSubscribed(5000L), false)

    private suspend fun getName(roomParam: RoomParam): String {
        when (roomParam.livePlaceType) {
            PRIVATE -> {
                val userInfo =
                    UserRelationCacheManager.getUserRelationInfoByUidSync(roomParam.targetId)
                return userInfo?.getContactFirstName() ?: roomParam.targetId.toString()
            }

            GROUP -> {
                val group = GroupInfoCacheManager.getGroupInfoBeanByIdSync(roomParam.targetId)
                return group?.groupName ?: roomParam.targetId.toString()
            }
        }
    }

    fun updateUILifecycle(lifecycle: Lifecycle.State) {
        viewModelScope.launch {
            uiLifecycle.emit(lifecycle)
        }
    }

    sealed interface UIState {
        data class VoiceCallState(
            val state: MinimizeState,
        ) : UIState
        data class LivePlaceState(
            val param: RoomParam,
            val topic: String,
            val connectedCount: Int,
            val room: OnAirRoom
        ) : UIState

        data class Empty(val isPaused: Boolean) : UIState
    }

    enum class MinimizeLogSource(val logStr: String) {
        CLICK_MINIMIZE_BUTTON("click_minimize_button"),
        CLICK_CHAT_BOTTOM("click_chat_button"),
        OTHERS("others"),
    }
}

fun UIState.isDismiss(): Boolean {
    return this is Empty && !this.isPaused
}