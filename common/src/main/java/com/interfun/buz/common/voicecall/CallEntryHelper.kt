package com.interfun.buz.common.voicecall

import com.interfun.buz.common.bean.voicecall.CallType
import com.interfun.buz.common.database.entity.UserRelationInfo
import com.interfun.buz.common.database.entity.UserStatus
import com.interfun.buz.common.database.entity.chat.GroupInfoBean
import com.interfun.buz.common.database.entity.chat.isBigGroup
import com.interfun.buz.common.ktx.*
import com.interfun.buz.common.manager.AppConfigRequestManager.enableFriendVoiceCall
import com.interfun.buz.common.manager.AppConfigRequestManager.enableGroupVoiceCall
import com.interfun.buz.common.manager.AppConfigRequestManager.enableVideoCall
import com.interfun.buz.common.manager.cache.group.GroupInfoCacheManager
import com.interfun.buz.common.manager.cache.user.UserRelationCacheManager

object CallEntryHelper {

    suspend fun getSupportCallEntryType(isPrivate: Boolean, targetId: Long): CallEntryType {
        return if (isPrivate) {
            UserRelationCacheManager.getUserRelationInfoByUidSync(targetId)?.let {
                getSupportCallEntryType(it)
            }
        } else {
            GroupInfoCacheManager.getGroupInfoBeanByIdSync(targetId)?.let {
                getSupportCallEntryType(it)
            }
        } ?: CallEntryType.NONE
    }

    suspend fun getSupportCallEntryType(user: UserRelationInfo?): CallEntryType {
        if (user == null
            || !user.isFriend
            || user.isRobot
            || user.isOfficial
            || user.userId.isMe()
            || user.userStatus != UserStatus.STATUS_NORMAL
            || UserRelationCacheManager.isUserBlocked(user.userId)
        ) {
            return CallEntryType.NONE
        }
        return if (enableFriendVoiceCall && enableVideoCall) {
            CallEntryType.MULTI_CALL
        } else if (enableFriendVoiceCall) {
            CallEntryType.VOICE_ONLY
        } else if (enableVideoCall) {
            CallEntryType.VIDEO_ONLY
        } else {
            CallEntryType.NONE
        }
    }

    fun getSupportCallEntryType(groupInfo: GroupInfoBean?): CallEntryType {
        if (groupInfo == null
            // || groupInfo.memberNum <= 1 //产品需求 自己一个人也能发起 call
            || groupInfo.isBigGroup
            || !groupInfo.isInGroup()
        ) {
            return CallEntryType.NONE
        }
        return if (enableFriendVoiceCall && enableVideoCall) {
            CallEntryType.MULTI_CALL
        } else if (enableFriendVoiceCall) {
            CallEntryType.VOICE_ONLY
        } else if (enableVideoCall) {
            CallEntryType.VIDEO_ONLY
        } else {
            CallEntryType.NONE
        }
    }

    suspend fun isRealTimeCallEntryEnable(user: UserRelationInfo?, callType: @CallType Int):Boolean{
        if (null == user) return false
        return isRealTimeCallEntryEnableIgnoreBlocked(user, callType)
                && UserRelationCacheManager.isUserBlocked(user.userId).not()
    }

    /**
     * 该方法单纯是为了减少suspend的麻烦
     */
    fun isRealTimeCallEntryEnableIgnoreBlocked(user: UserRelationInfo?, callType: @CallType Int):Boolean{
        if (null == user) return false
        val enableRealTimeCall = if (CallType.isVoiceCall(callType)) enableFriendVoiceCall else enableVideoCall
        return enableRealTimeCall
                && user.isFriend
                && !user.isRobot
                && !user.isOfficial
                && !user.userId.isMe()
                && user.userStatus == UserStatus.STATUS_NORMAL
    }

    fun isRealTimeCallEntryEnable(groupInfo: GroupInfoBean?, callType: @CallType Int):Boolean{
        if (null == groupInfo) return false
        return isRealTimeCallEntryEnable(groupInfo.isBigGroup, callType) && groupInfo.memberNum > 1
    }

    // Product requirement: In group profile page & more panel, even if the group has only one member, the call entry should still be displayed
    fun isRealTimeCallEntryEnable(isBigGroup: Boolean, callType: @CallType Int): Boolean {
        val enableRealTimeCall = if (CallType.isVoiceCall(callType)) enableGroupVoiceCall else enableVideoCall
        return enableRealTimeCall && !isBigGroup
    }
}

enum class CallEntryType {
    MULTI_CALL,
    VOICE_ONLY,
    VIDEO_ONLY,
    NONE
}