package com.interfun.buz.common.htmlparser

import android.text.Editable
import android.text.Html
import android.text.Spanned
import com.interfun.buz.base.ktx.logDebug
import org.xml.sax.Attributes
import org.xml.sax.ContentHandler
import org.xml.sax.Locator
import org.xml.sax.XMLReader

class HtmlParser private constructor(private val handler: TagHandler) : Html.TagHandler,
    ContentHandler {
    //This approach has the advantage that it allows to disable processing of some tags while using default processing for others,
    interface TagHandler {
        /**
         * @return  true here to indicate that this tag was handled and should not be processed further.
         * false to handle by system default
         */
        fun handleTag(
            opening: Boolean,
            tag: String?,
            output: Editable?,
            attributes: Attributes?
        ): Boolean
    }

    private var wrapped: ContentHandler? = null
    private var text: Editable? = null
    private val tagStatus: ArrayDeque<Boolean> = ArrayDeque()

    companion object {
        fun buildSpannedText(html: String, handler: <PERSON><PERSON><PERSON><PERSON>): Spanned {
            // add a tag at the start that is not handled by default,
            // allowing custom tag handler to replace xmlReader contentHandler
            logDebug("HtmlParser","html===>${html}")
            return Html.fromHtml("<inject/>$html", null, HtmlParser(handler))
        }

        fun getValue(attributes: Attributes, name: String): String? {
            var i = 0
            val n: Int = attributes.length
            while (i < n) {
                if (name == attributes.getLocalName(i)) return attributes.getValue(i)
                i++
            }
            return null
        }
    }

    override fun handleTag(
        opening: Boolean,
        tag: String,
        output: Editable,
        xmlReader: XMLReader
    ) {
        if (wrapped == null) {
            // record result object
            text = output

            // record current content handler
            wrapped = xmlReader.contentHandler

            // replace content handler with our own that forwards to calls to original when needed
            xmlReader.contentHandler = this

            // handle endElement() callback for <inject/> tag
            tagStatus.addLast(java.lang.Boolean.FALSE)
        }
    }


    override fun startElement(
        uri: String?,
        localName: String?,
        qName: String?,
        attributes: Attributes?
    ) {
        val isHandled = handler.handleTag(true, localName, text, attributes)
        tagStatus.addLast(isHandled)
        if (!isHandled) wrapped?.startElement(uri, localName, qName, attributes)
    }


    override fun endElement(uri: String?, localName: String?, qName: String?) {
        if (!tagStatus.removeLast()) wrapped?.endElement(uri, localName, qName)
        handler.handleTag(false, localName, text, null)
    }

    override fun setDocumentLocator(locator: Locator?) {
        wrapped?.setDocumentLocator(locator)
    }


    override fun startDocument() {
        wrapped?.startDocument()
    }


    override fun endDocument() {
        wrapped?.endDocument()
    }


    override fun startPrefixMapping(prefix: String?, uri: String?) {
        wrapped?.startPrefixMapping(prefix, uri)
    }


    override fun endPrefixMapping(prefix: String?) {
        wrapped?.endPrefixMapping(prefix)
    }


    override fun characters(ch: CharArray?, start: Int, length: Int) {
        wrapped?.characters(ch, start, length)
    }


    override fun ignorableWhitespace(ch: CharArray?, start: Int, length: Int) {
        wrapped?.ignorableWhitespace(ch, start, length)
    }


    override fun processingInstruction(target: String?, data: String?) {
        wrapped?.processingInstruction(target, data)
    }

    override fun skippedEntity(name: String?) {
        wrapped?.skippedEntity(name)
    }


}