package com.interfun.buz.common.broadcast

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.common.eventbus.voicecall.CancelVoiceCallInviteEvent
import com.interfun.buz.common.manager.CallNotificationCache
import com.interfun.buz.common.manager.OnlineChatRingtoneManager
import com.interfun.buz.common.manager.chat.CallPendStatus
import com.interfun.buz.common.manager.chat.ChannelInviteManager
import com.interfun.buz.common.manager.chat.ChannelPendStatusManager

/**
 * The function is the same as [DeclineChannelReceiver]，
 * but only used for voice call 通知栏左右滑忽略被呼叫，并不执行挂断/拒绝来电
 * 因为同一个PendingIntent（PendingIntent.FLAG_UPDATE_CURRENT）会导致Extras数据被更新，从而导致DeleteIntent与declineIntent冲突的问题
 * 所以只能另起一个Broadcast Intent
 */
class IgnoreDeleteVoiceCallReceiver:BroadcastReceiver() {
    private val TAG = "IgnoreVoiceCallReceiver"

    companion object{
        const val NOTIFICATION_ID = "notification_id"
        const val CALL_CHANNEL_ID = "call_channel_id"
        const val CALL_TYPE_ID = "call_type_id"
        const val STOP_RINGTONE = "stop_ringtone"
    }

    override fun onReceive(context: Context?, intent: Intent?) {
        val notificationId = intent?.getIntExtra(NOTIFICATION_ID,0)
        val channelId = intent?.getLongExtra(CALL_CHANNEL_ID,0)
        val callType = intent?.getIntExtra(CALL_TYPE_ID, -1)
        val stopRingtone = intent?.getBooleanExtra(STOP_RINGTONE, true)?:true
        logInfo(TAG,"onReceive: notificationId = $notificationId channelId = $channelId")
        if (channelId != null && channelId > 0){
            logInfo(TAG, "changeStatus IDLE: IgnoreDeleteVoiceCallReceiver")
            ChannelPendStatusManager.changeStatus(CallPendStatus.IDLE)
            CancelVoiceCallInviteEvent.post(channelId, if (callType == -1) null else callType)
        }
        ChannelInviteManager.removeInviteMapByChannelId(channelId.toString())
        if (notificationId != 0) {
            val notificationManager = appContext.getSystemService(Context.NOTIFICATION_SERVICE) as android.app.NotificationManager
            if (notificationId != null) {
                notificationManager.cancel(notificationId)
                if (stopRingtone){
                    OnlineChatRingtoneManager.stopIncomingSoundEffect()
                    val voiceCallNotifyIdIsExist =
                        CallNotificationCache.checkVoiceCallNotifyIdIsExist(notificationId)
                    val myVoiceCallStatus = ChannelPendStatusManager.getCallPendState()
                    if (voiceCallNotifyIdIsExist && myVoiceCallStatus.first != CallPendStatus.CONNECTING) {
                        logInfo(TAG, "changeStatus IDLE: IgnoreDeleteVoiceCallReceiver")
                        ChannelPendStatusManager.changeStatus(CallPendStatus.IDLE)
                    }
                }
                CallNotificationCache.removeVoiceCallNotifyId(notificationId)
            }
        }
    }
}