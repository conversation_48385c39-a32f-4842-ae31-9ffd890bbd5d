package com.interfun.buz.common.eventbus.voicecall

/**
 * @Desc voice call select members event
 * @Author:<PERSON><PERSON><PERSON><PERSON>@lizhi.fm
 * @Date: 2023/3/05
 */
//class VoiceCallGroupSelectMembersEvent(val userIds: List<Long>): BaseEvent() {
//
//    companion object{
//        fun post(userIds: List<Long>){
//            LiveEventBus.get(VoiceCallGroupSelectMembersEvent::class.java).post(VoiceCallGroupSelectMembersEvent(userIds))
//        }
//    }
//}