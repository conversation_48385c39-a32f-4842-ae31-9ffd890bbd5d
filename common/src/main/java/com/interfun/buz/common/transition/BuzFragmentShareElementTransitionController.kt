package com.interfun.buz.common.transition

import android.animation.Animator
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import com.interfun.buz.base.ktx.OneParamCallback
import com.interfun.buz.common.transition.BuzTransitionManager.ShareElementTransitionCallback
import com.lizhi.component.itnet.base.mainHandler

class BuzFragmentShareElementTransitionController(val fragment: Fragment) {

    private var postponeRunnable: Runnable? = null
    private var needPostponeTransition = false
    private var postponeTransitionCallback : OneParamCallback<Boolean>?=null


    fun postponeTransition(timeOutMillis: Long) {
        if (needPostponeTransition) {
            return
        }
        needPostponeTransition = true
        this.postponeTransitionCallback?.invoke(true)
        mainHandler.postDelayed({
            startPostponeTransition()
        }, timeOutMillis)
    }

    fun startPostponeTransition() {
        if (!needPostponeTransition) {
            return
        }
        postponeRunnable?.run()
        postponeRunnable = null
        needPostponeTransition = false
        this.postponeTransitionCallback?.invoke(false)
        fragment.view?.invalidate()
    }

    fun setOnPostponeTransitionCallback(callback: OneParamCallback<Boolean>) {
        this.postponeTransitionCallback = callback
    }

    fun startAddViewShareElementTransition(
        sceneRoot: ViewGroup,
        transition: Transition,
        sharedElementName: String,
        addView: View,
        customAnim: Animator?,
        callback: ShareElementTransitionCallback
    ) {
        val runnable = Runnable {
            BuzTransitionManager.startAddViewShareElementTransition(
                sceneRoot, transition, sharedElementName, addView, customAnim, callback
            )
        }
        if (needPostponeTransition) {
            ViewUtils.setTransitionAlpha(addView, 0f)
            transition.doOnStart {
                ViewUtils.setTransitionAlpha(addView, 1f)
            }
            postponeRunnable = runnable
        } else {
            runnable.run()
        }
    }

    fun startRemoveViewShareElementTransition(
        sceneRoot: ViewGroup,
        transition: Transition,
        sharedElementName: String,
        removeView: View,
        customAnim: Animator?
    ): Boolean {
        return BuzTransitionManager.startRemoveViewShareElementTransition(
            sceneRoot,
            transition,
            sharedElementName,
            removeView,
            customAnim
        )
    }
}