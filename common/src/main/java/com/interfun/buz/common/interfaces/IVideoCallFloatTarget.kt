package com.interfun.buz.common.interfaces

import androidx.annotation.ColorInt
import com.interfun.buz.base.ktx.OneParamCallback
import com.interfun.buz.base.ktx.TwoParamCallback
import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.base.ktx.getCompatColor
import com.interfun.buz.common.R
import com.interfun.buz.common.bean.voicecall.CallRoomUser

/**
 * Author: ChenYouSheng
 * Date: 2025/3/7
 * Email: <EMAIL>
 * Desc:
 */
interface IVideoCallFloatTarget {
    val isGroup: Boolean
    val currentUserId: Long
    val targetUserId: Long

    fun getBackgroundColor(
        myBackgroundCallback: OneParamCallback<BackgroundColor>,
        targetBackgroundCallback: OneParamCallback<BackgroundColor>
    )

    fun dispatchRoomUser(
        personalCallback: OneParamCallback<CallRoomUser>? = null,
        targetCallback: OneParamCallback<CallRoomUser>? = null
    )


    fun atLeastOneCameraOpen(): Boolean

    fun destroy()
}

data class BackgroundColor(
    @ColorInt val color: Int,
    @ColorInt val overlay: Int = appContext.getCompatColor(R.color.transparent)
)