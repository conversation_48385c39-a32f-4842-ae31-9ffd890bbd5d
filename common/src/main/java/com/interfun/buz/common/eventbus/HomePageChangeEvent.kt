package com.interfun.buz.common.eventbus

import com.interfun.buz.base.utils.BusUtil

/**
 * <AUTHOR>
 * @date 2022/8/4
 * @desc
 */
class HomePageChangeEvent(val pageIndex: HomePageEnum) : BaseEvent(){

    companion object {
        fun post(page: HomePageEnum) {
            BusUtil.post(HomePageChangeEvent(page))
        }
    }
}

enum class HomePageEnum(val value: Int){
    PageUserSetting(0),
    PageHome(1),
    PageContact(2)
}