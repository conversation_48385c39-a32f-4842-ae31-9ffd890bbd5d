package com.interfun.buz.common.manager

import android.content.Context
import android.widget.ImageView
import androidx.fragment.app.Fragment
import com.interfun.buz.base.ktx.asDrawable
import com.interfun.buz.base.ktx.screenHeight
import com.interfun.buz.base.ktx.screenWidth
import com.interfun.buz.common.R
import com.interfun.buz.common.ktx.loadWithThumbnail
import com.interfun.buz.common.view.activity.ImagePreViewActivity
import com.interfun.buz.common.view.fragment.ImageMsgPanoramicFragment
import com.interfun.buz.photopreview.interfaces.IZoomMediaLoader
import com.interfun.buz.photopreview.interfaces.MySimpleTarget

/**
 * @Desc
 * @Author:<EMAIL>
 * @Date: 2023/1/11
 */
class PreviewImageLoader : IZoomMediaLoader {

    private val errorRes = R.drawable.common_user_default_portrait_round
    override fun displayImage(
        fragment: Fragment,
        path: String,
        imageView: ImageView?,
        simpleTarget: MySimpleTarget
    ) {
        val aseComponent = (fragment.activity as? ImagePreViewActivity)?.getAesComponent(path)
        imageView?.loadWithThumbnail(
            thumbnailUrl = null,
            url = path,
            aesComponent = aseComponent
        ){
            size(screenWidth, screenHeight)
            error(errorRes)
            crossfade(false)
            target(onSuccess = {
                //fragment get high priority
                if (fragment is ImageMsgPanoramicFragment) {
                    fragment.onResourceReady()
                    fragment.initImageViewScale(it)
                }else{
                    simpleTarget.onResourceReady()
                }

                imageView.setImageDrawable(it)
            }, onError = {
                if (fragment is MySimpleTarget) {
                    fragment.onLoadFailed(errorRes.asDrawable())
                }else {
                    simpleTarget.onLoadFailed(errorRes.asDrawable())
                }
            })
        }
    }

    override fun displayGifImage(
        fragment: Fragment,
        path: String,
        imageView: ImageView?,
        simpleTarget: MySimpleTarget
    ) {
        displayImage(fragment, path, imageView, simpleTarget)
    }

    override fun onStop(fragment: Fragment) {

    }

    override fun clearMemory(context: Context) {

    }
}