package com.interfun.buz.common.eventbus.contact

import com.interfun.buz.common.database.entity.ContactsBean
import com.interfun.buz.common.eventbus.BaseEvent
import com.jeremyliao.liveeventbus.LiveEventBus

/**
 * @Desc
 * @Author:lvzhen<PERSON><PERSON>@lizhi.fm
 * @Date: 2022/8/8
 */
@Deprecated("Wait to delete" +
    "Maybe the function of observing address updates will be added later and can be used again")
class ContactEditSuccessEvent(val contact: ContactsBean, val editType: Int): BaseEvent() {

    companion object{

        const val EditType_Add = 1
        const val EditType_Update = 2
        const val EditType_Del = 3

        fun post(contact: ContactsBean, editType: Int){
            LiveEventBus.get(ContactEditSuccessEvent::class.java).post(ContactEditSuccessEvent(contact, editType))
        }
    }
}