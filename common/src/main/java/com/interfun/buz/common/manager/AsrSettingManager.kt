package com.interfun.buz.common.manager

import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.common.constants.CommonMMKV
import com.interfun.buz.common.manager.cache.user.UserSettingManager
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import com.lizhi.im5.sdk.message.IMessage

class AsrSettingManager {
    companion object {
        private const val TAG = "AsrSettingManager"
        private const val PREVIEW_OPEN = 1
        private const val PREVIEW_DEFAULT = 0
        private const val PREVIEW_CLOSE = -1

        val isOpenAsrPreview: Boolean
            get() {
                logInfo(TAG,"isOpenAsrPreview openTranscriptionPreview: ${CommonMMKV.openTranscriptionPreview}")
                return when (CommonMMKV.openTranscriptionPreview) {
                    PREVIEW_CLOSE -> false
                    PREVIEW_OPEN -> true
                    else -> {
                        true  // 固定为展示
                    }
                }
            }
        private val isAllMsgAsrEnabled get() = AppConfigRequestManager.isAllMsgSmartAsr

        private val isSmartTransEnabled
            get() = AppConfigRequestManager.let {
                it.smartAsrSwitch && it.smartAsrGlobalSwitch && it.asrFunctionSwitch
            } && if (isAllMsgAsrEnabled) true else UserSettingManager.isQuietModeEnable

        fun canShowAsrPreview(msg: IMessage?):Boolean {
            logInfo(
                TAG,
                "isSmartTransEnabled:$isSmartTransEnabled," +
                        " isAllMsgAsrEnabled:$isAllMsgAsrEnabled," +
                        " conversationType:${msg?.conversationType}," +
                        " isOpenAsrPreview:$isOpenAsrPreview"
            )
            if (!isAllMsgAsrEnabled) {
                if (msg?.conversationType == IM5ConversationType.GROUP) {
                    return false
                }
            }
            return isSmartTransEnabled && isOpenAsrPreview
        }

        fun defaultAsrFunction(msg: IMessage?):Boolean {
            logInfo(
                TAG,
                "isSmartTransEnabled:$isSmartTransEnabled," +
                        " isAllMsgAsrEnabled:$isAllMsgAsrEnabled," +
                        " conversationType:${msg?.conversationType}," +
                        " isOpenAsrPreview:$isOpenAsrPreview"
            )
            if (!isAllMsgAsrEnabled) {
                if (msg?.conversationType == IM5ConversationType.GROUP) {
                    return false
                }
            }
            return isSmartTransEnabled
        }

        fun switchAsrPreview(open: Boolean) {
            CommonMMKV.openTranscriptionPreview = if (open) PREVIEW_OPEN else PREVIEW_CLOSE
        }
    }
}