package com.interfun.buz.common.constants

val RouterParamValues.ICommon.SOURCE_PUSH get() = "push"
val RouterParamValues.ICommon.SOURCE_WIDGET get() = "widget"
val RouterParamValues.ICommon.SOURCE_UNKNOWN get() = "unknown"

class RouterParamValues {

    interface ICommon {
        val TRANSITION_ANIM_SLIDE_END get() = 1
        val TRANSITION_ANIM_SLIDE_BOTTOM get() = 2
    }

    object Common : ICommon

    object Startup : ICommon {

    }

    object ChatHome : ICommon {
        const val OPEN_FROM_REGISTER = "OPEN_FROM_REGISTER"
        const val OPEN_FROM_LOGIN = "OPEN_FROM_LOGIN"
    }

    object Chat : ICommon {
    }

    object AI : ICommon {
        const val TYPE_SOURCE_LANGUAGE = 0
        const val TYPE_TARGET_LANGUAGE = 1
        //profile 设置页进入
        const val TRANSLATION_SOURCE_PROFILE_SETTING = 0
        //居中消息进来
        const val TRANSLATION_SOURCE_CENTERED_MSG = 1
        // 首页面
        const val TRANSLATION_SOURCE_HOME = 2
    }

    object Friend:ICommon{
        const val ADD_FRIEND_FROM_PHONE_NUMBER = "ADD_FRIEND_FROM_PHONE_NUMBER"
        const val ADD_FRIEND_FROM_CONTACT = "ADD_FRIEND_FROM_CONTACT"
    }

}