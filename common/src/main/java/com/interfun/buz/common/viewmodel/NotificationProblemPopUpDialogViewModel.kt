package com.interfun.buz.common.viewmodel

import androidx.lifecycle.ViewModel
import com.buz.idl.common.request.RequestReportFeatureGuideComplete
import com.buz.idl.common.service.BuzNetCommonServiceClient
import com.interfun.buz.base.ktx.launchIO
import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.common.net.withConfig
import com.interfun.buz.common.utils.NotificationUtil

/**
 * @Desc
 * @Author:LumJunYean
 * @Date: 2024/8/2
 */
class NotificationProblemPopUpDialogViewModel : ViewModel() {

    companion object {
        const val TAG = "NotificationProblemPopUpDialogViewModel"
    }

    private val client by lazy { BuzNetCommonServiceClient().withConfig() }

    fun reportNotificationProblemNotComplete() {
        val allAccessOpen = NotificationUtil.isNotifyOpen() &&
                NotificationUtil.isIgnoringBatteryOptimizations()
        if (allAccessOpen) {
            return
        }
        launchIO {
            logDebug(TAG, "reportNotificationProblemNotComplete ==> start")
            val ret = client.reportFeatureGuideComplete(
                // TODO HWL Merge Code
                RequestReportFeatureGuideComplete(
                    type = 2,
                    source = "0",
                    extra = null
                )
            )
            logDebug(TAG, "reportNotificationProblemNotComplete ==> code=${ret.code}")
        }
    }
}
