package com.interfun.buz.common.arouter

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import androidx.fragment.app.FragmentActivity
import com.buz.idl.user.request.RequestInviteRegister
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.R.anim
import com.interfun.buz.common.base.BaseActivity
import com.interfun.buz.common.bean.chat.*
import com.interfun.buz.common.constants.*
import com.interfun.buz.common.constants.RouterParamKey.Chat
import com.interfun.buz.common.database.entity.UserRelationInfo
import com.interfun.buz.common.manager.AppConfigRequestManager
import com.interfun.buz.common.manager.cache.group.GroupInfoCacheManager
import com.interfun.buz.common.manager.userLifecycleScope
import com.interfun.buz.common.net.newInstanceBuzNetUserServiceClient
import com.interfun.buz.common.service.ChatService
import com.interfun.buz.common.service.ContactsService
import com.interfun.buz.common.utils.generateShareToInviteLandingPage
import com.interfun.buz.common.web.WebViewActivity
import com.interfun.buz.onair.standard.RoomParam
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

/**
 * <AUTHOR>
 * @date 2022/8/25
 * @desc
 */
object NavManager {
    private val TAG = "NavManager"

    fun startWebViewActivity(context: Context, url: String){
        WebViewActivity.start(context, url)
    }

    /**
     * @param targetType 1: private chat  2:group chat
     * @param targetId sendInviter userId or groupId
     */
    fun startChatHomeActivityWithWTInvite(targetType: Int, targetId: Long) {
        startActivityByRouter(
            PATH_CHAT_ACTIVITY_HOME,
            RouterParamKey.ChatHome.KEY_WT_TARGET_TYPE to targetType,
            RouterParamKey.ChatHome.KEY_WT_TARGET_ID to targetId
        )
    }

    fun startChatHomeActivity(targetId: Long) {
        startActivityByRouter(
            PATH_CHAT_ACTIVITY_HOME,
            RouterParamKey.ChatHome.KEY_WT_TARGET_ID to targetId
        )
    }fun startChatHomeActivity(targetId: Long,needHistoryOpen:Boolean,isGroupType:Boolean) {
        startActivityByRouter(
            PATH_CHAT_ACTIVITY_HOME,
            RouterParamKey.ChatHome.KEY_WT_TARGET_ID to targetId,
            RouterParamKey.ChatHome.KEY_WT_NEED_CHAT_HISTORY to needHistoryOpen,
            RouterParamKey.ChatHome.KEY_WT_TARGET_TYPE to if (isGroupType) 2 else 1,
        )
    }

    fun startChatHomeActivity(targetId: Long, fromSource: Int) {
        startActivityByRouter(
            PATH_CHAT_ACTIVITY_HOME,
            RouterParamKey.ChatHome.KEY_WT_TARGET_ID to targetId,
            RouterParamKey.ChatHome.KEY_SOURCE to fromSource
        )
    }


    fun startChatHomeActivity(targetId: Long, info: UserRelationInfo) {
        startActivityByRouter(
            PATH_CHAT_ACTIVITY_HOME,
            RouterParamKey.ChatHome.KEY_WT_TARGET_ID to targetId,
            RouterParamKey.ChatHome.KEY_USER_INFO to info
        )
    }

    fun startChatHomeActivityAfterRegister(isFromLogin: Boolean) {
        logLineInfo(TAG,LogLine.LOGIN,"startChatHomeActivityAfterRegister: isFromLogin = $isFromLogin")
        startActivityByRouter(PATH_CHAT_ACTIVITY_HOME, {
            val source = if (isFromLogin.not()) {
                RouterParamValues.ChatHome.OPEN_FROM_REGISTER
            } else {
                RouterParamValues.ChatHome.OPEN_FROM_LOGIN
            }
            withString(RouterParamKey.ChatHome.KEY_OPEN_SOURCE, source)
        })
    }

    fun startSendSmsMsgActivity(
        context: Context,
        phone: String?
    ) {
        val activity = context.activity

        generateShareToInviteLandingPage(context, true, onSuccessCallback =  {
            if (activity.isNotNull() && activity is BaseActivity) {
                activity.hideDataLoading()
            }

            val shareContent = AppConfigRequestManager.smsShareText + ": " + it.link
            val shareIntent = Intent()
            shareIntent.action = Intent.ACTION_SENDTO
            shareIntent.data = Uri.parse("smsto:$phone")
            shareIntent.putExtra("sms_body", shareContent)

            //线上有位用户系统内没有处理Intent.ACTION_SENDTO的应用，这时采用备选方案
            try {
                context.startActivity(shareIntent)
                if (phone.isNullOrEmpty()) return@generateShareToInviteLandingPage
                MainScope().launch(Dispatchers.IO) {

                    val request = RequestInviteRegister(phone)
                    newInstanceBuzNetUserServiceClient().inviteRegister(request)
                }
            } catch (e: Exception) {
                shareText(shareContent)
            }
        })
    }

    fun startPrivateChatActivity(
        context: Context?,
        jumpInfo: PrivateChatJumpInfo
    ) {
        context.startActivityByRouter(PATH_CHAT_ACTIVITY_PRIVATE, {
            withParcelable(RouterParamKey.Chat.JUMP_INFO, jumpInfo)
            addFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT)
        })
    }

    fun startPrivateChatActivity(
        activity: Activity?,
        jumpInfo: PrivateChatJumpInfo
    ) {
        activity.startActivityByRouter(PATH_CHAT_ACTIVITY_PRIVATE, {
            withParcelable(RouterParamKey.Chat.JUMP_INFO, jumpInfo)
            addFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT)
        })
    }

    fun startE2EEIntrductionActivity(activity: Activity?) {
        activity.startActivityByRouter(PATH_CHAT_ACTIVITY_E2EE_INTRODUCTION)
    }

    fun startGroupChatActivity(
        context: Context?,
        jumpInfo: GroupChatJumpInfo
    ) {
        context.startActivityByRouter(PATH_CHAT_ACTIVITY_GROUP, {
            withParcelable(Chat.JUMP_INFO, jumpInfo)
            addFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT)
        })
    }

    fun startGroupChatActivity(
        activity: Activity?,
        jumpInfo: GroupChatJumpInfo
    ) {
        activity.startActivityByRouter(PATH_CHAT_ACTIVITY_GROUP, {
            withParcelable(RouterParamKey.Chat.JUMP_INFO, jumpInfo)
            addFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT)
        })
    }

    fun startLivePlaceActivity(param: RoomParam) {
        startActivityByRouter(PATH_ACTIVITY_LIVE_PLACE, {
            withParcelable(RouterParamKey.Common.JUMP_INFO, param)
            addFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT)
        })
    }

    fun startOverlaySettingActivity(activity: Activity?) {
        activity.startActivityByRouter(PATH_FLOAT_OVERLAY_SETTING)
    }

    /**
     * @param source see [RouterParamKey.Feedback] for more detail
     */
    fun startFeedbackActivity(context: Context, source: Int, prefix: String? = null) {
        context.startActivityByRouter(
            PATH_FEEDBACK_SUGGESTION,
            RouterParamKey.Feedback.KEY_SOURCE to source,
            RouterParamKey.Feedback.KEY_PREFIX to prefix
        )
    }

    fun startLivePlaceFeedbackActivity(context: Context){
        context.startActivityByRouter(
            PATH_FEEDBACK_LIVEPLACE)
    }

    fun startFragmentHostActivity(context: Context, aRouterPath: String, bundle: Bundle?) {
        context.startActivityByRouter(
            PATH_COMMON_ROUTER_FRAGMENT_HOST_ACTIVITY,
            RouterParamKey.Common.KEY_A_ROUTER_PATH to aRouterPath,
            RouterParamKey.Common.KEY_BUNDLE to bundle
        )
    }

    /**
     * @param source FriendApplySource
     */
    fun showPersonalProfile(
        context: Context,
        userID: Long,
        source: Int,
        traceSource: Int = 3,
        shouldAutoAddFriend: Boolean = false
    ) {
        val activity = context.activity ?: topActivity
        if (activity is FragmentActivity) {
            routerServices<ContactsService>().value?.getProfileDialog(
                userID,
                source = source,
                businessId = null,
                trackerSource = traceSource,
                shouldAutoAddFriend = shouldAutoAddFriend
            )?.showDialog(activity)
        }
    }

    fun showGroupProfile(context: Context, groupId: Long, traceSource: Int = 0) {
        val activity = context.activity ?: topActivity
        if (activity is FragmentActivity) {
            routerServices<ChatService>().value?.getGroupInfoDialog(groupId,traceSource)
                ?.showDialog(activity)
        }
    }

    fun startOnlineChatActivity(
        channelId: Long,
        channelType: Int,
        callType: Int,
        targetId: Long,
        jumpType: Int = OnlineChatJumpType.joinChannel,
        jumpFrom:@JumpVoiceCallPageFrom Int,
        buildNaviCallbackBlock: (PostcardBuilder.() -> Unit)? = null
    ) {
        startActivityByRouter(
            path = PATH_ACTIVITY_REAL_TIME_CALL,
            buildPostcardBlock = {
                withParcelable(
                    Chat.JUMP_INFO, OnlineChatJumpInfo(
                        channelId = channelId,
                        channelType = channelType,
                        callType = callType,
                        targetId = targetId,
                        jumpType = jumpType
                    )
                )
                withInt(
                    RouterParamKey.ChannelInvite.KEY_JUMP_INVITE_PAGE_FROM,
                    jumpFrom
                )
                withTransition(anim.anim_dialog_theme_enter, anim.anim_dialog_theme_out)
            },
            buildNaviCallbackBlock = buildNaviCallbackBlock
        )
    }

}
