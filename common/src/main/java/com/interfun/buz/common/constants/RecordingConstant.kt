package com.interfun.buz.common.constants

import android.content.Context
import android.media.AudioManager
import com.interfun.buz.base.ktx.launchIO
import com.interfun.buz.base.ktx.logError
import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.common.utils.BuzTracker
import com.yibasan.lizhifm.sdk.platformtools.ApplicationContext
import kotlinx.coroutines.GlobalScope


//https://vocalbeats.sg.larksuite.com/wiki/GDpMw1wVbiUpmtkHmHtlYIGcgoe
object RecordingConstant {
    val NORMAL = 0

    /**
     * 录音器被系统提示静默
     */
    val ERRO_CLIENTSILENCED = -800

    /**
     * 利用音频检测算法探测开始录音时出现空音的回调。会在录音器warn中回调
     */
    val WARN_SILENT = -100

//    val ERRO_DETECT_ = -800

    private val audioManager by lazy {
        appContext.getSystemService(Context.AUDIO_SERVICE) as? AudioManager
    }

    private val isSystemMicMute:Int get() =
        if (audioManager?.isMicrophoneMute == true) 1 else 0



    fun systemReportSilent(){
        BuzTracker.onTechTrack {
            put(TrackConstant.KEY_EXCLUSIVE_ID,"TT2024042901")
            put(TrackConstant.KEY_EVENT_NAME,"SystemReportSilent")
            put(TrackConstant.KEY_CONTENT_3, isSystemMicMute)
        }
    }
    fun selfReportSilent(traceId: String, selfReportSilent: Boolean){
        BuzTracker.onTechTrack {
            put(TrackConstant.KEY_EXCLUSIVE_ID,"TT2024042902")
            put(TrackConstant.KEY_EVENT_NAME,"SelfReportSilent")
            put(TrackConstant.KEY_CONTENT_1,traceId)
            put(TrackConstant.KEY_CONTENT_2,if (selfReportSilent) "1" else "0")
            put(TrackConstant.KEY_CONTENT_3, isSystemMicMute)
        }
    }

    fun initErrorCode(code:Int){
        BuzTracker.onTechTrack {
            put(TrackConstant.KEY_EXCLUSIVE_ID,"TT2024050901")
            put(TrackConstant.KEY_EVENT_NAME,"StartRecInit")
            put(TrackConstant.KEY_CONTENT_1,"${code}")
            put(TrackConstant.KEY_CONTENT_3, isSystemMicMute)
        }
    }

    fun startRecordRet(code: Int) {
        BuzTracker.onTechTrack{
            put(TrackConstant.KEY_EXCLUSIVE_ID,"TT2024050801")
            put(TrackConstant.KEY_EVENT_NAME,"StartRecResult")
            put(TrackConstant.KEY_CONTENT_1,"${code}")
            put(TrackConstant.KEY_CONTENT_3, isSystemMicMute)
        }
    }

    fun sdkSelfSilentCheck(checkCode:Int){
        BuzTracker.onTechTrack{
            put(TrackConstant.KEY_EXCLUSIVE_ID,"TT2024050902")
            put(TrackConstant.KEY_EVENT_NAME,"StartRecResult")
            put(TrackConstant.KEY_CONTENT_1,"${checkCode}")
            put(TrackConstant.KEY_NUMBER_1, android.os.Build.VERSION.SDK_INT)
            put(TrackConstant.KEY_CONTENT_3, isSystemMicMute)
        }

    }

    /**
     * 前2秒检测到空音频
     */
    fun onSilenceWarnStart(traceId: String, isPrivateChat: Boolean, pageBusinessId: String) {
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2024112801")
            put(TrackConstant.KEY_RESULT_TYPE, "detected_empty_voice_result_using_2s_sdk")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, if (isPrivateChat) "private" else "group")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, pageBusinessId)
            put(TrackConstant.KEY_CONTENT_ID, traceId)

        }
    }

    /**
     * 完整检测空音频，与onSilenceWarnStart打点对应
     */
    fun onFullSilenceDetected(
        traceId: String,
        isPrivateChat: Boolean,
        pageBusinessId: String,
        isClientSilenced: Boolean
    ) {
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2024112802")
            put(TrackConstant.KEY_RESULT_TYPE, "detected_empty_voice_result_using_full_sdk")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, if (isPrivateChat) "private" else "group")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, pageBusinessId)
            put(TrackConstant.KEY_CONTENT_ID, traceId)
            put(TrackConstant.KEY_CONTENT_NAME, if (isClientSilenced) "empty" else "not_empty")
        }
    }

    fun tekTT2024071701(source:String) {
        GlobalScope.launchIO {
            try {
                val am =
                    ApplicationContext.getApplication()
                        .getSystemService(Context.AUDIO_SERVICE) as? AudioManager
                val microphoneMute = am?.isMicrophoneMute ?: return@launchIO
                BuzTracker.onTechTrack {
                    put(TrackConstant.KEY_EXCLUSIVE_ID, "TT2024071701")
                    put(TrackConstant.KEY_EVENT_NAME, "reportMicMuteState")
                    put(TrackConstant.KEY_CONTENT_1, if (microphoneMute) "1" else "0")
                    put(TrackConstant.KEY_CONTENT_2, source)
                }
            }catch (e:Exception){
                logError(e)
            }

        }
    }

    fun prohibitedRecording(source: String){
        BuzTracker.onTechTrack {
            put(TrackConstant.KEY_EXCLUSIVE_ID,"TT20250042901")
            put(TrackConstant.KEY_EVENT_NAME,"prohibitedRecording")
            put(TrackConstant.KEY_CONTENT_1, source)
        }
    }

}