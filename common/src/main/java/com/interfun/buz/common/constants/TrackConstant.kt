package com.interfun.buz.common.constants

/**
 * <AUTHOR>
 * @date 2022/9/13
 * @desc
 */
object TrackConstant {
    /** APP页面浏览 */
    const val EVENT_APP_VIEW_SCREEN = "\$AppViewScreen"

    /** 弹窗浏览事件 */
    const val EVENT_VIEW_SCREEN = "ViewScreen"
    /** 元素曝光 */
    const val EVENT_ELEMENT_EXPOSURE = "ElementExposure"

    /** 内容流曝光 */
    const val EVENT_CONTENT_EXPOSURE = "ContentExposure"

    /** 内容流点击 */
    const val EVENT_CONTENT_CLICK = "ContentClick"

    /** APP元素点击 */
    const val EVENT_APP_CLICK = "\$AppClick"

    /** 结果反馈 */
    const val EVENT_RESULT_BACK = "ResultBack"

    /** 技术打点 */
    const val EVENT_TECH_TRACK = "TechTrack"


    const val KEY_TITLE = "\$title" // 页面标题
    const val KEY_ELEMENT_NAME = "\$element_name" // 元素名称
    const val KEY_ELEMENT_TYPE = "\$element_type" // 元素类型
    const val KEY_ELEMENT_CONTENT = "\$element_content" // 元素内容
    const val KEY_PAGE_TYPE = "page_type" // 页面模块类型
    const val KEY_PAGE_BUSINESS_TYPE = "page_business_type" // 页面业务类型
    const val KEY_PAGE_BUSINESS_ID = "page_business_id" // 页面业务id
    const val KEY_PAGE_CONTENT = "page_content" // 页面内容
    const val KEY_PAGE_STATUS = "page_status" // 页面状态
    const val KEY_PAGE = "page" // 页面名
    const val KEY_ACTION_TYPE = "actionType" // 操作类型
    const val KEY_TARGET_ID = "targetId" // 目标ID
    const val KEY_VIEW_SOURCE = "view_source" // 页面来源
    const val KEY_SOURCE = "source" // 页面来源
    const val KEY_REASON = "reason" // 原因
    const val KEY_BUSINESS_NUM = "business_num" // 业务数值
    const val KEY_ELEMENT_BUSINESS_CONTENT = "element_business_content" // 元素内容
    const val KEY_ELEMENT_BUSINESS_TYPE = "element_business_type" // 元素业务类型
    const val KEY_ELEMENT_BUSINESS_ID = "element_business_id" // 元素业务id
    const val KEY_RESULT_TYPE = "result_type" //结结果类型
    const val KEY_CONTENT_ID = "content_id" // 内容id
    const val KEY_BUSINESS_ID = "business_id" //业务id
    const val KEY_BUSINESS_TYPE = "business_type" // 对应业务的类型
    const val KEY_CONTENT_NAME = "content_name" // 内容名称
    const val KEY_IS_SUCCESS = "is_success" // 是否成功
    const val KEY_ELEMENT_BUSINESS_PAGE = "element_business_page" // 是否成功
    const val KEY_FAIL_REASON = "fail_reason" // 失败原因
    const val KEY_REPORT_JSON = "reportJson" // 推荐下发的数据集
    const val KEY_OCCASION_TYPE = "occasion_type" // 触发时机类型
    const val KEY_DURATION = "duration" // 时长
    const val KEY_POSITION = "position" // 位置序号
    const val KEY_EXCLUSIVE_ID = "exclusive_id" //唯一标识ID（便于开发查找事件）
    const val KEY_LEAVE_GROUPSPACE = "LeaveGroupSpace" //退出群空间
    const val KEY_SESSION_ID = "session_id" //回话 id
    const val KEY_MODE = "mode" //模式
    const val KEY_COMMUNITY_ID = "communityId"
    const val KEY_TO_USER_ID = "toUserId"
    const val KEY_USER_TYPE = "userType"
    const val KEY_JOIN_TYPE = "joinType"
    const val KEY_RESULT = "result"
    const val KEY_FAILED_REASON = "failedReason"
    const val KEY_PHONE_NUMBER = "phone_number"
    const val KEY_URL = "url"

    const val KEY_TRACE_ID = "traceId"
    const val KEY_LOG_TIME = "log_time"
    const val KEY_NTP_TIME = "ntptime"

    const val KEY_EVENT_NAME = "event_name"
    const val KEY_CONTENT_1 = "content_1"
    const val KEY_CONTENT_2 = "content_2"
    const val KEY_CONTENT_3 = "content_3"
    const val KEY_CONTENT_4 = "content_4"
    const val KEY_CONTENT_5 = "content_5"
    const val KEY_CONTENT_6 = "content_6"
    const val KEY_CONTENT_7 = "content_7"
    const val KEY_CONTENT_8 = "content_8"
    const val KEY_CONTENT_9 = "content_9"
    const val KEY_CONTENT_10 = "content_10"
    const val KEY_NUMBER_1 = "number_1"
    const val KEY_NUMBER_2 = "number_2"
    const val KEY_NUMBER_3 = "number_3"
    const val KEY_MENU = "menu"
}