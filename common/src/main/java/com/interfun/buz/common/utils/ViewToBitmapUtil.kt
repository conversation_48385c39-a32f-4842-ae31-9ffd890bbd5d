package com.interfun.buz.common.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Picture
import android.os.Build
import android.view.View
import androidx.annotation.Keep
import androidx.annotation.RequiresApi
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.LayoutDirection
import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.base.ktx.logError
import com.interfun.buz.base.ktx.logInfo
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.util.UUID

/**
 * View转Bitmap工具类
 * 提供将View转换为图片并保存到指定目录的功能
 * 
 * <AUTHOR> Android Team
 */
@Keep
object ViewToBitmapUtil {
    
    private const val TAG = "ViewToBitmapUtil"
    
    /**
     * 图片格式枚举
     */
    enum class ImageFormat(val extension: String, val compressFormat: Bitmap.CompressFormat) {
        PNG("png", Bitmap.CompressFormat.PNG),
        JPEG("jpg", Bitmap.CompressFormat.JPEG)
    }
    
    /**
     * View转Bitmap异常
     */
    class ViewToBitmapException(message: String, cause: Throwable? = null) : Exception(message, cause)
    
    /**
     * 文件保存异常
     */
    class FileSaveException(message: String, cause: Throwable? = null) : Exception(message, cause)
    
    /**
     * 目录创建异常
     */
    class DirectoryCreateException(message: String, cause: Throwable? = null) : Exception(message, cause)
    
    /**
     * 将占位符视图转换为图片并保存到指定目录
     *
     * @param placeHolderView 需要转换的View
     * @param vfViewCacheDir 保存图片的目录
     * @param imageFormat 图片格式，默认为PNG
     * @param quality 图片质量（0-100），仅对JPEG格式有效，默认为90
     * @param useHardwareAcceleration 是否使用硬件加速，默认为true
     * @return 保存的图片文件的完整路径
     *
     * @throws ViewToBitmapException 当视图转换失败时抛出
     * @throws FileSaveException 当文件保存失败时抛出
     * @throws DirectoryCreateException 当目录创建失败时抛出
     */
    suspend fun convertViewToImageFile(
        placeHolderView: View,
        vfViewCacheDir: File,
        imageFormat: ImageFormat = ImageFormat.PNG,
        quality: Int = 90,
        useHardwareAcceleration: Boolean = true
    ): String = withContext(Dispatchers.IO) {
        
        logInfo(TAG, "开始转换View到图片文件，格式：${imageFormat.extension}")
        
        // 1. 检查并创建目录
        ensureDirectoryExists(vfViewCacheDir)
        
        // 2. 将View转换为Bitmap
        val bitmap = createBitmapFromView(placeHolderView, useHardwareAcceleration)
        
        try {
            // 3. 生成唯一文件名
            val fileName = generateUniqueFileName(imageFormat.extension)
            val imageFile = File(vfViewCacheDir, fileName)
            
            // 4. 保存Bitmap到文件
            saveBitmapToFile(bitmap, imageFile, imageFormat, quality)
            
            val filePath = imageFile.absolutePath
            logInfo(TAG, "View转换为图片成功，保存路径：$filePath")
            
            return@withContext filePath
            
        } finally {
            // 5. 及时回收Bitmap内存
            if (!bitmap.isRecycled) {
                bitmap.recycle()
                logInfo(TAG, "Bitmap内存已回收")
            }
        }
    }

    /**
     * 将Compose内容转换为图片并保存到指定目录
     *
     * @param content Compose内容
     * @param vfViewCacheDir 保存图片的目录
     * @param width 图片宽度（像素）
     * @param height 图片高度（像素）
     * @param imageFormat 图片格式，默认为PNG
     * @param quality 图片质量（0-100），仅对JPEG格式有效，默认为90
     * @param density 屏幕密度，默认使用应用上下文的密度
     * @param layoutDirection 布局方向，默认为LTR
     * @return 保存的图片文件的完整路径
     *
     * @throws ViewToBitmapException 当Compose内容转换失败时抛出
     * @throws FileSaveException 当文件保存失败时抛出
     * @throws DirectoryCreateException 当目录创建失败时抛出
     */
    suspend fun convertComposeToImageFile(
        content: @Composable () -> Unit,
        vfViewCacheDir: File,
        width: Int,
        height: Int,
        imageFormat: ImageFormat = ImageFormat.PNG,
        quality: Int = 90,
        density: Density = Density(appContext),
        layoutDirection: LayoutDirection = LayoutDirection.Ltr
    ): String = withContext(Dispatchers.Main) {

        logInfo(TAG, "开始转换Compose内容到图片文件，格式：${imageFormat.extension}，尺寸：${width}x${height}")

        // 1. 检查并创建目录
        ensureDirectoryExists(vfViewCacheDir)

        // 2. 将Compose内容转换为Bitmap
        val bitmap = createBitmapFromCompose(content, width, height, density, layoutDirection)

        try {
            // 3. 生成唯一文件名
            val fileName = generateUniqueFileName(imageFormat.extension)
            val imageFile = File(vfViewCacheDir, fileName)

            // 4. 保存Bitmap到文件
            withContext(Dispatchers.IO) {
                saveBitmapToFile(bitmap, imageFile, imageFormat, quality)
            }

            val filePath = imageFile.absolutePath
            logInfo(TAG, "Compose内容转换为图片成功，保存路径：$filePath")

            return@withContext filePath

        } finally {
            // 5. 及时回收Bitmap内存
            if (!bitmap.isRecycled) {
                bitmap.recycle()
                logInfo(TAG, "Bitmap内存已回收")
            }
        }
    }

    /**
     * 将ComposeView转换为图片并保存到指定目录
     *
     * @param composeView 需要转换的ComposeView
     * @param vfViewCacheDir 保存图片的目录
     * @param imageFormat 图片格式，默认为PNG
     * @param quality 图片质量（0-100），仅对JPEG格式有效，默认为90
     * @param useHardwareAcceleration 是否使用硬件加速，默认为true
     * @return 保存的图片文件的完整路径
     *
     * @throws ViewToBitmapException 当ComposeView转换失败时抛出
     * @throws FileSaveException 当文件保存失败时抛出
     * @throws DirectoryCreateException 当目录创建失败时抛出
     */
    suspend fun convertComposeViewToImageFile(
        composeView: ComposeView,
        vfViewCacheDir: File,
        imageFormat: ImageFormat = ImageFormat.PNG,
        quality: Int = 90,
        useHardwareAcceleration: Boolean = true
    ): String {
        logInfo(TAG, "开始转换ComposeView到图片文件，格式：${imageFormat.extension}")

        // ComposeView本质上也是View，可以直接使用现有的View转换方法
        return convertViewToImageFile(
            placeHolderView = composeView,
            vfViewCacheDir = vfViewCacheDir,
            imageFormat = imageFormat,
            quality = quality,
            useHardwareAcceleration = useHardwareAcceleration
        )
    }

    /**
     * 将View转换为Bitmap
     * 支持硬件加速和软件渲染两种模式
     * 
     * @param view 需要转换的View
     * @param useHardwareAcceleration 是否使用硬件加速
     * @return 转换后的Bitmap
     * 
     * @throws ViewToBitmapException 当转换失败时抛出
     */
    private fun createBitmapFromView(view: View, useHardwareAcceleration: Boolean): Bitmap {
        try {
            // 确保View已经测量和布局
            if (view.width <= 0 || view.height <= 0) {
                throw ViewToBitmapException("View的宽度或高度为0，无法转换为Bitmap。请确保View已经完成测量和布局。")
            }
            
            val width = view.width
            val height = view.height
            
            logInfo(TAG, "View尺寸：${width}x${height}，使用硬件加速：$useHardwareAcceleration")
            
            val bitmap = if (useHardwareAcceleration && Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                // 使用硬件加速渲染（Android 9.0+，因为Bitmap.createBitmap(Picture)方法在API 28才引入）
                createBitmapWithHardwareAcceleration(view, width, height)
            } else {
                // 使用软件渲染
                createBitmapWithSoftwareRendering(view, width, height)
            }
            
            if (bitmap == null) {
                throw ViewToBitmapException("Bitmap创建失败，返回null")
            }
            
            logInfo(TAG, "Bitmap创建成功，配置：${bitmap.config}")
            return bitmap
            
        } catch (e: Exception) {
            logError(TAG, "View转Bitmap失败：${e.message}")
            throw ViewToBitmapException("View转换为Bitmap失败", e)
        }
    }

    /**
     * 将Compose内容转换为Bitmap
     * 使用GraphicsLayer进行渲染，支持硬件加速
     *
     * @param content Compose内容
     * @param width 图片宽度（像素）
     * @param height 图片高度（像素）
     * @param density 屏幕密度
     * @param layoutDirection 布局方向
     * @return 转换后的Bitmap
     *
     * @throws ViewToBitmapException 当转换失败时抛出
     */
    private suspend fun createBitmapFromCompose(
        content: @Composable () -> Unit,
        width: Int,
        height: Int,
        density: Density,
        layoutDirection: LayoutDirection
    ): Bitmap = withContext(Dispatchers.Main) {
        try {
            if (width <= 0 || height <= 0) {
                throw ViewToBitmapException("Compose内容的宽度或高度必须大于0，当前尺寸：${width}x${height}")
            }

            logInfo(TAG, "开始创建Compose Bitmap，尺寸：${width}x${height}，密度：${density.density}")

            // 创建一个临时的ComposeView来渲染内容
            val composeView = ComposeView(appContext)

            // 设置ComposeView的内容
            composeView.setContent {
                CompositionLocalProvider(
                    LocalDensity provides density,
                    LocalLayoutDirection provides layoutDirection
                ) {
                    content()
                }
            }

            // 测量和布局ComposeView
            val widthMeasureSpec = View.MeasureSpec.makeMeasureSpec(width, View.MeasureSpec.EXACTLY)
            val heightMeasureSpec = View.MeasureSpec.makeMeasureSpec(height, View.MeasureSpec.EXACTLY)

            composeView.measure(widthMeasureSpec, heightMeasureSpec)
            composeView.layout(0, 0, width, height)

            // 等待Compose内容完成渲染
            // 这里使用一个简单的延迟来确保渲染完成
            kotlinx.coroutines.delay(100)

            // 将ComposeView转换为Bitmap
            val bitmap = withContext(Dispatchers.IO) {
                createBitmapFromView(composeView, true)
            }

            logInfo(TAG, "Compose Bitmap创建成功，配置：${bitmap.config}")
            return@withContext bitmap

        } catch (e: Exception) {
            logError(TAG, "Compose转Bitmap失败：${e.message}")
            throw ViewToBitmapException("Compose内容转换为Bitmap失败", e)
        }
    }

    /**
     * 使用硬件加速创建Bitmap（Android 9.0+）
     * 注意：Bitmap.createBitmap(Picture)方法在API 28（Android 9）才引入
     */
    @RequiresApi(Build.VERSION_CODES.P)
    private fun createBitmapWithHardwareAcceleration(view: View, width: Int, height: Int): Bitmap {
        val picture = Picture()
        val canvas = picture.beginRecording(width, height)
        view.draw(canvas)
        picture.endRecording()
        return Bitmap.createBitmap(picture)
    }
    
    /**
     * 使用软件渲染创建Bitmap
     */
    private fun createBitmapWithSoftwareRendering(view: View, width: Int, height: Int): Bitmap {
        val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)
        view.draw(canvas)
        return bitmap
    }
    
    /**
     * 将Bitmap保存到文件
     * 
     * @param bitmap 要保存的Bitmap
     * @param file 目标文件
     * @param imageFormat 图片格式
     * @param quality 图片质量
     * 
     * @throws FileSaveException 当保存失败时抛出
     */
    private fun saveBitmapToFile(
        bitmap: Bitmap,
        file: File,
        imageFormat: ImageFormat,
        quality: Int
    ) {
        var outputStream: FileOutputStream? = null
        try {
            outputStream = FileOutputStream(file)
            
            val actualQuality = if (imageFormat == ImageFormat.PNG) {
                // PNG格式不支持质量参数，使用100
                100
            } else {
                // JPEG格式使用指定质量，确保在有效范围内
                quality.coerceIn(0, 100)
            }
            
            val success = bitmap.compress(imageFormat.compressFormat, actualQuality, outputStream)
            
            if (!success) {
                throw FileSaveException("Bitmap压缩失败")
            }
            
            outputStream.flush()
            logInfo(TAG, "图片保存成功，格式：${imageFormat.extension}，质量：$actualQuality")
            
        } catch (e: IOException) {
            logError(TAG, "文件保存IO异常：${e.message}")
            throw FileSaveException("文件保存失败", e)
        } catch (e: Exception) {
            logError(TAG, "文件保存异常：${e.message}")
            throw FileSaveException("文件保存失败", e)
        } finally {
            try {
                outputStream?.close()
            } catch (e: IOException) {
                logError(TAG, "关闭文件流异常：${e.message}")
            }
        }
    }
    
    /**
     * 确保目录存在，如不存在则创建
     * 
     * @param directory 目标目录
     * @throws DirectoryCreateException 当目录创建失败时抛出
     */
    private fun ensureDirectoryExists(directory: File) {
        try {
            if (!directory.exists()) {
                val created = directory.mkdirs()
                if (!created) {
                    throw DirectoryCreateException("目录创建失败：${directory.absolutePath}")
                }
                logInfo(TAG, "目录创建成功：${directory.absolutePath}")
            } else {
                logInfo(TAG, "目录已存在：${directory.absolutePath}")
            }
            
            // 检查目录是否可写
            if (!directory.canWrite()) {
                throw DirectoryCreateException("目录无写入权限：${directory.absolutePath}")
            }
            
        } catch (e: SecurityException) {
            logError(TAG, "目录权限异常：${e.message}")
            throw DirectoryCreateException("目录权限不足", e)
        } catch (e: Exception) {
            logError(TAG, "目录创建异常：${e.message}")
            throw DirectoryCreateException("目录创建失败", e)
        }
    }
    
    /**
     * 生成唯一的文件名
     * 
     * @param extension 文件扩展名
     * @return 唯一的文件名
     */
    private fun generateUniqueFileName(extension: String): String {
        val timestamp = System.currentTimeMillis()
        val uuid = UUID.randomUUID().toString().replace("-", "").substring(0, 8)
        return "view_${timestamp}_${uuid}.$extension"
    }

    /**
     * 使用简化方法将Compose内容转换为Bitmap（推荐方法）
     * 通过创建临时ComposeView来实现截图
     *
     * @param content Compose内容
     * @param vfViewCacheDir 保存图片的目录
     * @param width 图片宽度（像素）
     * @param height 图片高度（像素）
     * @param imageFormat 图片格式，默认为PNG
     * @param quality 图片质量（0-100），仅对JPEG格式有效，默认为90
     * @param density 屏幕密度，默认使用应用上下文的密度
     * @param layoutDirection 布局方向，默认为LTR
     * @return 保存的图片文件的完整路径
     *
     * @throws ViewToBitmapException 当Compose内容转换失败时抛出
     * @throws FileSaveException 当文件保存失败时抛出
     * @throws DirectoryCreateException 当目录创建失败时抛出
     */
    suspend fun convertComposeToImageFileSimple(
        content: @Composable () -> Unit,
        vfViewCacheDir: File,
        width: Int,
        height: Int,
        imageFormat: ImageFormat = ImageFormat.PNG,
        quality: Int = 90,
        density: Density = Density(appContext),
        layoutDirection: LayoutDirection = LayoutDirection.Ltr
    ): String {
        logInfo(TAG, "使用简化方法转换Compose内容到图片文件，格式：${imageFormat.extension}，尺寸：${width}x${height}")

        // 直接使用现有的convertComposeToImageFile方法
        return convertComposeToImageFile(
            content = content,
            vfViewCacheDir = vfViewCacheDir,
            width = width,
            height = height,
            imageFormat = imageFormat,
            quality = quality,
            density = density,
            layoutDirection = layoutDirection
        )
    }
}
