package com.interfun.buz.common.interceptor

import coil.intercept.Interceptor
import coil.request.ErrorResult
import coil.request.ImageResult
import com.interfun.buz.base.ktx.logInfo

class CoilLogInterceptor : Interceptor {
    companion object {
        const val TAG = "AesDecryptionCoilInterceptor"
    }

    override suspend fun intercept(chain: Interceptor.Chain): ImageResult {
        val request = chain.request
        val result = chain.proceed(request)
        if (result is ErrorResult) {
            logInfo(
                TAG,
                "CoilLogInterceptor request failed, url=${request.data}, throwable=${result.throwable.message}"
            )
        } else {
            logInfo(TAG, "CoilLogInterceptor request success, url=${request.data}")
        }
        return result
    }

}