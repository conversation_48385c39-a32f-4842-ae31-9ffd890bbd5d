package com.interfun.buz.common.broadcast

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.bean.voicecall.CallType
import com.interfun.buz.common.bean.voicecall.ChannelType
import com.interfun.buz.common.constants.IncomingRealTimeCallSource
import com.interfun.buz.common.ktx.getLongDefault
import com.interfun.buz.common.manager.chat.ChannelInviteManager
import com.interfun.buz.common.service.RealTimeCallService
import com.interfun.buz.common.utils.CommonTracker
import com.interfun.buz.common.utils.NotificationUtil

class DeclineChannelReceiver:BroadcastReceiver() {
    private val TAG = "DeclineVoiceCallReceiver"

    companion object{
        const val NOTIFICATION_ID = "notification_id"
        const val CALL_CHANNEL_ID = "call_channel_id" //当用户点击拒绝接听时，channelId是有正常值的（>0）
        const val STOP_RINGTONE = "stop_ringtone"

        const val DECLINE_FROM_CALL_STYLE = "declineFromCallStyle"//仅用于区分来源进行埋点
        const val DECLINE_CHANNEL_TYPE = "decline_channel_type"//仅用于埋点
        const val DECLINE_CALL_TYPE = "decline_call_type"//仅用于埋点
    }

    override fun onReceive(context: Context?, intent: Intent?) {
        val notificationId = intent?.getIntExtra(NOTIFICATION_ID,0)
        val channelId = intent?.getLongExtra(CALL_CHANNEL_ID,0)
        val stopRingtone = intent?.getBooleanExtra(STOP_RINGTONE, true)?:true
        val declineFromCallStyle = intent?.getBooleanExtra(DECLINE_FROM_CALL_STYLE, false)?:false
        val channelType = intent?.getIntExtra(DECLINE_CHANNEL_TYPE, ChannelType.TYPE_VOICE_CALL_1V1)?: ChannelType.TYPE_VOICE_CALL_1V1
        val callType = intent?.getIntExtra(DECLINE_CALL_TYPE, CallType.TYPE_VOICE)?: CallType.TYPE_VOICE

        logInfo(TAG,"onReceive: notificationId = $notificationId, channelId = $channelId, stopRingtone = $stopRingtone $declineFromCallStyle")
        if (notificationId != null){
            NotificationUtil.cancelVoiceCallNotificationAndUpdatePendStatus(notificationId,channelType, channelId, stopRingtone)
        }
        if (declineFromCallStyle){
            ChannelInviteManager.removeInviteMapByChannelId(channelId.toString())
            ChannelInviteManager.dismiss(channelId.toString(), emitCancel = true)
            routerServices<RealTimeCallService>().value?.onClickPendAnswerEvent(
                channelId = channelId.getLongDefault(),
                channelType = channelType,
                callType = callType,
                isAnswer = false,
                source = IncomingRealTimeCallSource.Notification
            )
            if (ChannelType.isLivePlaceType(channelType)){
                CommonTracker.onClickAnswerOnAirInvite(ChannelType.isPrivateChannel(channelType),channelId.toString(),false)
            }
        }
    }

}