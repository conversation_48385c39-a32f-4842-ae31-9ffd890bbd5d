package com.interfun.buz.common.ccoder

import android.content.Context
import com.google.android.gms.common.GoogleApiAvailability
import com.interfun.buz.common.constants.TrackConstant
import com.interfun.buz.common.manager.launchAsync
import com.interfun.buz.common.utils.BuzTracker
import com.yibasan.lizhifm.lzlogan.Logz
import com.yibasan.lizhifm.sdk.platformtools.ApplicationContext
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch

object DetectTrack {
    val TAG = "DetectTrack"

    var report: Boolean = true

    /**
     * 上报设备所支持的音视频支持
     */
    fun reportDeviceInfo() {
        /**
         * 不要阻塞主线程渲染流程 下面代码可以忽律严格的多线程问题，多打没问题的
         */
        GlobalScope.launchAsync {
            kotlinx.coroutines.delay(3000)
            try {
                if (report) {
                    reportAvailableGP(ApplicationContext.getContext())
                    report = false
                }
            } catch (e: Exception) {
                Logz.tag(TAG).e(e)
            }
        }
    }

    private fun reportAvailableGP(activity: Context) {
        try {
            val googlePlayServicesAvailable =
                GoogleApiAvailability.getInstance().isGooglePlayServicesAvailable(activity);
            val apkVersion = GoogleApiAvailability.getInstance().getApkVersion(activity)
            val clientVersion = GoogleApiAvailability.getInstance().getClientVersion(activity)
            Logz.tag(TAG).i("googlePlayServicesAvailable ${googlePlayServicesAvailable} apkVersion ${apkVersion} clientVersion ${clientVersion}")
            BuzTracker.onTechTrack {
                put(TrackConstant.KEY_EXCLUSIVE_ID, "TT2024031501")
                put(TrackConstant.KEY_EVENT_NAME, "avaliableGPService")
                put(TrackConstant.KEY_CONTENT_1, "${googlePlayServicesAvailable}")
                put(TrackConstant.KEY_CONTENT_2, "${apkVersion}")
                put(TrackConstant.KEY_CONTENT_3, "${clientVersion}")
            }
        } catch (e: Exception) {
            Logz.tag(TAG).e(e)
        }
    }
}
