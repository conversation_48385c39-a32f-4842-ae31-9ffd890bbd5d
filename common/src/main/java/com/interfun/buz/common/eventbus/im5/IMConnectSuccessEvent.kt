package com.interfun.buz.common.eventbus.im5

import com.interfun.buz.common.eventbus.BaseEvent
import com.jeremyliao.liveeventbus.LiveEventBus
import org.greenrobot.eventbus.EventBus

/**
 * Desc: IM连接成功事件 </p>
 * Author: lv<PERSON><PERSON><PERSON>@lizhi.fm
 * Time: 2021/6/24.
 */
class IMConnectSuccessEvent: BaseEvent() {

    companion object{
        fun post(){
            //LiveEventBus
            LiveEventBus.get(IMConnectSuccessEvent::class.java).post(IMConnectSuccessEvent())
        }
    }
}