package com.interfun.buz.common.interfaces

import com.lizhi.component.tekiplayer.MediaItem
import com.lizhi.component.tekiplayer.PlayEventListener

/**
 * <AUTHOR>
 * @date 2024/4/9
 * @desc
 */
abstract class TekiPlayerListenerAdapter : PlayEventListener {
    override fun onBufferedPositionUpdate(index: Int, item: MediaItem?, bufferPosition: Long) {}

    override fun onError(errCode: Int, message: String) {}

    override fun onPlayListFinished() {}

    override fun onPlayListUpdate() {}

    override fun onPlayZeroItem(item: MediaItem?) {}

    override fun onPlaybackChange(index: Int, item: MediaItem?, lastPosition: Long, reason: Int) {}

    override fun onPlaybackRemoveOnList() {}

    override fun onPlaybackStateChange(status: Int) {}

    override fun onPlayedPositionUpdate(index: Int, item: MediaItem?, position: Long) {}
}