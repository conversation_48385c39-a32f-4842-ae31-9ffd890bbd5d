package com.interfun.buz.common.interceptor

import com.interfun.buz.base.ktx.logInfo
import com.lizhi.fm.e2ee.aes.IAesComponent
import okhttp3.Interceptor
import okhttp3.Response
import okhttp3.ResponseBody
import okhttp3.ResponseBody.Companion.asResponseBody
import okio.BufferedSource
import okio.buffer
import okio.source
import java.io.InputStream

class AesDecryptionInterceptor(val aesComponent: IAesComponent) : Interceptor {

    private val TAG = "AesDecryptionInterceptor"

    override fun intercept(chain: Interceptor.Chain): Response {
        logInfo(TAG, "intercept start.... ")
        val request = chain.request()
        val response = chain.proceed(request)
        // Decryption response body
        val decryptedResponseBody = decryptResponse(response.body)
        // Build new response, replace encrypted ResponseBody
        return response.newBuilder()
            .body(decryptedResponseBody)
            .build()
    }

    private fun decryptResponse(responseBody: ResponseBody?): ResponseBody? {
        // Get encrypted InputStream
        val encryptedInputStream = responseBody?.byteStream()
        // Decrypting InputStream
        val decryptedInputStream = decryptInputStream(encryptedInputStream)
        // Build the decrypted ResponseBody
        return decryptedInputStream?.asResponseBody(responseBody?.contentType(), responseBody?.contentLength() ?: -1)
    }


    private fun decryptInputStream(inputStream: InputStream?): BufferedSource? {
        logInfo(TAG, "decryptInputStream start.... ")
        var decryptInputStream: InputStream? = null
        try {
            decryptInputStream = inputStream?.let { aesComponent.decryptInputStreamToInputStream(it) }
        } catch (e: Exception) {
            logInfo(TAG, "decryptInputStream error ${e.message}")
        }
        logInfo(TAG, "decryptInputStream end.... ")
        return decryptInputStream?.source()?.buffer()
    }

}