package com.interfun.buz.common.constants

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.PorterDuff
import android.graphics.PorterDuffColorFilter
import android.graphics.Rect
import android.location.LocationManager
import android.net.Uri
import android.os.Parcelable
import android.provider.Settings
import androidx.annotation.DrawableRes
import androidx.core.app.ActivityCompat
import androidx.core.content.PermissionChecker
import com.interfun.buz.base.ktx.asColor
import com.interfun.buz.base.ktx.asDimension
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.base.ktx.dp
import com.interfun.buz.common.R
import com.interfun.buz.common.gns.GmsStateHelper
import com.interfun.buz.common.widget.dialog.CommonAlertDialog
import com.yibasan.lizhifm.sdk.platformtools.ApplicationContext
import kotlinx.parcelize.Parcelize


interface ILocationPermissionCheck {
    fun onGranted()

    fun onDenied()
    fun onGmsInvalid()
}

@Parcelize
sealed class LocationType(val content: String) : Parcelable {
    data object CurrentLocation : LocationType("current_location")
    data object NearbyLocation : LocationType("nearby_location")
    data object PinLocation : LocationType("pin_location")
    data object SearchLocation : LocationType("search_location")
}

object MapLocationHelper {
    val KEY_RESULT_FOR_LOCATION = "key_result_for_location"
    val KEY_RESULT_FOR_INFO = "key_result_for_info"
    val KEY_RESULT_FOR_LOCATION_TYPE = "KEY_RESULT_FOR_LOCATION_TYPE"

    fun createMapMark(
        width: Int,
        /**
         * 注意这个高度不包含dot高度，内部会自行处理
         */
        height: Int,
        @DrawableRes resId: Int,
        @DrawableRes resCenterIcon: Int,
        centerIcon: Bitmap?,
        enableDot: Boolean = false
    ): Bitmap {
        val anchor =
            BitmapFactory.decodeResource(ApplicationContext.getApplication().resources, resId)
        val centerIcon = centerIcon ?: BitmapFactory.decodeResource(
            ApplicationContext.getApplication().resources,
            resCenterIcon
        )

        val dotHeight = width / 10
        val dotRadius = dotHeight / 2
        val centerWidthPercent = 32 / 60f
        val centerWidth = centerWidthPercent * width
        val centerHeight = centerWidth
        val centerStart = (width - centerWidth) / 2
        val centerEnd = width - centerStart
        val centerTop = (height - centerHeight) / 2
        val centerIconBias = (height * 1 / 11f) / 2

        val gap = R.dimen.chat_map_anchor_gap.asDimension().toInt()
        val bitMapHeight = if (enableDot) dotHeight + gap + height else height
        val bitmap = Bitmap.createBitmap(width, bitMapHeight, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)
        val paint = Paint()
        canvas.drawBitmap(anchor, null, Rect(0, 0, width, height), paint)
        val colorFilter = PorterDuffColorFilter(
            R.color.text_black_main.asColor(),
            PorterDuff.Mode.SRC_ATOP
        )
        paint.style = Paint.Style.STROKE
        paint.isAntiAlias = true
        paint.strokeWidth=0f
        paint.setColorFilter(colorFilter)
        val centerIconTop = centerTop.toInt() - centerIconBias.toInt()
        canvas.drawBitmap(
            centerIcon,
            null,
            Rect(
                centerStart.toInt(),
                centerIconTop,
                centerEnd.toInt(),
                (centerIconTop + centerHeight).toInt()
            ),
            paint
        )
        paint.setColorFilter(null)

        paint.color = R.color.basic_primary.asColor()
        paint.style = Paint.Style.FILL
        if (enableDot) {
            canvas.drawCircle(
                width / 2f,
                bitMapHeight - dotRadius.toFloat(),
                dotRadius.toFloat(),
                paint
            )
        }
        return bitmap
    }

    fun createPoiBitmap(
        width: Int,
        /**
         * 注意这个高度不包含dot高度，内部会自行处理
         */
        height: Int,
    ): Bitmap {

        val paint = Paint()
        val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)
        paint.color = R.color.white.asColor()
        canvas.drawCircle(
            width / 2f,
            height / 2f,
            height / 2f,
            paint
        )
        paint.color = R.color.basic_primary.asColor()
        canvas.drawCircle(
            width / 2f,
            height / 2f,
            (height / 2f) - (height / 2 * 0.2f),
            paint
        )
        return bitmap
    }

    fun openMap(activity: Activity, lat: Double, lon: Double, title: String): Boolean {
        val uri =
            Uri.parse("geo:${lat},${lon}?q=${lat},${lon}(${title})")
        val mapIntent = Intent(Intent.ACTION_VIEW, uri)
        mapIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        val resolveActivity = mapIntent.resolveActivity(activity.packageManager)
        if (resolveActivity != null) {
            activity.startActivity(mapIntent)
            return true
        } else {
            CommonAlertDialog(
                context = activity,
                title = com.interfun.buz.common.R.string.install_google_maps.asString(),
                positiveText = R.string.ok.asString(),
                positiveCallback = {
                    it.dismiss()
                },
            ).show()
        }
        return false
    }

    fun checkPermission(activity: Activity, callBack: ILocationPermissionCheck) {
        if (!GmsStateHelper.gmsAvailable()) {
            return callBack.onGmsInvalid()
        }
        val permission1 = PermissionChecker.checkSelfPermission(
            activity,
            Manifest.permission.ACCESS_FINE_LOCATION
        )
        val permission2 = PermissionChecker.checkSelfPermission(
            activity,
            Manifest.permission.ACCESS_COARSE_LOCATION
        )

        if (permission1 == PermissionChecker.PERMISSION_GRANTED && permission2 == PermissionChecker.PERMISSION_GRANTED) {
            callBack.onGranted()
        } else {
            callBack.onDenied()
        }
    }

    fun shouldShowRequestPermissionRationale(activity: Activity): Boolean {
        if (
            ActivityCompat.shouldShowRequestPermissionRationale(
                activity,
                Manifest.permission.ACCESS_COARSE_LOCATION
            ).not() ||
            ActivityCompat.shouldShowRequestPermissionRationale(
                activity,
                Manifest.permission.ACCESS_FINE_LOCATION
            ).not()
        ) {
            return false
        } else {
            //权限检查
            return true
        }
    }

    fun buildPermissionDlg(activity: Activity) {
        CommonAlertDialog(
            context = activity,
            title = R.string.chat_permission_tips_dialog_title.asString(),
            tips = R.string.map_access_your_location.asString(),
            positiveText = R.string.settings.asString(),
            negativeText = R.string.cancel.asString(),
            positiveCallback = {
                jumpSettingPer(activity)
                it.dismiss()
            },
            negativeCallback = {
                it.dismiss()
            }
        ).show()

    }

    /**
     * 由于拒绝了权限需要跳转到系统设置界面
     */
    fun jumpSettingPer(activity: Activity) {
        val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
            .setData(Uri.fromParts("package", activity.packageName, null))
        activity.startActivity(intent)
    }

    fun shouldOpenGps(requireActivity: Activity, openedGps: () -> Unit) {
        val lms = requireActivity.getSystemService(Context.LOCATION_SERVICE) as LocationManager
        if (!lms.isProviderEnabled(LocationManager.GPS_PROVIDER)) {
            CommonAlertDialog(
                context = requireActivity,
                title = R.string.chat_permission_tips_dialog_title.asString(),
                tips = R.string.open_gps.asString(),
                positiveText = R.string.settings.asString(),
                negativeText = R.string.cancel.asString(),
                positiveCallback = {
                    val intent = Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS)
                    requireActivity.startActivity(intent)
                    it.dismiss()
                },
                negativeCallback = {
                    it.dismiss()
                }
            ).show()
        } else {
            openedGps.invoke()
        }
    }
}