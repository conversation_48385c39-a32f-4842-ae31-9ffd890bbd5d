package com.interfun.buz.common.interceptor

import okhttp3.Interceptor
import okhttp3.Request
import okhttp3.Response
import okhttp3.ResponseBody
import okhttp3.MediaType
import okio.*

class ProgressInterceptor(private val listener: ProgressListener) : Interceptor {

    interface ProgressListener {
        fun onProgress(request: Request, bytesRead: Long, contentLength: Long, done: Boolean)
    }

    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest = chain.request()
        val response = chain.proceed(originalRequest)

        return response.newBuilder()
            .body(ProgressResponseBody(originalRequest, response.body, listener))
            .build()
    }

}

class ProgressResponseBody(private val request: Request, private val responseBody: ResponseBody?, private val progressListener: ProgressInterceptor.ProgressListener) :
    ResponseBody() {

    private var bufferedSource: BufferedSource? = null

    override fun contentLength(): Long {
        return responseBody?.contentLength() ?: 0L
    }

    override fun contentType(): MediaType? {
        return responseBody?.contentType()
    }

    override fun source(): BufferedSource {
        if (bufferedSource == null) {
            bufferedSource = source(responseBody!!.source()).buffer()
        }
        return bufferedSource!!
    }

    private fun source(source: Source): Source {
        return object : ForwardingSource(source) {
            var totalBytesRead = 0L

            override fun read(sink: Buffer, byteCount: Long): Long {
                val bytesRead = super.read(sink, byteCount)
                totalBytesRead += if (bytesRead != -1L) bytesRead else 0
                progressListener.onProgress(request, totalBytesRead, responseBody!!.contentLength(), bytesRead == -1L)
                return bytesRead
            }
        }
    }
}

