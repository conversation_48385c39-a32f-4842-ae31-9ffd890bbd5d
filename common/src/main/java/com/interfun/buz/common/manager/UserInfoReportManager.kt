package com.interfun.buz.common.manager

import com.buz.idl.common.bean.ReportUserInfo
import com.buz.idl.common.bean.SwitchStatus
import com.buz.idl.common.request.RequestReportUserInfo
import com.buz.idl.common.service.BuzNetCommonServiceClient
import com.interfun.buz.base.ktx.OneParamCallbackSuspend
import com.interfun.buz.base.ktx.log
import com.interfun.buz.common.manager.cache.user.UserSettingManager
import com.interfun.buz.common.net.withConfig
import com.interfun.buz.common.utils.language.LanguageManager
import com.interfun.buz.common.utils.language.LanguageProvider.DEFAULT_LANGUAGE
import com.interfun.buz.common.utils.parse
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch

/**
 * <AUTHOR>
 * @time 2023/5/30
 * @desc
 **/
object UserInfoReportManager {
    private const val TAG = "UserInfoReportManager"

    private var reportJob: Job? = null

    fun reportUserInfo(
        languageCode: String? = LanguageManager.getLocaleLanguageCode(),
        smartAsrSwitch: Int? = null, // 自动 asr 开关，仅变动时传递
        switchStatusList: List<SwitchStatus>? = null, // 用户id
        settingMap: Map<String, String>? = null,
        callback: OneParamCallbackSuspend<Int>? = null
    ) {
        if (reportJob?.isActive == true) {
            reportJob?.cancel()
        }
        reportJob = userLifecycleScope?.launch(Dispatchers.IO) {
            val code = LanguageManager.getReportLanguageCode(
                languageCode = languageCode ?: DEFAULT_LANGUAGE.code
            )
            val reportUserInfo = ReportUserInfo(
                language = code,
                quietMode = if (UserSettingManager.isQuietModeEnable) 2 else 1,
                sendTimestamp = System.currentTimeMillis(),
                switchStatusList = switchStatusList,
                smartAsrSwitch = smartAsrSwitch,
                settingMap = settingMap
            )
            log(TAG, "reportUserInfo quietMode: ${UserSettingManager.isQuietModeEnable} languageCode: $code settingMap: $settingMap")
            val request = RequestReportUserInfo(reportUserInfo)
            val resp = BuzNetCommonServiceClient().withConfig().reportUserInfo(request)
            resp.data?.prompt?.parse()
            log(TAG, "responseReportUserInfo code: ${resp.code}  msg: ${resp.msg}")
            callback?.invoke(resp.code)
        }
    }
}
