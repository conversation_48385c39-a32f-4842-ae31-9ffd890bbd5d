package com.interfun.buz.common.service

import android.content.Context
import android.content.Intent
import android.os.Parcelable
import android.view.View
import android.view.WindowManager
import androidx.fragment.app.Fragment
import androidx.lifecycle.LiveData
import com.alibaba.android.arouter.facade.template.IProvider
import com.buz.idl.common.bean.PopWindow
import com.interfun.buz.base.ktx.DefaultCallback
import com.interfun.buz.common.base.BaseActivity
import com.interfun.buz.common.base.BaseFragment
import com.interfun.buz.common.base.BaseManualBlock
import com.interfun.buz.common.bean.HomeWTItemType
import com.interfun.buz.common.database.entity.UserRelationInfo
import com.interfun.buz.common.widget.dialog.BaseBottomSheetDialogFragment
import com.interfun.buz.common.widget.dialog.BasePriorityBottomSheetDialogFragment
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import com.lizhi.im5.sdk.message.IMessage
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.StateFlow

interface IWTFloatingViewManager {
    fun getTarget(): Any?
    fun getLayoutResId(): Int
    fun getRootViewId(): Int
    fun onViewCreated(newView: View, target: Any)
    fun destroyView()
    fun getFloatWindowTag(): String
    fun setInitialized(isInitialized: Boolean)
    fun updateOrientation(screenStartX: Int, screenEndX: Int)
    fun updateWindowView(windowView: View, params: WindowManager.LayoutParams)
    fun isCollapsed(): Boolean
    fun minimize()
    fun expand()
    fun getStateLogString() : String?
    fun dragAnimStart()
    fun dragAnimEnd()
    fun updateInLeft(isInLeft:Boolean)
}

enum class GlobalPlayingState {
    IDLE,
    PLAYING,
    PAUSE
}

data class GlobalPlayingMsgState(val iMessage: IMessage?, val state: GlobalPlayingState)

interface ChatService : IProvider {
    /**
     * 当前是否正在预览语音滤镜
     */
    fun chatPreviewVF(): StateFlow<Boolean>

    fun setChatPreviewVF(isPreview: Boolean)

    fun createHomeIntent(context: Context, router: String?): Intent

    fun getPrivateChatJumpInfo(
        userId: Long,
        source: String,
        serMsgId: String? = null,
        reactionOpUserId: String? = null,
        msgId: Long? = null,
        openVoiceFilter:Boolean = false,
        voiceFilterId:Long? = null
    ): Parcelable

    fun getGroupChatJumpInfo(
        groupId: Long,
        source: String,
        serMsgId: String? = null,
        addressUserInfo: UserRelationInfo? = null,
        reactionOpUserId: String? = null,
        msgId: Long? = null,
    ): Parcelable
    fun getConversationAutoTranslateSwitch(conversationId: Long): Boolean
    fun enableTranslationFun(): Boolean
    fun closeGroupChat(groupChatFragment: Fragment?)

    fun getHomeAddressUserInfo(groupId: Long): UserRelationInfo?

    fun isChatHomeActivity(obj:Any?):Boolean

    fun isChatActivity(obj: Any?): Boolean

    fun setIsPlayOnlineChatRingtone(play: Boolean, targetId: Long? = null, isGroup: Boolean? = null)

    fun onLogin()

    fun onLogout()

    fun onLoginBeforeIM()

    fun createWTMessageBlock(activity: BaseActivity) : BaseManualBlock

    fun isWTOnlineStatus(): Boolean

    fun getWtSwitchFlow(): StateFlow<Boolean>

    fun checkIsWTOnlineStatus(doWhenWTOnline: DefaultCallback, doWhenWTOffline: DefaultCallback)

    fun requestChangeSwitchStatus(turnOn: Boolean)

    fun isWTRecording() :Boolean

    fun isRecordBtnPressing(): Boolean

    fun getGroupInfoDialog(groupId: Long,traceSource: Int = 0): BaseBottomSheetDialogFragment

    fun getInviteBotToGroupDialog(botUserId: Long): BaseBottomSheetDialogFragment

    fun createFlowViewManager(): IWTFloatingViewManager

    fun setQuietModeEnable(isEnable: Boolean)

    fun isQuietModeEnable(): Boolean

    fun muteMessagesByTargetId(targetId: Long)

    @Deprecated("为了兼容旧逻辑，请不要使用")
    fun getHomeListSize(): Int

    fun getInviteToGroupFragment(userId: Long): BaseBottomSheetDialogFragment

    fun getIsRecordingFlow(): StateFlow<Boolean>

    fun isPlayingVoiceMsg(): Boolean

    fun stopVoiceMessagePlayback()

    fun findEnterConversationId(conversationId: String): Boolean

    fun topActivityIsChatOngoing(conversationId: String, conversationType: IM5ConversationType): Boolean

    fun getMediaPlayerConflictBlock(fragment: BaseFragment): BaseManualBlock

    fun isTakingVideo(): Boolean

    fun isAlbumPreviewing(): Boolean

    fun setTakingVideoStatus(isTakingVideo: Boolean)

    fun setAlbumPreviewStatus(isPreview: Boolean)

    fun stopRecording()

    fun getChatVoiceCallMinimizeHeight(context: Context): Int

    suspend fun forwardShareIntent(
        activity: BaseActivity,
        intent: Intent
    )


    fun getCollectVoicemojiBlindBoxDialog(priority: Int = 0): BasePriorityBottomSheetDialogFragment

    fun setCollectVoicemojiBlindBoxDialogData(
        dialog: BasePriorityBottomSheetDialogFragment,
        popWindow: PopWindow
    )

    fun onChaHomeContactClick()

    fun onChaHomeMinePortraitClick()

    // 聊天界面是否在播放音频
    fun isPlayingChatListMsg(): Boolean

    fun getGlobalPlayedMsgFlow(): Flow<GlobalPlayingMsgState>

    fun nowChatListTargetId(): LiveData<Long?>

    val isPlayingFlow: Flow<Boolean>

}