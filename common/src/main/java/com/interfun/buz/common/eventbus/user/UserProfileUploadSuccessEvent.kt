package com.interfun.buz.common.eventbus.user

import com.interfun.buz.common.eventbus.BaseEvent
import com.jeremyliao.liveeventbus.LiveEventBus

/**
 * @Desc
 * @Author:lvzhen<PERSON><PERSON>@lizhi.fm
 * @Date: 2022/9/6
 */
class UserProfileUploadSuccessEvent(val uid: Long): BaseEvent() {

    companion object{
        fun post(userId: Long){
            LiveEventBus.get(UserProfileUploadSuccessEvent::class.java).post(UserProfileUploadSuccessEvent(userId))
        }
    }
}