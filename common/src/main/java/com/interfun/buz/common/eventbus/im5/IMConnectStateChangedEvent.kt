package com.interfun.buz.common.eventbus.im5

import com.interfun.buz.common.eventbus.BaseEvent
import com.jeremyliao.liveeventbus.LiveEventBus
import com.lizhi.im5.sdk.auth.AuthStatus

/**
 * Desc: im登陆 连接状态监听eventbus事件 </p>
 * Author: lv<PERSON><PERSON><PERSON>@lizhi.fm
 * Time: 2021/4/20.
 */
class IMConnectStateChangedEvent(val authStatus: AuthStatus) : BaseEvent() {

    companion object{
        fun post(authStatus: AuthStatus){
            LiveEventBus.get(IMConnectStateChangedEvent::class.java).post(IMConnectStateChangedEvent(authStatus))
        }
    }

}