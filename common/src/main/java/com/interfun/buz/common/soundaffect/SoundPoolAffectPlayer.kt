package com.interfun.buz.common.soundaffect

import android.media.AudioAttributes
import android.media.SoundPool
import android.util.LruCache
import androidx.annotation.OptIn
import androidx.media3.common.util.UnstableApi
import com.interfun.buz.base.ktx.log
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.base.ktx.put

/**
 *
 */
class SoundPoolAffectPlayer(
    val config: Config = Config()
) : SoundAffectPlayer {
    private val TAG = "SoundPoolAffectPlayer"
    private val defaultPriority = 1

    private val audioAttributes = AudioAttributes.Builder()
        .setUsage(config.usage)
        .setContentType(config.contentType)
        .build()

    private val soundPool = getSoundPool()
    private val soundIdCache = object : LruCache<String, Int>(config.cacheSize) {
        override fun entryRemoved(evicted: <PERSON><PERSON><PERSON>, key: String, oldValue: Int, newValue: Int?) {
            unload(oldValue)
        }
    }
    private val loadedIdPathMap = hashMapOf<Int, String>()
    private val loadedIdStreamIdMap = hashMapOf<Int, Int>()

    private fun getSoundPool(): SoundPool {
        val pool = SoundPool.Builder()
            .setAudioAttributes(audioAttributes)
            .setMaxStreams(config.maxStream)
            .build()

        pool.setOnLoadCompleteListener { _, soundId, status ->
            if (0 != status) return@setOnLoadCompleteListener
            saveLoadedSoundId(soundId)
            play(soundId)
        }
        return pool
    }

    @Synchronized
    override fun play(path: String) {
        val soundId = getLoadedId(path)
        if (soundId != null) {
            log(TAG, "play:$soundId,path:$path")
            play(soundId)
        } else {
            log(TAG, "play load:$path")
            load(path)
        }
    }

    @Synchronized
    private fun unload(loadId: Int) {
        loadedIdPathMap.remove(loadId)
        soundPool.unload(loadId)
    }

    @Synchronized
    private fun load(path: String): Int {
        val soundId = soundPool.load(path, defaultPriority)
        loadedIdPathMap.put(soundId, path)
        return soundId
    }

    @Synchronized
    private fun saveLoadedSoundId(soundId: Int) {
        val path = loadedIdPathMap.get(soundId)

        if (path != null) {
            soundIdCache.put(path, soundId)
        }
    }

    @Synchronized
    private fun getLoadedId(path: String): Int? {
        return soundIdCache.get(path)
    }

    @Synchronized
    override fun release() {
        soundIdCache.evictAll()
        soundPool.release()
    }

    @Synchronized
    override fun stop(path: String) {
        val soundId = getLoadedId(path)
        val streamId = loadedIdStreamIdMap[soundId]
        streamId?.let {
            if (it != 0) {
                soundPool.stop(it)
            }
        }
        loadedIdStreamIdMap.remove(soundId)
    }

    val volumeFactor =1/30f;
    private fun play(soundId: Int) {
        logInfo(TAG, "play:soundId = $soundId config = $config")
        val streamId = soundPool.play(
            soundId,
            //产品需求 sound pool 音量直接减半
            config.volume * volumeFactor,
            config.volume * volumeFactor,
            defaultPriority,
            0,
            1f
        )
        loadedIdStreamIdMap[soundId] = streamId
    }

    data class Config(
        var volume: Float = 1f,
        val maxStream: Int = 20,
        val cacheSize: Int = 20,
        val usage: Int = AudioAttributes.USAGE_MEDIA,
        val contentType: Int = AudioAttributes.CONTENT_TYPE_SONIFICATION,
    )
}