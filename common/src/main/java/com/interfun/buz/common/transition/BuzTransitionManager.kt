package com.interfun.buz.common.transition

import android.animation.Animator
import android.view.View
import android.view.View.OnAttachStateChangeListener
import android.view.ViewGroup
import android.view.ViewTreeObserver.OnPreDrawListener
import androidx.annotation.CallSuper
import androidx.core.animation.addListener
import androidx.core.view.children
import com.interfun.buz.base.ktx.OneParamCallback
import com.interfun.buz.base.ktx.logInfo

object BuzTransitionManager {
    const val TAG = "BuzTransitionManager"

    open class ShareElementTransitionCallback {
        var isFinished = false
            private set

        open fun onStart() {}

        open fun onTargetStartFound(view: View) {}

        open fun onTargetEndFound(view: View) {}

        open fun onAnimStart() {}

        open fun onAnimEnd() {}

        @CallSuper
        open fun onFinish() {
            isFinished = true
        }
    }

    fun startRemoveViewShareElementTransition(
        sceneRoot: ViewGroup,
        transition: Transition,
        sharedElementName: String,
        removeView: View,
        customAnim: Animator?
    ): Boolean {
        val targetStart = getTarget(removeView, sharedElementName, null) ?: return false
        val startValues = TransitionValues(targetStart)
        transition.captureStartValues(startValues)
        val targetEnd =
            getTarget(sceneRoot, targetStart.transitionName, targetStart) ?: return false
        val endValues = TransitionValues(targetEnd)
        transition.captureEndValues(endValues)
        removeView.traverseView {
            if (it is ViewGroup) {
                ViewUtils.setTransitionAlpha(it, 0f)
            }
        }
        var parent = targetStart.parent
        while (parent != null) {
            if (parent is ViewGroup) {
                ViewUtils.setTransitionAlpha(parent, 1f)
            }
            if (parent == targetStart) {
                break
            }
            parent = parent.parent
        }
        startTransition(transition, sceneRoot, startValues, endValues, customAnim)
        return true
    }

    fun startAddViewShareElementTransition(
        sceneRoot: ViewGroup,
        transition: Transition,
        sharedElementName: String,
        addView: View,
        customAnim: Animator?,
        callback: ShareElementTransitionCallback
    ) {
        callback.onStart()
        val targetStart = getTarget(sceneRoot, sharedElementName, null)
        if (targetStart == null) {
            callback.onFinish()
            return
        }
        callback.onTargetEndFound(targetStart)
        val startValues = TransitionValues(targetStart)
        transition.captureStartValues(startValues)
        val listener = MultiListener(
            sharedElementName,
            transition,
            sceneRoot,
            targetStart,
            addView,
            startValues,
            customAnim,
            callback,
        ) { endTarget ->
            addView.traverseView {
                if (it is ViewGroup) {
                    ViewUtils.setTransitionAlpha(it, 0f)
                }
            }
            var parent = endTarget.parent
            while (parent != null) {
                if (parent is ViewGroup) {
                    ViewUtils.setTransitionAlpha(parent, 1f)
                }
                if (parent == addView) {
                    break
                }
                parent = parent.parent
            }
        }
        transition.doOnEnd {
            logInfo(TAG, "startAddViewShareElementTransition doOnEnd")
            addView.traverseView {
                if (it is ViewGroup) {
                    ViewUtils.setTransitionAlpha(it, 1f)
                }
            }
        }
        sceneRoot.addOnAttachStateChangeListener(listener)
        sceneRoot.getViewTreeObserver().addOnPreDrawListener(listener)
        // TransitionManager.beginDelayedTransition(sceneRoot,transition)
    }

    private fun View.traverseView(action: OneParamCallback<View>) {
        action.invoke(this)
        if (this is ViewGroup) {
            for (child in this.children) {
                child.traverseView(action)
            }
        }
    }

    private class MultiListener internal constructor(
        val sharedElementName: String,
        val mTransition: Transition,
        val mSceneRoot: ViewGroup,
        val startView: View,
        val addView: View,
        val startValues: TransitionValues,
        val customAnim: Animator?,
        val callback: ShareElementTransitionCallback?,
        val beforeTransition: OneParamCallback<View>?
    ) : OnPreDrawListener, OnAttachStateChangeListener {
        private fun removeListeners() {
            mSceneRoot.getViewTreeObserver().removeOnPreDrawListener(this)
            mSceneRoot.removeOnAttachStateChangeListener(this)
        }

        override fun onViewAttachedToWindow(v: View) {
        }

        override fun onViewDetachedFromWindow(v: View) {
            removeListeners()
            if (callback?.isFinished == false) {
                callback.onFinish()
            }
        }

        override fun onPreDraw(): Boolean {
            removeListeners()
            mTransition.addListener(object : TransitionListenerAdapter() {
                override fun onTransitionEnd(transition: Transition) {
                    transition.removeListener(this)
                    callback?.onAnimEnd()
                    if (callback?.isFinished == false) {
                        callback.onFinish()
                    }
                }

                override fun onTransitionStart(transition: Transition) {
                    callback?.onAnimStart()
                }
            })
            val target = getTarget(addView, sharedElementName, null)
            if (target == null) {
                if (callback?.isFinished == false) {
                    callback.onFinish()
                }
                return false
            }
            callback?.onTargetEndFound(target)
            target.addOnAttachStateChangeListener(object : OnAttachStateChangeListener {
                override fun onViewAttachedToWindow(v: View) {
                }

                override fun onViewDetachedFromWindow(v: View) {
                    ViewUtils.setTransitionAlpha(startView, 1f)
                }
            })
            val endValues = TransitionValues(target)
            mTransition.captureEndValues(endValues)
            beforeTransition?.invoke(target)
            logInfo(TAG, "startTransition")
            startTransition(mTransition, mSceneRoot, startValues, endValues, customAnim)
            return true
        }
    }

    private fun startTransition(
        transition: Transition,
        sceneRoot: ViewGroup,
        startValues: TransitionValues,
        endValues: TransitionValues,
        customAnim: Animator?
    ) {
        if (transition is TransitionSet) {
            val transitionCount = transition.transitionCount
            if (transitionCount == 0) {
                transition.start()
                customAnim?.let { anim ->
                    anim.addListener(onEnd = {
                        transition.end()
                    }, onCancel = {
                        transition.end()
                    })
                    transition.start()
                    anim.start()
                }
                transition.end()
                return
            }
            for (i in 0 until transitionCount) {
                val childTransition = transition.getTransitionAt(i) ?: continue
                transition.start()
                childTransition.start()
                childTransition.createAnimator(sceneRoot, startValues, endValues)?.let { anim ->
                    anim.addListener(onEnd = {
                        childTransition.end()
                        transition.end()
                    }, onCancel = {
                        childTransition.end()
                        transition.end()
                    })
                    transition.start()
                    childTransition.start()
                    anim.start()
                }
                childTransition.end()
                transition.end()
            }
            customAnim?.let { anim ->
                anim.addListener(onEnd = {
                    transition.end()
                }, onCancel = {
                    transition.end()
                })
                transition.start()
                anim.start()
            }
        } else {
            val anim = transition.createAnimator(sceneRoot, startValues, endValues)
            anim?.addListener(onEnd = {
                transition.end()
            })
            transition.start()
            if (anim == null) {
                transition.end()
            }
            anim?.start()
        }
    }

    fun getTarget(view: View, transitionName: String, excludeView: View?): View? {
        if (view != excludeView && view.transitionName == transitionName) {
            return view
        }
        if (view != excludeView && view is ViewGroup) {
            for (child in view.children) {
                val childView = getTarget(child, transitionName, excludeView)
                if (childView != null) {
                    return childView
                }
            }
        }
        return null
    }
}