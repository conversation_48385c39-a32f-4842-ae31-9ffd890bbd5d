package com.interfun.buz.common.eventbus

import com.jeremyliao.liveeventbus.LiveEventBus
import com.lizhi.im5.sdk.conversation.IM5ConversationType

class ChatOnResumeEvent(val type: IM5ConversationType, val target: Long) : BaseEvent() {
    companion object {
        fun post(type: IM5ConversationType, target: Long) {
            LiveEventBus.get(ChatOnResumeEvent::class.java).post(ChatOnResumeEvent(type, target))
        }
    }
}