package com.interfun.buz.common.constants

/**
 * 路由常量表，请按照规范添加：
 * 1. 根据模块放置
 * 2. 常量格式：PATH_{模块名}_{类型名}_{业务名}
 * 3. ARouter路由常量格式："/{模块名}/{类名}"
 *
 * Routing path constant table
 * Please add according to the specification：
 * 1. Place according by module
 * 2. Constant format：PATH_{ModuleName}_{TypeName}_{BusinessName}
 * 3. ARoute path format："/{ModuleName}/{ClassName}"
 */

/**--------------- APP -----------------**/
const val PATH_APP_TEKI_PLAYER_DEMO = "/app/TekiPLayerDemoActivity"
/**--------------- APP -----------------**/


/**-------------- LOGIN ----------------**/
const val PATH_LOGIN_ACTIVITY = "/login/LoginActivity"
const val PATH_LOGIN_UPDATE_ACCOUNT_ACTIVITY = "/login/UpdateAccountActivity"
const val PATH_LOGIN_SERVICE = "/login/service"
/**-------------- LOGIN ----------------**/


/**--------------- IM5 -----------------**/
const val PATH_IM_SERVICE = "/im/service"
/**--------------- IM5 -----------------**/

/**--------------- biz-center/voicemoji -----------------**/
const val PATH_VE_SERVICE = "/voicemoji/service"
/**--------------- IM5 -----------------**/


/**--------------- CHAT ----------------**/

const val PATH_CHAT_SERVICE = "/chat/service"
const val PATH_CHAT_ACTIVITY_HOME = "/chat/ChatHomeActivity"
const val PATH_CHAT_FRAGMENT_HOME = "/chat/ChatHomeFragment"
const val PATH_CHAT_FRAGMENT_HOME_NEW = "/home/<USER>"
const val PATH_CHAT_ACTIVITY_PRIVATE = "/chat/PrivateChatActivity"
const val PATH_CHAT_ACTIVITY_GROUP = "/chat/GroupChatActivity"
const val PATH_CHAT_ACTIVITY_EDIT_GROUP_INFO = "/chat/GroupEditInfoActivity"
const val PATH_CHAT_ACTIVITY_SELECT_GROUP_MEMBER = "/chat/GroupSelectMemberActivity"
const val PATH_CHAT_GROUP_INFO = "/chat/GroupInfoDialog"
const val PATH_CHAT_ACTIVITY_TAKE_PHOTO_SEND = "/chat/TakePhotoSendActivity"
const val PATH_CHAT_ACTIVITY_E2EE_INTRODUCTION = "/chat/E2EEIntroductionActivity"
const val PATH_VOICEEMOJI_SERVICE = "/chat/voiceEmojiPlayerService"
const val PATH_SEND_LOCATION_ACTIVITY = "/chat/SendLocationActivity"
const val PATH_LOCATION_DETAIL_ACTIVITY = "/chat/LocationDetailActivity"
const val PATH_EDIT_GROUP_INFO_DLG = "/chat/GroupEditInfoFragment"
const val PATH_ADD_USER_TO_GROUP_DLG = "/chat/GroupSelectMemberFragment"

/**--------------- CHAT ----------------**/


/**-------------- Contacts -------------**/
const val PATH_CONTACTS_SERVICE = "/contacts/service"
const val PATH_CONTACTS_FRAGMENT_HOME = "/contacts/ContactsHomeFragment"
const val PATH_CONTACTS_FRAGMENT_PROFILE = "/contacts/ProfileDialogFragment"
const val PATH_CONTACTS_ACTIVITY_ADD_FRIENDS = "/contacts/ContactsAddFriendsActivity"
const val PATH_CONTACTS_ACTIVITY_RECOMMEND = "/contacts/ContactsRecommendActivity"
const val PATH_CONTACTS_ACTIVITY_REQUESTS = "/contacts/ContactsRequestsActivity"
const val PATH_CONTACTS_ACTIVITY_CONV_SEARCH = "/contacts/ContactsConvSearchActivity"
const val PATH_CONTACTS_FRAGMENT_TRANSLATION_LANGUAGE_SETTING = "/contacts/BotTranslationLangSettingFragment"
const val PATH_CONTACTS_FRIENDS_GUIDE_DIALOG = "/contacts/AddFriendsGuideDialog"


/**-------------- Contacts -------------**/

/**--------------- Push ----------------**/
const val PATH_PUSH_SERVICE = "/push/service"
/**--------------- Push ----------------**/

/**-------------- Startup --------------**/
const val PATH_STARTUP_ACTIVITY_ENTRY = "/startup/EntryPointActivity"
const val PATH_STARTUP_SERVICE = "/startup/service"
/**-------------- Startup --------------**/


/**--------------- User ----------------**/
const val PATH_USER_SERVICE = "/user/service"
const val PATH_USER_FRAGMENT_PROFILE = "/user/UserProfileFragment"
const val PATH_USER_ACTIVITY_ABOUT = "/user/AboutActivity"
const val PATH_AI_MARKET_ACTIVITY = "/user/AiMarketActivity"
const val PATH_USER_ACTIVITY_BLOCKLIST = "/user/BlockListActivity"
const val PATH_USER_ACTIVITY_ACCOUNT = "/user/AccountActivity"
const val PATH_USER_ACTIVITY_DELETE_ACCOUNT = "/user/DeleteAccountActivity"
const val PATH_USER_ACTIVITY_UPDATE_PROFILE = "/user/UpdateProfileActivity"
const val PATH_USER_ACTIVITY_LANGUAGE_SETTING = "/user/LanguageSettingActivity"
const val PATH_USER_ACTIVITY_EDIT_INFO = "/user/EditUserInfoActivity"
const val PATH_USER_ACTIVITY_SETTING = "/user/SettingActivity"
const val PATH_USER_ACTIVITY_NOTIFICATION_SETTING = "/user/NotificationSettingActivity"
const val PATH_USER_ACTIVITY_MEDIA_DOWNLOAD_SETTING = "/user/MediaDownloadSettingActivity"
const val PATH_USER_ACTIVITY_STORAGE_SETTING = "/user/StorageSettingActivity"
const val PATH_USER_ACTIVITY_ALERT_SOUND_SETTING = "/user/AlertSoundSettingActivity"
const val PATH_USER_ACTIVITY_NOTIFICATION = "/user/NotificationActivity"


/**--------------- User ----------------**/

/**------------- Feedback --------------**/
const val PATH_FEEDBACK_SERVICE = "/feedback/service"
const val PATH_FEEDBACK_SUGGESTION = "/feedback/suggestion"
const val PATH_FEEDBACK_LIVEPLACE = "/feedback/liveplace"
/**------------- Feedback --------------**/

/**-------------- Common ---------------**/
const val PATH_COMMON_ACTIVITY_WEB_VIEW = "/common/WebViewActivity"
const val PATH_COMMON_DIALOG_NEW_FEATURE = "/common/NewFeatureDialog"
const val PATH_COMMON_ACTIVITY_QR_CODE = "/common/QRCodeActivity"
const val PATH_COMMON_ACTIVITY_SHARE_QRCODE = "/common/ShareQRCodeActivity"
const val PATH_COMMON_ALERT_DIALOG = "/common/WrapperCommonDialog"
const val PATH_COMMON_ROUTER_FRAGMENT_HOST_ACTIVITY = "/common/RouterFragmentHostActivity"
const val PATH_COMMON_DIALOG_PROMPT = "/common/RouterPromptDialogFragment"
/**-------------- Common ---------------**/

/**-------------- Float ---------------**/
const val PATH_FLOAT_GUIDE_SERVICE = "/float/service"
const val PATH_FLOAT_OVERLAY_SETTING = "/float/overlay/setting"
/**-------------- Float ---------------**/

/**-------------- campaign ---------------**/
const val PATH_ACTIVITY_CAMPAIGN = "/campaign/CampaignActivity"
/**-------------- campaign ---------------**/

/**-------------- RealTimeCall ---------------**/
const val PATH_ACTIVITY_REAL_TIME_CALL = "/voicecall/PrivateVoiceCallActivity"
const val PATH_REAL_TIME_SERVICE = "/voicecall/service"
const val PATH_GROUPCALL_DIALOG_FRAGMENT = "/voicecall/GroupCallDialog"
const val PATH_GROUPCALL_EXIST_DIALOG = "/voicecall/GroupCallExitDialog"
const val PATH_ACTIVITY_GROUP_VOICECALL = "/voicecall/GroupVoiceCallActivity"
const val PATH_FRAGMENT_APPRAISE = "/voicecall/AppraiseFragment"

/**-------------- voicecall ---------------**/

/**-------------- album ---------------**/
const val PATH_ALBUM_LIST = "/album/AlbumActivity"
const val PATH_ALBUM_SERVICE = "/album/service"
const val PATH_ALBUM_PREVIEW_LIST = "/album/MediaPreviewActivity"
/**-------------- album ---------------**/

/**-------------- media ---------------**/
const val PATH_MEDIA_SERVICE = "/media/service"
/**-------------- media ---------------**/
/**-------------- shared ---------------**/
const val PATH_SHARED_SERVICE = "/shared/service"
/**-------------- shared ---------------**/
/**-------------- receive shared ---------------**/
const val PATH_RECEIVE_SHARED = "/receive_shared/service"
/**-------------- receive shared ---------------**/

/**-------------- storage ---------------**/
const val PATH_STORAGE_SERVICE = "/storage/service"
/**-------------- storage ---------------**/


/**-------------- on air ---------------**/
const val PATH_ONAIR_GLOBAL_SERVICE = "/on_air/global_service"
const val PATH_ACTIVITY_LIVE_PLACE = "/on_air/LivePlaceActivity"
//const val PATH_ON_AIR_PREVIEW_DIALOG = "/onair/OnAirPreviewDialog"
const val PATH_ON_AIR_EXCEPTION_DIALOG = "/onair/OnAirExceptionDlg"
const val PATH_LIVE_PLACE_ROUTE_DIALOG = "/onair/live_place_route_dialog"
const val PATH_LIVE_PLACE_CREATE_ROUTE_DIALOG = "/onair/live_place_create_route_dialog"
const val PATH_LIVE_PLACE_SERVICE = "/onair/service"
const val PATH_ACTIVITY_ONAIR_Test = "/on_air/OnAirTestActivity"
const val PATH_LIVE_PLACE_VISIT = "/on_air/LivePlaceVisitActivity"
/**-------------- on air ---------------**/

/**-------------- Home ---------------**/
const val PATH_HOME_SERVICE = "/home/<USER>"
