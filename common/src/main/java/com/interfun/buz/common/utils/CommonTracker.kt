package com.interfun.buz.common.utils

import android.os.Build
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.bean.voicecall.CallType
import com.interfun.buz.common.constants.AddFriendSource
import com.interfun.buz.common.constants.CommonMMKV
import com.interfun.buz.common.constants.TrackConstant
import com.interfun.buz.common.constants.channelId
import com.interfun.buz.common.manager.cache.user.UserSettingManager
import com.interfun.buz.common.manager.launchAsync
import com.interfun.buz.onair.standard.IGlobalOnAirController
import com.interfun.buz.onair.standard.LivePlaceType
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import com.yibasan.lizhifm.sdk.platformtools.Util
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import java.util.concurrent.TimeUnit

object CommonTracker {

    fun onMainProcessLaunch(){
        BuzTracker.onEvent("CLIENT_MAIN_PROCESS_LAUNCH"){}
    }

    fun onLocalLogin(){
        BuzTracker.onEvent("CLIENT_LOCAL_LOGIN"){}
    }

    /** 自定义事件-活跃统计 */
    fun onEventActiveUsers() {
        GlobalScope.launchAsync(Dispatchers.IO) {
            BuzTracker.onEvent("EVENT_ACTIVE_USERS", true) {
                put("androidId", Util.getAndroidId(appContext))
                put("channelId", channelId)
                put("attribute_ua", "")
            }
            logAFEvent(true,"Launch_app"){}
        }
    }

    /** 自定义事件-安装后首次激活 */
    fun onEventInstallResult() {
        BuzTracker.onEvent("InstallResult", true) {

        }
    }

    fun onPageViewScreen(
        exclusive_id: String,
        title: String,
        pageType: String,
        isReportImmediately: Boolean = false,
        builder: (MutableMap<String, Any>.() -> Unit)? = null
    ) {
        BuzTracker.onPageViewScreen(isReportImmediately) {
            put(TrackConstant.KEY_EXCLUSIVE_ID, exclusive_id)
            put(TrackConstant.KEY_TITLE, title)
            put(TrackConstant.KEY_PAGE_TYPE, pageType)
            builder?.invoke(this)
        }
    }

    fun onDialogViewScreen(exclusive_id: String, title: String, pageType: String, content_name: String){
        BuzTracker.onDialogViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID, exclusive_id)
            put(TrackConstant.KEY_TITLE, title)
            put(TrackConstant.KEY_PAGE_TYPE, pageType)
            put(TrackConstant.KEY_CONTENT_NAME, content_name)
        }
    }

    fun postClickEvent(exclusive_id: String,
                       title: String,
                       elementContent: String,
                       pageType: String,
                       page_business_type: String? = null,
                       page_business_id: String? = null,
                       element_business_content: String? = null,
                       element_business_type: String? = null,
                       element_business_id: String? = null,
                       business_num: Int? = null,
                       content_name: String? = null,
                       source: String? = null,
                       logTime: String? = null){
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, exclusive_id)
            put(TrackConstant.KEY_TITLE, title)
            put(TrackConstant.KEY_ELEMENT_CONTENT, elementContent)
            put(TrackConstant.KEY_PAGE_TYPE, pageType)
            if (page_business_type != null){
                put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, page_business_type)
            }
            if (page_business_id != null){
                put(TrackConstant.KEY_PAGE_BUSINESS_ID, page_business_id)
            }
            if (element_business_content != null){
                put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT, element_business_content)
            }
            if (element_business_type != null){
                put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, element_business_type)
            }
            if (element_business_id != null){
                put(TrackConstant.KEY_ELEMENT_BUSINESS_ID, element_business_id)
            }
            if (business_num != null){
                put(TrackConstant.KEY_BUSINESS_NUM, business_num)
            }
            if (content_name != null) {
                put(TrackConstant.KEY_CONTENT_NAME, content_name)
            }
            if (source != null){
                put(TrackConstant.KEY_SOURCE,source)
            }
            if (logTime != null){
                put(TrackConstant.KEY_LOG_TIME, logTime)
            }
        }
    }

    fun trackShareToInvite(source:Int){
        postClickEvent(
            "AC2022123003",
            "邀请链接",
            "邀请好友",
            "chat",
            source = source.toString()
        )
        logAFAndFirebaseInviteEvent()
    }


    fun postContactPermissionCheckResult(content_name: String){
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2022102801")
            put(TrackConstant.KEY_RESULT_TYPE, "authorize_contact_permission")
            put(TrackConstant.KEY_PAGE_TYPE, "home_setting")
            put(TrackConstant.KEY_CONTENT_NAME, content_name)
        }
    }

    fun onReceiveFriendOnlinePush(isOnline: Boolean){
        val pageBusinessType = if (isOnline){
            if (UserSettingManager.isQuietModeEnable) "quiet" else "available"
        }else{
            "offline"
        }
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2023072003")
            put(TrackConstant.KEY_RESULT_TYPE, "friend_online")
            put(TrackConstant.KEY_PAGE_TYPE, "reminder")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, pageBusinessType)
            put(TrackConstant.KEY_IS_SUCCESS,"success")
            put(TrackConstant.KEY_FAIL_REASON, "")

        }
    }

    fun postAudioPermissionCheckResult(content_name: String, isUserBehaviour: Boolean = false){
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2022102802")
            put(TrackConstant.KEY_RESULT_TYPE, "authorize_audio_permission")
            put(TrackConstant.KEY_PAGE_TYPE, "home_setting")
            put(TrackConstant.KEY_CONTENT_NAME, content_name)
            put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, if (isUserBehaviour) "user_behaviour" else "start_up")
        }
    }

    fun trackQRCodeViewScreen(exclusive_id:String, title:String, page_business_id: String,source:String? = null){
        BuzTracker.onPageViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID, exclusive_id)
            put(TrackConstant.KEY_TITLE, title)
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, page_business_id)
            if (source?.isNotEmpty() == true){
                put(TrackConstant.KEY_SOURCE, source)
            }
        }
    }

    //每次启动时，完成overlay权限检查时上报
    fun postOverlayPermissionResult(isPermissionGranted: Boolean) {
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2023021401")
            put(TrackConstant.KEY_RESULT_TYPE, "authorize_overlay_permission")
            put(TrackConstant.KEY_PAGE_TYPE, "home_setting")
            put(TrackConstant.KEY_CONTENT_NAME, if (isPermissionGranted) "enable" else "disable")
        }
    }


    private fun logAFAndFirebaseInviteEvent(){
        val afEvent = "af_invite"
        val firebaseEvent = "fb_invite"
        logAFAndFireBaseEvent(afEvent,firebaseEvent,true)
    }


    fun onQrcodeScanExpose(source: Int) {
        BuzTracker.onPageViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AV2023051702")
            put(TrackConstant.KEY_TITLE, "扫码")
            put(TrackConstant.KEY_SOURCE, "$source")
        }
    }

    fun onClickScan(exclusiveId: String, title: String) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, exclusiveId)
            put(TrackConstant.KEY_TITLE, title)
            put(TrackConstant.KEY_ELEMENT_CONTENT, "扫码")
            put(TrackConstant.KEY_PAGE_TYPE, "QR")
        }
    }

    fun onClickScanAlbum() {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2023051711")
            put(TrackConstant.KEY_TITLE, "扫码")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "上传图片识别")
            put(TrackConstant.KEY_PAGE_TYPE, "QR")
        }
    }

    fun onClickMyQrCode() {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2023051712")
            put(TrackConstant.KEY_TITLE, "扫码")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "我的二维码")
            put(TrackConstant.KEY_PAGE_TYPE, "QR")
        }
    }

    fun onClickOnAirEntry(livePlaceType: LivePlaceType, targetId: Long, source: String) {
        val isUserInOnAir = routerServices<IGlobalOnAirController>().value?.isInOnAir() == true
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024082001")
            put(TrackConstant.KEY_TITLE, "on_air")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "on_air_entrance")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE,if (livePlaceType == LivePlaceType.GROUP) "group" else "private")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID,targetId)
            put(TrackConstant.KEY_PAGE_STATUS,if (isUserInOnAir) "currently_opening_onair" else "currently_not_opening_onair")
            put(TrackConstant.KEY_SOURCE,source)
        }
    }

    fun onClickAnswerOnAirInvite(isPrivate:Boolean,channelId:String,isAccept:Boolean){
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024082012")
            put(TrackConstant.KEY_TITLE, "on_air_call")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "on_air_call_answer")
            put(TrackConstant.KEY_PAGE_TYPE,"on_air_call")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE,if (isPrivate) "private" else "group")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID,channelId)
            put(TrackConstant.KEY_SOURCE,if (isAppInForeground) "front" else "back")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT,if (isAccept) "accept" else "reject")
        }
    }

    fun onOnAirInviteExpose(isPrivate:Boolean,targetId:String,channelId:String){
        BuzTracker.onAppViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AVS2024082002")
            put(TrackConstant.KEY_TITLE, "on_air_calling_page")
            put(TrackConstant.KEY_PAGE_TYPE,"on_air_call")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE,if (isPrivate) "private" else "group")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID,targetId)
            put(TrackConstant.KEY_PAGE_CONTENT,channelId)
            put(TrackConstant.KEY_SOURCE,if (isAppInForeground) "front" else "back")
        }
    }


    /**
     * 通知用户上线通知点击
     */
    fun onNotifyUserOnLineNotificationClick(isOnline:Boolean){
        log("CommonTracker","onNotifyUserOnLineNotificationClick: $isOnline")
        val pageBusinessType = if (UserSettingManager.isQuietModeEnable) "quiet" else "available"
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2023072002")
            put(TrackConstant.KEY_TITLE, "公共推送")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "好友上线")
            put(TrackConstant.KEY_PAGE_TYPE, "push")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, pageBusinessType)
            put(TrackConstant.KEY_PAGE_STATUS,if (isOnline) "0" else "1")
        }
    }



    fun onScanResult(pageStatus: Int? = null, failReason: Int? = null) {
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2023051701")
            put(TrackConstant.KEY_RESULT_TYPE, "scan_qr")
            put(TrackConstant.KEY_PAGE_TYPE, "QR")
            pageStatus?.let {
                put(TrackConstant.KEY_PAGE_STATUS, "$pageStatus")
                put(TrackConstant.KEY_IS_SUCCESS, "success")
            } ?: let {
                failReason?.let {
                    put(TrackConstant.KEY_IS_SUCCESS, "fail")
                    put(TrackConstant.KEY_FAIL_REASON, "$failReason")
                }
            }
        }
    }

    /**
     * 在聊天页点击【+】后，再点【take photo】按钮时上报
     * @param source:"来源枚举值:0:添加机器人引导弹窗 1:添加好友页面推荐 2:通讯录"
     */
    fun onClickAddAiRootResult(source: Int = AddFriendSource.AddFriendPage.value, success: Boolean, errorCode: Int = 0) {
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2023070601")
            put(TrackConstant.KEY_RESULT_TYPE, "add_buz")
            put(TrackConstant.KEY_PAGE_TYPE, "add")
            put(TrackConstant.KEY_IS_SUCCESS, if (success) "success" else "fail")
            put(TrackConstant.KEY_FAIL_REASON, if (success) "" else "$errorCode")
            put(TrackConstant.KEY_SOURCE, "$source")
        }
    }

    fun reportRegisterRetain() {
        if (CommonMMKV.isUserRegister.not()) return
        if (CommonMMKV.isUserRegister && CommonMMKV.userRegisterTime == 0L) {
            CommonMMKV.userRegisterTime = System.currentTimeMillis()
        }
        val gapTime = System.currentTimeMillis() - CommonMMKV.userRegisterTime
        val gapDay = TimeUnit.MILLISECONDS.toDays(gapTime)
        var has1DayRetainTracked = (CommonMMKV.hasUserRetainTracked and 1) != 0
        var has3DayRetainTracked = (CommonMMKV.hasUserRetainTracked and 2) != 0
        var has7DayRetainTracked = (CommonMMKV.hasUserRetainTracked and 4) != 0

        val content = if (gapDay == 1L && has1DayRetainTracked.not()) {
            logAFEvent(true,"Day 1 Retention"){}
            has1DayRetainTracked = true
            "af_01_retain"
        } else if (gapDay == 3L && has3DayRetainTracked.not()) {
            has3DayRetainTracked = true
            "af_03_retain"
        } else if (gapDay == 7L && has7DayRetainTracked.not()) {
            has7DayRetainTracked = true
            "af_07_retain"
        } else {
            return
        }
        CommonMMKV.hasUserRetainTracked = (if (has1DayRetainTracked) 1 else 0) or
            (if (has3DayRetainTracked) 2 else 0) or
            (if (has7DayRetainTracked) 4 else 0)
        logAFAndFireBaseEvent(content, content, true)
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2023080701")
            put(TrackConstant.KEY_RESULT_TYPE, "af_retain")
            put(TrackConstant.KEY_CONTENT_NAME, content)
        }
    }
    fun onResultRB2023092101(
        source: String,
        isSuccess: Boolean,
        failReason: String,
        botUserId:Long?
    ) {
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2023092101")
            put(TrackConstant.KEY_RESULT_TYPE, "add_AI_to_home")
            put(TrackConstant.KEY_PAGE_TYPE, "home")
            put(TrackConstant.KEY_SOURCE, "$source")
            put(TrackConstant.KEY_IS_SUCCESS, if (isSuccess) "success" else "fail")
            put(TrackConstant.KEY_FAIL_REASON, failReason)
            if (null != botUserId) {
                put(TrackConstant.KEY_PAGE_BUSINESS_ID, "$botUserId")
            }
        }
    }

    fun onReportAFLinkType(afType: Int, linkHash: String?) {
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2023120706")
            put(TrackConstant.KEY_RESULT_TYPE, "register_from_share_link")
            put(TrackConstant.KEY_PAGE_TYPE, "register")
            put(TrackConstant.KEY_BUSINESS_TYPE, afType.toString())
            put(TrackConstant.KEY_IS_SUCCESS, "success")
            linkHash?.let {
                put(TrackConstant.KEY_BUSINESS_ID, it)
            }
        }
    }

    fun postRunInBackgroundPermissionResult(isEnable: Boolean) {
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2024010801")
            put(TrackConstant.KEY_RESULT_TYPE, "authorize_run_background_permission")
            put(TrackConstant.KEY_PAGE_TYPE, "home_setting")
            put(TrackConstant.KEY_CONTENT_NAME, if (isEnable) "1" else "0")
        }
    }

    fun onReportNotificationSettingResult(from:String) {
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2024010802")
            put(TrackConstant.KEY_RESULT_TYPE, "authorize_push_sound&vibration_permission")
            put(TrackConstant.KEY_PAGE_TYPE, "home_setting")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE,if (CommonMMKV.settingAPMSoundsOpen) "1" else "0")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID,if (CommonMMKV.vibration) "1" else "0")
            put(TrackConstant.KEY_CONTENT_ID, if (CommonMMKV.settingONSoundsOpen) "1" else "0")
            put(TrackConstant.KEY_CONTENT_NAME, if (CommonMMKV.vibration) "1" else "0")
            put(TrackConstant.KEY_SOURCE, from)
        }
    }



    /** APP元素点击-聊天页-语音呼叫按钮 */
    fun onClickStartVoiceCallInChatList(targetId: Long, source: String) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024031301")
            put(TrackConstant.KEY_TITLE, "聊天页")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "语音呼叫按钮")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID,targetId)
            put(TrackConstant.KEY_SOURCE,source)
        }
    }

    /** APP元素点击-资料页-语音呼叫按钮 */
    fun onClickStartVoiceCallInProfile(targetId: Long, source: String) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024031302")
            put(TrackConstant.KEY_TITLE, "资料页")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "语音呼叫按钮")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID,targetId)
            put(TrackConstant.KEY_SOURCE,source)
        }
    }

    /** APP元素点击-首页工具箱-语音呼叫按钮 */
    fun onClickStartVoiceCallInHomeKit(targetId: Long, source: String) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024123001")
            put(TrackConstant.KEY_TITLE, "homepage_toolbox")
            put(TrackConstant.KEY_PAGE_TYPE, "home")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "语音呼叫按钮")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID,targetId)
            put(TrackConstant.KEY_SOURCE,source)
        }
    }

    fun onStartPrivateVoiceCallResult(
        targetId: Long,
        channelId: Long,
        callType: @CallType Int,
        waitTime: Long,
        isSuccess: Boolean,
        failReason: String?
    ) {
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2024031301")
            put(TrackConstant.KEY_RESULT_TYPE, "initate_private_voice_call")
            put(TrackConstant.KEY_PAGE_TYPE, "voice_call")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID,targetId)
            put(TrackConstant.KEY_CONTENT_ID,channelId.toString())
            put(TrackConstant.KEY_BUSINESS_NUM,waitTime)
            put(TrackConstant.KEY_IS_SUCCESS,if (isSuccess) "success" else "fail")
            put(TrackConstant.KEY_SOURCE, if (callType == CallType.TYPE_VOICE) "voice_call" else "video_call")
            if (failReason != null) {
                put(TrackConstant.KEY_FAIL_REASON,failReason)
            }
        }
    }

    fun onStartGroupVoiceCallResult(
        targetId: Long,
        memberCount: Int,
        callType: @CallType Int,
        channelId: Long,
        waitTime: Long,
        isSuccess: Boolean,
        failReason: String?
    ) {
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2024031302")
            put(TrackConstant.KEY_RESULT_TYPE, "initate_group_voice_call")
            put(TrackConstant.KEY_PAGE_TYPE, "voice_call")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID,targetId)
            put(TrackConstant.KEY_MENU,memberCount.toString())
            put(TrackConstant.KEY_CONTENT_ID,channelId.toString())
            put(TrackConstant.KEY_BUSINESS_NUM,waitTime)
            put(TrackConstant.KEY_IS_SUCCESS,if (isSuccess) "success" else "fail")
            put(TrackConstant.KEY_SOURCE, if (callType == CallType.TYPE_VOICE) "voice_call" else "video_call")
            if (failReason != null) {
                put(TrackConstant.KEY_FAIL_REASON,failReason)
            }
        }
    }

    fun onVoiceCallJoinChannel(channelId:Long,channelType:Int,time:Long){
        BuzTracker.onTechTrack {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "TT2024040902")
            put(TrackConstant.KEY_EVENT_NAME, "JoinChannel")
            put(TrackConstant.KEY_CONTENT_1, channelId.toString())
            put(TrackConstant.KEY_CONTENT_2, channelType.toString())
            put(TrackConstant.KEY_CONTENT_3, time.toString())
        }
    }

    fun onVoiceCallJoinSuccess(channelId:Long,channelType:Int,time:Long,cost:Long){
        BuzTracker.onTechTrack {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "TT2024040903")
            put(TrackConstant.KEY_EVENT_NAME, "JoinChannelResult")
            put(TrackConstant.KEY_CONTENT_1, channelId.toString())
            put(TrackConstant.KEY_CONTENT_2, channelType.toString())
            put(TrackConstant.KEY_CONTENT_3, time.toString())
            put(TrackConstant.KEY_CONTENT_4, cost.toString())
            put(TrackConstant.KEY_CONTENT_5, "1")
        }
    }

    fun onSaveVideoException(error: Throwable){
        GlobalScope.launchIO {
            BuzTracker.onTechTrack {
                put(TrackConstant.KEY_EXCLUSIVE_ID, "TT2024053002")
                put(TrackConstant.KEY_EVENT_NAME, "SaveVideoToGallery")
                put(TrackConstant.KEY_CONTENT_1, Build.VERSION.SDK_INT)
                put(TrackConstant.KEY_CONTENT_2, error.stackTraceToString())
            }
        }
    }

    /**
     * 媒体下载结果
     */
    fun onResultMediaDownload(
        isVideo: Boolean,
        videoDuration: Long = 0L,
        isAutoDownload: Boolean,
        downloadDuration: Long,
        traceID: String?,
        mediaDownloadSize: Long,
        isSuccess: Boolean,
        iM5ConversationType: IM5ConversationType?,
        failReason: String? = null
    ) {
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2024032507")
            put(TrackConstant.KEY_RESULT_TYPE, "media_download_result")
            put(TrackConstant.KEY_PAGE_TYPE, "聊天页")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, if (isVideo) "video" else "image")
            if (videoDuration != 0L) { put(TrackConstant.KEY_MENU, videoDuration.toString()) }
            put(TrackConstant.KEY_BUSINESS_TYPE, if (isAutoDownload) "auto" else "manual")
            put(TrackConstant.KEY_CONTENT_ID, downloadDuration.toString())
            put(TrackConstant.KEY_CONTENT_NAME, traceID?:"")
            put(TrackConstant.KEY_BUSINESS_NUM, mediaDownloadSize)
            put(TrackConstant.KEY_IS_SUCCESS, if (isSuccess) "success" else "fail")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, if (iM5ConversationType == IM5ConversationType.GROUP) "group" else "private")
            failReason?.let { put(TrackConstant.KEY_FAIL_REASON, it) }
        }
    }

    /**
     * In buz official page, when user turn on or off the message/notification settings
     */
    fun onClickMuteOfficialAccount(muteType: String, muteStatus: String) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024072602")
            put(TrackConstant.KEY_TITLE, "buz_account_page")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "mute")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
            put(TrackConstant.KEY_PAGE_STATUS, "official_account")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT, muteType)
            put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, muteStatus)
        }
    }

    /**
     * gms,自动填充优化打点
     */
    fun onResultRB2024091105(phoneNumberBefore: String, phoneNumberNew: String) {
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2024091105")
            put(TrackConstant.KEY_RESULT_TYPE, "GMS_autocorrect_result")
            put(TrackConstant.KEY_PAGE_TYPE, "log_register")
            put(TrackConstant.KEY_BUSINESS_ID, phoneNumberBefore)
            put(TrackConstant.KEY_PHONE_NUMBER, phoneNumberNew)
        }
    }

    /**
     * when user click the go to settings button in the notification problems intro page
     */
    fun onClickNotificationProblemIntroButton() {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024100101")
            put(TrackConstant.KEY_TITLE, "notification_problems_intro")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "notification_problems_intro_go")
            put(TrackConstant.KEY_PAGE_TYPE, "home")
        }
    }

    /**
     * when user has change their run in background permission in the notification settings
     */
    fun postBatteryOptimizationPermissionCheckResult() {
        val batteryOptimizeAllowSetting = NotificationUtil.isIgnoringBatteryOptimizations()
        postStartUpPermissionCheckResult(false)
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2024100101")
            put(TrackConstant.KEY_RESULT_TYPE, "authorize_run_background_permission_noti_setting")
            put(TrackConstant.KEY_PAGE_TYPE, "home_setting")
            put(TrackConstant.KEY_CONTENT_NAME, if (batteryOptimizeAllowSetting) "enable" else "disable")
        }
    }

    /**
     * when user has change their push notification permission in the notification settings
     */
    fun postNotificationPermissionCheckResult() {
        val notificationAllowSetting = NotificationUtil.isNotifyOpen()
        postStartUpPermissionCheckResult(false)
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2024100102")
            put(TrackConstant.KEY_RESULT_TYPE, "authorize_push_notification_permission_noti_setting")
            put(TrackConstant.KEY_PAGE_TYPE, "home_setting")
            put(TrackConstant.KEY_CONTENT_NAME, if (notificationAllowSetting) "enable" else "disable")
        }
    }

    /**
     * everytime when the user cold-start their phone
     */
    fun postStartUpPermissionCheckResult(fromHomePage: Boolean = true) {
        val batteryOptimizeAllowSetting = NotificationUtil.isIgnoringBatteryOptimizations()
        val notificationAllowSetting = NotificationUtil.isNotifyOpen()
        val keySource = if (CommonMMKV.notificationProblemTrackerStartUp) {
            CommonMMKV.notificationProblemTrackerStartUp = false
            "start_up"
        } else if (fromHomePage) {
            val isBatteryPermissionNoChange = CommonMMKV.batteryOptimizeAllowSetting == batteryOptimizeAllowSetting
            val isNotificationPermissionNoChange = CommonMMKV.notificationAllowSetting == notificationAllowSetting
            if (isBatteryPermissionNoChange && isNotificationPermissionNoChange) return
            "change_outside_from_the_app"
        } else {
            "change_in_noti_settings"
        }
        CommonMMKV.batteryOptimizeAllowSetting = batteryOptimizeAllowSetting
        CommonMMKV.notificationAllowSetting = notificationAllowSetting
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2024100103")
            put(TrackConstant.KEY_RESULT_TYPE, "authorize_startup_run_bg_push_permission")
            put(TrackConstant.KEY_PAGE_TYPE, "home")
            put(TrackConstant.KEY_SOURCE, keySource)
            put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, "run_bg:${if (batteryOptimizeAllowSetting) "Y" else "N"},push_noti:${if (notificationAllowSetting) "Y" else "N"}")
        }
    }

    fun onClickHomeSearch(source: String, type: String? = null) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024101806")
            put(TrackConstant.KEY_TITLE, "home_page")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "homepage_search")
            put(TrackConstant.KEY_PAGE_TYPE, "home")
            put(TrackConstant.KEY_SOURCE, source)
            type?.let { put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT, type) }
        }
    }

    fun onPageViewVS2024102103() {
        BuzTracker.onDialogViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "VS2024102103")
            put(TrackConstant.KEY_TITLE, "official_account_overlay_intro")
            put(TrackConstant.KEY_PAGE_TYPE, "home")

        }
    }

    fun onClickAC2024102102(businessType: String) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024102102")
            put(TrackConstant.KEY_TITLE, "autoplay_guidance_click")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "home")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, businessType)
        }
    }

    fun onClickAC2024102103() {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024102103")
            put(TrackConstant.KEY_TITLE, "official_account_overlay_intro_click")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "home")
        }
    }

    fun onClickAC2024102104(friendNum: Int) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024102104")
            put(TrackConstant.KEY_TITLE, "official_account_restart_guidance")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "chat")
            put(TrackConstant.KEY_BUSINESS_NUM, friendNum)
        }
    }

    fun onResultRB2024091106(key: Int, value: String) {
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2024091106")
            put(TrackConstant.KEY_RESULT_TYPE, "ab_test_grouping_result")
            put(TrackConstant.KEY_PAGE_TYPE, "home_setting")
            put(TrackConstant.KEY_CONTENT_ID, "$key")
            put(TrackConstant.KEY_CONTENT_NAME, value)
        }
    }

    fun onResultRB2024103101(id: Int, isPlanB: Boolean) {
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2024103101")
            put(TrackConstant.KEY_RESULT_TYPE, "ab_test_group_register")
            put(TrackConstant.KEY_PAGE_TYPE, "home_setting")
            put(TrackConstant.KEY_CONTENT_ID, id.toString())
            put(TrackConstant.KEY_CONTENT_NAME, if (isPlanB) "B" else "A")
        }
    }

    fun postABTestTimeCost(costTime:Long) {
        logDebug("ABTestWithoutLoginManager","costTime: $costTime")
        BuzTracker.onTechTrack {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "TT2024121104")
            put(TrackConstant.KEY_EVENT_NAME, "ABTestTimeCost")
            put(TrackConstant.KEY_CONTENT_1, costTime)
        }
    }

    fun trackPageViewScreenAVS2024121302(userId: String, source:String? = null, afLinkHash: String?){
        BuzTracker.onPageViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AVS2024121302")
            put(TrackConstant.KEY_TITLE, "personal_qrcode")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, userId)
            if (source?.isNotEmpty() == true){
                put(TrackConstant.KEY_SOURCE, source)
            }
            afLinkHash?.let {
                put(TrackConstant.KEY_ELEMENT_BUSINESS_ID, afLinkHash)
            }
        }
    }


    fun trackPageViewScreenAVS2024121303(groupId: String, afLinkHash: String?){
        BuzTracker.onPageViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AVS2024121303")
            put(TrackConstant.KEY_TITLE, "group_qrcode")
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, groupId)
            afLinkHash?.let {
                put(TrackConstant.KEY_ELEMENT_BUSINESS_ID, afLinkHash)
            }
        }
    }

    fun onClickACAC2024120603(afLinkHash: String?) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024120603")
            put(TrackConstant.KEY_TITLE, "personal_profile")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "personal_profile_share")
            put(TrackConstant.KEY_PAGE_TYPE, "home")
            afLinkHash?.let {
                put(TrackConstant.KEY_ELEMENT_BUSINESS_ID, afLinkHash)
            }
        }
    }

    fun onClickCopyBuzId(fromQR: Boolean) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2025041705")
            put(TrackConstant.KEY_TITLE, "buzid")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "buzid_copy")
            put(TrackConstant.KEY_PAGE_TYPE, "buzid")
            put(TrackConstant.KEY_SOURCE, if (fromQR) "qrcode" else "personal_profile")
        }
    }
}