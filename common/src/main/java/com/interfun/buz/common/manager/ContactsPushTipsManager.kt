package com.interfun.buz.common.manager

import com.interfun.buz.base.ktx.emitInScopeIfDifferent
import com.interfun.buz.base.ktx.launchIO
import com.interfun.buz.base.ktx.log
import com.interfun.buz.common.constants.CommonMMKV
import com.interfun.buz.common.manager.tips.BasePushTipsManager
import com.interfun.buz.common.manager.tips.PushTipsType
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

/**
 * <AUTHOR>
 * @date 2024/6/14
 * @desc 推送 Tips 管理
 */
object ContactsPushTipsManager : BasePushTipsManager() {

    private const val TAG = "ContactsPushTipsManager"
    val inContactPageFlow = MutableStateFlow(false)
    private val openStatusMap: MutableMap<String, Boolean> = mutableMapOf()

    private val contactsTipsFlow_: MutableStateFlow<ContactTipsUpdateInfo?> = MutableStateFlow(null)
    val contactsTipsFlow: StateFlow<ContactTipsUpdateInfo?> = contactsTipsFlow_

    /**
     *  定义：用户点击 Tips 或者进入过 Tips 对应的跳转页，则认为 Tips 已读；
     *   - 如 Tips 对应跳转加好友页，若用户主动跳转、点 Tips 跳转、点通知栏消息跳转等途径进入了加好友页，则与跳转加好友页相关的 Tips 认为已读；
     *
     *  调用时机：
     *  1. 进入通讯录页或加好友页面
     *  2. 在加好友页面收到了 Tips，则直接标记此 Tips 已读
     *  3. 若因弱网等原因导致调用标记已读协议失败，客户端本地记录，等下次拉取 Tips 前先重试标记已读，再拉取新的 Tips
     */
    fun updateContactPageOpenStatus(tag: String, isOpen: Boolean) {
        if (isOpen) {
            openStatusMap[tag] = true
            userLifecycleScope?.let { scope ->
                CommonMMKV.lastShownContactsTipsId.takeIf { it > 0L }?.let { tipsId ->
                    scope.launchIO {
                        markTipsRead(PushTipsType.ContactRegistered, tipsId)
                    }
                }
                inContactPageFlow.emitInScopeIfDifferent(scope, true)
            }
        } else {
            openStatusMap.remove(tag)
            userLifecycleScope?.let { scope ->
                inContactPageFlow.emitInScopeIfDifferent(scope, openStatusMap.any { it.value })
            }
        }
        log(TAG, "$tag isOpen: $isOpen, inContactPage: ${inContactPageFlow.value}")
    }

    suspend fun handlePush(tipsId: Long, sendTimestamp: Long) {
        val canShowTips = inContactPageFlow.value.not()
        val updateInfo = ContactTipsUpdateInfo(tipsId, sendTimestamp, canShowTips)
        log(TAG, "handlePush updateInfo: $updateInfo current: ${contactsTipsFlow_.value}")
        contactsTipsFlow_.emit(ContactTipsUpdateInfo(tipsId, sendTimestamp, canShowTips))
        if (canShowTips.not()) {
            markTipsRead(PushTipsType.ContactRegistered, tipsId)
        }
    }

    override suspend fun onTipsRead(type: PushTipsType) {
        if (type != PushTipsType.ContactRegistered) return
        CommonMMKV.lastShownContactsTipsId = 0L
        contactsTipsFlow_.emit(null)
    }
}

data class ContactTipsUpdateInfo(
    val tipsId: Long,
    val sendTimestamp: Long,
    val needRequestTips: Boolean
) {
    override fun equals(other: Any?): Boolean {
        (other as? ContactTipsUpdateInfo)?.let {
            return tipsId == it.tipsId && sendTimestamp == it.sendTimestamp
        }
        return false
    }
}



