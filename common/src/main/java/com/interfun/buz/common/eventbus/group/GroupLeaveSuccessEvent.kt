package com.interfun.buz.common.eventbus.group

import com.interfun.buz.common.eventbus.BaseEvent
import com.jeremyliao.liveeventbus.LiveEventBus

/**
 * @Desc the event for Leave Group
 * @Author:huang<PERSON><PERSON><PERSON>@lizhi.fm
 * @Date: 2022/7/27
 */
@Deprecated("请不要使用这个了，可以使用GroupRepository获取GroupFlow监听群状态")
class GroupLeaveSuccessEvent(val groupId: Long): BaseEvent() {

    companion object{
        fun post(groupId: Long){
            LiveEventBus.get(GroupLeaveSuccessEvent::class.java).post(
                GroupLeaveSuccessEvent(groupId)
            )
        }
    }

}