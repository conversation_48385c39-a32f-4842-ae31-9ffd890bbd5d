package com.interfun.buz.signal

import kotlinx.coroutines.flow.Flow
import org.json.JSONObject

enum class TopicSubscribeRet {
    /**
     * 已经订阅
     */
    AVAILABLE,

    /**
     * 无效订阅
     */
    INVALID,

    /**
     * 订阅中
     */
    PROCESSING
}

interface TopiSubscribeListener {
    fun onSignalTopicSubscribeResult(topic: String, status: TopicSubscribeRet)
}

data class SignalData(
    val topic: Int,
    val sendTimeStamp: Long,
    val data: JSONObject?
)

interface CompensateCallBack {
    fun onCompensateCallBack()
}

interface ISignalManagerPresenter {
    /**
     * 订阅 topic
     */
    fun subscribe(topic: String)

    /**
     * 取消订阅topic
     */
    fun unsubscribe(topic: String)

    /**
     * 监听各种数据流
     */
    fun obtainSignalingFlow(filter: ((SignalData) -> Boolean) = { true }): Flow<SignalData>

    /**
     * 注册 topic 订阅结果
     */
    @Deprecated("IM信令后不需要了,雄哥原来都不想给这个回调的")
    fun registerTopiSubscribeRet(listener: TopiSubscribeListener)

    /**
     * 反注册 topic 订阅结果
     */
    @Deprecated("IM信令后不需要了,雄哥原来都不想给这个回调的")
    fun unregisterTopiSubscribeRet(listener: TopiSubscribeListener)

    /**
     * 标识准备好可以进行订阅操作
     */
    fun ready()

    /**
     * 当前没有准备好订阅,所以要清空之前的资源
     */
    fun unready()

    /**
     * 回调的时候提示你丢了信令需要补偿操作
     * 举个例子:
     *      信令没有投递到的时候会给你保留 5 分钟的时间,如果 5 分钟内没有投递成功,那么就会回调这个方法
     */
    fun registerCompensateCallBack(callBack: CompensateCallBack)

    fun unregisterCompensateCallBack(callBack: CompensateCallBack)

}