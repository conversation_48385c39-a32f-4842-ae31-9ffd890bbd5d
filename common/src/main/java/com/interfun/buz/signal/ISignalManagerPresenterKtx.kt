package com.interfun.buz.signal

import com.interfun.buz.common.bean.push.PushOP
import kotlinx.coroutines.flow.mapNotNull

fun ISignalManagerPresenter.getProtocolDataChangeFlow() =
    this.obtainSignalingFlow { data ->
        data.topic == PushOP.PushProtocolDataChange.op
    }.mapNotNull {
        val businessType = it.data?.optInt("businessType")
        if (businessType == null) {
            null
        } else {
            val businessId = it.data.optString("businessId")
            ProtocolDataChangedSignal(businessType, businessId, it.sendTimeStamp, it.data)
        }
    }