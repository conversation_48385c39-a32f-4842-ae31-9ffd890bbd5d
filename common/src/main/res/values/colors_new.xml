<?xml version="1.0" encoding="utf-8"?>
<resources>

    <!-- text -->
    <!-- Highlight -->
    <color name="color_text_highlight_default">@color/brand_100</color>
    <color name="color_text_highlight_disable">@color/brand_30</color>

    <!-- White -->
    <color name="color_text_white_important">@color/neutral_white</color>
    <color name="color_text_white_primary">@color/neutral_88</color>
    <color name="color_text_white_secondary">@color/neutral_70</color>
    <color name="color_text_white_tertiary">@color/neutral_50</color>
    <color name="color_text_white_disable">@color/neutral_30</color>

    <!-- Black -->
    <color name="color_text_black_primary">@color/neutral_black</color>
    <color name="color_text_black_secondary">@color/neutral_20</color>
    <color name="color_text_black_tertiary">@color/neutral_50</color>
    <color name="color_text_black_disable">@color/neutral_70</color>
<!--    //Color/Text/Black On Color/Primary-->
    <color name="color_text_blackOnColor_primary">@color/brand_10</color>

    <!-- Hyperlink -->
    <color name="color_text_hyperlink_primary">@color/blue_100</color>
    <color name="color_text_hyperlink_disable">@color/blue_30</color>

    <!-- Consequential -->
    <color name="color_text_consequential_default">@color/red_100</color>
    <color name="color_text_consequential_disable">@color/red_30</color>

    <!-- Foreground -->
    <!-- Highlight -->
    <color name="color_foreground_highlight_default">@color/brand_100</color>
    <color name="color_foreground_highlight_pressed">@color/brand_80</color>
    <color name="color_foreground_highlight_disable">@color/brand_30</color>

    <!-- On Color -->
    <color name="color_foreground_onColor_1_default">@color/neutral_black</color>
    <color name="color_foreground_onColor_1_pressed">@color/brand_20</color>
    <color name="color_foreground_onColor_1_disable">@color/brand_10</color>
    <color name="color_foreground_onColor_2_default">@color/brand_70</color>

    <!-- On Light -->
    <color name="color_foreground_onLight_default">@color/neutral_black</color>
    <color name="color_foreground_onLight_pressed">@color/neutral_20</color>
    <color name="color_foreground_onLight_disable">@color/neutral_10</color>

    <!-- Neutral -->
    <color name="color_foreground_neutral_important_default">@color/neutral_white</color>
    <color name="color_foreground_neutral_important_pressed">@color/neutral_60</color>
    <color name="color_foreground_neutral_important_disable">@color/neutral_30</color>
    <color name="color_foreground_neutral_primary_default">@color/neutral_90</color>
    <color name="color_foreground_neutral_primary_pressed">@color/neutral_60</color>
    <color name="color_foreground_neutral_primary_disable">@color/neutral_30</color>
    <color name="color_foreground_neutral_grey_default">@color/neutral_30</color>
    <color name="color_foreground_neutral_black_default">@color/neutral_black</color>
    <color name="color_foreground_neutral_black_pressed">@color/neutral_40</color>
    <color name="color_foreground_neutral_black_disable">@color/neutral_70</color>

    <!-- Consequential -->
    <color name="color_foreground_consequential_default">@color/red_100</color>
    <color name="color_foreground_consequential_pressed">@color/red_60</color>
    <color name="color_foreground_consequential_disable">@color/red_30</color>

    <!-- DND -->
    <color name="color_foreground_dnd_default">@color/purple_100</color>

    <!-- Background -->
    <color name="color_background_1_default">@color/neutral_8</color>
    <color name="color_background_2_default">@color/neutral_10</color>
    <color name="color_background_3_default">@color/neutral_12</color>
    <color name="color_background_4_default">@color/neutral_16</color>
    <color name="color_background_5_default">@color/neutral_20</color>
    <color name="color_background_5_pressed">@color/neutral_24</color>
    <color name="color_background_5_disable">@color/neutral_16</color>
    <color name="color_background_6_default">@color/neutral_24</color>
    <color name="color_background_7_default">@color/neutral_30</color>
    <color name="color_background_8_default">@color/neutral_40</color>

    <color name="color_background_highlight_1_default">@color/brand_100</color>
    <color name="color_background_highlight_1_pressed">@color/brand_80</color>
    <color name="color_background_highlight_1_disable">@color/brand_30</color>
    <color name="color_background_highlight_2_default">@color/brand_10</color>

    <color name="color_background_dnd_1_default">@color/purple_100</color>
    <color name="color_background_dnd_1_pressed">@color/purple_80</color>
    <color name="color_background_dnd_1_disable">@color/purple_30</color>
    <color name="color_background_dnd_2_default">@color/purple_20</color>

    <color name="color_background_light_default">@color/neutral_white</color>
    <color name="color_background_light_pressed">@color/neutral_80</color>
    <color name="color_background_light_disable">@color/neutral_30</color>

    <color name="color_background_consequential_default">@color/red_100</color>

    <!-- Outline -->
    <color name="color_outline_1_default">@color/neutral_16</color>
    <color name="color_outline_2_default">@color/neutral_24</color>
    <color name="color_outline_3_default">@color/neutral_30</color>

    <!-- Overlay -->
    <color name="color_overlay_black_heavy">@color/alpha_black_94</color>
    <color name="color_overlay_black_medium">@color/alpha_black_70</color>
    <color name="color_overlay_black_light">@color/alpha_black_40</color>
    <color name="color_overlay_white_light">@color/alpha_white_10</color>

    <color name="color_keys_purple_20">#312E48</color>
    <color name="color_keys_purple_60">#6055A3</color>
    <color name="color_keys_brand_20">#3B4525</color>

    
</resources>