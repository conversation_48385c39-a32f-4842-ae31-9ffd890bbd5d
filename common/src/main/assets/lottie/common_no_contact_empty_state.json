{"v": "5.12.2", "fr": 60, "ip": 0, "op": 120, "w": 400, "h": 400, "nm": "friends9.13", "ddd": 0, "assets": [{"id": "image_0", "w": 400, "h": 400, "u": "", "p": "data:image/png;base64,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", "e": 1}], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Union 2", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 50, "s": [100], "e": [0]}, {"t": 60}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 1, "y": 1}, "t": 0, "s": [211, 195, 0], "e": [211, 195, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 1, "y": 0.049}, "t": 50, "s": [211, 195, 0], "e": [211, 175, 0], "to": [0, -3.333, 0], "ti": [0, 3.333, 0]}, {"t": 60}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [200, 200, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[2.244, 0], [0, -2.434], [0, 0], [-2.244, 0], [0, 2.434], [0, 0]], "o": [[-2.244, 0], [0, 0], [0, 2.434], [2.244, 0], [0, 0], [0, -2.434]], "v": [[0, -10], [-4.063, -5.593], [-4.063, -3.559], [0, 0.847], [4.062, -3.559], [4.062, -5.593]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[1.036, 0], [0, 0], [0.585, -0.145], [0.454, -2.158], [0, -1.124], [0, 0], [-3.64, 0], [0, 0], [0.134, 0.635], [1.99, 0.493]], "o": [[0, 0], [-1.036, 0], [-1.99, 0.493], [-0.134, 0.635], [0, 0], [6.592, 0], [0, 0], [0, -1.124], [-0.454, -2.158], [-0.585, -0.145]], "v": [[1.068, 2.881], [-1.068, 2.881], [-3.37, 3.026], [-7.367, 7.362], [-7.5, 9.858], [-7.5, 10], [7.5, 10], [7.5, 9.858], [7.367, 7.362], [3.37, 3.026]], "c": true}, "ix": 2}, "nm": "路径 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "合并路径 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.792156862745, 0.952941176471, 0.325490196078, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Union", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 120, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Union", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [211, 237, 0], "e": [211, 215, 0], "to": [0, -3.667, 0], "ti": [0, 7, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 50, "s": [211, 215, 0], "e": [211, 195, 0], "to": [0, -7, 0], "ti": [0, 3.333, 0]}, {"t": 60}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [200, 200, 100], "ix": 6, "l": 2}}, "ao": 0, "sy": [{"c": {"a": 0, "k": [1, 1, 1, 0.25], "ix": 2}, "o": {"a": 0, "k": 25, "ix": 3}, "a": {"a": 0, "k": 135, "ix": 5}, "s": {"a": 0, "k": 10, "ix": 8}, "d": {"a": 0, "k": 5.657, "ix": 6}, "ch": {"a": 0, "k": 0, "ix": 7}, "bm": {"a": 0, "k": 1, "ix": 1}, "no": {"a": 0, "k": 0, "ix": 9}, "ty": 2, "nm": "Inner Shadow"}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.759, 0], [0, -0.762], [0, 0], [0.502, -0.403], [0, 0], [0, -1.936], [0, 0], [-0.529, 0.424], [0, 0], [0, 1.902], [0, 0], [3.038, 0], [0, -3.047]], "o": [[0, -0.762], [0.759, 0], [0, 0], [0, 0.645], [0, 0], [-1.507, 1.21], [0, 0], [0, -0.679], [0, 0], [1.481, -1.188], [0, 0], [0, -3.047], [-3.038, 0], [0, 0]], "v": [[-1.375, -4.483], [0, -5.862], [1.375, -4.483], [1.375, -3.739], [0.581, -2.081], [0.322, -1.874], [-2.062, 3.103], [2.062, 3.103], [2.899, 1.358], [3.158, 1.15], [5.5, -3.739], [5.5, -4.483], [0, -10], [-5.5, -4.483]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2.062, 10], [2.062, 5.862], [-2.062, 5.862], [-2.062, 10]], "c": true}, "ix": 2}, "nm": "路径 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "合并路径 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.792156875134, 0.952941179276, 0.32549020648, 1], "ix": 4}, "o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 50, "s": [0], "e": [100]}, {"t": 60}], "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Union", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 120, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 2, "nm": "friends9.13.png", "cl": "13 png", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"s": true, "x": {"a": 0, "k": 200, "ix": 3}, "y": {"a": 0, "k": 200, "ix": 4}}, "a": {"a": 0, "k": [200, 200, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 120, "st": 0, "bm": 0}], "markers": [], "props": {}}