{"version": "6.6.1", "name": "push", "id": "100004", "enableDemo": "false", "subComponent": [{"version": "6.6.1", "name": "hua<PERSON>", "id": "100031", "extra": {"appId": "110599309"}}, {"version": "6.6.1", "name": "fcm", "id": "100034", "extra": {"appId": "fcm"}}], "extra": {"pushAppId": "buz", "demoVersion": "6.6.1"}, "serverEnv": [{"env": "towerEnv", "serverConfig": {"fcmURL": "http://pushcallback.yfxn.lzpsap1.com", "uploadTokenURL": "http://push-token.yfxn.lzpsap1.com", "clickURL": "http://pushcallback.yfxn.lzpsap1.com"}}, {"env": "preEnv", "serverConfig": {"fcmURL": "https://growthpushcbpre.buz-app.com", "uploadTokenURL": "https://pushtokenpre.buz-app.com", "clickURL": "https://growthpushcbpre.buz-app.com"}}, {"env": "productEnv", "serverConfig": {"fcmURL": "https://growthpushcb.buz-app.com", "uploadTokenURL": "https://pushtoken.buz-app.com", "clickURL": "https://growthpushcb.buz-app.com"}}]}