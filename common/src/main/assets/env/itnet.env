{"version": "6.5.8", "name": "itnet", "id": "100001", "enableDemo": false, "extra": {"appid": "87075309"}, "subComponent": [{"name": "dispatchCenter", "serverEnv": [{"env": "productEnv", "serverConfig": {"enable": true, "resources": ["https://compass101.buz-app.com", "https://compass102.buz-app.com"], "defaultReqResps": ["https://httpproxy101.buz-app.com", "https://httpproxy102.buz-app.com"]}}, {"env": "preEnv", "serverConfig": {"resources": [], "defaultReqResps": ["https://httpproxypre.buz-app.com:4005"]}}, {"env": "towerEnv", "serverConfig": {"enable": true, "resources": ["http://************:7004"], "defaultReqResps": ["http://************:7004", "ws://************:7004/lthrift"]}}]}, {"version": "6.5.8", "name": "upload", "id": "100094"}, {"version": "6.5.8", "name": "push", "id": "100095"}, {"version": "6.5.8", "name": "idl", "id": "100096"}, {"version": "6.5.8", "name": "compat", "id": "100097"}, {"name": "diagnosis", "extra": {"snoopHost": "romesnoop.tekiusa.com"}}]}