package com.interfun.buz.assertutil

import android.os.Looper

private val defaultLazyMessage = { "<PERSON><PERSON><PERSON> failed" }

/**
 * 断言是否在[expectThread]线程执行
 */
fun buzAsserThread(expectThread: Thread?, lazyMessage: (() -> Any) = defaultLazyMessage) {
    if (expectThread == null) {
        return
    }

    buzAssert({ Thread.currentThread() == expectThread }, lazyMessage)
}

fun buzAssertMain(lazyMessage: (() -> Any) = defaultLazyMessage) {
    buzAsserThread(Looper.getMainLooper().thread, lazyMessage)
}

/**
 * 断言当前非主线程
 */
fun buzAssertNotMain(lazyMessage: (() -> Any) = defaultLazyMessage) {
    buzAssert({
        Thread.currentThread() != Looper.getMainLooper().thread
    }, lazyMessage)
}

/**
 * [predicate]返回[false]触发断言错误
 */
fun buzAssert(predicate: () -> Boolean, lazyMessage: (() -> Any) = defaultLazyMessage) {
    assert(predicate.invoke(), lazyMessage)
}

