ext {
    android = [
        compileSdkVersion: 35,
        targetSdkVersion: 34,
        minSdkVersion: 21,
        versionCode: 1,
        versionName: "1.66.0",
    ]

    ext.idlVersion = "0.5.47-im3.1-SNAPSHOT"
    ext.doremeVersion = "*******"
    ext.cameraxVersion = "1.3.2"
    ext.vxVersion = "4.3.0"
    ext.tekiPlayerVersion = "0.4.3"
    ext.kotlin_coroutines_version = "1.8.1"
    ext.kotlin_room_version = "2.6.1"
    ext.lifecycle_version = "2.7.0"
    ext.navigation_version = "2.7.7"
    ext.activity_version = "1.10.1"
    ext.compose_version = "1.8.3"
    ext.coil_version = "2.7.0"
    ext.lottie_version = "6.4.1"
    ext.fresco_version = "3.5.0"

    /**
     * 引入新依赖请遵守以下规则：
     * 1. 尽量引入最新或较新的稳定版本
     * 2. 避免多个模块重复依赖
     * 3. 声明和使用都按照分类放置
     * 4. 简写使用小写+下划线
     */
    dependencies = [
        /** JUnit Test **/
        junit: "junit:junit:4.13.2",
        junit_androidx: "androidx.test.ext:junit:1.1.3",
        espresso_core: "androidx.test.espresso:espresso-core:3.4.0",

        /** Kotlin **/
        kotlin_stdlib: "org.jetbrains.kotlin:kotlin-stdlib-jdk8:$kotlin_version",
        kotlinx_coroutines_core: "org.jetbrains.kotlinx:kotlinx-coroutines-core:$kotlin_coroutines_version",
        kotlinx_coroutines_android: "org.jetbrains.kotlinx:kotlinx-coroutines-android:$kotlin_coroutines_version",

        /** AndroidX **/
        multidex: "androidx.multidex:multidex:2.0.1",
        activity: "androidx.activity:activity-ktx:$activity_version",
        annotation: "androidx.annotation:annotation:1.8.1",
        app_compat: "androidx.appcompat:appcompat:1.7.0",
        core_ktx: "androidx.core:core-ktx:1.13.1",
        core_animation: "androidx.core:core-animation:1.0.0",
        constraintlayout: "androidx.constraintlayout:constraintlayout:2.2.0",
        dynamicanimation: "androidx.dynamicanimation:dynamicanimation:1.0.0",
        fragment: "androidx.fragment:fragment-ktx:1.6.2",
        lifecycle_runtime: "androidx.lifecycle:lifecycle-runtime-ktx:$lifecycle_version",
        lifecycle_viewmodel: "androidx.lifecycle:lifecycle-viewmodel-ktx:$lifecycle_version",
        lifecycle_livedata: "androidx.lifecycle:lifecycle-livedata-ktx:$lifecycle_version",
        navigation_runtime: "androidx.navigation:navigation-runtime-ktx:$navigation_version",
        navigation_fragment: "androidx.navigation:navigation-fragment-ktx:$navigation_version",
        navigation_ui: "androidx.navigation:navigation-ui-ktx:$navigation_version",
        recyclerview: "androidx.recyclerview:recyclerview:1.3.2",
        room_runtime: "androidx.room:room-runtime:$kotlin_room_version",
        room_ktx: "androidx.room:room-ktx:$kotlin_room_version",
        room_compiler: "androidx.room:room-compiler:$kotlin_room_version",
        transition: "androidx.transition:transition-ktx:1.4.1",
        viewpager2: "androidx.viewpager2:viewpager2:1.1.0",
        splashScreen: "androidx.core:core-splashscreen:1.0.0-rc01",
        lifecycle_ext: "android.arch.lifecycle:extensions:1.1.1",
        palette: "androidx.palette:palette:1.0.0",

        /** Compose **/
        // https://developer.android.com/develop/ui/compose/setup?hl=zh-cn#bom-version-mapping
        compose_material: "androidx.compose.material3:material3:1.3.0-beta05",
        compose_foundation: "androidx.compose.foundation:foundation:$compose_version",
        compose_foundation_layout: "androidx.compose.foundation:foundation-layout:$compose_version",

        // Android Studio Preview support
        compose_ui_tooling_preview: "androidx.compose.ui:ui-tooling-preview:$compose_version",
        compose_ui_tooling: "androidx.compose.ui:ui-tooling:$compose_version",

        compose_activity: "androidx.activity:activity-compose:$activity_version",
        compose_viewmodel: "androidx.lifecycle:lifecycle-viewmodel-compose:$kotlin_room_version",
        compose_livedata: "androidx.compose.runtime:runtime-livedata:$compose_version",
        compose_navigation: "androidx.navigation:navigation-compose:$navigation_version",
        compose_coil: "io.coil-kt:coil-compose:$coil_version",
        compose_lottie: "com.airbnb.android:lottie-compose:$lottie_version",
        compose_constraintlayout: "androidx.constraintlayout:constraintlayout-compose:1.1.0",

        /** java */
        //bugfix: Duplicate class com.google.common.util.concurrent.ListenableFuture found in modules guava-20.0.jar (com.google.guava:guava:20.0)
        //beacuse itnet version 5.5.3 remove dependence 'guava:29.0-android'
        guava: "com.google.guava:guava:29.0-android",

        /** 荔枝中台组件 **/
        //还有部分组件通过闪电插件引入（在common/env下）
        //文档地址：https://lizhi2021.feishu.cn/wiki/wikcnfvsIjyGdsKVlMQfhlJGBEh
        platformtools: "com.yibasan.lizhifm.sdk.platformtools:lzplatformtools-lib:4.0.0-ty",
        taskmanger: "com.yibasan.lizhifm.taskmanger:taskmanger-lib:1.1.2",
        deviceidentification: "com.yibasan.lizhifm.deviceidentification:deviceidentification-lib:1.5.1.0",
        // 基础组件的通用底层工具库
        basetool: "com.lizhi.component.base:basetool-base:1.6.4",
        basetool_network: "com.lizhi.component.base:basetool-network:1.6.0",
        ntp_time: "com.lizhi.component.base:basetool-ntp-base:1.6.0",

        idl_buz: "com.lizhi.idl.client.Buz:idl-Buz:$idlVersion",

        voderx: "com.lizhi.im5.sdk:im5-sdk:$vxVersion",
        e2ee  : "com.lizhi.fm.e2ee:e2ee-sdk:1.4.0",


        lizhi_traker: "com.yibasan.lizhi.tracker:tracker-lib:2.2.3", //星斗sdk
        lizhi_client_data: "com.yibasan.lizhi.tracker:tracker-clientdataintl:1.1.1",//ClientData,客户端信息采集用于召回、风控等场景,
        //doreme
        lzaudio: "com.yibasan.lizhifm:lzaudio-lib:$doremeVersion",
        rtcdorime: "com.yibasan.lizhifm:rtcdorime-lib:$doremeVersion",
        //音频组提供的视频压缩组件（https://lizhi2021.feishu.cn/wiki/Vc7hwqwGqiujICkumOVcm4eanvd）
        videoedit: "com.yibasan.lizhifm:videoedit-lib:1.2.2",
        //公共的编解码组件
        drtc_vcodec: "com.yibasan.lizhifm:drtc-vcodec:1.0.0",

        webview: "com.yibasan.lizhifm.sdk.webview:webview-lib:2.1.1",
        webview_jsbridge: "com.lizhi.component.lib:webview-jsbridge-lib:2.1.1",

        lzuploadmanager: "com.yibasan.lizhifm.lzuploadmanager:lzuploadmanager-lib:2.0.0", //Agora SDK的依赖
        tekiplayer: "com.lizhi.component.base:tekiplayer-base:$tekiPlayerVersion",
        tekiplayer_okhttp: "com.lizhi.component.base:tekiplayer-okhttp-base:$tekiPlayerVersion",
        lizhi_leakcanary:"fm.lizhi.testing.leakcanary:leakcanary-android:2.0.4",
        lizhi_leakcanary_no_op:"fm.lizhi.testing.leakcanary:leakcanary-android-no-op:2.0.4",

        /** 功能组件 **/
        arouter: "com.alibaba:arouter-api:1.5.2",
        arouter_compiler: "com.alibaba:arouter-compiler:1.5.2",
        dokit_debug: "io.github.didi.dokit:dokitx:3.7.11",
        dokit_release: "io.github.didi.dokit:dokitx-no-op:3.7.11",
        gson: "com.google.code.gson:gson:2.9.0",
        mmkv: "com.tencent:mmkv:1.2.13",
        live_eventbus: "com.github.neo-turak:LiveEventBus:1.8.1",
        desugar_jdk_libs: "com.android.tools:desugar_jdk_libs:2.1.5",
        camera_core:"androidx.camera:camera-core:$cameraxVersion",
        camera2: "androidx.camera:camera-camera2:$cameraxVersion",
        camera_lifecyle: "androidx.camera:camera-lifecycle:$cameraxVersion",
        camera_view: "androidx.camera:camera-view:$cameraxVersion",
        camera_extension: "androidx.camera:camera-extensions:$cameraxVersion",
        camera_video: "androidx.camera:camera-video:$cameraxVersion",
        jsoup:"org.jsoup:jsoup:1.18.1",

        /** 第三方应用sdk **/
        line_login_sdk    : "com.linecorp.linesdk:linesdk:latest.release",
        facebook_login_sdk: "com.facebook.android:facebook-login:latest.release",
        lokalise_sdk: "com.lokalise.android:sdk:2.3.2",

        //Google服务
        gms_auth: "com.google.android.gms:play-services-auth:21.2.0",
        gms_auth_phone: "com.google.android.gms:play-services-auth-api-phone:18.1.0",
        firebase_bom:"com.google.firebase:firebase-bom:31.2.0",
        firebase_analytics:"com.google.firebase:firebase-analytics-ktx",
        firebase_iid:"com.google.firebase:firebase-iid:21.1.0",
        google_play_review:"com.google.android.play:review:2.0.1",
        google_paly_review_ktx:"com.google.android.play:review-ktx:2.0.1",
        //归因相关
        appsflyer      : "com.appsflyer:af-android-sdk:6.12.5",
        installreferrer: "com.android.installreferrer:installreferrer:2.2",
        libphonenumber: "com.googlecode.libphonenumber:libphonenumber:8.13.39",
        pinyin4j : "com.belerweb:pinyin4j:2.5.1",

        /** UI组件 **/
        material: "com.google.android.material:material:1.6.1",
        multitype: "com.drakeet.multitype:multitype:4.3.0",
        lottie: "com.airbnb.android:lottie:$lottie_version",
        libpag: "com.tencent.tav:libpag:4.3.33",
        coil: "io.coil-kt:coil:$coil_version",
        coil_gif: "io.coil-kt:coil-gif:$coil_version",
        coil_video: "io.coil-kt:coil-video:$coil_version",
        refresh_layout_kernel: "io.github.scwang90:refresh-layout-kernel:2.0.5",
        refresh_layout_header: "io.github.scwang90:refresh-header-classics:2.0.5",
        refresh_layout_footer: "io.github.scwang90:refresh-footer-classics:2.0.5",
        //图片裁剪库
        image_crop: "com.github.yalantis:ucrop:2.2.8",
        shimmer: "com.facebook.shimmer:shimmer:0.5.0",
        //二维码库
        zxing: "com.google.zxing:core:3.5.1",
        bitmapcheck: "com.huanliao.tiya.bitmapcheck:bitmap-checker:1.2.5-Release",
        bitmapcheck_no_op: "com.huanliao.tiya.bitmapcheck:bitmap-checker-no-op:1.1.2-Release",
        // 视频库
        media3_exoplayer        : "androidx.media3:media3-exoplayer:1.3.0",
        media3_ui               : "androidx.media3:media3-ui:1.3.0",
            //升级请务必检查一下类（具体原因参阅注释 用于更改main profile的等级以兼容ios）:
        // com.interfun.buz.media.video.compressor.encoder.BuzEncoderUtil
        // com.interfun.buz.media.video.compressor.encoder.DefaultEncoderFactory
        // com.interfun.buz.media.video.compressor.encoder.DeviceMappedEncoderBitrateProvider
        // com.interfun.buz.media.video.compressor.encoder.EncoderBitrateProvider
        media3_transformer               : "androidx.media3:media3-transformer:1.3.0",
        media3_effect               : "androidx.media3:media3-effect:1.3.0",
//        media3_exoplayer_dash   : "androidx.media3:media3-exoplayer-dash:1.3.0",
//        media3_datasource_cronet: "androidx.media3:media3-datasource-cronet:1.3.0",
        adjust: "com.adjust.sdk:adjust-android:4.38.3",
        referrer: 'com.android.installreferrer:installreferrer:2.2',
        ads: "com.google.android.gms:play-services-ads-identifier:18.0.1",
        appset: 'com.google.android.gms:play-services-appset:16.0.2',
        gmsMap: 'com.google.android.gms:play-services-maps:18.2.0',
        gmsMapLocaiton: 'com.google.android.gms:play-services-location:21.3.0',
        tracing: 'androidx.tracing:tracing:1.2.0',
        tracingX: 'androidx.tracing:tracing-ktx:1.2.0',
        gifDrawable: 'pl.droidsonroids.gif:android-gif-drawable:1.2.29',
        datastorePreferences: 'androidx.datastore:datastore-preferences:1.1.7',
        fresco: "com.facebook.fresco:fresco:$fresco_version",
        frescoWebp: "com.facebook.fresco:animated-webp:$fresco_version",
        frescoAnimated: "com.facebook.fresco:animated-drawable:$fresco_version",
        compose_shape:"androidx.graphics:graphics-shapes:1.0.0-rc01",
        accompanist_permissions:"com.google.accompanist:accompanist-permissions:0.37.0",
        collection_immutable:"org.jetbrains.kotlinx:kotlinx-collections-immutable:0.3.8",
        flexbox: "com.google.android.flexbox:flexbox:3.0.0"
    ]
}
