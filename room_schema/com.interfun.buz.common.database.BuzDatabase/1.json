{"formatVersion": 1, "database": {"version": 1, "identityHash": "b60619d326d0437a15867215a0a65f14", "entities": [{"tableName": "session", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`uid` INTEGER NOT NULL, `type` INTEGER NOT NULL, `key` INTEGER NOT NULL, `value` TEXT, PRIMARY KEY(`uid`, `key`))", "fields": [{"fieldPath": "uid", "columnName": "uid", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "key", "columnName": "key", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "value", "columnName": "value", "affinity": "TEXT", "notNull": false}], "primaryKey": {"columnNames": ["uid", "key"], "autoGenerate": false}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'b60619d326d0437a15867215a0a65f14')"]}}