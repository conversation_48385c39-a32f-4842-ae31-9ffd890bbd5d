{"formatVersion": 1, "database": {"version": 2, "identityHash": "7c69562cd22f491ae35313fe4a99cd07", "entities": [{"tableName": "session", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`uid` INTEGER NOT NULL, `type` INTEGER NOT NULL, `key` INTEGER NOT NULL, `value` TEXT, PRIMARY KEY(`uid`, `key`))", "fields": [{"fieldPath": "uid", "columnName": "uid", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "key", "columnName": "key", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "value", "columnName": "value", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["uid", "key"]}, "indices": [], "foreignKeys": []}, {"tableName": "buz_media_cache", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`mediaUrl` TEXT NOT NULL, `mediaIndex` TEXT NOT NULL, `mediaType` INTEGER NOT NULL, `updateTime` INTEGER NOT NULL, `userId` INTEGER NOT NULL, `targetId` INTEGER NOT NULL, `convType` INTEGER NOT NULL, `servMsgId` INTEGER NOT NULL, PRIMARY KEY(`userId`, `targetId`, `convType`, `servMsgId`, `mediaIndex`))", "fields": [{"fieldPath": "mediaUrl", "columnName": "mediaUrl", "affinity": "TEXT", "notNull": true}, {"fieldPath": "mediaIndex", "columnName": "mediaIndex", "affinity": "TEXT", "notNull": true}, {"fieldPath": "mediaType", "columnName": "mediaType", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "updateTime", "columnName": "updateTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "userId", "columnName": "userId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "targetId", "columnName": "targetId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "convType", "columnName": "convType", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "servMsgId", "columnName": "servMsgId", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["userId", "targetId", "convType", "servMsgId", "mediaIndex"]}, "indices": [{"name": "index_buz_media_cache_mediaIndex", "unique": false, "columnNames": ["mediaIndex"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_buz_media_cache_mediaIndex` ON `${TABLE_NAME}` (`mediaIndex`)"}], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '7c69562cd22f491ae35313fe4a99cd07')"]}}