{"formatVersion": 1, "database": {"version": 24, "identityHash": "7f496a8816f8f4fc8886a7617c88fe27", "entities": [{"tableName": "contacts", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `phone` TEXT NOT NULL, `firstName` TEXT, `lastName` TEXT, `firstLetter` TEXT NOT NULL, `displayName` TEXT, `contactId` INTEGER, `filteredPhone` TEXT, `formatResult` INTEGER)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "phone", "columnName": "phone", "affinity": "TEXT", "notNull": true}, {"fieldPath": "firstName", "columnName": "firstName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "lastName", "columnName": "lastName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "firstLetter", "columnName": "firstLetter", "affinity": "TEXT", "notNull": true}, {"fieldPath": "displayName", "columnName": "displayName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "contactId", "columnName": "contactId", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "filteredPhone", "columnName": "filteredPhone", "affinity": "TEXT", "notNull": false}, {"fieldPath": "formatResult", "columnName": "formatResult", "affinity": "INTEGER", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "conv_not_played_count", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`targetId` INTEGER NOT NULL, `notPlayedCount` INTEGER NOT NULL, `convType` INTEGER NOT NULL, `updateTime` INTEGER NOT NULL, PRIMARY KEY(`targetId`))", "fields": [{"fieldPath": "targetId", "columnName": "targetId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "notPlayedCount", "columnName": "notPlayedCount", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "convType", "columnName": "convType", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "updateTime", "columnName": "updateTime", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["targetId"]}, "indices": [], "foreignKeys": []}, {"tableName": "botuser_setting_table", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`userId` INTEGER NOT NULL, `botUserId` INTEGER NOT NULL, `languageCode` TEXT NOT NULL, `voiceStyleId` INTEGER NOT NULL, PRIMARY KEY(`botUserId`))", "fields": [{"fieldPath": "userId", "columnName": "userId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "botUserId", "columnName": "botUserId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "languageCode", "columnName": "languageCode", "affinity": "TEXT", "notNull": true}, {"fieldPath": "voiceStyleId", "columnName": "voiceStyleId", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["botUserId"]}, "indices": [], "foreignKeys": []}, {"tableName": "botinfo_table", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`botUserId` INTEGER NOT NULL, `description` TEXT NOT NULL, `topics` TEXT NOT NULL, `options` TEXT, `shortDescription` TEXT, `status` INTEGER NOT NULL DEFAULT 0, `BotUIConfigWrapper_showTopic` INTEGER, `BotUIConfigWrapper_showLanguage` INTEGER, `BotUIConfigWrapper_showVoiceStyle` INTEGER, `BotUIConfigWrapper_useRemotePortrait` INTEGER, `Bot<PERSON>ConfigWrapper_showTranslation` INTEGER, `BotUIConfigWrapper_showImageButton` INTEGER, `BotUIConfigWrapper_showJoinGroup` INTEGER, `BotUIConfigWrapper_showPrivateChatMsgFeedbackEntrance` INTEGER, `BotDescriptionLink_displayName` TEXT, `BotDescriptionLink_type` INTEGER, `BotDescriptionLink_scheme` TEXT, `BotDescriptionLink_extraData` TEXT, PRIMARY KEY(`botUserId`))", "fields": [{"fieldPath": "botUserId", "columnName": "botUserId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "description", "columnName": "description", "affinity": "TEXT", "notNull": true}, {"fieldPath": "topics", "columnName": "topics", "affinity": "TEXT", "notNull": true}, {"fieldPath": "options", "columnName": "options", "affinity": "TEXT", "notNull": false}, {"fieldPath": "shortDescription", "columnName": "shortDescription", "affinity": "TEXT", "notNull": false}, {"fieldPath": "status", "columnName": "status", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "botUIConfig.showTopic", "columnName": "BotUIConfigWrapper_showTopic", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "botUIConfig.showLanguage", "columnName": "BotUIConfigWrapper_showLanguage", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "botUIConfig.showVoiceStyle", "columnName": "BotUIConfigWrapper_showVoiceStyle", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "botUIConfig.useRemotePortrait", "columnName": "BotUIConfigWrapper_useRemotePortrait", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "botUIConfig.showTranslation", "columnName": "BotUIConfigWrapper_showTranslation", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "botUIConfig.showImageButton", "columnName": "BotUIConfigWrapper_showImageButton", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "botUIConfig.showJoinGroup", "columnName": "BotUIConfigWrapper_showJoinGroup", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "botUIConfig.showPrivateChatMsgFeedbackEntrance", "columnName": "BotUIConfigWrapper_showPrivateChatMsgFeedbackEntrance", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "descriptionLink.displayName", "columnName": "BotDescriptionLink_displayName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "descriptionLink.type", "columnName": "BotDescriptionLink_type", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "descriptionLink.scheme", "columnName": "BotDescriptionLink_scheme", "affinity": "TEXT", "notNull": false}, {"fieldPath": "descriptionLink.extraData", "columnName": "BotDescriptionLink_extraData", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["botUserId"]}, "indices": [], "foreignKeys": []}, {"tableName": "bot_single_setting", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`botUserId` INTEGER NOT NULL, `key` INTEGER NOT NULL, `value` TEXT NOT NULL, PRIMARY KEY(`botUserId`, `key`))", "fields": [{"fieldPath": "botUserId", "columnName": "botUserId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "key", "columnName": "key", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "value", "columnName": "value", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["botUserId", "key"]}, "indices": [], "foreignKeys": []}, {"tableName": "group_bot_table", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`groupId` INTEGER NOT NULL, `botUserId` INTEGER NOT NULL, `joinGroupTime` INTEGER NOT NULL DEFAULT 0, PRIMARY KEY(`groupId`, `botUserId`))", "fields": [{"fieldPath": "groupId", "columnName": "groupId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "botUserId", "columnName": "botUserId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "joinGroupTime", "columnName": "joinGroupTime", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}], "primaryKey": {"autoGenerate": false, "columnNames": ["groupId", "botUserId"]}, "indices": [], "foreignKeys": []}, {"tableName": "liveplace_base_info", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`uid` INTEGER NOT NULL, `placeId` INTEGER NOT NULL, `topic` TEXT NOT NULL, `placeType` INTEGER NOT NULL, `bgImgUrl` TEXT NOT NULL, `isCustomizeBg` INTEGER NOT NULL, `bgmUrl` TEXT NOT NULL, `bgmName` TEXT NOT NULL, `visibleType` INTEGER NOT NULL, `startNotification` INTEGER NOT NULL, PRIMARY KEY(`uid`, `placeType`))", "fields": [{"fieldPath": "uid", "columnName": "uid", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "placeId", "columnName": "placeId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "topic", "columnName": "topic", "affinity": "TEXT", "notNull": true}, {"fieldPath": "placeType", "columnName": "placeType", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "bgImgUrl", "columnName": "bgImgUrl", "affinity": "TEXT", "notNull": true}, {"fieldPath": "isCustomizeBg", "columnName": "isCustomizeBg", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "bgmUrl", "columnName": "bgmUrl", "affinity": "TEXT", "notNull": true}, {"fieldPath": "bgmName", "columnName": "bgmName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "visibleType", "columnName": "visibleType", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "startNotification", "columnName": "startNotification", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["uid", "placeType"]}, "indices": [], "foreignKeys": []}, {"tableName": "liveplace_base_exist_info", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`uid` INTEGER NOT NULL, `placeType` INTEGER NOT NULL, `existPlaceType` INTEGER NOT NULL, PRIMARY KEY(`uid`))", "fields": [{"fieldPath": "uid", "columnName": "uid", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "placeType", "columnName": "placeType", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "existPlaceType", "columnName": "existPlaceType", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["uid"]}, "indices": [], "foreignKeys": []}, {"tableName": "liveplace_last_channel_id", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`uid` INTEGER NOT NULL, `channelId` INTEGER NOT NULL, PRIMARY KEY(`uid`))", "fields": [{"fieldPath": "uid", "columnName": "uid", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "channelId", "columnName": "channelId", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["uid"]}, "indices": [], "foreignKeys": []}, {"tableName": "group_member_user_table", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`groupId` INTEGER NOT NULL, `userId` INTEGER NOT NULL, `userRole` INTEGER NOT NULL, `timeStamp` INTEGER NOT NULL DEFAULT 0, PRIMARY KEY(`groupId`, `userId`))", "fields": [{"fieldPath": "groupId", "columnName": "groupId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "userId", "columnName": "userId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "userRole", "columnName": "userRole", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "timeStamp", "columnName": "timeStamp", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}], "primaryKey": {"autoGenerate": false, "columnNames": ["groupId", "userId"]}, "indices": [], "foreignKeys": []}, {"tableName": "share_link", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`targetId` INTEGER NOT NULL, `convType` INTEGER NOT NULL, `link` TEXT NOT NULL, `expiredTime` INTEGER NOT NULL, PRIMARY KEY(`targetId`, `convType`))", "fields": [{"fieldPath": "targetId", "columnName": "targetId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "convType", "columnName": "convType", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "link", "columnName": "link", "affinity": "TEXT", "notNull": true}, {"fieldPath": "expiredTime", "columnName": "expiredTime", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["targetId", "convType"]}, "indices": [], "foreignKeys": []}, {"tableName": "buz_user", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`userId` INTEGER NOT NULL, `userName` TEXT DEFAULT '', `firstName` TEXT DEFAULT '', `lastName` TEXT DEFAULT '', `portrait` TEXT DEFAULT '', `registerTime` INTEGER DEFAULT 0, `buzId` TEXT DEFAULT '', `userType` INTEGER NOT NULL DEFAULT 0, `userStatus` INTEGER NOT NULL DEFAULT 0, `isAccountDeleted` INTEGER NOT NULL DEFAULT 0, PRIMARY KEY(`userId`))", "fields": [{"fieldPath": "userId", "columnName": "userId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "userName", "columnName": "userName", "affinity": "TEXT", "notNull": false, "defaultValue": "''"}, {"fieldPath": "firstName", "columnName": "firstName", "affinity": "TEXT", "notNull": false, "defaultValue": "''"}, {"fieldPath": "lastName", "columnName": "lastName", "affinity": "TEXT", "notNull": false, "defaultValue": "''"}, {"fieldPath": "portrait", "columnName": "portrait", "affinity": "TEXT", "notNull": false, "defaultValue": "''"}, {"fieldPath": "registerTime", "columnName": "registerTime", "affinity": "INTEGER", "notNull": false, "defaultValue": "0"}, {"fieldPath": "buzId", "columnName": "buzId", "affinity": "TEXT", "notNull": false, "defaultValue": "''"}, {"fieldPath": "userType", "columnName": "userType", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "userStatus", "columnName": "userStatus", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "isAccountDeleted", "columnName": "isAccountDeleted", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}], "primaryKey": {"autoGenerate": false, "columnNames": ["userId"]}, "indices": [], "foreignKeys": []}, {"tableName": "buz_user_relation", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`userId` INTEGER NOT NULL, `muteMessages` INTEGER DEFAULT 0, `muteNotification` INTEGER DEFAULT 0, `relation` INTEGER NOT NULL DEFAULT 4, `remark` TEXT DEFAULT '', `friendTime` INTEGER NOT NULL DEFAULT 0, `isBlocked` INTEGER NOT NULL DEFAULT 0, PRIMARY KEY(`userId`))", "fields": [{"fieldPath": "userId", "columnName": "userId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "muteMessages", "columnName": "muteMessages", "affinity": "INTEGER", "notNull": false, "defaultValue": "0"}, {"fieldPath": "muteNotification", "columnName": "muteNotification", "affinity": "INTEGER", "notNull": false, "defaultValue": "0"}, {"fieldPath": "relation", "columnName": "relation", "affinity": "INTEGER", "notNull": true, "defaultValue": "4"}, {"fieldPath": "remark", "columnName": "remark", "affinity": "TEXT", "notNull": false, "defaultValue": "''"}, {"fieldPath": "friendTime", "columnName": "friendTime", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "isBlocked", "columnName": "isBlocked", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}], "primaryKey": {"autoGenerate": false, "columnNames": ["userId"]}, "indices": [], "foreignKeys": []}, {"tableName": "buz_official_account_extra", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`userId` INTEGER NOT NULL, `description` TEXT DEFAULT '', `shortDescription` TEXT DEFAULT '', PRIMARY KEY(`userId`))", "fields": [{"fieldPath": "userId", "columnName": "userId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "description", "columnName": "description", "affinity": "TEXT", "notNull": false, "defaultValue": "''"}, {"fieldPath": "shortDescription", "columnName": "shortDescription", "affinity": "TEXT", "notNull": false, "defaultValue": "''"}], "primaryKey": {"autoGenerate": false, "columnNames": ["userId"]}, "indices": [], "foreignKeys": []}, {"tableName": "buz_group", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`groupId` INTEGER NOT NULL, `serverGroupName` TEXT DEFAULT '', `displayName` TEXT DEFAULT '', `portraitUrl` TEXT DEFAULT '', `serverPortraitUrl` TEXT DEFAULT '', `memberNum` INTEGER NOT NULL DEFAULT 0, `maxMemberNum` INTEGER NOT NULL DEFAULT 0, `groupStatus` INTEGER NOT NULL DEFAULT 1, `groupType` INTEGER NOT NULL DEFAULT 1, `firstFewUsers` TEXT DEFAULT NULL, `oldFirstFewPortraits` TEXT DEFAULT NULL, PRIMARY KEY(`groupId`))", "fields": [{"fieldPath": "groupId", "columnName": "groupId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "serverGroupName", "columnName": "serverGroupName", "affinity": "TEXT", "notNull": false, "defaultValue": "''"}, {"fieldPath": "displayName", "columnName": "displayName", "affinity": "TEXT", "notNull": false, "defaultValue": "''"}, {"fieldPath": "portraitUrl", "columnName": "portraitUrl", "affinity": "TEXT", "notNull": false, "defaultValue": "''"}, {"fieldPath": "serverPortraitUrl", "columnName": "serverPortraitUrl", "affinity": "TEXT", "notNull": false, "defaultValue": "''"}, {"fieldPath": "memberNum", "columnName": "memberNum", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "maxMemberNum", "columnName": "maxMemberNum", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "groupStatus", "columnName": "groupStatus", "affinity": "INTEGER", "notNull": true, "defaultValue": "1"}, {"fieldPath": "groupType", "columnName": "groupType", "affinity": "INTEGER", "notNull": true, "defaultValue": "1"}, {"fieldPath": "firstFewUsers", "columnName": "firstFewUsers", "affinity": "TEXT", "notNull": false, "defaultValue": "NULL"}, {"fieldPath": "oldFirstFewPortraits", "columnName": "oldFirstFewPortraits", "affinity": "TEXT", "notNull": false, "defaultValue": "NULL"}], "primaryKey": {"autoGenerate": false, "columnNames": ["groupId"]}, "indices": [], "foreignKeys": []}, {"tableName": "buz_group_extra", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`groupId` INTEGER NOT NULL, `canInvite` INTEGER NOT NULL DEFAULT 0, `canEdit` INTEGER NOT NULL DEFAULT 0, `userRole` INTEGER DEFAULT 3, `userStatus` INTEGER NOT NULL DEFAULT 2, `muteMessages` INTEGER DEFAULT 0, `muteNotification` INTEGER DEFAULT 0, PRIMARY KEY(`groupId`))", "fields": [{"fieldPath": "groupId", "columnName": "groupId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "canInvite", "columnName": "canInvite", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "canEdit", "columnName": "canEdit", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "userRole", "columnName": "userRole", "affinity": "INTEGER", "notNull": false, "defaultValue": "3"}, {"fieldPath": "userStatus", "columnName": "userStatus", "affinity": "INTEGER", "notNull": true, "defaultValue": "2"}, {"fieldPath": "muteMessages", "columnName": "muteMessages", "affinity": "INTEGER", "notNull": false, "defaultValue": "0"}, {"fieldPath": "muteNotification", "columnName": "muteNotification", "affinity": "INTEGER", "notNull": false, "defaultValue": "0"}], "primaryKey": {"autoGenerate": false, "columnNames": ["groupId"]}, "indices": [], "foreignKeys": []}, {"tableName": "buz_user_un_uploaded_setting", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`userId` INTEGER NOT NULL, `muteMessages` INTEGER, `muteNotification` INTEGER, `remark` TEXT, PRIMARY KEY(`userId`))", "fields": [{"fieldPath": "userId", "columnName": "userId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "muteMessages", "columnName": "muteMessages", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "muteNotification", "columnName": "muteNotification", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "remark", "columnName": "remark", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["userId"]}, "indices": [], "foreignKeys": []}, {"tableName": "buz_group_un_uploaded_setting", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`groupId` INTEGER NOT NULL, `muteMessages` INTEGER, `muteNotification` INTEGER, PRIMARY KEY(`groupId`))", "fields": [{"fieldPath": "groupId", "columnName": "groupId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "muteMessages", "columnName": "muteMessages", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "muteNotification", "columnName": "muteNotification", "affinity": "INTEGER", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["groupId"]}, "indices": [], "foreignKeys": []}, {"tableName": "my_official_account", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`userId` INTEGER NOT NULL, `index` INTEGER NOT NULL, PRIMARY KEY(`userId`))", "fields": [{"fieldPath": "userId", "columnName": "userId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "index", "columnName": "index", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["userId"]}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '7f496a8816f8f4fc8886a7617c88fe27')"]}}