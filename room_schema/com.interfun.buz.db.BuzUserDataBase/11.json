{"formatVersion": 1, "database": {"version": 11, "identityHash": "0102e05dfdb97324fc668b0b2dd272af", "entities": [{"tableName": "msg_send_record", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`msgId` INTEGER NOT NULL, `convType` INTEGER NOT NULL, `createTime` INTEGER NOT NULL, `targetId` TEXT NOT NULL, PRIMARY KEY(`msgId`))", "fields": [{"fieldPath": "msgId", "columnName": "msgId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "convType", "columnName": "convType", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createTime", "columnName": "createTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "targetId", "columnName": "targetId", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["msgId"]}, "indices": [], "foreignKeys": []}, {"tableName": "contacts", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `phone` TEXT NOT NULL, `firstName` TEXT, `lastName` TEXT, `firstLetter` TEXT NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "phone", "columnName": "phone", "affinity": "TEXT", "notNull": true}, {"fieldPath": "firstName", "columnName": "firstName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "lastName", "columnName": "lastName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "firstLetter", "columnName": "firstLetter", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "user_relation", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`userId` INTEGER NOT NULL, `phone` TEXT NOT NULL, `userName` TEXT, `firstName` TEXT, `lastName` TEXT, `portrait` TEXT, `registerTime` INTEGER NOT NULL, `relation` INTEGER NOT NULL, `remark` TEXT, `buzId` TEXT, `email` TEXT, `friendTime` INTEGER NOT NULL DEFAULT 0, `userType` INTEGER NOT NULL DEFAULT 0, `muteMessages` INTEGER, `muteNotification` INTEGER, PRIMARY KEY(`userId`))", "fields": [{"fieldPath": "userId", "columnName": "userId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "phone", "columnName": "phone", "affinity": "TEXT", "notNull": true}, {"fieldPath": "userName", "columnName": "userName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "firstName", "columnName": "firstName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "lastName", "columnName": "lastName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "portrait", "columnName": "portrait", "affinity": "TEXT", "notNull": false}, {"fieldPath": "registerTime", "columnName": "registerTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "relation", "columnName": "relation", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "remark", "columnName": "remark", "affinity": "TEXT", "notNull": false}, {"fieldPath": "buzId", "columnName": "buzId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "email", "columnName": "email", "affinity": "TEXT", "notNull": false}, {"fieldPath": "friendTime", "columnName": "friendTime", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "userType", "columnName": "userType", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "muteMessages", "columnName": "muteMessages", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "muteNotification", "columnName": "muteNotification", "affinity": "INTEGER", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["userId"]}, "indices": [], "foreignKeys": []}, {"tableName": "black_list", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`userId` INTEGER NOT NULL, `phone` TEXT NOT NULL, PRIMARY KEY(`userId`))", "fields": [{"fieldPath": "userId", "columnName": "userId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "phone", "columnName": "phone", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["userId"]}, "indices": [], "foreignKeys": []}, {"tableName": "group_info", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`groupId` INTEGER NOT NULL, `groupName` TEXT, `portraitUrl` TEXT, `firstFewPortraits` TEXT, `memberNum` INTEGER NOT NULL, `maxMemberNum` INTEGER NOT NULL, `groupStatus` INTEGER NOT NULL, `canInvite` INTEGER NOT NULL, `canEdit` INTEGER NOT NULL, `userRole` INTEGER NOT NULL, `userStatus` INTEGER NOT NULL, `muteMessages` INTEGER, `muteNotification` INTEGER, `serverPortraitUrl` TEXT, PRIMARY KEY(`groupId`))", "fields": [{"fieldPath": "groupId", "columnName": "groupId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "groupName", "columnName": "groupName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "portraitUrl", "columnName": "portraitUrl", "affinity": "TEXT", "notNull": false}, {"fieldPath": "firstFewPortraits", "columnName": "firstFewPortraits", "affinity": "TEXT", "notNull": false}, {"fieldPath": "memberNum", "columnName": "memberNum", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "maxMemberNum", "columnName": "maxMemberNum", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "groupStatus", "columnName": "groupStatus", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "canInvite", "columnName": "canInvite", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "canEdit", "columnName": "canEdit", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "userRole", "columnName": "userRole", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "userStatus", "columnName": "userStatus", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "muteMessages", "columnName": "muteMessages", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "muteNotification", "columnName": "muteNotification", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "serverPortraitUrl", "columnName": "serverPortraitUrl", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["groupId"]}, "indices": [], "foreignKeys": []}, {"tableName": "local_mute_info", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`targetId` INTEGER NOT NULL, `isMuteMessages` INTEGER NOT NULL, `isMuteNotification` INTEGER NOT NULL, PRIMARY KEY(`targetId`))", "fields": [{"fieldPath": "targetId", "columnName": "targetId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isMuteMessages", "columnName": "isMuteMessages", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isMuteNotification", "columnName": "isMuteNotification", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["targetId"]}, "indices": [], "foreignKeys": []}, {"tableName": "conv_not_played_count", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`targetId` INTEGER NOT NULL, `notPlayedCount` INTEGER NOT NULL, `convType` INTEGER NOT NULL, `updateTime` INTEGER NOT NULL, PRIMARY KEY(`targetId`))", "fields": [{"fieldPath": "targetId", "columnName": "targetId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "notPlayedCount", "columnName": "notPlayedCount", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "convType", "columnName": "convType", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "updateTime", "columnName": "updateTime", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["targetId"]}, "indices": [], "foreignKeys": []}, {"tableName": "rtp_stream_info", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`streamId` INTEGER NOT NULL, `callerId` INTEGER NOT NULL, `targetId` INTEGER NOT NULL, `convType` INTEGER NOT NULL, `convTargetId` INTEGER NOT NULL, `rtpPullTimestamp` INTEGER NOT NULL, `duration` INTEGER NOT NULL, `audioFilePath` TEXT NOT NULL, PRIMARY KEY(`streamId`))", "fields": [{"fieldPath": "streamId", "columnName": "streamId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "callerId", "columnName": "callerId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "targetId", "columnName": "targetId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "convType", "columnName": "convType", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "convTargetId", "columnName": "convTargetId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "rtpPullTimestamp", "columnName": "rtpPullTimestamp", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "duration", "columnName": "duration", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "audioFilePath", "columnName": "audioFilePath", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["streamId"]}, "indices": [], "foreignKeys": []}, {"tableName": "botuser_setting_table", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`userId` INTEGER NOT NULL, `botUserId` INTEGER NOT NULL, `languageCode` TEXT NOT NULL, `voiceStyleId` INTEGER NOT NULL, PRIMARY KEY(`botUserId`))", "fields": [{"fieldPath": "userId", "columnName": "userId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "botUserId", "columnName": "botUserId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "languageCode", "columnName": "languageCode", "affinity": "TEXT", "notNull": true}, {"fieldPath": "voiceStyleId", "columnName": "voiceStyleId", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["botUserId"]}, "indices": [], "foreignKeys": []}, {"tableName": "botinfo_table", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`botUserId` INTEGER NOT NULL, `description` TEXT NOT NULL, `userInfo` TEXT, `topics` TEXT NOT NULL, `options` TEXT, `shortDescription` TEXT, `sortIndex` INTEGER NOT NULL DEFAULT 0, `BotUIConfigWrapper_showTopic` INTEGER, `BotUIConfigWrapper_showLanguage` INTEGER, `BotUIConfigWrapper_showVoiceStyle` INTEGER, `BotUIConfigWrapper_useRemotePortrait` INTEGER, `BotUIConfigWrapper_showTranslation` INTEGER, `Bot<PERSON>ConfigWrapper_showImageButton` INTEGER, PRIMARY KEY(`botUserId`))", "fields": [{"fieldPath": "botUserId", "columnName": "botUserId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "description", "columnName": "description", "affinity": "TEXT", "notNull": true}, {"fieldPath": "userInfo", "columnName": "userInfo", "affinity": "TEXT", "notNull": false}, {"fieldPath": "topics", "columnName": "topics", "affinity": "TEXT", "notNull": true}, {"fieldPath": "options", "columnName": "options", "affinity": "TEXT", "notNull": false}, {"fieldPath": "shortDescription", "columnName": "shortDescription", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sortIndex", "columnName": "sortIndex", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "botUIConfig.showTopic", "columnName": "BotUIConfigWrapper_showTopic", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "botUIConfig.showLanguage", "columnName": "BotUIConfigWrapper_showLanguage", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "botUIConfig.showVoiceStyle", "columnName": "BotUIConfigWrapper_showVoiceStyle", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "botUIConfig.useRemotePortrait", "columnName": "BotUIConfigWrapper_useRemotePortrait", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "botUIConfig.showTranslation", "columnName": "BotUIConfigWrapper_showTranslation", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "botUIConfig.showImageButton", "columnName": "BotUIConfigWrapper_showImageButton", "affinity": "INTEGER", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["botUserId"]}, "indices": [], "foreignKeys": []}, {"tableName": "bot_single_setting", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`botUserId` INTEGER NOT NULL, `key` INTEGER NOT NULL, `value` TEXT NOT NULL, PRIMARY KEY(`botUserId`, `key`))", "fields": [{"fieldPath": "botUserId", "columnName": "botUserId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "key", "columnName": "key", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "value", "columnName": "value", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["botUserId", "key"]}, "indices": [], "foreignKeys": []}], "views": [{"viewName": "ContactUserInfo", "createSql": "CREATE VIEW `${VIEW_NAME}` AS select A.id, A.phone, A.firstName, A<PERSON>lastName, A.firstLetter, B.userId as userId, <PERSON><PERSON>userName as userName, B<PERSON>firstName as userFirstName, <PERSON><PERSON>lastName as userLastName, B<PERSON>portrait as portrait, B.registerTime as registerTime from contacts as A left join user_relation as B on A.phone = B.phone"}], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '0102e05dfdb97324fc668b0b2dd272af')"]}}