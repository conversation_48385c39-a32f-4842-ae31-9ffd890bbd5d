<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools" >

    <application>
        <activity
            android:name="com.interfun.buz.sdk.push.NotifyDispatchActivity"
            android:configChanges="keyboard|keyboardHidden|screenSize|orientation"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/AppNoTitleTheme.transparent">
            <intent-filter tools:ignore="AppLinkUrlError">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>

            <intent-filter>
                <action android:name="FCM_NOTIFICATION_CLICK_ACTION" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

        </activity>

    </application>

</manifest>