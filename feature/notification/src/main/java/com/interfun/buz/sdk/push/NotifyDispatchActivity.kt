package com.interfun.buz.sdk.push

import android.content.Intent
import android.os.Bundle
import com.alibaba.android.arouter.launcher.ARouter
import com.google.gson.Gson
import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.base.ktx.log
import com.interfun.buz.base.ktx.logError
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.common.base.BaseActivity
import com.interfun.buz.common.ktx.uid
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.service.StartUpService
import com.interfun.buz.notification.model.Error
import com.interfun.buz.notification.model.ErrorReason
import com.interfun.buz.notification.utils.NotificationUtils
import com.interfun.buz.push.model.PushActionOld
import com.interfun.buz.push.model.PushPayloadNew
import com.interfun.buz.push.model.PushPayloadOld
import com.interfun.buz.push.model.PushPayloadType
import com.interfun.buz.push.util.toPushPayloadNew
import com.lizhi.component.push.lzpushbase.utils.PushLogzUtil
import com.lizhi.component.push.lzpushsdk.PushSdkManager
import dagger.hilt.android.AndroidEntryPoint
import org.json.JSONObject

/**
 * 目前华为推送会用到，未来fcm的代理（不拉起应用进程）推送也会用到，保留后面如果需要接入厂商通道时使用。现在使用[EntryPointActivity](启动页)
 */
@AndroidEntryPoint
class NotifyDispatchActivity : BaseActivity() {

    val TAG = "NotifyDispatchActivity"
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        PushLogzUtil.logD("NotifyDispatchActivity created ${intent}")
//        Debug.waitForDebugger()
        logInfo(TAG, "NotifyDispatchActivity onCreate ${intent.toURI()}")
        val pushDeviceId: String? = intent.getStringExtra("deviceId")
        val pushTokenId: String? = intent.getStringExtra("token")

        /**
         * 解析对应的跳转后携带的内容
         * 注意bestBean可能为null，注意做好判断,点击通知栏冷启动的时候pushBean为空，因为这个时候还没有获取到
         */
        PushSdkManager.instance.parseIntent(this, intent, com.interfun.buz.common.constants.deviceId, UserSessionManager.uid.toString()) {con->
            if (con != null) {
                logInfo(TAG, "reportClickToPushPlatform,groupId:${con.groupId}, action:${con.actionString},token:${pushTokenId},channel:${con.channel}")
                PushSdkManager.instance.uploadNotifyClick(
                    appContext,
                    pushDeviceId?:"",
                    UserSessionManager.uid.toString(),
                    con.groupId?:"",
                    pushTokenId,
                    con.channel
                )
                handleActionString(con.actionString)
            }
            finish()
        }
    }

    private fun handleActionString(actionString: String?) {
        try {
            val intent = if (actionString == null) {
                logInfo(TAG, "handleActionString actionString is null")
                createIntent(null)
            } else {
                val actionJson = JSONObject(actionString)
                val version = actionJson.optInt("ver", 1)
                logInfo(TAG, "handleActionString version = $version")
                if (version == 2) {
                    val payload = Gson().fromJson(actionString, PushPayloadNew::class.java)
                    createIntent(recreateRouter(payload))
                } else {
                    val actionOld = Gson().fromJson(actionString, PushActionOld::class.java)
                    createIntent(recreateRouter(actionOld.toPushPayloadNew()))
                }
            }
            startActivity(intent)
        } catch (e: Exception) {
            logError(TAG, "handleActionString error", e)
            startActivity(packageManager.getLaunchIntentForPackage(this.packageName))
        }
    }

    private fun recreateRouter(payload: PushPayloadNew): String {
        if (payload.type != PushPayloadType.TYPE_PRIVATE && payload.type != PushPayloadType.TYPE_GROUP) {
            return payload.router?.toString() ?: throw IllegalArgumentException("router is null")
        }
        val (convId, isGroup) = if (payload.type == PushPayloadType.TYPE_PRIVATE) {
            payload.senderUserInfo?.userId to false
        } else {
            payload.groupInfo?.groupId to true
        }
        return NotificationUtils.recreateChatRouter(
            convId ?: throw IllegalArgumentException("convId is null"),
            isGroup,
            payload.router?.toString(),
            payload.imInfo?.svrMsgId,
            payload.pushExtra?.reactionOpUserId
        )
    }

    private fun createIntent(router: String?): Intent {
        logInfo(TAG, "createIntent router = $router")
        return ARouter.getInstance().navigation(StartUpService::class.java)
            .getGatewayActivityIntentByRouter(this, router)
    }
}