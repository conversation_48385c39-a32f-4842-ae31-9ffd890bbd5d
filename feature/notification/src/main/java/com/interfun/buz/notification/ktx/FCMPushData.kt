package com.interfun.buz.notification.ktx

import com.interfun.buz.notification.utils.NotificationUtils
import com.interfun.buz.push.model.FCMPushData

suspend fun FCMPushData.defaultContent(): String {
    val payload = payload
    return payload.contentReplaceInfo?.let {
        if (it.replaceType == 1) {
            //get replace pushContent from senderUserInfo
            val userInfo = payload.senderUserInfo
            if (userInfo != null) {
                val userName = NotificationUtils.createUserName(userInfo.userId, userInfo.name)
                body.replace(it.replaceText, userName.toString())
            } else {
                body
            }
        } else if (it.replaceType == 2) {
            //get replace pushContent from groupBaseInfo
            val groupInfo = payload.groupInfo
            if (groupInfo != null) {
                val groupName = NotificationUtils.createGroupName(groupInfo.groupId, groupInfo.name)
                body.replace(it.replaceText, groupName.toString())
            } else {
                body
            }
        } else if (it.replaceType == 3) {
            //the replaced content is emptied(置空)
            body.replace(it.replaceText, "")
        } else {
            body
        }
    } ?: body
}

suspend fun FCMPushData.defaultTitle(): String {
    val payload = payload
    return payload.titleReplaceInfo?.let {
        if (it.replaceType == 1) {
            //get replace pushContent from senderUserInfo
            val userInfo = payload.senderUserInfo
            if (userInfo != null) {
                val userName = NotificationUtils.createUserName(userInfo.userId, userInfo.name)
                title.replace(it.replaceText, userName.toString())
            } else {
                title
            }
        } else if (it.replaceType == 2) {
            //get replace pushContent from groupBaseInfo
            val groupInfo = payload.groupInfo
            if (groupInfo != null) {
                val groupName = NotificationUtils.createGroupName(groupInfo.groupId, groupInfo.name)
                title.replace(it.replaceText, groupName.toString())
            } else {
                title
            }
        } else if (it.replaceType == 3) {
            //the replaced content is emptied(置空)
            title.replace(it.replaceText, "")
        } else {
            title
        }
    } ?: title
}