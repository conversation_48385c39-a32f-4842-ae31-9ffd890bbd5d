package com.interfun.buz.notification.utils

import android.content.Intent
import android.os.Bundle
import com.google.gson.Gson
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.bean.push.PushPayload
import com.interfun.buz.common.constants.CommonMMKV
import com.interfun.buz.common.constants.RouterParamKey
import com.interfun.buz.common.constants.TrackConstant
import com.interfun.buz.common.ktx.uid
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.utils.BuzTracker
import com.interfun.buz.notification.NotificationManager
import com.interfun.buz.notification.model.NotificationLogModel
import com.interfun.buz.push.model.PushPlatformBean
import com.lizhi.component.push.lzpushsdk.PushSdkManager

internal object NotificationTracker {
    const val TAG = "NotificationTracker"
    private val gson = Gson()
    fun logNotificationExpose(
        logModel: NotificationLogModel?
    ) {
        logModel ?: return
        val imMsgType = logModel.imMsgType ?: 0
        val isProcessOnline = createdActivityCount > 0
        val pageBusinessType = logModel.fromTargetType ?: ""
        val convId = logModel.convId
        if (logModel.isEditAsr==null ||logModel.isEditAsr == false) {
            BuzTracker.onElementExposure {
                put(TrackConstant.KEY_EXCLUSIVE_ID, "EE2023041001")
                put(TrackConstant.KEY_TITLE, "公共推送")
                put(TrackConstant.KEY_ELEMENT_CONTENT, "推送通知")
                put(TrackConstant.KEY_PAGE_TYPE, "push")
                put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, "${logModel.pushType}")
                put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT, imMsgType.toString())
                put(TrackConstant.KEY_VIEW_SOURCE, if (isProcessOnline) "0" else "1")
                put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, pageBusinessType)
                put(TrackConstant.KEY_PAGE_BUSINESS_ID, convId ?: "")
            }
        } else {
            BuzTracker.onElementExposure {
                put(TrackConstant.KEY_EXCLUSIVE_ID, "EE2024082702")
                put(TrackConstant.KEY_TITLE, "notification")
                put(TrackConstant.KEY_ELEMENT_CONTENT, "push_notification")
                put(TrackConstant.KEY_PAGE_TYPE, "push")
                put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, pageBusinessType)
                put(TrackConstant.KEY_PAGE_BUSINESS_ID, convId ?: "")
                put(
                    TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT,
                    if (logModel.isAutoPlayedMsg == true) "autoplay" else "no_autoplay"
                )
                put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, "${logModel.pushType}")
                put(TrackConstant.KEY_VIEW_SOURCE, if (isProcessOnline) "0" else "1")
            }
        }
    }

    fun handlePushClickLog(intent: Intent) {
        try {
            val logModelStr = intent.getStringExtra(RouterParamKey.Startup.KEY_LOG_PUSH_MODEL)
            val pushPlatformStr =
                intent.getStringExtra(RouterParamKey.Startup.KEY_PLATFORM_PUSH_BEAN)
            logInfo(
                TAG,
                "handlePushClick,logModelStr:$logModelStr,pushPlatformStr:$pushPlatformStr"
            )
            val logModel = if (logModelStr.isNullOrEmpty()) null else gson.fromJson(
                logModelStr,
                NotificationLogModel::class.java
            )
            val pushPlatformBean = if (pushPlatformStr.isNullOrEmpty()) null else gson.fromJson(
                pushPlatformStr,
                PushPlatformBean::class.java
            )
            logModel?.let {
                if (logModel.pushType == PushPayload.TYPE_FEEDBACK_REPLY_NOTICE) {
                    //以前的逻辑搞在这块的，时间来不及弄这块了，以后再优化，后面其他需求不要参考这个在这里做逻辑
                    CommonMMKV.isFeedbackEntryDotVisible = false
                    CommonMMKV.isFeedbackLogDotVisible = false
                }
                val isFromOnlineNotification =
                    intent.getBooleanExtra(
                        RouterParamKey.Startup.KEY_IS_FROM_ONLINE_NOTIFICATION,
                        false
                    )
                BuzTracker.trackHandlePush(
                    type = logModel.pushType.toString(),
                    imMsgType = logModel.imMsgType ?: 0,
                    imConvType = logModel.fromTargetType,
                    convId = logModel.convId,
                    serMsgId = logModel.serMsgId,
                    isFromOnlineNotification = isFromOnlineNotification
                )
            }
            pushPlatformBean?.let {
                PushSdkManager.instance.uploadNotifyClick(
                    appContext,
                    pushPlatformBean.deviceId,
                    UserSessionManager.uid.toString(),
                    pushPlatformBean.groupId,
                    pushPlatformBean.token,
                    pushPlatformBean.channel
                )
            }
        } catch (t: Throwable) {
            logError(TAG, t, "handlePushClickLog error")
        }
    }

    fun createIntentExtra(
        logModel: NotificationLogModel?,
        pushPlatformBean: PushPlatformBean?
    ): Bundle {
        val bundle = Bundle()
        bundle.putBoolean(RouterParamKey.Startup.KEY_LOG_PUSH_CLICK, true)
        val logModelStr = if (logModel == null) null else gson.toJson(logModel)
        val pushPlatformStr = if (pushPlatformBean == null) null else gson.toJson(pushPlatformBean)
        bundle.putString(RouterParamKey.Startup.KEY_LOG_PUSH_MODEL, logModelStr)
        bundle.putString(RouterParamKey.Startup.KEY_PLATFORM_PUSH_BEAN, pushPlatformStr)
        return bundle
    }

    /**
     * When a file pause its download when user is using mobile data when app reopens
     */
    fun onPauseDownloadResult() {
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2025061603")
            put(TrackConstant.KEY_RESULT_TYPE, "file_pause_donwload_result")
            put(TrackConstant.KEY_PAGE_TYPE, "chat")
        }
    }
}