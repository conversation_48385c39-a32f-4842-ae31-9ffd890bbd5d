package com.interfun.buz.notification.repository.mapping

import com.interfun.buz.base.ktx.delayInScope
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.common.bean.voicecall.ChannelType
import com.interfun.buz.common.eventbus.voicecall.CancelVoiceCallInviteEvent
import com.interfun.buz.common.manager.CallNotificationCache.getVoiceCallNotifiIdByChannelId
import com.interfun.buz.common.manager.chat.CallPendStatus
import com.interfun.buz.common.manager.chat.ChannelInviteManager
import com.interfun.buz.common.manager.chat.ChannelPendStatusManager
import com.interfun.buz.common.utils.NotificationUtil
import com.interfun.buz.notification.model.Cancel
import com.interfun.buz.notification.model.CancelReason
import com.interfun.buz.notification.model.NotificationHandleResult
import com.interfun.buz.notification.storage.NotificationMemStorage
import com.interfun.buz.push.model.CallRevokeExtra
import com.interfun.buz.push.model.FCMPushData
import kotlinx.coroutines.GlobalScope

class CallRevokeMapping : FcmNotificationMapping {
    companion object {
        const val TAG = "CallRevokeMapping"
    }

    override suspend fun mapToNotificationModel(fcmPushData: FCMPushData): NotificationHandleResult {
        val extra: CallRevokeExtra =
            fcmPushData.payload.pushExtra ?: return Cancel(CancelReason.PUSH_EXTRA_NULL)
        val channelId = extra.channelId
        val channelType = extra.channelType
        val callType = extra.callType
        val voiceCallNotifiId = getVoiceCallNotifiIdByChannelId(channelId)
        logInfo(
            TAG,
            "Receive push notification and cancel voice call invitation, channelId = $channelId, voiceCallNotifiId = $voiceCallNotifiId channelType: $channelType"
        )
        if (voiceCallNotifiId != null) {
            NotificationUtil.cancelVoiceCallNotificationAndUpdatePendStatus(
                voiceCallNotifiId,
                channelType
            )
            NotificationMemStorage.removeVoiceCallRevokeChannel(channelId)
        } else {
            NotificationMemStorage.addVoiceCallRevokeChannel(channelId)
            //onair 里面channelid是不会变的，所以可能会导致打了取消再打被过滤问题，所以延迟短一点，
            // 旧逻辑延迟都挺长，不管了，很快onair就被移除了，live place 也是随机id，不会有问题
            val delayTime = if (ChannelType.isLivePlaceType(channelType)) {
                100
            } else {
                10_000L
            }
            delayInScope(
                GlobalScope,
                delayTime
            ) { NotificationMemStorage.removeVoiceCallRevokeChannel(channelId) }
        }
        CancelVoiceCallInviteEvent.post(channelId,callType = callType)
        ChannelInviteManager.removeInviteMapByChannelId(channelId.toString())
        ChannelPendStatusManager.changeStatus(CallPendStatus.IDLE)
        return Cancel(CancelReason.CALL_REVOKE)
    }
}