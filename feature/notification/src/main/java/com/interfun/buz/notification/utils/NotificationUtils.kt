package com.interfun.buz.notification.utils

import android.app.PendingIntent
import android.content.Intent
import android.graphics.drawable.BitmapDrawable
import android.os.Build.VERSION
import android.os.Build.VERSION_CODES
import android.os.Bundle
import androidx.annotation.ColorInt
import androidx.annotation.DrawableRes
import androidx.core.app.Person
import androidx.core.content.pm.ShortcutInfoCompat
import androidx.core.graphics.drawable.IconCompat
import coil.Coil
import coil.request.ImageRequest
import coil.transform.CircleCropTransformation
import coil.transform.Transformation
import com.alibaba.android.arouter.launcher.ARouter
import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.base.ktx.asColor
import com.interfun.buz.base.ktx.createdActivityCount
import com.interfun.buz.base.ktx.dp
import com.interfun.buz.base.ktx.ifNullOrEmpty
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.common.R
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.constants.RouterParamKey
import com.interfun.buz.common.ktx.getContactFirstName
import com.interfun.buz.common.ktx.toAdaptiveBitmap
import com.interfun.buz.common.ktx.uid
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.manager.cache.group.GroupInfoCacheManager
import com.interfun.buz.common.manager.cache.user.UserRelationCacheManager
import com.interfun.buz.common.manager.router.RouterCreator
import com.interfun.buz.common.service.StartUpService
import com.interfun.buz.common.widget.portrait.PortraitUtil
import com.interfun.buz.im.ktx.getConvTargetIdLong
import com.interfun.buz.im.ktx.isGroup
import com.interfun.buz.im.ktx.isReceive
import com.interfun.buz.notification.model.PushFrom
import com.lizhi.im5.sdk.message.IMessage
import com.yibasan.lizhifm.sdk.platformtools.ApplicationContext
import org.json.JSONObject
import java.util.Random

internal object NotificationUtils {

    //size大小按照adaptive icon标准
    //https://developer.android.com/guide/practices/ui_guidelines/icon_design_adaptive?hl=zh-cn
    private val SIZE_AVATAR = 72.dp
    private const val TAG = "NotificationUtils"

    suspend fun createGroupName(groupId: Long, defaultName: String?): CharSequence {
        val groupInfo = GroupInfoCacheManager.getGroupInfoBeanByIdSync(groupId)
        return groupInfo?.groupName
            .ifNullOrEmpty { defaultName.ifNullOrEmpty { defaultName.toString() } }
    }

    suspend fun createUserName(userId: Long, defaultName: String?): CharSequence {
        val userRelation = UserRelationCacheManager.getUserRelationInfoByUidSync(userId)
        return userRelation?.getContactFirstName()
            .ifNullOrEmpty { defaultName.ifNullOrEmpty { userId.toString() } }
    }

    suspend fun createPersonUser(
        uid: Long,
        defaultPortrait: String? = null,
        defaultName: String? = null,
        @ColorInt iconBgColor: Int? = null
    ): Person {
        var icon: IconCompat? = null
        getUserAvatarDrawable(
            uid, defaultPortrait, false, CircleCropTransformation()
        )?.let {
            val adaptiveIcon =
                it.toAdaptiveBitmap(backgroundColor = iconBgColor)
            icon = IconCompat.createWithAdaptiveBitmap(adaptiveIcon)
        }
        return Person.Builder().setName(createUserName(uid, defaultName)).setIcon(icon).build()
    }

    suspend fun createPersonGroup(
        groupId: Long,
        defaultPortrait: String? = null,
        defaultName: String? = null,
    ): Person {
        var icon: IconCompat? = null
        getGroupAvatar(
            groupId, defaultPortrait, false, CircleCropTransformation()
        )?.let {
            val adaptiveIcon =
                it.toAdaptiveBitmap(backgroundColor = R.color.notification_avatar_bg.asColor())
            icon = IconCompat.createWithAdaptiveBitmap(adaptiveIcon)
        }
        return Person.Builder().setName(createGroupName(groupId, defaultName)).setIcon(icon).build()
    }

    suspend fun createSendInfoPerson(imMessage: IMessage): Person {
        val senderInfo = imMessage.userInfo
        return createPersonUser(
            senderInfo.userId.toLongOrNull() ?: 0L,
            senderInfo.portraitURL,
            senderInfo?.nickName
        )
    }

    suspend fun createTargetPerson(imMessage: IMessage): Person {
        return if (imMessage.isGroup) {
            createPersonGroup(imMessage.getConvTargetIdLong())
        } else {
            val defaultUrl = if (imMessage.isReceive) {
                imMessage.userInfo.portraitURL
            } else {
                null
            }
            val defaultName = if (imMessage.isReceive) {
                imMessage.userInfo.nickName
            } else {
                null
            }
            createPersonUser(
                imMessage.getConvTargetIdLong(),
                defaultPortrait = defaultUrl,
                defaultName = defaultName
            )
        }
    }


    suspend fun requestBitmap(
        url: String,
        width: Int,
        height: Int,
        allowHardware: Boolean = false,
        vararg transformations: Transformation
    ): BitmapDrawable? {
        return Coil.imageLoader(appContext).execute(
            ImageRequest.Builder(appContext).data(url).size(width, height)
                .allowHardware(allowHardware).transformations(*transformations).build()
        ).drawable as? BitmapDrawable
    }

    private suspend fun getGroupAvatar(
        groupId: Long,
        defaultUrl: String?,
        allowHardware: Boolean,
        vararg transformationsBelowO: Transformation
    ): BitmapDrawable? {
        val groupPortrait =
            GroupInfoCacheManager.getGroupInfoBeanByIdSync(groupId)?.serverPortraitUrl ?: defaultUrl
        if (groupPortrait.isNullOrEmpty()) {
            logInfo(
                TAG,
                "getLargeIcon,groupInfoIsNull:groupId:$groupId,groupPortrait:$groupPortrait"
            )
            return defaultAvatar(R.drawable.common_pic_portrait_group_default)
        }
        val resizeUrl = PortraitUtil.resizePortraitUrl(groupPortrait)
        return if (VERSION.SDK_INT < VERSION_CODES.O) {
            requestBitmap(
                resizeUrl, SIZE_AVATAR, SIZE_AVATAR, allowHardware, *transformationsBelowO
            )
        } else {
            requestBitmap(
                resizeUrl, SIZE_AVATAR, SIZE_AVATAR, allowHardware
            )
        } ?: defaultAvatar(R.drawable.common_pic_portrait_group_default)
    }

    private suspend fun getUserAvatarDrawable(
        userId: Long,
        defaultUrl: String?,
        allowHardware: Boolean,
        vararg transformationsBelowO: Transformation,
    ): BitmapDrawable? {
        val uid = userId
        val portrait =
            UserRelationCacheManager.getUserRelationInfoByUidSync(uid)?.portrait ?: defaultUrl
        if (portrait.isNullOrEmpty()) {
            return defaultAvatar(R.drawable.common_pic_portrait_user_default)
        }
        val resizeUrl = PortraitUtil.resizePortraitUrl(portrait)
        return if (VERSION.SDK_INT < VERSION_CODES.O) {
            requestBitmap(
                resizeUrl, SIZE_AVATAR, SIZE_AVATAR, allowHardware, *transformationsBelowO
            )
        } else {
            requestBitmap(
                resizeUrl, SIZE_AVATAR, SIZE_AVATAR, allowHardware
            )
        } ?: defaultAvatar(R.drawable.common_pic_portrait_user_default)
    }

    private suspend fun defaultAvatar(@DrawableRes icon: Int): BitmapDrawable? {
        val builder = ImageRequest.Builder(appContext).data(icon).allowHardware(false)
            .size(SIZE_AVATAR, SIZE_AVATAR)
        if (VERSION.SDK_INT < VERSION_CODES.O) {
            builder.transformations(CircleCropTransformation())
        }
        return Coil.imageLoader(appContext).execute(builder.build()).drawable as? BitmapDrawable
    }

    fun getPendingIntentByRouter(
        requestCode: Int,
        router: String?,
        extra: Bundle? = null
    ): PendingIntent {
        val intent = ARouter.getInstance().navigation(StartUpService::class.java)
            .getGatewayActivityIntentByRouter(appContext, router)
        extra?.let { intent.putExtras(extra) }
        return getPendingIntent(requestCode, intent)
    }


    fun getPendingIntent(requestCode: Int, intent: Intent): PendingIntent {
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_MULTIPLE_TASK)
        val flag = if (VERSION.SDK_INT >= 23) {
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        } else {
            PendingIntent.FLAG_UPDATE_CURRENT
        }
        val isProcessOnline = createdActivityCount > 0
        intent.putExtra(RouterParamKey.Startup.KEY_IS_FROM_ONLINE_NOTIFICATION, isProcessOnline)
        return PendingIntent.getActivity(
            ApplicationContext.getContext(),
            requestCode,
            intent,
            flag
        )
    }

    suspend fun createShortcutInfo(
        targetId: Long,
        person: Person,
        isGroup: Boolean,
    ): ShortcutInfoCompat {
        val router = if (isGroup) {
            createGroupChatRouter(targetId, null, null)
        } else {
            createPrivateChatRouter(
                targetId.toString(),
                UserSessionManager.uid.toString(),
                null,
                null
            )
        }
        val shortLabel = person.name.ifNullOrEmpty { targetId.toString() }
        return ShortcutInfoCompat.Builder(appContext, targetId.toString())
            .setIcon(person.icon)
            .setShortLabel(shortLabel)
            .setIntent(getIntent(router, "chat"))
            .setLongLived(true)
            .setPerson(person)
            .setIsConversation()
            .build()
    }

    fun getIntent(router: String, action: String): Intent {
        return routerServices<StartUpService>().value?.getGatewayActivityIntentByRouter(
            appContext, router
        )!!.setAction(action)
    }

    fun createPrivateChatRouter(
        fromId: String,
        targetId: String,
        serMsgId: String?,
        reactionOpUserId: String?,
    ): String {
        return RouterCreator.createPrivateChatRouter(
            fromId,
            targetId,
            serMsgId,
            reactionOpUserId,
            null
        ).toString()
    }

    fun createGroupChatRouter(
        groupId: Long,
        serMsgId: String?,
        reactionOpUserId: String?,
    ): String {
        return RouterCreator.createGroupChatRouter(
            groupId,
            serMsgId,
            reactionOpUserId,
            null
        ).toString()
    }

    /**
     * @param type 1:private 2:group
     */
    fun createHomeRouter(targetId: String, type: Int): String {
        return RouterCreator.createHomeListRouter(targetId, type).toString()
    }

    fun generateRandomNotificationId(): Int {
        return Random().nextInt(1000000000)
    }

    fun recreateChatRouter(
        targetId: Long,
        isGroup: Boolean,
        originRouter: String?,
        serMsgId: String?,
        reactionOpUserId: String?
    ): String {
        if (originRouter.isNullOrEmpty()) {
            return if (isGroup) {
                createGroupChatRouter(targetId, serMsgId, reactionOpUserId)
            } else {
                createPrivateChatRouter(
                    targetId.toString(),
                    UserSessionManager.uid.toString(),
                    serMsgId,
                    reactionOpUserId
                )
            }
        }
        try {
            val originRouterJson = JSONObject(originRouter)
            val extraData = originRouterJson.optJSONObject("extraData")
            extraData?.put(RouterParamKey.Chat.KEY_SER_MSG_ID, serMsgId)
            extraData?.put(
                RouterParamKey.Chat.KEY_REACTION_OP_USER_ID,
                reactionOpUserId
            )
            extraData?.let {
                originRouterJson.put("extraData", it)
            }
            return originRouterJson.toString()
        } catch (e: Exception) {
            logInfo(TAG, "recreateChatRouter error:${e.message}")
            return originRouter
        }
    }
}