package com.interfun.buz.notification.repository.mapping

import com.interfun.buz.notification.model.NotificationHandleResult
import com.interfun.buz.push.model.FCMPushData
import com.interfun.buz.push.model.PushPayloadType

internal class FcmMappingStrategy(
    private val chatMsgMapping: FcmNotificationMapping = ChatNotificationMapping(),
    private val defaultMapping: FcmNotificationMapping = DefaultFcmNotificationMapping(),
    private val callInvitationMapping: FcmNotificationMapping = CallInvitationNotificationMapping(),
    private val callRevokeMapping: FcmNotificationMapping = CallRevokeMapping(),
    private val friendApplyMapping: FriendApplyNotificationMapping = FriendApplyNotificationMapping()
) {

    private val livePlaceInvitationMapping: FcmNotificationMapping = LivePlaceInvitationNotificationMapping()
    private val livePlaceInvitationRevokeMapping: FcmNotificationMapping = LivePlaceInvitationRevokeMapping()
    private val livePlaceOpenMapping: FcmNotificationMapping = LivePlaceOpenNotificationMapping()
    private val livePlaceKnockMapping: FcmNotificationMapping = LivePlaceKnockNotificationMapping()

    suspend fun map(fcmPushData: FCMPushData): NotificationHandleResult {
        return getMapping(fcmPushData.payload.type).mapToNotificationModel(fcmPushData)
    }

    private fun getMapping(type: Int): FcmNotificationMapping {
        return when (type) {
            PushPayloadType.TYPE_PRIVATE,
            PushPayloadType.TYPE_GROUP -> chatMsgMapping

            PushPayloadType.TYPE_CHANNEL_1V1_INVITE,
            PushPayloadType.TYPE_CHANNEL_GROUP_INVITE -> callInvitationMapping
            PushPayloadType.TYPE_CHANNEL_INVITE_REVOKE -> callRevokeMapping

            PushPayloadType.TYPE_FRIEND_APPLY,
            PushPayloadType.TYPE_FRIEND_APPLY_PASSED -> friendApplyMapping
            //live place invite
            PushPayloadType.TYPE_LIVE_PLACE_INVITE_1V1,
            PushPayloadType.TYPE_LIVE_PLACE_INVITE_GROUP -> livePlaceInvitationMapping
            PushPayloadType.TYPE_LIVE_PLACE_INVITE_REVOKE -> livePlaceInvitationRevokeMapping
            //live place opened
            PushPayloadType.TYPE_LIVE_PLACE_START_1V1,
            PushPayloadType.TYPE_LIVE_PLACE_START_GROUP -> livePlaceOpenMapping
            //live place knock knock
            PushPayloadType.TYPE_LIVE_PLACE_KNOCK_KNOCK -> livePlaceKnockMapping

            else -> defaultMapping
        }
    }
}