package com.interfun.buz.notification.repository

import com.interfun.buz.common.database.UserDatabase
import com.interfun.buz.common.database.dao.chat.insertOrUpdate
import com.interfun.buz.common.database.entity.chat.ConvNotPlayedCountEntity
import com.interfun.buz.common.manager.Empty
import com.interfun.buz.common.manager.OnLogin
import com.interfun.buz.common.manager.OnLogout
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.manager.launchAsync
import com.interfun.buz.common.manager.userLifecycleScope
import com.interfun.buz.im.IMAgent
import com.lizhi.im5.sdk.conversation.IConversation
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * only use for notification.the notPlayedCount is not 100% correct.
 * 如果im群消息同步机制上线后可以移除这个，通知栏同步完会话后直接用im接口的未读数，但是现在群的同步机制没有同步未读数的回调接口，所以先这样处理（推送到达+1）
 */
internal object ConvNotPlayedCountManager {
    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.Main.immediate)
    fun init() {
        scope.launch {
            IMAgent.conversationChangedFlow.collect { conversation ->
                updateNotPlayedCount(conversation)
            }
        }
        scope.launch {
            UserSessionManager.userLoginStateFlow.collect { state ->
                when (state) {
                    Empty -> {}
                    is OnLogin -> ConvNotPlayedCountManager.onLogin()
                    is OnLogout -> ConvNotPlayedCountManager.onLogout()
                }
            }
        }
    }

    fun onLogin() {
        (userLifecycleScope ?: GlobalScope).launchAsync(Dispatchers.IO) {
            val (list, error) = IMAgent.getConversationListSync(null)
            if (error == null && list != null) {
                replaceAll(list)
            }
        }
    }

    fun onLogout() {
    }

    suspend fun onReceivedFcmPush(targetId: Long, conversationType: IM5ConversationType) =
        withContext(Dispatchers.IO) {
            if (conversationType != IM5ConversationType.PRIVATE && conversationType != IM5ConversationType.GROUP) {
                return@withContext
            }
            val dao = UserDatabase.currInstance?.getConvNotPlayedCountDao() ?: return@withContext
            val savedEntity = dao.query(targetId)
            val savedCount = savedEntity?.notPlayedCount ?: 0
            val newCount = savedCount + 1
            val convType =
                if (conversationType == IM5ConversationType.GROUP) ConvNotPlayedCountEntity.CONV_TYPE_GROUP else ConvNotPlayedCountEntity.CONV_TYPE_PRIVATE
            //im推送没有时间，先用本地的，这个时间目前没有使用
            val newEntity = ConvNotPlayedCountEntity(
                targetId,
                newCount,
                convType,
                System.currentTimeMillis()
            )
            dao.insertOrUpdate(newEntity)
        }

    suspend fun getNotPlayedCount(targetId: Long): Long? {
        val dao = UserDatabase.currInstance?.getConvNotPlayedCountDao() ?: return null
        val savedEntity = dao.query(targetId)
        return savedEntity?.notPlayedCount
    }

    private fun replaceAll(list: List<IConversation>) {
        val dao = UserDatabase.currInstance?.getConvNotPlayedCountDao() ?: return
        val entityList = list.asSequence().map { conv ->
            val notPlayedCount = conv.notPlayedCount.toLong()
            val targetId = conv.targetId.toLongOrNull() ?: return@map null
            val convType = getConvType(conv.convType) ?: return@map null
            return@map ConvNotPlayedCountEntity(
                targetId,
                notPlayedCount.toLong(),
                convType,
                conv.convModifyTime
            )
        }.filterNotNull().toList()
        if (entityList.isEmpty()) {
            if (UserDatabase.currInstance.isDataBaseOpen == false) {
                return
            }
            dao.deleteAll()
        } else {
            dao.insert(entityList)
        }
    }

    private fun updateNotPlayedCount(conv: IConversation) {
        val notPlayedCount = conv.notPlayedCount.toLong()
        val targetId = conv.targetId.toLongOrNull() ?: return
        val convType = getConvType(conv.convType) ?: return
        val entity =
            ConvNotPlayedCountEntity(
                targetId,
                notPlayedCount,
                convType,
                conv.convModifyTime
            )
        UserDatabase.currInstance?.getConvNotPlayedCountDao()?.insertOrUpdate(entity)
    }

    private fun getConvType(imConvType: Int): Int? {
        return when (imConvType) {
            IM5ConversationType.PRIVATE.value -> {
                ConvNotPlayedCountEntity.CONV_TYPE_PRIVATE
            }

            IM5ConversationType.GROUP.value -> {
                ConvNotPlayedCountEntity.CONV_TYPE_GROUP
            }

            else -> {
                null
            }
        }
    }
}