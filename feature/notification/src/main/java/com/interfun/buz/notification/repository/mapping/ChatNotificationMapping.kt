package com.interfun.buz.notification.repository.mapping

import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationCompat.MessagingStyle
import androidx.core.app.NotificationCompat.MessagingStyle.Message
import androidx.core.app.Person
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.ProcessLifecycleOwner
import androidx.lifecycle.eventFlow
import coil.util.CoilUtils
import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.base.ktx.appStringContext
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.base.ktx.isAppInForeground
import com.interfun.buz.base.ktx.logError
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.base.ktx.logWarn
import com.interfun.buz.base.ktx.plus
import com.interfun.buz.base.ktx.resumedActivity
import com.interfun.buz.base.manager.retry.impl.FibonacciInterval
import com.interfun.buz.biz.center.voicemoji.repository.voiceemoji.VoiceEmojiRepository
import com.interfun.buz.common.R
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.bean.push.extra.BasePushExtra.Companion.KEY_SER_MSG_ID
import com.interfun.buz.common.bean.push.extra.BuzReactionOperateType
import com.interfun.buz.common.bean.push.extra.IMReactionType
import com.interfun.buz.common.constants.CommonMMKV
import com.interfun.buz.common.ktx.getBooleanDefault
import com.interfun.buz.common.ktx.getProviderUri
import com.interfun.buz.common.ktx.isOfficial
import com.interfun.buz.common.ktx.uid
import com.interfun.buz.common.manager.MuteInfoManager
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.manager.cache.user.UserRelationCacheManager
import com.interfun.buz.common.manager.cache.user.UserSettingManager
import com.interfun.buz.common.service.ChatService
import com.interfun.buz.common.service.RealTimeCallService
import com.interfun.buz.common.utils.NotificationChannelUtils
import com.interfun.buz.common.utils.NotificationUtil
import com.interfun.buz.common.utils.NotificationUtil.restoreMessagingStyle
import com.interfun.buz.im.IMAgent
import com.interfun.buz.im.IMErrorInfo
import com.interfun.buz.im.entity.BuzNotifyType
import com.interfun.buz.im.entity.IMType
import com.interfun.buz.im.entity.VoiceMsgReceivedStatus
import com.interfun.buz.im.entity.isAsrEditMsg
import com.interfun.buz.im.entity.isNewMsg
import com.interfun.buz.im.ktx.asrText
import com.interfun.buz.im.ktx.getConvTargetIdLong
import com.interfun.buz.im.ktx.getConversationId
import com.interfun.buz.im.ktx.isCommandMessage
import com.interfun.buz.im.ktx.isDecryptFail
import com.interfun.buz.im.ktx.isLeaveVoiceMessage
import com.interfun.buz.im.ktx.isNotifyShowCommandMsg
import com.interfun.buz.im.ktx.isReceive
import com.interfun.buz.im.ktx.isSendType
import com.interfun.buz.im.ktx.isVoiceMojiMessage
import com.interfun.buz.im.ktx.isWTVoiceMessage
import com.interfun.buz.im.ktx.localExtraModel
import com.interfun.buz.im.model.IMResult
import com.interfun.buz.notification.model.Cancel
import com.interfun.buz.notification.model.CancelReason
import com.interfun.buz.notification.model.Data
import com.interfun.buz.notification.model.Error
import com.interfun.buz.notification.model.ErrorReason
import com.interfun.buz.notification.model.NotificationHandleResult
import com.interfun.buz.notification.model.NotificationLogModel
import com.interfun.buz.notification.model.NotificationModel
import com.interfun.buz.notification.model.PushFrom
import com.interfun.buz.notification.repository.ConvNotPlayedCountManager
import com.interfun.buz.notification.utils.NotificationUtils
import com.interfun.buz.notification.utils.getNotificationText
import com.interfun.buz.onair.standard.IGlobalOnAirController
import com.interfun.buz.push.model.FCMPushData
import com.interfun.buz.push.model.PushIMInfo
import com.interfun.buz.push.model.PushPayloadType
import com.lizhi.im5.sdk.base.ReactionOperation
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import com.lizhi.im5.sdk.group.UnreadData
import com.lizhi.im5.sdk.message.IM5Message
import com.lizhi.im5.sdk.message.IMessage
import com.lizhi.im5.sdk.message.MsgDirection
import com.lizhi.im5.sdk.message.model.IM5ImageMessage
import com.lizhi.im5.sdk.message.model.IM5VoiceMessage
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.ensureActive
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.produceIn
import kotlinx.coroutines.selects.select
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import java.util.LinkedList
import kotlin.collections.set
import kotlin.coroutines.coroutineContext

internal class ChatNotificationMapping(
    private val imRepository: IMAgent = IMAgent,
    private val notPlayedCountManager: ConvNotPlayedCountManager = ConvNotPlayedCountManager,
    private val emojiRepository: VoiceEmojiRepository = VoiceEmojiRepository,
) : FcmNotificationMapping, IMNotificationMapping {

    companion object {
        private const val TAG = "ChatNotificationMapping"
    }

    private val foregroundCancelFlow = ProcessLifecycleOwner.get().lifecycle.eventFlow.map {
        it.targetState.isAtLeast(
            Lifecycle.State.STARTED
        )
    }.filter { it }.map {
        Cancel(CancelReason.CHAT_APP_IN_FOREGROUND)
    }

    override suspend fun mapToNotificationModel(fcmPushData: FCMPushData): NotificationHandleResult {
        onPushMessageReceived(fcmPushData)
        imRepository.connect()
        return handleFcmPush(fcmPushData)
    }

    override suspend fun mapToNotificationModel(
        msg: IMessage,
        type: BuzNotifyType
    ): NotificationHandleResult {
        return handleImMessageReceived(msg, type)
    }

    private suspend fun handleFcmPush(
        fcmPushData: FCMPushData
    ): NotificationHandleResult {
        return coroutineScope {
            val isInForegroundProducer = foregroundCancelFlow.produceIn(this)
            val resultDeferred = this.async {
                createNotificationModelForFcm(fcmPushData)
            }
            val result = select<NotificationHandleResult> {
                isInForegroundProducer.onReceive {
                    it
                }
                resultDeferred.onAwait { result ->
                    return@onAwait result
                }
            }
            isInForegroundProducer.cancel()
            resultDeferred.cancel()
            return@coroutineScope result
        }
    }

    private suspend fun handleImMessageReceived(
        imMessage: IMessage,
        notifyType: BuzNotifyType
    ): NotificationHandleResult {
        val isEditType = notifyType == BuzNotifyType.ReactionMsg
                || notifyType == BuzNotifyType.RecallMsg
                || notifyType == BuzNotifyType.AsrEditMSG
                || notifyType == BuzNotifyType.EditMsg
        if (notifyType != BuzNotifyType.NewMsg && !isEditType) {
            return Cancel(CancelReason.TYPE_NOT_MATCHED)
        }
        if ((notifyType.isNewMsg || notifyType.isAsrEditMsg) && imMessage.isSendType) {
            return Cancel(CancelReason.TYPE_NOT_MATCHED)
        }
        if (notifyType == BuzNotifyType.ReactionMsg && imMessage.isReceive) {
            return Cancel(CancelReason.TYPE_NOT_MATCHED)
        }
        // 当处于画中画模式推到桌面时，isAppInForeground仍然为true，导致判断为前台，此时不显示通知，因此加多resumedActivity判断
        // 回到桌面，进入画中画模式，activity会进入onPause状态，此时resumedActivity为null，但是仍然需要展示通知
        if (isAppInForeground && resumedActivity != null) {
            return Cancel(CancelReason.CHAT_APP_IN_FOREGROUND)
        }
        val isVoiceMsg =
            imMessage.isLeaveVoiceMessage || imMessage.isWTVoiceMessage || imMessage.isVoiceMojiMessage
        if (notifyType.isNewMsg && isVoiceMsg) {
            val isWtOnlineStatus = routerServices<ChatService>().value?.isWTOnlineStatus() ?: false
            val isQuietModeEnable = UserSettingManager.isQuietModeEnable
            val isMute = MuteInfoManager.isMessageMuted(imMessage, TAG)
            val isOnRealTimeCall =
                routerServices<RealTimeCallService>().value?.isOnRealTimeCall() ?: false
            val isOnAir = routerServices<IGlobalOnAirController>().value?.isInOnAir() ?: false
            val isTakingVideo = routerServices<ChatService>().value?.isTakingVideo() ?: false
            val isAlbumPreviewing =
                routerServices<ChatService>().value?.isAlbumPreviewing() ?: false
            val canAutoPlay = isWtOnlineStatus
                    && isMute.not()
                    && isQuietModeEnable.not()
                    && isOnRealTimeCall.not()
                    && isOnAir.not()
                    && isTakingVideo.not()
                    && isAlbumPreviewing.not()
            if (canAutoPlay) {
                return Cancel(CancelReason.CHAT_MSG_WILL_AUTO_PLAY)
            }
        }
        if (MuteInfoManager.isMessageNotificationMuted(imMessage, TAG)) {
            return Cancel(CancelReason.CHAT_NOTIFICATION_MUTED)
        }
        if (notifyType.isAsrEditMsg && imMessage.asrText.isNullOrEmpty()) {
            return Cancel(CancelReason.CHAT_ASR_EDIT_TEXT_EMPTY)
        }
        //不符合显示条件的居中消息提前过滤掉
        if (imMessage.isCommandMessage && imMessage.isNotifyShowCommandMsg().not()){
            return Cancel(CancelReason.TYPE_NOT_MATCHED)
        }
        val servMsgId = imMessage.serMsgId?.toLongOrNull()
        val operateServMsgId = (imMessage as? IM5Message)?.operateSvrMsgId
        val currentServMsgId = if (notifyType == BuzNotifyType.NewMsg) {
            servMsgId
        } else {
            operateServMsgId?.takeIf { it != 0L } ?: servMsgId
        }
        if (isMessageDisplayed(currentServMsgId, imMessage.conversationType)) {
            return Cancel(CancelReason.CHAT_MSG_ALREADY_DISPLAYED)
        }
        if (imMessage.conversationType != IM5ConversationType.GROUP && imMessage.conversationType != IM5ConversationType.PRIVATE) {
            return Error(ErrorReason.CHAT_NOT_CHAT_PUSH_TYPE)
        }
        val isGroup = imMessage.conversationType == IM5ConversationType.GROUP
        val targetId = imMessage.getConvTargetIdLong()
        val notificationId = targetId.toInt()
        val targetPerson = NotificationUtils.createTargetPerson(imMessage)
        val reactionOpUserId = if (notifyType == BuzNotifyType.ReactionMsg) {
            imMessage.reactionOperation?.operator
        } else {
            null
        }
        val router = if (isGroup) {
            NotificationUtils.createGroupChatRouter(targetId, imMessage.serMsgId, reactionOpUserId)
        } else {
            //需要注意因为消息可能是自己发的，所以不能用message.fromId跟targetId
            NotificationUtils.createPrivateChatRouter(
                imMessage.getConversationId(),
                UserSessionManager.uid.toString(),
                imMessage.serMsgId,
                reactionOpUserId
            )
        }
        val shortcutInfo = NotificationUtils.createShortcutInfo(targetId, targetPerson, isGroup)
        val isRecallType = notifyType == BuzNotifyType.RecallMsg
        val isAsrEditType = notifyType == BuzNotifyType.AsrEditMSG
        val isReactionType = notifyType == BuzNotifyType.ReactionMsg
        if (isAsrEditType) {
            if (!isContainInExistedNotification(notificationId, imMessage.serMsgId)) {
                return Cancel(CancelReason.CHAT_RECALL_NOT_FOUND)
            }
        }
        val styleResult = if (isRecallType) {
            createRecallMsgStyleOrCancelNotification(notificationId, imMessage.serMsgId)
        } else {
            createChatStyle(
                targetId,
                imMessage.conversationType,
                targetPerson,
                PushFrom.IM_MSG,
                notificationId,
                isReactionType
            )
        }
        if (styleResult.style == null && !styleResult.dismissPending) {
            logInfo(
                TAG,
                "don't dismiss pending notification,wait app back to foreground,result:${styleResult}"
            )
            //wait app back to foreground
            foregroundCancelFlow.first()
            return styleResult.error ?: styleResult.cancel
            ?: Error(ErrorReason.CHAT_STYLE_IS_NULL)
        }
        val style = styleResult.style
            ?: return styleResult.error ?: styleResult.cancel
            ?: Error(ErrorReason.CHAT_STYLE_IS_NULL)
        val fromTargetType = if (isGroup) {
            "group"
        } else {
            if (isSenderOfficialAccount(targetId))
                "official_account"
            else
                "private"
        }
        val logModel = NotificationLogModel(
            pushType = if (isGroup) PushPayloadType.TYPE_GROUP else PushPayloadType.TYPE_PRIVATE,
            imMsgType = imMessage.msgType,
            fromTargetType = fromTargetType,
            convId = targetId.toString(),
            serMsgId = imMessage.serMsgId,
            isEditAsr = isAsrEditType
        )
        val channel = NotificationChannelUtils.getFriendMsgChannel()
        val notPlayedCount = notPlayedCountManager.getNotPlayedCount(targetId) ?: 1L
        val onlyAlertOnce = isRecallType || isAsrEditType
        val model =
            NotificationModel(
                logModel = logModel,
                notificationId = notificationId,
                pendingRouter = router,
                number = notPlayedCount.toInt(),
                channel = channel,
                shortcutInfo = shortcutInfo,
                onlyAlertOnce = onlyAlertOnce,
                style = style
            )
        return Data(model)
    }

    private suspend fun createNotificationModelForFcm(fcmPushData: FCMPushData): NotificationHandleResult {
        if (fcmPushData.payload.type != PushPayloadType.TYPE_GROUP
            && fcmPushData.payload.type != PushPayloadType.TYPE_PRIVATE
        ) {
            return Cancel(CancelReason.TYPE_NOT_MATCHED)
        }
        if (isAppInForeground) {
            return Cancel(CancelReason.CHAT_APP_IN_FOREGROUND)
        }
        val convType = if (fcmPushData.payload.type == PushPayloadType.TYPE_PRIVATE) {
            IM5ConversationType.PRIVATE
        } else {
            IM5ConversationType.GROUP
        }
        val servMsgId = fcmPushData.payload.imInfo?.svrMsgId?.toLongOrNull()
        if (isMessageDisplayed(servMsgId, convType)) {
            return Cancel(CancelReason.CHAT_MSG_ALREADY_DISPLAYED)
        }
        val payload = fcmPushData.payload
        val type = payload.type
        if (type != PushPayloadType.TYPE_PRIVATE && type != PushPayloadType.TYPE_GROUP) {
            return Error(ErrorReason.CHAT_NOT_CHAT_PUSH_TYPE)
        }
        return coroutineScope {
            val senderUser = payload.senderUserInfo
            val convId = if (type == PushPayloadType.TYPE_PRIVATE) {
                payload.senderUserInfo?.userId
                    ?: return@coroutineScope Error(ErrorReason.CHAT_USER_ID_IS_NULL)
            } else {
                payload.groupInfo?.groupId
                    ?: return@coroutineScope Error(ErrorReason.CHAT_GROUP_ID_IS_NULL)
            }
            val imConvType = if (type == PushPayloadType.TYPE_PRIVATE) {
                IM5ConversationType.PRIVATE
            } else {
                IM5ConversationType.GROUP
            }
            val isGroup = type == PushPayloadType.TYPE_GROUP
            val person = if (isGroup) {
                NotificationUtils.createPersonGroup(
                    convId,
                    payload.groupInfo?.portrait,
                    payload.groupInfo?.name
                )
            } else {
                NotificationUtils.createPersonUser(convId, senderUser?.portrait, senderUser?.name)
            }
            val isRecallType = payload.imInfo?.msgType == IMType.TYPE_RECALL_MSG
            val isQRType = payload.imInfo?.msgType == IMType.TYPE_MSG_OPERATION_QR
            val isAsrEditType = payload.imInfo?.msgActionType == PushIMInfo.IM_MSG_ACTION_ASR
            val originSerMsgId =
                payload.imInfo?.orgSvrMsgId?.takeIf { it != 0L } ?: servMsgId
            val router = NotificationUtils.recreateChatRouter(
                convId,
                isGroup,
                payload.router?.toString(),
                originSerMsgId?.toString(),
                payload.pushExtra?.reactionOpUserId
            )
            val notificationId = convId.toInt()
            val shortcutInfo =
                NotificationUtils.createShortcutInfo(convId, person, isGroup)
            if (isAsrEditType) {
                if (!isContainInExistedNotification(
                        notificationId,
                        originSerMsgId?.toString() ?: ""
                    )
                ) {
                    return@coroutineScope Cancel(CancelReason.CHAT_RECALL_NOT_FOUND)
                }
            }
            val styleResult = if (isRecallType) {
                val orgSvrMsgId = payload.imInfo?.orgSvrMsgId?.toString()
                createRecallMsgStyleOrCancelNotification(notificationId, orgSvrMsgId)
            } else {
                createChatStyle(convId, imConvType, person, PushFrom.FCM, notificationId, isQRType)
            }
            if (styleResult.style == null && !styleResult.dismissPending) {
                logInfo(
                    TAG,
                    "don't dismiss pending notification,wait app back to foreground,result:${styleResult}"
                )
                //wait app back to foreground
                foregroundCancelFlow.first()
                return@coroutineScope styleResult.error ?: styleResult.cancel
                ?: Error(ErrorReason.CHAT_STYLE_IS_NULL)
            }
            val style = styleResult.style
                ?: return@coroutineScope styleResult.error ?: styleResult.cancel
                ?: Error(ErrorReason.CHAT_STYLE_IS_NULL)
            val fromTargetType = when (type) {
                PushPayloadType.TYPE_PRIVATE -> {
                    if (isSenderOfficialAccount(payload.senderUserInfo?.userId))
                        "official_account"
                    else
                        "private"
                }

                PushPayloadType.TYPE_GROUP -> "group"
                else -> ""
            }
            val logModel = NotificationLogModel(
                pushType = type,
                imMsgType = payload.imInfo?.msgType,
                fromTargetType = fromTargetType,
                convId = convId.toString(),
                serMsgId = payload.imInfo?.svrMsgId,
                isEditAsr = isAsrEditType
            )
            val channel = NotificationChannelUtils.getFriendMsgChannel()
            val notPlayedCount = notPlayedCountManager.getNotPlayedCount(convId) ?: 1L
            val onlyAlertOnce = isRecallType || isAsrEditType
            val model =
                NotificationModel(
                    logModel = logModel,
                    notificationId = notificationId,
                    pendingRouter = router,
                    number = notPlayedCount.toInt(),
                    channel = channel,
                    onlyAlertOnce = onlyAlertOnce,
                    shortcutInfo = shortcutInfo,
                    pushPlatformBean = fcmPushData.pushPlatformBean,
                    style = style
                )
            return@coroutineScope Data(model)
        }
    }

    private fun isContainInExistedNotification(notificationId: Int, serMsgId: String): Boolean {
        val existStyle = restoreMessagingStyle(notificationId) ?: return false
        val result = existStyle.messages.firstOrNull { message ->
            message.extras.getString(KEY_SER_MSG_ID) == serMsgId
        }
        return result != null
    }

    private fun createRecallMsgStyleOrCancelNotification(
        notificationId: Int,
        serMsgId: String?
    ): CreateStyleResult {
        serMsgId ?: return CreateStyleResult(
            null,
            cancel = Cancel(CancelReason.CHAT_RECALL_REPLACE_FAILED)
        )
        val existStyle = restoreMessagingStyle(notificationId)
        if (existStyle == null) {
            return CreateStyleResult(
                null,
                cancel = Cancel(CancelReason.CHAT_RECALL_REPLACE_FAILED)
            )
        } else {
            var foundIndex: Int? = null
            existStyle.messages.forEachIndexed { index, notificationMessage ->
                val existStyleMessageSerMsgId = notificationMessage.extras.getString(KEY_SER_MSG_ID)
                if (serMsgId == existStyleMessageSerMsgId) {
                    foundIndex = index
                    return@forEachIndexed
                }
            }
            val index = foundIndex ?: return CreateStyleResult(
                null,
                cancel = Cancel(CancelReason.CHAT_RECALL_REPLACE_FAILED)
            )
            existStyle.messages.removeAt(index)
            val size = existStyle.messages.size
            logWarn(TAG, "createRecallMsgStyle,size:${size}")
            return if (size == 0) {
                NotificationUtil.cancelNotification(appContext, notificationId)
                CreateStyleResult(
                    null,
                    cancel = Cancel(CancelReason.CHAT_RECALL_REPLACE_FAILED)
                )
            } else {
                CreateStyleResult(existStyle)
            }
        }
    }

    private suspend fun isSenderOfficialAccount(uid: Long?): Boolean {
        uid ?: return false
        val userInfo = UserRelationCacheManager.getUserRelationInfoFromCacheSync(uid)
        return userInfo?.isOfficial.getBooleanDefault()
    }

    private suspend fun createChatStyle(
        targetId: Long,
        convType: IM5ConversationType,
        person: Person,
        pushFrom: PushFrom,
        notificationId : Int,
        isFromQR : Boolean
    ): CreateStyleResult {
        logInfo(TAG, "createChatStyle:targetId:${targetId},convType:${convType},pushFrom:$pushFrom")
        if (pushFrom == PushFrom.FCM) {
            logInfo(TAG, "sync msg start")
            val interval = FibonacciInterval()
            var syncError: IMErrorInfo? = null
            var times = 0
            do {
                delay(interval.interval(++times))
                syncError = imRepository.checkPrivateSyncResult(null)
                logInfo(TAG, "sync finished,error:$syncError")
            } while (syncError != null && times <= 5)
            if (syncError != null) {
                logInfo(TAG, "sync failed with $times times.")
                return CreateStyleResult(
                    null,
                    error = Error(ErrorReason.CHAT_SYNC_MSG_FAILED),
                    dismissPending = false
                )
            }
        }
        logInfo(TAG, "loadUnreadHistoryMessage start")
        var unreadData: UnreadData? = null
        var times = 0
        val interval = FibonacciInterval()
        while (unreadData == null && times < 5) {
            coroutineContext.ensureActive()
            delay(interval.interval(++times))
            val historyResult = imRepository.loadUnreadHistoryMessage(
                convType,
                targetId.toString(),
                50
            )
            unreadData = when (historyResult) {
                is IMResult.Error -> {
                    logInfo(TAG, "loadUnreadHistoryMessage error:$historyResult")
                    null
                }

                is IMResult.Success -> historyResult.data
            }
        }
        if (unreadData == null) {
            return CreateStyleResult(
                null,
                error = Error(ErrorReason.CHAT_FETCH_MSG_ERROR),
                dismissPending = false
            )
        }
        val filteredMsgList = unreadData.messageList.filter {
            when (it.msgType) {
                IMType.TYPE_TOAST, IMType.TYPE_DIALOG -> false

                IMType.TYPE_COMMAND -> {
                    it.isNotifyShowCommandMsg()
                }

                IMType.TYPE_RECALL_MSG -> false
                else -> true
            } && (it.content !is IM5VoiceMessage || !isVoiceMsgListened(it)) && it.messageDirection == MsgDirection.RECEIVE
        }
        val qrList = createQrList(unreadData.operationList)
        val combinedList = combineMsgAndQrListLatest10(filteredMsgList, qrList)
        logInfo(
            TAG, "filteredListSize:${filteredMsgList.size}"
        )
        val messageList = getMessageList(combinedList)
        if (messageList.isEmpty()) {
            logError(TAG, "message list is empty.")
            if (isFromQR){
                NotificationUtil.cancelNotification(appContext, notificationId)
            }
            return CreateStyleResult(
                null,
                cancel = Cancel(CancelReason.CHAT_MSG_EMPTY)
            )
        }
        val messagingStyle = NotificationCompat.MessagingStyle(person)
            .setGroupConversation(convType == IM5ConversationType.GROUP)
        for (message in messageList) {
            messagingStyle.addMessage(message)
        }
        logInfo(TAG, "return messageStyle,targetId:$targetId,size:${messageList.size}")
        return CreateStyleResult(messagingStyle)
    }

    private fun combineMsgAndQrListLatest10(
        msgList: List<IMessage>, qrList: List<ReactionOperation>
    ): List<Any> {
        val result = msgList + qrList
        return result?.sortedBy { item ->
            when (item) {
                is IMessage -> item.createTime
                is ReactionOperation -> item.createTime
                else -> 0L
            }
        }?.takeLast(10) ?: emptyList()
    }

    private fun createQrList(originList: List<ReactionOperation>): List<ReactionOperation> {
        //需要倒序,从后往前看操作，如果后面的操作已经取消了，那么前面的操作需要忽略
        val sortedList = originList.sortedByDescending { it.createTime }
        //正序结果列表
        val result = ArrayList<ReactionOperation>(originList.size)
        val cancelSet = hashSetOf<Pair<Long, String>>()
        //去除点了表情又取消的操作
        for (operation in sortedList) {
            when (operation.type) {
                BuzReactionOperateType.REPLACE.value -> {
                    val setItem = Pair(operation.orgSvrMsgId, operation.operator)
                    if (!cancelSet.contains(setItem)) {
                        result.add(result.size, operation)
                    }
                    cancelSet.add(setItem)
                }

                BuzReactionOperateType.ADD.value -> {
                    val setItem = Pair(operation.orgSvrMsgId, operation.operator)
                    if (!cancelSet.contains(setItem)) {
                        result.add(result.size, operation)
                    }
                }

                BuzReactionOperateType.REMOVE.value -> {
                    cancelSet.add(Pair(operation.orgSvrMsgId, operation.operator))
                }
            }
        }
        return result
    }

    /**
     * voiceMsgReceivedStatus:see class VoiceMsgReceivedStatus
     * warning: be sure to call this method when the msg is voice msg
     */
    private fun isVoiceMsgListened(msg: IMessage): Boolean {
        if (msg.content !is IM5VoiceMessage) {
            return false
        }
        return  msg.localExtraModel().voiceMsgReceivedStatus == VoiceMsgReceivedStatus.LISTENED.value
    }


    private suspend fun getMessageList(
        sortedList: List<Any>
    ): List<Message> {
        if (sortedList.isEmpty()) {
            return emptyList()
        }
        val personMap = hashMapOf<String, Person>()
        val messageList = LinkedList<Message>()
        for ((index, item) in sortedList.withIndex()) {
            if (item is IMessage) {
                val imMessage = item
                val person = personMap[imMessage.userInfo.userId] ?: kotlin.run {
                    val person = NotificationUtils.createSendInfoPerson(imMessage)
                    personMap[imMessage.fromId] = person
                    return@run person
                }
                val notificationMessage = Message(
                    getNotificationText(imMessage), imMessage.createTime, person
                ).apply { extras.putString(KEY_SER_MSG_ID, imMessage.serMsgId) }
                //只有第一个是图片才展示,为了避免图片占用空间大，上面的内容无法显示
                if (index == 0 && imMessage.msgType == IMType.TYPE_IMAGE_MSG && !imMessage.isDecryptFail) {
                    val content = imMessage.content as IM5ImageMessage
                    if (content.imageWidth != 0 && content.imageHeight != 0) {
                        content.remoteUrl?.let { picUrl ->
                            NotificationUtils.requestBitmap(
                                picUrl,
                                content.imageWidth,
                                content.imageHeight,
                                allowHardware = true
                            )
                            CoilUtils.getProviderUri(picUrl)?.let {
                                notificationMessage.setData("image/*", it)
                            }
                        }
                    }
                }
                messageList.add(notificationMessage)
            } else if (item is ReactionOperation) {
                val uidStr = item.operator
                val uidLong = uidStr.toLongOrNull() ?: continue
                val person = personMap[uidStr] ?: kotlin.run {
                    val person = NotificationUtils.createPersonUser(uidLong)
                    personMap[uidStr] = person
                    return@run person
                }
                val voicemojiId = item.reactionId.toLongOrNull()
                val content = if (voicemojiId != null &&
                    item.reactionType == IMReactionType.QUICK_REACT.value
                ) {
                    val emoji = emojiRepository.getVoiceEmojiById(voicemojiId).firstOrNull()
                    if (emoji != null) {
                        appStringContext.getString(R.string.qr_notify_tip, emoji.emojiIcon)
                    } else {
                        appStringContext.getString(R.string.qr_notify_tip, "")
                    }
                } else {
                    R.string.chat_pop_msg_unsupported_tag.asString()
                }
                val notificationMessage = Message(content, item.createTime, person)
                messageList.add(notificationMessage)
            }
        }
        return messageList
    }

    private val imDisplayedMutex = Mutex()
    private suspend fun isMessageDisplayed(
        im5SvrMsgId: Long?,
        convType: IM5ConversationType?
    ): Boolean {
        imDisplayedMutex.withLock {
            if (im5SvrMsgId != null && im5SvrMsgId > 0 && convType != null) {
                val isDisplayed = imRepository.isMessageDisplayed(im5SvrMsgId, convType)
                logInfo(
                    TAG,
                    "isMessageDisplayed im5SvrMsgId:$im5SvrMsgId convType:$convType isDisplayed:$isDisplayed"
                )
                return isDisplayed
            }
            return false
        }
    }

    private fun onPushMessageReceived(fcmPushData: FCMPushData): Boolean {
        if (fcmPushData.payload.type != PushPayloadType.TYPE_PRIVATE && fcmPushData.payload.type != PushPayloadType.TYPE_GROUP) {
            return false
        }
        val convTypeEnum = if (fcmPushData.payload.type == PushPayloadType.TYPE_PRIVATE) {
            IM5ConversationType.PRIVATE
        } else {
            IM5ConversationType.GROUP
        }
        val im5SvrMsgId = fcmPushData.payload.imInfo?.svrMsgId ?: ""
        val convType = convTypeEnum.value
        if (im5SvrMsgId.isNotEmpty() && convType != null) {
            val imConversationType = if (convType == IM5ConversationType.GROUP.value) {
                IM5ConversationType.GROUP
            } else {
                IM5ConversationType.PRIVATE
            }
            im5SvrMsgId.toLongOrNull()?.let {
                return IMAgent.onPushMessageReceived(
                    it,
                    imConversationType
                )
            }
        }
        logInfo(TAG, "onPushMessageReceived is filtered, im5SvrMsgId is empty")
        return false
    }

    private class CreateStyleResult(
        val style: MessagingStyle?,
        val cancel: Cancel? = null,
        val error: Error? = null,
        val dismissPending: Boolean = true
    )
}

