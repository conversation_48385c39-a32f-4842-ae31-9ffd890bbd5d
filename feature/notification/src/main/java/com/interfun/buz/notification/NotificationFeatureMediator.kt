package com.interfun.buz.notification

import com.interfun.buz.common.manager.Empty
import com.interfun.buz.common.manager.OnLogin
import com.interfun.buz.common.manager.OnLogout
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.notification.repository.ConvNotPlayedCountManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch

object NotificationFeatureMediator {
    fun init() {
        ConvNotPlayedCountManager.init()
        NotificationManager.init()
    }
}