package com.interfun.buz.notification.repository.mapping

import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.base.ktx.toIntLower32Bits
import com.interfun.buz.common.utils.NotificationUtil
import com.interfun.buz.notification.model.Cancel
import com.interfun.buz.notification.model.CancelReason
import com.interfun.buz.notification.model.NotificationHandleResult
import com.interfun.buz.push.model.CallRevokeExtra
import com.interfun.buz.push.model.FCMPushData

class LivePlaceInvitationRevokeMapping: FcmNotificationMapping {

    companion object{
        const val TAG = "LPCallRevokeMapping"
    }

    override suspend fun mapToNotificationModel(fcmPushData: FCMPushData): NotificationHandleResult {
        val extra: CallRevokeExtra = fcmPushData.payload.pushExtra ?: return Cancel(CancelReason.PUSH_EXTRA_NULL)
        val channelId = extra.channelId
        val notifyId = channelId.toIntLower32Bits()
        logInfo(TAG, "Receive push notification and cancel live place invitation, channelId = $channelId, cancel notificationId = $notifyId")
        NotificationUtil.cancelNotification(appContext, notifyId)
        return Cancel(CancelReason.CALL_REVOKE)
    }
}