package com.interfun.buz.notification.usecase

import android.os.Build.VERSION
import android.os.Build.VERSION_CODES
import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.base.ktx.appStringContext
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.base.ktx.toast
import com.interfun.buz.common.ktx.getDisplayName
import com.interfun.buz.im.ktx.isDeleteBool
import com.interfun.buz.common.ktx.uid
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.manager.cache.group.GroupInfoCacheManager
import com.interfun.buz.common.manager.cache.user.UserRelationCacheManager
import com.interfun.buz.common.manager.router.RouterCreator
import com.interfun.buz.common.utils.NotificationChannelUtils
import com.interfun.buz.feature.notification.R
import com.interfun.buz.im.IMAgent
import com.interfun.buz.im.entity.IMSendStateEvent.OnError
import com.interfun.buz.im.entity.NoVoiceFilterCode
import com.interfun.buz.im.entity.VoiceFilterAIErrorType
import com.interfun.buz.im.entity.VoiceFilterUserCancel
import com.interfun.buz.im.ktx.BuzSendingState.Failed
import com.interfun.buz.im.ktx.buzSendingState
import com.interfun.buz.notification.model.NotificationModel
import com.lizhi.im5.sdk.conversation.IM5ConversationType.GROUP
import com.lizhi.im5.sdk.conversation.IM5ConversationType.PRIVATE
import com.lizhi.im5.sdk.message.IMessage
import com.lizhi.im5.sdk.message.MsgDirection.SEND
import com.lizhi.im5.sdk.utils.IM5MsgUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map

class SendErrorNotificationUseCase(private val imRepository: IMAgent) {
    operator fun invoke(): Flow<NotificationModel> =
        imRepository.msgSendStateFlow.map { imSendState ->
            if (imSendState is OnError && !imSendState.msg.isDeleteBool) {
                if (imSendState.errorType == VoiceFilterAIErrorType) {
                    if (imSendState.errorCode == NoVoiceFilterCode) {
                        R.string.voice_flter_expired.toast()
                    }
                    if (imSendState.errorCode == VoiceFilterUserCancel) {
                        return@map null
                    }
                }
                return@map if (imSendState.msg.buzSendingState == Failed) {
                    imSendState.msg
                } else {
                    null
                }
            } else {
                return@map null
            }
        }.filterNotNull().map { msg ->
            if (msg.messageDirection != SEND) {
                return@map null
            }
            if (msg.conversationType != PRIVATE && msg.conversationType != GROUP) {
                return@map null
            }
            val convId = IM5MsgUtils.getConvTargetId(msg).toLongOrNull() ?: return@map null
            val name = if (msg.conversationType == PRIVATE) {
                val user = UserRelationCacheManager.getUserRelationInfoByUidSync(convId)
                user?.getDisplayName() ?: convId
            } else {
                val group = GroupInfoCacheManager.getGroupInfoBeanByIdSync(convId)
                group?.groupName ?: convId
            }
            val channel = if (VERSION.SDK_INT >= VERSION_CODES.O) {
                NotificationChannelUtils.getFriendMsgChannel()
            } else {
                null
            }
            val router = if (msg.conversationType == PRIVATE) {
                RouterCreator.createPrivateChatRouter(
                    convId.toString(),
                    UserSessionManager.uid.toString(),
                    msgId = msg.msgId
                )
            } else {
                RouterCreator.createGroupChatRouter(
                    convId,
                    msgId = msg.msgId
                )
            }
            val content =
                appStringContext.getString(
                    com.interfun.buz.common.R.string.resend_you_message_no_send,
                    name
                )
            val title = R.string.app_name.asString()
            val model = NotificationModel(
                logModel = null,
                notificationId = createFailedMsgNotifyId(msg),
                pendingRouter = router.toString(),
                number = 1,
                content = content,
                title = title,
                channel = channel
            )
            return@map model
        }.flowOn(Dispatchers.IO).filterNotNull()

    private fun createFailedMsgNotifyId(msg: IMessage): Int {
        val id = IM5MsgUtils.getConvTargetId(msg).toLongOrNull() ?: msg.msgId
        //后16位异或
        val xorId = id.toInt().xor(0x0000FFFF)
        return xorId
    }
}