package com.interfun.buz.notification.repository.mapping

import com.interfun.buz.base.ktx.log
import com.interfun.buz.common.ktx.generateNotificationId
import com.interfun.buz.common.manager.FeedbackManager
import com.interfun.buz.common.utils.NotificationChannelUtils
import com.interfun.buz.notification.ktx.defaultContent
import com.interfun.buz.notification.ktx.defaultTitle
import com.interfun.buz.notification.model.Data
import com.interfun.buz.notification.model.NotificationHandleResult
import com.interfun.buz.notification.model.NotificationLogModel
import com.interfun.buz.notification.model.NotificationModel
import com.interfun.buz.push.model.FCMPushData
import com.interfun.buz.push.model.PushPayloadType
import org.json.JSONObject

internal class DefaultFcmNotificationMapping :
    FcmNotificationMapping {
    companion object {
        const val TAG = "DefaultFcmNotificationMapping"
    }

    override suspend fun mapToNotificationModel(fcmPushData: FCMPushData): NotificationHandleResult {
        log(TAG, "mapToNotificationModel:type:${fcmPushData.payload.type}")
        val title = fcmPushData.defaultTitle()
        val content = fcmPushData.defaultContent()
        val payload = fcmPushData.payload
        val logModel = NotificationLogModel(
            pushType = payload.type
        )
        val type = payload.type
        val notificationId = generateNotificationId()
        if (type == PushPayloadType.TYPE_FEEDBACK_REPLY_NOTICE) {
            FeedbackManager.addFeedbackNotificationId(notificationId)
        }
        val model = NotificationModel(
            logModel = logModel,
            notificationId = generateNotificationId(),
            pendingRouter = fcmPushData.payload.router?.toString() ?: JSONObject().toString(),
            channel = getChannel(payload.type),
            number = 0,
            title = title,
            content = content,
            pushPlatformBean = fcmPushData.pushPlatformBean
        )
        return Data(model)
    }

    private fun getChannel(type: Int) = when (type) {
        PushPayloadType.TYPE_CONTACT_REGISTERED_NOTICE,
        PushPayloadType.TYPE_UN_HANDLE_FRIEND_REQUEST,
        PushPayloadType.TYPE_UN_FRIEND_CONTACT,
        PushPayloadType.TYPE_CONTACT_NO_REGISTER,
        PushPayloadType.TYPE_UN_HANDLE_FRIEND_REQUEST_AFTER_ADD,
        PushPayloadType.TYPE_UN_FRIEND_CONTACT_AFTER_ADD,
        PushPayloadType.TYPE_CONTACT_NO_REGISTER_AFTER_ADD,
        PushPayloadType.TYPE_UN_HANDLE_FRIEND_REQUEST_AFTER_LOSS,
        PushPayloadType.TYPE_UN_FRIEND_CONTACT_AFTER_LOSS,
        PushPayloadType.TYPE_CONTACT_NO_REGISTER_AFTER_LOSS,
        PushPayloadType.TYPE_NEW_CONTACT_REGISTERED,
        PushPayloadType.TYPE_NEW_CONTACT_RECOMMEND ->
            NotificationChannelUtils.getFriendSuggestionNotificationChannel()

        else -> NotificationChannelUtils.getSystemNotificationChannel()
    }
}