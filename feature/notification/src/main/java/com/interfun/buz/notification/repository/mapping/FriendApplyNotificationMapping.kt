package com.interfun.buz.notification.repository.mapping

import androidx.core.app.NotificationCompat.MessagingStyle
import androidx.core.app.NotificationCompat.MessagingStyle.Message
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.common.utils.NotificationChannelUtils
import com.interfun.buz.notification.model.Cancel
import com.interfun.buz.notification.model.CancelReason
import com.interfun.buz.notification.model.Data
import com.interfun.buz.notification.model.NotificationHandleResult
import com.interfun.buz.notification.model.NotificationLogModel
import com.interfun.buz.notification.model.NotificationModel
import com.interfun.buz.notification.utils.NotificationUtils
import com.interfun.buz.push.model.FCMPushData
import com.lizhi.component.basetool.ntp.NtpTime
import org.json.JSONObject

class FriendApplyNotificationMapping : FcmNotificationMapping {
    companion object {
        private const val TAG = "FriendApplyNotificationMapping"
    }

    override suspend fun mapToNotificationModel(fcmPushData: FCMPushData): NotificationHandleResult {
        logInfo(TAG, "mapToNotificationModel :$fcmPushData")
        val payload = fcmPushData.payload
        val userInfo =
            payload.senderUserInfo ?: return Cancel(CancelReason.PARAMETER_ERROR)
        val person = NotificationUtils.createPersonUser(
            userInfo.userId,
            defaultPortrait = userInfo.portrait,
            defaultName = userInfo.name
        )
        val shortcutInfo = NotificationUtils.createShortcutInfo(userInfo.userId, person, false)
        val messageStyle = MessagingStyle(person).setGroupConversation(false)
        messageStyle.addMessage(Message(fcmPushData.body, NtpTime.nowForce(), person))
        val logModel = NotificationLogModel(
            pushType = fcmPushData.payload.type
        )
        val channel = NotificationChannelUtils.getFriendRequestChannel()
        val model =
            NotificationModel(
                logModel = logModel,
                notificationId = NotificationUtils.generateRandomNotificationId(),
                pendingRouter = payload.router?.toString() ?: JSONObject().toString(),
                number = 1,
                channel = channel,
                shortcutInfo = shortcutInfo,
                style = messageStyle,
                pushPlatformBean = fcmPushData.pushPlatformBean
            )
        return Data(model)
    }
}