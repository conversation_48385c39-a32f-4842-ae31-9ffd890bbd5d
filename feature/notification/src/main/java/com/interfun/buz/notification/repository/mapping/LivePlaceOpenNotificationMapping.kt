package com.interfun.buz.notification.repository.mapping

import android.app.Notification
import android.app.NotificationChannel
import androidx.core.app.NotificationCompat
import com.interfun.buz.common.ktx.generateNotificationId
import com.interfun.buz.common.utils.NotificationChannelUtils
import com.interfun.buz.notification.model.Data
import com.interfun.buz.notification.model.NotificationHandleResult
import com.interfun.buz.notification.model.NotificationLogModel
import com.interfun.buz.notification.model.NotificationModel
import com.interfun.buz.notification.utils.LivePlaceNotifyUtils
import com.interfun.buz.push.model.FCMPushData

/**
 * type=34和35 个人/群组开启LivePlace通知
 */
class LivePlaceOpenNotificationMapping: FcmNotificationMapping {

    override suspend fun mapToNotificationModel(fcmPushData: FCMPushData): NotificationHandleResult {
        val payload = fcmPushData.payload
        val notificationId = payload.pushExtra?.channelId?.hashCode()?: generateNotificationId()
        val chatStyle = LivePlaceNotifyUtils.createOpenLpChatStyle(fcmPushData)
        val logModel = NotificationLogModel(pushType = payload.type)
        val notificationChannel = NotificationChannelUtils.getLivePlaceOpenMsgChannel()
        val router = payload.router.toString()
        return createNotificationModel(logModel, notificationId, notificationChannel, chatStyle, router)
    }

    private fun createNotificationModel(
        logModel: NotificationLogModel,
        notificationId: Int,
        notificationChannel: NotificationChannel?,
        style: NotificationCompat.Style,
        router: String?= null
    ): NotificationHandleResult {
        val model = NotificationModel(
            logModel = logModel,
            notificationId = notificationId,
            pendingRouter = router,
            number = 1,
            channel = notificationChannel,
            category = Notification.CATEGORY_MESSAGE,
            visibility = NotificationCompat.VISIBILITY_PUBLIC,
            style = style
        )
        return Data(model)
    }
}