package com.interfun.buz.notification.storage

object NotificationMemStorage {
    private val voiceCallRevokeChannelSet = mutableSetOf<Long>()

    @Synchronized
    fun addVoiceCallRevokeChannel(channelId: Long) {
        voiceCallRevokeChannelSet.add(channelId)
    }

    @Synchronized
    fun removeVoiceCallRevokeChannel(channelId: Long) : <PERSON><PERSON>an {
        return voiceCallRevokeChannelSet.remove(channelId)
    }
}