package com.interfun.buz.notification.model

import android.app.Notification
import android.app.NotificationChannel
import android.app.PendingIntent
import android.graphics.Bitmap
import androidx.core.app.NotificationCompat
import androidx.core.content.pm.ShortcutInfoCompat
import com.interfun.buz.push.model.PushPlatformBean

sealed interface INotificationModel

class NotificationModel(
    val logModel: NotificationLogModel?,
    val notificationId: Int,
    val pendingRouter: String?,
    val number: Int,
    val channel: NotificationChannel?,
    val title: String? = null,
    val content: String? = null,
    val largeIcon: Bitmap? = null,
    val timeout: Long? = null,
    val shortcutInfo: ShortcutInfoCompat? = null,
    val isOngoing: Boolean? = null,
    val style: NotificationCompat.Style? = null,
    val onlyAlertOnce: Boolean = false,
    val fullscreenPendingIntent: PendingIntent? = null,
    val fullScreenHighPriority: Boolean = true,
    val category: String? = null,
    @NotificationCompat.NotificationVisibility val visibility: Int? = null,
    val deleteIntent: PendingIntent? = null,
    val pushPlatformBean: PushPlatformBean? = null,
    val showCompleteHandler: (Throwable?) -> Unit = {},
    val notificationFlags: Int? = null
) : INotificationModel {
    override fun toString(): String {
        return "NotificationModel [notificationId:$notificationId,number:$number,title:${title},content:${content},style:${style},onlyAlertOnce:${onlyAlertOnce},isFullscreen:${fullscreenPendingIntent != null},pendingRouter:$pendingRouter]"
    }
}

data object ShowPending : INotificationModel
data object HidePending : INotificationModel

sealed interface NotificationHandleResult
class Cancel(val reason: CancelReason) : NotificationHandleResult
class Data(val data: NotificationModel) : NotificationHandleResult
class Error(val reason: ErrorReason) : NotificationHandleResult

enum class CancelReason(val logEnable: Boolean = false) {
    TYPE_NOT_MATCHED,
    PUSH_EXTRA_NULL,
    PUSH_SENDER_INFO_NULL,
    PARAMETER_ERROR(true),
    CHAT_TIMEOUT,
    CHAT_APP_IN_FOREGROUND,
    CHAT_MSG_ALREADY_DISPLAYED,
    CHAT_NOTIFICATION_MUTED(true),
    CHAT_ASR_EDIT_TEXT_EMPTY,
    CHAT_MSG_WILL_AUTO_PLAY,
    CHAT_ASR_TEXT_EMPTY,
    CHAT_ASR_UPDATE_NOT_FOUND,
    CHAT_RECALL_NOT_FOUND,
    CHAT_MSG_EMPTY_AFTER_RECALL,
    CHAT_MSG_EMPTY,
    CHAT_RECALL_REPLACE_FAILED,
    CALL_REVOKE(true),
    CALL_ALREADY_IN_CALL(true),
    CALL_ALREADY_REVOKE(true),
    CALL_IS_SHOWING_UPDATE_DIALOG,
}

enum class ErrorReason {
    CHAT_NOT_CHAT_PUSH_TYPE,
    CHAT_STYLE_IS_NULL,
    CHAT_USER_ID_IS_NULL,
    CHAT_GROUP_ID_IS_NULL,
    CHAT_SYNC_MSG_FAILED,
    CHAT_FETCH_MSG_ERROR,
}

enum class PendingOperation {
    ADD,
    REDUCE
}

