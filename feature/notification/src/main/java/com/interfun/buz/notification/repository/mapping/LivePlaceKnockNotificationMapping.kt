package com.interfun.buz.notification.repository.mapping

import android.app.Notification
import android.app.NotificationChannel
import androidx.core.app.NotificationCompat
import com.interfun.buz.common.ktx.generateNotificationId
import com.interfun.buz.common.utils.NotificationChannelUtils
import com.interfun.buz.notification.model.Data
import com.interfun.buz.notification.model.NotificationHandleResult
import com.interfun.buz.notification.model.NotificationLogModel
import com.interfun.buz.notification.model.NotificationModel
import com.interfun.buz.notification.utils.LivePlaceNotifyUtils
import com.interfun.buz.push.model.FCMPushData

/**
 * live place knock knock notification mapping
 */
class LivePlaceKnockNotificationMapping: FcmNotificationMapping {

    override suspend fun mapToNotificationModel(fcmPushData: FCMPushData): NotificationHandleResult {
        val payload = fcmPushData.payload
        val chatStyle = LivePlaceNotifyUtils.createKonckMsgStyle(fcmPushData)
        val logModel = NotificationLogModel(pushType = payload.type)
        val notificationChannel = NotificationChannelUtils.getLivePlaceMsgChannel()
        val router = payload.router.toString()
        return createNotificationModel(logModel, notificationChannel, chatStyle, router)
    }

    private fun createNotificationModel(
        logModel: NotificationLogModel,
        notificationChannel: NotificationChannel?,
        style: NotificationCompat.Style,
        router: String?= null
    ): NotificationHandleResult {
        val notificationId = generateNotificationId()
        val model = NotificationModel(
            logModel = logModel,
            notificationId = notificationId,
            pendingRouter = router,
            number = 1,
            channel = notificationChannel,
            category = Notification.CATEGORY_MESSAGE,
            visibility = NotificationCompat.VISIBILITY_PUBLIC,
            style = style
        )
        return Data(model)
    }
}