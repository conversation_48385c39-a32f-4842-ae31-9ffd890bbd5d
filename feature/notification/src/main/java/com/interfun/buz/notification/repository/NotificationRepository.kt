package com.interfun.buz.notification.repository

import com.interfun.buz.base.ktx.LogLine
import com.interfun.buz.base.ktx.isAppInForeground
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.base.ktx.simpleName
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.manager.chat.ChannelInviteManager
import com.interfun.buz.common.manager.liveplace.LivePlaceInviteManager
import com.interfun.buz.common.service.ChatService
import com.interfun.buz.common.service.GlobalPlayingState
import com.interfun.buz.im.entity.BuzNotifyType
import com.interfun.buz.im.IMAgent
import com.interfun.buz.im.ktx.getConversationId
import com.interfun.buz.notification.model.*
import com.interfun.buz.notification.repository.mapping.*
import com.interfun.buz.notification.usecase.SendErrorNotificationUseCase
import com.interfun.buz.notification.usecase.UnreadMsgNotificationUseCase
import com.interfun.buz.push.model.FCMPushData
import com.interfun.buz.push.repository.PushRepository
import com.lizhi.im5.sdk.message.IMessage
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.channelFlow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch

class NotificationRepository(
    private val pushRepository: PushRepository,
    private val imRepository: IMAgent = IMAgent,
    private val callInvitationRepository : ChannelInviteManager = ChannelInviteManager,
    private val livePlaceInvitationRepository: LivePlaceInviteManager = LivePlaceInviteManager
) {
    companion object {
        const val TAG = "NotificationRepo"
    }

    private val autoPlayedMapping = AutoPlayedMsgNotificationMapping()
    private val chatMapping = ChatNotificationMapping()
    private val callInvitationMapping = CallInvitationNotificationMapping()
    private val livePlaceInvitationMapping = LivePlaceInvitationNotificationMapping()
    private val fcmMappingStrategy = FcmMappingStrategy(
        chatMsgMapping = chatMapping,
        callInvitationMapping = callInvitationMapping
    )
    private val imMappingStrategy = IMMappingStrategy(chatMsgMapping = chatMapping)
    private val sendErrorNotificationUseCase = SendErrorNotificationUseCase(imRepository)
    private val unreadMsgNotificationUseCase = UnreadMsgNotificationUseCase(imRepository)
    private val autoPlayedSourceFlow = channelFlow<NotificationSource.AutoPlayedMsg> {
        launch {
            routerServices<ChatService>().value?.getGlobalPlayedMsgFlow()?.collect { (msg, state) ->
                if (msg != null && state == GlobalPlayingState.PLAYING) {
                    send(
                        NotificationSource.AutoPlayedMsg(
                            msg,
                            AutoPlayedMsgType.NEW
                        )
                    )
                }
            }
        }
        launch {
            imRepository.msgReceivedFlow.collect { data ->
                if (data.type == BuzNotifyType.AsrEditMSG) {
                    data.list.forEach { msg ->
                        send(
                            NotificationSource.AutoPlayedMsg(
                                msg,
                                AutoPlayedMsgType.UPDATE_ASR
                            )
                        )
                    }
                } else if (data.type == BuzNotifyType.RecallMsg) {
                    data.list.forEach { msg ->
                        send(
                            NotificationSource.AutoPlayedMsg(
                                msg,
                                AutoPlayedMsgType.RECALL_MSG
                            )
                        )
                    }
                }
            }
        }
    }

    private val incomeSource = channelFlow<NotificationSource> {
        launch {
            pushRepository.fcmPushFlow.collect {
                send(NotificationSource.FCM(it))
            }
        }
        launch {
            imRepository.msgReceivedFlow.collect { data ->
                data.list.forEach {
                    send(NotificationSource.IM(it, data.type))
                }
            }
        }
        launch {
            callInvitationRepository.signalInvitationFlow.collect {
                send(NotificationSource.CallInvitationSignal(it))
            }
        }
        launch {
            autoPlayedSourceFlow.collect {
                send(it)
            }
        }
        launch {
            livePlaceInvitationRepository.inviteFlow.collect {
                if (isAppInForeground){
                    return@collect
                }
                send(NotificationSource.LivePlaceInvitationSignal(it))
            }
        }
    }

    val notificationFlow = channelFlow<INotificationModel> {
        val pendingOperationChannel = Channel<PendingOperation>(capacity = Channel.UNLIMITED)
        val showPendingFlow = flow<Boolean> {
            var pendingCount = 0
            while (isActive) {
                val operation = pendingOperationChannel.receive()
                logInfo(TAG, "pendingOperation:$operation,oldPendingCount:$pendingCount")
                when (operation) {
                    PendingOperation.ADD -> {
                        pendingCount++
                        if (pendingCount > 0) {
                            emit(true)
                        }
                    }

                    PendingOperation.REDUCE -> {
                        pendingCount--
                        if (pendingCount <= 0) {
                            emit(false)
                        }
                    }
                }
            }
        }
        launch {
            showPendingFlow.collect { showPending ->
                if (showPending) {
                    logInfo(TAG, "ShowPending")
                    send(ShowPending)
                } else {
                    logInfo(TAG, "HidePending")
                    send(HidePending)
                }
            }
        }
        launch {
            //消息发送失败通知（message send error notification）
            sendErrorNotificationUseCase.invoke().collect {
                send(it)
            }
        }
        launch {
            unreadMsgNotificationUseCase.invoke().collect {
                send(it)
            }
        }
        launch {
            incomeSource.collect { source ->
                val sourceType = source.simpleName
                val job = launch {
                    logInfo(TAG, "handleNotification start,sourceType:$sourceType,source:$source")
                    val result = when (source) {
                        is NotificationSource.FCM -> fcmMappingStrategy.map(source.data)
                        is NotificationSource.IM -> imMappingStrategy.map(source.msg, source.type)
                        is NotificationSource.CallInvitationSignal -> callInvitationMapping.mapToNotificationModel(source.signalData)
                        is NotificationSource.AutoPlayedMsg -> autoPlayedMapping.mapToNotificationModel(source.msg, source.type)
                        is NotificationSource.LivePlaceInvitationSignal -> {
                            livePlaceInvitationMapping.mapToNotificationModel(source.livePlaceInvite)
                        }
                    }
                    when (result) {
                        is Cancel -> {
                            if (result.reason.logEnable) {
                                logInfo(TAG, "handleNotification cancel,sourceType:${sourceType},reason:${result.reason}")
                            }
                        }

                        is Data -> {
                            logInfo(
                                TAG,
                                "handleNotification success,sourceType:${sourceType},data:${result.data}",
                                logLine = LogLine.PUSH
                            )
                            send(result.data)
                        }

                        is Error -> logInfo(
                            TAG,
                            "handleNotification error,sourceType:${sourceType},reason:${result.reason}"
                        )
                    }
                }
                launch {
                    delay(3000)
                    if (job.isActive) {
                        logInfo(TAG, "handleNotification send pending ADD")
                        pendingOperationChannel.send(PendingOperation.ADD)
                        job.join()
                        logInfo(TAG, "handleNotification send pending REDUCE")
                        pendingOperationChannel.send(PendingOperation.REDUCE)
                    }
                }
            }
        }
    }.flowOn(Dispatchers.IO)

    private sealed interface NotificationSource {
        data class FCM(val data: FCMPushData) : NotificationSource

        class IM(val msg: IMessage, val type: BuzNotifyType) : NotificationSource {
            override fun toString(): String {
                return "[notifyType:${type},,msgId:${msg.msgId},msgType:${msg.msgType},servMsgId:${msg.serMsgId},convType:${msg.conversationType},targetId:${msg.getConversationId()}]"
            }
        }

        data class CallInvitationSignal(val signalData: ChannelInviteManager.ChannelInvite) : NotificationSource

        data class AutoPlayedMsg(val msg: IMessage, val type: AutoPlayedMsgType) : NotificationSource

        data class LivePlaceInvitationSignal(val livePlaceInvite: LivePlaceInviteManager.LivePlaceInvite) : NotificationSource
    }
    enum class AutoPlayedMsgType {
        NEW,
        UPDATE_ASR,
        RECALL_MSG
    }
}