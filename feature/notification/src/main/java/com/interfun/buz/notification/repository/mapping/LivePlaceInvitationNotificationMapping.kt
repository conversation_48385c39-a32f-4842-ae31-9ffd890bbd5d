package com.interfun.buz.notification.repository.mapping

import android.app.Notification
import androidx.core.app.NotificationCompat
import com.interfun.buz.base.ktx.toIntLower32Bits
import com.interfun.buz.common.bean.voicecall.ChannelType
import com.interfun.buz.common.ktx.getContactFirstName
import com.interfun.buz.common.manager.cache.user.UserRelationCacheManager
import com.interfun.buz.common.manager.liveplace.LivePlaceInviteManager
import com.interfun.buz.common.utils.NotificationChannelUtils
import com.interfun.buz.notification.model.*
import com.interfun.buz.notification.utils.LivePlaceNotifyUtils
import com.interfun.buz.push.model.FCMPushData
import com.interfun.buz.push.model.LivePlaceInviteExtra
import com.interfun.buz.push.model.PushGroupInfo
import com.interfun.buz.push.model.PushPayloadType
import com.interfun.buz.push.model.PushUserInfo

class LivePlaceInvitationNotificationMapping: LivePlaceInvitationSignalMapping, FcmNotificationMapping {

    override suspend fun mapToNotificationModel(data: LivePlaceInviteManager.LivePlaceInvite): NotificationHandleResult {
        val callUserName = data.callerUserInfo?.userId?.let {
            UserRelationCacheManager.getUserRelationInfoFromCacheSync(it)?.getContactFirstName()?:data.callerUserInfo?.userName
        }
        val senderUserInfo = PushUserInfo(
            userId = data.callerUserInfo?.userId?:0L,
            name = callUserName ?:data.callerUserInfo?.userName?: "",
            portrait = data.callerUserInfo?.portrait?:""
        )
        val groupInfo = data.simpleGroupInfo?.let {
            PushGroupInfo(
                groupId = data.simpleGroupInfo?.groupId?:0L,
                name = data.simpleGroupInfo?.groupName?:"",
                portrait = data.simpleGroupInfo?.serverPortraitUrl?:""
            )
        }
        val topic = data.topic
        val logModel = NotificationLogModel(
            pushType = if (data.channelType == ChannelType.TYPE_LIVE_PLACE_GROUP) {
                PushPayloadType.TYPE_LIVE_PLACE_INVITE_GROUP
            } else {
                PushPayloadType.TYPE_LIVE_PLACE_INVITE_1V1
            }
        )
        val messageStyle = LivePlaceNotifyUtils.createInviteMsgStyle(senderUserInfo, groupInfo, topic)
        return createCallNotificationModel(
            messageStyle,
            logModel,
            data.channelId,
            data.timeout
        )
    }

    override suspend fun mapToNotificationModel(fcmPushData: FCMPushData): NotificationHandleResult {
        val payload = fcmPushData.payload
        val pushExtra: LivePlaceInviteExtra = payload.pushExtra?: return Cancel(CancelReason.PUSH_EXTRA_NULL)
        val topic = pushExtra.topic
        val groupInfo = payload.groupInfo
        val logModel = NotificationLogModel(
            pushType = fcmPushData.payload.type
        )
        if (payload.senderUserInfo != null){
            val messageStyle = LivePlaceNotifyUtils.createInviteMsgStyle(payload.senderUserInfo!!, groupInfo, topic)
            return createCallNotificationModel(
                messageStyle,
                logModel,
                pushExtra.channelId,
                pushExtra.timeout
            )
        }else{
            return Cancel(CancelReason.PUSH_SENDER_INFO_NULL)
        }
    }

    private fun createCallNotificationModel(
        style: NotificationCompat.Style,
        logModel: NotificationLogModel,
        channelId: Long,
        timeoutSec : Int
    ): NotificationHandleResult {
        val notificationId = channelId.toIntLower32Bits()
        val pendingRouter = null
        val notificationChannel = NotificationChannelUtils.getLivePlaceCallInviteChannel()
        /*val filterReason = filterCallNotification(channelId, channelType, pushFrom)
        if (filterReason != null) {
            return Cancel(filterReason)
        }*/
        val model = NotificationModel(
            logModel = logModel,
            notificationId = notificationId,
            pendingRouter = pendingRouter,
            number = 1,
            style = style,
            channel = notificationChannel,
            fullScreenHighPriority = true,
            category = Notification.CATEGORY_CALL,
            visibility = NotificationCompat.VISIBILITY_PUBLIC,
            timeout = timeoutSec * 1000L,
            notificationFlags = Notification.FLAG_INSISTENT,
            showCompleteHandler = { error ->
                if (error == null){
                    // need do nothing
                }
            }
        )
        return Data(model)
    }

}