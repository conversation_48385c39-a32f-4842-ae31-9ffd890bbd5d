package com.interfun.buz.notification.repository.mapping

import android.app.Notification
import android.app.PendingIntent
import android.content.Intent
import android.os.Build.VERSION
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationCompat.CallStyle
import androidx.core.app.Person
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.bean.chat.JumpVoiceCallPageFrom
import com.interfun.buz.common.bean.chat.OnlineChatJumpType
import com.interfun.buz.common.bean.voicecall.CallType
import com.interfun.buz.common.bean.voicecall.ChannelType
import com.interfun.buz.common.broadcast.DeclineChannelReceiver
import com.interfun.buz.common.broadcast.IgnoreDeleteVoiceCallReceiver
import com.interfun.buz.common.constants.IncomingRealTimeCallSource
import com.interfun.buz.common.eventbus.voicecall.CancelVoiceCallInviteEvent
import com.interfun.buz.common.ktx.generateNotificationId
import com.interfun.buz.common.manager.CallNotificationCache
import com.interfun.buz.common.manager.chat.CallPendInfo
import com.interfun.buz.common.manager.chat.CallPendStatus
import com.interfun.buz.common.manager.chat.ChannelInviteManager
import com.interfun.buz.common.manager.chat.ChannelInviteManager.targetId
import com.interfun.buz.common.manager.chat.ChannelPendStatusManager
import com.interfun.buz.common.manager.router.RouterCreator
import com.interfun.buz.common.manager.update.UpdateVersionManager
import com.interfun.buz.common.manager.voicecall.DoreRTCEnginManager
import com.interfun.buz.common.manager.voicecall.VoiceCallNotificationConflictManager
import com.interfun.buz.common.service.RealTimeCallService
import com.interfun.buz.common.utils.*
import com.interfun.buz.common.utils.NotifyFailReason.Companion.WANT_SHOW_BUT_NO_PERMISSION
import com.interfun.buz.common.utils.VoiceCallNotificationTracker.NotifyVoiceCallWay
import com.interfun.buz.common.utils.VoiceCallNotificationTracker.PushFrom
import com.interfun.buz.feature.notification.R
import com.interfun.buz.notification.model.*
import com.interfun.buz.notification.storage.NotificationMemStorage
import com.interfun.buz.notification.utils.NotificationUtils
import com.interfun.buz.onair.standard.IGlobalOnAirController
import com.interfun.buz.push.model.*
import kotlinx.coroutines.*
import okhttp3.internal.toLongOrDefault

class CallInvitationNotificationMapping : FcmNotificationMapping, CallInvitationSignalMapping {

    companion object {
        const val TAG = "CallInviteNotifyMapping"
    }

    override suspend fun mapToNotificationModel(data: ChannelInviteManager.ChannelInvite): NotificationHandleResult {
        if (isAppInForeground && UpdateVersionManager.hasShowUpdateVersion()) {
            logInfo(TAG, "hasShowUpdateVersion--can not show callStyle")
            return Cancel(CancelReason.CALL_IS_SHOWING_UPDATE_DIALOG)
        }
        val channelId =
            data.channelId?.toLongOrNull() ?: return Cancel(CancelReason.PARAMETER_ERROR)
        val channelType = data.channelType
        val callType = data.callType
        if (isUnavailableVoiceCall(channelType, channelId, PushFrom.FROM_ROME)) {
            return Cancel(CancelReason.CALL_ALREADY_REVOKE)
        }
        val callerUserId =
            data.callerUserInfo?.userId ?: return Cancel(CancelReason.PARAMETER_ERROR)
        val isGroup =
            (channelType == ChannelType.TYPE_VOICE_CALL_GROUP /*|| channelType == ChannelType.TYPE_ON_AIR_GROUP*/)
        val targetId = if (isGroup) {
            data.simpleGroupInfo?.groupId
        } else {
            data.callerUserInfo?.userId
        } ?: return Cancel(CancelReason.PARAMETER_ERROR)
        val callerPerson = if (isGroup) {
            val group = data.simpleGroupInfo ?: return Cancel(CancelReason.PARAMETER_ERROR)
            NotificationUtils.createPersonGroup(
                targetId,
                group.serverPortraitUrl,
                group.groupName
            )
        } else {
            val user = data.callerUserInfo ?: return Cancel(CancelReason.PARAMETER_ERROR)
            NotificationUtils.createPersonUser(
                targetId,
                user.portrait,
                user.firstName
            )
        }
        val pushType =
            if (isGroup) PushPayloadType.TYPE_CHANNEL_GROUP_INVITE else PushPayloadType.TYPE_CHANNEL_1V1_INVITE
        val logModel = NotificationLogModel(pushType = pushType)
        val createChannelInvite = { data }
        return createCallNotificationModel(
            logModel,
            channelId,
            channelType,
            callType,
            callerPerson,
            callerUserId,
            targetId,
            data.pushFrom,
            data.timeout,
            null,
            createChannelInvite
        )
    }

    override suspend fun mapToNotificationModel(fcmPushData: FCMPushData): NotificationHandleResult {
        val payload = fcmPushData.payload
        val pushExtra: CallInviteExtra =
            payload.pushExtra ?: return Cancel(CancelReason.PUSH_EXTRA_NULL)
        val channelType = pushExtra.channelType
        val callType = pushExtra.callType
        val channelId = pushExtra.channelId
        VoiceCallNotificationTracker.onReceiveVoiceCallInvitePush(
            channelId,
            fcmPushData.payload.type,
            PushFrom.FROM_FCM
        )
        if (isUnavailableVoiceCall(channelType, channelId, PushFrom.FROM_FCM)) {
            return Cancel(CancelReason.CALL_ALREADY_REVOKE)
        }
        val isGroup =
            (channelType == ChannelType.TYPE_VOICE_CALL_GROUP /*|| channelType == ChannelType.TYPE_ON_AIR_GROUP*/)
        val targetId = if (isGroup) {
            payload.groupInfo?.groupId
        } else {
            payload.senderUserInfo?.userId
        } ?: return Cancel(CancelReason.PARAMETER_ERROR)
        val callerPerson = if (isGroup) {
            NotificationUtils.createPersonGroup(
                targetId,
                payload.groupInfo?.portrait,
                payload.groupInfo?.name
            )
        } else {
            NotificationUtils.createPersonUser(
                targetId,
                payload.senderUserInfo?.portrait,
                payload.senderUserInfo?.name
            )
        }
        val logModel = NotificationLogModel(
            pushType = payload.type
        )
        val createChannelInvite = { createChannelInvite(pushExtra, payload, PushFrom.FROM_FCM) }
        return createCallNotificationModel(
            logModel,
            channelId,
            channelType,
            callType,
            callerPerson,
            payload.senderUserInfo?.userId ?: 0L,
            targetId,
            PushFrom.FROM_FCM,
            pushExtra.timeout,
            fcmPushData.pushPlatformBean,
            createChannelInvite
        )
    }

    private fun createCallNotificationModel(
        logModel: NotificationLogModel,
        channelId: Long,
        channelType: Int,
        callType: Int,
        callerPerson: Person,
        callerUserId: Long,
        targetId: Long,
        @PushFrom pushFrom: Int,
        timeoutSec: Int,
        pushPlatformBean: PushPlatformBean?,
        createChannelInvite: () -> ChannelInviteManager.ChannelInvite
    ): NotificationHandleResult {
        val notificationId = generateNotificationId()
        val isGroup =
            (channelType == ChannelType.TYPE_VOICE_CALL_GROUP /*|| channelType == ChannelType.TYPE_ON_AIR_GROUP*/)
        val answerRouter = if (isGroup) {
            getCallRouterForGroup(
                channelId,
                channelType,
                callType,
                callerUserId,
                targetId,
                OnlineChatJumpType.joinChannel,
                1
            )
        } else {
            getCallRouterForUser(
                channelId,
                channelType,
                callType,
                targetId,
                OnlineChatJumpType.joinChannel,
                1
            )
        }
        val pendingRouter = if (isGroup) {
            getCallRouterForGroup(
                channelId,
                channelType,
                callType,
                callerUserId,
                targetId,
                OnlineChatJumpType.pendAnswer,
                0
            )
        } else {
            getCallRouterForUser(
                channelId,
                channelType,
                callType,
                targetId,
                OnlineChatJumpType.pendAnswer,
                0
            )
        }
        val pendingIntent = NotificationUtils.getPendingIntentByRouter(
            notificationId,
            pendingRouter
        )
        val style =
            style(notificationId, channelId, channelType, callType, callerPerson, answerRouter)
        val notificationChannel = NotificationChannelUtils.getVoiceCallChannel(targetId, isGroup)
        DoreRTCEnginManager.initDore()
        val filterReason = filterCallNotification(channelId, channelType, pushFrom)
        val contentText =
            if (ChannelType.isPrivateChannel(channelType)) {
                if (CallType.isVideoCall(callType)) R.string.rtc_incoming_videocall.asString()
                else R.string.rtc_incoming_voicecall.asString()
            } else {
                if (CallType.isVideoCall(callType)) R.string.rtc_incoming_group_videocall.asString()
                else R.string.rtc_incoming_group_voicecall.asString()
            }

        if (filterReason != null) {
            return Cancel(filterReason)
        }
        val model = NotificationModel(
            logModel = logModel,
            notificationId = notificationId,
            pendingRouter = pendingRouter,
            number = 1,
            channel = notificationChannel,
            content = contentText,
            fullscreenPendingIntent = pendingIntent,
            fullScreenHighPriority = true,
            category = Notification.CATEGORY_CALL,
            visibility = NotificationCompat.VISIBILITY_PUBLIC,
            deleteIntent = getIgnoreDeleteCallPendingIntent(notificationId, channelId, callType),
            timeout = timeoutSec * 1000L,
            style = style,
            isOngoing = true,
            pushPlatformBean = pushPlatformBean,
            notificationFlags = Notification.FLAG_INSISTENT,
            showCompleteHandler = { error ->
                if (error == null) {
                    CallNotificationCache.addVoiceCallNotifyId(channelId, notificationId)
                }
                onShowCompleted(createChannelInvite(), error)
            }
        )
        return Data(model)
    }

    /**
     * 过滤无需显示的voiceCall
     */
    private fun isUnavailableVoiceCall(
        channelType: Int,
        channelId: Long,
        @PushFrom pushFrom: Int
    ): Boolean {
        logInfo(
            TAG,
            "handleUnavailableVoiceCall,channelId:$channelId,channelType:${channelType},lastShowChannelId:${VoiceCallNotificationConflictManager.lastShowChannelId}"
        )
        val isFcm = pushFrom == PushFrom.FROM_FCM
        if (NotificationMemStorage.removeVoiceCallRevokeChannel(channelId) || (isFcm && channelId == VoiceCallNotificationConflictManager.lastShowChannelId)) {
            if (channelId == VoiceCallNotificationConflictManager.lastShowChannelId) {
                logInfo(TAG, "VoiceCallNotificationConflictManager:bingo ")
                val reason = if (pushFrom == PushFrom.FROM_FCM) {
                    NotifyFailReason.FILTER_FROM_FCM_BECAUSE_DUPLICATE_NOTIFY
                } else {
                    NotifyFailReason.DUPLICATE_NOTIFY
                }
                VoiceCallNotificationTracker.onNotifyUserIncomingCallResult(
                    channelId, channelType, pushFrom, null, false,
                    reason
                )
            } else {
                logInfo(TAG, "voiceCallRevokeChannelId:bingo ")
                val reason = if (pushFrom == PushFrom.FROM_FCM) {
                    NotifyFailReason.FILTER_FROM_FCM_BECAUSE_CALL_END
                } else {
                    NotifyFailReason.FILTER_CALL_END
                }
                VoiceCallNotificationTracker.onNotifyUserIncomingCallResult(
                    channelId, channelType, pushFrom, null, false,
                    reason
                )
            }
            return true
        } else {
            if (pushFrom == PushFrom.FROM_FCM) {
                if (ChannelType.isVoiceCallType(channelType)) {
                    logInfo(
                        TAG,
                        "handleUnavailableVoiceCall: change lastShowChannelId to $channelId"
                    )
                    VoiceCallNotificationConflictManager.lastShowChannelId = channelId
                }
            }
        }
        return false
    }

    private fun filterCallNotification(
        channelId: Long,
        channelType: Int,
        @PushFrom pushFrom: Int
    ): CancelReason? {
        val callRoomIfExist =
            routerServices<RealTimeCallService>().value?.getVoiceCallRoomIfExist()
        val onAirRoomChannelId =
            routerServices<IGlobalOnAirController>().value?.curOnAirContext()
                ?.obtainChannelIdFlow()?.value

        if (callRoomIfExist?.roomChannelId == channelId
            && callRoomIfExist.channelType == channelType
        ) {
            logWarn(
                TAG,
                "callRoom channelId = ${channelId} has created, and don't need to show notification again"
            )
            VoiceCallNotificationTracker.onNotifyUserIncomingCallResult(
                channelId,
                callRoomIfExist.channelType,
                pushFrom,
                null,
                false,
                NotifyFailReason.CHANNEL_IS_CONNECTED
            )
            return CancelReason.CALL_ALREADY_IN_CALL
        }

        if (onAirRoomChannelId == channelId) {
            return CancelReason.CALL_ALREADY_IN_CALL
        }

        if (ChannelType.isLivePlaceType(channelType) && CallNotificationCache.getVoiceCallNotifiIdByChannelId(
                channelId
            ) != null
        ) {
            logInfo(TAG, "showNotification: bingo on air ")
            return CancelReason.CALL_ALREADY_IN_CALL
        }
        return null
    }

    private fun onShowCompleted(
        channelInvite: ChannelInviteManager.ChannelInvite,
        throwable: Throwable?
    ) {
        log(TAG, "onShowCompleted: $channelInvite, throwable: $throwable")
        if (throwable != null) {
            fallbackToInAppNotification(channelInvite)
        } else {
            logNotificationShown(channelInvite)
            onNotificationShowSuccess(channelInvite)
        }
    }

    private fun logNotificationShown(channelInvite: ChannelInviteManager.ChannelInvite) {
        val from = channelInvite.pushFrom
        val channelId = channelInvite.channelId?.toLongOrDefault(0) ?: 0
        if (NotificationUtil.isNotifyOpen()) {
            VoiceCallNotificationTracker.onNotifyUserIncomingCallResult(
                channelId,
                channelInvite.channelType,
                from,
                NotifyVoiceCallWay.WAY_CALL_STYLE,
                true
            )

            if (ChannelType.isLivePlaceType(channelInvite.channelType)) {
                CommonTracker.onOnAirInviteExpose(
                    ChannelType.isPrivateChannel(channelType = channelInvite.channelType),
                    channelInvite.targetId?.toString() ?: "0",
                    channelId = channelId.toString()
                )
            }
        } else {
            VoiceCallNotificationTracker.onNotifyUserIncomingCallResult(
                channelId,
                channelInvite.channelType,
                from,
                NotifyVoiceCallWay.WAY_CALL_STYLE,
                false,
                WANT_SHOW_BUT_NO_PERMISSION
            )
        }
    }

    private fun onNotificationShowSuccess(channelInvite: ChannelInviteManager.ChannelInvite) {
        val channelId = channelInvite.channelId ?: return
        ChannelInviteManager.add2ChannelInviteMap(
            channelId,
            channelInvite
        )
        //更新pend answer状态，来电音效和震动不需要业务自己处理
        ChannelPendStatusManager.changeStatus(
            CallPendStatus.BEING_INVITED,
            CallPendInfo(
                channelId = channelId.toLongOrDefault(0),
                channelInvite.channelType,
                channelInvite.targetId ?: 0L,
                useAppSoundEffect = false,
                callType = channelInvite.callType
            )
        )
        // voicecall 呼叫邀请通知展示后，执行delay，再timeout秒后去cancel通知栏
        if (channelInvite.timeout > 0) {
            postCancelVoiceCallEventAfterTimeout(
                channelId.toLongOrDefault(0),
                channelInvite.timeout,
                channelInvite.callType
            )
        }
        routerServices<RealTimeCallService>().value?.let {
            it.notifyMinimizeInvited(
                shouldShow = true,
                channelId = channelId.toLongOrDefault(0),
                targetId = channelInvite.targetId ?: 0L,
                channelType = channelInvite.channelType,
                callType = channelInvite.callType
            )
            it.onCallComeInPageView(
                channelId = channelId.toLongOrDefault(0),
                channelType = channelInvite.channelType,
                targetId = channelInvite.targetId ?: 0L,
                callType = channelInvite.callType,
                source = IncomingRealTimeCallSource.Notification
            )
        }
    }

    private var voiceCallPendJob: Job? = null

    @OptIn(DelicateCoroutinesApi::class)
    private fun postCancelVoiceCallEventAfterTimeout(
        channelId: Long,
        timeoutSec: Int,
        callType: @CallType Int
    ) {
        if (voiceCallPendJob?.isActive == true) {
            logInfo(TAG, "delay cancel")
            voiceCallPendJob?.cancel()
        }
        voiceCallPendJob = GlobalScope.launch(Dispatchers.IO) {
            logInfo(TAG, "delay before")
            //在离线收到推送时，会受到系统的限制导致delay执行失效从而导致call style通知栏超时不会消失,
            //30秒超时自动消失现在改成setTimeoutAfter方式，此方法只处理进程在时发送超时事件通知pendAnswer页面超时关闭
            delay(timeoutSec * 1000L)
            logInfo(
                TAG,
                "delay timeout to post CancelVoiceCallInviteEvent, current rtcChannelId = $channelId"
            )
            CancelVoiceCallInviteEvent.post(channelId, callType)
        }
    }

    private fun createChannelInvite(
        pushExtra: CallInviteExtra,
        payloadNew: PushPayloadNew,
        @PushFrom pushFrom: Int
    ): ChannelInviteManager.ChannelInvite {
        logWarn(
            TAG,
            "Call style notification show exception or nonsupport，so fallback to in-app notification"
        )
        val user = payloadNew.senderUserInfo
        val group = payloadNew.groupInfo
        val callerUserInfo = if (user == null) {
            null
        } else ChannelInviteManager.CallerUserInfo(
            user.userId,
            userName = user.name,
            firstName = null,
            lastName = null,
            portrait = user.portrait
        )
        val callGroupInfo = if (group == null) {
            null
        } else ChannelInviteManager.SimpleGroupInfo(
            group.groupId,
            group.name,
            group.portrait
        )
        return ChannelInviteManager.ChannelInvite(
            sendTimestamp = System.currentTimeMillis(),
            callerUserInfo = callerUserInfo,
            simpleGroupInfo = callGroupInfo,
            channelId = pushExtra.channelId.toString(),
            timeout = pushExtra.timeout,
            channelType = pushExtra.channelType,
            callType = pushExtra.callType,
            expectedDismissTime = System.currentTimeMillis() + pushExtra.timeout * 1000L,
            pushFrom = pushFrom
        )
    }

    private fun fallbackToInAppNotification(channelInvite: ChannelInviteManager.ChannelInvite) {
        logWarn(
            TAG,
            "Call style notification show exception or nonsupport，so fallback to in-app notification"
        )
        ChannelInviteManager.sendInviteCallFallbackFromNotification(channelInvite)
    }


    private fun style(
        notificationId: Int,
        channelId: Long,
        channelType: Int,
        callType: Int,
        callerPerson: Person,
        answerRouter: String,
    ): NotificationCompat.Style {
        val answerIntent =
            NotificationUtils.getPendingIntentByRouter(randomInt, answerRouter)
        val cancelCallPendingIntent =
            getCancelCallPendingIntent(notificationId, channelId, channelType, callType)
        val callStyle = CallStyle.forIncomingCall(
            callerPerson,
            cancelCallPendingIntent,
            answerIntent
        )
        callStyle.setIsVideo(CallType.isVideoCall(callType))
        return callStyle
    }

    private fun getCancelCallPendingIntent(
        notificationId: Int,
        channelId: Long?,
        channelType: @ChannelType Int,
        callType: @CallType Int
    ): PendingIntent {
        val declineIntent = Intent(appContext, DeclineChannelReceiver::class.java).apply {
            putExtra(DeclineChannelReceiver.NOTIFICATION_ID, notificationId)
            putExtra(DeclineChannelReceiver.CALL_CHANNEL_ID, channelId)
            //以下两个参数仅用于打点，失败是通过call style通知栏点击了拒绝的点击事件
            putExtra(DeclineChannelReceiver.DECLINE_FROM_CALL_STYLE, true)
            putExtra(DeclineChannelReceiver.DECLINE_CHANNEL_TYPE, channelType)
            putExtra(DeclineChannelReceiver.DECLINE_CALL_TYPE, callType)
        }
        val flag = if (VERSION.SDK_INT >= 23) {
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_ONE_SHOT
        } else {
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_ONE_SHOT
        }
        return PendingIntent.getBroadcast(appContext, randomInt, declineIntent, flag)
    }

    /**
     * @param behaviorType see [OnlineChatJumpType]
     * @param sourceFromAnswer 来源 用于埋点+判断是否点击call_style通知栏的answer 1:来自call style的 answerIntent
     */
    private fun getCallRouterForGroup(
        channelId: Long,
        channelType: Int,
        callType: Int,
        callerUserId: Long,
        groupId: Long,
        behaviorType: Int,
        sourceFromAnswer: Int
    ): String {
        return RouterCreator.createChannelGroupChatRouter(
            channelId,
            channelType,
            callType,
            callerUserId,
            groupId,
            behaviorType,
            sourceFromAnswer = sourceFromAnswer,
            JumpVoiceCallPageFrom.CALL_STYLE
        ).toString()
    }

    private fun getCallRouterForUser(
        channelId: Long,
        channelType: Int,
        callType: Int,
        callerUserId: Long,
        behaviorType: Int,
        sourceFromAnswer: Int
    ): String {
        return RouterCreator.createChannelPrivateChatRouter(
            channelId,
            channelType,
            callType,
            callerUserId,
            behaviorType,
            sourceFromAnswer = sourceFromAnswer,
            JumpVoiceCallPageFrom.CALL_STYLE
        ).toString()
    }

    /**
     * The user swipes left and right to ignore the DeleteIntent processing of the notification.
     */
    private fun getIgnoreDeleteCallPendingIntent(
        notificationId: Int,
        channelId: Long,
        callType: Int
    ): PendingIntent {
        val declineIntent = Intent(appContext, IgnoreDeleteVoiceCallReceiver::class.java).apply {
            putExtra(IgnoreDeleteVoiceCallReceiver.NOTIFICATION_ID, notificationId)
            putExtra(IgnoreDeleteVoiceCallReceiver.CALL_CHANNEL_ID, channelId)
            putExtra(IgnoreDeleteVoiceCallReceiver.CALL_TYPE_ID, callType)
        }
        val flag = if (VERSION.SDK_INT >= 23) {
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_ONE_SHOT
        } else {
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_ONE_SHOT
        }
        return PendingIntent.getBroadcast(appContext, notificationId, declineIntent, flag)
    }
}