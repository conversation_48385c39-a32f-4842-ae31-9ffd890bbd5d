package com.interfun.buz.notification

import android.app.NotificationManager
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Build
import android.os.Build.VERSION
import android.os.Build.VERSION_CODES
import androidx.core.app.NotificationCompat
import androidx.core.content.pm.ShortcutManagerCompat
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.R
import com.interfun.buz.common.constants.CommonConstant
import com.interfun.buz.common.constants.TrackConstant
import com.interfun.buz.common.ktx.show
import com.interfun.buz.common.manager.router.RouterCreator
import com.interfun.buz.common.utils.BuzTracker
import com.interfun.buz.common.utils.NotificationChannelUtils
import com.interfun.buz.common.utils.NotificationUtil
import com.interfun.buz.im.IMMediator
import com.interfun.buz.im.repo.DownloadNotificationModel
import com.interfun.buz.im.repo.IMFileUploadNotificationRepository
import com.interfun.buz.notification.model.HidePending
import com.interfun.buz.notification.model.NotificationModel
import com.interfun.buz.notification.model.ShowPending
import com.interfun.buz.notification.repository.NotificationRepository
import com.interfun.buz.notification.utils.NotificationTracker
import com.interfun.buz.notification.utils.NotificationUtils
import com.interfun.buz.push.repository.PushRepositoryImpl
import com.yibasan.lizhifm.sdk.platformtools.ResUtil
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.flatMapLatest

object NotificationManager {
    private const val TAG = "NotificationManager1"
    private const val PENDING_NOTIFICATION_ID = 1000001
    private const val REQUEST_CODE_PENDING = 1000002
    private const val PAUSE_DOWNLOAD_NOTIFICATION_ID = 1000003
    private const val REQUEST_CODE_PAUSE_DOWNLOAD = 1000004
    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.Main.immediate)
    private val notificationRepository = NotificationRepository(PushRepositoryImpl)
    private val imFileUploadNotificationRepository = IMFileUploadNotificationRepository()

    @OptIn(ExperimentalCoroutinesApi::class)
    private val pauseDownloadNotificationFlow = IMMediator.fileDownloadRepositoryFlow
        .filterNotNull()
        .flatMapLatest { repo ->
            logInfo(TAG, "Switched to new repo.notificationFlow: ${repo.notificationFlow}")
            repo.notificationFlow
        }

    internal fun init() {
        scope.launch {
            notificationRepository.notificationFlow.collect { model ->
                when (model) {
                    ShowPending -> showPendingNotification()
                    HidePending -> hidePendingNotification()

                    is NotificationModel -> showNotification(model)
                }
            }
        }
        scope.launch {
            imFileUploadNotificationRepository.start()
        }

        scope.launch {
            pauseDownloadNotificationFlow.collect {
                when (it) {
                    is DownloadNotificationModel.StopDownload -> {
                        showDownloadStopNotification(it.title)
                    }

                    is DownloadNotificationModel.StopDownloadTips -> {
                        it.tips.forEach { _ ->
                            NotificationTracker.onPauseDownloadResult()
                        }
                        showDownloadStopTipsNotification(it.tips)
                    }
                }
            }
        }
    }

    private fun showNotification(model: NotificationModel) {
        try {
            logInfo(TAG, "showNotification start:$model")
            val channelId = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                model.channel?.id ?: ""
            } else {
                ""
            }
            val intentExtra =
                NotificationTracker.createIntentExtra(model.logModel, model.pushPlatformBean)
            //需要注意pendingIntent里面的requestCode如果不同的通知的requestCode是一样的，可能会导致点击上个通知实际调整了另外一个通知
            val builder = NotificationUtil.createBaseBuilder(
                appContext,
                channelId,
                NotificationUtils.getPendingIntentByRouter(
                    model.notificationId,
                    model.pendingRouter,
                    intentExtra
                ),
                R.mipmap.common_ic_notification_big,
                model.isOngoing ?: false,
                onlyAlertOnce = model.onlyAlertOnce
            )
            model.timeout?.let {
                builder.setTimeoutAfter(it)
            }
            model.title?.let {
                builder.setContentTitle(it)
            }
            model.content?.let {
                builder.setContentText(it)
            }
            model.fullscreenPendingIntent?.let {
                builder.setFullScreenIntent(it, model.fullScreenHighPriority)
            }
            model.largeIcon?.let {
                builder.setLargeIcon(it)
            }
            builder.setNumber(model.number)
            model.shortcutInfo?.let {
                ShortcutManagerCompat.pushDynamicShortcut(appContext, it)
                builder.setShortcutId(it.id)
            }
            model.style?.let {
                builder.setStyle(it)
            }
            builder.color = R.color.notification_icon_color.asColor()
            model.deleteIntent?.let {
                builder.setDeleteIntent(it)
            }
            model.visibility?.let { visibility ->
                builder.setVisibility(visibility)
            }
            model.category?.let { category ->
                builder.setCategory(category)
            }
            builder.build().apply {
                if (model.notificationFlags != null) {
                    flags = model.notificationFlags
                }
            }.show(model.notificationId)
            logWarn(TAG, "show notification success:$model")
            model.showCompleteHandler(null)
            NotificationTracker.logNotificationExpose(model.logModel)
        } catch (t: Throwable) {
            logError(TAG, t, "show notification error")
            model.showCompleteHandler(t)
            BuzTracker.onTechTrack {
                put(TrackConstant.KEY_EXCLUSIVE_ID, "TT2024040901")
                put(TrackConstant.KEY_EVENT_NAME, "notifyShowException")
                put(TrackConstant.KEY_CONTENT_1, "${model.logModel?.pushType}")
                put(TrackConstant.KEY_CONTENT_2, "${t.message}")
            }
        }
    }

    fun handlePushClickLog(intent: Intent){
        NotificationTracker.handlePushClickLog(intent)
    }

    private fun hidePendingNotification() {
        logInfo(TAG, "hidePendingNotification")
        NotificationUtil.cancelNotification(appContext, PENDING_NOTIFICATION_ID)
    }

    private fun showPendingNotification(onlyAlertOnce: Boolean = false) {
        logInfo(TAG, "showPendingNotification")
        val channelId = if (VERSION.SDK_INT >= VERSION_CODES.O) {
            NotificationChannelUtils.getFriendMsgChannel()?.id
        } else {
            CommonConstant.NOTIFICATION_CHANNEL_ID_CHAT
        }
        val homeRouter = RouterCreator.createHomeListRouter(null)
        val notification = NotificationUtil.createBaseBuilder(
            appContext,
            channelId ?: CommonConstant.NOTIFICATION_CHANNEL_ID_CHAT,
            NotificationUtils.getPendingIntentByRouter(REQUEST_CODE_PENDING, homeRouter.toString()),
            R.mipmap.common_ic_notification_big,
            onlyAlertOnce = onlyAlertOnce
        )
            .setColor(Color.BLACK)
            .setContentText(R.string.notification_retrieving_new_message.asString())
            .build()

        NotificationUtil.showNotification(
            context = appContext,
            PENDING_NOTIFICATION_ID,
            notification
        )
    }



    private fun showDownloadStopNotification(title: String) {
        logInfo(TAG,"showDownloadStopNotification:$title")
        val channel = NotificationChannelUtils.getFileDownloadNotificationChannel()
        val channelId = if (VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            channel?.id ?: ""
        } else {
            ""
        }
        val homeRouter = RouterCreator.createHomeListRouter(null)
        val defaultNotification = NotificationUtil.createBaseBuilder(
            context = appContext,
            channelId = channelId,
            intent = NotificationUtils.getPendingIntentByRouter(
                REQUEST_CODE_PAUSE_DOWNLOAD,
                homeRouter.toString()
            ),
            smallIcon = R.mipmap.common_ic_notification_big,
        ).apply {
            setContentTitle(title)
            color = R.color.notification_icon_color.asColor()
        }.build()
        val notificationManager =
            appContext.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(PAUSE_DOWNLOAD_NOTIFICATION_ID, defaultNotification)
    }

    private fun showDownloadStopTipsNotification(tips: List<String>) {
        logDebug(TAG,"showDownloadStopTipsNotification:${tips.joinToString(";")}")
        val channel = NotificationChannelUtils.getFileDownloadNotificationChannel()
        val channelId = if (VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            channel?.id ?: ""
        } else {
            ""
        }
        val inboxStyle = NotificationCompat.InboxStyle()
        val maxDisplay = 5
        tips.take(maxDisplay).forEach { title ->
            inboxStyle.addLine("⏸ $title")
        }
        if (tips.size > maxDisplay) {
            inboxStyle.addLine(R.string.dot_more.asString())
        }
        inboxStyle.setSummaryText(ResUtil.getString(R.string.notification_pause_count,"${tips.size}"))
        val homeRouter = RouterCreator.createHomeListRouter(null)
        val defaultNotification = NotificationUtil.createBaseBuilder(
            context = appContext,
            channelId = channelId,
            intent = NotificationUtils.getPendingIntentByRouter(
                REQUEST_CODE_PAUSE_DOWNLOAD,
                homeRouter.toString()
            ),
            smallIcon = R.mipmap.common_ic_notification_big,
        ).apply {
            setContentTitle(R.string.notification_current_download_stopped_by_net_change.asString())
            setContentText(ResUtil.getString(R.string.notification_pause_count,"${tips.size}"))
            color = R.color.notification_icon_color.asColor()
            setStyle(inboxStyle)
        }.build()
        val notificationManager =
            appContext.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(PAUSE_DOWNLOAD_NOTIFICATION_ID, defaultNotification)
    }
}