package com.interfun.buz.notification.repository.mapping

import com.interfun.buz.im.entity.BuzNotifyType
import com.interfun.buz.notification.model.NotificationHandleResult
import com.lizhi.im5.sdk.message.IMessage

class IMMappingStrategy(private val chatMsgMapping: IMNotificationMapping = ChatNotificationMapping()) {
    suspend fun map(
        msg: IMessage,
        type: BuzNotifyType
    ): NotificationHandleResult {
        return chatMsgMapping.mapToNotificationModel(msg, type)
    }
}