apply plugin: 'kotlin-android'
apply plugin: 'kotlin-parcelize'
apply plugin: 'com.google.devtools.ksp'
apply plugin:  'com.google.dagger.hilt.android'
apply plugin: 'kotlinx-serialization' // 支持sealed class的Serializable
ksp {
    arg("AROUTER_MODULE_NAME", project.getName())
    arg("room.schemaLocation", "$rootProject.rootDir/room_schema")
}
android {
    compileSdkVersion rootProject.ext.android["compileSdkVersion"]

    defaultConfig {
        manifestPlaceholders =[HW_APP_ID:"110599309"]
        minSdkVersion rootProject.ext.android["minSdkVersion"]
        targetSdkVersion rootProject.ext.android["targetSdkVersion"]
        versionName rootProject.ext.getGlobalProperty("QA_VERSION_NAME",
                rootProject.ext.android["versionName"])
        versionCode rootProject.ext.getGlobalProperty("QA_VERSION_CODE",
                getGitRevision().toInteger())
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'),
                    'proguard-rules.pro'
            consumerProguardFiles 'proguard-rules.pro'
        }
        debug {
            minifyEnabled false
        }
    }

    compileOptions {
        coreLibraryDesugaringEnabled true
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8.toString()
    }

    buildFeatures {
        viewBinding true
        dataBinding true
        compose true
    }

    composeOptions {
        kotlinCompilerExtensionVersion = "1.5.14"
    }
}

dependencies {
    //JUnit Test
    testImplementation rootProject.ext.dependencies["junit"]
    androidTestImplementation rootProject.ext.dependencies["junit_androidx"]
    androidTestImplementation rootProject.ext.dependencies["espresso_core"]
    coreLibraryDesugaring rootProject.ext.dependencies["desugar_jdk_libs"]
    //compiler需要每个模块添加
    ksp 'com.github.JailedBird:ArouterKspCompiler:1.9.20-1.0.7'
    ksp rootProject.ext.dependencies["room_compiler"]
    ksp "com.google.dagger:hilt-compiler:2.52"   // Hilt compiler
    api 'com.google.dagger:hilt-android:2.52'
    api "androidx.hilt:hilt-navigation-compose:1.2.0"
    api "org.jetbrains.kotlinx:kotlinx-serialization-json:1.6.3"
    implementation fileTree(dir: "libs", include: ["*.jar"])
    implementation rootProject.ext.dependencies["arouter"]
}

/**
 * 根据git commit次数生成版本号.
 */
def getGitRevision() {
    def buildnum = 1
    try {
        def getGit = 'git rev-list --count HEAD'.execute([], project.rootDir)
        def gitVersion = getGit.text.trim().toInteger()
        buildnum = buildnum + gitVersion
    } catch (e) {
        print(e.message)
    }
    return buildnum
}