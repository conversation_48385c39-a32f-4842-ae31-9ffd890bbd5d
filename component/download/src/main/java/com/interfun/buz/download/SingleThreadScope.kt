package com.interfun.buz.download

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.ExecutorCoroutineDispatcher
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.launch
import java.util.concurrent.Executors
import kotlin.coroutines.CoroutineContext

class SingleThreadScope {

    @Volatile
    private var dispatcher: ExecutorCoroutineDispatcher = createDispatcher()

    @Volatile
    private var job = SupervisorJob()

    @Volatile
    private var _scope = CoroutineScope(dispatcher + job)

    val coroutineContext: CoroutineContext
        get() {
            ensureScopeActive()
            return dispatcher + job
        }

    val scope: CoroutineScope
        get() {
            ensureScopeActive()
            return _scope
        }

    private fun createDispatcher(): ExecutorCoroutineDispatcher {
        return Executors.newSingleThreadExecutor { runnable ->
            Thread(runnable, "single-thread").apply { isDaemon = true }
        }.asCoroutineDispatcher()
    }

    private fun ensureScopeActive() {
        if (!job.isActive) {
            synchronized(this) {
                if (!job.isActive) {
                    dispatcher = createDispatcher()
                    job = SupervisorJob()
                    _scope = CoroutineScope(dispatcher + job)
                }
            }
        }
    }

    /**
     * 提交任务，如果之前 scope 被 cancel，会自动重建
     */
    fun launch(block: suspend CoroutineScope.() -> Unit): Job {
        ensureScopeActive()
        return _scope.launch {
            block()
        }
    }

    /**
     * 取消当前所有任务（dispatcher 保留）
     */
    fun cancel() {
        if (job.isActive) {
            job.cancel()
        }
    }

    /**
     * 彻底释放线程资源（不可再用）
     */
    fun close() {
        cancel()
        dispatcher.close()
    }
}