package com.interfun.buz.download.db.entity

import android.os.Parcelable
import androidx.annotation.Keep
import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Index
import androidx.room.TypeConverter
import androidx.room.TypeConverters
import kotlinx.parcelize.Parcelize

/**
 * Author: ChenYouSheng
 * Date: 2025/6/3
 * Email: <EMAIL>
 * Desc: 下载缓存表
 */
@Keep
@Parcelize
@Entity(
    tableName = "buz_download_cache",
    primaryKeys = ["taskId", "storagePath"],
    indices = [Index(value = ["taskId"])]
)
data class BuzDownloadCacheEntity(

    @ColumnInfo(name = "taskId", defaultValue = "")
    val taskId: String,

    @ColumnInfo(name = "storagePath", defaultValue = "")
    val storagePath: String, // 不同的业务，不同的缓存目录

    @ColumnInfo(name = "remoteUrl", defaultValue = "")
    val remoteUrl: String,

    @ColumnInfo(name = "totalLength", defaultValue = "0")
    val totalLength: Long,

    @ColumnInfo(name = "fileName", defaultValue = "")
    val fileName: String,

    @ColumnInfo(name = "status", defaultValue = "0")
    val status: Int, // IDLE(0),PENDING(1),STARTED(2),PAUSED(3),CANCEL(4),FAILURE(5),SUCCESS(6)

    @ColumnInfo(name = "startTime", defaultValue = "0")
    val startTime: Long,

    @ColumnInfo(name = "endTime", defaultValue = "0")
    val endTime: Long,

) : Parcelable

