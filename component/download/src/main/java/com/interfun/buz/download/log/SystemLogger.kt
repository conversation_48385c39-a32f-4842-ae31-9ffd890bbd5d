package com.interfun.buz.download.log

import android.util.Log
import com.interfun.buz.download.IDownloadManager.Logger

/**
 * Author: <PERSON><PERSON>ouSheng
 * Date: 2025/6/19
 * Email: ch<PERSON><PERSON><PERSON><PERSON>@vocalbeats.com
 * Desc: 默认的日志打印实现，用的是系统的
 */
class SystemLogger : Logger {

    override fun logI(tag: String, msg: String) {
        Log.i(msg, msg)
    }

    override fun logE(tag: String, e: Throwable?, msg: String) {
        Log.e(tag, msg, e)
    }
}