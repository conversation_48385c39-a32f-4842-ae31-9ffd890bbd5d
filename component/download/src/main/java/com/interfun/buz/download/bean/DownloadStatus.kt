package com.interfun.buz.download.bean


/**
 * 下载状态
 */
sealed class DownloadStatus(val value: Int) {
    // Initial state or after a task is fully reset/removed
    data object IDLE : DownloadStatus(0)
    // Set when a new download is queued or a paused task is resumed
    data object PENDING : DownloadStatus(1)
    // Set just before the download network operation begins for a task
    data object STARTED : DownloadStatus(2)
    // Set when a download is explicitly paused by the user
    data object PAUSED : DownloadStatus(3)
    // Set when a download is explicitly cancelled by the user or due to a coroutine cancellation
    data object CANCEL : DownloadStatus(4)
    // Set when a download fails due to an error (e.g., network issue, server error, interruption)
    data class FAILURE(val error: String? = null) : DownloadStatus(5)
    // Set when the file has been completely and successfully downloaded
    data object SUCCESS : DownloadStatus(6)

    override fun toString(): String {
        return "${this::class.java.simpleName}(${value})"
    }

    companion object{
        fun Int.toDownloadStatus(): DownloadStatus {
            return when (this) {
                0 -> IDLE
                1 -> PENDING
                2 -> STARTED
                3 -> PAUSED
                4 -> CANCEL
                5 -> FAILURE()
                6 -> SUCCESS
                else -> IDLE
            }
        }
    }
}

