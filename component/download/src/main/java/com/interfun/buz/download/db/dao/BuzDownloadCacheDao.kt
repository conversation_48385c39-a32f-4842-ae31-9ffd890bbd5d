package com.interfun.buz.download.db.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import androidx.room.Upsert
import com.interfun.buz.download.db.entity.BuzDownloadCacheEntity

/**
 * Author: ChenYouSheng
 * Date: 2025/6/3
 * Email: <EMAIL>
 * Desc: 文件下载缓存记录，记录的是下载文件的状态，remoteUrl相关信息
 */
@Dao
interface BuzDownloadCacheDao {
    /**
     * 插入一条记录
     */
    @Insert(onConflict = OnConflictStrategy.Companion.REPLACE)
    suspend fun insertDownloadCache(downloadCache: BuzDownloadCacheEntity): Long

    /**
     * 更新一条记录
     */
    @Update
    suspend fun updateDownloadCache(downloadCache: BuzDownloadCacheEntity): Int


    /**
     * 更新和插入
     */
    @Upsert
    suspend fun upsertDownloadCache(entity: BuzDownloadCacheEntity)

    /**
     * 更新状态
     */
    @Query("UPDATE buz_download_cache SET status = :status WHERE taskId = :taskId and storagePath = :storagePath")
    suspend fun updateDownloadState(taskId: String, storagePath: String, status: Int)

    /**
     * 更新状态
     */
    @Query("UPDATE buz_download_cache SET status = :newState , endTime = :endTime WHERE  storagePath = :storagePath and status = :oldState")
    suspend fun updateDownloadState(
        storagePath: String,
        endTime: Long,
        newState: Int,
        oldState: Int
    )

    /**
     * 获取所有缓存记录
     */
    @Query("SELECT * FROM buz_download_cache WHERE taskId = :taskId and storagePath = :storagePath")
    suspend fun queryAllDownloadCache(
        taskId: String,
        storagePath: String
    ): List<BuzDownloadCacheEntity>

    /**
     * 获取所有缓存记录
     */
    @Query("SELECT * FROM buz_download_cache WHERE storagePath = :storagePath")
    suspend fun queryAllDownloadCache(storagePath: String): List<BuzDownloadCacheEntity>


    /**
     * 删除一条记录
     */
    @Query("DELETE FROM buz_download_cache WHERE taskId = :taskId and storagePath = :storagePath")
    suspend fun deleteDownloadCache(
        taskId: String,
        storagePath: String
    )


    /**
     * 获取所有未下载成功的记录
     */
    @Query("SELECT * FROM buz_download_cache WHERE status != 6 and storagePath = :storagePath")
    suspend fun getAllDownloadFailureCacheByBusinessType(storagePath: String): List<BuzDownloadCacheEntity>


    /**
     * 查询一条记录
     */
    @Query("SELECT * FROM buz_download_cache WHERE taskId = :taskId and storagePath = :storagePath")
    suspend fun querySingleCache(
        taskId: String,
        storagePath: String
    ): BuzDownloadCacheEntity?


    /**
     * 清除所有
     */
    @Query("DELETE FROM buz_download_cache")
    suspend fun deleteAllCache()

    /**
     * 清除所有指定业务类型的缓存
     */
    @Query("DELETE FROM buz_download_cache WHERE storagePath = :storagePath")
    suspend fun deleteAllCache(storagePath: String)

    /**
     * 批量清除记录
     */
    @Delete
    suspend fun removeCacheList(cacheList: List<BuzDownloadCacheEntity>)

}