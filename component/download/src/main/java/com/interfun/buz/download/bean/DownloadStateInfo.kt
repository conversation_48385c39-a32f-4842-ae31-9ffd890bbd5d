package com.interfun.buz.download.bean

/**
 * 下载状态变更事件
 */
data class DownloadStateInfo(
    val url: String,
    val fileName: String?,
    val state: DownloadStatus,
    val progress: Int = 0
)

data class FileIdentity(
    val url: String,
    val fileName: String?
) {
    companion object {
        fun from(url: String, fileName: String?) = FileIdentity(url, fileName)
        fun from(pair: Pair<String, String>) = FileIdentity(pair.first, pair.second)
        fun from(downloadStateChange: DownloadStateInfo) = FileIdentity(downloadStateChange.url, downloadStateChange.fileName)
    }
}