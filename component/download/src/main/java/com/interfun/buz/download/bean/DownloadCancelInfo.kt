package com.interfun.buz.download.bean

/**
 * Author: Chen<PERSON>ouSheng
 * Date: 2025/6/20
 * Email: chen<PERSON><PERSON><PERSON>@vocalbeats.com
 * Desc: cancelAll触发的时候返回的信息
 */
sealed interface DownloadCancelInfo {
    data class PendingInfo(val url: String, val fileName: String?) : DownloadCancelInfo
    data class PauseInfo(val url: String, val fileName: String?) : DownloadCancelInfo
    data class DownloadInfo(val url: String, val fileName: String?) : DownloadCancelInfo
    data class FailureInfo(val url: String, val fileName: String?) : DownloadCancelInfo
}