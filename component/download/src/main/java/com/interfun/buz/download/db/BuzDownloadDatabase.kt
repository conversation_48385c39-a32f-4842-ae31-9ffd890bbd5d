package com.interfun.buz.download.db

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import com.interfun.buz.download.db.dao.BuzDownloadCacheDao
import com.interfun.buz.download.db.entity.BuzDownloadCacheEntity

@Database(
    entities = [
        BuzDownloadCacheEntity::class,
    ],
    version = 1,
    autoMigrations = [
    ],
    exportSchema = true
)
abstract class BuzDownloadDatabase : RoomDatabase() {

    abstract fun getDownloadCacheDao(): BuzDownloadCacheDao

    companion object {
        @Volatile
        private var INSTANCE: BuzDownloadDatabase? = null
        private const val DATABASE_NAME = "buz_download.db"

        fun getInstance(context: Context): BuzDownloadDatabase {
            if (INSTANCE == null) {
                synchronized(BuzDownloadDatabase::class) {
                    if (INSTANCE != null) return INSTANCE!!
                    INSTANCE = Room.databaseBuilder(
                        context.applicationContext,
                        BuzDownloadDatabase::class.java,
                        DATABASE_NAME
                    ).allowMainThreadQueries().build()
                }
            }
            return INSTANCE!!
        }
    }
}


