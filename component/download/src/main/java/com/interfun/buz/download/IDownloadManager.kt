package com.interfun.buz.download

import com.interfun.buz.download.bean.DownloadCancelInfo
import com.interfun.buz.download.bean.DownloadProgressChange
import com.interfun.buz.download.bean.DownloadStateInfo
import com.interfun.buz.download.bean.DownloadStatus
import com.interfun.buz.download.db.entity.BuzDownloadCacheEntity
import kotlinx.coroutines.flow.SharedFlow
import java.io.File

interface IDownloadManager {

    interface Logger {
        fun logI(tag: String, msg: String)
        fun logE(tag: String, e: Throwable?, msg: String)
    }

    /**
     * For download progress changes event.
     */
    val progressFlow: SharedFlow<DownloadProgressChange>

    /**
     * For download state change events.
     */
    val stateFlow: SharedFlow<DownloadStateInfo>

    /**
     * Generate a unique task ID based on the URL and filename.
     */
    fun generateTaskId(url: String, fileName: String? = null): String

    /**
     * Adds a task to the pending queue if not exists in pendingTasks and try to trigger download.
     * If already in pause or pending queue, it will resume it.
     * @param url The URL to download.
     * @param fileName Optional. The name to save the file as. Defaults to name from URL.
     * @param serverFileSize Optional. The total size of the file on the server.
     * @param source Optional source identifier for the task.
     */
    suspend fun pendingDownload(url: String, fileName: String? = null,
                                serverFileSize: Long? = null, source: String? = null): Boolean


    /**
     * Adds a task to the pending queue if not exists in pendingTasks and try to trigger download.
     * If already in pause or pending queue, it will resume it.
     * @param url The URL to download.
     * @param fileName Optional. The name to save the file as. Defaults to name from URL.
     * @param serverFileSize Optional. The total size of the file on the server.
     * @param source Optional source identifier for the task
     */
    suspend fun pendingDownloadAsync(url: String, fileName: String? = null,
                        serverFileSize: Long? = null, source: String? = null)

    /**
     * Attempts to start a download task if already in the pending queue.
     * If the task is not in the pending queue, it will not start download.
     */
    suspend fun download(): Boolean


    /**
     * Attempts to start a download task if already in the pending queue.
     * If the task is not in the pending queue, it will not start download.
     * This method does not suspend, allowing the caller to continue executing other tasks.
     */
    fun downloadAsync()

    /**
     * Attempts to pause a currently downloading or pending task.
     * Moves the task to the paused queue.
     * @param url The URL of the task to pause.
     */
    suspend fun pause(url: String, fileName: String?): Boolean


    /**
     * Attempts to resume a paused download task.
     * Moves the task from the paused queue back to the pending queue.
     * If the task already in pending task ,it will change the priority to the top of the queue.
     * If the task is not found in the paused queue and pending task, it will call [pendingDownloadAsync] to try to add it to the pending queue.
     * @param url The URL of the task to resume.
     */
    suspend fun resume(url: String, fileName: String?): Boolean

    /**
     * Attempts to resume all  download error tasks.
     * Special note: Android data traffic switching to Wi-Fi network, or Wi-Fi network switching to data traffic,
     * this process will have a network interruption transition state,
     * so it will also cause the download task to be interrupted, and will throw Software caused connection abort exception.
     * this situation after the network to restore the connection to Wi-Fi or traffic, if you need to re-download, you can call this method
     * @return the count of resumed task.
     */
    suspend fun resumeFailureTask(): Int

    /**
     * Attempts to resume a paused download task.
     * Moves the task from the paused queue back to the pending queue.
     * If the task is not found in the paused queue, it won't start download.
     * This method does not suspend, allowing the caller to continue executing other tasks.
     * @param url The URL of the task to resume.
     */
    fun resumeAsync(url: String, fileName: String?)

    /**
     * cancel single task
     * Attempts to cancel a downloading, pending, or paused task.
     * Removes the task from its current queue/list.
     * The partial file will remain for breakpoint resume unless explicitly deleted.
     * @param url The URL of the task to cancel.
     * @param needDeleteFile if true, it will delete the partial file.
     */
    suspend fun cancel(url: String, fileName: String?, needDeleteFile: Boolean = false): Boolean

    /**
     * Attempts to cancel a downloading, pending, or paused task.
     * Removes the task from its current queue/list.
     * The partial file will remain for breakpoint resume unless explicitly deleted.
     * This method does not suspend, allowing the caller to continue executing other tasks.
     * @param url The URL of the task to cancel.
     */
    fun cancelAsync(url: String, fileName: String?)

    /**
     * cancel all the task
     * @return the information about [DownloadCancelInfo]  that were cancelled.
     */
    suspend fun cancelAll(): List<DownloadCancelInfo>

    /**
     * Delete a downloaded or partial file
     * If delete success, it will also remove the record from the database.
     * @param url The URL of the file to delete.
     * @param fileName Optional. The name of the file to delete. Defaults to name from URL.
     */
    suspend fun deleteFile(url: String, fileName: String? = null): Boolean

    /**
     * get the downloaded file size
     */
    suspend fun getDownloadFileSize(url: String, fileName: String? = null): Long


    /**
     * get all the downloaded file size
     */
    suspend fun getAllDownloadFileSize(): Long

    /**
     * get the downloaded file
     */
    suspend fun getDownloadFile(url: String, fileName: String? = null): File?

    /**
     * Get a list of all caches that were not downloaded successfully
     */
    suspend fun getAllDownloadFailureCache(): List<BuzDownloadCacheEntity>

    /**
     * Get a list of all caches
     */
    suspend fun queryAllDownloadCache(): List<BuzDownloadCacheEntity>

    /**
     * remove a cache list from the database
     */
    suspend fun removeCacheList(entities: List<BuzDownloadCacheEntity>)

    /**
     *  Get the status of a download state. The state will get from memory queue first,
     *  like pendingTask, downloadingTask and pauseTask. if there is no task in the queue, it will try to get the status from the database.
     *  If can not get any status from the queue and database, it will return [DownloadStatus.IDLE].
     *  @param url The URL of the file to check.
     *  @return The status of the download. [DownloadStatus.IDLE] if no task found.
     */
    suspend fun getDownloadStateInfo(
        url: String,
        fileName: String?,
        checkDataBase: Boolean = true
    ): DownloadStateInfo


    /**
     * Determine if the file is the same file
     * @param currentStateInfo The current state of the download.
     * @param url The URL of the file to check.
     * @param fileName The name of the file to check.
     * @return true if the file is the same file, false otherwise.
     */
    fun isSameTask(currentStateInfo: DownloadStateInfo, url: String, fileName: String?): Boolean

    /**
     * Shut down the manager
     * Note: Call with caution.
     * If called, the entire BuzDownLoadManager will no longer be usable and a new BuzDownLoadManager needs to be defined to use it
     */
    fun shutdown()
}