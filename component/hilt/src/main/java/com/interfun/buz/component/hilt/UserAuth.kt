package com.interfun.buz.component.hilt


import kotlinx.coroutines.*

sealed interface UserAuth {
    val userId: Long
    val userScope: CoroutineScope

    data class Authenticated(
        override val userId: Long,
        override val userScope: CoroutineScope
    ) : UserAuth

    data class Unauthenticated(
        override val userId: Long,
        override val userScope: CoroutineScope
    ) : UserAuth
}
