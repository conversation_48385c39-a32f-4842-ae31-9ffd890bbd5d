import { log } from "./logger.js";

Java.perform(function () {
    log("[*] WSConnection Hook Script loaded successfully");

    const targetClassName = "com.lizhi.im5.netadapter.websocket.WSConnection";
    const newUrl = "ws://***********:8081";

    try {
        // 获取目标类
        const WSConnectionClass = Java.use(targetClassName);
        log(`[*] Found class: ${targetClassName}`);

        // Hook 构造函数
        WSConnectionClass.$init.overload('java.lang.String', 'java.lang.String', 'com.lizhi.im5.netadapter.websocket.WebSocketHandler').implementation = function (url: string, transactionId: string, webSocketHandler: any) {
            log(`[*] WSConnection constructor called`);
            log(`[*] Original URL: ${url}`);
            log(`[*] Transaction ID: ${transactionId}`);
            log(`[*] Modified URL: ${newUrl}`);
            
            // 打印调用栈
            log(`[*] Call stack: ${Java.use("android.util.Log").getStackTraceString(Java.use("java.lang.Exception").$new())}`);
            
            // 调用原始构造函数，但使用修改后的 URL
            return this.$init(newUrl, transactionId, webSocketHandler);
        };

        log(`[*] Successfully hooked ${targetClassName} constructor`);
        
    } catch (error) {
        log(`[!] Error hooking ${targetClassName}: ${error}`);
        
        // 如果直接 hook 失败，尝试枚举查找类
        log("[*] Attempting to find class through enumeration...");
        Java.enumerateLoadedClasses({
            onMatch: function (className: string) {
                if (className.includes("WSConnection") || className.includes("websocket")) {
                    log(`[*] Found potential class: ${className}`);
                    try {
                        const foundClass = Java.use(className);
                        const constructors = foundClass.class.getDeclaredConstructors();
                        for (let i = 0; i < constructors.length; i++) {
                            const constructor = constructors[i];
                            const paramTypes = constructor.getParameterTypes();
                            if (paramTypes.length === 3) {
                                log(`[*] Found constructor with 3 parameters in class ${className}`);
                                
                                // 尝试 hook 构造函数
                                try {
                                    foundClass.$init.overload('java.lang.String', 'java.lang.String', 'com.lizhi.im5.netadapter.websocket.WebSocketHandler').implementation = function (url: string, transactionId: string, webSocketHandler: any) {
                                        log(`[*] WSConnection constructor called on ${className}`);
                                        log(`[*] Original URL: ${url}`);
                                        log(`[*] Transaction ID: ${transactionId}`);
                                        log(`[*] Modified URL: ${newUrl}`);
                                        
                                        // 打印调用栈
                                        log(`[*] Call stack: ${Java.use("android.util.Log").getStackTraceString(Java.use("java.lang.Exception").$new())}`);
                                        
                                        // 调用原始构造函数，但使用修改后的 URL
                                        return this.$init(newUrl, transactionId, webSocketHandler);
                                    };
                                    
                                    log(`[*] Successfully hooked ${className} constructor`);
                                    break;
                                } catch (hookError) {
                                    log(`[!] Error hooking constructor in ${className}: ${hookError}`);
                                }
                            }
                        }
                    } catch (e) {
                        log(`[!] Error processing class ${className}: ${e}`);
                    }
                }
            },
            onComplete: function () {
                log("[*] Class enumeration completed");
            }
        });
    }
});