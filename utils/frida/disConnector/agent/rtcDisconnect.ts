import { log } from "./logger.js";

Java.perform(function () {
    log("[*] Script loaded successfully");

    const targetClassName = "com.yibasan.lizhifm.rtcdorime.DorimeRTCEngine";
    const methodName = "setDispatchRespond";
    
    // 要替换的参数内容
    const newRespond = '{"audioBitRate":"35000","audioBweCurrentBitrateBps":96,"audioBweMaxBitrateBps":128,"audioBweMinBitrateBps":25,"audioEncodeMaxBitrateBps":35,"audioEncodeMinBitrateBps":15,"channelId":"051-5450608272969307263","disableIceDtls":"1","dispatchPolicy":"Default$Abtest","dtxControl":"0","enableAudioBWE":1,"enableAudioLevelFilter":true,"enableJitterTS":true,"enableSignal":false,"gopInterval":2,"jbDelayMinMs":"160","jtsMaxTargetDelay":3000,"jtsScaleInterval":5,"pcWorkMode":5,"signalServerIp":"**************","signalServerPort":"8188","stunServerIp":"**************","stunServerPort":"8000","tryConnectTimesPerJIP":"800","videoFPS":15,"device":"{\\"enableNs\\":1,\\"enableSoftAec\\":0,\\"encodeResolution\\":\\"960x540\\",\\"gain\\":12}","noPubData":0,"noPubVideoData":0,"localFileModel":false,"recordScreenForTest":false,"requestId":"iPyqg","rtcModel":"sfu","transactionId":****************,"callId":"mbg92725-l7VHnOuXS5","qosTestIndex":"","qosLogPath":"\/sdcard"}';

    try {
        // 获取目标类
        const TargetClass = Java.use(targetClassName);
        log(`[*] Found class: ${targetClassName}`);

        // Hook setDispatchRespond 方法
        const targetMethod = TargetClass[methodName];
        if (!targetMethod) {
            log(`[!] Method ${methodName} not found in class ${targetClassName}`);
            return;
        }
        log(`[*] Found method: ${methodName}`);

        // 实现 Hook
        targetMethod.implementation = function (respond: string) {
            log(`[*] ${methodName} called`);
            log(`[*] Original parameter: ${respond}`);
            log(`[*] Modified parameter: ${newRespond}`);
            
            // 打印调用栈
            log(`[*] Call stack: ${Java.use("android.util.Log").getStackTraceString(Java.use("java.lang.Exception").$new())}`);
            
            // 调用原始方法，但使用修改后的参数
            return this[methodName](newRespond);
        };

        log(`[*] Successfully hooked ${targetClassName}.${methodName}`);
        
    } catch (error) {
        log(`[!] Error hooking ${targetClassName}.${methodName}: ${error}`);
        
        // 如果直接 hook 失败，尝试枚举查找类
        log("[*] Attempting to find class through enumeration...");
        Java.enumerateLoadedClasses({
            onMatch: function (className: string) {
                if (className.includes("DorimeRTCEngine")) {
                    log(`[*] Found potential class: ${className}`);
                    try {
                        const foundClass = Java.use(className);
                        const methods = foundClass.class.getDeclaredMethods();
                        for (let i = 0; i < methods.length; i++) {
                            const method = methods[i];
                            if (method.getName() === methodName) {
                                log(`[*] Found method ${methodName} in class ${className}`);
                                
                                // 实现 Hook
                                foundClass[methodName].implementation = function (respond: string) {
                                    log(`[*] ${methodName} called on ${className}`);
                                    log(`[*] Original parameter: ${respond}`);
                                    log(`[*] Modified parameter: ${newRespond}`);
                                    
                                    // 打印调用栈
                                    log(`[*] Call stack: ${Java.use("android.util.Log").getStackTraceString(Java.use("java.lang.Exception").$new())}`);
                                    
                                    // 调用原始方法，但使用修改后的参数
                                    return this[methodName](newRespond);
                                };
                                
                                log(`[*] Successfully hooked ${className}.${methodName}`);
                                break;
                            }
                        }
                    } catch (e) {
                        log(`[!] Error processing class ${className}: ${e}`);
                    }
                }
            },
            onComplete: function () {
                log("[*] Class enumeration completed");
            }
        });
    }
});