{"name": "frida-agent-example", "version": "1.0.0", "description": "Example Frida agent written in TypeScript", "private": true, "main": "agent/index.ts", "scripts": {"prepare": "npm run build", "build": "frida-compile agent/index.ts -o _agent.js -c", "watch": "frida-compile agent/index.ts -o _agent.js -w"}, "devDependencies": {"@types/frida-gum": "^18.5.1", "@types/node": "^18.19.3", "frida-compile": "^16.4.1"}}