import java.io.File
import java.io.FileNotFoundException
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter
import java.util.regex.Pattern
import java.lang.StringBuilder

// 1. 定义原始日志解析的数据类
data class LogEntry(
    val time: LocalDateTime,
    val thread: String,
    val logLevel: String,
    val logTag: String,
    val logContent: String
)

// 2. 解析原始日志行的函数 (正则表达式已包含之前的修正)
fun parseLogLine(logLine: String): LogEntry? {
    val regex: String = """^(\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2}\.\d{3})\s+([\w:.-]+)(?:\s+.*?)?\s+([A-Z])\s+(.+?)\s*:\s*(.*)$"""
    val pattern: Pattern = Pattern.compile(regex)
    val matcher = pattern.matcher(logLine)

    if (matcher.find()) {
        val timeString: String = matcher.group(1)
        val thread: String = matcher.group(2)
        val logLevelChar: String = matcher.group(3)
        val logTag: String = matcher.group(4).trim()
        val logContent: String = matcher.group(5).trim()

        val formatter: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS")
        val time: LocalDateTime = try {
            LocalDateTime.parse(timeString, formatter)
        } catch (e: Exception) {
            println("警告：无法解析时间戳 '$timeString' 在行: $logLine")
            return null
        }

        return LogEntry(time, thread, logLevelChar, logTag, logContent)
    } else {
        return null
    }
}

// 3. 辅助函数：将日志级别字符映射到完整名称
fun mapLogLevel(levelChar: String): String {
    return when (levelChar) {
        "I" -> "INFO"
        "E" -> "ERROR"
        "W" -> "WARN"
        "D" -> "DEBUG"
        "V" -> "VERBOSE"
        else -> levelChar
    }
}

// 4. 辅助函数：转义 JSON 字符串中的特殊字符
fun escapeJsonString(input: String): String {
    val builder = StringBuilder()
    for (char in input) {
        when (char) {
            '"' -> builder.append("\\\"")
            '\\' -> builder.append("\\\\")
            '\b' -> builder.append("\\b")
            '\n' -> builder.append("\\n")
            '\r' -> builder.append("\\r")
            '\t' -> builder.append("\\t")
            else -> builder.append(char)
        }
    }
    return builder.toString()
}

// --- 脚本执行逻辑开始 ---

// 输入文件路径仍然可以保持硬编码，或者根据需要改为相对路径或命令行参数
val inputFilePath: String = "3137563127605485056.zip.txt"
if (inputFilePath.isEmpty()) {
    throw Exception("请输入inputFilePath地址")
}

// *** 修改点：输出文件路径改为相对路径 (仅文件名) ***
val outputFileName: String = "outputLog.json" // 可以自定义文件名
// ****************************************************

val inputFile = File(inputFilePath)
// 使用相对路径创建输出文件对象
val outputFile = File(outputFileName)


if (!inputFile.exists() || !inputFile.canRead()) {
    println("错误：无法读取输入文件 '$inputFilePath'。请检查文件是否存在且具有读取权限。")
    kotlin.system.exitProcess(1)
}

println("开始解析文件: $inputFilePath")
println("---")

val jsonStringBuilder = StringBuilder()
var isFirstMessage = true
var lineNumber: Int = 0
var parsedCount: Int = 0
var skippedCount: Int = 0

val beijingZoneOffset: ZoneOffset = ZoneOffset.ofHours(8)

jsonStringBuilder.append("{\n  \"logcatMessages\": [\n")

try {
    inputFile.forEachLine { line ->
        lineNumber++
        val parsedEntry: LogEntry? = parseLogLine(line)

        if (parsedEntry != null) {
            parsedCount++

            if (!isFirstMessage) {
                jsonStringBuilder.append(",\n")
            } else {
                isFirstMessage = false
            }

            val epochSeconds: Long = parsedEntry.time.toEpochSecond(beijingZoneOffset)
            val logLevelString = mapLogLevel(parsedEntry.logLevel)
            val escapedTag = escapeJsonString(parsedEntry.logTag)
            val combinedMessage = "Thread = ${parsedEntry.thread}  content = ${parsedEntry.logContent}"
            val escapedMessage = escapeJsonString(combinedMessage)

            jsonStringBuilder.append("    {\n")
            jsonStringBuilder.append("      \"header\": {\n")
            jsonStringBuilder.append("        \"logLevel\": \"${logLevelString}\",\n")
            jsonStringBuilder.append("        \"pid\": 0,\n")
            jsonStringBuilder.append("        \"tid\": 0,\n")
            jsonStringBuilder.append("        \"applicationId\": \"com.interfun.buz\",\n")
            jsonStringBuilder.append("        \"processName\": \"com.interfun.buz\",\n")
            jsonStringBuilder.append("        \"tag\": \"${escapedTag}\",\n")
            jsonStringBuilder.append("        \"timestamp\": {\n")
            jsonStringBuilder.append("          \"seconds\": ${epochSeconds},\n")
            jsonStringBuilder.append("          \"nanos\": 0\n")
            jsonStringBuilder.append("        }\n")
            jsonStringBuilder.append("      },\n")
            jsonStringBuilder.append("      \"message\": \"${escapedMessage}\"\n")
            jsonStringBuilder.append("    }")

        } else {
            skippedCount++
            println("警告：第 $lineNumber 行格式不匹配或解析失败，已跳过: \"$line\"")
        }
    }

    jsonStringBuilder.append("\n  ]\n}")

    println("---")
    println("文件解析完成。共处理 $lineNumber 行，成功解析 $parsedCount 行，跳过 $skippedCount 行。")

    try {
        // 使用 outputFile 对象写入
        outputFile.writeText(jsonStringBuilder.toString())
        // 获取绝对路径用于打印确认信息
        println("成功将结果写入 JSON 文件: ${outputFile.absolutePath}")
    } catch (e: Exception) {
        println("错误：无法写入 JSON 文件 '${outputFile.absolutePath}': ${e.message}")
        e.printStackTrace()
        kotlin.system.exitProcess(1)
    }

} catch (e: FileNotFoundException) {
    println("错误：输入文件未找到 '$inputFilePath'")
    kotlin.system.exitProcess(1)
} catch (e: SecurityException) {
    println("错误：没有读取输入文件 '$inputFilePath' 的权限")
    kotlin.system.exitProcess(1)
} catch (e: Exception) {
    println("读取或解析输入文件时发生错误: ${e.message}")
    e.printStackTrace()
    kotlin.system.exitProcess(1)
}

// --- 脚本执行逻辑结束 ---