package com.interfun.buz.shared.vfvideoshared

import android.app.Application
import android.os.Build
import android.view.View
import com.interfun.buz.base.ktx.logError
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.common.di.standard.IVFVideoGeneration
import com.interfun.buz.common.utils.BuzTracker
import com.interfun.buz.common.constants.TrackConstant
import com.interfun.buz.common.utils.ViewToBitmapUtil
import com.lizhi.component.basetool.common.ApplicationUtils
import com.yibasan.lizhifm.videoedit.VideoGenerator
import com.yibasan.lizhifm.videoedit.VideoGenerator.HWAccelerationMode
import com.yibasan.lizhifm.videoedit.VideoGenerator.Observer
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException
import java.io.File
import java.net.URI
import java.net.URL
import java.security.MessageDigest
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class VFVideoGeneration @Inject constructor(val application: Application) : IVFVideoGeneration {

    private val COMMON_PARENT_DIR = "vf_video_generation"
    private val outDir by lazy { File(ApplicationUtils.context.cacheDir, "$COMMON_PARENT_DIR/vf_video_shared") }
    private val vfAudioCacheOutDir by lazy {
        File(
            ApplicationUtils.context.cacheDir, "$COMMON_PARENT_DIR/vf_audio_cache"
        )
    }
    private val vfViewCacheDir by lazy { File(ApplicationUtils.context.cacheDir, "$COMMON_PARENT_DIR/vf_view_cache") }
    private val templateDir by lazy { File(ApplicationUtils.context.cacheDir, "$COMMON_PARENT_DIR/vf_video_template") }

    companion object {
        const val TAG = "VFVideoGeneration"
    }

    private suspend fun generateVideoImp(
        templateUrl: String,
        templateMd5: String,
        duration: Int,
        audioUrl: String,
        placeHolderView: View,
    ): File {
        //file:///data/user/0/com.interfun.buz/files/ef77e66f4a322ce0a5eff8e1d144be70/5435604876334544511/3/5447107728593926204/media/embed/4f520b75066a40b89977108126c99e258_embed.aac
        logInfo(
            TAG,
            "generateVideo() called with: templateUrl = $templateUrl, templateMd5 = $templateMd5, duration = $duration, audioUrl = $audioUrl, placeHolderView = $placeHolderView"
        )

        // 初始化时间变量，用于技术埋点
        var downloadStartTime = 0L
        var downloadEndTime = 0L
        var audioDownloadStartTime = 0L
        var audioDownloadEndTime = 0L
        var videoGenStartTime = 0L
        var videoGenEndTime = 0L
        return try {
            // 判断audioUrl是否为本地文件路径
            val (audioFile, audioUrlIsFile) = if (audioUrl.startsWith("file://")) {
                try {
                    // 使用URI类标准化解析file://协议的URL，更加健壮和安全
                    val uri = URI(audioUrl)
                    val file = File(uri.path)
                    val isFile = file.exists() && file.isFile()
                    logInfo(TAG, "检测到file://协议URL，使用URI解析，实际文件路径: ${uri.path}, 文件存在: $isFile")
                    Pair(file, isFile)
                } catch (e: Exception) {
                    // URI解析失败时的降级处理
                    logInfo(TAG, "URI解析失败，使用简单字符串处理: ${e.message}")
                    val actualFilePath = audioUrl.removePrefix("file://")
                    val file = File(actualFilePath)
                    val isFile = file.exists() && file.isFile()
                    logInfo(TAG, "降级处理，实际文件路径: $actualFilePath, 文件存在: $isFile")
                    Pair(file, isFile)
                }
            } else {
                // 处理普通文件路径或网络URL
                val file = File(audioUrl)
                val isFile = file.exists() && file.isFile()
                logInfo(TAG, "处理普通路径: $audioUrl, 文件存在: $isFile")
                Pair(file, isFile)
            }

            val outVideoFile = File(
                outDir, "vf_video_$duration" + (templateUrl + templateMd5 + duration + audioUrl).hashCode() + ".mp4"
            )
            val outVideoFileTemp = File(outDir, outVideoFile.name + "_temp")

            logInfo(
                TAG, "generateVideo() called with: templateUrl = $templateUrl, templateMd5 = $templateMd5"
            )
            if (outVideoFile.exists() && outVideoFile.length() > 0) {
                return outVideoFile
            }

            //没有就创建目录，并清理缓存文件
            withContext(Dispatchers.IO) {
                outDir.deleteRecursively()
                vfAudioCacheOutDir.deleteRecursively()
                vfViewCacheDir.deleteRecursively()
                if (!outDir.exists()) {
                    logInfo(TAG, "outDir not exists")
                    outDir.mkdirs()
                }
                if (!templateDir.exists()) {
                    logInfo(TAG, "templateDir not exists")
                    templateDir.mkdirs()
                }
                if (!vfViewCacheDir.exists()) {
                    logInfo(TAG, "vfViewCacheDir not exists")
                    vfViewCacheDir.mkdirs()
                }
                if (!vfAudioCacheOutDir.exists()) {
                    logInfo(TAG, "vfAudioCacheOutDir not exists")
                    vfAudioCacheOutDir.mkdirs()
                }

            }
            val url2FileName = generateSafeFilename(templateUrl)
            logInfo(TAG, "templateUrl map ret  $templateUrl -> $url2FileName")

            // 下载模板视频文件 - 添加计时
            logInfo(TAG, " downloadFileWithLRUAndMd5 开始执行")
            downloadStartTime = System.currentTimeMillis()
            val downloadFileWithLRUAndMd5 = VFShareVideoDownloader.downloadFileWithLRUAndMd5(
                templateUrl, templateDir, url2FileName, templateMd5, 5
            )
            downloadEndTime = System.currentTimeMillis()
            logInfo(TAG, " downloadFileWithLRUAndMd5 执行完成，耗时: ${downloadEndTime - downloadStartTime}ms")

            // 下载音频文件 - 添加计时
            val finalAudioFile = if (!audioUrlIsFile) {
                logInfo(TAG, " VFAudioUrlDownloader.download 开始执行")
                audioDownloadStartTime = System.currentTimeMillis()
                val audioFile = VFAudioUrlDownloader.download(
                    audioUrl, generateSafeFilename(audioUrl), vfAudioCacheOutDir
                ).getOrThrow()
                audioDownloadEndTime = System.currentTimeMillis()
                logInfo(
                    TAG,
                    " VFAudioUrlDownloader.download 执行完成，耗时: ${audioDownloadEndTime - audioDownloadStartTime}ms"
                )
                audioFile
            } else {
                audioFile
            }

            //placeHolderView 转化成一个图片到vfViewCacheDir目录下.然后返回一个文件地址，失败抛出异常 - 添加计时
            logInfo(TAG, " ViewToBitmapUtil.convertViewToImageFile 开始执行")
            val viewConvertStartTime = System.currentTimeMillis()
            val viewPngPath = ViewToBitmapUtil.convertViewToImageFile(placeHolderView, vfViewCacheDir)
            val viewConvertEndTime = System.currentTimeMillis()
            logInfo(
                TAG,
                " ViewToBitmapUtil.convertViewToImageFile 执行完成，耗时: ${viewConvertEndTime - viewConvertStartTime}ms"
            )

            //这里之所以多一步outVideoFileTemp，是为了方便缓存。这样在同一个VF多次点击保存或者其他分享不会重复生成。
            //如果直接用outVideoFile那么会有一个问题生成到一半情况 - 添加计时
            logInfo(TAG, " executeVideoGeneration 开始执行")
            videoGenStartTime = System.currentTimeMillis()
            executeVideoGeneration(
                outVideoFile = outVideoFileTemp,
                downloadFileWithLRUAndMd5 = downloadFileWithLRUAndMd5,
                audioFile = finalAudioFile,
                duration = duration,
                viewPngPath = viewPngPath
            )
            videoGenEndTime = System.currentTimeMillis()
            logInfo(TAG, " executeVideoGeneration 执行完成，耗时: ${videoGenEndTime - videoGenStartTime}ms")
            outVideoFileTemp.renameTo(outVideoFile)
            if (!outVideoFile.exists()) {
                throw RuntimeException("executeVideoGeneration executed successfully, but file does not exist.")
            }
            if (outVideoFile.length() <= 0) {
                throw RuntimeException("executeVideoGeneration executed successfully, but file length is zero.")
            }
            outVideoFile
        } catch (e: Exception) {
            logError(TAG, t = e)
            //读取客户端技术埋点.csv 然后完成TT2025070101在这
            BuzTracker.onTechTrack {
                put(TrackConstant.KEY_EXCLUSIVE_ID, "TT2025070101")
                put(TrackConstant.KEY_EVENT_NAME, "export_voice_duration")
                put(TrackConstant.KEY_NUMBER_1, audioDownloadEndTime - audioDownloadStartTime)
                put(TrackConstant.KEY_NUMBER_2, downloadEndTime - downloadStartTime)
                put(TrackConstant.KEY_NUMBER_3, videoGenEndTime - videoGenStartTime)
            }
            throw e
        }

    }

    override suspend fun generateVideo(
        templateUrl: String,
        templateMd5: String,
        duration: Int,
        audioUrl: String,
        placeHolderView: View,
    ): File {
        return generateVideoImp(
            templateUrl = templateUrl,
            templateMd5 = templateMd5,
            duration = duration,
            audioUrl = audioUrl,
            placeHolderView = placeHolderView
        )
    }

    /**
     * 执行视频生成
     *
     * @param outVideoFile 输出视频文件
     * @param downloadFileWithLRUAndMd5 下载的模板视频文件
     * @param audioFile 音频文件
     * @param duration 音频时长（毫秒）
     * @param viewPngPath 视图转换的PNG图片路径
     * @return 生成的视频文件
     */
    private suspend fun executeVideoGeneration(
        outVideoFile: File, downloadFileWithLRUAndMd5: File, audioFile: File, duration: Int, viewPngPath: String
    ): File = suspendCancellableCoroutine { continuation ->
        val stencilGenerator = createStencilGenerator(
            outVideoFile = outVideoFile,
            downloadFileWithLRUAndMd5 = downloadFileWithLRUAndMd5,
            audioFile = audioFile,
            duration = duration,
            viewPngPath = viewPngPath,
            observer = object : Observer {
                override fun onStateChanged(gen: VideoGenerator, status: Int) {
                    logInfo(TAG, "onStateChanged: status = $status")
                }

                override fun onProgress(gen: VideoGenerator, progress: Float) {
                    logInfo(TAG, "onProgress: progress = $progress")
                }

                override fun onProcessDone(gen: VideoGenerator, result: Int, report: String?) {
                    logInfo(TAG, "onProcessDone: result = $result, report = $report")
                    try {
                        //别问。音频要求。他们的回调不能再次调他们 API
                        GlobalScope.launch {
                            gen.release()
                        }
                        if (result == 0) {
                            continuation.resume(outVideoFile)
                        } else {
                            continuation.resumeWithException(
                                RuntimeException("Video generation failed with result: $result, report: $report")
                            )
                        }
                    } catch (e: Exception) {
                        continuation.resumeWithException(e)
                    }
                }
            })

        continuation.invokeOnCancellation {
            try {
                GlobalScope.launch {
//                stencilGenerator.cancel()
                    stencilGenerator.release()
                }
                logInfo(TAG, "Video generation cancelled and resources cleaned up")
            } catch (e: Exception) {
                logInfo(TAG, "Error during cancellation cleanup: ${e.message}")
            }
        }

        stencilGenerator.start()
    }

    /**
     * 创建并配置StencilGenerator实例
     *
     * @param outVideoFile 输出视频文件
     * @param downloadFileWithLRUAndMd5 下载的模板视频文件
     * @param audioFile 音频文件
     * @param duration 音频时长（毫秒）
     * @param viewPngPath 视图转换的PNG图片路径
     * @param observer 视频生成观察者回调
     * @return 配置好的VideoGenerator实例
     */
    private fun createStencilGenerator(
        outVideoFile: File,
        downloadFileWithLRUAndMd5: File,
        audioFile: File,
        duration: Int,
        viewPngPath: String,
        observer: Observer
    ): VideoGenerator {
        logInfo(TAG, "createStencilGenerator() called")

        val stencilBuilder = VideoGenerator.StencilBuilder()
        stencilBuilder.setOutputFile(outVideoFile.absolutePath)
        stencilBuilder.setInputBGVideoFilePath(downloadFileWithLRUAndMd5.absolutePath)
        stencilBuilder.setInputVoiceFilePath(audioFile.absolutePath)
        stencilBuilder.setVoiceFileDurationMs(duration.toLong())
        stencilBuilder.setOutputMaxPixelCnt(1080 * 1920)
        //如果小于等于 Android 10那么启用HWAccelerationMode.forceSoftware，否则启用自动
        if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.Q) {
            // Android 10 (API level 29) 及以下版本使用软件加速
            stencilBuilder.setHWAccelerationMode(HWAccelerationMode.forceSoftware)
            logInfo(TAG, "设备Android版本: ${Build.VERSION.SDK_INT} <= 29, 使用软件加速模式")
        } else {
            // Android 10 以上版本使用自动加速
            stencilBuilder.setHWAccelerationMode(HWAccelerationMode.auto)
            logInfo(TAG, "设备Android版本: ${Build.VERSION.SDK_INT} > 29, 使用自动加速模式")
        }

        stencilBuilder.setInputTextImgFilePath(viewPngPath)

        return stencilBuilder.build(observer)
    }

    /**
     * 这里采用了一个不严谨方式。URL哈希+文件名截取，这种方式可能有重复的可能性，但是要上数据库那些实在太重了。
     * 出现了哈希命中+URL 名字也一样这个概率几乎可以忽略不计。
     */
    internal fun generateSafeFilename(url: String): String {
        val hash = MessageDigest.getInstance("SHA-256").digest(url.toByteArray()).joinToString("") { "%02x".format(it) }
            .take(10) // 只取前 10 位，减少长度

        val parsedUrl = URL(url)
        val originalName = parsedUrl.path.substringAfterLast("/", "file")

        // 限制最终文件名长度不超过 100 字符
        val safeName = if (originalName.length > 80) {
            originalName.takeLast(80)
        } else originalName

        return "${hash}_$safeName"
    }


}
