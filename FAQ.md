### 性能问题
- Compose performance profile `./gradlew    app:assembleDebug -Pandroidx.enableComposeCompilerMetrics=true -Pandroidx.enableComposeCompilerReports=true`
- room does not generate scheme: pls run `./gradlew  clean  app:assembleDebug --no-build-cache`

### 无法编译
问题1: failed to xxx.aar to math attributes or zip END header not found
控制台执行下面的命令安装 lfs
`brew install git-lfs && git lfs install && git lfs pull`
