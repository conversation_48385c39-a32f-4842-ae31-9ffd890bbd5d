import requests
import json
import sys
import os
import shutil
from lxml import etree
import xml.dom.minidom
import subprocess

tmpDir = "temp"
if os.path.exists("./" + tmpDir):
    shutil.rmtree("./" + tmpDir)

os.makedirs("./" + tmpDir)
languageFp = open("./localizable_config.json")
languageEntity = json.load(languageFp)
languageFp.close()
removeDefaultLanguage = languageEntity["removeDefaultLanguage"]
business = languageEntity["business"]
languageDic = languageEntity["languages"]
languageResourceModuleName = languageEntity["languageResourceModuleName"]
languageFileName = languageEntity["fileName"]
defaultLanguage = languageEntity["default"]
defaultTargetPath = "./" + languageResourceModuleName + "/src/main/res/values"
defaultXmlPath = "./" + tmpDir + "/default.xml"
businessTag = sys.argv[1]
specialString = "~!@#$%^&*()+-*/<>,.[]\/"

def fetchDataFromLokalise():
    url = "https://api.lokalise.com/api2/projects/5348674867eccf20d66141.84358942:branch/keys?include_translations=1&filter_tags=" + businessTag

    headers = {
        "accept": "application/json",
        "X-Api-Token": "0ca1d12bc926a9273ebc3843dad0fb233beef8b4"
    }

    response = requests.get(url, headers=headers)
    return response.text

def parseData(data):
    result = json.loads(data)
    keys = result.get("keys")
    if not keys:
        return

    language_maps = {}

    for key_and_value in keys:
        key_name = key_and_value.get("key_name", {}).get("android")
        if not key_name:
            continue

        translations = key_and_value.get("translations")
        if not translations:
            continue

        for translation_item in translations:
            language = translation_item.get("language_iso")
            value = translation_item.get("translation")

            reflectLanguage = getCorrectIsoLanguage(language)
            if reflectLanguage not in language_maps:
                language_maps[reflectLanguage] = []

            language_maps[reflectLanguage].append((key_name, value))
    for language, pairs in language_maps.items():
        print("language = " + language)
        for key, value in pairs:
            print(f"key = {key}, value = {value}")
    return language_maps



def getCorrectIsoLanguage(languageIsoCode):
    if languageIsoCode == "zh_CN":
        return "zh"
    elif languageIsoCode == "zh_Hant_TW":
        return "zh-Hant"
    elif languageIsoCode == "id":
        return "in"
    else:
        return languageIsoCode

def readXml(language, androidModule, fileName, defaultLanguage):
    if language == defaultLanguage:
        filePath = "./" + androidModule + "/src/main/res/values/" + fileName
    else:
        filePath = "./" + androidModule + "/src/main/res/values-" + language + "/" + fileName

    print("\033[32mstart load language.xml:" + language + " address:" + filePath + "\033[0m")

    if os.path.exists(filePath):
        tree = etree.parse(filePath)
    else:
        tree = etree.parse(defaultXmlPath)
        # tree.setroot(tree)

    print("\033[32mend load language.xml:" + language + " address:" + filePath + "\033[0m \033[34mSuccess\033[0m")
    return tree


def makeDefaultXml():
    try:
        fp = open(defaultXmlPath, "w")
        fp.write("<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n"
             "<resources xmlns:tools=\"http://schemas.android.com/tools\" tools:ignore=\"MissingTranslation,TypographyEllipsis\">\n"
             "</resources>")
        fp.close()
    except Exception  as err:
        print("\033[31makeDefaultXml error + " + str(err) + "\033[0m")
        os.exit(1)

def replace_first_special_letter(string):
    if len(string) > 0:
        first_letter = string[0]
        if first_letter == '@':
            new_string = '\\@' + string[1:]
            return new_string
        return string
    else:
        return string

def handleLanguage(language, translations, tree, filepath):
    parent = tree.xpath("//resources")[0]
    contents = parent.xpath("//string")
    etree.indent(parent, space="    ")

    for content in contents:
        if content.attrib.has_key("version") and content.attrib["version"] == businessTag:
            parent.remove(content)
        content.tail = None

    for key,value in translations:
        node = etree.SubElement(parent, "string")
        print(value)
        value = value.replace("\\\'", "\'")
        value = value.replace("\'", "\\\'")
        value = replace_first_special_letter(value)
        node.text = value.replace("\"", "\\\"")
        node.attrib["name"] = key
        node.attrib["version"] = businessTag

    result = etree.tostring(tree.getroot(),
                        pretty_print=True,
                        xml_declaration=True,
                        encoding='UTF-8')

    result = xml.dom.minidom.parseString(result).toprettyxml()
    fp = open(filepath, "w")
    fp.write(result)
    fp.close()


def xmlKeys(tree):
    keyValues = {}
    parent = tree.xpath("//resources")[0]
    contents = parent.xpath("//string")

    for content in contents:
        if content.attrib.has_key("version") and content.attrib["version"] == businessTag:
            parent.remove(content)
            continue

        keyValues[content.attrib["name"]] = ""

    return keyValues


def writeTranslationInXml(language, translations):
    print("language: " + language + " translations = " + str(translations))

    try:
           filepath = "./" + tmpDir + "/" + language + "-language.xml"
           tree = readXml(languageDic[language], languageResourceModuleName, languageFileName, defaultLanguage)
           treeKeys = xmlKeys(tree)

           for key,value in translations:
               for i in specialString:
                   if i in key:
                       raise Exception("contain invalidate key : " + key)

               if key in treeKeys:
                   raise Exception("duplicate key : " + key)

           handleLanguage(language, translations, tree, filepath)
    except Exception as err:
           print("\033[31mdeal language： " + language + ", error + " + str(err) + "\033[0m")
           os.exit(1)


originalData = fetchDataFromLokalise()
allLanguage= parseData(originalData)

for language, translations in allLanguage.items():
    writeTranslationInXml(language, translations)


if os.path.exists(defaultTargetPath) == False:
    os.makedirs(defaultTargetPath)

for language in languageDic.keys():
    try:
        filepath = "./" + tmpDir + "/" + language + "-language.xml"
        targetPath = "./" + languageResourceModuleName + "/src/main/res/values-" + languageDic[language]

        if language == defaultLanguage:
            subprocess.Popen("cp " + filepath + " " + defaultTargetPath + "/" + languageFileName, shell=True)

        if removeDefaultLanguage == "true" and language == defaultLanguage:
            continue

        if os.path.exists(targetPath) == False:
            os.makedirs(targetPath)

        subprocess.Popen("mv " + filepath + " " + targetPath + "/" + languageFileName, shell=True)
    except:
        print("\033[31mdeal language： " + language + ", error\033[0m")
        os.exit(1)
