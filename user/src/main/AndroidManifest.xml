<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <application>
        <activity android:name=".view.activity.AboutActivity"
            android:configChanges="keyboard|keyboardHidden|screenSize|orientation"
            android:exported="false"
            android:screenOrientation="portrait"/>
        <activity android:name=".view.activity.AiMarketActivity"
            android:configChanges="keyboard|keyboardHidden|screenSize|orientation"
            android:exported="false"
            android:screenOrientation="portrait"/>
        <activity android:name=".view.activity.BlockListActivity"
            android:configChanges="keyboard|keyboardHidden|screenSize|orientation"
            android:exported="false"
            android:screenOrientation="portrait"/>
        <activity android:name=".view.activity.AccountActivity"
            android:configChanges="keyboard|keyboardHidden|screenSize|orientation"
            android:exported="false"
            android:screenOrientation="portrait"/>
        <activity android:name=".view.activity.DeleteAccountActivity"
            android:configChanges="keyboard|keyboardHidden|screenSize|orientation"
            android:exported="false"
            android:screenOrientation="portrait"/>
        <activity android:name=".view.activity.UpdateProfileActivity"
            android:configChanges="keyboard|keyboardHidden|screenSize|orientation"
            android:exported="false"
            android:screenOrientation="portrait"/>
        <activity android:name=".view.activity.EditUserInfoActivity"
            android:configChanges="keyboard|keyboardHidden|screenSize|orientation"
            android:windowSoftInputMode="adjustResize"
            android:exported="false"
            android:screenOrientation="portrait"/>
        <activity android:name=".view.activity.LanguageSettingActivity"
            android:configChanges="keyboard|keyboardHidden|screenSize|orientation"
            android:exported="false"
            android:screenOrientation="portrait"/>
        <activity android:name=".view.activity.SettingActivity"
            android:configChanges="keyboard|keyboardHidden|screenSize|orientation"
            android:exported="false"
            android:screenOrientation="portrait"/>
        <activity android:name=".view.activity.NotificationSettingActivity"
            android:configChanges="keyboard|keyboardHidden|screenSize|orientation"
            android:exported="false"
            android:screenOrientation="portrait"/>
        <activity android:name=".view.activity.MediaDownloadSettingActivity"
            android:configChanges="keyboard|keyboardHidden|screenSize|orientation"
            android:exported="false"
            android:screenOrientation="portrait"/>
        <activity android:name=".view.activity.StorageSettingActivity"
            android:configChanges="keyboard|keyboardHidden|screenSize|orientation"
            android:exported="false"
            android:screenOrientation="portrait"/>
        <activity android:name=".view.activity.AlertSoundActivity"
            android:configChanges="keyboard|keyboardHidden|screenSize|orientation"
            android:exported="false"
            android:screenOrientation="portrait"/>
        <activity android:name=".view.activity.NotificationActivity"
            android:configChanges="keyboard|keyboardHidden|screenSize|orientation"
            android:exported="false"
            android:screenOrientation="portrait"/>
    </application>
</manifest>