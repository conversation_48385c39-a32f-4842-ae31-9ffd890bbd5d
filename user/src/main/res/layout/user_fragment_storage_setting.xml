<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/common_secondary_background"
    android:orientation="vertical">

    <Space
        android:id="@+id/spaceStatusBar"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_height="44dp" />


    <RelativeLayout
        android:id="@+id/titleBar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/title_bar_height"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/spaceStatusBar">

        <com.interfun.buz.common.widget.view.IconFontTextView
            android:id="@+id/iftvLeftBack"
            android:layout_width="64dp"
            android:layout_height="match_parent"
            android:layout_alignParentStart="true"
            android:gravity="center"
            app:autoRTL="true"
            android:text="@string/ic_back"
            android:textColor="@color/text_white_main"
            android:textSize="24sp" />

        <TextView
            android:id="@+id/tvTitle"
            style="@style/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:gravity="center"
            android:text="@string/storage_title"
            android:textColor="@color/text_white_main" />

    </RelativeLayout>

    <ScrollView
        android:id="@+id/scrollView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/titleBar"
        app:layout_constraintBottom_toBottomOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingHorizontal="20dp"
            android:paddingBottom="40dp">

            <TextView
                android:id="@+id/tvStorageTitle"
                style="@style/body"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:gravity="center_vertical"
                android:text="@string/buz_storage_usage_title"
                android:textColor="@color/text_white_secondary"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.interfun.buz.base.widget.round.RoundConstraintLayout
                android:id="@+id/roundStorageUsage"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:background="@color/overlay_white_6"
                android:padding="20dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvStorageTitle"
                app:round_radius="8dp">

                <TextView
                    android:id="@+id/tvAppSize"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/basic_primary"
                    android:textSize="32sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="321.2 MB" />

                <TextView
                    android:id="@+id/tvAppSizeDesc"
                    style="@style/body"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/buz_used_desc"
                    android:textAlignment="viewEnd"
                    android:textColor="@color/text_white_default"
                    android:textSize="14sp"
                    app:layout_constraintStart_toStartOf="@id/tvAppSize"
                    app:layout_constraintTop_toBottomOf="@id/tvAppSize" />

                <TextView
                    android:id="@+id/tvAvailableSize"
                    style="@style/body"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textAlignment="viewEnd"
                    android:textColor="@color/text_white_default"
                    android:textSize="14sp"
                    app:layout_constraintBottom_toBottomOf="@id/tvAppSize"
                    app:layout_constraintEnd_toEndOf="parent"
                    tools:text="432.32 GB" />

                <TextView
                    android:id="@+id/tvAvailableSizeDesc"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/available_size_desc"
                    android:textAlignment="viewEnd"
                    android:textColor="@color/text_white_default"
                    android:textSize="14sp"
                    app:layout_constraintEnd_toEndOf="@id/tvAvailableSize"
                    app:layout_constraintTop_toBottomOf="@id/tvAvailableSize" />

                <ProgressBar
                    android:id="@+id/progressStorageSize"
                    style="?android:attr/progressBarStyleHorizontal"
                    android:layout_width="match_parent"
                    android:layout_height="12dp"
                    android:layout_marginTop="12dp"
                    android:foregroundGravity="center"
                    android:scaleY="1"
                    android:progressDrawable="@drawable/common_progressbar_12"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tvAppSizeDesc"
                    tools:max="100"
                    tools:progress="20"
                    tools:secondaryProgress="50"/>

                <com.interfun.buz.base.widget.round.RoundView
                    android:id="@+id/rvBuzProgressLegend"
                    android:layout_width="8dp"
                    android:layout_height="8dp"
                    android:layout_marginTop="15dp"
                    android:background="@color/basic_primary"
                    app:layout_constraintStart_toStartOf="@+id/progressStorageSize"
                    app:layout_constraintTop_toBottomOf="@id/progressStorageSize"
                    app:round_radius="4dp" />

                <TextView
                    android:id="@+id/tvBuzProgressLegend"
                    style="@style/caption"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:text="@string/app_name"
                    android:textColor="@color/text_white_default"
                    app:layout_constraintBottom_toBottomOf="@id/rvBuzProgressLegend"
                    app:layout_constraintStart_toEndOf="@+id/rvBuzProgressLegend"
                    app:layout_constraintTop_toTopOf="@id/rvBuzProgressLegend" />

                <com.interfun.buz.base.widget.round.RoundView
                    android:id="@+id/rvOtherProgressLegend"
                    android:layout_width="8dp"
                    android:layout_height="8dp"
                    android:layout_marginStart="24dp"
                    android:background="@color/color_929292"
                    app:layout_constraintBottom_toBottomOf="@id/rvBuzProgressLegend"
                    app:layout_constraintStart_toEndOf="@+id/tvBuzProgressLegend"
                    app:layout_constraintTop_toTopOf="@id/rvBuzProgressLegend"
                    app:round_radius="4dp" />

                <TextView
                    android:id="@+id/tvOtherProgressLegend"
                    style="@style/caption"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:text="@string/storage_apps_and_other_progress_legend"
                    android:textColor="@color/text_white_default"
                    android:lines="1"
                    android:ellipsize="end"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintBottom_toBottomOf="@id/rvOtherProgressLegend"
                    app:layout_constraintStart_toEndOf="@id/rvOtherProgressLegend"
                    app:layout_constraintTop_toTopOf="@id/rvOtherProgressLegend" />


            </com.interfun.buz.base.widget.round.RoundConstraintLayout>

            <TextView
                android:id="@+id/tvCacheUsageTitle"
                style="@style/body"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:layout_marginTop="48dp"
                android:gravity="center_vertical"
                android:text="@string/cache_usage_title"
                android:textColor="@color/text_white_secondary"
                app:layout_constraintBottom_toTopOf="@id/roundCacheUsage"
                app:layout_constraintStart_toStartOf="@id/roundStorageUsage"
                app:layout_constraintTop_toBottomOf="@id/roundStorageUsage"
                app:layout_constraintVertical_chainStyle="packed" />

            <TextView
                android:id="@+id/tvCacheUsageSize"
                style="@style/body"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:gravity="center_vertical"
                android:textColor="@color/basic_primary"
                app:layout_constraintEnd_toEndOf="@id/roundStorageUsage"
                app:layout_constraintTop_toTopOf="@id/tvCacheUsageTitle"
                tools:text="221.1 MB" />

            <com.interfun.buz.base.widget.round.RoundConstraintLayout
                android:id="@+id/roundCacheUsage"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:background="@color/overlay_white_6"
                android:padding="20dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvCacheUsageTitle"
                app:round_radius="8dp">

                <TextView
                    android:id="@+id/tvCacheUsageDesc"
                    style="@style/description"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@string/clear_cache_desc"
                    android:textColor="@color/text_white_secondary"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toTopOf="@id/btnClearCache"/>

                <com.interfun.buz.common.widget.button.CommonButton
                    android:id="@+id/btnClearCache"
                    style="@style/button"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tvCacheUsageDesc"
                    app:text="@string/clear_cache"
                    app:type="primary_medium" />

            </com.interfun.buz.base.widget.round.RoundConstraintLayout>

            <TextView
                android:id="@+id/tvAutoCacheRemovedTitle"
                style="@style/body"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:layout_marginTop="48dp"
                android:gravity="center_vertical"
                android:text="@string/auto_remove_cached_files_title"
                android:textColor="@color/text_white_secondary"
                app:layout_constraintStart_toStartOf="@id/roundStorageUsage"
                app:layout_constraintTop_toBottomOf="@id/roundCacheUsage" />

            <com.interfun.buz.base.widget.round.RoundConstraintLayout
                android:id="@+id/roundAutoCacheRemoved"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:background="@color/overlay_white_6"
                android:padding="20dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvAutoCacheRemovedTitle"
                app:round_radius="8dp">

                <TextView
                    android:id="@+id/tvRetentionPeriodTitle"
                    style="@style/body"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:text="@string/retention_period"
                    android:textColor="@color/text_white_main"
                    app:layout_constraintStart_toStartOf="@id/roundAutoCacheRemoved"
                    app:layout_constraintBottom_toBottomOf="@id/roundAutoCacheRemoved"
                    app:layout_constraintTop_toTopOf="@id/roundAutoCacheRemoved"/>

                <TextView
                    android:id="@+id/tvRetentionPeriodValue"
                    style="@style/body"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="6dp"
                    android:gravity="center_vertical"
                    tools:text="@string/auto_remove_always_keep"
                    android:textColor="@color/text_white_secondary"
                    app:layout_constraintEnd_toStartOf="@id/llSpinner"
                    app:layout_constraintBottom_toBottomOf="@id/roundAutoCacheRemoved"
                    app:layout_constraintTop_toTopOf="@id/roundAutoCacheRemoved"/>

                <LinearLayout
                    android:id="@+id/llSpinner"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <com.interfun.buz.common.widget.view.IconFontTextView
                        android:layout_width="12dp"
                        android:layout_height="12dp"
                        android:foregroundGravity="center"
                        android:text="@string/ic_arrow_up"
                        android:textSize="12sp"
                        android:textColor="@color/text_white_secondary"/>

                    <com.interfun.buz.common.widget.view.IconFontTextView
                        android:layout_width="12dp"
                        android:layout_height="12dp"
                        android:foregroundGravity="center"
                        android:text="@string/ic_arrow_down"
                        android:textSize="12sp"
                        android:textColor="@color/text_white_secondary"/>

                </LinearLayout>

            </com.interfun.buz.base.widget.round.RoundConstraintLayout>

            <TextView
                android:id="@+id/tvDescription"
                style="@style/caption"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:text="@string/auto_remove_cached_files_desc"
                android:textColor="@color/text_white_secondary"
                app:layout_constraintTop_toBottomOf="@id/roundAutoCacheRemoved"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

            <View
                android:id="@+id/viewAlertPageMask"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@color/overlay_page_mask"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>