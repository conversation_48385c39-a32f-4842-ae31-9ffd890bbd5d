<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/clRoot"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/common_secondary_background"
    tools:ignore="SpUsage">

    <androidx.legacy.widget.Space
        android:id="@+id/spaceStatusBar"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_height="@dimen/status_bar_height" />

    <!--标题栏区域-->
    <androidx.legacy.widget.Space
        android:id="@+id/spaceTitleBar"
        android:layout_width="0dp"
        android:layout_height="@dimen/title_bar_height"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/spaceStatusBar" />

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftvBack"
        style="@style/iconfont_24"
        android:layout_marginStart="4dp"
        app:autoRTL="true"
        android:text="@string/ic_back"
        android:textColor="@color/text_white_main"
        app:layout_constraintBottom_toBottomOf="@+id/spaceTitleBar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/spaceTitleBar" />

    <TextView
        android:id="@+id/tvTitle"
        style="@style/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/msg_setting"
        android:textColor="@color/text_white_main"
        app:layout_constraintBottom_toBottomOf="@+id/spaceTitleBar"
        app:layout_constraintEnd_toEndOf="@+id/spaceTitleBar"
        app:layout_constraintStart_toStartOf="@+id/spaceTitleBar"
        app:layout_constraintTop_toTopOf="@+id/spaceTitleBar" />

    <ScrollView
        android:id="@+id/scrollView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:scrollbars="none"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/spaceTitleBar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingHorizontal="20dp"
            android:paddingBottom="40dp">


            <TextView
                android:id="@+id/tvAPMAlertTitle"
                style="@style/body"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:text="@string/auto_play_msg_alert_uppercase"
                android:textColor="@color/text_white_secondary"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/clAPMAlert"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:background="@drawable/common_rect_overlay_white_6_radius_12"
                app:layout_constraintTop_toBottomOf="@+id/tvAPMAlertTitle">

                <com.interfun.buz.common.widget.view.CustomSwitchCompat
                    android:id="@+id/switchAPMAlert"
                    android:layout_width="48dp"
                    android:layout_height="30dp"
                    android:layout_marginTop="9dp"
                    android:layout_marginEnd="20dp"
                    android:layout_marginBottom="9dp"
                    android:thumb="@drawable/common_switch_thumb"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:track="@drawable/common_switch_track" />

                <TextView
                    android:id="@+id/tvAPMAlert"
                    style="@style/main_body"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginVertical="14.5dp"
                    android:layout_marginStart="20dp"
                    android:layout_marginEnd="16dp"
                    android:text="@string/auto_play_msg_alert"
                    android:textColor="@color/text_white_main"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/switchAPMAlert"
                    app:layout_constraintTop_toTopOf="@id/switchAPMAlert"
                    app:layout_constraintBottom_toBottomOf="@id/switchAPMAlert"/>

                <View
                    android:id="@+id/vAutoPlaySplitLine"
                    android:layout_width="0dp"
                    android:layout_height="0.5dp"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="9dp"
                    android:background="@color/overlay_grey_26"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/switchAPMAlert"/>

                <TextView
                    android:id="@+id/tvAlertSoundTitle"
                    style="@style/main_body"
                    android:textColor="@color/text_white_main"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/tvAlertSound"
                    app:layout_constraintTop_toTopOf="@id/tvAlertSound"
                    app:layout_constraintBottom_toBottomOf="@id/tvAlertSound"
                    android:layout_marginStart="20dp"
                    android:layout_marginEnd="16dp"
                    android:layout_marginVertical="14.5dp"
                    app:layout_constrainedWidth="true"
                    android:text="@string/alert_sound"
                    />

                <View
                    android:id="@+id/alertSoundView"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/vAutoPlaySplitLine"/>

                <TextView
                    android:id="@+id/tvAlertSound"
                    style="@style/body"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="6dp"
                    android:layout_marginTop="16dp"
                    android:layout_marginBottom="16dp"
                    android:ellipsize="end"
                    android:gravity="end"
                    android:textColor="@color/text_white_secondary"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/iftvAlertSound"
                    app:layout_constraintTop_toBottomOf="@+id/vAutoPlaySplitLine"
                    tools:text="Pops Up" />

                <com.interfun.buz.common.widget.view.IconFontTextView
                    android:id="@+id/iftvAlertSound"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="20dp"
                    app:autoRTL="true"
                    android:text="@string/ic_arrow_right"
                    android:textColor="@color/text_white_secondary"
                    android:textSize="16sp"
                    app:layout_constraintBottom_toBottomOf="@id/tvAlertSound"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/tvAlertSound" />


            </androidx.constraintlayout.widget.ConstraintLayout>


            <TextView
                android:id="@+id/tvMessageTranscription"
                style="@style/body"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="30dp"
                android:text="@string/msg_transcription"
                android:textColor="@color/text_white_secondary"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/clAPMAlert" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/clMessageTranscription"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:background="@drawable/common_rect_overlay_white_6_radius_12"
                android:paddingBottom="9dp"
                app:layout_constraintTop_toBottomOf="@+id/tvMessageTranscription">


                <com.interfun.buz.common.widget.view.CustomSwitchCompat
                    android:id="@+id/switchSmartTranscription"
                    android:layout_width="48dp"
                    android:layout_height="30dp"
                    android:layout_marginTop="9dp"
                    android:layout_marginEnd="20dp"
                    android:layout_marginBottom="9dp"
                    android:thumb="@drawable/common_switch_thumb"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:track="@drawable/common_switch_track" />

                <TextView
                    android:id="@+id/tvONTranscription"
                    style="@style/main_body"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginVertical="14.5dp"
                    android:layout_marginStart="20dp"
                    android:layout_marginEnd="16dp"
                    android:text="@string/auto_transcribe"
                    android:textColor="@color/text_white_main"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintBottom_toBottomOf="@id/switchSmartTranscription"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/switchSmartTranscription"
                    app:layout_constraintTop_toTopOf="@id/switchSmartTranscription" />

                <com.interfun.buz.common.widget.view.CustomSwitchCompat
                    android:id="@+id/switchTranscriptionPreview"
                    android:layout_width="48dp"
                    android:layout_height="30dp"
                    android:layout_marginTop="18dp"
                    android:layout_marginEnd="20dp"
                    android:thumb="@drawable/common_switch_thumb"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/switchSmartTranscription"
                    app:track="@drawable/common_switch_track" />

                <TextView
                    android:id="@+id/tvTranscriptionPreview"
                    style="@style/main_body"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@string/transcription_preview"
                    android:textColor="@color/text_white_main"
                    android:layout_marginEnd="16dp"
                    app:layout_constraintBottom_toBottomOf="@id/switchTranscriptionPreview"
                    app:layout_constraintStart_toStartOf="@id/tvONTranscription"
                    app:layout_constraintEnd_toStartOf="@+id/switchTranscriptionPreview"
                    app:layout_constraintTop_toTopOf="@id/switchTranscriptionPreview" />

                <View
                    android:id="@+id/vSplitLineAsr"
                    android:layout_width="0dp"
                    android:layout_height="0.4dp"
                    android:background="@color/overlay_grey_26"
                    app:layout_constraintBottom_toTopOf="@id/switchTranscriptionPreview"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@id/tvONTranscription"
                    app:layout_constraintTop_toBottomOf="@id/switchSmartTranscription" />

                <androidx.constraintlayout.widget.Group
                    android:id="@+id/groupTranscriptionPreview"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:constraint_referenced_ids="switchTranscriptionPreview,tvTranscriptionPreview,vSplitLineAsr" />


            </androidx.constraintlayout.widget.ConstraintLayout>


            <TextView
                android:id="@+id/tvONTranscriptionTips"
                style="@style/description"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:text="@string/transcription_auto_tips"
                android:textColor="@color/text_white_secondary"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/clMessageTranscription" />

            <TextView
                android:id="@+id/tvSyncDNDTitle"
                style="@style/body"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="30dp"
                android:text="@string/dnd_my_profile_item_title"
                android:textColor="@color/text_white_secondary"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvONTranscriptionTips" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/clDNDItem"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:background="@drawable/common_rect_overlay_white_6_radius_12"
                app:layout_constraintTop_toBottomOf="@+id/tvSyncDNDTitle">


                <com.interfun.buz.common.widget.view.CustomSwitchCompat
                    android:id="@+id/switchDND"
                    android:layout_width="48dp"
                    android:layout_height="30dp"
                    android:thumb="@drawable/common_switch_thumb"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    android:layout_marginEnd="20dp"
                    android:layout_marginTop="9dp"
                    android:layout_marginBottom="9dp"
                    app:track="@drawable/common_switch_track" />

                <TextView
                    android:id="@+id/tvDNDDescription"
                    style="@style/main_body"
                    android:textColor="@color/text_white_main"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    android:layout_marginStart="20dp"
                    android:layout_marginVertical="14.5dp"
                    android:layout_marginEnd="16dp"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintEnd_toStartOf="@+id/switchDND"
                    android:text="@string/dnd_my_profile_item_content"
                    />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <TextView
                android:id="@+id/tvBottomDNDTips"
                style="@style/description"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:text="@string/buz_auto_play_by_silent_tips"
                android:textColor="@color/text_white_secondary"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/clDNDItem" />

            <TextView
                android:id="@+id/tvNotificationSettingTitle"
                style="@style/body"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="30dp"
                android:text="@string/setting_notification_setting_title"
                android:textColor="@color/text_white_secondary"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvBottomDNDTips" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/clNotificationSettingItem"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:background="@drawable/common_rect_overlay_white_6_radius_12"
                app:layout_constraintTop_toBottomOf="@+id/tvNotificationSettingTitle">


                <com.interfun.buz.common.widget.view.IconFontTextView
                    android:id="@+id/iftvNotificationSetting"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="20dp"
                    app:autoRTL="true"
                    android:text="@string/ic_arrow_right"
                    android:textColor="@color/text_white_secondary"
                    android:textSize="16sp"
                    app:layout_constraintBottom_toBottomOf="@id/tvNotificationSettingDescription"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/tvNotificationSettingDescription" />

                <View
                    android:id="@+id/viewNotificationProblemGuidanceRedDot"
                    android:layout_width="6dp"
                    android:layout_height="6dp"
                    android:layout_marginStart="8dp"
                    android:background="@drawable/common_oval_red_point"
                    android:visibility="gone"
                    tools:visibility="visible"
                    app:layout_constraintBottom_toBottomOf="@id/tvNotificationSettingDescription"
                    app:layout_constraintStart_toEndOf="@id/tvNotificationSettingDescription"
                    app:layout_constraintTop_toTopOf="@id/tvNotificationSettingDescription" />

                <TextView
                    android:id="@+id/tvNotificationSettingDescription"
                    style="@style/main_body"
                    android:textColor="@color/text_white_main"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    android:layout_marginStart="20dp"
                    android:layout_marginVertical="14.5dp"
                    android:layout_marginEnd="16dp"
                    app:layout_constrainedWidth="true"
                    android:text="@string/setting_notification_setting_content"
                    />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <TextView
                android:id="@+id/tvBottomNotificationSettingTips"
                style="@style/description"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:text="@string/setting_notification_setting_tips"
                android:textColor="@color/text_white_secondary"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/clNotificationSettingItem" />

            <TextView
                android:id="@+id/tvInAppNotification"
                style="@style/body"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="30dp"
                android:text="@string/in_app_notifications"
                android:textColor="@color/text_white_secondary"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvBottomNotificationSettingTips" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/clEditAPMMsg"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:background="@drawable/common_rect_overlay_white_6_radius_12"
                android:paddingBottom="9dp"
                app:layout_constraintTop_toBottomOf="@+id/tvInAppNotification">


                <com.interfun.buz.common.widget.view.CustomSwitchCompat
                    android:id="@+id/switchIANSounds"
                    android:layout_width="48dp"
                    android:layout_height="30dp"
                    android:layout_marginTop="9dp"
                    android:layout_marginEnd="20dp"
                    android:thumb="@drawable/common_switch_thumb"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:track="@drawable/common_switch_track" />

                <TextView
                    android:id="@+id/tvIANSounds"
                    style="@style/main_body"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginEnd="16dp"
                    android:text="@string/sounds"
                    android:textColor="@color/text_white_main"
                    app:layout_constraintEnd_toStartOf="@+id/switchIANSounds"
                    app:layout_constraintBottom_toBottomOf="@id/switchIANSounds"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@id/switchIANSounds" />

                <com.interfun.buz.common.widget.view.CustomSwitchCompat
                    android:id="@+id/switchIANVibrate"
                    android:layout_width="48dp"
                    android:layout_height="30dp"
                    android:layout_marginTop="18dp"
                    android:layout_marginEnd="20dp"
                    android:thumb="@drawable/common_switch_thumb"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/switchIANSounds"
                    app:track="@drawable/common_switch_track" />

                <TextView
                    android:id="@+id/tvIANVibrate"
                    style="@style/main_body"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@string/vibration"
                    android:textColor="@color/text_white_main"
                    android:layout_marginEnd="16dp"
                    app:layout_constraintBottom_toBottomOf="@id/switchIANVibrate"
                    app:layout_constraintStart_toStartOf="@id/tvIANSounds"
                    app:layout_constraintEnd_toStartOf="@+id/switchIANVibrate"
                    app:layout_constraintTop_toTopOf="@id/switchIANVibrate" />

                <View
                    android:id="@+id/vSplitLine"
                    android:layout_width="0dp"
                    android:layout_height="0.4dp"
                    android:background="@color/overlay_grey_26"
                    app:layout_constraintBottom_toTopOf="@id/switchIANVibrate"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@id/tvIANSounds"
                    app:layout_constraintTop_toBottomOf="@id/switchIANSounds" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>