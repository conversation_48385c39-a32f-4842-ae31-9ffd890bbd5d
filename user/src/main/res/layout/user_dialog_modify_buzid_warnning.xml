<?xml version="1.0" encoding="utf-8"?>
<com.interfun.buz.base.widget.round.RoundConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/clRoot"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/overlay_grey_10"
    android:paddingBottom="14dp"
    app:round_top_left_radius="@dimen/bottom_sheet_dialog_top_radius"
    app:round_top_right_radius="@dimen/bottom_sheet_dialog_top_radius"
    tools:layout_gravity="bottom">

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideStart"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_begin="40dp" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideEnd"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_end="40dp" />

    <Space
        android:id="@+id/dragSpace"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:tag="@string/common_tag_drag_view"
        app:layout_constraintBottom_toTopOf="@+id/tvTitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/ivArrow"
        android:layout_width="wrap_content"
        android:layout_height="34dp"
        android:paddingHorizontal="20dp"
        android:src="@drawable/feedback_ic_arrow"
        app:layout_constraintEnd_toEndOf="@+id/guideEnd"
        app:layout_constraintStart_toStartOf="@+id/guideStart"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvTitle"
        style="@style/large_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:gravity="center"
        android:textColor="@color/text_white_main"
        app:layout_constraintEnd_toEndOf="@+id/guideEnd"
        app:layout_constraintStart_toStartOf="@+id/guideStart"
        app:layout_constraintTop_toBottomOf="@+id/ivArrow" />


    <TextView
        android:id="@+id/tvContent"
        style="@style/regular"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="40dp"
        android:layout_marginTop="15dp"
        android:gravity="center"
        android:text="@string/login_info_buz_id_change_popup_tip"
        android:textColor="@color/text_white_default"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle" />


    <com.interfun.buz.common.widget.button.CommonButton
        android:id="@+id/btnContinue"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        app:layout_constraintEnd_toEndOf="@+id/guideEnd"
        app:layout_constraintStart_toStartOf="@+id/guideStart"
        app:layout_constraintTop_toBottomOf="@+id/tvContent"
        app:text="@string/continue_text"
        app:type="primary_medium"
        tools:layout_height="48dp" />

    <TextView
        android:id="@+id/tvCancle"
        style="@style/button"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:gravity="center"
        android:paddingTop="10dp"
        android:text="@string/cancel"
        android:textColor="@color/basic_primary"
        app:layout_constraintEnd_toEndOf="@+id/guideEnd"
        app:layout_constraintStart_toStartOf="@+id/guideStart"
        app:layout_constraintTop_toBottomOf="@+id/btnContinue" />

    <View
        android:id="@+id/vCancelClick"
        android:layout_width="0dp"
        android:layout_height="10dp"
        app:layout_constraintEnd_toEndOf="@id/tvCancle"
        app:layout_constraintStart_toStartOf="@id/tvCancle"
        app:layout_constraintTop_toBottomOf="@id/tvCancle" />

    <Space
        android:id="@+id/spaceBottom"
        android:layout_width="match_parent"
        android:layout_height="20dp"
        android:layout_marginTop="10dp"
        app:layout_constraintTop_toBottomOf="@id/tvCancle" />

</com.interfun.buz.base.widget.round.RoundConstraintLayout>