<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/clRoot"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/common_secondary_background"
    tools:ignore="SpUsage">

    <androidx.legacy.widget.Space
        android:id="@+id/spaceStatusBar"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_height="@dimen/status_bar_height" />

    <!--标题栏区域-->
    <androidx.legacy.widget.Space
        android:id="@+id/spaceTitleBar"
        android:layout_width="0dp"
        android:layout_height="@dimen/title_bar_height"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/spaceStatusBar" />

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftvBack"
        style="@style/iconfont_24"
        android:layout_marginStart="4dp"
        app:autoRTL="true"
        android:text="@string/ic_back"
        android:textColor="@color/text_white_main"
        app:layout_constraintBottom_toBottomOf="@+id/spaceTitleBar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/spaceTitleBar" />

    <TextView
        android:id="@+id/tvTitle"
        style="@style/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/account"
        android:textColor="@color/text_white_main"
        app:layout_constraintBottom_toBottomOf="@+id/spaceTitleBar"
        app:layout_constraintEnd_toEndOf="@+id/spaceTitleBar"
        app:layout_constraintStart_toStartOf="@+id/spaceTitleBar"
        app:layout_constraintTop_toTopOf="@+id/spaceTitleBar" />

    <ScrollView
        android:id="@+id/scrollView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:scrollbars="none"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/spaceTitleBar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingHorizontal="20dp"
            android:paddingBottom="40dp">

            <TextView
                android:id="@+id/tvProfile"
                style="@style/body"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="13dp"
                android:text="@string/profile"
                android:textColor="@color/text_white_secondary"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/clEditProfile"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="14dp"
                android:paddingHorizontal="20dp"
                android:paddingVertical="22dp"
                app:layout_constraintTop_toBottomOf="@+id/tvProfile">

                <TextView
                    android:id="@+id/tvEditProfile"
                    style="@style/main_body"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/user_edit_profile"
                    android:textColor="@color/text_white_main"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <com.interfun.buz.common.widget.view.IconFontTextView
                    android:id="@+id/iftvGoEditProfile"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:autoRTL="true"
                    android:text="@string/ic_arrow_right"
                    android:textColor="@color/text_white_secondary"
                    android:textSize="16sp"
                    app:layout_constraintBottom_toBottomOf="@+id/tvEditProfile"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/tvEditProfile" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <com.interfun.buz.base.widget.round.RoundView
                android:id="@+id/roundEditProfileBg"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:background="@color/overlay_white_4"
                app:layout_constraintBottom_toBottomOf="@+id/clEditProfile"
                app:layout_constraintTop_toTopOf="@+id/clEditProfile"
                app:round_radius="8dp" />


            <TextView
                android:id="@+id/tvAccount"
                style="@style/body"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="33dp"
                android:text="@string/account"
                android:textColor="@color/text_white_secondary"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/roundEditProfileBg" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/clPhoneNumber"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="14dp"
                android:paddingHorizontal="20dp"
                android:paddingVertical="14dp"
                app:layout_constraintTop_toBottomOf="@+id/tvAccount">

                <TextView
                    android:id="@+id/tvPhoneNumber"
                    style="@style/main_body"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/phone_number"
                    android:textColor="@color/text_white_main"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tvMyPhone"
                    style="@style/body"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:gravity="end"
                    android:layout_marginTop="2dp"
                    tools:text="+86 ***********"
                    android:textColor="@color/text_white_secondary"
                    app:layout_constraintStart_toStartOf="@+id/tvPhoneNumber"
                    app:layout_constraintTop_toBottomOf="@+id/tvPhoneNumber" />


                <TextView
                    android:id="@+id/tvBindPhone"
                    style="@style/body"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="6dp"
                    android:text="@string/add"
                    android:textColor="@color/basic_primary"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/iftvGoBind"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:visibility="visible" />

                <com.interfun.buz.common.widget.view.IconFontTextView
                    android:id="@+id/iftvGoBind"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:autoRTL="true"
                    android:text="@string/ic_arrow_right"
                    android:textColor="@color/text_white_secondary"
                    android:textSize="16sp"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:visibility="visible" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/clEmail"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingHorizontal="20dp"
                android:paddingVertical="14dp"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@+id/clPhoneNumber"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/tvEmail"
                    style="@style/main_body"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/email"
                    android:textColor="@color/text_white_main"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tvMyEmail"
                    style="@style/body"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:layout_marginTop="2dp"
                    android:lines="1"
                    app:layout_constraintHorizontal_bias="1"
                    tools:text="<EMAIL>"
                    android:textColor="@color/text_white_secondary"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintStart_toStartOf="@+id/tvEmail"
                    app:layout_constraintTop_toBottomOf="@+id/tvEmail" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <com.interfun.buz.base.widget.round.RoundView
                android:id="@+id/roundAccountBg"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:background="@color/overlay_white_4"
                app:layout_constraintBottom_toBottomOf="@+id/clEmail"
                app:layout_constraintTop_toTopOf="@+id/clPhoneNumber"
                app:round_radius="8dp" />

            <!--Language setting-->

            <TextView
                android:id="@+id/tvAnyMethods"
                style="@style/body"
                android:visibility="gone"
                tools:visibility="visible"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:text="@string/login_methods_tips"
                android:textColor="@color/text_white_secondary"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/clEmail" />

            <TextView
                android:id="@+id/tvLanguageSettingTitle"
                style="@style/body"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="33dp"
                android:text="@string/app_settings"
                android:textColor="@color/text_white_secondary"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvAnyMethods" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/clLanguageSetting"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="14dp"
                android:paddingHorizontal="20dp"
                android:paddingVertical="22dp"
                app:layout_constraintTop_toBottomOf="@+id/tvLanguageSettingTitle">

                <TextView
                    android:id="@+id/tvLanguageSetting"
                    style="@style/main_body"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/language"
                    android:textColor="@color/text_white_main"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />


                <TextView
                    android:id="@+id/tvLanguageName"
                    style="@style/body"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:gravity="end"
                    android:textColor="@color/text_white_secondary"
                    app:layout_constraintBottom_toBottomOf="@+id/tvLanguageSetting"
                    app:layout_constraintEnd_toStartOf="@+id/iftvLanguageSetting"
                    app:layout_constraintStart_toEndOf="@+id/tvLanguageSetting"
                    app:layout_constraintTop_toTopOf="@+id/tvLanguageSetting"
                    tools:text="简体中文" />

                <com.interfun.buz.common.widget.view.IconFontTextView
                    android:id="@+id/iftvLanguageSetting"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:autoRTL="true"
                    android:text="@string/ic_arrow_right"
                    android:textColor="@color/text_white_secondary"
                    android:textSize="16sp"
                    app:layout_constraintBottom_toBottomOf="@+id/tvLanguageSetting"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/tvLanguageSetting" />


            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/clMediaDownloadSetting"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingHorizontal="20dp"
                android:paddingVertical="22dp"
                app:layout_constraintTop_toBottomOf="@+id/clLanguageSetting">

                <TextView
                    android:id="@+id/tvMediaDownloadSetting"
                    style="@style/main_body"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/media_download_option"
                    android:textColor="@color/text_white_main"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />


                <com.interfun.buz.common.widget.view.IconFontTextView
                    android:id="@+id/iftvMediaDownloadSetting"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:autoRTL="true"
                    android:text="@string/ic_arrow_right"
                    android:textColor="@color/text_white_secondary"
                    android:textSize="16sp"
                    app:layout_constraintBottom_toBottomOf="@+id/tvMediaDownloadSetting"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/tvMediaDownloadSetting" />


            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/clStorageSetting"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingHorizontal="20dp"
                android:paddingVertical="22dp"
                app:layout_constraintTop_toBottomOf="@+id/clMediaDownloadSetting">

                <TextView
                    android:id="@+id/tvStorageSetting"
                    style="@style/main_body"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/storage_title"
                    android:textColor="@color/text_white_main"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />


                <com.interfun.buz.common.widget.view.IconFontTextView
                    android:id="@+id/iftvStorageSetting"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:autoRTL="true"
                    android:text="@string/ic_arrow_right"
                    android:textColor="@color/text_white_secondary"
                    android:textSize="16sp"
                    app:layout_constraintBottom_toBottomOf="@+id/tvStorageSetting"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/tvStorageSetting" />


            </androidx.constraintlayout.widget.ConstraintLayout>

            <com.interfun.buz.base.widget.round.RoundView
                android:id="@+id/roundLanguageSettingBg"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:background="@color/overlay_white_4"
                app:layout_constraintBottom_toBottomOf="@+id/clStorageSetting"
                app:layout_constraintTop_toTopOf="@+id/clLanguageSetting"
                app:round_radius="8dp" />


            <TextView
                android:id="@+id/tvPrivacy"
                style="@style/body"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="33dp"
                android:text="@string/user_privacy"
                android:textColor="@color/text_white_secondary"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/roundLanguageSettingBg" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/clBlockList"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="14dp"
                android:paddingHorizontal="20dp"
                android:paddingVertical="22dp"
                app:layout_constraintTop_toBottomOf="@+id/tvPrivacy">

                <TextView
                    android:id="@+id/tvBlockList"
                    style="@style/main_body"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/blocklist_v2"
                    android:textColor="@color/text_white_main"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <com.interfun.buz.common.widget.view.IconFontTextView
                    android:id="@+id/iftvGoBlockList"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:autoRTL="true"
                    android:text="@string/ic_arrow_right"
                    android:textColor="@color/text_white_secondary"
                    android:textSize="16sp"
                    app:layout_constraintBottom_toBottomOf="@+id/tvBlockList"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/tvBlockList" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <com.interfun.buz.base.widget.round.RoundView
                android:id="@+id/roundBlockListBg"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:background="@color/overlay_white_4"
                app:layout_constraintBottom_toBottomOf="@+id/clBlockList"
                app:layout_constraintTop_toTopOf="@+id/clBlockList"
                app:round_radius="8dp" />

            <com.interfun.buz.common.widget.button.CommonButton
                android:id="@+id/btnLogout"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:layout_marginTop="40dp"
                app:layout_constraintTop_toBottomOf="@+id/roundBlockListBg"
                app:text="@string/user_log_out"
                app:textColor="@color/text_white_main"
                app:type="tertiary_larger" />

            <TextView
                android:id="@+id/clDeleteAccount"
                style="@style/button"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingHorizontal="20dp"
                android:paddingVertical="18.5dp"
                android:text="@string/delete_my_buz_account"
                android:textColor="@color/secondary_error"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/btnLogout" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>