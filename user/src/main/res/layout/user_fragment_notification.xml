<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:background="@drawable/common_secondary_background">
        <Space
            android:id="@+id/spaceStatusBar"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:layout_height="44dp" />

        <com.interfun.buz.common.widget.view.IconFontTextView
            android:id="@+id/iftvLeftBack"
            app:autoRTL="true"
            android:layout_width="64dp"
            android:layout_height="0dp"
            android:layout_alignParentStart="true"
            android:text="@string/ic_back"
            android:textSize="24sp"
            android:gravity="center"
            android:textColor="@color/text_white_main"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/tvTitle"
            app:layout_constraintBottom_toBottomOf="@id/tvTitle"/>

        <TextView
            android:id="@+id/tvTitle"
            style="@style/title"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/title_bar_height"
            android:gravity="center"
            android:layout_centerInParent="true"
            android:textColor="@color/text_white_main"
            android:text="@string/chat_home_notify_go_setting_v2"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/spaceStatusBar"/>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clAutoStartItem"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:layout_marginHorizontal="20dp"
            android:background="@drawable/common_rect_overlay_white_6_radius_12"
            app:layout_constraintTop_toBottomOf="@+id/tvTitle">


                <com.interfun.buz.common.widget.view.IconFontTextView
                    android:id="@+id/iftvAutoStart"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="20dp"
                    app:autoRTL="true"
                    android:text="@string/ic_arrow_right"
                    android:textColor="@color/text_white_secondary"
                    android:textSize="16sp"
                    app:layout_constraintBottom_toBottomOf="@id/tvAutoStartDescription"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/tvAutoStartDescription" />

                <TextView
                    android:id="@+id/tvAutoStartDescription"
                    style="@style/main_body"
                    android:textColor="@color/text_white_main"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    android:layout_marginStart="20dp"
                    android:layout_marginVertical="14.5dp"
                    android:layout_marginEnd="16dp"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintEnd_toStartOf="@+id/iftvAutoStart"
                    android:text="@string/notification_setting_allow_start_content"
                    />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/tvBottomAutoStartTips"
            style="@style/description"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:layout_marginHorizontal="20dp"
            android:text="@string/notification_setting_allow_start_tips"
            android:textColor="@color/text_white_secondary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/clAutoStartItem" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clRunInBackgroundItem"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:layout_marginHorizontal="20dp"
            android:background="@drawable/common_rect_overlay_white_6_radius_12"
            app:layout_constraintTop_toBottomOf="@+id/tvBottomAutoStartTips">


                <com.interfun.buz.common.widget.view.IconFontTextView
                    android:id="@+id/iftvRunInBackground"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="20dp"
                    app:autoRTL="true"
                    android:text="@string/ic_arrow_right"
                    android:textColor="@color/text_white_secondary"
                    android:textSize="16sp"
                    app:layout_constraintBottom_toBottomOf="@id/tvRunInBackgroundDescription"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/tvRunInBackgroundDescription" />

                <TextView
                    android:id="@+id/tvRunInBackgroundDescription"
                    style="@style/main_body"
                    android:textColor="@color/text_white_main"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    android:layout_marginStart="20dp"
                    android:layout_marginVertical="14.5dp"
                    android:layout_marginEnd="16dp"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintEnd_toStartOf="@+id/iftvRunInBackground"
                    android:text="@string/notification_setting_run_in_background_content"
                    />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/tvBottomRunInBackgroundTips"
            style="@style/description"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:layout_marginHorizontal="20dp"
            android:text="@string/notification_setting_run_in_background_tips"
            android:textColor="@color/text_white_secondary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/clRunInBackgroundItem" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clAllowNotificationItem"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:layout_marginHorizontal="20dp"
            android:background="@drawable/common_rect_overlay_white_6_radius_12"
            app:layout_constraintTop_toBottomOf="@+id/tvBottomRunInBackgroundTips">


                <com.interfun.buz.common.widget.view.IconFontTextView
                    android:id="@+id/iftvAllowNotification"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="20dp"
                    app:autoRTL="true"
                    android:text="@string/ic_arrow_right"
                    android:textColor="@color/text_white_secondary"
                    android:textSize="16sp"
                    app:layout_constraintBottom_toBottomOf="@id/tvAllowNotificationDescription"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/tvAllowNotificationDescription" />


                <com.interfun.buz.common.widget.view.IconFontTextView
                    style="@style/body"
                    android:id="@+id/tvAllowNotification"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="20dp"
                    app:autoRTL="true"
                    android:text="@string/on"
                    android:textColor="@color/text_white_secondary"
                    app:layout_constraintBottom_toBottomOf="@id/tvAllowNotificationDescription"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/tvAllowNotificationDescription" />

                <TextView
                    android:id="@+id/tvAllowNotificationDescription"
                    style="@style/main_body"
                    android:textColor="@color/text_white_main"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    android:layout_marginStart="20dp"
                    android:layout_marginVertical="14.5dp"
                    android:layout_marginEnd="16dp"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintEnd_toStartOf="@+id/iftvAllowNotification"
                    android:text="@string/notification_setting_allow_notification_content"
                    />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/tvBottomAllowNotificationTips"
            style="@style/description"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:layout_marginHorizontal="20dp"
            android:text="@string/notification_setting_allow_notification_tips"
            android:textColor="@color/text_white_secondary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/clAllowNotificationItem" />

</androidx.constraintlayout.widget.ConstraintLayout>