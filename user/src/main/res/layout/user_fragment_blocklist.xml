<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:background="@drawable/common_secondary_background">

      <Space
          android:id="@+id/spaceStatusBar"
          android:layout_width="0dp"
          android:layout_height="wrap_content"
          app:layout_constraintEnd_toEndOf="parent"
          app:layout_constraintStart_toStartOf="parent"
          app:layout_constraintTop_toTopOf="parent"
          tools:layout_height="44dp" />


      <RelativeLayout
          android:id="@+id/titleBar"
          android:layout_width="match_parent"
          android:layout_height="@dimen/title_bar_height"
          app:layout_constraintEnd_toEndOf="parent"
          app:layout_constraintStart_toStartOf="parent"
          app:layout_constraintTop_toBottomOf="@+id/spaceStatusBar"
          android:orientation="horizontal">

            <com.interfun.buz.common.widget.view.IconFontTextView
                android:id="@+id/iftvLeftBack"
                android:layout_width="64dp"
                app:autoRTL="true"
                android:layout_alignParentStart="true"
                android:text="@string/ic_back"
                android:textSize="24sp"
                android:gravity="center"
                android:textColor="@color/text_white_main"
                android:layout_height="match_parent"/>

            <TextView
                android:id="@+id/tvTitle"
                style="@style/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:layout_centerInParent="true"
                android:textColor="@color/text_white_main"
                android:text="@string/blocklist_v2"
                />

      </RelativeLayout>
      <androidx.recyclerview.widget.RecyclerView
          android:id="@+id/rlvBlockList"
          android:layout_width="match_parent"
          android:layout_height="0dp"
          app:layout_constraintEnd_toEndOf="parent"
          app:layout_constraintStart_toStartOf="parent"
          app:layout_constraintTop_toBottomOf="@+id/titleBar"
          app:layout_constraintBottom_toBottomOf="parent"
          android:scrollbars="none"/>

    <com.interfun.buz.common.widget.view.EmptyDataView
        android:id="@+id/emptyDataView"
        android:layout_width="match_parent"
        android:layout_marginHorizontal="40dp"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:text="@string/user_its_empty_now"
        app:layout_constraintBottom_toBottomOf="parent"
        app:empty_type="no_contact"/>
</androidx.constraintlayout.widget.ConstraintLayout>