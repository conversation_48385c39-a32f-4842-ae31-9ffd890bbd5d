<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:background="@drawable/common_secondary_background">
        <Space
            android:id="@+id/spaceStatusBar"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:layout_height="44dp" />

        <com.interfun.buz.common.widget.view.IconFontTextView
            android:id="@+id/iftvLeftBack"
            app:autoRTL="true"
            android:layout_width="64dp"
            android:layout_height="0dp"
            android:layout_alignParentStart="true"
            android:text="@string/ic_back"
            android:textSize="24sp"
            android:gravity="center"
            android:textColor="@color/text_white_main"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/tvTitle"
            app:layout_constraintBottom_toBottomOf="@id/tvTitle"/>

        <TextView
            android:id="@+id/tvTitle"
            style="@style/title"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/title_bar_height"
            android:gravity="center"
            android:layout_centerInParent="true"
            android:textColor="@color/text_white_main"
            android:text="@string/alert_sound_page_title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/spaceStatusBar"/>

        <TextView
            android:id="@+id/tvSave"
            style="@style/button"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:layout_alignParentEnd="true"
            android:paddingHorizontal="20dp"
            android:text="@string/save"
            android:textSize="16sp"
            android:gravity="center"
            android:textColor="@color/basic_primary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/tvTitle"
            app:layout_constraintBottom_toBottomOf="@id/tvTitle"/>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvAlertSoundList"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="20dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvTitle"
            app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>