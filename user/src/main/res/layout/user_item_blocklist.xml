<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="70dp"
    tools:background="@color/overlay_grey_12">

    <com.interfun.buz.common.widget.view.PortraitImageView
        android:id="@+id/ivPortrait"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:layout_marginStart="20dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvUserName"
        style="@style/main_body"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:ellipsize="end"
        android:gravity="start"
        android:lines="1"
        android:textColor="@color/text_white_main"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/btnUnblock"
        app:layout_constraintStart_toEndOf="@+id/ivPortrait"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="反馈时间法力水晶" />


    <com.interfun.buz.common.widget.button.CommonButton
        android:id="@+id/btnUnblock"
        android:layout_width="wrap_content"
        android:layout_height="30dp"
        android:layout_marginEnd="20dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:text="@string/unblock"
        app:type="tertiary_small" />


</androidx.constraintlayout.widget.ConstraintLayout>