<?xml version="1.0" encoding="utf-8"?>
<com.interfun.buz.base.widget.round.RoundConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="20dp"
    android:layout_marginTop="10dp"
    android:background="@color/overlay_white_10"
    android:minHeight="90dp"
    android:paddingVertical="20dp"
    android:paddingHorizontal="20dp"
    app:round_radius="12dp"
    tools:background="@color/black_100">

    <com.interfun.buz.common.widget.view.PortraitImageView
        android:id="@+id/ivContactPortrait"
        android:layout_width="50dp"
        android:layout_height="50dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvName"
        style="@style/main_body"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/white_80"
        android:textSize="16sp"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toTopOf="@id/tvDes"
        app:layout_constraintEnd_toStartOf="@id/ivAIFlag"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toEndOf="@id/ivContactPortrait"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="PsychologistPsychologistPsychologistPsychologistPsychologistPsychologist" />

    <ImageView
        android:id="@+id/ivAIFlag"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginStart="4dp"
        android:layout_marginEnd="10dp"
        android:visibility="visible"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="@id/tvName"
        app:layout_constraintEnd_toStartOf="@+id/iftvArrow"
        app:layout_constraintStart_toEndOf="@id/tvName"
        app:layout_constraintTop_toTopOf="@id/tvName"
        tools:src="@drawable/common_icon_ai_flag"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvDes"
        style="@style/body"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:layout_marginEnd="10dp"
        android:textColor="@color/white_40"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/iftvArrow"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toStartOf="@id/tvName"
        app:layout_constraintTop_toBottomOf="@id/tvName"
        tools:text="AI CharacterAIAI CharacterAIAI CharacterAIAI CharacterAIAI CharacterAIAI CharacterAIAI CharacterAIAI CharacterAIAI CharacterAIAI CharacterAIAI CharacterAIAI CharacterAI " />


    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/barrier"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="end"
        app:constraint_referenced_ids="ivAIFlag,tvDes" />


    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftvArrow"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_marginStart="10dp"
        android:text="@string/ic_arrow_right_rtl"
        android:textSize="16sp"
        android:textColor="@color/text_white_secondary"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="1"
        app:layout_constraintStart_toEndOf="@+id/barrier"
        app:layout_constraintTop_toTopOf="parent" />

</com.interfun.buz.base.widget.round.RoundConstraintLayout>