<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/common_secondary_background"
    android:orientation="vertical">

    <Space
        android:id="@+id/spaceStatusBar"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_height="44dp" />


    <RelativeLayout
        android:id="@+id/titleBar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/title_bar_height"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/spaceStatusBar">

        <com.interfun.buz.common.widget.view.IconFontTextView
            android:id="@+id/iftvLeftBack"
            android:layout_width="64dp"
            android:layout_height="match_parent"
            android:layout_alignParentStart="true"
            android:gravity="center"
            app:autoRTL="true"
            android:text="@string/ic_back"
            android:textColor="@color/text_white_main"
            android:textSize="24sp" />

        <TextView
            android:id="@+id/tvTitle"
            style="@style/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:gravity="center"
            android:text="@string/ai_market_entry"
            android:textColor="@color/text_white_main" />

    </RelativeLayout>


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/list"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/titleBar" />


    <com.interfun.buz.common.widget.view.network.BuzLoadingView
        android:id="@+id/loadingView"
        android:visibility="visible"
        android:layout_width="120dp"
        android:layout_height="120dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintVertical_bias="0.3"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/titleBar" />


</androidx.constraintlayout.widget.ConstraintLayout>