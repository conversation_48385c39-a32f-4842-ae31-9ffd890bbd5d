<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/common_secondary_background">

    <androidx.legacy.widget.Space
        android:id="@+id/spaceStatusBar"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_height="44dp" />

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftvLeftBack"
        android:layout_width="64dp"
        android:layout_height="@dimen/title_bar_height"
        android:gravity="center"
        app:autoRTL="true"
        android:text="@string/ic_back"
        android:textColor="@color/text_white_main"
        android:textSize="24sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/spaceStatusBar" />

    <TextView
        android:id="@+id/tvTitle"
        style="@style/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center"
        android:text="@string/login_info_edit_buz_id"
        android:textColor="@color/text_white_main"
        app:layout_constraintBottom_toBottomOf="@+id/iftvLeftBack"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/iftvLeftBack" />

    <TextView
        android:id="@+id/tvOnceAYearTip"
        style="@style/body"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="40dp"
        android:layout_marginTop="10dp"
        android:textColor="@color/text_white_secondary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iftvLeftBack" />

    <EditText
        android:id="@+id/etBuzId"
        style="@style/regular"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:layout_marginHorizontal="40dp"
        android:layout_marginTop="20dp"
        android:background="@drawable/common_rect_overlay_white_10_radius_12"
        android:gravity="center_vertical"
        android:inputType="textVisiblePassword"
        android:lines="1"
        android:paddingStart="41dp"
        android:paddingEnd="45dp"
        android:singleLine="true"
        android:textColor="@color/text_white_main"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvOnceAYearTip" />

    <TextView
        android:id="@+id/tvSymbolAt"
        style="@style/regular"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:text="@string/common_symbol_at"
        android:textColor="@color/text_white_disable"
        app:layout_constraintBottom_toBottomOf="@id/etBuzId"
        app:layout_constraintStart_toStartOf="@id/etBuzId"
        app:layout_constraintTop_toTopOf="@id/etBuzId" />

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftvClearBuzId"
        style="@style/iconfont_base"
        android:layout_width="42dp"
        android:layout_height="0dp"
        android:gravity="center"
        android:text="@string/ic_clear_input_solid"
        android:textColor="@color/text_white_secondary"
        android:textSize="18dp"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintBottom_toBottomOf="@id/etBuzId"
        app:layout_constraintEnd_toEndOf="@id/etBuzId"
        app:layout_constraintTop_toTopOf="@id/etBuzId" />

    <TextView
        android:id="@+id/tvTips"
        style="@style/body"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="20dp"
        android:text="@string/login_info_edit_checking_user_name"
        android:textColor="@color/text_white_default"
        android:visibility="invisible"
        app:layout_constraintStart_toStartOf="@+id/etBuzId"
        app:layout_constraintTop_toBottomOf="@+id/etBuzId"
        app:layout_constraintEnd_toEndOf="@id/etBuzId"
        tools:visibility="visible" />

    <com.interfun.buz.common.widget.view.loading.CircleLoadingView
        android:id="@+id/pagLoading"
        android:layout_width="18dp"
        android:layout_height="18dp"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@+id/tvTips"
        app:layout_constraintStart_toStartOf="@+id/etBuzId"
        app:layout_constraintTop_toTopOf="@+id/tvTips" />

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftvError"
        style="@style/iconfont_base"
        android:layout_width="18dp"
        android:layout_height="18dp"
        android:text="@string/ic_warning_solid"
        android:textColor="@color/secondary_error"
        android:textSize="18dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/tvTips"
        app:layout_constraintStart_toStartOf="@+id/etBuzId"
        app:layout_constraintTop_toTopOf="@+id/tvTips"
        tools:ignore="SpUsage"
        tools:visibility="visible" />

    <com.interfun.buz.common.widget.button.CommonButton
        android:id="@+id/cbSave"
        android:layout_width="0dp"
        android:layout_height="56dp"
        android:layout_marginHorizontal="40dp"
        android:layout_marginBottom="20dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:text="@string/save"
        app:type="secondary_larger" />

</androidx.constraintlayout.widget.ConstraintLayout>