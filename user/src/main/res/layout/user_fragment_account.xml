<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:background="@drawable/common_secondary_background">
        <Space
            android:id="@+id/spaceStatusBar"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:layout_height="44dp" />


        <RelativeLayout
            android:id="@+id/titleBar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/title_bar_height"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/spaceStatusBar"
            android:orientation="horizontal">

                <com.interfun.buz.common.widget.view.IconFontTextView
                    android:id="@+id/iftvLeftBack"
                    app:autoRTL="true"
                    android:layout_width="64dp"
                    android:layout_alignParentStart="true"
                    android:text="@string/ic_back"
                    android:textSize="24sp"
                    android:gravity="center"
                    android:textColor="@color/text_white_main"
                    android:layout_height="match_parent"/>

                <TextView
                    android:id="@+id/tvTitle"
                    style="@style/title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:layout_centerInParent="true"
                    android:textColor="@color/text_white_main"
                    android:text="@string/account"
                    />

        </RelativeLayout>

        <LinearLayout
            android:id="@+id/llDeleteMyAccount"
            android:layout_width="match_parent"
            android:layout_height="64dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingHorizontal="20dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/titleBar">
                <TextView
                    style="@style/regular"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginEnd="20dp"
                    android:gravity="start|center"
                    android:textColor="@color/text_white_main"
                    android:text="@string/user_delete_my_account" />

                <com.interfun.buz.common.widget.view.IconFontTextView
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    app:autoRTL="true"
                    android:text="@string/ic_arrow_right"
                    android:textSize="16sp"
                    android:textColor="@color/text_white_secondary"
                    />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/llLogout"
            android:layout_width="match_parent"
            android:layout_height="64dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingHorizontal="20dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/llDeleteMyAccount">
                <TextView
                    style="@style/regular"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginEnd="20dp"
                    android:gravity="start|center"
                    android:textColor="@color/text_white_main"
                    android:text="@string/user_log_out" />

                <com.interfun.buz.common.widget.view.IconFontTextView
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    app:autoRTL="true"
                    android:text="@string/ic_arrow_right"
                    android:textSize="16sp"
                    android:textColor="@color/text_white_secondary"
                    />
        </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>