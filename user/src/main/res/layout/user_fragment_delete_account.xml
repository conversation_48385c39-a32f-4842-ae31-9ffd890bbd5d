<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:gravity="center_horizontal"
    android:background="@drawable/common_secondary_background">


        <Space
            android:id="@+id/spaceStatusBar"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:layout_height="44dp" />


        <RelativeLayout
            android:id="@+id/titleBar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/title_bar_height"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/spaceStatusBar"
            android:orientation="horizontal">

                <com.interfun.buz.common.widget.view.IconFontTextView
                    android:id="@+id/iftvLeftBack"
                    android:layout_width="64dp"
                    app:autoRTL="true"
                    android:layout_alignParentStart="true"
                    android:text="@string/ic_back"
                    android:textSize="24sp"
                    android:gravity="center"
                    android:textColor="@color/text_white_main"
                    android:layout_height="match_parent"/>

                <TextView
                    android:id="@+id/tvTitle"
                    style="@style/title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:layout_centerInParent="true"
                    android:textColor="@color/text_white_main"
                    />

        </RelativeLayout>


        <com.interfun.buz.common.widget.view.IconFontTextView
            android:id="@+id/iftDenied"
            android:textSize="60sp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/ic_warning_solid"
            android:layout_marginTop="20dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/titleBar"
            android:textColor="@color/secondary_error"
            />

        <TextView
            android:id="@+id/tvDelete"
            style="@style/large_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:textColor="@color/text_white_main"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/iftDenied"
            android:text="@string/user_delete_account" />

        <TextView
            android:id="@+id/tvDeleteContent"
            style="@style/regular"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="6dp"
            android:paddingHorizontal="20dp"
            android:textColor="@color/text_white_default"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvDelete"
            android:text="@string/user_delete_account_content"/>



        <LinearLayout
            android:id="@+id/llCheck"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="15dp"
            android:paddingHorizontal="20dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toTopOf="@+id/btnNext"
            android:orientation="horizontal">

                <com.interfun.buz.common.widget.view.IconFontTextView
                    android:id="@+id/iftvCheck"
                    android:layout_width="21dp"
                    android:layout_height="21dp"
                    android:gravity="center"
                    android:text="@string/ic_correct_empty"
                    android:layout_marginTop="2dp"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/tvReadTips"
                    style="@style/body"
                    android:textColor="@color/text_white_main"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="5dp"
                    android:paddingBottom="5dp"
                    android:text="@string/user_read_notice_tips"
                    />
        </LinearLayout>

        <com.interfun.buz.common.widget.button.CommonButton
            android:id="@+id/btnNext"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:layout_marginBottom="72dp"
            app:text="@string/next"
            android:layout_marginHorizontal="20dp"
            android:enabled="false"
            app:type="tertiary_larger"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>



</androidx.constraintlayout.widget.ConstraintLayout>