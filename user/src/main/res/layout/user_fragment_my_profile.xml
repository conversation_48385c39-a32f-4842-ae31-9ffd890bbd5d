<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_background_1_default"
    tools:ignore="SpUsage">

    <androidx.legacy.widget.Space
        android:id="@+id/spaceStatusBar"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_height="@dimen/status_bar_height" />

    <!--标题栏区域-->
    <androidx.legacy.widget.Space
        android:id="@+id/spaceTitleBar"
        android:layout_width="0dp"
        android:layout_height="@dimen/title_bar_height"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/spaceStatusBar" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/scrollView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:scrollbars="none"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clRoot"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingBottom="40dp">

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/glStart"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintGuide_begin="20dp" />

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/glEnd"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintGuide_end="20dp" />

            <androidx.compose.ui.platform.ComposeView
                android:id="@+id/cvLivePlaceEntrance"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                app:layout_constraintHeight_min="100dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/viewPortraitBg"
                android:layout_width="116dp"
                android:layout_height="116dp"
                android:layout_marginTop="-24dp"
                android:background="@drawable/common_oval_background_1_default"
                app:layout_constraintEnd_toEndOf="@+id/glEnd"
                app:layout_constraintStart_toStartOf="@+id/glStart"
                app:layout_constraintTop_toBottomOf="@+id/cvLivePlaceEntrance" />

            <com.interfun.buz.common.widget.view.PortraitImageView
                android:id="@+id/ivPortrait"
                android:layout_width="100dp"
                android:layout_height="100dp"
                app:layout_constraintBottom_toBottomOf="@+id/viewPortraitBg"
                app:layout_constraintEnd_toEndOf="@+id/viewPortraitBg"
                app:layout_constraintStart_toStartOf="@+id/viewPortraitBg"
                app:layout_constraintTop_toTopOf="@+id/viewPortraitBg"
                tools:background="@tools:sample/avatars"/>

            <com.interfun.buz.common.widget.view.IconFontTextView
                android:id="@+id/iftvEditProfile"
                style="@style/iconfont_base"
                android:layout_width="34dp"
                android:layout_height="34dp"
                android:layout_marginEnd="4dp"
                android:background="@drawable/common_oval_overlay_grey_10"
                android:text="@string/ic_edit"
                android:textSize="16dp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@+id/ivPortrait"
                app:layout_constraintEnd_toEndOf="@+id/viewPortraitBg" />

            <TextView
                android:id="@+id/tvUserName"
                style="@style/number"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="20dp"
                android:ellipsize="end"
                android:gravity="center"
                android:textColor="@color/text_white_main"
                app:layout_constrainedWidth="true"
                app:layout_constraintEnd_toEndOf="@+id/glEnd"
                app:layout_constraintStart_toStartOf="@+id/glStart"
                app:layout_constraintTop_toBottomOf="@+id/ivPortrait"
                tools:text="Mike" />

            <com.interfun.buz.base.widget.round.RoundTextView
                android:id="@+id/tvBuzId"
                style="@style/text_label_medium"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:paddingHorizontal="12dp"
                android:paddingVertical="4dp"
                android:background="@color/color_background_3_default"
                android:textColor="@color/color_text_white_secondary"
                android:textSize="14sp"
                app:round_radius="@dimen/radius_full"
                app:layout_constraintEnd_toEndOf="@id/tvUserName"
                app:layout_constraintStart_toStartOf="@id/tvUserName"
                app:layout_constraintTop_toBottomOf="@id/tvUserName"
                tools:text="\@mike"/>

            <View
                android:id="@+id/vBuzIdClickArea"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_marginStart="-30dp"
                android:layout_marginEnd="-30dp"
                app:layout_constraintBottom_toBottomOf="@id/tvBuzId"
                app:layout_constraintEnd_toEndOf="@id/tvBuzId"
                app:layout_constraintStart_toStartOf="@id/tvBuzId"
                app:layout_constraintTop_toTopOf="@id/tvBuzId" />

            <View
                android:id="@+id/viewShareBg"
                android:layout_width="0dp"
                android:layout_height="68dp"
                app:layout_constraintBottom_toBottomOf="@+id/viewSettingBg"
                app:layout_constraintEnd_toStartOf="@+id/viewSettingBg"
                app:layout_constraintStart_toStartOf="@+id/glStart"
                app:layout_constraintTop_toTopOf="@id/viewSettingBg" />

            <com.interfun.buz.common.widget.view.IconFontTextView
                android:id="@+id/iftvShare"
                style="@style/iconfont_24"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginTop="10dp"
                android:text="@string/ic_share"
                android:textColor="@color/text_white_main"
                app:layout_constraintEnd_toEndOf="@+id/viewShareBg"
                app:layout_constraintStart_toStartOf="@+id/viewShareBg"
                app:layout_constraintTop_toTopOf="@+id/viewShareBg" />

            <TextView
                android:id="@+id/tvShare"
                style="@style/body"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="10dp"
                android:text="@string/share"
                android:textColor="@color/text_white_default"
                android:textSize="12sp"
                app:layout_constraintBottom_toBottomOf="@+id/viewShareBg"
                app:layout_constraintEnd_toEndOf="@+id/viewShareBg"
                app:layout_constraintStart_toStartOf="@+id/viewShareBg" />

            <View
                android:id="@+id/viewSettingBg"
                android:layout_width="0dp"
                android:layout_height="68dp"
                android:layout_marginTop="30dp"
                app:layout_constraintEnd_toStartOf="@+id/viewQrCodeBg"
                app:layout_constraintStart_toEndOf="@+id/viewShareBg"
                app:layout_constraintTop_toBottomOf="@id/tvBuzId" />

            <com.interfun.buz.common.widget.view.IconFontTextView
                android:id="@+id/iftvSetting"
                style="@style/iconfont_24"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginTop="10dp"
                android:text="@string/ic_account"
                android:textColor="@color/text_white_main"
                app:layout_constraintEnd_toEndOf="@+id/viewSettingBg"
                app:layout_constraintStart_toStartOf="@+id/viewSettingBg"
                app:layout_constraintTop_toTopOf="@+id/viewSettingBg" />

            <TextView
                android:id="@+id/tvSettings"
                style="@style/body"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="10dp"
                android:text="@string/account"
                android:textColor="@color/text_white_default"
                android:textSize="12sp"
                app:layout_constraintBottom_toBottomOf="@+id/viewSettingBg"
                app:layout_constraintEnd_toEndOf="@+id/viewSettingBg"
                app:layout_constraintStart_toStartOf="@+id/viewSettingBg" />

            <View
                android:id="@+id/viewQrCodeBg"
                android:layout_width="0dp"
                android:layout_height="68dp"
                app:layout_constraintBottom_toBottomOf="@+id/viewSettingBg"
                app:layout_constraintEnd_toEndOf="@+id/glEnd"
                app:layout_constraintStart_toEndOf="@+id/viewSettingBg"
                app:layout_constraintTop_toTopOf="@id/viewSettingBg" />

            <com.interfun.buz.common.widget.view.IconFontTextView
                android:id="@+id/iftvQRCode"
                style="@style/iconfont_24"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginTop="10dp"
                android:text="@string/ic_qrcode"
                android:textColor="@color/text_white_main"
                app:layout_constraintEnd_toEndOf="@+id/viewQrCodeBg"
                app:layout_constraintStart_toStartOf="@+id/viewQrCodeBg"
                app:layout_constraintTop_toTopOf="@+id/viewQrCodeBg" />

            <TextView
                android:id="@+id/tvQRCode"
                style="@style/body"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="10dp"
                android:text="@string/common_qrcode"
                android:textColor="@color/text_white_default"
                android:textSize="12sp"
                app:layout_constraintBottom_toBottomOf="@+id/viewQrCodeBg"
                app:layout_constraintEnd_toEndOf="@+id/viewQrCodeBg"
                app:layout_constraintStart_toStartOf="@+id/viewQrCodeBg" />

            <com.interfun.buz.base.widget.round.RoundConstraintLayout
                android:id="@+id/clBindPhone"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:background="@color/overlay_grey_20"
                android:paddingStart="16dp"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="@+id/glEnd"
                app:layout_constraintStart_toStartOf="@+id/glStart"
                app:layout_constraintTop_toBottomOf="@+id/viewSettingBg"
                app:round_radius="@dimen/guide_layout_radius_min"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/tvBindPhone"
                    style="@style/main_describe"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:layout_marginEnd="12dp"
                    android:text="@string/verify_phone_number"
                    android:textColor="@color/text_white_main"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintBottom_toTopOf="@+id/tvBindPhoneTips"
                    app:layout_constraintEnd_toStartOf="@+id/iftvCloseBindPhone"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_chainStyle="packed" />

                <TextView
                    android:id="@+id/tvBindPhoneTips"
                    style="@style/body"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:layout_marginEnd="12dp"
                    android:layout_marginBottom="12dp"
                    android:text="make it easier for your friends to find you qibq qdb "
                    android:textColor="@color/text_white_default"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/iftvCloseBindPhone"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvBindPhone"
                    app:layout_constraintVertical_chainStyle="packed" />

                <com.interfun.buz.common.widget.view.IconFontTextView
                    android:id="@+id/iftvCloseBindPhone"
                    style="@style/iconfont_base"
                    android:layout_width="44dp"
                    android:layout_height="0dp"
                    android:gravity="center"
                    android:paddingStart="0dp"
                    android:paddingEnd="4dp"
                    android:text="@string/ic_exit"
                    android:textColor="@color/text_white_default"
                    android:textSize="16dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </com.interfun.buz.base.widget.round.RoundConstraintLayout>

            <!--     广告页面，本来想用ViewStub去做但是太麻烦了       -->
            <include
                android:id="@+id/adBanner"
                layout="@layout/user_profile_adbanner"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginTop="10dp"
                android:visibility="gone"
                app:layout_constraintDimensionRatio="h,4:1"
                app:layout_constraintEnd_toEndOf="@+id/glEnd"
                app:layout_constraintStart_toStartOf="@+id/glStart"
                app:layout_constraintTop_toBottomOf="@id/clBindPhone"
                tools:background="#0ff"
                tools:visibility="visible" />

            <View
                android:id="@+id/viewBgSetting"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/common_rect_overlay_white_4_radius_12"
                app:layout_constraintBottom_toBottomOf="@+id/llAbout"
                app:layout_constraintEnd_toEndOf="@+id/glEnd"
                app:layout_constraintStart_toStartOf="@+id/glStart"
                app:layout_constraintTop_toTopOf="@+id/llNotificationSetting" />


            <LinearLayout
                android:id="@+id/llNotificationSetting"
                android:layout_width="0dp"
                android:layout_height="64dp"
                android:layout_marginTop="20dp"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingHorizontal="20dp"
                app:layout_constraintEnd_toEndOf="@+id/glEnd"
                app:layout_constraintStart_toStartOf="@+id/glStart"
                app:layout_constraintTop_toBottomOf="@+id/adBanner">

                <com.interfun.buz.common.widget.view.IconFontTextView
                    android:id="@+id/iftvNotificationSetting"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:autoRTL="true"
                    android:text="@string/ic_notification"
                    android:textColor="@color/basic_primary"
                    android:textSize="22sp" />

                <TextView
                    android:id="@+id/tvNotificationSetting"
                    style="@style/main_body"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:text="@string/msg_setting"
                    android:textColor="@color/text_white_main" />

                <View
                    android:id="@+id/viewMessageSettingGuidanceRedDot"
                    android:layout_width="6dp"
                    android:layout_height="6dp"
                    android:layout_marginStart="8dp"
                    android:background="@drawable/common_oval_red_point"
                    android:visibility="gone"
                    tools:visibility="visible" />

                <androidx.legacy.widget.Space
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_weight="1" />

                <com.interfun.buz.common.widget.view.IconFontTextView
                    style="@style/iconfont_base"
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    app:autoRTL="true"
                    android:text="@string/ic_arrow_right"
                    android:textColor="@color/text_white_secondary"
                    android:textSize="16dp" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/llOverlaySetting"
                android:layout_width="0dp"
                android:layout_height="64dp"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingHorizontal="20dp"
                app:layout_constraintEnd_toEndOf="@+id/glEnd"
                app:layout_constraintStart_toStartOf="@+id/glStart"
                app:layout_constraintTop_toBottomOf="@+id/llNotificationSetting">

                <com.interfun.buz.common.widget.view.IconFontTextView
                    android:id="@+id/iftOverlaySetting"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:autoRTL="true"
                    android:text="@string/ic_overlay"
                    android:textColor="@color/basic_primary"
                    android:textSize="22sp" />

                <TextView
                    android:id="@+id/tvOverlaySetting"
                    style="@style/main_body"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:text="@string/wt_overlay_name"
                    android:textColor="@color/text_white_main" />

                <View
                    android:id="@+id/viewOverlayGuidanceRedDot"
                    android:layout_width="6dp"
                    android:layout_height="6dp"
                    android:layout_marginStart="8dp"
                    android:background="@drawable/common_oval_red_point"
                    android:visibility="gone"
                    tools:visibility="visible" />

                <androidx.legacy.widget.Space
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_weight="1" />

                <com.interfun.buz.common.widget.view.IconFontTextView
                    style="@style/iconfont_base"
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    app:autoRTL="true"
                    android:text="@string/ic_arrow_right"
                    android:textColor="@color/text_white_secondary"
                    android:textSize="16dp" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/llSupport"
                android:layout_width="0dp"
                android:layout_height="64dp"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingHorizontal="20dp"
                app:layout_constraintEnd_toEndOf="@+id/glEnd"
                app:layout_constraintStart_toStartOf="@+id/glStart"
                app:layout_constraintTop_toBottomOf="@+id/llOverlaySetting">

                <com.interfun.buz.common.widget.view.IconFontTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:autoRTL="true"
                    android:text="@string/ic_book"
                    android:textColor="@color/basic_primary"
                    android:textSize="22sp" />

                <TextView
                    style="@style/main_body"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_weight="1"
                    android:gravity="start"
                    android:text="@string/support"
                    android:textColor="@color/text_white_main" />

                <com.interfun.buz.common.widget.view.IconFontTextView
                    style="@style/iconfont_base"
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    app:autoRTL="true"
                    android:text="@string/ic_arrow_right"
                    android:textColor="@color/text_white_secondary"
                    android:textSize="16dp" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/llFeedback"
                android:layout_width="0dp"
                android:layout_height="64dp"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingHorizontal="20dp"
                app:layout_constraintEnd_toEndOf="@+id/glEnd"
                app:layout_constraintStart_toStartOf="@+id/glStart"
                app:layout_constraintTop_toBottomOf="@+id/llSupport">

                <com.interfun.buz.common.widget.view.IconFontTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:autoRTL="true"
                    android:text="@string/ic_question"
                    android:textColor="@color/basic_primary"
                    android:textSize="22sp" />

                <TextView
                    style="@style/main_body"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:gravity="start"
                    android:text="@string/feedback"
                    android:textColor="@color/text_white_main" />

                <View
                    android:id="@+id/viewFeedbackEntryDot"
                    android:layout_width="6dp"
                    android:layout_height="6dp"
                    android:layout_marginStart="8dp"
                    android:background="@drawable/common_oval_red_point"
                    android:visibility="gone"
                    tools:visibility="visible" />

                <androidx.legacy.widget.Space
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_weight="1" />

                <com.interfun.buz.common.widget.view.IconFontTextView
                    style="@style/iconfont_base"
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    app:autoRTL="true"
                    android:text="@string/ic_arrow_right"
                    android:textColor="@color/text_white_secondary"
                    android:textSize="16dp" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/llAbout"
                android:layout_width="0dp"
                android:layout_height="64dp"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingHorizontal="20dp"
                app:layout_constraintEnd_toEndOf="@+id/glEnd"
                app:layout_constraintStart_toStartOf="@+id/glStart"
                app:layout_constraintTop_toBottomOf="@+id/llFeedback">

                <com.interfun.buz.common.widget.view.IconFontTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:autoRTL="true"
                    android:text="@string/ic_info"
                    android:textColor="@color/basic_primary"
                    android:textSize="22sp" />

                <TextView
                    style="@style/main_body"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_weight="1"
                    android:gravity="start"
                    android:text="@string/user_about_buz"
                    android:textColor="@color/text_white_main" />

                <com.interfun.buz.common.widget.view.IconFontTextView
                    style="@style/iconfont_base"
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    app:autoRTL="true"
                    android:text="@string/ic_arrow_right"
                    android:textColor="@color/text_white_secondary"
                    android:textSize="16dp" />

            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>

    <View
        android:id="@+id/viewTitleBarBg"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:alpha="0"
        android:background="@color/color_background_1_default"
        app:layout_constraintBottom_toBottomOf="@+id/spaceTitleBar"
        app:layout_constraintEnd_toEndOf="@+id/spaceTitleBar"
        app:layout_constraintStart_toStartOf="@+id/spaceTitleBar"
        app:layout_constraintTop_toTopOf="parent" />

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftvRightBack"
        style="@style/iconfont_base"
        android:layout_width="64dp"
        android:layout_height="0dp"
        app:autoRTL="true"
        android:text="@string/ic_back"
        app:layout_constraintBottom_toBottomOf="@+id/spaceTitleBar"
        app:layout_constraintStart_toStartOf="@+id/spaceTitleBar"
        app:layout_constraintTop_toTopOf="@+id/spaceTitleBar" />

    <TextView
        android:id="@+id/tvMyName"
        style="@style/text_title_small"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="64dp"
        android:alpha="0"
        android:ellipsize="end"
        android:singleLine="true"
        android:textColor="@color/color_foreground_neutral_important_default"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="@+id/spaceTitleBar"
        app:layout_constraintEnd_toEndOf="@+id/spaceTitleBar"
        app:layout_constraintStart_toStartOf="@+id/spaceTitleBar"
        app:layout_constraintTop_toTopOf="@+id/spaceTitleBar"
        tools:alpha="1"
        tools:text="MyName" />

</androidx.constraintlayout.widget.ConstraintLayout>