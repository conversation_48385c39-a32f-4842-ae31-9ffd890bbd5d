<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/common_secondary_background">

    <Space
        android:id="@+id/spaceStatusBar"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_height="44dp" />


    <RelativeLayout
        android:id="@+id/titleBar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/title_bar_height"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/spaceStatusBar"
        android:orientation="horizontal">

        <com.interfun.buz.common.widget.view.IconFontTextView
            android:id="@+id/iftvLeftBack"
            android:layout_width="64dp"
            app:autoRTL="true"
            android:layout_alignParentStart="true"
            android:text="@string/ic_back"
            android:textSize="24sp"
            android:gravity="center"
            android:textColor="@color/text_white_main"
            android:layout_height="match_parent"/>


        <com.interfun.buz.common.widget.view.IconFontTextView
            android:id="@+id/iftvRightBack"
            android:layout_width="64dp"
            android:textColor="@color/text_white_main"
            android:textSize="24sp"
            android:gravity="center"
            android:text="@string/ic_forward"
            android:layout_alignParentEnd="true"
            android:layout_height="match_parent"/>

        <TextView
            android:id="@+id/tvTitle"
            style="@style/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:layout_centerInParent="true"
            android:textColor="@color/text_white_main"
            tools:text="Profile"
            />

    </RelativeLayout>

</androidx.constraintlayout.widget.ConstraintLayout>