<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:background="@drawable/common_secondary_background">

      <Space
          android:id="@+id/spaceStatusBar"
          android:layout_width="0dp"
          android:layout_height="wrap_content"
          app:layout_constraintEnd_toEndOf="parent"
          app:layout_constraintStart_toStartOf="parent"
          app:layout_constraintTop_toTopOf="parent"
          tools:layout_height="44dp" />


      <RelativeLayout
          android:id="@+id/titleBar"
          android:layout_width="match_parent"
          android:layout_height="@dimen/title_bar_height"
          app:layout_constraintEnd_toEndOf="parent"
          app:layout_constraintStart_toStartOf="parent"
          app:layout_constraintTop_toBottomOf="@+id/spaceStatusBar"
          android:orientation="horizontal">

            <com.interfun.buz.common.widget.view.IconFontTextView
                android:id="@+id/iftvLeftBack"
                android:layout_width="64dp"
                app:autoRTL="true"
                android:layout_alignParentStart="true"
                android:text="@string/ic_back"
                android:textSize="24sp"
                android:gravity="center"
                android:textColor="@color/text_white_main"
                android:layout_height="match_parent"/>

            <TextView
                android:id="@+id/tvTitle"
                style="@style/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:layout_centerInParent="true"
                android:textColor="@color/text_white_main"
                android:text="@string/media_download_option"
                />

      </RelativeLayout>

    <TextView
        android:id="@+id/tvAutoDownloadTitle"
        style="@style/body"
        android:layout_width="wrap_content"
        android:layout_height="40dp"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginStart="17dp"
        app:layout_constraintTop_toBottomOf="@id/titleBar"
        android:text="@string/auto_download_title"
        android:textColor="@color/text_white_secondary"
        android:gravity="center_vertical"/>
    
    <com.interfun.buz.base.widget.round.RoundConstraintLayout
        android:id="@+id/roundAutoDownloadMedia"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginStart="17dp"
        android:layout_marginEnd="23dp"
        android:background="@color/overlay_white_6"
        app:round_radius="12dp"
        app:layout_constraintTop_toBottomOf="@id/tvAutoDownloadTitle">

        <TextView
            android:id="@+id/tvPhoto"
            style="@style/main_body"
            android:textColor="@color/text_white_main"
            android:layout_width="wrap_content"
            android:layout_height="48dp"
            android:text="@string/photos_v2"
            android:gravity="center_vertical"
            android:layout_marginStart="20dp"
            app:layout_constraintStart_toStartOf="parent"/>

        <com.interfun.buz.common.widget.view.CustomSwitchCompat
            android:id="@+id/switchAutoDownloadPhoto"
            android:layout_width="48dp"
            android:layout_height="30dp"
            android:layout_marginEnd="20dp"
            android:thumb="@drawable/common_switch_thumb"
            app:layout_constraintBottom_toBottomOf="@+id/tvPhoto"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tvPhoto"
            app:track="@drawable/common_switch_track" />


        <TextView
            android:id="@+id/tvVideo"
            style="@style/main_body"
            android:textColor="@color/text_white_main"
            android:layout_width="wrap_content"
            android:layout_height="48dp"
            app:layout_constraintTop_toBottomOf="@id/tvPhoto"
            android:text="@string/camera_video"
            android:gravity="center_vertical"
            app:layout_constraintStart_toStartOf="@id/tvPhoto"/>

        <com.interfun.buz.common.widget.view.CustomSwitchCompat
            android:id="@+id/switchAutoDownloadVideo"
            android:layout_width="48dp"
            android:layout_height="30dp"
            android:layout_marginEnd="20dp"
            android:thumb="@drawable/common_switch_thumb"
            app:layout_constraintBottom_toBottomOf="@+id/tvVideo"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tvVideo"
            app:track="@drawable/common_switch_track" />

        <View
            android:id="@+id/splitLine"
            android:layout_width="0dp"
            android:layout_height="0.4dp"
            app:layout_constraintTop_toBottomOf="@id/tvPhoto"
            app:layout_constraintStart_toStartOf="@id/tvPhoto"
            app:layout_constraintEnd_toEndOf="parent"
            android:background="@color/overlay_grey_26"/>

    </com.interfun.buz.base.widget.round.RoundConstraintLayout>

    <TextView
        android:id="@+id/tvDescription"
        style="@style/description"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/roundAutoDownloadMedia"
        android:layout_marginTop="12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginStart="17dp"
        android:layout_marginEnd="23dp"
        android:textColor="@color/text_white_secondary"
        android:text="@string/auto_download_desc"
        />

</androidx.constraintlayout.widget.ConstraintLayout>