<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="20dp"
    android:background="@drawable/ic_ai_learn_more_bg"
    android:minHeight="71dp"
    android:paddingHorizontal="20dp"
    android:paddingVertical="14.5dp">


    <TextView
        android:id="@+id/tvTips"
        style="@style/text_body_medium"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="12dp"
        android:gravity="center_vertical"
        android:text="@string/ai_can_invited_to_group"
        android:textColor="@color/text_white_main"
        app:layout_constraintEnd_toStartOf="@+id/iftvClose"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.interfun.buz.common.widget.view.LinearGradientTextView
        android:id="@+id/lgtMore"
        style="@style/text_label_medium"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:text="@string/learn_more"
        android:textSize="14sp"
        app:colorEnd="#d19c5a"
        app:colorMiddle="#7870de"
        app:colorStart="#63c0e8"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="@+id/tvTips"
        app:layout_constraintTop_toBottomOf="@+id/tvTips" />

    <com.interfun.buz.common.widget.view.LinearGradientIconFontTextView
        android:id="@+id/lgtMoreArrow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:text="@string/ic_arrow_right_rtl"
        android:textSize="14sp"
        app:colorEnd="#d19c5a"
        app:colorMiddle="#7870de"
        app:colorStart="#63c0e8"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/lgtMore"
        app:layout_constraintTop_toBottomOf="@+id/tvTips" />

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftvClose"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/ic_exit"
        android:textColor="@color/text_white_disable"
        android:textSize="16sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>