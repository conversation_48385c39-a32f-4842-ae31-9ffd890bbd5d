<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/common_secondary_background">


    <Space
        android:id="@+id/spaceStatusBar"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_height="44dp" />


    <RelativeLayout
        android:id="@+id/titleBar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/title_bar_height"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/spaceStatusBar"
        android:orientation="horizontal">

        <com.interfun.buz.common.widget.view.IconFontTextView
            android:id="@+id/iftvLeftBack"
            android:layout_width="64dp"
            app:autoRTL="true"
            android:layout_alignParentStart="true"
            android:text="@string/ic_back"
            android:textSize="24sp"
            android:gravity="center"
            android:textColor="@color/text_white_main"
            android:layout_height="match_parent"/>

        <TextView
            android:id="@+id/tvTitle"
            style="@style/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:layout_centerInParent="true"
            android:textColor="@color/text_white_main"
            android:text="@string/profile"
            />
    </RelativeLayout>

    <com.interfun.buz.common.widget.view.PortraitImageView
        android:id="@+id/ivProfile"
        android:layout_width="140dp"
        android:layout_height="140dp"
        android:layout_marginTop="40dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/titleBar"
        app:layout_constraintVertical_chainStyle="packed"/>

    <com.interfun.buz.base.widget.round.RoundView
        android:id="@+id/viewProfileMask"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/overlay_mask"
        app:layout_constraintBottom_toBottomOf="@+id/ivProfile"
        app:layout_constraintEnd_toEndOf="@+id/ivProfile"
        app:layout_constraintStart_toStartOf="@+id/ivProfile"
        app:layout_constraintTop_toTopOf="@+id/ivProfile" />

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftCamera"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/ic_camera"
        android:textColor="@color/text_white_main"
        android:textSize="40dp"
        app:layout_constraintBottom_toBottomOf="@+id/ivProfile"
        app:layout_constraintEnd_toEndOf="@+id/ivProfile"
        app:layout_constraintStart_toStartOf="@+id/ivProfile"
        app:layout_constraintTop_toTopOf="@+id/ivProfile"
        tools:ignore="SpUsage" />

    <TextView
        android:id="@+id/tvTitleName"
        style="@style/body"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginStart="40dp"
        app:layout_constraintTop_toBottomOf="@id/ivProfile"
        android:layout_marginTop="53dp"
        android:textColor="@color/text_white_secondary"
        android:text="@string/display_name"
        />

    <TextView
        android:id="@+id/tvName"
        style="@style/main_body"
        android:layout_width="0dp"
        android:layout_height="64dp"
        android:layout_marginTop="14dp"
        android:background="@drawable/common_rect_overlay_white_6_radius_8"
        android:gravity="center_vertical"
        android:maxLength="50"
        android:paddingStart="20dp"
        android:textColor="@color/text_white_main"
        android:singleLine="true"
        android:layout_marginHorizontal="40dp"
        android:lines="1"
        android:paddingEnd="45dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTitleName"
       />
    
    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftvGoSetName"
        style="@style/iconfont_16"
        android:textColor="@color/text_white_secondary"
        android:text="@string/ic_arrow_right"
        app:autoRTL="true"
        app:layout_constraintTop_toTopOf="@id/tvName"
        app:layout_constraintBottom_toBottomOf="@id/tvName"
        app:layout_constraintEnd_toEndOf="@id/tvName"
        android:layout_marginEnd="20dp" />

    <TextView
        android:id="@+id/tvTitleBuzId"
        style="@style/body"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="40dp"
        android:layout_marginTop="33dp"
        android:text="@string/buz_id"
        android:textColor="@color/text_white_secondary"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvName" />

    <TextView
        android:id="@+id/tvBuzId"
        style="@style/main_body"
        android:layout_width="0dp"
        android:layout_height="64dp"
        android:layout_marginTop="14dp"
        android:background="@drawable/common_rect_overlay_white_6_radius_8"
        android:gravity="center_vertical"
        android:maxLength="50"
        android:paddingStart="41dp"
        android:textColor="@color/text_white_main"
        android:singleLine="true"
        android:layout_marginHorizontal="40dp"
        android:lines="1"
        android:paddingEnd="45dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTitleBuzId"
        />

    <TextView
        android:id="@+id/tvSymbolAt"
        style="@style/regular"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="@id/tvBuzId"
        app:layout_constraintBottom_toBottomOf="@id/tvBuzId"
        app:layout_constraintStart_toStartOf="@id/tvBuzId"
        android:layout_marginStart="20dp"
        android:text="@string/common_symbol_at"
        android:textColor="@color/text_white_disable"/>

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftvGoSetBuzId"
        style="@style/iconfont_16"
        android:textColor="@color/text_white_secondary"
        android:text="@string/ic_arrow_right"
        app:autoRTL="true"
        app:layout_constraintTop_toTopOf="@id/tvBuzId"
        app:layout_constraintBottom_toBottomOf="@id/tvBuzId"
        app:layout_constraintEnd_toEndOf="@id/tvBuzId"
        android:layout_marginEnd="20dp" />



</androidx.constraintlayout.widget.ConstraintLayout>