package com.interfun.buz.user.view.aimarket

import com.interfun.buz.base.ktx.BindingViewHolder
import com.interfun.buz.base.ktx.DefaultCallback
import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.base.ktx.click
import com.interfun.buz.base.ktx.dp
import com.interfun.buz.base.ktx.expandClickArea
import com.interfun.buz.base.ktx.toast
import com.interfun.buz.common.base.binding.BaseBindingDelegate
import com.interfun.buz.common.constants.CommonMMKV
import com.interfun.buz.common.ktx.getStringDefault
import com.interfun.buz.common.manager.AppConfigRequestManager
import com.interfun.buz.common.utils.combineView
import com.interfun.buz.common.web.WebViewActivity
import com.interfun.buz.user.bean.AiMarketCommonBean
import com.interfun.buz.user.databinding.UserItemAiMarketLearnMoreBinding
import com.interfun.buz.user.log.UserTracker
import com.interfun.buz.user.viewmodel.AiMarketViewModel.AiMarketItem


class MarketLearnMoreItemViewDelegate(private val closeCallback: DefaultCallback) :
    BaseBindingDelegate<AiMarketItem.Guide, UserItemAiMarketLearnMoreBinding>() {
    override fun onViewHolderCreated(holder: BindingViewHolder<UserItemAiMarketLearnMoreBinding>) {
        super.onViewHolderCreated(holder)
        holder.binding.iftvClose.expandClickArea(10.dp, 10.dp)
        holder.binding.iftvClose.click {
            closeCallback.invoke()
        }
        combineView(holder.binding.lgtMore, holder.binding.lgtMoreArrow).click {
            WebViewActivity.start(holder.binding.root.context, AppConfigRequestManager.aiLearnMoreUrl.getStringDefault())
            UserTracker.onBotHelperCenterExposure()
        }
    }
}