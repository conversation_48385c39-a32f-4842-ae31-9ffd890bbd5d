package com.interfun.buz.user.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.buz.idl.user.request.RequestGetBlackList
import com.buz.idl.user.response.ResponseGetBlackList
import com.interfun.buz.common.net.newInstanceBuzNetUserServiceClient
import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.common.bean.Resp.Success
import com.interfun.buz.common.widget.toast.BuzToast
import com.interfun.buz.social.repo.UserRepository
import com.lizhi.itnet.lthrift.service.ITResponse
import com.yibasan.lizhifm.lzlogan.Logz
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * @Desc
 * @Author:<EMAIL>
 * @Date: 2022/7/25
 */
@HiltViewModel
class BlockListViewModel @Inject constructor(
    private val userRepository: UserRepository
) : ViewModel() {


    companion object {
        const val TAG = "BlockListViewModel"
    }

    val blockListFlow = userRepository.getBlockListFromCacheFlow()

    init {
        userRepository.syncBlockList()
    }

    fun unblockFriend(userId: Long) {
        viewModelScope.launch(Dispatchers.IO) {
            val resp = userRepository.unblockUser(userId)
            if (resp !is Success) {
                BuzToast.showToast(appContext, "Unblock failed, please retry")
            }
        }
    }

}