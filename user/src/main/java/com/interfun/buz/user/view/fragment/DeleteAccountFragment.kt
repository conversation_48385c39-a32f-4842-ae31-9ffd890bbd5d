package com.interfun.buz.user.view.fragment

import com.alibaba.android.arouter.launcher.ARouter
import com.interfun.buz.base.ktx.bind
import com.interfun.buz.base.ktx.click
import com.interfun.buz.base.ktx.finishActivity
import com.interfun.buz.base.ktx.initStatusBarHeight
import com.interfun.buz.common.base.binding.BaseBindingFragment
import com.interfun.buz.common.constants.PATH_USER_ACTIVITY_DELETE_ACCOUNT
import com.interfun.buz.user.R
import com.interfun.buz.user.databinding.UserFragmentDeleteAccountBinding
import com.interfun.buz.user.view.block.DeleteAccountBlock

/**
 * <AUTHOR>
 *
 * @date 2022/7/21
 *
 * @desc 个人设置Fragment
 * 宿主:[com.interfun.buz.user.view.activity.DeleteAccountActivity]
 */
class DeleteAccountFragment : BaseBindingFragment<UserFragmentDeleteAccountBinding>() {

    override fun initBlock() {
        DeleteAccountBlock(this, binding).bind(viewLifecycleOwner)
    }

    override fun initView() {
        binding.spaceStatusBar.initStatusBarHeight()

        binding.iftvLeftBack.click {
            finishActivity()
            activity?.overridePendingTransition(R.anim.anim_nav_enter_pop,R.anim.anim_nav_exit_pop)
        }
    }
}