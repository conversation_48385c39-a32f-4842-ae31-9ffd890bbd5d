package com.interfun.buz.user.view.itemdelegate

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.buz.idl.user.bean.UserInfo
import com.drakeet.multitype.ItemViewBinder
import com.interfun.buz.base.ktx.click
import com.interfun.buz.social.db.entity.BuzUser
import com.interfun.buz.social.db.entity.BuzUserComposite
import com.interfun.buz.user.R
import com.interfun.buz.user.databinding.UserItemBlocklistBinding

/**
 * @Desc
 * @Author:<EMAIL>
 * @Date: 2022/7/25
 */
class BlackFriendItemView(var callBackListener :OnCallBackListener?): ItemViewBinder<BuzUserComposite, BlackFriendItemView.MyViewHolder>() {

    override fun onCreateViewHolder(inflater: LayoutInflater, parent: ViewGroup): MyViewHolder {
        return MyViewHolder(inflater.inflate(R.layout.user_item_blocklist, parent, false))
    }

    override fun onBindViewHolder(holder: MyViewHolder, item: BuzUserComposite) {
        holder.setData(item)

    }

    inner class MyViewHolder constructor(var holderItemView: View): RecyclerView.ViewHolder(holderItemView){

        var binding: UserItemBlocklistBinding = UserItemBlocklistBinding.bind(holderItemView)

        fun setData(user: BuzUserComposite) {
            binding.ivPortrait.setPortrait(user.user.portrait)
            binding.tvUserName.text = user.fullNickName
            binding.btnUnblock.click {
                callBackListener?.onUnblockClick(user, this)
            }
        }
    }

    interface OnCallBackListener{
        fun onUnblockClick(user: BuzUserComposite,holder: MyViewHolder)
    }
}