package com.interfun.buz.user.log

import com.interfun.buz.common.constants.CommonMMKV
import com.interfun.buz.common.constants.NotificationPageSource
import com.interfun.buz.common.constants.RouterParamKey
import com.interfun.buz.common.constants.TrackConstant
import com.interfun.buz.common.utils.BuzTracker
import com.interfun.buz.user.viewmodel.BuzStorageInfo

object UserTracker {

    //个人主页，点击Buz Overlay设置入口时上报
    fun onOverlaySettingEntranceClick() {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2023021409")
            put(TrackConstant.KEY_TITLE, "个人主页-设置")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "设置选项")
            put(TrackConstant.KEY_PAGE_TYPE, "home_setting")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, "overlay")
        }
    }

    //个人主页，点击二维码时上报
    fun onQRCodeEntranceClick() {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2023051716")
            put(TrackConstant.KEY_TITLE, "我的主页")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "二维码")
            put(TrackConstant.KEY_PAGE_TYPE, "QR")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, "overlay")
        }
    }

    fun onClickAC2023092104(robot: Long,robotName:String) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2023092104")
            put(TrackConstant.KEY_TITLE, "机器人列表")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "聊天按钮")
            put(TrackConstant.KEY_PAGE_TYPE, "robot_list")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_ID, "$robot")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, "$robotName")
        }
    }

    fun onAppViewScreenAVS2023092101(source: String) {
        BuzTracker.onAppViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AVS2023092101")
            put(TrackConstant.KEY_TITLE, "机器人列表")
            put(TrackConstant.KEY_PAGE_TYPE, "robot_list")
            put(TrackConstant.KEY_SOURCE, "$source")
        }
    }

    fun aiMarketSourceConvert(source: Int): String {
        return when (source) {
            RouterParamKey.AI.AIMARKET_SOURCE_UNKNOWN -> "UNKNOWN"
            RouterParamKey.AI.AiMarket_SOURCE_HOMEPAGE -> "home"
            RouterParamKey.AI.AiMarket_SOURCE_ADDFRIEND -> "add_button"
            RouterParamKey.AI.AiMarket_SOURCE_CONTACT -> "contact"
            RouterParamKey.AI.AiMarket_SOURCE_OTHER -> "other"
            else -> "UNKNOWN"
        }
    }




    fun onBindPhoneEntranceClick() {
        val source = when(CommonMMKV.loginSource){
            1 -> "google"
            3 -> "line"
            4 -> "facebook"
            -1002 -> "email"
            else -> "unknown"

        }
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2023101006")
            put(TrackConstant.KEY_TITLE, "我的主页")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "绑定手机引导")
            put(TrackConstant.KEY_PAGE_TYPE, "home")
            put(TrackConstant.KEY_SOURCE,source)
        }
    }

    fun onUpdateBuzIdSaveClick(){
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2023101003")
            put(TrackConstant.KEY_TITLE, "用户名修改编辑页")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "保存按钮")
            put(TrackConstant.KEY_PAGE_TYPE, "name_edit")
        }
    }

    fun onUpdateBuzNameResult(source: String,userName:String,code:Int){
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2023101003")
            put(TrackConstant.KEY_RESULT_TYPE, "username_edit")
            put(TrackConstant.KEY_SOURCE, source)
            put(TrackConstant.KEY_CONTENT_NAME, userName)
            put(TrackConstant.KEY_IS_SUCCESS,if (code == 0) "success" else "fail")
            if (code != 0){
                put(TrackConstant.KEY_FAIL_REASON,code)
            }
        }
    }

    fun onUpdateBuzIdViewScreen(source:String){
        BuzTracker.onAppViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AVS2023101003")
            put(TrackConstant.KEY_TITLE, "用户名修改编辑页")
            put(TrackConstant.KEY_PAGE_TYPE, "name_edit")
            put(TrackConstant.KEY_SOURCE, source)
        }
    }

    fun onBotHelperCenterExposure() {
        BuzTracker.onAppViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID,"AVS2023111301")
            put(TrackConstant.KEY_TITLE,"AI拉群机制简介")
            put(TrackConstant.KEY_PAGE_TYPE,"AI_to_group_suggest")
            put(TrackConstant.KEY_SOURCE,"robot_list")
        }
    }


    /** 元素点击-自动音转文开关切换 **/
    fun onClickAutoAsrSwitch(isOn: Boolean) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024051303")
            put(TrackConstant.KEY_TITLE, "setting_page")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "smart_transcription")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, if(isOn) "open" else "close")
        }
    }

    /**
     *  点击下载机制按钮时上报
     */
    fun onClickAutoManualDownload(isOn: Boolean, type: String){
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024032518")
            put(TrackConstant.KEY_TITLE, "设置页")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "auto_manual_download")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT, if (isOn) "auto" else "manual")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_TYPE, type)
        }
    }

    /**
     *  当用户单击清除缓存确认页面上的确定按钮时
     */
    fun onClickClearCache(cacheSize: String){
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024062101")
            put(TrackConstant.KEY_TITLE, "clear_cache_settings")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "clear_cache")
            put(TrackConstant.KEY_PAGE_TYPE, "settings")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT, cacheSize)
        }
    }

    /**
     *  当用户选择自动清除缓存周期时
     */
    fun onClickAutoClearCache(){
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024062102")
            put(TrackConstant.KEY_TITLE, "auto_clear_cache_settings")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "auto_clear_cache")
            put(TrackConstant.KEY_PAGE_TYPE, "settings")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT,
                when (CommonMMKV.settingAutoRemoveCachePeriod) {
                    1 -> "1_day"
                    7 -> "7_day"
                    30 -> "1_month"
                    180 -> "6_month"
                    -1 -> "always_keep"
                    else -> ""
                }
            )
        }
    }

    // 当buz存储使用情况页面被曝光时
    fun onStoragePageExposure(buzStorageInfo: BuzStorageInfo) {
        BuzTracker.onAppViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AVS2024062101")
            put(TrackConstant.KEY_TITLE, "buz_storage_usage")
            put(TrackConstant.KEY_PAGE_TYPE, "settings")
            put(TrackConstant.KEY_PAGE_BUSINESS_TYPE, buzStorageInfo.buzUsedSize.toString())
            put(TrackConstant.KEY_PAGE_BUSINESS_ID, buzStorageInfo.deviceTotalStorageSize.toString())
            put(TrackConstant.KEY_PAGE_CONTENT, buzStorageInfo.availableSize.toString())
        }
    }

    /** 元素点击-关闭首页ASR预览弹窗 **/
    fun onClickSwitchAsrPreview(isOpen: Boolean) {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024082703")
            put(TrackConstant.KEY_TITLE, "transcribe_setting")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "homepage_transcribe")
            put(TrackConstant.KEY_PAGE_TYPE, "settings")
            put(TrackConstant.KEY_ELEMENT_BUSINESS_CONTENT, if (isOpen) "open" else "off")
        }
    }

    fun onClickNotificationSetting() {
        BuzTracker.onClick {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024100103")
            put(TrackConstant.KEY_TITLE, "message_settings")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "notification_settings_button")
            put(TrackConstant.KEY_PAGE_TYPE, "home_setting")
        }
    }



    // 当buz存储使用情况页面被曝光时
    fun onNotificationProblemPageExposure(source: Int) {
        val keySource = when (source) {
            NotificationPageSource.NotificationIntro.value -> "noti_intro"
            NotificationPageSource.Other.value -> "others"
            else -> "buz_oa"
        }
        BuzTracker.onAppViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AVS2024100102")
            put(TrackConstant.KEY_TITLE, "notification_settings")
            put(TrackConstant.KEY_PAGE_TYPE, "home_setting")
            put(TrackConstant.KEY_SOURCE, keySource)
        }
    }
}