package com.interfun.buz.user.view.aimarket

import android.content.Context
import android.view.ViewGroup
import coil.load
import com.interfun.buz.base.ktx.BindingViewHolder
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.base.ktx.click
import com.interfun.buz.base.ktx.isEmpty
import com.interfun.buz.common.widget.item.BaseSimpleClickItem
import com.interfun.buz.common.widget.item.OnItemCallback
import com.interfun.buz.user.R
import com.interfun.buz.user.databinding.UserItemAiMarketViewBinding
import com.interfun.buz.user.viewmodel.AiMarketViewModel.AiMarketItem

class MarketItemViewDelegate constructor(
    callBack: OnItemCallback<AiMarketItem.Bot>,
) :
    BaseSimpleClickItem<AiMarketItem.Bot, UserItemAiMarketViewBinding>(callBack) {

    override fun onCreateViewHolder(
        context: Context,
        parent: ViewGroup
    ): BindingViewHolder<UserItemAiMarketViewBinding> {
        val onCreateViewHolder = super.onCreateViewHolder(context, parent)
        onCreateViewHolder.binding.ivAIFlag.load(R.drawable.common_icon_ai_flag)
        return onCreateViewHolder
    }

    override fun onBindViewHolder(
        holder: BindingViewHolder<UserItemAiMarketViewBinding>,
        item: AiMarketItem.Bot
    ) {
        super.onBindViewHolder(holder, item)
        holder.binding.ivContactPortrait.setPortrait(item.portrait)
        holder.binding.tvName.text = item.userName
        if (item.shortDescription.isEmpty()) {
            holder.binding.tvDes.text = R.string.ai_buz_ai_character.asString()
        } else {
            holder.binding.tvDes.text = item.shortDescription
        }
    }


}