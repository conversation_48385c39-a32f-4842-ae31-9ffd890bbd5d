package com.interfun.buz.user.view.block

import android.content.Intent
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.view.View
import androidx.fragment.app.Fragment
import androidx.lifecycle.viewModelScope
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.arouter.startActivityByRouter
import com.interfun.buz.common.base.binding.BaseBindingBlock
import com.interfun.buz.common.constants.PATH_LOGIN_ACTIVITY
import com.interfun.buz.common.ktx.isSuccess
import com.interfun.buz.common.manager.AppConfigRequestManager
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.service.StorageService
import com.interfun.buz.common.web.WebViewActivity
import com.interfun.buz.common.widget.dialog.CommonAlertDialog
import com.interfun.buz.user.R
import com.interfun.buz.user.databinding.UserFragmentDeleteAccountBinding
import com.interfun.buz.user.viewmodel.DeleteAccountViewModel
import com.yibasan.lizhifm.sdk.platformtools.ResUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * <AUTHOR>
 *
 * @date 2022/6/30
 *
 * @desc 处理手机注册登录页输入及发送验证码相关逻辑
 */
class DeleteAccountBlock(
    private val fragment: Fragment,
    binding: UserFragmentDeleteAccountBinding
) :
    BaseBindingBlock<UserFragmentDeleteAccountBinding>(binding) {


    private val deleteAccountViewModel by fragment.activityViewModels<DeleteAccountViewModel>()


    private var isCheck: Boolean = false
    override fun initView() {

        binding.iftvCheck.click {
            isCheck = !isCheck
            binding.iftvCheck.text = if (isCheck) fragment.getString(R.string.ic_correct_solid) else fragment.getString(R.string.ic_correct_empty)
            binding.iftvCheck.setTextColor(if (isCheck) ResUtil.getColor(R.color.basic_primary) else  ResUtil.getColor(
                R.color.text_white_main))
            binding.btnNext.isEnabled = isCheck
        }

        binding.btnNext.click {
            showCloseAccountAlertDialog()
        }

        val highlightText = fragment.getText(R.string.user_buz_cancellation_notice)
        val content = fragment.getString(R.string.user_read_notice_tips)
        val spannableStringBuilder = SpannableStringBuilder(content)
        val urlSpan = HighlightSpan{
            WebViewActivity.start(fragment.requireContext, AppConfigRequestManager.closeAccountAgreementUrl)
            fragment.activity?.overridePendingTransition(R.anim.anim_nav_enter, R.anim.anim_nav_exit)
        }
        spannableStringBuilder.setSpan(urlSpan, content.indexOf(highlightText.toString()), content.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)

        binding.tvReadTips.text = spannableStringBuilder
        //设置超链接为可点击状态
        binding.tvReadTips.movementMethod = LinkMovementMethod.getInstance()
    }

    private fun showCloseAccountAlertDialog() {
        if (fragment.activity.isNull()) return
        CommonAlertDialog(
            context = fragment.activity!!,
            title = fragment.getString(R.string.warning),
            tips = fragment.getString(R.string.user_delete_account_warning_tips),
            isDangerousOperation = true,
            positiveText = fragment.getString(R.string.delete),
            negativeText = fragment.getString(R.string.cancel),
            positiveCallback = {
                logLineInfo("DeleteAccountBlock", LogLine.LOGIN, "showCloseAccountAlertDialog and click delete button")
                closeAccount(it)
            },
            negativeCallback = {
                it.dismiss()
            }
        ).show()
    }

    private fun closeAccount(dialog: CommonAlertDialog?) {
        deleteAccountViewModel.viewModelScope.launch {
            val resp = deleteAccountViewModel.closeAccount()
            if (resp.isSuccess) {
                routerServices<StorageService>().value?.deleteUserCacheAsync()
            }
            dialog?.dismiss()
            launch (Dispatchers.Main) {
                if (resp.isSuccess) {
                    UserSessionManager.setLoginActive(UserSessionManager.getSessionUid(), 0)
                    fragment.finishActivity()
                    startActivityByRouter(PATH_LOGIN_ACTIVITY) {
                        this.flags(Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK)
                    }
                }
            }
        }
    }

    private inner class HighlightSpan(click: () -> Unit) : ClickableSpan() {

        private val click = click
        override fun updateDrawState(textPaint: TextPaint) {
            super.updateDrawState(textPaint)
            textPaint.color = ResUtil.getColor(R.color.basic_primary)
            textPaint.isUnderlineText = false
        }

        override fun onClick(view: View) {
            click?.invoke()
        }

    }

}