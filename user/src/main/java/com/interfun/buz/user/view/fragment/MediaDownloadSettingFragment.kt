package com.interfun.buz.user.view.fragment

import com.interfun.buz.base.ktx.click
import com.interfun.buz.base.ktx.finishActivity
import com.interfun.buz.base.ktx.initStatusBarHeight
import com.interfun.buz.common.base.binding.BaseBindingFragment
import com.interfun.buz.common.constants.CommonMMKV
import com.interfun.buz.user.R
import com.interfun.buz.user.databinding.UserFragmentMediaDownloadSettingBinding
import com.interfun.buz.user.log.UserTracker


class MediaDownloadSettingFragment : BaseBindingFragment<UserFragmentMediaDownloadSettingBinding>() {
    override fun initView() {
        binding.spaceStatusBar.initStatusBarHeight()
        binding.iftvLeftBack.click {
            finishActivity()
            activity?.overridePendingTransition(R.anim.anim_nav_enter_pop, R.anim.anim_nav_exit_pop)
        }
        binding.switchAutoDownloadPhoto.isChecked = CommonMMKV.settingAutoDownloadMediaPhoto
        binding.switchAutoDownloadPhoto.setOnCheckedChangeListener { view, isChecked ->
            UserTracker.onClickAutoManualDownload(isOn = isChecked, "image")
            if (!view.isPressed) return@setOnCheckedChangeListener
            CommonMMKV.settingAutoDownloadMediaPhoto = isChecked
        }

        binding.switchAutoDownloadVideo.isChecked = CommonMMKV.settingAutoDownloadMediaVideo
        binding.switchAutoDownloadVideo.setOnCheckedChangeListener { view, isChecked ->
            UserTracker.onClickAutoManualDownload(isOn = isChecked, "video")
            if (!view.isPressed) return@setOnCheckedChangeListener
            CommonMMKV.settingAutoDownloadMediaVideo = isChecked
        }
    }

}