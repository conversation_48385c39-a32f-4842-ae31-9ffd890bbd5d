package com.interfun.buz.user.view.dialog

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.View
import com.interfun.buz.base.ktx.activityViewModels
import com.interfun.buz.base.ktx.click
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.common.constants.RouterParamKey.User.UPDATE_BUZ_ID_IN_PROFILE
import com.interfun.buz.common.ktx.uid
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.utils.combineView
import com.interfun.buz.common.widget.dialog.BaseBottomSheetDialogFragment
import com.interfun.buz.user.R
import com.interfun.buz.user.databinding.UserDialogModifyBuzidWarnningBinding
import com.interfun.buz.user.viewmodel.UserProfileUpdateViewModel

class BuzIdModifyWarningDialog:BaseBottomSheetDialogFragment() {

    override val TAG = "BuzIdModifyWarningDialog"
    private val updateViewModel by activityViewModels<UserProfileUpdateViewModel>()

    private var buzId: String? = null

    companion object{
        private const val BUZ_ID = "buz_id"
        fun newInstance(buzId: String): BuzIdModifyWarningDialog {
            val bundle = Bundle()
            bundle.putString(BUZ_ID, buzId)
            val dialog = BuzIdModifyWarningDialog()
            dialog.arguments = bundle
            return dialog
        }
    }

    private val binding: UserDialogModifyBuzidWarnningBinding by lazy {
        UserDialogModifyBuzidWarnningBinding.inflate(layoutInflater)
    }

    override fun onCreateView(): View {
        return binding.root
    }

    @SuppressLint("StringFormatInvalid")
    override fun initView(view: View?) {
        buzId = arguments?.getString(BUZ_ID)
        binding.tvTitle.text = getString(R.string.login_info_user_name_change_name_to_xxxx, buzId)
    }

    override fun initListener(view: View?) {
        combineView(binding.tvCancle,binding.vCancelClick).click { dismiss() }
        binding.btnContinue.click {
            logInfo(TAG,"initListener: userId = ${UserSessionManager.uid} ")
            if (buzId.isNullOrEmpty()){
                logInfo(TAG,"buzId is null or empty")
                return@click
            }
            binding.btnContinue.showLoading()
            updateViewModel.updateBuzId(buzId!!, UPDATE_BUZ_ID_IN_PROFILE)
        }

        updateViewModel.updateBuzIdCode.observe(this){
            binding.btnContinue.hideLoading()
            if (it.first == 0){
                dismiss()
            }
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        outState.putString(BUZ_ID,buzId)
        super.onSaveInstanceState(outState)
    }

    override fun onViewStateRestored(savedInstanceState: Bundle?) {
        super.onViewStateRestored(savedInstanceState)
        buzId = savedInstanceState?.getString(BUZ_ID)
    }
}