package com.interfun.buz.user.viewmodel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.buz.idl.user.request.RequestUpdateBuzId
import com.buz.idl.user.request.RequestUpdateUserInfo
import com.buz.idl.user.request.RequestVerifyBuzId
import com.buz.idl.user.response.ResponseUpdateUserInfo
import com.interfun.buz.common.net.newInstanceBuzNetUserServiceClient
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.bean.user.UserInfoUpdateBean
import com.interfun.buz.common.database.entity.SessionKey
import com.interfun.buz.common.eventbus.user.UserProfileUploadSuccessEvent
import com.interfun.buz.common.ktx.buzId
import com.interfun.buz.common.ktx.getName
import com.interfun.buz.common.ktx.isSuccess
import com.interfun.buz.common.ktx.uid
import com.interfun.buz.common.ktx.updateUserProfile
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.manager.cache.user.UserRelationCacheManager
import com.interfun.buz.common.manager.upload.CommonUploadManager
import com.interfun.buz.common.manager.upload.UploadParams
import com.interfun.buz.common.utils.PromptUtil
import com.interfun.buz.common.utils.parse
import com.interfun.buz.social.compat.UserRepositoryCompatImpl
import com.interfun.buz.user.R
import com.lizhi.itnet.lthrift.service.ITResponse
import com.lizhi.itnet.upload.common.UploadTask
import com.lizhi.itnet.upload.model.UploadStatus
import com.yibasan.lizhifm.lzlogan.Logz
import kotlinx.coroutines.*
import java.io.File

class UserProfileUpdateViewModel : ViewModel() {


    companion object{
        const val TAG = "UserProfileUpdateViewModel"

        fun getErrorTip(verifyState: Int): String? {
            return when (verifyState) {
                BuzIDVerifyState.EXIST.value -> R.string.login_info_user_name_had_been_taken.asString()
                BuzIDVerifyState.FORMAT_ERROR.value -> R.string.login_info_user_name_had_been_taken.asString()
                BuzIDVerifyState.OFFENSIVE_WORD_ERROR.value -> R.string.login_info_user_name_not_use_offensive.asString()
                BuzIDVerifyState.TOO_MORE_TIMES_ERROR.value -> R.string.login_info_user_name_change_once_a_year.asString()
                BuzIDVerifyState.END_ERROR.value -> R.string.login_info_user_name_must_end_with_letter.asString()
                BuzIDVerifyState.TOO_SHORT_ERROR.value -> R.string.username_must_at_least_5_characters.asString()
                BuzIDVerifyState.NETWORK_ERROR.value -> R.string.network_error.asString()
                BuzIDVerifyState.NOT_VERIFY.value -> R.string.tips_network_error.asString()
                else -> return null
            }
        }
    }

    private val updateService by lazy { newInstanceBuzNetUserServiceClient() }
    private val buzIdMinLength = 5
    val updateBuzIdCode = MutableLiveData<Pair<Int,String>>()
    val verifyBuzIdResult = MutableLiveData<Pair<String,Int>>()
    val updatePortraitResult = MutableLiveData<String>()

    /**
     * @param updateType 1 普通修改，2 初始化基本信息（注册）
     */
    suspend fun updateProfile(updateBean: UserInfoUpdateBean,updateType : Int): ITResponse<ResponseUpdateUserInfo> {
        val resp = updateService.updateUserInfo(RequestUpdateUserInfo(updateBean.toJson(),updateType))
        PromptUtil.parse(resp.data?.prompt)
        Logz.tag(TAG).d("updateUserInfo code:${resp.code},msg:${resp.msg},portraitUrl:${resp.data?.userInfo?.portrait}" +
                " ,name:${resp.data?.userInfo?.getName()}")
        return resp
    }

    fun updateBuzId(buzId: String, source: Int) {
        launch(Dispatchers.IO) {
            val request = RequestUpdateBuzId(buzId, source)
            val resp = updateService.updateBuzId(request)
            resp.data?.prompt.parse()
            logInfo(TAG, "updateBuzId: code = ${resp.code},msg = ${resp.msg}")
            if(resp.isSuccess){
                UserSessionManager.setSessionValue(UserSessionManager.uid,SessionKey.KEY_USER_BUZ_ID,buzId)
            }
            updateBuzIdCode.postValue(Pair(resp.code,buzId))
        }
    }

    fun verifyBuzID(buzID: String, delayTime: Long): Job {
        return launch(Dispatchers.IO) {
            delay(delayTime)

            if (buzID.isEmpty() || buzID == UserSessionManager.buzId) {
                verifyBuzIdResult.postValue(Pair(buzID,BuzIDVerifyState.NOT_VERIFY.value))
                return@launch
            }

            val localVerifyBuzIdResult = localVerifyBuzId(buzID)
            if (localVerifyBuzIdResult != BuzIDVerifyState.SUCCESS){
                verifyBuzIdResult.postValue(Pair(buzID,localVerifyBuzIdResult.value))
                return@launch
            }

            verifyBuzIdResult.postValue(Pair(buzID,BuzIDVerifyState.CHECKING.value))

            val request = RequestVerifyBuzId(buzID)
            if (!isActive) return@launch
            logInfo(TAG,"verifyBuzID:start verifyBuzId")
            val resp = updateService.verifyBuzId(request)
            val result = Pair(buzID, resp.data?.result?:resp.code)
            if (!isActive) return@launch
            verifyBuzIdResult.postValue(result)
        }
    }

    fun localVerifyBuzId(buzID:String):BuzIDVerifyState{
        if (!isEndLegal(buzID)){
            return BuzIDVerifyState.END_ERROR
        }

        if (isBuzIdTooShort(buzID)){
            return BuzIDVerifyState.TOO_SHORT_ERROR
        }

        return BuzIDVerifyState.SUCCESS
    }



    private fun isBuzIdTooShort(buzId: String):Boolean{
        return buzId.length < buzIdMinLength
    }
    private fun isEndLegal(buzId: String):Boolean{
        return buzId.last().isLetterOrDigit()
    }

     suspend fun getLastModifyTime(buzID: String):Pair<Boolean,Long> = withContext(Dispatchers.IO){
         val request = RequestVerifyBuzId(buzID)
         val resp = updateService.verifyBuzId(request)
         Pair(resp.isSuccess,resp.data?.lastModifyTime?:System.currentTimeMillis())
     }


    fun uploadProfile(file: File): UploadTask?  {
        val uploadParams = UploadParams()
        uploadParams.path = file.absolutePath
        val task = CommonUploadManager.upload(uploadParams)
        if (task.isNull()) return null
        CommonUploadManager.registerListenerUntilComplete(task!!.taskId){
            val path = task.uploadPath
            log(TAG,"uploadProfile: status = $it")
            if (it != UploadStatus.SUCCESS) return@registerListenerUntilComplete
            log(TAG, "onSuccess: path = $path")
            val updateBean = UserInfoUpdateBean(portrait = path)

            GlobalScope.launch {
                log(TAG,"uploadProfile: begin updateProfile")
                val resp = updateProfile(updateBean, 1)
                log(TAG,"uploadProfile: isSuccess = ${resp.isSuccess}")
                if (!resp.isSuccess) return@launch
                resp.data?.userInfo?.let { info->
                    UserSessionManager.updateUserProfile(info)
                    UserRepositoryCompatImpl.userRepository.updateUser(info)
                    info.portrait?.let { portraitUrl ->
                        updatePortraitResult.postValue(portraitUrl)
                    }
                }
                UserProfileUploadSuccessEvent.post(UserSessionManager.uid)
            }
        }

        return task
    }
}

enum class BuzIDVerifyState(val value:Int){
    NETWORK_ERROR(-6),
    NOT_VERIFY(-1),
    SUCCESS(0),
    EXIST(1),
    FORMAT_ERROR(2),
    OFFENSIVE_WORD_ERROR(3),
    TOO_MORE_TIMES_ERROR(4),
    CHECKING(5),
    END_ERROR(6),
    TOO_SHORT_ERROR(7)
}
