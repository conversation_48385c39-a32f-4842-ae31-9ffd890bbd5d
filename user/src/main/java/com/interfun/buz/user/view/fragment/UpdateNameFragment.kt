package com.interfun.buz.user.view.fragment

import androidx.core.widget.doAfterTextChanged
import androidx.lifecycle.viewModelScope
import com.interfun.buz.base.ime.IMEMarginAdjustment
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.base.binding.BaseBindingFragment
import com.interfun.buz.common.bean.user.UserInfoUpdateBean
import com.interfun.buz.common.ktx.firstName
import com.interfun.buz.common.ktx.isSuccess
import com.interfun.buz.common.ktx.lastName
import com.interfun.buz.common.ktx.updateUserProfile
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.user.databinding.UserFragmentEditNameBinding
import com.interfun.buz.user.viewmodel.UserProfileUpdateViewModel
import kotlinx.coroutines.launch

class UpdateNameFragment: BaseBindingFragment<UserFragmentEditNameBinding>() {
    private val updateViewModel by fragmentViewModels<UserProfileUpdateViewModel>()


    override fun initView() {
        binding.spaceStatusBar.initStatusBarHeight()
        binding.cbSave.adjustViewAboveIME(
            adjustment = IMEMarginAdjustment(40.dp),
            gapBetweenViewAndIME = 20.dp
        )

        binding.cbSave.setDisable(true)
        binding.edtFirstName.setText(UserSessionManager.firstName)
        binding.edtLastName.setText(UserSessionManager.lastName)
        binding.edtFirstName.showKeyboard()
        binding.edtFirstName.setSelection(binding.edtFirstName.text.length)
        initListener()
    }

    private fun initListener() {
        binding.iftvLeftBack.click {
            onBackPressed()
        }

        binding.edtFirstName.setOnFocusChangeListener { _, hasFocus ->
            if (hasFocus && binding.edtFirstName.text.toString().isNotEmpty()) {
                binding.iftvClearFirstName.visible()
                return@setOnFocusChangeListener
            }
            if (!hasFocus){
                binding.iftvClearFirstName.gone()
            }
        }

        binding.edtFirstName.doAfterTextChanged {
            binding.iftvClearFirstName.visibleIf(binding.edtFirstName.text.toString().isNotEmpty())
            updateSaveButtonState()
        }

       binding.edtLastName.setOnFocusChangeListener { _, hasFocus ->
           if (hasFocus && binding.edtLastName.text.toString().isNotEmpty()) {
               binding.iftvClearLastName.visible()
               return@setOnFocusChangeListener
           }
           if (!hasFocus){
               binding.iftvClearLastName.gone()
           }
       }

        binding.edtLastName.doAfterTextChanged {
            binding.iftvClearLastName.visibleIf(binding.edtLastName.text.toString().isNotEmpty())
            updateSaveButtonState()
        }

        binding.iftvClearFirstName.expandClickArea(10.dp,10.dp)
        binding.iftvClearFirstName.click {
            binding.edtFirstName.clearText()
        }
        binding.iftvClearLastName.expandClickArea(10.dp,10.dp)
        binding.iftvClearLastName.click {
            binding.edtLastName.clearText()
        }

        binding.cbSave.click {
            val firstName = binding.edtFirstName.text?.toString()?.trim()
            val lastName = binding.edtLastName.text?.toString()?.trim()
            if (firstName.isNullOrEmpty() && lastName.isNullOrEmpty()) {
                return@click
            }
            updateViewModel.viewModelScope.launch {
                val resp = updateViewModel.updateProfile(
                    UserInfoUpdateBean(
                        firstName = firstName,
                        lastName = lastName
                    ),
                    1
                )
                if (resp.isSuccess) {
                    resp.data?.userInfo?.let { UserSessionManager.updateUserProfile(it) }
                    finishActivity()
                }
            }
        }
    }

    private fun updateSaveButtonState(){
        val firstName = binding.edtFirstName.text?.toString()?.trim()
        val lastName = binding.edtLastName.text?.toString()?.trim()
        val isNameEmpty = firstName.isEmpty() && lastName.isEmpty()
        val isFirstNameChanged = firstName != UserSessionManager.firstName
        val isLastNameChanged = lastName != UserSessionManager.lastName
        binding.cbSave.setDisable(isNameEmpty || !(isFirstNameChanged || isLastNameChanged))
    }
}