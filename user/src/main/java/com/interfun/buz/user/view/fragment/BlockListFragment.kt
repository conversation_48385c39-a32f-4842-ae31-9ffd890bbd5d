package com.interfun.buz.user.view.fragment

import android.view.View
import android.view.animation.OvershootInterpolator
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.DiffUtil.ItemCallback
import androidx.recyclerview.widget.LinearLayoutManager
import com.buz.idl.user.bean.UserInfo
import com.drakeet.multitype.MultiTypeAdapter
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.base.binding.BaseBindingFragment
import com.interfun.buz.common.widget.dialog.CommonAlertDialog
import com.interfun.buz.common.widget.recyclerview.adapter.AsyncDiffMultiTypeAdapter
import com.interfun.buz.common.widget.recyclerview.itemanimators.ScaleInAnimator
import com.interfun.buz.social.db.entity.BuzUserComposite
import com.interfun.buz.user.R
import com.interfun.buz.user.databinding.UserFragmentBlocklistBinding
import com.interfun.buz.user.view.itemdelegate.BlackFriendItemView
import com.interfun.buz.user.viewmodel.BlockListViewModel
import dagger.hilt.android.AndroidEntryPoint

/**
 * <AUTHOR>
 *
 * @date 2022/7/25
 *
 * @desc 黑名单列表页面Fragment
 * 宿主:[com.interfun.buz.user.view.activity.BlockListActivity]
 */

@AndroidEntryPoint
class BlockListFragment : BaseBindingFragment<UserFragmentBlocklistBinding>() {
    private val mAdapter = AsyncDiffMultiTypeAdapter(object : ItemCallback<BuzUserComposite>() {
        override fun areItemsTheSame(
            oldItem: BuzUserComposite,
            newItem: BuzUserComposite
        ): Boolean {
            return oldItem.user.userId == newItem.user.userId
        }

        override fun areContentsTheSame(
            oldItem: BuzUserComposite,
            newItem: BuzUserComposite
        ): Boolean {
            return oldItem == newItem
        }
    })

    private val blockListViewModel by viewModels<BlockListViewModel>()
    override fun initData() {
        super.initData()
        blockListViewModel.blockListFlow.collectInScope(viewLifecycleScope){
            updateEmptyUI(it.isEmpty())
            mAdapter.submitList(it)
        }
    }

    private fun updateEmptyUI(isEmpty : Boolean) {
        binding.emptyDataView.visibleIf(isEmpty)
        binding.rlvBlockList.visibleIf(!isEmpty)
    }
    override fun initView() {
        binding.spaceStatusBar.initStatusBarHeight()

        mAdapter.register(BlackFriendItemView(object : BlackFriendItemView.OnCallBackListener {
            override fun onUnblockClick(
                user: BuzUserComposite,
                holder: BlackFriendItemView.MyViewHolder
            ) {
                confirmUnblockFriend(user)
            }
        }))

        binding.iftvLeftBack.click {
            finishActivity()
            activity?.overridePendingTransition(R.anim.anim_nav_enter_pop, R.anim.anim_nav_exit_pop)
        }

        binding.rlvBlockList.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL,false)
            adapter = mAdapter
            itemAnimator = ScaleInAnimator(OvershootInterpolator()).apply {
                addDuration = 300
                removeDuration = 100
                moveDuration = 200
                changeDuration = 200
            }
        }


    }

    private fun confirmUnblockFriend(user: BuzUserComposite) {
        activity?.let { activity ->
            CommonAlertDialog(
                context = activity,
                tips = fragment.getString(R.string.user_are_you_sure_to_unblock, user.fullNickName),
                positiveText = fragment.getString(R.string.unblock),
                negativeText = fragment.getString(R.string.cancel),
                positiveCallback = {
                    blockListViewModel.unblockFriend(user.user.userId)
                    it.dismiss()
                },
                negativeCallback = {
                    it.dismiss()
                }
            ).show()
        }
    }
}