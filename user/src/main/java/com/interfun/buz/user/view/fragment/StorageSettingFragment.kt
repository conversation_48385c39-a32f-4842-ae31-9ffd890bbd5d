package com.interfun.buz.user.view.fragment

import android.graphics.Typeface
import android.text.SpannableString
import android.text.style.StyleSpan
import android.view.Gravity
import android.widget.Toast
import androidx.fragment.app.viewModels
import com.interfun.buz.base.ktx.IconToastStyle
import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.base.ktx.appStringContext
import com.interfun.buz.base.ktx.asColor
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.base.ktx.backgroundColor
import com.interfun.buz.base.ktx.click
import com.interfun.buz.base.ktx.collectIn
import com.interfun.buz.base.ktx.finishActivity
import com.interfun.buz.base.ktx.fragment
import com.interfun.buz.base.ktx.gone
import com.interfun.buz.base.ktx.initStatusBarHeight
import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.base.ktx.requireContext
import com.interfun.buz.base.ktx.toastIconFontMsg
import com.interfun.buz.base.ktx.visible
import com.interfun.buz.common.base.binding.BaseBindingFragment
import com.interfun.buz.common.constants.CommonMMKV
import com.interfun.buz.common.ktx.toastSolidCorrect
import com.interfun.buz.common.utils.StorageUtil.toReadableFileSize
import com.interfun.buz.common.widget.dialog.CommonAlertDialog
import com.interfun.buz.common.widget.dialog.bottomlist.CommonBottomListDialog
import com.interfun.buz.user.R
import com.interfun.buz.user.databinding.UserFragmentStorageSettingBinding
import com.interfun.buz.user.log.UserTracker
import com.interfun.buz.user.viewmodel.BuzStorageInfo
import com.interfun.buz.user.viewmodel.StorageSettingViewModel
import com.lizhi.component.basetool.common.AppStateWatcher


class StorageSettingFragment : BaseBindingFragment<UserFragmentStorageSettingBinding>() {

    companion object {
        const val TAG = "StorageSettingFragment"
        const val TAG_DIALOG_AUTO_CLEAR_CACHE = "TAG_DIALOG_AUTO_CLEAR_CACHE"
    }

    private val storageSettingViewModel by fragment.viewModels<StorageSettingViewModel>()
    private val MINIMUM_BUZ_STORAGE_USAGE_DISPLAY = 4
    private val MINIMUM_APPS_AND_OTHER_ITEMS_DISPLAY = 5
    private var buzCacheSize: Long = 0L

    override fun initView() {
        binding.spaceStatusBar.initStatusBarHeight()
        binding.iftvLeftBack.click {
            finishActivity()
            activity?.overridePendingTransition(R.anim.anim_nav_enter_pop, R.anim.anim_nav_exit_pop)
        }
        binding.viewAlertPageMask.click { }
        binding.btnClearCache.click {
            showConfirmAlertDialog()
        }
        binding.roundAutoCacheRemoved.click {
            showSetAutoCacheDurationDialog()
        }
        binding.tvRetentionPeriodValue.text =
            when (CommonMMKV.settingAutoRemoveCachePeriod) {
                1 -> R.string.auto_remove_1_day.asString()
                7 -> R.string.auto_remove_7_days.asString()
                30 -> R.string.auto_remove_1_month.asString()
                180 -> R.string.auto_remove_6_months.asString()
                -1 -> R.string.auto_remove_always_keep.asString()
                else -> ""
            }
    }

    override fun initData() {
        super.initData()
        // 更新页面数据
        storageSettingViewModel.buzStorageInfoStateFlow.collectIn(fragment.viewLifecycleOwner) { storageInfo ->
            if (storageInfo != null) {
                updateStorageUsageStats(storageInfo)
            }
        }
        // 通知缓存已清除完毕
        storageSettingViewModel.cacheRemovedSuccess.collectIn(fragment.viewLifecycleOwner) {
            dismissCacheRemoveLoading()
        }
        storageSettingViewModel.getStorageData()
        AppStateWatcher.addForegroundWatcher { storageSettingViewModel.getStorageData() }
    }

    /**
     * 更新页面UI
     */
    private fun updateStorageUsageStats(storageData: BuzStorageInfo) {
        // Set text values of Buz Storage Usage
        binding.tvAppSize.text = storageData.buzUsedSize.toReadableFileSize()
        binding.tvAvailableSize.text = storageData.availableSize.toReadableFileSize()
        binding.tvCacheUsageSize.text = storageData.buzCacheSize.toReadableFileSize()
        buzCacheSize = storageData.buzCacheSize

        // Set button text
        val btnClearCacheText = if (storageData.buzCacheSize < 1000L) {
            R.string.clear_cache.asString()
        } else {
            val boldText = storageData.buzCacheSize.toReadableFileSize()
            val originalClearCacheText = appStringContext.getString(
                R.string.clear_cache_with_size,
                storageData.buzCacheSize.toReadableFileSize()
            )
            val startIndex = originalClearCacheText.indexOf(boldText)
            val endIndex = startIndex + boldText.length
            SpannableString(originalClearCacheText).apply {
                setSpan(
                    StyleSpan(Typeface.BOLD),
                    startIndex,
                    endIndex,
                    SpannableString.SPAN_INCLUSIVE_EXCLUSIVE
                )
            }
        }
        binding.btnClearCache.setText(btnClearCacheText)

        // Set progress bar
        updateProgressBar(storageData)
    }

    /**
     * 更新库存进度条UI
     */
    private fun updateProgressBar(storageData: BuzStorageInfo) {
        if (storageData.deviceTotalStorageSize == 0L) return
        val secondaryProgress =
            ((storageData.deviceTotalStorageSize - storageData.availableSize) * 100 / storageData.deviceTotalStorageSize).toInt()
        val buzUsedProgress =
            (storageData.buzUsedSize * 100 / storageData.deviceTotalStorageSize).toInt()

        binding.progressStorageSize.max = 100
        binding.progressStorageSize.secondaryProgress =
            if (secondaryProgress < MINIMUM_APPS_AND_OTHER_ITEMS_DISPLAY)
                MINIMUM_APPS_AND_OTHER_ITEMS_DISPLAY
            else
                secondaryProgress
        binding.progressStorageSize.progress =
            if (buzUsedProgress < MINIMUM_BUZ_STORAGE_USAGE_DISPLAY)
                MINIMUM_BUZ_STORAGE_USAGE_DISPLAY
            else
                buzUsedProgress
    }

    private fun showConfirmAlertDialog() {
        binding.viewAlertPageMask.visible()
        binding.viewAlertPageMask.backgroundColor(R.color.overlay_page_mask.asColor())

        CommonAlertDialog(
            context = requireContext,
            title = R.string.clear_cache_dialog_title.asString(),
            tips = R.string.clear_cache_dialog_desc.asString(),
            positiveText = R.string.ok.asString(),
            positiveCallback = {
                showCacheRemoveLoading()
                UserTracker.onClickClearCache(buzCacheSize.toString())
                binding.viewAlertPageMask.backgroundColor(R.color.transparent.asColor())
                storageSettingViewModel.deleteUserCache()
                it.dismiss()
            },
            negativeText = R.string.cancel.asString(),
            negativeCallback = {
                binding.viewAlertPageMask.gone()
                it.dismiss()
            },
            canceledOnTouchOutside = false
        ).show()
    }

    private fun showCacheRemoveLoading() {
        showDataLoading(
            message = R.string.clearing_cache_loading_dialog.asString()
        )
    }

    private fun dismissCacheRemoveLoading() {
        hideDataLoading()
        clearingCacheSucceedDialog()
    }

    private fun showSetAutoCacheDurationDialog() {
        CommonBottomListDialog.build {
            addTag(TAG_DIALOG_AUTO_CLEAR_CACHE)
            addSelectOption(
                title = R.string.auto_remove_1_day.asString(),
                isSelect = CommonMMKV.settingAutoRemoveCachePeriod == 1
            ) {
                onAutoCachePeriodClicked(1)
            }
            addSelectOption(
                title = R.string.auto_remove_7_days.asString(),
                isSelect = CommonMMKV.settingAutoRemoveCachePeriod == 7
            ) {
                onAutoCachePeriodClicked(7)
            }
            addSelectOption(
                title = R.string.auto_remove_1_month.asString(),
                isSelect = CommonMMKV.settingAutoRemoveCachePeriod == 30
            ) {
                onAutoCachePeriodClicked(30)
            }
            addSelectOption(
                title = R.string.auto_remove_6_months.asString(),
                isSelect = CommonMMKV.settingAutoRemoveCachePeriod == 180
            ) {
                onAutoCachePeriodClicked(180)
            }
            addSelectOption(
                title = R.string.auto_remove_always_keep.asString(),
                isSelect = CommonMMKV.settingAutoRemoveCachePeriod == -1
            ) {
                onAutoCachePeriodClicked(-1)
            }
        }.showDialog(parentFragmentManager)
    }

    private fun onAutoCachePeriodClicked(period: Int) {
        CommonMMKV.settingAutoRemoveCachePeriod = period
        UserTracker.onClickAutoClearCache()
        binding.tvRetentionPeriodValue.text =
            when (period) {
                1 -> R.string.auto_remove_1_day.asString()
                7 -> R.string.auto_remove_7_days.asString()
                30 -> R.string.auto_remove_1_month.asString()
                180 -> R.string.auto_remove_6_months.asString()
                -1 -> R.string.auto_remove_always_keep.asString()
                else -> ""
            }
        logDebug(TAG, "onAutoCachePeriodClicked: $period")
    }

    private fun clearingCacheSucceedDialog() {
        toastSolidCorrect(R.string.cache_has_been_cleared_dialog.asString(), IconToastStyle.ICON_TOP_TEXT_BOTTOM)
        binding.viewAlertPageMask.gone()
    }
}