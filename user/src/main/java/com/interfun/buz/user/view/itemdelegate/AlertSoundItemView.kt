package com.interfun.buz.user.view.itemdelegate

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.drakeet.multitype.ItemViewBinder
import com.interfun.buz.base.ktx.asColor
import com.interfun.buz.base.ktx.click
import com.interfun.buz.base.ktx.invisible
import com.interfun.buz.base.ktx.visible
import com.interfun.buz.user.R
import com.interfun.buz.common.manager.AlertSoundItemBean
import com.interfun.buz.common.manager.AlertSoundManager
import com.interfun.buz.user.databinding.UserItemAlertSoundListBinding

/**
 * @Desc
 * @Author:<EMAIL>
 * @Date: 2022/7/25
 */
class AlertSoundItemView(var callBackListener :OnCallBackListener?): ItemViewBinder<AlertSoundItemBean, AlertSoundItemView.AlertSoundViewHolder>() {

    override fun onCreateViewHolder(inflater: LayoutInflater, parent: ViewGroup): AlertSoundViewHolder {
        return AlertSoundViewHolder(inflater.inflate(R.layout.user_item_alert_sound_list, parent, false))
    }

    override fun onBindViewHolder(holder: AlertSoundViewHolder, alertSound: AlertSoundItemBean) {
        holder.setData(alertSound)
    }

    inner class AlertSoundViewHolder constructor(var holderItemView: View): RecyclerView.ViewHolder(holderItemView){

        var binding: UserItemAlertSoundListBinding = UserItemAlertSoundListBinding.bind(holderItemView)

        @SuppressLint("ClickableViewAccessibility")
        fun setData(alertSound: AlertSoundItemBean) {
            binding.apply {
                tvAlertSongName.text = alertSound.name
                if (AlertSoundManager.getCurrentSelectItem().type == alertSound.type) {
                    tvAlertSongName.setTextColor(R.color.basic_primary.asColor())
                    iftvCheck.visible()
                } else {
                    tvAlertSongName.setTextColor(R.color.text_white_main.asColor())
                    iftvCheck.invisible()
                }
            }
            binding.root.apply {
                click {
                    callBackListener?.onItemClick(alertSound)
                }
                setOnTouchListener { _, event ->
                    when (event.action) {
                        MotionEvent.ACTION_DOWN -> {
                            binding.root.setBackgroundColor(R.color.overlay_white_10.asColor())
                        }
                        MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                            binding.root.setBackgroundColor(R.color.transparent.asColor())
                        }
                    }
                    false
                }
            }
        }
    }

    interface OnCallBackListener{
        fun onItemClick(alertSound: AlertSoundItemBean)
    }
}