package com.interfun.buz.user.view.fragment

import android.content.Intent
import androidx.activity.OnBackPressedCallback
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.arouter.startActivityByRouter
import com.interfun.buz.common.base.binding.BaseBindingFragment
import com.interfun.buz.common.constants.*
import com.interfun.buz.common.ktx.email
import com.interfun.buz.common.ktx.formatPhoneStandard
import com.interfun.buz.common.ktx.formatUnverifiedPhoneStandard
import com.interfun.buz.common.ktx.googleAccount
import com.interfun.buz.common.ktx.phone
import com.interfun.buz.common.ktx.unverifiedPhone
import com.interfun.buz.common.manager.AppConfigRequestManager
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.utils.CommonTracker
import com.interfun.buz.common.utils.language.LanguageManager
import com.interfun.buz.common.widget.button.CommonButton
import com.interfun.buz.common.widget.dialog.CommonAlertDialog
import com.interfun.buz.user.R
import com.interfun.buz.user.databinding.UserFragmentSettingBinding
import com.interfun.buz.user.viewmodel.LogoutViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 设置界面
 * <note>
 * 退出按钮功能所在的界面必须要依附在主activity，否则如果主界面被回收，这时候退出登陆再进入
 * 主界面，这个时候主activity会重新创建，并打开被销毁之前的其他fragment。
 * </note>
 */
class SettingFragment : BaseBindingFragment<UserFragmentSettingBinding>() {
    private var logoutViewModel: LogoutViewModel? = null
    private var onBackPressCallback: OnBackPressedCallback? = null

    companion object {
        val TAG = "SettingFragment"
    }

    override fun initView() {
        logoutViewModel = activity?.let {
            ViewModelProvider(it)[LogoutViewModel::class.java]
        }
        binding.spaceStatusBar.initStatusBarHeight()
        binding.tvLanguageName.text = LanguageManager.getLocaleLanguageName()
        initListener()
    }

    override fun onResume() {
        super.onResume()
        binding.tvLanguageName.text = LanguageManager.getLocaleLanguageName()

        binding.clPhoneNumber.apply {
            if (!UserSessionManager.phone.isNullOrEmpty()) {
                binding.clPhoneNumber.layoutPaddingVertical(14.dp)
                binding.tvBindPhone.gone()
                binding.iftvGoBind.gone()
                binding.tvMyPhone.text = UserSessionManager.formatPhoneStandard
                binding.tvMyPhone.visible()
                click {

                }
                return@apply
            }
            if (!UserSessionManager.unverifiedPhone.isNullOrEmpty()) {
                binding.clPhoneNumber.layoutPaddingVertical(14.dp)
                binding.tvBindPhone.visible()
                binding.iftvGoBind.visible()
                binding.tvBindPhone.text = R.string.verify.asString()
                binding.tvMyPhone.text = UserSessionManager.formatUnverifiedPhoneStandard
                binding.tvMyPhone.visible()
                click {
                    startUpdateAccountPage(
                        type = RouterParamKey.Login.TYPE_BINDING_PHONE,
                        source = RouterParamKey.Login.SOURCE_SETTING
                    )
                }
                return@apply
            }

            binding.clPhoneNumber.layoutPaddingVertical(22.dp)
            binding.tvBindPhone.text = R.string.add.asString()
            binding.tvBindPhone.visible()
            binding.iftvGoBind.visible()
            binding.tvMyPhone.gone()
            binding.tvMyPhone.text = ""
            click {
                startUpdateAccountPage(
                    type = RouterParamKey.Login.TYPE_BINDING_PHONE,
                    source = RouterParamKey.Login.SOURCE_SETTING
                )
            }
        }
        binding.clEmail.apply {
            if (UserSessionManager.email.isNullOrEmpty()&&UserSessionManager.googleAccount.isNullOrEmpty()) {
                gone()
            } else {
                visible()
                if (!UserSessionManager.googleAccount.isNullOrEmpty()) {
                    binding.tvEmail.text = R.string.google_account.asString()
                    binding.tvMyEmail.text = UserSessionManager.googleAccount
                } else {
                    binding.tvEmail.text = R.string.email.asString()
                    binding.tvMyEmail.text = UserSessionManager.email
                }
            }
        }
        binding.tvAnyMethods.visibleIf(!UserSessionManager.phone.isNullOrEmpty() && (!UserSessionManager.email.isNullOrEmpty() || !UserSessionManager.googleAccount.isNullOrEmpty()))
    }

    private fun initListener() {
        binding.root.setOnClickListener {

        }

        binding.clEditProfile.click {
            startActivityByRouter(PATH_USER_ACTIVITY_UPDATE_PROFILE, {
                withTransition(R.anim.anim_nav_enter, R.anim.anim_nav_exit)
            })
            CommonTracker.postClickEvent(
                "AC2022091410",
                "个人主页",
                "编辑头像按钮",
                "profile"
            )
        }

        binding.clDeleteAccount.click {
            startActivityByRouter(PATH_USER_ACTIVITY_DELETE_ACCOUNT, {
                withTransition(R.anim.anim_nav_enter, R.anim.anim_nav_exit)
            })
        }

        binding.clBlockList.click {
            startActivityByRouter(PATH_USER_ACTIVITY_BLOCKLIST, {
                withTransition(R.anim.anim_nav_enter, R.anim.anim_nav_exit)
            })
        }

        binding.iftvBack.click {
            onBackPressed()
        }

        binding.clLanguageSetting.click {
            startActivityByRouter(PATH_USER_ACTIVITY_LANGUAGE_SETTING, {
                withTransition(R.anim.anim_nav_enter, R.anim.anim_nav_exit)
            })
        }

        binding.clMediaDownloadSetting.click {
            startActivityByRouter(PATH_USER_ACTIVITY_MEDIA_DOWNLOAD_SETTING, {
                withTransition(R.anim.anim_nav_enter, R.anim.anim_nav_exit)
            })
        }

        binding.clStorageSetting.click {
            startActivityByRouter(PATH_USER_ACTIVITY_STORAGE_SETTING, {
                withTransition(R.anim.anim_nav_enter, R.anim.anim_nav_exit)
            })
        }

        binding.btnLogout.click {
            CommonTracker.postClickEvent(
                "AC2022091413",
                "个人主页",
                "退出登陆按钮",
                "profile"
            )
            CommonAlertDialog(
                context = activity ?: appContext,
                title = getString(R.string.user_log_out),
                tips = getString(R.string.user_log_out_tips),
                positiveText = getString(R.string.user_log_out),
                negativeText = getString(R.string.cancel),
                positiveCallback = {
                    logout(this, it)
                },
                negativeCallback = {
                    it.dismiss()
                }
            ).show()
        }
    }

    private fun startUpdateAccountPage(type: String, source: String) {
        startActivityByRouter(PATH_LOGIN_UPDATE_ACCOUNT_ACTIVITY, {
            withString(RouterParamKey.Common.JUMP_INFO, type)
            withString(RouterParamKey.Common.KEY_SOURCE, source)
            withTransition(R.anim.anim_nav_enter, R.anim.anim_nav_exit)
        })
    }

    private fun removeSelf() {
        parentFragmentManager.beginTransaction()
            .setCustomAnimations(R.anim.anim_nav_enter_pop, R.anim.anim_nav_exit_pop)
            .remove(this)
            .commitAllowingStateLoss()
    }

    override fun onDestroy() {
        super.onDestroy()
        onBackPressCallback?.remove()
    }

    private fun logout(button: CommonButton, dialog: CommonAlertDialog?) {
        if (AppConfigRequestManager.enableShowBindPhoneDialogWhenLogout) {
            showAddPhoneNumberDialog()
            return
        }
        logoutViewModel?.viewModelScope?.launch(Dispatchers.IO) {
            button.showLoading()
            logoutViewModel?.logout(1)
            button.hideLoading()
            launch(Dispatchers.Main) {
                dialog?.dismiss()
                UserSessionManager.setLoginActive(UserSessionManager.getSessionUid(), 0)
                finishActivity()
                startActivityByRouter(PATH_LOGIN_ACTIVITY) {
                    this.flags(Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK)
                }
            }
        }
    }

    private fun showAddPhoneNumberDialog() {
        CommonAlertDialog(
            context = activity ?: appContext,
            title = getString(R.string.ask_you_to_bind_phone_number),
            tips = getString(R.string.email_user_can_not_logout_tip),
            positiveText = getString(R.string.bind_phone_number),
            negativeText = getString(R.string.cancel),
            positiveCallback = {
                startUpdateAccountPage(
                    type = RouterParamKey.Login.TYPE_BINDING_PHONE,
                    source = RouterParamKey.Login.SOURCE_LOGOUT
                )
                it.dismiss()
            },
            negativeCallback = {
                it.dismiss()
            }
        ).show()
    }
}