package com.interfun.buz.user.view.activity

import android.os.Bundle
import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.animateColorAsState
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.alibaba.android.arouter.facade.annotation.Route
import com.interfun.buz.common.constants.PATH_USER_ACTIVITY_LANGUAGE_SETTING
import com.interfun.buz.common.constants.TrackConstant
import com.interfun.buz.common.utils.BuzTracker
import com.interfun.buz.common.utils.language.LanguageBean
import com.interfun.buz.compose.base.BaseComposeActivity
import com.interfun.buz.compose.components.CommonTitleBar
import com.interfun.buz.compose.components.IconFontText
import com.interfun.buz.compose.ktx.asColor
import com.interfun.buz.compose.modifier.debouncedClickable
import com.interfun.buz.compose.styles.TextStyles
import com.interfun.buz.user.R
import com.interfun.buz.user.viewmodel.LanguageSettingViewModel
import dagger.hilt.android.AndroidEntryPoint

/**
 * <AUTHOR>
 * @date 2024/5/28
 * @desc
 */
@Route(path = PATH_USER_ACTIVITY_LANGUAGE_SETTING)
@AndroidEntryPoint
class LanguageSettingActivity : BaseComposeActivity() {

    @Composable
    override fun ComposeContent() {
        LanguageSettingPage()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        onPageViewScreen()
    }

    @Composable
    private fun LanguageSettingPage(
        viewModel: LanguageSettingViewModel = viewModel()
    ) {
        val languageList = viewModel.languages.toList()
        // 当前选中的语言Flow转换为Compose中的 State，这样在发生变化的时候，就可以让所有用到的地方刷新 UI
        val selectedCodeState = viewModel.selectedLanguageCodeFlow.collectAsState()
        // 从selectedCodeState中衍生出一个新的State，用于判断是否需要显示保存按钮
        val isSaveEnabledState = remember {
            derivedStateOf { selectedCodeState.value != viewModel.localeLanguageCode }
        }
        Column(
            modifier = Modifier
                .fillMaxSize()
                .statusBarsPadding()
        ) {
            // 标题栏
            TitleBar(
                isSaveEnabledState = isSaveEnabledState,
                onSaveClick = { viewModel.saveLanguageSetting() }
            )
            // 语言列表
            Content(
                languageList = languageList,
                selectedCodeState = selectedCodeState,
                onLanguageSelected = { viewModel.onLanguageSelected(it) }
            )
        }
    }

    @Composable
    private fun TitleBar(isSaveEnabledState: State<Boolean>, onSaveClick: () -> Unit) {
        CommonTitleBar(
            title = stringResource(id = R.string.language),
            onBackClick = { finish() }
        ) {
            val saveButtonColor by animateColorAsState(
                targetValue = if (isSaveEnabledState.value) {
                    R.color.basic_primary.asColor()
                } else {
                    R.color.secondary_primary_disable.asColor()
                }
            )
            Text(
                modifier = Modifier
                    .fillMaxHeight()
                    .align(Alignment.CenterEnd)
                    .debouncedClickable(
                        enabled = isSaveEnabledState.value,
                        onClick = onSaveClick
                    )
                    .padding(horizontal = 20.dp)
                    .wrapContentSize(align = Alignment.Center),
                text = stringResource(id = R.string.save),
                style = TextStyles.labelLarge(),
                color = saveButtonColor
            )
        }
    }

    @Composable
    private fun Content(
        languageList: List<LanguageBean>,
        selectedCodeState: State<String>,
        onLanguageSelected: (LanguageBean) -> Unit
    ) {
        LazyColumn(
            modifier = Modifier.fillMaxSize()
        ) {
            items(
                items = languageList,
                // 优化 使用key避免重复渲染
                key = { it.code }
            ) { language ->
                LanguageItem(
                    language = language,
                    selectedCodeState = selectedCodeState,
                    onLanguageSelected = onLanguageSelected
                )
            }
        }
    }

    @Composable
    private fun LanguageItem(
        language: LanguageBean,
        selectedCodeState: State<String>,
        onLanguageSelected: (LanguageBean) -> Unit
    ) {
        val isSelected by remember {
            derivedStateOf {
                language.code == selectedCodeState.value
            }
        }
        // 单个语言 Item，横向排列，内部元素竖向居中
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(63.dp)
                .debouncedClickable(
                    enabled = isSelected.not(),
                    onClick = { onLanguageSelected(language) }
                )
                .padding(horizontal = 20.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            val textColor by animateColorAsState(
                targetValue = if (isSelected) {
                    R.color.basic_primary.asColor()
                } else {
                    R.color.text_white_important.asColor()
                }
            )
            Text(
                modifier = Modifier.weight(1f),
                text = language.language,
                style = TextStyles.bodyMedium(),
                // 选中状态下，显示高亮颜色
                color = textColor
            )
            // 选中状态下，显示勾选图标
            AnimatedContent(targetState = isSelected) { isSelected ->
                if (isSelected) {
                    IconFontText(
                        iconRes = R.string.ic_check,
                        iconColor = R.color.basic_primary.asColor()
                    )
                }
            }
        }
    }

    private fun onPageViewScreen() {
        BuzTracker.onPageViewScreen {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "AVS2023060801")
            put(TrackConstant.KEY_TITLE, "语言设置页")
        }
    }

    override fun finish() {
        super.finish()
        overridePendingTransition(R.anim.anim_nav_enter_pop, R.anim.anim_nav_exit_pop)
    }
}