package com.interfun.buz.user.view.activity

import android.os.Bundle
import androidx.activity.OnBackPressedCallback
import com.alibaba.android.arouter.facade.annotation.Route
import com.interfun.buz.common.base.SimpleContainerActivity
import com.interfun.buz.common.constants.PATH_USER_ACTIVITY_STORAGE_SETTING
import com.interfun.buz.user.R
import com.interfun.buz.user.view.fragment.StorageSettingFragment
import dagger.hilt.android.AndroidEntryPoint

@Route(path = PATH_USER_ACTIVITY_STORAGE_SETTING)
@AndroidEntryPoint
class StorageSettingActivity : SimpleContainerActivity() {
    private val storageFragmentTag = "StorageSettingActivity"

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val savedFragment = supportFragmentManager.findFragmentByTag(storageFragmentTag)
        if (savedFragment == null) {
            supportFragmentManager.beginTransaction()
                .add(containerId, StorageSettingFragment(), storageFragmentTag)
                .commit()
        }

        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                finish()
                overridePendingTransition(R.anim.anim_nav_enter_pop, R.anim.anim_nav_exit_pop)
            }
        })
    }

}