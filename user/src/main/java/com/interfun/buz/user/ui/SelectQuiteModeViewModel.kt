package com.interfun.buz.user.ui

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.interfun.buz.user.entity.QuiteModeFromType
import com.interfun.buz.user.entity.TimingCycle
import com.interfun.buz.user.repository.UserSettingRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * SelectQuiteModeViewModel
 * 选择模式界面的ViewModel
 */
@HiltViewModel
class SelectQuiteModeViewModel @Inject constructor(
    private val userSettingRepository: UserSettingRepository
) : ViewModel() {

    // UI状态流
    private val _uiState = MutableStateFlow(createInitialUiState())
    val uiState: StateFlow<SelectQuiteModeUiState> = _uiState.asStateFlow()

    init {
        observeUserSettings()
    }

    /**
     * 观察用户设置变化
     */
    private fun observeUserSettings() {
        viewModelScope.launch {
            combine(
                userSettingRepository.getQuiteMode(),
                userSettingRepository.getTimingQuietMode()
            ) { quiteMode, timingQuiteMode ->
                // 根据静音模式状态确定选择的模式类型
                val selectedMode = if (quiteMode.enable) {
                    ModeType.QUIET
                } else {
                    ModeType.AUTOPLAY
                }

                // 组装UI状态
                SelectQuiteModeUiState(
                    selectedMode = selectedMode,
                    isQuietTimeEnabled = timingQuiteMode?.enable ?: false,
                    quietTimeFrom = formatTime(
                        timingQuiteMode?.startHour ?: 9,
                        timingQuiteMode?.startMinute ?: 0
                    ),
                    quietTimeTo = formatTime(
                        timingQuiteMode?.endHour ?: 17,
                        timingQuiteMode?.endMinute ?: 0
                    ),
                    quietTimeRepetition = formatTimingCycle(timingQuiteMode?.timingCycle),
                    uiEvent = createUiEvent()
                )
            }.collect { newState ->
                _uiState.value = newState
            }
        }
    }

    /**
     * 创建初始UI状态
     */
    private fun createInitialUiState(): SelectQuiteModeUiState {
        return SelectQuiteModeUiState(
            selectedMode = ModeType.AUTOPLAY,
            isQuietTimeEnabled = false,
            quietTimeFrom = "",
            quietTimeTo = "",
            quietTimeRepetition = "",
            uiEvent = createUiEvent()
        )
    }

    /**
     * 创建UI事件处理器
     */
    private fun createUiEvent(): SelectQuiteModeUiEvent {
        return SelectQuiteModeUiEvent(
            onBackClick = ::handleBackClick,
            onModeSelected = ::handleModeSelected,
            onQuietTimeEnabledChanged = ::handleQuietTimeEnabledChanged,
            onQuietTimeFromChanged = ::handleQuietTimeFromChanged,
            onQuietTimeToChanged = ::handleQuietTimeToChanged,
            onQuietTimeRepetitionChanged = ::handleQuietTimeRepetitionChanged
        )
    }

    /**
     * 处理返回按钮点击
     */
    private fun handleBackClick() {
        // 这里可以添加返回逻辑，比如发送导航事件
        // 暂时留空，由Activity处理
    }

    /**
     * 处理模式选择
     */
    private fun handleModeSelected(mode: ModeType) {
        viewModelScope.launch {
            when (mode) {
                ModeType.AUTOPLAY -> {
                    // 关闭静音模式
                    userSettingRepository.enableQuiteMode(false, QuiteModeFromType.Manual)
                }
                ModeType.QUIET -> {
                    // 开启静音模式
                    userSettingRepository.enableQuiteMode(true, QuiteModeFromType.Manual)
                }
            }
        }
    }

    /**
     * 处理安静时间开关变化
     */
    private fun handleQuietTimeEnabledChanged(enabled: Boolean) {
        viewModelScope.launch {
            userSettingRepository.enableTimingQuiteMode(enable = enabled)
        }
    }

    /**
     * 处理开始时间变化
     */
    private fun handleQuietTimeFromChanged(timeString: String) {
        val (hour, minute) = parseTime(timeString)
        viewModelScope.launch {
            userSettingRepository.enableTimingQuiteMode(
                startHour = hour,
                startMinute = minute
            )
        }
    }

    /**
     * 处理结束时间变化
     */
    private fun handleQuietTimeToChanged(timeString: String) {
        val (hour, minute) = parseTime(timeString)
        viewModelScope.launch {
            userSettingRepository.enableTimingQuiteMode(
                endHour = hour,
                endMinute = minute
            )
        }
    }

    /**
     * 处理重复设置变化
     */
    private fun handleQuietTimeRepetitionChanged(repetition: String) {
        val timingCycle = parseTimingCycle(repetition)
        viewModelScope.launch {
            userSettingRepository.enableTimingQuiteMode(timingCycle = timingCycle)
        }
    }

    /**
     * 格式化时间显示
     */
    private fun formatTime(hour: Int, minute: Int): String {
        return "${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}"
    }

    /**
     * 解析时间字符串
     */
    private fun parseTime(timeString: String): Pair<Int, Int> {
        return try {
            val parts = timeString.split(":")
            if (parts.size == 2) {
                val hour = parts[0].toInt()
                val minute = parts[1].toInt()
                hour to minute
            } else {
                0 to 0
            }
        } catch (e: Exception) {
            0 to 0
        }
    }

    /**
     * 格式化定时周期显示
     */
    private fun formatTimingCycle(timingCycle: TimingCycle?): String {
        return when (timingCycle) {
            is TimingCycle.Daily -> "Daily"
            is TimingCycle.WeekDays -> "Weekdays"
            null -> "Weekdays"
        }
    }

    /**
     * 解析定时周期
     */
    private fun parseTimingCycle(repetition: String): TimingCycle {
        return when (repetition) {
            "Daily" -> TimingCycle.Daily
            "Weekdays" -> TimingCycle.WeekDays
            else -> TimingCycle.WeekDays
        }
    }
}
