package com.interfun.buz.user.view.activity

import android.os.Bundle
import com.alibaba.android.arouter.facade.annotation.Route
import com.interfun.buz.common.base.SimpleContainerActivity
import com.interfun.buz.common.constants.KEY_SOURCE
import com.interfun.buz.common.constants.NotificationPageSource
import com.interfun.buz.common.constants.PATH_USER_ACTIVITY_NOTIFICATION
import com.interfun.buz.common.constants.RouterParamKey
import com.interfun.buz.user.R
import com.interfun.buz.user.view.fragment.NotificationFragment
import dagger.hilt.android.AndroidEntryPoint

/**
 * <AUTHOR>
 * @date 2024/9/17
 * @desc Include：
 * [com.interfun.buz.user.view.fragment.NotificationFragment]
 */
@Route(path = PATH_USER_ACTIVITY_NOTIFICATION)
@AndroidEntryPoint
class NotificationActivity : SimpleContainerActivity() {

    private val fragmentTag = "notificationFragmentTag"

    val source: Int by lazy {
        intent.getIntExtra(RouterParamKey.Common.KEY_SOURCE, NotificationPageSource.OA.value)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val savedFragment = supportFragmentManager.findFragmentByTag(fragmentTag)
        if (savedFragment == null) {
            supportFragmentManager.beginTransaction()
                .add(containerId, NotificationFragment.newInstance(source), fragmentTag)
                .commit()
        }
    }

    override fun finish() {
        super.finish()
        overridePendingTransition(R.anim.anim_nav_enter_pop, R.anim.anim_nav_exit_pop)
    }
}