package com.interfun.buz.user.view.fragment

import android.os.Bundle
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.base.binding.BaseBindingFragment
import com.interfun.buz.common.bean.user.FriendApplySource
import com.interfun.buz.common.constants.KEY_SOURCE
import com.interfun.buz.common.constants.RouterParamKey
import com.interfun.buz.common.constants.RouterParamKey.AI.AIMARKET_SOURCE_UNKNOWN
import com.interfun.buz.common.service.ContactsService
import com.interfun.buz.common.widget.item.OnItemCallback
import com.interfun.buz.common.widget.recyclerview.adapter.AsyncDiffMultiTypeAdapter
import com.interfun.buz.user.R
import com.interfun.buz.user.databinding.UserFragmentAiMarketBinding
import com.interfun.buz.user.log.UserTracker
import com.interfun.buz.user.view.aimarket.MarketItemViewDelegate
import com.interfun.buz.user.view.aimarket.MarketLearnMoreItemViewDelegate
import com.interfun.buz.user.view.aimarket.MarketTitleItemViewDelegate
import com.interfun.buz.user.viewmodel.AiMarketViewModel
import com.interfun.buz.user.viewmodel.AiMarketViewModel.AiMarketItem
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class AiMarketFragment : BaseBindingFragment<UserFragmentAiMarketBinding>() {
    private val adapter = AsyncDiffMultiTypeAdapter<AiMarketItem>(object : DiffUtil.ItemCallback<AiMarketItem>()  {
        override fun areItemsTheSame(oldItem: AiMarketItem, newItem: AiMarketItem): Boolean {
            return oldItem === newItem || (oldItem is AiMarketItem.Bot && newItem is AiMarketItem.Bot && oldItem.botUserId == newItem.botUserId)
        }

        override fun areContentsTheSame(oldItem: AiMarketItem, newItem: AiMarketItem): Boolean {
            return oldItem == newItem
        }

    })
    private val source by lazy {
        arguments?.getInt(RouterParamKey.Common.KEY_SOURCE, AIMARKET_SOURCE_UNKNOWN)
            ?: AIMARKET_SOURCE_UNKNOWN
    }
    private val viewModel by viewModels<AiMarketViewModel>()

    override fun onResume() {
        super.onResume()
        UserTracker.onAppViewScreenAVS2023092101(UserTracker.aiMarketSourceConvert(source))
    }

    override fun initView() {
        super.initView()
        binding.iftvLeftBack.click {
            finishActivity()
            activity?.overridePendingTransition(R.anim.anim_nav_enter_pop, R.anim.anim_nav_exit_pop)
        }
        binding.spaceStatusBar.initStatusBarHeight()
        binding.list.layoutManager = LinearLayoutManager(this.context)
        binding.list.adapter = adapter
        adapter.register(MarketTitleItemViewDelegate())
        adapter.register(MarketLearnMoreItemViewDelegate{
            viewModel.closeGuide()
        })
        adapter.register(
            MarketItemViewDelegate(
                // 点击除了chat按钮以外的区域
                object : OnItemCallback<AiMarketItem.Bot> {
                    override fun onItemClick(item: AiMarketItem.Bot) {
                        routerServices<ContactsService>().value!!.getProfileDialog(
                            item.botUserId,
                            source = FriendApplySource.AI_MARKET, /* chat list source */
                            businessId = null,
                        ).showDialog(<EMAIL>())
                    }
                }
            )
        )

    }

    override fun initData() {
        super.initData()
        viewModel.dataFlow.collectIn(viewLifecycleOwner) { uiState ->
            when (uiState) {
                is AiMarketViewModel.AIMarketUIState.Loading -> {
                    binding.loadingView.visible()
                    binding.loadingView.startLoading()
                }
                is AiMarketViewModel.AIMarketUIState.Success -> {
                    binding.loadingView.stopLoading()
                    binding.loadingView.gone()
                    adapter.submitList(uiState.data)
                }
            }
        }
    }

    companion object {
        fun newInstance(data: Bundle): AiMarketFragment {
            val args = Bundle(data)

            val fragment = AiMarketFragment()
            fragment.arguments = args
            return fragment
        }
    }
}