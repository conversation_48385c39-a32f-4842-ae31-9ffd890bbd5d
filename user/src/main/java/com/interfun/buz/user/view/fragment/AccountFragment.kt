package com.interfun.buz.user.view.fragment

import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import com.interfun.buz.base.ktx.activityViewModels
import com.interfun.buz.base.ktx.click
import com.interfun.buz.base.ktx.finishActivity
import com.interfun.buz.base.ktx.initStatusBarHeight
import com.interfun.buz.base.ktx.isNull
import com.interfun.buz.common.arouter.startActivityByRouter
import com.interfun.buz.common.base.binding.BaseBindingFragment
import com.interfun.buz.common.constants.PATH_LOGIN_ACTIVITY
import com.interfun.buz.common.constants.PATH_USER_ACTIVITY_DELETE_ACCOUNT
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.widget.dialog.CommonAlertDialog
import com.interfun.buz.user.R
import com.interfun.buz.user.databinding.UserFragmentAccountBinding
import com.interfun.buz.user.viewmodel.LogoutViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * <AUTHOR>
 *
 * @date 2022/7/21
 *
 * @desc 个人设置Fragment
 * 宿主:[com.interfun.buz.user.view.activity.AccountActivity]
 */
class AccountFragment : BaseBindingFragment<UserFragmentAccountBinding>() {

    private var logoutViewModel: LogoutViewModel? = null

    override fun initView() {
        logoutViewModel = activity?.let {
            ViewModelProvider(it)[LogoutViewModel::class.java]
        }
        binding.spaceStatusBar.initStatusBarHeight()

        binding.llDeleteMyAccount.click {
            startActivityByRouter(PATH_USER_ACTIVITY_DELETE_ACCOUNT, {
                withTransition(R.anim.anim_nav_enter, R.anim.anim_nav_exit)
            })
        }

        binding.iftvLeftBack.click {
            finishActivity()
            activity?.overridePendingTransition(R.anim.anim_nav_enter_pop,R.anim.anim_nav_exit_pop)
        }

        binding.llLogout.click {
            if (activity.isNull()) return@click
            CommonAlertDialog(
                context = activity!!,
                title = getString(R.string.user_log_out),
                tips = getString(R.string.user_log_out_tips),
                positiveText = getString(R.string.user_log_out),
                negativeText = getString(R.string.cancel),
                positiveCallback = {
                    logout(it)
                },
                negativeCallback = {
                    it.dismiss()
                }
            ).show()
        }
    }

    private fun logout(dialog: CommonAlertDialog?) {
        logoutViewModel?.viewModelScope?.launch {
            val resp = logoutViewModel?.logout(1)
            launch(Dispatchers.Main){
                dialog?.dismiss()
                UserSessionManager.setLoginActive(UserSessionManager.getSessionUid(), 0)
                finishActivity()
                startActivityByRouter(PATH_LOGIN_ACTIVITY)
            }
        }
    }

}