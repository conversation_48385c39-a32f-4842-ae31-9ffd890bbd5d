package com.interfun.buz.user.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.interfun.buz.common.bean.Result
import com.interfun.buz.common.bean.asResult
import com.interfun.buz.common.constants.CommonMMKV.needShowAiLearnMoreGuide
import com.interfun.buz.social.repo.BotRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class AiMarketViewModel @Inject constructor(private val botRepository: BotRepository) :
    ViewModel() {

    private val guideRefresh = MutableSharedFlow<Any>()
    private val guideFlow =
        guideRefresh.onSubscription { emit(Any()) }.map { needShowAiLearnMoreGuide }

    val dataFlow = combine(botRepository.getAllMarketBotListFlow(true), guideFlow, ::Pair)
        .asResult()
        .map { result ->
            when (result) {
                is Result.Success -> {
                    val (botList, showGuide) = result.data
                    val resultList = ArrayList<AiMarketItem>(botList.size + 2)
                    if (showGuide) {
                        resultList.add(AiMarketItem.Guide)
                    }
                    resultList.add(AiMarketItem.Title)
                    botList.forEach { bot ->
                        resultList.add(
                            AiMarketItem.Bot(
                                botUserId = bot.buzUser.userId,
                                userName = bot.buzUser.userName ?: "${bot.buzUser.userId}",
                                portrait = bot.buzUser.portrait ?: "",
                                shortDescription = bot.botExtraInfo?.shortDescription ?: ""
                            )
                        )
                    }
                    AIMarketUIState.Success(resultList)
                }

                else -> AIMarketUIState.Loading
            }

        }
        .stateIn(viewModelScope, SharingStarted.WhileSubscribed(5000L), AIMarketUIState.Loading)

    fun closeGuide() {
        viewModelScope.launch {
            needShowAiLearnMoreGuide = false
            guideRefresh.emit(Any())
        }
    }

    sealed interface AiMarketItem {
        data object Title : AiMarketItem
        data object Guide : AiMarketItem
        data class Bot(
            val botUserId: Long,
            val userName: String,
            val portrait: String,
            val shortDescription: String
        ) : AiMarketItem
    }

    sealed interface AIMarketUIState {
        data object Loading : AIMarketUIState
        data class Success(val data: List<AiMarketItem>) : AIMarketUIState
    }

}