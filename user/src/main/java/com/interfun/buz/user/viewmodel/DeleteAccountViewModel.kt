package com.interfun.buz.user.viewmodel

import androidx.lifecycle.ViewModel
import com.buz.idl.login.request.RequestCloseAccount
import com.buz.idl.login.response.ResponseCloseAccount
import com.buz.idl.login.service.BuzNetLoginServiceClient
import com.interfun.buz.base.ktx.LogLine
import com.interfun.buz.base.ktx.logLineInfo
import com.interfun.buz.common.net.withConfig
import com.interfun.buz.common.utils.PromptUtil
import com.lizhi.itnet.lthrift.service.ITResponse
import com.yibasan.lizhifm.lzlogan.Logz

class DeleteAccountViewModel : ViewModel() {

    companion object{
        const val TAG = "DeleteAccountViewModel"
    }

    private val loginService by lazy { BuzNetLoginServiceClient().withConfig() }

    suspend fun closeAccount(): ITResponse<ResponseCloseAccount> {
        val resp = loginService.closeAccount(RequestCloseAccount())
        PromptUtil.parse(resp.data?.prompt)
        logLineInfo(TAG, LogLine.LOGIN, "closeAccount code:${resp.code},msg:${resp.msg}")
        return resp
    }

}