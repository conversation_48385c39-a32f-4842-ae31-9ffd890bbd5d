package com.interfun.buz.user.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import com.interfun.buz.compose.R
import com.interfun.buz.compose.components.CommonButton
import com.interfun.buz.compose.components.CommonButtonType
import com.interfun.buz.compose.components.TimePicker
import com.interfun.buz.compose.ktx.VerticalSpace
import com.interfun.buz.compose.ktx.asColor
import com.interfun.buz.compose.modifier.debouncedClickable
import com.interfun.buz.compose.styles.TextStyles

/**
 * 时间选择对话框
 */
@Composable
fun TimePickerDialog(
    title: String,
    initialHour: Int = 9,
    initialMinute: Int = 0,
    onTimeSelected: (hour: Int, minute: Int) -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    var selectedHour by remember { mutableIntStateOf(initialHour) }
    var selectedMinute by remember { mutableIntStateOf(initialMinute) }

    Dialog(onDismissRequest = onDismiss) {
        Column(
            modifier = modifier
                .background(
                    color = R.color.color_background_2_default.asColor(),
                    shape = RoundedCornerShape(16.dp)
                )
                .padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Title
            Text(
                text = title,
                style = TextStyles.titleMedium(),
                color = R.color.text_white_main.asColor()
            )

            VerticalSpace(24.dp)

            // Time Picker
            TimePicker(
                modifier = Modifier
                    .background(
                        color = R.color.color_background_1_default.asColor(),
                        shape = RoundedCornerShape(12.dp)
                    )
                    .padding(horizontal = 20.dp, vertical = 16.dp),
                selectedHour = selectedHour,
                selectedMinute = selectedMinute,
                onTimeSelected = { hour, minute ->
                    selectedHour = hour
                    selectedMinute = minute
                }
            )

            VerticalSpace(24.dp)

            // Buttons
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                // Cancel Button
                CommonButton(
                    modifier = Modifier.weight(1f),
                    type = CommonButtonType.SECONDARY_MEDIUM,
                    text = stringResource(R.string.cancel),
                    onClick = onDismiss
                )

                // Confirm Button
                CommonButton(
                    modifier = Modifier.weight(1f),
                    type = CommonButtonType.PRIMARY_MEDIUM,
                    text = stringResource(R.string.confirm),
                    onClick = {
                        onTimeSelected(selectedHour, selectedMinute)
                        onDismiss()
                    }
                )
            }
        }
    }
}

/**
 * 重复设置选择对话框
 */
@Composable
fun RepetitionPickerDialog(
    title: String,
    selectedRepetition: String,
    onRepetitionSelected: (repetition: String) -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    val repetitionOptions = listOf(
        stringResource(R.string.default_quiet_time_repetition), // "Weekdays"
        "Daily"
    )

    Dialog(onDismissRequest = onDismiss) {
        Column(
            modifier = modifier
                .background(
                    color = R.color.color_background_2_default.asColor(),
                    shape = RoundedCornerShape(16.dp)
                )
                .padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Title
            Text(
                text = title,
                style = TextStyles.titleMedium(),
                color = R.color.text_white_main.asColor()
            )

            VerticalSpace(24.dp)

            // Options
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                repetitionOptions.forEach { option ->
                    RepetitionOptionItem(
                        text = option,
                        isSelected = option == selectedRepetition,
                        onClick = {
                            onRepetitionSelected(option)
                            onDismiss()
                        }
                    )
                }
            }

            VerticalSpace(24.dp)

            // Cancel Button
            CommonButton(
                modifier = Modifier.fillMaxWidth(),
                type = CommonButtonType.SECONDARY_MEDIUM,
                text = stringResource(R.string.cancel),
                onClick = onDismiss
            )
        }
    }
}

/**
 * 重复选项项目
 */
@Composable
private fun RepetitionOptionItem(
    text: String,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .background(
                color = if (isSelected) {
                    R.color.color_foreground_highlight_default.asColor().copy(alpha = 0.1f)
                } else {
                    R.color.color_background_3_default.asColor()
                },
                shape = RoundedCornerShape(12.dp)
            )
            .padding(16.dp)
            .debouncedClickable { onClick() },
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = text,
            style = TextStyles.labelMedium(),
            color = R.color.text_white_main.asColor()
        )

        if (isSelected) {
            Text(
                text = "✓",
                style = TextStyles.labelMedium(),
                color = R.color.color_foreground_highlight_default.asColor()
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun TimePickerDialogPreview() {
    TimePickerDialog(
        title = "Select Start Time",
        initialHour = 9,
        initialMinute = 0,
        onTimeSelected = { _, _ -> },
        onDismiss = { }
    )
}

@Preview(showBackground = true)
@Composable
private fun RepetitionPickerDialogPreview() {
    RepetitionPickerDialog(
        title = "Select Repetition",
        selectedRepetition = "Weekdays",
        onRepetitionSelected = { },
        onDismiss = { }
    )
}
