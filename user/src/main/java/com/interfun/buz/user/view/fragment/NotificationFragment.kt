package com.interfun.buz.user.view.fragment

import android.Manifest
import android.annotation.SuppressLint
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.view.Gravity
import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.base.ktx.asColor
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.base.ktx.cancelToast
import com.interfun.buz.base.ktx.click
import com.interfun.buz.base.ktx.finishActivity
import com.interfun.buz.base.ktx.fragment
import com.interfun.buz.base.ktx.initStatusBarHeight
import com.interfun.buz.base.ktx.isNotNull
import com.interfun.buz.base.ktx.logError
import com.interfun.buz.base.ktx.startActivityLauncher
import com.interfun.buz.base.ktx.toastIconFontMsg
import com.interfun.buz.base.ktx.visibleIf
import com.interfun.buz.common.base.binding.BaseBindingFragment
import com.interfun.buz.common.constants.KEY_SOURCE
import com.interfun.buz.common.constants.NotificationPageSource
import com.interfun.buz.common.constants.RouterParamKey
import com.interfun.buz.common.helper.AutoStartPermissionHelper
import com.interfun.buz.common.ktx.uid
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.utils.CommonTracker
import com.interfun.buz.common.utils.NotificationUtil
import com.interfun.buz.common.utils.NotificationUtil.REQUEST_CODE_NOTIFY
import com.interfun.buz.common.utils.PermissionHelper
import com.interfun.buz.user.R
import com.interfun.buz.user.databinding.UserFragmentNotificationBinding
import com.interfun.buz.user.log.UserTracker
import com.lizhi.component.push.lzpushsdk.PushSdkManager
import com.yibasan.lizhifm.lzlogan.Logz

/**
 * <AUTHOR>
 *
 * @date 2024/9/3
 *
 * @desc 处理通知栏设置
 * 宿主:[com.interfun.buz.user.view.activity.NotificationActivity]
 */
class NotificationFragment : BaseBindingFragment<UserFragmentNotificationBinding>() {
    private val permissionHelper = PermissionHelper(fragment)
    private val fragmentSource by lazy {
        arguments?.getInt(RouterParamKey.Common.KEY_SOURCE)?: NotificationPageSource.OA.value
    }
    private val runInBgPermissionResult = startActivityLauncher {
        CommonTracker.postBatteryOptimizationPermissionCheckResult()
    }

    companion object {
        const val TAG = "NotificationFragment"
        fun newInstance(source: Int) = NotificationFragment().apply {
            arguments = Bundle().apply {
                putInt(RouterParamKey.Common.KEY_SOURCE, source)
            }
        }
    }

    override fun initView() {
        super.initView()
        binding.spaceStatusBar.initStatusBarHeight()
        binding.iftvLeftBack.click { back() }
        binding.clAutoStartItem.click { requestAutoStart() }
        binding.clRunInBackgroundItem.click { requestIgnoreBatteryOptimizations() }
        binding.clAllowNotificationItem.click { requestEnabledNotification() }
        updateNotificationStatus()
        UserTracker.onNotificationProblemPageExposure(fragmentSource)
    }

    private fun requestAutoStart() {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M) {
            logError(TAG, "requestAutoStart ==> Not Supported")
            toast( R.string.notification_setting_run_in_background_unsupported_model.asString())
            return
        }
        val autoStarter = AutoStartPermissionHelper.getInstance()
        context?.let {
            if (autoStarter.getAutoStartPermission(it).not()) {
                logError(TAG, "requestAutoStart ==> Not Supported")
                toast( R.string.notification_setting_run_in_background_unsupported_model.asString())
            }
        }
    }

    @SuppressLint("BatteryLife")
    private fun requestIgnoreBatteryOptimizations() {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M) {
            logError(TAG, "requestIgnoreBatteryOptimizations ==> Not Supported")
            toast( R.string.notification_setting_run_in_background_unsupported_model.asString())
            return
        }
        if (NotificationUtil.isIgnoringBatteryOptimizations()) {
            logError(TAG, "requestIgnoreBatteryOptimizations ==> Already Enabled")
            toast( R.string.notification_setting_run_in_background_enabled.asString())
            return
        }
        try {
            val intent = Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS)
            intent.setData(Uri.parse("package:" + appContext.packageName))
            runInBgPermissionResult.launch(intent)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun requestEnabledNotification(){
        if (NotificationUtil.isNotifyOpen()) {
            logError(TAG, "requestEnabledNotification ==> Already Enabled")
            toast(R.string.notification_setting_allow_notification_enabled.asString())
            return
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (fragment.activity.isNotNull()){
                permissionHelper.request(
                    fragment.requireActivity(),
                    false,
                    Manifest.permission.POST_NOTIFICATIONS
                ) { result ->
                    val item = result.resultMap[Manifest.permission.POST_NOTIFICATIONS]
                    if (item?.isGranted == true) {
                        Logz.tag(TAG).i("NotifyPermissionCheckBlock refreshToken ${UserSessionManager.uid}")
                        PushSdkManager.instance.refreshToken(UserSessionManager.uid.toString())
                        CommonTracker.postNotificationPermissionCheckResult()
                    } else if (item?.hasSysDialogShown != true) {
                        NotificationUtil.requestsNotifyPermission(fragment)
                    }
                }
            }
        } else {
            NotificationUtil.requestsNotifyPermission(fragment)
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?){
        if (requestCode == REQUEST_CODE_NOTIFY){
            CommonTracker.postNotificationPermissionCheckResult()
        }
    }

    override fun onResume() {
        super.onResume()
        updateNotificationStatus()
    }

    private fun toast(message: String) {
        cancelToast()
        toastIconFontMsg(
            message = message,
            textColor = R.color.text_white_default.asColor(),
            iconFont = R.string.ic_info_solid.asString(),
            iconFontColor = R.color.text_white_important.asColor(),
            gravity = Gravity.CENTER
        )
    }

    private fun updateNotificationStatus() {
        cancelToast() // Avoid toast appear again when deny in battery optimization
        binding.tvAllowNotification.visibleIf(NotificationUtil.isNotifyOpen())
        binding.iftvAllowNotification.visibleIf(NotificationUtil.isNotifyOpen().not())
    }

    private fun back() {
        finishActivity()
        activity?.overridePendingTransition(R.anim.anim_nav_enter_pop, R.anim.anim_nav_exit_pop)
    }
}