package com.interfun.buz.user.view.fragment


import android.transition.AutoTransition
import android.transition.TransitionManager
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.arouter.startActivityByRouter
import com.interfun.buz.common.base.binding.BaseBindingFragment
import com.interfun.buz.common.constants.*
import com.interfun.buz.common.ktx.isSuccess
import com.interfun.buz.common.manager.AlertSoundManager
import com.interfun.buz.common.manager.AppConfigRequestManager
import com.interfun.buz.common.manager.AppConfigRequestManager.asrFunctionSwitch
import com.interfun.buz.common.manager.AppConfigRequestManager.smartAsrGlobalSwitch
import com.interfun.buz.common.manager.AppConfigRequestManager.smartAsrSwitch
import com.interfun.buz.common.manager.AsrSettingManager
import com.interfun.buz.common.manager.UserInfoReportManager
import com.interfun.buz.common.manager.login.LoginMainABTestManager
import com.interfun.buz.common.utils.CommonTracker
import com.interfun.buz.common.utils.combineView
import com.interfun.buz.user.R
import com.interfun.buz.user.databinding.UserFragmentNotificationSettingBinding
import com.interfun.buz.user.log.UserTracker
import com.interfun.buz.user.storage.UserSettingMMKV
import com.interfun.buz.user.viewmodel.NotificationSettingViewModel
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class NotificationSettingFragment : BaseBindingFragment<UserFragmentNotificationSettingBinding>() {

    companion object{
        val TAG = "NotificationSettingFragment"
    }
    private val oldSettingAPMAlert = CommonMMKV.settingAPMSoundsOpen
    private val oldSettingONSoundsOpen = CommonMMKV.settingONSoundsOpen
    private val oldSettingONVibrateOpen = CommonMMKV.vibration

    private val notificationSettingViewModel by viewModels<NotificationSettingViewModel>()

    override fun initData() {
        super.initData()
        notificationSettingViewModel.enableSyncDNDFlow.collectIn(viewLifecycleOwner) {
            binding.switchDND.isChecked = it
        }
    }
    override fun initView() {
        binding.spaceStatusBar.initStatusBarHeight()

        binding.iftvBack.click {
            activity?.finish()
        }


        binding.switchIANSounds.isChecked = CommonMMKV.settingONSoundsOpen
        binding.switchIANVibrate.isChecked = CommonMMKV.vibration
        binding.switchAPMAlert.isChecked = CommonMMKV.settingAPMSoundsOpen
        binding.switchSmartTranscription.isChecked = smartAsrSwitch
        //binding.switchDND.isChecked = CommonMMKV.settingSyncDND
        updateTranscriptionPreview(smartAsrSwitch)
        binding.tvAlertSound.text = AlertSoundManager.getSavedAlertSoundType().name
        // binding.switchSelfMsgASR.isChecked = selfSmartAsrSwitch

        binding.switchIANSounds.setOnCheckedChangeListener { view, isChecked ->
            if (!view.isPressed) return@setOnCheckedChangeListener
            CommonMMKV.settingONSoundsOpen = isChecked
        }

        binding.switchIANVibrate.setOnCheckedChangeListener { view, isChecked ->
            if (!view.isPressed) return@setOnCheckedChangeListener
            CommonMMKV.vibration = isChecked
        }

        binding.switchAPMAlert.setOnCheckedChangeListener { view, isChecked ->
            if (!view.isPressed) return@setOnCheckedChangeListener
            CommonMMKV.settingAPMSoundsOpen = isChecked
        }

        binding.switchDND.setOnCheckedChangeListener { view, isChecked ->
            if (!view.isPressed) return@setOnCheckedChangeListener
//            CommonMMKV.hasSettingSyncDNDInPlayA = true
//            CommonMMKV.settingSyncDND = isChecked
            notificationSettingViewModel.enableSyncDND(isChecked)
//            val chatService = routerServices<ChatService>().value
//            chatService?.let {
//                // 打开开关且打开了勿扰，且当前为非静音模式，立即设置为静音模式
//                if (isChecked && it.isInDND() && !CommonMMKV.isQuietModeEnable) {
//                    chatService.setQuietModeEnable(true)
//                }
//            }

        }

        binding.viewNotificationProblemGuidanceRedDot.visibleIf(UserSettingMMKV.hasClickedNotificationSettingEntrance.not() && AppConfigRequestManager.showNotificationProblemPopUpDialog)
        binding.clNotificationSettingItem.click {
            UserSettingMMKV.hasClickedNotificationSettingEntrance = true
            binding.viewNotificationProblemGuidanceRedDot.gone()
            activity?.startActivityByRouter(PATH_USER_ACTIVITY_NOTIFICATION, {
                withInt(RouterParamKey.Common.KEY_SOURCE, NotificationPageSource.Other.value)
                withTransition(R.anim.anim_nav_enter, R.anim.anim_nav_exit)
            })
            UserTracker.onClickNotificationSetting()
        }

        (asrFunctionSwitch && smartAsrGlobalSwitch).let {
            combineView(
                binding.clMessageTranscription,
                binding.tvMessageTranscription,
                binding.tvONTranscriptionTips,
                // binding.tvSelfMsgASR,
                // binding.clSelfMsgASR,
            ).goneIf(!it)
        }

        combineView(
            binding.iftvAlertSound,
            binding.tvAlertSoundTitle,
            binding.tvAlertSound,
            binding.alertSoundView
        ).click {
            activity?.startActivityByRouter(PATH_USER_ACTIVITY_ALERT_SOUND_SETTING, {
                withTransition(R.anim.anim_nav_enter, R.anim.anim_nav_exit)
            })
        }

        binding.switchSmartTranscription.setOnCheckedChangeListener { view, isChecked ->
            if (!view.isPressed) return@setOnCheckedChangeListener
            updateTranscriptionPreview(isChecked)
            updateAppConfig(smartAsrSwitch = if(isChecked) 1 else 2) { rCode ->
                val reportSuccess = rCode.isSuccess
                if (reportSuccess) {
                    UserTracker.onClickAutoAsrSwitch(isChecked)
                }else{
                    lifecycleScope.launchMain {
                        binding.switchSmartTranscription.isChecked = smartAsrSwitch
                        updateTranscriptionPreview(smartAsrSwitch)
                    }
                }
            }
        }

        binding.switchTranscriptionPreview.setOnCheckedChangeListener { view, isChecked ->
            if (!view.isPressed) return@setOnCheckedChangeListener
            binding.switchTranscriptionPreview.isChecked = isChecked
            AsrSettingManager.switchAsrPreview(isChecked)
            UserTracker.onClickSwitchAsrPreview(isChecked)
        }

    }

    private fun updateTranscriptionPreview(smartAsrSwitch:Boolean) {
        TransitionManager.beginDelayedTransition(binding.root, AutoTransition().apply {
            duration = 100
        })
        if (LoginMainABTestManager.isShowNewHomePagePlanB) {
            binding.groupTranscriptionPreview.gone()
        } else {
            if (smartAsrSwitch) {
                binding.groupTranscriptionPreview.visible()
            } else {
                binding.groupTranscriptionPreview.gone()
            }
        }
        binding.switchTranscriptionPreview.isChecked = AsrSettingManager.isOpenAsrPreview
    }

    private fun updateAppConfig(
        smartAsrSwitch: Int? = null, // 自动 asr 开关，仅变动时传递
        // selfSmartAsrSwitch: Int? = null,
        callback: OneParamCallback<Int>
    ) {
        UserInfoReportManager.reportUserInfo(
            smartAsrSwitch = smartAsrSwitch,
            // selfSmartAsrSwitch = selfSmartAsrSwitch
        ) { rCode ->
            val reportSuccess = rCode.isSuccess
            if (reportSuccess) {
                AppConfigRequestManager.requestAppConfigWithLogin()
            } else {
                R.string.online_chat_network_not_good.asString().toast()
            }
            callback.invoke(rCode)
        }
    }

    override fun onResume() {
        super.onResume()
        binding.tvAlertSound.text = AlertSoundManager.getSavedAlertSoundType().name
    }

    override fun onDestroy() {
        super.onDestroy()

        if (oldSettingAPMAlert != CommonMMKV.settingAPMSoundsOpen
            || oldSettingONSoundsOpen != CommonMMKV.settingONSoundsOpen
            || oldSettingONVibrateOpen != CommonMMKV.vibration
        ) {
            CommonTracker.onReportNotificationSettingResult("setting")
        }
    }

}