package com.interfun.buz.user.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.interfun.buz.compose.components.CommonRedDot
import com.interfun.buz.compose.ktx.*
import com.interfun.buz.compose.modifier.debouncedClickable
import com.interfun.buz.core.widget_liveplace.R
import com.interfun.buz.core.widget_liveplace.state.LivePlaceEntranceUIState
import com.interfun.buz.core.widget_liveplace.ui.LivePlaceEntranceButton
import com.interfun.buz.core.widget_liveplace.ui.LivePlaceProfileEntrance
import com.interfun.buz.core.widget_liveplace.utils.LivePlaceEntranceHeightHelper
import com.interfun.buz.user.viewmodel.MyLivePlaceEntranceViewModel

/**
 * <AUTHOR>
 * @date 2024/12/24
 * @desc
 */
@Composable
internal fun MyProfileLivePlaceEntrance(
    viewModel: MyLivePlaceEntranceViewModel = hiltViewModel(),
    onClickCreate: () -> Unit,
    onClickEntrance: (LivePlaceEntranceUIState) -> Unit,
    modifier: Modifier = Modifier
) {
    val uiState by viewModel.myLivePlaceUiStateFlow.collectAsStateWithLifecycle()
    val isShowRedDot by viewModel.showRedDotFlow.collectAsStateWithLifecycle()
    val createMyLivePlaceByRouter by viewModel.createLivePlaceByRouterFlow.collectAsStateWithLifecycle()
    MyProfileLivePlaceEntrance(
        uiStateLambda = { uiState },
        isShowRedDotLambda = { isShowRedDot },
        onClickCreate = onClickCreate,
        onClickEntrance = onClickEntrance,
        createMyLivePlaceByRouter = createMyLivePlaceByRouter,
        modifier = modifier
    )
}

@Composable
private fun MyProfileLivePlaceEntrance(
    uiStateLambda: () -> LivePlaceEntranceUIState,
    isShowRedDotLambda: () -> Boolean,
    onClickCreate: () -> Unit,
    onClickEntrance: (LivePlaceEntranceUIState) -> Unit,
    createMyLivePlaceByRouter: Boolean = false,
    modifier: Modifier = Modifier
) {
    val uiState = uiStateLambda()
    val height = (LivePlaceEntranceHeightHelper.getEntranceFullHeight(LocalContext.current) * 0.67f).pxToDp()
    when (uiState) {
        is LivePlaceEntranceUIState.Loading,
        is LivePlaceEntranceUIState.Created -> {
            LivePlaceProfileEntrance(
                uiStateLambda = { uiState },
                showSofaIcon = true,
                isMyProfile = true,
                onBtnClick = onClickEntrance,
                heightLambda = { height },
                modifier = modifier
            )
        }

        LivePlaceEntranceUIState.NotCreated -> {
            CreateMyLivePlace(
                isShowRedDotLambda = isShowRedDotLambda,
                onClickCreate = onClickCreate,
                modifier = modifier
                    .fillMaxWidth()
                    .height(height)
            )
            if (createMyLivePlaceByRouter) {
                onClickCreate.invoke()
            }
        }

        LivePlaceEntranceUIState.NotEnable,
        LivePlaceEntranceUIState.Unknown -> {
            VerticalSpace(100.dp)
        }
    }
}

@Composable
private fun CreateMyLivePlace(
    isShowRedDotLambda: () -> Boolean,
    onClickCreate: () -> Unit,
    modifier: Modifier = Modifier,
) {
    ConstraintLayout(modifier = modifier) {
        val (refBg, refButton, refDot) = createRefs()
        Box(
            modifier = modifier
                .fillMaxSize()
                .clip(RoundedCornerShape(bottomStart = 30.dp, bottomEnd = 30.dp))
                .background(color = R.color.color_background_5_default.asColor())
                .constrainAs(refBg) {
                    fillToParent()
                }
        )
        LivePlaceEntranceButton(
            textRes = R.string.create_my_live_place.asString(),
            showSofaIcon = true,
            isNotCreated = true,
            isMyProfile = true,
            modifier = Modifier
                .debouncedClickable(onClick = onClickCreate)
                .constrainAs(refButton) {
                    linkToParent()
                    verticalBias = 0.6f
                }
        )
        if (isShowRedDotLambda()) {
            CommonRedDot(
                strokeColor = R.color.color_background_5_default.asColor(),
                modifier = Modifier
                    .offset(x = 6.dp, y = (-4).dp)
                    .constrainAs(refDot) {
                        top.linkTo(refButton.top)
                        end.linkTo(refButton.end)
                    }
            )
        }
    }
}

@Composable
@Preview
private fun PreviewCreateLivePlace() {
    CreateMyLivePlace(
        isShowRedDotLambda = { true },
        onClickCreate = {},
    )
}