package com.interfun.buz.user.view.fragment

import android.annotation.SuppressLint
import android.transition.AutoTransition
import android.transition.TransitionManager
import androidx.core.text.buildSpannedString
import androidx.core.widget.NestedScrollView.OnScrollChangeListener
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.withResumed
import coil.load
import coil.transform.RoundedCornersTransformation
import com.airbnb.lottie.LottieDrawable
import com.alibaba.android.arouter.facade.annotation.Route
import com.interfun.buz.base.ktx.*
import com.interfun.buz.base.utils.BusUtil
import com.interfun.buz.common.arouter.NavManager
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.arouter.startActivityByRouter
import com.interfun.buz.common.base.binding.ViewPager2LazyBindingFragment
import com.interfun.buz.common.bean.feedback.H5FeedbackSource
import com.interfun.buz.common.bean.push.PushOP
import com.interfun.buz.common.constants.*
import com.interfun.buz.common.eventbus.CreateLpSuccessEvent
import com.interfun.buz.common.eventbus.HomePageChangeEvent
import com.interfun.buz.common.eventbus.HomePageEnum.PageHome
import com.interfun.buz.common.eventbus.user.UpdateFeedbackDotEvent
import com.interfun.buz.common.eventbus.user.UserProfileUploadSuccessEvent
import com.interfun.buz.common.ktx.*
import com.interfun.buz.common.manager.*
import com.interfun.buz.common.manager.realtimecall.MinimizeViewModel
import com.interfun.buz.common.manager.router.RouterManager
import com.interfun.buz.common.service.ILivePlaceService
import com.interfun.buz.common.utils.*
import com.interfun.buz.common.viewmodel.CampaignViewModel
import com.interfun.buz.common.widget.toast.BuzToast
import com.interfun.buz.im.signal.SignalManagerPresenter
import com.interfun.buz.liveplace.track.LivePlaceTracker
import com.interfun.buz.onair.helper.LivePlaceEntranceHelper
import com.interfun.buz.onair.standard.LivePlaceSource
import com.interfun.buz.onair.standard.LivePlaceType
import com.interfun.buz.user.R
import com.interfun.buz.user.databinding.UserFragmentMyProfileBinding
import com.interfun.buz.user.log.UserTracker
import com.interfun.buz.user.storage.UserSettingMMKV
import com.interfun.buz.user.ui.MyProfileLivePlaceEntrance
import com.interfun.buz.user.viewmodel.MyLivePlaceEntranceViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import org.json.JSONObject

/**
 * <AUTHOR>
 *
 * @date 2022/7/21
 *
 * @desc 个人设置Fragment
 * 宿主:[com.interfun.buz.chat.common.view.activity.ChatHomeActivity.initView]
 */
@Route(path = PATH_USER_FRAGMENT_PROFILE)
@AndroidEntryPoint
class MyProfileFragment : ViewPager2LazyBindingFragment<UserFragmentMyProfileBinding>() {

    companion object {
        const val TAG = "MyProfileFragment"
    }

    private val minimizeViewModel by activityViewModels<MinimizeViewModel>()
    private val livePlaceViewModel: MyLivePlaceEntranceViewModel by viewModels()

    private val myUid get() = UserSessionManager.uid

    override fun onResume() {
        super.onResume()
        initUserInfoView()
        updateFeedbackDot()
        updateMessageSettingDot()
        if (BindPhonePopupManager.checkNeedShowPopup()) {
            showBindPhonePopup()
        } else {
            hideBindPhonePopup()
        }
        livePlaceViewModel.refreshLivePlace()
        livePlaceViewModel.trackOnPageView()
        GlobalEventManager.createLivePlace.observe(this.viewLifecycleOwner) {
            if (it == true) {
                GlobalEventManager.createLivePlace.value = null
                //触发点击创建我的livePlace按钮
                livePlaceViewModel.createLivePlaceByRouterFlow.value = true
            }
        }
    }

    @SuppressLint("SetTextI18n")
    private fun initUserInfoView() {
        binding.ivPortrait.setPortrait(UserSessionManager.portrait, size = 140.dp)
        binding.tvUserName.text = UserSessionManager.userName
        binding.tvBuzId.goneIf(UserSessionManager.buzId.isEmpty())
        binding.tvBuzId.text = buildSpannedString {
            append(R.string.common_symbol_at.asString())
            append(UserSessionManager.buzId)
            appendSpace(4.dp)
            size(14.dp) {
                iconFontAlign {
                    typeface(FontUtil.fontIcon!!) {
                        append(R.string.ic_copy.asString())
                    }
                }
            }
        }
    }

    override fun initView() {
        handleADBanner()
        binding.spaceStatusBar.initStatusBarHeight()
        binding.tvMyName.text = UserSessionManager.userName
        BusUtil.observe<CreateLpSuccessEvent>(this) { data ->
            if (!data.isGroup && data.target == UserSessionManager.getSessionUid()) {
                this.lifecycleScope.launch {
                    <EMAIL> {
                        BuzToast.showTwoLineImgToast(
                            requireContext(),
                            imgRes = R.drawable.compose_firworks,
                            title = R.string.live_place_created.asString(),
                            subTitle = R.string.setup_your_live_place_done.asString()
                        )
                    }
                }
            }
        }
        val titleBarHeight = R.dimen.title_bar_height.asDimension()
        binding.scrollView.setOnScrollChangeListener(OnScrollChangeListener { _, _, y, _, _ ->
            val gap = binding.clRoot.height - binding.scrollView.height
            if (gap > 0) {
                val alpha = (y / minOf(gap.toFloat(), titleBarHeight)).coerceIn(0f, 1f)
                binding.viewTitleBarBg.alpha = alpha
                binding.tvMyName.alpha = alpha
            }
        })

        binding.cvLivePlaceEntrance.setContent { //xml integration with compose
            MyProfileLivePlaceEntrance(
                viewModel = livePlaceViewModel,
                onClickCreate = {
                    livePlaceViewModel.setHasShowRedDot()
                    if (livePlaceViewModel.createLivePlaceByRouterFlow.value) {
                        livePlaceViewModel.createLivePlaceByRouterFlow.value = false
                    }
                    if (!CommonMMKV.hadShownLPIntroduceDialogPrivate) {
                        routerServices<ILivePlaceService>().value?.getIntroduceLivePlaceDialog(
                            livePlaceType = LivePlaceType.PRIVATE,
                            targetId = myUid
                        )?.show(fragment.childFragmentManager, "LivePlaceIntroduceDialog")
                    } else {
                        LivePlaceEntranceHelper.createLivePlace(
                            activity = activity,
                            targetId = myUid,
                            livePlaceType = LivePlaceType.PRIVATE,
                            source = LivePlaceSource.SOURCE_PERSON_PROFILE
                        )
                    }
                    LivePlaceTracker.onAC2025010201()
                },
                onClickEntrance = {
                    livePlaceViewModel.setHasShowRedDot()
                    LivePlaceEntranceHelper.handleEnterLivePlace(
                        activity = activity,
                        targetId = myUid,
                        livePlaceType = LivePlaceType.PRIVATE,
                        traceId = TAG,
                        source = LivePlaceSource.SOURCE_PERSON_PROFILE
                    )
                }
            )
        }

        binding.iftvRightBack.click {
            BusUtil.post(HomePageChangeEvent(PageHome))
        }

        binding.ivPortrait.click {
            startActivityByRouter(PATH_USER_ACTIVITY_UPDATE_PROFILE, {
                withTransition(R.anim.anim_nav_enter, R.anim.anim_nav_exit)
            })
            CommonTracker.postClickEvent(
                "AC2022091410",
                "个人主页",
                "编辑头像按钮",
                "profile"
            )
        }

        combineView(binding.tvBuzId, binding.vBuzIdClickArea).click(vibrate = true) {
            toastSolidCorrect(
                R.string.ftue_v3_buzIdCopied.asString(),
                IconToastStyle.ICON_TOP_TEXT_BOTTOM
            )
            UserSessionManager.buzId.copyToClipboard()
            CommonTracker.onClickCopyBuzId(false)
        }

        binding.llAbout.click {
            startActivityByRouter(PATH_USER_ACTIVITY_ABOUT, {
                withTransition(R.anim.anim_nav_enter, R.anim.anim_nav_exit)
            })
        }

        binding.llNotificationSetting.click {
            startActivityByRouter(PATH_USER_ACTIVITY_NOTIFICATION_SETTING, {
                withTransition(R.anim.anim_nav_enter, R.anim.anim_nav_exit)
            })
            CommonTracker.postClickEvent(
                "AC2024100102",
                "home_settings",
                "message_settings_button",
                "home_setting"
            )
        }

        binding.llFeedback.click {
            CommonMMKV.isFeedbackEntryDotVisible = false
            FeedbackManager.jumpToFeedbackWebView(activity, RouterParamKey.Feedback.FEEDBACK_SOURCE_SETTING)
            CommonTracker.postClickEvent(
                "AC2022091411",
                "个人主页",
                "反馈按钮",
                "profile"
            )
        }

        binding.llSupport.click {
            activity?.let {
                NavManager.startWebViewActivity(it.context, AppConfigRequestManager.supportUrl)
                CommonTracker.postClickEvent(
                    "AC2022112504",
                    "对讲机首页",
                    "帮助入口",
                    "home_setting"
                )
            }
        }

        binding.viewSettingBg.click {
            startActivityByRouter(PATH_USER_ACTIVITY_SETTING, {
                withTransition(R.anim.anim_nav_enter, R.anim.anim_nav_exit)
            })
        }

        binding.viewQrCodeBg.click {
            if (context.isNull()) return@click
            UserTracker.onQRCodeEntranceClick()
            startShareQRCodeActivity(
                list = listOf(UserSessionManager.portrait),
                name = UserSessionManager.userName,
                buzId = UserSessionManager.buzId,
                type = 1,
                source = ShareQRCodeSource.UserProfile.value,
                hasAnim = true,
            )
        }

        binding.viewShareBg.click {
            if (context.isNull()) return@click
            generateShareToInviteOneLinkAndShare(requireContext()) { link ->
                CommonTracker.onClickACAC2024120603(afLinkHash = getLinkHashFromLinkUrl(link = link))
            }
            CommonTracker.postClickEvent(
                "AC2022091412",
                "个人主页",
                "推荐按钮",
                "profile"
            )
        }

        binding.viewOverlayGuidanceRedDot.visibleIf(UserSettingMMKV.hasClickedOverlaySettingEntrance.not())
        binding.llOverlaySetting.click {
            UserTracker.onOverlaySettingEntranceClick()
            UserSettingMMKV.hasClickedOverlaySettingEntrance = true
            binding.viewOverlayGuidanceRedDot.gone()
            NavManager.startOverlaySettingActivity(activity)
        }
        updateFeedbackDot()
        updateMessageSettingDot()
    }

    /**
     * 个人主页的广告页
     */
    private fun handleADBanner() {
        CampaignViewModel.activityConfig.observe(<EMAIL>) { config ->
            if (config == null || config.iconUrl.isNullOrEmpty()) {
                binding.adBanner.root.gone()
            } else {
                binding.adBanner.root.click {
                    BuzTracker.onClick {
                        put(TrackConstant.KEY_EXCLUSIVE_ID, "AC2024040812")
                        put(TrackConstant.KEY_TITLE, "设置页")
                        put(TrackConstant.KEY_ELEMENT_CONTENT, "activity_entrance")
                        put(TrackConstant.KEY_ELEMENT_BUSINESS_ID, config.id.toString())
                    }
                    val router = config.actionInfo?.router
                    if (router?.scheme.isNullOrEmpty() || router?.extraData.isNullOrEmpty()) return@click
                    val stringRouter =
                        RouterManager.create(router!!.scheme, JSONObject(router.extraData))
                    if (this.activity != null) {
                        RouterManager.handle(this.requireActivity(), stringRouter)
                    }
                }
                binding.adBanner.root.visible()
                if (config.iconUrl.endsWith("json", true)) {
//                if (false) {
                    binding.adBanner.adLottie.visible()
                    binding.adBanner.adLottie.repeatCount = LottieDrawable.INFINITE // 设置无限循环
                    binding.adBanner.adLottie.setAnimationFromUrl(config.iconUrl)
                    binding.adBanner.adLottie.playAnimation()
                    binding.adBanner.adIv.gone()
                } else {
                    binding.adBanner.adIv.visible()
                    binding.adBanner.adLottie.gone()
                    binding.adBanner.adIv.load(config.iconUrl) {
//                    binding.adBanner.adIv.load("https://img95.699pic.com/photo/40112/1956.jpg_wh300.jpg") {
                        crossfade(true) //渐进进出
//                        scale(Scale.FIT)
                        placeholder(R.drawable.campaign_entry) //加载中占位图
                        transformations(RoundedCornersTransformation(12.dpFloat))  //圆形图
                        error(R.drawable.ic_error) //加载错误占位图
                    }
                }
            }
        }

    }

    private fun updateFeedbackDot() {
        binding.viewFeedbackEntryDot.visibleIf(CommonMMKV.isFeedbackEntryDotVisible)
    }

    private fun updateMessageSettingDot() {
        binding.viewMessageSettingGuidanceRedDot.visibleIf(UserSettingMMKV.hasClickedNotificationSettingEntrance.not() && AppConfigRequestManager.showNotificationProblemPopUpDialog)
    }

    override fun initData() {
        super.initData()
        BusUtil.observe<UserProfileUploadSuccessEvent>(this) {
            initUserInfoView()
        }
        BusUtil.observe<UpdateFeedbackDotEvent>(this) {
            updateFeedbackDot()
        }
        minimizeViewModel.isMinimizingFlow.collectIn(viewLifecycleOwner) { isMinimizing ->
            if (isMinimizing) {
                binding.spaceStatusBar.layoutHeight(0.dp)
            } else {
                binding.spaceStatusBar.layoutHeight(fragment.requireContext.statusBarHeight)
            }
        }
        SignalManagerPresenter.obtainInstance().obtainSignalingFlow(filter = { signalData ->
            signalData.topic == PushOP.PUSH_LIVE_PLACE_BASIC_UPDATE.op
        }).collectIn(viewLifecycleOwner) {
            livePlaceViewModel.requestLivePlace()
        }
        livePlaceViewModel.requestLivePlace()
    }

    private fun showBindPhonePopup() {
        if (binding.clBindPhone.isVisible()) return
        binding.tvBindPhoneTips.text = buildSpannedString {
            append(R.string.make_it_easier_for_friend_to_find_you.asString())
            appendSpace(6.dp)
            autoEnterLine(14f.sp, R.color.basic_primary.asColor()) {
                appendClickable(
                    if (UserSessionManager.unverifiedPhone.isNullOrEmpty()) R.string.add.asString() else R.string.verify.asString(),
                    R.color.basic_primary.asColor(),
                    false
                ) { binding.clBindPhone.performClick() }
                iconFontAlign(color = R.color.basic_primary.asColor()) {
                    size(16.dp) {
                        typeface(FontUtil.fontIcon!!) {
                            append(R.string.ic_arrow_right_rtl.asString())
                        }
                    }
                }
            }
        }

        TransitionManager.beginDelayedTransition(binding.root, AutoTransition().apply {
            duration = 100
        })
        binding.clBindPhone.visible()
        binding.tvBindPhone.text = buildSpannedString {
            iconFontAlign {
                size(18.dp) {
                    typeface(FontUtil.fontIcon!!) {
                        append(R.string.ic_tel.asString())
                    }
                }
            }
            appendSpace(2.dp)
            append(R.string.verify_phone_number.asString())
        }
        binding.clBindPhone.click {
            startActivityByRouter(PATH_LOGIN_UPDATE_ACCOUNT_ACTIVITY, {
                withString(RouterParamKey.Common.JUMP_INFO, RouterParamKey.Login.TYPE_BINDING_PHONE)
                withString(RouterParamKey.Common.KEY_SOURCE, RouterParamKey.Login.SOURCE_GUIDE)
                withTransition(R.anim.anim_nav_enter, R.anim.anim_nav_exit)
            })
            UserTracker.onBindPhoneEntranceClick()
        }
        binding.iftvCloseBindPhone.click {
            BindPhonePopupManager.handleClosePopup()
            hideBindPhonePopup()
        }
    }

    private fun hideBindPhonePopup() {
        if (binding.clBindPhone.isVisible()) {
            TransitionManager.beginDelayedTransition(binding.root, AutoTransition().apply {
                duration = 100
            })
            binding.clBindPhone.gone()
        }
    }
}