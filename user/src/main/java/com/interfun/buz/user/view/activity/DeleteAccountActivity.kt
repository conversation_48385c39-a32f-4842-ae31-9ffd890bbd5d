package com.interfun.buz.user.view.activity

import android.os.Bundle
import androidx.activity.OnBackPressedCallback
import com.alibaba.android.arouter.facade.annotation.Route
import com.interfun.buz.common.base.SimpleContainerActivity
import com.interfun.buz.common.constants.PATH_USER_ACTIVITY_DELETE_ACCOUNT
import com.interfun.buz.user.R
import com.interfun.buz.user.view.fragment.BlockListFragment
import com.interfun.buz.user.view.fragment.DeleteAccountFragment
import dagger.hilt.android.AndroidEntryPoint

/**
 * Include：
 * [com.interfun.buz.user.view.fragment.DeleteAccountFragment]
 */
@Route(path = PATH_USER_ACTIVITY_DELETE_ACCOUNT)
@AndroidEntryPoint
class DeleteAccountActivity : SimpleContainerActivity() {
    private val deleteAccountFragmentTag = "DeleteAccountFragment"

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val savedFragment = supportFragmentManager.findFragmentByTag(deleteAccountFragmentTag)
        if (savedFragment == null) {
            supportFragmentManager.beginTransaction()
                .add(containerId, DeleteAccountFragment(), deleteAccountFragmentTag)
                .commit()
        }

        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                finish()
                overridePendingTransition(R.anim.anim_nav_enter_pop, R.anim.anim_nav_exit_pop)
            }
        })
    }

}