package com.interfun.buz.user.view.fragment

import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import com.drakeet.multitype.MultiTypeAdapter
import com.interfun.buz.base.ktx.activityViewModels
import com.interfun.buz.base.ktx.click
import com.interfun.buz.base.ktx.finishActivity
import com.interfun.buz.base.ktx.fragment
import com.interfun.buz.base.ktx.initStatusBarHeight
import com.interfun.buz.base.ktx.isVolumeMuted
import com.interfun.buz.common.base.binding.BaseBindingFragment
import com.interfun.buz.common.ktx.toastSolidWarning
import com.interfun.buz.common.manager.AlertSoundItemBean
import com.interfun.buz.common.manager.AlertSoundManager
import com.interfun.buz.common.manager.OnlineChatRingtoneManager
import com.interfun.buz.common.utils.AlertSoundPlayer
import com.interfun.buz.user.R
import com.interfun.buz.user.databinding.UserFragmentAlertSoundBinding
import com.interfun.buz.user.view.itemdelegate.AlertSoundItemView
import com.interfun.buz.user.viewmodel.AlertSoundViewModel

/**
 * <AUTHOR>
 *
 * @date 2024/9/3
 *
 * @desc 选择通知音
 * 宿主:[com.interfun.buz.user.view.activity.AlertSoundActivity]
 */
class AlertSoundFragment : BaseBindingFragment<UserFragmentAlertSoundBinding>() {
    lateinit var mAdapter: MultiTypeAdapter
    private val alertSoundList by lazy { AlertSoundManager.alertSoundList }
    private val alertSoundPlayer by lazy { AlertSoundPlayer.getInstance() }
    private val alertSoundViewModel by fragment.activityViewModels<AlertSoundViewModel>()

    override fun initView() {
        super.initView()
        binding.spaceStatusBar.initStatusBarHeight()
        binding.iftvLeftBack.click {
            finish()
        }
        binding.tvSave.click {
            //保存通知音
            val type = AlertSoundManager.getCurrentSelectItem().type
            if (AlertSoundManager.getSavedAlertSoundType().type != type) {
                alertSoundViewModel.saveAlertSoundType(type)
                AlertSoundManager.setSavedAlertSoundType(type)
                OnlineChatRingtoneManager.updateRingtone()
            }
            finish()
        }

        mAdapter = MultiTypeAdapter(alertSoundList).apply {
            register(
                AlertSoundItemView(object : AlertSoundItemView.OnCallBackListener {
                    override fun onItemClick(alertSound: AlertSoundItemBean) {
                        if (isVolumeMuted) {
                            toastSolidWarning(R.string.alert_sound_page_error)
                        }
                        AlertSoundManager.setCurrentSelectItem(alertSound.type)
                        alertSoundPlayer.play(alertSound.type)
                        mAdapter.notifyItemRangeChanged(0, alertSoundList.size)
                    }
                })
            )
        }

        binding.rvAlertSoundList.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL,false)
            adapter = mAdapter
        }
    }

    private fun finish() {
        finishActivity()
        activity?.overridePendingTransition(R.anim.anim_nav_enter_pop,R.anim.anim_nav_exit_pop)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        AlertSoundManager.setCurrentSelectItem(AlertSoundManager.getSavedAlertSoundType().type)
        alertSoundPlayer.prepare()
    }

    override fun onDestroy() {
        alertSoundPlayer.release()
        super.onDestroy()
    }
}