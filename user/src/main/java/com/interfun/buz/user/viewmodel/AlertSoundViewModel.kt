package com.interfun.buz.user.viewmodel

import androidx.lifecycle.ViewModel
import com.interfun.buz.base.ktx.launchIO
import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.common.manager.AlertSoundManager
import com.interfun.buz.common.manager.UserInfoReportManager

class AlertSoundViewModel : ViewModel() {

    companion object{
        const val TAG = "AlertSoundViewModel"
    }

    fun saveAlertSoundType (type: Int) {
        launchIO {
            UserInfoReportManager.reportUserInfo(
                settingMap = mapOf("alertSoundType" to type.toString())
            ) {
                logDebug(TAG, "saveAlertSoundType = $type")
            }
        }
    }
}