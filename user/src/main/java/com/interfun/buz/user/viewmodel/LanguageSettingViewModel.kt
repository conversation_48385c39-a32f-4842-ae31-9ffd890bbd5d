package com.interfun.buz.user.viewmodel

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.base.ktx.delayInMainThread
import com.interfun.buz.base.ktx.toast
import com.interfun.buz.biz.center.voicefilter.repository.VoiceFilterDataRepository
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.constants.TrackConstant
import com.interfun.buz.common.ktx.isSuccess
import com.interfun.buz.common.manager.UserInfoReportManager
import com.interfun.buz.common.manager.chat.ChannelPendStatusManager
import com.interfun.buz.common.manager.voicecall.VoiceCallPortal
import com.interfun.buz.common.service.RealTimeCallService
import com.interfun.buz.common.utils.BuzTracker
import com.interfun.buz.common.utils.language.LanguageBean
import com.interfun.buz.common.utils.language.LanguageManager
import com.interfun.buz.common.utils.language.LanguageProvider
import com.interfun.buz.onair.standard.IGlobalOnAirController
import com.interfun.buz.user.R
import com.yibasan.lizhifm.sdk.platformtools.ApplicationContext
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import javax.inject.Inject

/**
 * <AUTHOR>
 * @time 2023/5/25
 * @desc language setting
 **/
@HiltViewModel
class LanguageSettingViewModel @Inject constructor(
    private val savedStateHandle: SavedStateHandle,
    val voiceFilterRepository: VoiceFilterDataRepository
) : ViewModel() {

    val languages = LanguageProvider.getSupportLanguageList()

    val localeLanguageCode = LanguageManager.getLocaleLanguageCode()

    val selectedLanguageCodeFlow = MutableStateFlow(
        savedStateHandle.get<String>("selectedLanguageCode") ?: LanguageManager.getLocaleLanguageCode()
    )

    fun onLanguageSelected(language: LanguageBean) {
        selectedLanguageCodeFlow.value = language.code
        savedStateHandle["selectedLanguageCode"] = language.code
    }

    fun saveLanguageSetting() {
        selectedLanguageCodeFlow.value.let {
            UserInfoReportManager.reportUserInfo(it) { rCode ->
                val reportSuccess = rCode.isSuccess
                onResultBack(it, reportSuccess)
                if (reportSuccess) {
                    LanguageManager.changeLanguage(appContext, it)
                    voiceFilterRepository.clearVoiceFilterData()
                    delayInMainThread(50) {
                        val voiceCallService = routerServices<RealTimeCallService>().value
                        val onAirService = routerServices<IGlobalOnAirController>().value
                        val isOnRealTimeCall = voiceCallService?.isOnRealTimeCall() == true
                        val isInOnAir = onAirService?.isInOnAir() == true
                        val isBeingCall = ChannelPendStatusManager.isBeingCall
                        if (isBeingCall) {
                            val value = ChannelPendStatusManager.statusFlow.value
                            val channelId = value.second?.channelId
                            if (channelId != null) {
                                VoiceCallPortal.reject(channelId)
                            }
                        } else if (isOnRealTimeCall) {
                            VoiceCallPortal.hangUp()
                        }
                        onAirService?.switchLanguage()
                        //切换语言强行退出app了。要延迟一点时间让其访问网络请求挂断电话
                        if (isOnRealTimeCall || isBeingCall || isInOnAir) {
                            delayInMainThread(150) {
                                LanguageManager.restartApp(ApplicationContext.getApplication())
                            }
                        } else {
                            //fragment.requireContext() is null
                            LanguageManager.restartApp(ApplicationContext.getApplication())
                        }
                    }
                } else {
                    R.string.online_chat_network_not_good.asString().toast()
                }
            }
        }
    }

    private fun onResultBack(code: String, isSuccess: Boolean) {
        BuzTracker.onResult {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "RB2023060801")
            put(TrackConstant.KEY_RESULT_TYPE, "language_setting")
            put(TrackConstant.KEY_PAGE_TYPE, "language")
            put(TrackConstant.KEY_CONTENT_NAME, code)
            put(TrackConstant.KEY_IS_SUCCESS, if (isSuccess) "success" else "fail")
        }
    }
}