package com.interfun.buz.user.view.activity

import android.os.Bundle
import androidx.activity.OnBackPressedCallback
import com.alibaba.android.arouter.facade.annotation.Autowired
import com.alibaba.android.arouter.facade.annotation.Route
import com.alibaba.android.arouter.launcher.ARouter
import com.interfun.buz.common.base.SimpleContainerActivity
import com.interfun.buz.common.constants.PATH_AI_MARKET_ACTIVITY
import com.interfun.buz.user.R
import com.interfun.buz.user.view.fragment.AiMarketFragment
import dagger.hilt.android.AndroidEntryPoint

/**
 * Include：
 * [com.interfun.buz.user.view.fragment.AboutFragment]
 */
@Route(path = PATH_AI_MARKET_ACTIVITY)
@AndroidEntryPoint
class AiMarketActivity : SimpleContainerActivity() {

    private val fragmentTag = "AiMarketFragment"

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        ARouter.getInstance().inject(this)

        val savedFragment = supportFragmentManager.findFragmentByTag(fragmentTag)
        if (savedFragment == null) {
            supportFragmentManager.beginTransaction()
                .add(
                    containerId,
                    AiMarketFragment.newInstance(this.intent.extras ?: Bundle()),
                    fragmentTag
                )
                .commit()
        }


        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                finish()
                overridePendingTransition(R.anim.anim_nav_enter_pop, R.anim.anim_nav_exit_pop)
            }
        })
    }


}