package com.interfun.buz.user.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.base.ktx.withIOContext
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.interfaces.IStorageHelper
import com.interfun.buz.common.ktx.getLongDefault
import com.interfun.buz.common.ktx.uid
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.service.StorageService
import com.interfun.buz.common.utils.StorageUtil
import com.interfun.buz.common.utils.StorageUtil.toAccurateFileSize
import com.interfun.buz.user.log.UserTracker
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

class BuzStorageInfo(
    var deviceTotalStorageSize: Long = 0L,
    var availableSize: Long = 0L,
    var buzUsedSize: Long = 0L,
    var buzCacheSize: Long = 0L
)

class StorageSettingViewModel : ViewModel() {

    companion object {
        const val TAG = "StorageSettingViewModel"
    }

    private val buzStorageHelper: IStorageHelper? by lazy {
        routerServices<StorageService>().value?.getStorageHelper(UserSessionManager.uid)
    }
    private var buzCacheSize: Long = 0L
    private val _buzStorageInfoStateFlow = MutableStateFlow<BuzStorageInfo?>(null)
    private val _cacheRemovedSuccess = MutableSharedFlow<Any>()

    val buzStorageInfoStateFlow: StateFlow<BuzStorageInfo?>
        get() = _buzStorageInfoStateFlow.asStateFlow()
    val cacheRemovedSuccess: SharedFlow<Any>
        get() = _cacheRemovedSuccess.asSharedFlow()

    /**
     * 获取页面数据
     */
    fun getStorageData() {
        viewModelScope.launch {
            buzCacheSize = buzStorageHelper?.getUserCacheSize().getLongDefault()

            val buzStorageInfo = withIOContext {
                BuzStorageInfo(
                    deviceTotalStorageSize = StorageUtil.getDeviceTotalStorageSize(),
                    availableSize = StorageUtil.getDeviceAvailableSize(), // Available size
                    buzUsedSize = StorageUtil.getBuzUsedSize(), // Buz-used size
                    buzCacheSize = buzCacheSize
                )
            }

            logDebug(
                TAG,
                "deviceTotalStorageSize: ${buzStorageInfo.deviceTotalStorageSize.toAccurateFileSize()}" +
                        "\ndeviceAvailableSize: ${buzStorageInfo.availableSize.toAccurateFileSize()}" +
                        "\nbuzUsedSize: ${buzStorageInfo.buzUsedSize.toAccurateFileSize()}" +
                        "\nbuzCacheSize: ${buzCacheSize.toAccurateFileSize()}"
            )

            UserTracker.onStoragePageExposure(buzStorageInfo)
            _buzStorageInfoStateFlow.emit(buzStorageInfo)
        }
    }

    /**
     * 删除该用户的所有缓存
     */
    fun deleteUserCache() {
        viewModelScope.launch {
            buzStorageHelper?.deleteUserCache()
            getStorageDataAfterClear()
            _cacheRemovedSuccess.emit(Any())
        }
    }

    /**
     * 更新清理后的页面数据
     */
    private fun getStorageDataAfterClear() {
        viewModelScope.launch {
            val buzStorageInfo = withIOContext {
                BuzStorageInfo(
                    deviceTotalStorageSize = StorageUtil.getDeviceTotalStorageSize(),
                    availableSize = StorageUtil.getDeviceAvailableSize() + buzCacheSize,
                    buzUsedSize = StorageUtil.getBuzUsedSize() - buzCacheSize,
                    buzCacheSize = 0L
                )
            }
            _buzStorageInfoStateFlow.emit(buzStorageInfo)
        }

    }
}