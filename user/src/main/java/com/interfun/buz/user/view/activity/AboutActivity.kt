package com.interfun.buz.user.view.activity

import android.os.SystemClock
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.alibaba.android.arouter.facade.annotation.Route
import com.interfun.buz.base.ktx.appBuildType
import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.base.ktx.toast
import com.interfun.buz.common.constants.PATH_USER_ACTIVITY_ABOUT
import com.interfun.buz.common.constants.channelAppKey
import com.interfun.buz.common.constants.channelId
import com.interfun.buz.common.manager.AppConfigRequestManager
import com.interfun.buz.common.manager.router.converter.WebViewRouterArgs
import com.interfun.buz.common.web.WebViewActivity
import com.interfun.buz.compose.base.BaseComposeActivity
import com.interfun.buz.compose.components.CommonTitleBar
import com.interfun.buz.compose.components.IconFontText
import com.interfun.buz.compose.ktx.asColor
import com.interfun.buz.compose.ktx.rotateWhenArLanguage
import com.interfun.buz.compose.modifier.debouncedClickable
import com.interfun.buz.compose.styles.TextStyles
import com.interfun.buz.user.R
import com.interfun.buz.user.R.drawable
import com.lizhi.component.basetool.env.AppEnvironment
import com.lizhi.component.basetool.env.Environments
import com.yibasan.lizhifm.sdk.platformtools.Const
import dagger.hilt.android.AndroidEntryPoint

/**
 * <AUTHOR>
 * @date 2024/5/24
 * @desc
 */
@Route(path = PATH_USER_ACTIVITY_ABOUT)
@AndroidEntryPoint
class AboutActivity : BaseComposeActivity() {

    @Composable
    override fun ComposeContent() {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .statusBarsPadding()
        ) {
            CommonTitleBar(
                title = stringResource(id = R.string.user_about_buz),
                onBackClick = { finish() }
            )
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState()),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Content()
            }
        }
    }

    @Composable
    private fun Content() {
        Spacer(Modifier.height(40.dp))
        Image(
            painter = painterResource(id = drawable.about_logo),
            contentDescription = null,
            modifier = Modifier.size(120.dp)
        )
        Spacer(Modifier.height(12.dp))
        Text(
            text = stringResource(id = R.string.app_name).lowercase(),
            style = TextStyles.titleMedium(),
            color = R.color.basic_primary.asColor()
        )
        Spacer(Modifier.height(6.dp))
        val version = "V ${Const.VersionName} (${Const.clientVersion})"
        Text(
            modifier = Modifier.clickable(
                onClick = { onVersionClick() },
                indication = null,
                interactionSource = remember { MutableInteractionSource() }
            ),
            text = when (Environments.getEnv(appContext)) {
                AppEnvironment.TOWER -> "$version - Tower"
                AppEnvironment.PRE -> "$version - Pre"
                else -> version
            },
            style = TextStyles.bodyMedium(),
            color = R.color.text_white_secondary.asColor()
        )
        Spacer(Modifier.height(80.dp))
        ContentItem(R.string.user_agreement) {
            openWeb(AppConfigRequestManager.userAgreementUrl)
        }
        ContentItem(R.string.privacy_policy) {
            openWeb(AppConfigRequestManager.privacyPolicyUrl)
        }
        ContentItem(R.string.community_guidelines) {
            openWeb(AppConfigRequestManager.communityGuidelinesUrl)
        }
        ContentItem(R.string.ve_attribution) {
            openWeb(AppConfigRequestManager.attributionUrl, showNativeTitleBar = false)
        }
    }

    @Composable
    private fun ContentItem(textRes: Int, onClick: () -> Unit) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(64.dp)
                .padding(horizontal = 20.dp)
                .debouncedClickable(onClick = onClick),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                modifier = Modifier.weight(1f),
                text = stringResource(id = textRes),
                style = TextStyles.bodyLarge(),
                color = R.color.text_white_main.asColor()
            )
            IconFontText(
                modifier = Modifier.rotateWhenArLanguage(),
                iconRes = R.string.ic_arrow_right,
                iconSize = 16.dp,
                iconColor = R.color.text_white_secondary.asColor(),
                size = 16.dp,
            )
        }
    }

    private val CLICK_NUM = 5
    private val CLICK_INTERVER_TIME = 2000
    private var lastClickTime: Long = 0
    private var clickNum = 0
    private fun onVersionClick() {
        val currentClickTime: Long = SystemClock.uptimeMillis()
        if (currentClickTime - lastClickTime <= CLICK_INTERVER_TIME
            || lastClickTime == 0L
        ) {
            lastClickTime = currentClickTime
            clickNum += 1
        } else {
            clickNum = 1
            lastClickTime = 0
            return
        }
        if (clickNum == CLICK_NUM) {
            clickNum = 0
            lastClickTime = 0
            toast(
                "build type is $appBuildType, channelInfo is $channelId"
            )
        }
    }

    private fun openWeb(url: String, showNativeTitleBar: Boolean? = null) {
        if (showNativeTitleBar != null) {
            WebViewActivity.start(
                this,
                WebViewRouterArgs(url, showNativeTitleBar = showNativeTitleBar)
            )
        } else {
            WebViewActivity.start(this, url)
        }
        overridePendingTransition(R.anim.anim_nav_enter, R.anim.anim_nav_exit)
    }

    override fun finish() {
        super.finish()
        overridePendingTransition(R.anim.anim_nav_enter_pop, R.anim.anim_nav_exit_pop)
    }
}