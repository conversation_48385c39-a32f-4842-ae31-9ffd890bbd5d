package com.interfun.buz.user.service

import android.content.Context
import com.alibaba.android.arouter.facade.annotation.Route
import com.interfun.buz.common.constants.PATH_USER_SERVICE
import com.interfun.buz.common.service.UserModuleService
import com.interfun.buz.user.storage.UserSettingMMKV
import com.interfun.buz.user.viewmodel.UserProfileUpdateViewModel

@Route(path = PATH_USER_SERVICE)
class UserModuleServiceImpl : UserModuleService {
    override fun hasClickedOverlaySettingEntrance(): Bo<PERSON>an {
        return UserSettingMMKV.hasClickedOverlaySettingEntrance
    }

    override fun getUpdateBuzIdRCodeTips(code: Int): String? {
        return UserProfileUpdateViewModel.getErrorTip(code)
    }

    override fun init(context: Context?) {
    }
}