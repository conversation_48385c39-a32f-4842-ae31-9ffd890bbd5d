package com.interfun.buz.user.view.activity

import android.os.Bundle
import com.alibaba.android.arouter.facade.annotation.Route
import com.interfun.buz.common.base.SimpleContainerActivity
import com.interfun.buz.common.constants.JUMP_INFO
import com.interfun.buz.common.constants.PATH_USER_ACTIVITY_EDIT_INFO
import com.interfun.buz.common.constants.RouterParamKey
import com.interfun.buz.common.constants.RouterParamKey.User.TYPE_UPDATE_BUZ_ID
import com.interfun.buz.common.constants.RouterParamKey.User.TYPE_UPDATE_USER_NAME
import com.interfun.buz.user.R
import com.interfun.buz.user.view.fragment.UpdateBuzIdFragment
import com.interfun.buz.user.view.fragment.UpdateNameFragment
import dagger.hilt.android.AndroidEntryPoint

@Route(path = PATH_USER_ACTIVITY_EDIT_INFO)
@AndroidEntryPoint
class EditUserInfoActivity : SimpleContainerActivity() {

    private val editFragmentTag = "editFragmentTag"

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val savedFragment = supportFragmentManager.findFragmentByTag(editFragmentTag)
        if (savedFragment == null) {
            val addFragment = when (intent.getStringExtra(RouterParamKey.Common.JUMP_INFO)) {
                TYPE_UPDATE_USER_NAME -> UpdateNameFragment()
                TYPE_UPDATE_BUZ_ID -> UpdateBuzIdFragment().apply {
                    arguments = intent.extras
                }
                else -> UpdateNameFragment()
            }
            supportFragmentManager.beginTransaction()
                .add(containerId, addFragment, editFragmentTag)
                .commit()
        }
    }

    override fun finish() {
        super.finish()
        overridePendingTransition(R.anim.anim_nav_enter_pop, R.anim.anim_nav_exit_pop)
    }
}