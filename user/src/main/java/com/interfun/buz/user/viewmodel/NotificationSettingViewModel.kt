package com.interfun.buz.user.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.interfun.buz.user.repository.UserSettingRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.stateIn
import javax.inject.Inject

/**
 * Author: ChenYouSheng
 * Date: 2025/7/29
 * Email: chenyoush<PERSON>@vocalbeats.com
 * Desc:
 */
@HiltViewModel
class NotificationSettingViewModel @Inject constructor(
    private val userSettingRepository: UserSettingRepository
) : ViewModel() {

    fun enableSyncDND(enable: Boolean) {
        userSettingRepository.enableSyncDND(enable)
    }

    val enableSyncDNDFlow = userSettingRepository.getEnableSyncDND()
        .stateIn(
            viewModelScope,
            SharingStarted.WhileSubscribed(5000L),
            false
        )

}