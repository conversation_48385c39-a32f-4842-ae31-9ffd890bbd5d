package com.interfun.buz.user.view.activity

import android.os.Bundle
import com.alibaba.android.arouter.facade.annotation.Route
import com.interfun.buz.common.base.SimpleContainerActivity
import com.interfun.buz.common.constants.PATH_USER_ACTIVITY_ALERT_SOUND_SETTING
import com.interfun.buz.user.R
import com.interfun.buz.user.view.fragment.AlertSoundFragment
import dagger.hilt.android.AndroidEntryPoint

/**
 * <AUTHOR>
 * @date 2024/9/3
 * @desc Include：
 * [com.interfun.buz.user.view.fragment.AlertSoundFragment]
 */
@Route(path = PATH_USER_ACTIVITY_ALERT_SOUND_SETTING)
@AndroidEntryPoint
class AlertSoundActivity : SimpleContainerActivity() {

    private val fragmentTag = "alertSoundFragmentTag"

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val savedFragment = supportFragmentManager.findFragmentByTag(fragmentTag)
        if (savedFragment == null) {
            supportFragmentManager.beginTransaction()
                .add(containerId, AlertSoundFragment(), fragmentTag)
                .commit()
        }
    }

    override fun finish() {
        super.finish()
        overridePendingTransition(R.anim.anim_nav_enter_pop, R.anim.anim_nav_exit_pop)
    }
}