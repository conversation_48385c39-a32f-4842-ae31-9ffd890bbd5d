package com.interfun.buz.user.view.activity

import android.os.Bundle
import com.alibaba.android.arouter.facade.annotation.Route
import com.interfun.buz.common.base.SimpleContainerActivity
import com.interfun.buz.common.constants.PATH_USER_ACTIVITY_NOTIFICATION_SETTING
import com.interfun.buz.user.R
import com.interfun.buz.user.view.fragment.NotificationSettingFragment
import dagger.hilt.android.AndroidEntryPoint


@Route(path = PATH_USER_ACTIVITY_NOTIFICATION_SETTING)
@AndroidEntryPoint
class NotificationSettingActivity : SimpleContainerActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val tag = NotificationSettingFragment.TAG
        val savedFragment = supportFragmentManager.findFragmentByTag(tag)
        if (savedFragment == null) {
            supportFragmentManager.beginTransaction()
                .add(containerId, NotificationSettingFragment(), tag)
                .commit()
        }
    }

    override fun finish() {
        super.finish()
        overridePendingTransition(R.anim.anim_nav_enter_pop, R.anim.anim_nav_exit_pop)
    }
}