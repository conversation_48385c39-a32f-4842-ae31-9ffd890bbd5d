package com.interfun.buz.user.view.fragment

import android.Manifest
import android.annotation.SuppressLint
import android.net.Uri
import android.os.Build
import android.os.Bundle
import androidx.activity.result.PickVisualMediaRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.result.contract.ActivityResultContracts.PickVisualMedia.ImageOnly
import androidx.core.content.FileProvider
import androidx.lifecycle.viewModelScope
import coil.load
import coil.transform.CircleCropTransformation
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.arouter.startActivityByRouter
import com.interfun.buz.common.base.binding.BaseBindingFragment
import com.interfun.buz.common.constants.*
import com.interfun.buz.common.crop.CommonCropImageContract
import com.interfun.buz.common.crop.CropParams
import com.interfun.buz.common.ktx.buzId
import com.interfun.buz.common.ktx.portrait
import com.interfun.buz.common.ktx.userName
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.common.manager.upload.CommonUploadManager
import com.interfun.buz.common.utils.showAskCameraPermissionDialog
import com.interfun.buz.common.widget.dialog.bottomlist.CommonBottomListDialog
import com.interfun.buz.user.R
import com.interfun.buz.user.databinding.UserFragmentUpdateProfileBinding
import com.interfun.buz.user.viewmodel.UserProfileUpdateViewModel
import com.lizhi.itnet.upload.common.UploadTask
import com.yalantis.ucrop.UCrop
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import java.io.File


class UpdateProfileFragment : BaseBindingFragment<UserFragmentUpdateProfileBinding>() {

    companion object{
        const val TAG = "UpdateProfileFragment"
        const val KEY_PHOTO_URI = "key_photo_uri"
        private const val TAG_DIALOG_SELECT_IMAGE = "TAG_DIALOG_SELECT_IMAGE"
    }

    private val updateViewModel by fragmentViewModels<UserProfileUpdateViewModel>()
    private var uploadImageJob: Job? = null
    private var newPortraitFilePath: String? = null
    private val permissionLauncher = fragment.requestPermissionLauncher{
        if (it.not()){
            if (activity.isNull()) return@requestPermissionLauncher
            showAskCameraPermissionDialog(activity!!)
        }else{
            takePhoto()
        }
    }


    //上一次上传的凭证，用户更换头像时取消上次上传
    private var lastUpload: UploadTask? = null

    private val pickPictureLauncher = pickContentLauncher { startCrop(it) }
    private val photoPicker = registerForActivityResult(ActivityResultContracts.PickVisualMedia()) {
        startCrop(it)
    }
    private var photoUri:Uri? = null

    private val contract = TakePhotoContract {
        photoUri = it
    }

    private val takePhotoLauncher = takePictureLauncher(contract) { result ->
        if (result && photoUri != null) {
            startCrop(photoUri!!)
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        outState.putParcelable(KEY_PHOTO_URI, photoUri)
    }

    override fun onViewStateRestored(savedInstanceState: Bundle?) {
        super.onViewStateRestored(savedInstanceState)
        photoUri = savedInstanceState?.getParcelable(KEY_PHOTO_URI)
        if (savedInstanceState != null) {
            CommonBottomListDialog.rebindClickWhenRestored(activity, TAG_DIALOG_SELECT_IMAGE) {
                when (it) {
                    0 -> checkPermissionAndTakePhoto()
                    1 -> selectFromGallery()
                }
            }
        }
    }

    private val cropImageLauncher = registerForActivityResult(CommonCropImageContract()) { result ->
        if (result != null) {
            val uriFilePath = result.path // optional usage
            binding.ivProfile.load(uriFilePath) {
                transformations(CircleCropTransformation())
                listener(
                    onStart = {
                        logInfo(TAG, "cropImageLauncher onStart==>${result}")
                    },
                    onSuccess = { _, _ ->
                        logInfo(TAG, "cropImageLauncher onSuccess==>${result}")
                    },
                    onError = { _, _ ->
                        logInfo(TAG, "cropImageLauncher onError==>${result}")
                    }
                )
            }
            // Most of the time，ivProfile.load(result) will block，it won't be called onSuccess listener
            newPortraitFilePath = uriFilePath
            uploadImage(uriFilePath)
        }
    }

    @SuppressLint("SetTextI18n")
    override fun initData() {
        super.initData()
        if (UserSessionManager.hasSession()) {
            binding.ivProfile.setPortrait(UserSessionManager.portrait, size = 140.dp)
            binding.tvName.text = UserSessionManager.userName
            binding.tvBuzId.text = UserSessionManager.buzId
        }
        updateViewModel.updatePortraitResult.observe(viewLifecycleOwner) {
            binding.ivProfile.load(it) {
                transformations(CircleCropTransformation())
            }
        }
    }

    override fun initView() {
        super.initView()

        binding.spaceStatusBar.initStatusBarHeight()

        binding.iftvLeftBack.click {
            finishActivity()
            activity?.overridePendingTransition(R.anim.anim_nav_enter_pop,R.anim.anim_nav_exit_pop)
        }

        binding.ivProfile.click {
            showSelectDialog()
        }

        binding.tvName.click {
            startActivityByRouter(
                PATH_USER_ACTIVITY_EDIT_INFO,
                RouterParamKey.Common.JUMP_INFO to RouterParamKey.User.TYPE_UPDATE_USER_NAME,
                RouterParamKey.Common.KEY_SOURCE to RouterParamKey.User.UPDATE_BUZ_ID_IN_PROFILE,
                buildPostcardBlock = { withTransition(R.anim.anim_nav_enter, R.anim.anim_nav_exit) }
            )
        }

        binding.tvBuzId.click {
            startActivityByRouter(
                PATH_USER_ACTIVITY_EDIT_INFO,
                RouterParamKey.Common.JUMP_INFO to RouterParamKey.User.TYPE_UPDATE_BUZ_ID,
                RouterParamKey.Common.KEY_SOURCE to RouterParamKey.User.UPDATE_BUZ_ID_IN_PROFILE,
                buildPostcardBlock = { withTransition(R.anim.anim_nav_enter, R.anim.anim_nav_exit) }
            )
        }

    }

    private fun uploadImage(filePath: String?) {
        logInfo(TAG, "uploadImage==>${filePath}")
        if (filePath == null) {
            return
        }
        val file = File(filePath)
        if (!file.exists()) {
            return
        }
        uploadImageJob?.cancel()
        uploadImageJob = updateViewModel.viewModelScope.launch {
                lastUpload?.let { CommonUploadManager.cancel(it.taskId) }
                lastUpload = updateViewModel.uploadProfile(file)
        }
    }

    override fun onResume() {
        super.onResume()
        binding.tvName.text = UserSessionManager.userName
        binding.tvBuzId.text = UserSessionManager.buzId
        if (UserSessionManager.portrait.isNullOrEmpty().not()) {
            binding.ivProfile.setPortrait(UserSessionManager.portrait, size = 140.dp)
        }
    }


    private fun showSelectDialog() {
        CommonBottomListDialog.build {
            addTag(TAG_DIALOG_SELECT_IMAGE)
            addDefaultOption(R.string.ic_camera, R.string.take_photo) {
                checkPermissionAndTakePhoto()
            }
            addDefaultOption(R.string.ic_album_solid, R.string.album) {
                selectFromGallery()
            }
        }.showDialog(activity)
    }

    private fun checkPermissionAndTakePhoto(){
        permissionLauncher.launch(Manifest.permission.CAMERA)
    }

    private fun takePhoto() {
        takePhotoLauncher.launch(getPortraitUri())
    }

    private fun selectFromGallery() {
        if (ActivityResultContracts.PickVisualMedia.isPhotoPickerAvailable()) {
            photoPicker.launch(PickVisualMediaRequest(ImageOnly))
        } else {
            pickPictureLauncher.launchForImage()
        }
    }

    private fun startCrop(uri: Uri?) {
        if (uri == null) return
        // start picker to get image for cropping and then use the image in cropping activity
        cropImageLauncher.launch(
            CropParams(
                inputUri = uri,
                options = UCrop.Options().apply { setCompressionQuality(85) })
        )
    }

    private fun getPortraitUri(): Uri {
        val file = File(FILE_PATH_CACHED_PIC_DIR, "signup_profile.jpg")
        file.isExistOrCreateNewFile()
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            FileProvider.getUriForFile(requireContext, FILE_PROVIDER_AUTHORITY, file)
        } else {
            Uri.fromFile(file)
        }
    }
}