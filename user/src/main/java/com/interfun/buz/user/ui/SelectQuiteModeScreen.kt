package com.interfun.buz.user.ui

import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.interfun.buz.compose.R
import com.interfun.buz.compose.components.*
import com.interfun.buz.compose.ktx.VerticalSpace
import com.interfun.buz.compose.ktx.asColor
import com.interfun.buz.compose.ktx.textSp
import com.interfun.buz.compose.modifier.debouncedClickable
import com.interfun.buz.compose.styles.TextStyles

/**
 * Select Mode Screen UI State
 */
data class SelectQuiteModeUiState(
    val selectedMode: ModeType = ModeType.AUTOPLAY,
    val isQuietTimeEnabled: Boolean = false,
    val quietTimeFrom: String = "9:00",
    val quietTimeTo: String = "17:00",
    val quietTimeRepetition: String = "Weekdays",
    val uiEvent: SelectQuiteModeUiEvent = SelectQuiteModeUiEvent()
)

/**
 * Select Mode Screen UI Events
 */
data class SelectQuiteModeUiEvent(
    val onBackClick: () -> Unit = {},
    val onModeSelected: (ModeType) -> Unit = {},
    val onQuietTimeEnabledChanged: (Boolean) -> Unit = {},
    val onQuietTimeFromChanged: (String) -> Unit = {},
    val onQuietTimeToChanged: (String) -> Unit = {},
    val onQuietTimeRepetitionChanged: (String) -> Unit = {}
)

/**
 * Select Mode Screen
 * 选择模式界面，包含自动播放模式和安静模式的选择
 */
@Composable
fun SelectQuiteModeScreen(
    uiState: SelectQuiteModeUiState,
    modifier: Modifier = Modifier
) {
    val scrollState = rememberScrollState()
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(R.color.color_background_1_default.asColor())
            .statusBarsPadding()
            .navigationBarsPadding()
    ) {

        Column(modifier = Modifier.fillMaxWidth()) {
            // Close Button
            IconFontText(
                modifier = Modifier
                    .padding(20.dp)
                    .size(24.dp)
                    .debouncedClickable { uiState.uiEvent.onBackClick() },
                iconRes = R.string.ic_exit,
                iconSize = 24.dp,
                iconColor = R.color.text_white_main.asColor()
            )

            // Title
            Text(
                text = "Select Mode",
                style = TextStyles.titleLarge(),
                color = R.color.text_white_main.asColor(),
                modifier = Modifier
                    .padding(bottom = 32.dp)
                    .align(Alignment.CenterHorizontally)
            )
        }

        // Main Content with scroll
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(scrollState)
                .padding(top = 40.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {

            // Mode Selection
            Column(
                modifier = Modifier.padding(horizontal = 40.dp)
            ) {
                // Autoplay Mode
                ModeSelectionItem(
                    icon = stringResource(R.string.ic_sound_open),
                    title = "Autoplay Mode",
                    subtitle = "Voice messages autoplays when received.",
                    isSelected = uiState.selectedMode == ModeType.AUTOPLAY,
                    selectedBackground = colorResource(R.color.color_foreground_highlight_default),
                    onClick = { uiState.uiEvent.onModeSelected(ModeType.AUTOPLAY) }
                )

                VerticalSpace(16.dp)

                // Quiet Mode
                ModeSelectionItem(
                    icon = stringResource(R.string.ic_mute),
                    title = "Quiet Mode",
                    subtitle = "Synced with \"Focus\"",
                    isSelected = uiState.selectedMode == ModeType.QUIET,
                    selectedBackground = colorResource(R.color.color_foreground_dnd_default),
                    onClick = { uiState.uiEvent.onModeSelected(ModeType.QUIET) }
                )
            }
            VerticalSpace(40.dp)
            // Set Quiet Time Section
            Column(
                modifier = Modifier.padding(horizontal = 40.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column {
                        Text(
                            text = "Set Quiet Time",
                            style = TextStyles.labelLarge(),
                            color = R.color.text_white_main.asColor()
                        )
                        Text(
                            text = "Automatically disable voice message autoplay in chosen time slots.",
                            style = TextStyles.bodySmall(),
                            color = R.color.text_white_secondary.asColor()
                        )
                    }

                    CommonSwitch(
                        checked = uiState.isQuietTimeEnabled,
                        onCheckedChange = uiState.uiEvent.onQuietTimeEnabledChanged
                    )
                }
                // Time Settings
                AnimatedVisibility(uiState.isQuietTimeEnabled) {
                    VerticalSpace(24.dp)
                    QuietTimeSettings(
                        fromTime = uiState.quietTimeFrom,
                        toTime = uiState.quietTimeTo,
                        repetition = uiState.quietTimeRepetition,
                        onFromTimeChanged = uiState.uiEvent.onQuietTimeFromChanged,
                        onToTimeChanged = uiState.uiEvent.onQuietTimeToChanged,
                        onRepetitionChanged = uiState.uiEvent.onQuietTimeRepetitionChanged
                    )
                }
            }

            // 底部留白，确保滚动时有足够空间
            VerticalSpace(40.dp)
        }
    }
}

/**
 * Mode selection item component
 */
@Composable
private fun ModeSelectionItem(
    icon: String,
    title: String,
    subtitle: String,
    isSelected: Boolean,
    selectedBackground: Color,
    modifier: Modifier = Modifier,
    selectedTextColor: Color = colorResource(R.color.color_text_black_primary),
    onClick: () -> Unit,
) {
    val textColor = if (isSelected) {
        selectedTextColor
    } else {
        R.color.text_white_important.asColor()
    }
    Row(
        modifier = modifier
            .fillMaxWidth()
            .height(80.dp)
            .background(
                color = if (isSelected) {
                    selectedBackground
                } else {
                    R.color.color_background_4_default.asColor()
                },
                shape = CircleShape
            )
            .debouncedClickable { onClick() }
            .padding(horizontal = 16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Icon
        IconFontText(
            modifier = Modifier.padding(6.dp),
            iconText = icon,
            iconSize = 20.dp,
            iconColor = textColor
        )

        Column(
            modifier = Modifier
                .weight(1f)
                .padding(start = 10.dp)
        ) {
            Text(
                text = title,
                style = TextStyles.labelMedium(),
                color = textColor
            )
            Text(
                text = subtitle,
                style = TextStyles.bodySmall(),
                color = textColor
            )
        }
    }
}

/**
 * Quiet time settings component
 */
@Composable
private fun QuietTimeSettings(
    fromTime: String,
    toTime: String,
    repetition: String,
    onFromTimeChanged: (String) -> Unit,
    onToTimeChanged: (String) -> Unit,
    onRepetitionChanged: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        // From time setting
        QuietTimeSettingItem(
            label = "From",
            value = fromTime,
            onClick = { onFromTimeChanged(fromTime) }
        )

        VerticalSpace(16.dp)

        // To time setting
        QuietTimeSettingItem(
            label = "To",
            value = toTime,
            onClick = { onToTimeChanged(toTime) }
        )

        VerticalSpace(16.dp)

        // Repetition setting
        QuietTimeSettingItem(
            label = "Repetition",
            value = repetition,
            onClick = { onRepetitionChanged(repetition) }
        )
    }
}

/**
 * Individual quiet time setting item
 */
@Composable
private fun QuietTimeSettingItem(
    label: String,
    value: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .background(
                color = R.color.color_background_2_default.asColor(),
                shape = RoundedCornerShape(12.dp)
            )
            .debouncedClickable { onClick() }
            .padding(horizontal = 16.dp, vertical = 16.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = label,
            style = TextStyles.labelMedium(),
            color = R.color.text_white_secondary.asColor()
        )

        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = value,
                style = TextStyles.labelMedium(),
                color = R.color.text_white_main.asColor()
            )

            IconFontText(
                modifier = Modifier.padding(start = 8.dp),
                iconText = stringResource(R.string.ic_arrow_right),
                iconSize = 16.dp,
                iconColor = R.color.text_white_secondary.asColor()
            )
        }
    }
}

/**
 * Mode types enum
 */
enum class ModeType {
    AUTOPLAY,
    QUIET
}

/**
 * Preview function
 */
@Composable
@Preview(showBackground = true)
private fun SelectQuiteModeScreenPreview() {
    SelectQuiteModeScreen(
        uiState = SelectQuiteModeUiState(
            selectedMode = ModeType.QUIET,
            isQuietTimeEnabled = true,
            quietTimeFrom = "9:00",
            quietTimeTo = "17:00",
            quietTimeRepetition = "Weekdays"
        )
    )
}

@Composable
@Preview(showBackground = true)
private fun SelectQuiteModeScreenAutoplayPreview() {
    SelectQuiteModeScreen(
        uiState = SelectQuiteModeUiState(
            selectedMode = ModeType.AUTOPLAY,
            isQuietTimeEnabled = false
        )
    )
}
