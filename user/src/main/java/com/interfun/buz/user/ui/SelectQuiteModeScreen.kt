package com.interfun.buz.user.ui

import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.interfun.buz.base.ktx.logMine
import com.interfun.buz.compose.R
import com.interfun.buz.compose.components.*
import com.interfun.buz.compose.ktx.VerticalSpace
import com.interfun.buz.compose.ktx.asColor
import com.interfun.buz.compose.ktx.textSp
import com.interfun.buz.compose.modifier.debouncedClickable
import com.interfun.buz.compose.styles.TextStyles

/**
 * Select Mode Screen UI State
 */
data class SelectQuiteModeUiState(
    val selectedMode: ModeType = ModeType.AUTOPLAY,
    val isQuietTimeEnabled: Boolean = false,
    val quietTimeFrom: String = "",
    val quietTimeTo: String = "",
    val quietTimeRepetition: String = "",
    val uiEvent: SelectQuiteModeUiEvent = SelectQuiteModeUiEvent()
)

/**
 * Select Mode Screen UI Events
 */
data class SelectQuiteModeUiEvent(
    val onBackClick: () -> Unit = {},
    val onModeSelected: (ModeType) -> Unit = {},
    val onQuietTimeEnabledChanged: (Boolean) -> Unit = {},
    val onQuietTimeFromChanged: (String) -> Unit = {},
    val onQuietTimeToChanged: (String) -> Unit = {},
    val onQuietTimeRepetitionChanged: (String) -> Unit = {}
)

@Composable
fun SelectQuiteModeScreen(
    viewmodel: SelectQuiteModeViewModel = hiltViewModel(),
    onDismiss: () -> Unit
) {
    val uiState by viewmodel.uiState.collectAsStateWithLifecycle()
    // 对话框状态
    var showTimeFromDialog by remember { mutableStateOf(false) }
    var showTimeToDialog by remember { mutableStateOf(false) }
    var showRepetitionDialog by remember { mutableStateOf(false) }

    // 创建带有对话框处理的UI事件
    val uiStateWithDialogs = uiState.copy(
        uiEvent = uiState.uiEvent.copy(
            onBackClick = {
                onDismiss()
            },
            onQuietTimeFromChanged = {
                showTimeFromDialog = true
            },
            onQuietTimeToChanged = {
                showTimeToDialog = true
            },
            onQuietTimeRepetitionChanged = {
                showRepetitionDialog = true
            }
        )
    )

    SelectQuiteModeScreen(uiState = uiStateWithDialogs)

    // 时间选择对话框
    if (showTimeFromDialog) {
        val (hour, minute) = parseTimeString(uiState.quietTimeFrom)
        TimePickerDialog(
            title = "Select Start Time",
            initialHour = hour,
            initialMinute = minute,
            onTimeSelected = { selectedHour, selectedMinute ->
                val timeString = "${selectedHour.toString().padStart(2, '0')}:${
                    selectedMinute.toString().padStart(2, '0')
                }"
                uiState.uiEvent.onQuietTimeFromChanged(timeString)
            },
            onDismiss = { showTimeFromDialog = false }
        )
    }

    if (showTimeToDialog) {
        val (hour, minute) = parseTimeString(uiState.quietTimeTo)
        TimePickerDialog(
            title = "Select End Time",
            initialHour = hour,
            initialMinute = minute,
            onTimeSelected = { selectedHour, selectedMinute ->
                val timeString = "${selectedHour.toString().padStart(2, '0')}:${
                    selectedMinute.toString().padStart(2, '0')
                }"
                uiState.uiEvent.onQuietTimeToChanged(timeString)
            },
            onDismiss = { showTimeToDialog = false }
        )
    }

    if (showRepetitionDialog) {
        RepetitionPickerDialog(
            title = "Select Repetition",
            selectedRepetition = uiState.quietTimeRepetition,
            onRepetitionSelected = { repetition ->
                uiState.uiEvent.onQuietTimeRepetitionChanged(repetition)
            },
            onDismiss = { showRepetitionDialog = false }
        )
    }
}

/**
 * Select Mode Screen
 * 选择模式界面，包含自动播放模式和安静模式的选择
 */
@Composable
fun SelectQuiteModeScreen(
    uiState: SelectQuiteModeUiState,
    modifier: Modifier = Modifier
) {
    val scrollState = rememberScrollState()
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(R.color.color_background_1_default.asColor())
            .statusBarsPadding()
            .navigationBarsPadding()
    ) {

        Column(modifier = Modifier.fillMaxWidth()) {
            // Close Button
            Box(
                Modifier
                    .padding(20.dp)
                    .clickable { uiState.uiEvent.onBackClick() }) {
                IconFontText(
                    iconRes = R.string.ic_exit,
                    iconSize = 24.dp,
                    iconColor = R.color.text_white_main.asColor(),
                )
            }

            // Title
            Text(
                text = "Select Mode",
                style = TextStyles.titleLarge(),
                color = R.color.text_white_main.asColor(),
                modifier = Modifier
                    .padding(bottom = 32.dp)
                    .align(Alignment.CenterHorizontally)
            )
        }

        // Main Content with scroll
        Column(
            modifier = Modifier
                .fillMaxSize()
                .weight(1f)
                .verticalScroll(scrollState)
                .padding(top = 40.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            // Mode Selection
            Column(
                modifier = Modifier.padding(horizontal = 40.dp)
            ) {
                val isAutoPlaySelected = uiState.selectedMode == ModeType.AUTOPLAY
                val isQuietSelected = uiState.selectedMode == ModeType.QUIET
                // Autoplay Mode
                ModeSelectionItem(
                    icon = stringResource(R.string.ic_sound_open),
                    title = stringResource(R.string.mode_autoplay),
                    subtitle = stringResource(R.string.mode_autoplay_subtitle),
                    isSelected = uiState.selectedMode == ModeType.AUTOPLAY,
                    backgroundColor = if (isAutoPlaySelected) colorResource(R.color.color_foreground_highlight_default) else R.color.color_background_4_default.asColor(),
                    iconTextColor = if (isAutoPlaySelected) {
                        colorResource(R.color.color_text_black_primary)
                    } else {
                        colorResource(R.color.color_text_white_important)
                    },
                    titleTextColor = if (isAutoPlaySelected) {
                        colorResource(R.color.color_text_black_primary)
                    } else {
                        colorResource(R.color.color_text_white_important)
                    },
                    contentTextColor = if (isAutoPlaySelected) {
                        colorResource(R.color.color_text_black_secondary)
                    } else {
                        colorResource(R.color.color_text_white_tertiary)
                    },
                    onClick = { uiState.uiEvent.onModeSelected(ModeType.AUTOPLAY) }
                )

                VerticalSpace(16.dp)

                // Quiet Mode
                ModeSelectionItem(
                    icon = stringResource(R.string.ic_mute),
                    title = stringResource(R.string.mode_quiet),
                    subtitle = stringResource(R.string.mode_quiet_subtitle),
                    isSelected = uiState.selectedMode == ModeType.QUIET,
                    backgroundColor = if (isQuietSelected) colorResource(R.color.color_background_dnd_1_default) else R.color.color_background_4_default.asColor(),
                    iconTextColor = if (isQuietSelected) {
                        colorResource(R.color.color_text_black_primary)
                    } else {
                        colorResource(R.color.color_text_white_important)
                    },
                    titleTextColor = if (isQuietSelected) {
                        colorResource(R.color.color_text_black_primary)
                    } else {
                        colorResource(R.color.color_text_white_important)
                    },
                    contentTextColor = if (isQuietSelected) {
                        colorResource(R.color.color_background_dnd_1_disable)
                    } else {
                        colorResource(R.color.color_text_white_tertiary)
                    },
                    onClick = { uiState.uiEvent.onModeSelected(ModeType.QUIET) }
                )
            }
            VerticalSpace(40.dp)
            // Set Quiet Time Section
            Column(
                modifier = Modifier.padding(horizontal = 40.dp)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 10.dp, horizontal = 20.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column(modifier = Modifier.weight(1f)) {
                        Text(
                            text = stringResource(R.string.set_quiet_time),
                            style = TextStyles.labelLarge(),
                            color = R.color.text_white_main.asColor()
                        )
                        Text(
                            text = stringResource(R.string.set_quiet_time_subtitle),
                            style = TextStyles.bodySmall(),
                            color = R.color.text_white_secondary.asColor()
                        )
                    }

                    CommonSwitch(
                        checked = uiState.isQuietTimeEnabled,
                        onCheckedChange = uiState.uiEvent.onQuietTimeEnabledChanged
                    )
                }
                VerticalSpace(10.dp)
                // Time Settings
                AnimatedVisibility(uiState.isQuietTimeEnabled) {
                    QuietTimeSettings(
                        fromTime = uiState.quietTimeFrom,
                        toTime = uiState.quietTimeTo,
                        repetition = uiState.quietTimeRepetition,
                        onFromTimeChanged = uiState.uiEvent.onQuietTimeFromChanged,
                        onToTimeChanged = uiState.uiEvent.onQuietTimeToChanged,
                        onRepetitionChanged = uiState.uiEvent.onQuietTimeRepetitionChanged
                    )
                }
            }

            // 底部留白，确保滚动时有足够空间
            VerticalSpace(40.dp)
        }
    }
}

/**
 * Mode selection item component
 */
@Composable
private fun ModeSelectionItem(
    icon: String,
    title: String,
    subtitle: String,
    isSelected: Boolean,
    backgroundColor: Color,
    iconTextColor: Color,
    titleTextColor: Color,
    contentTextColor: Color,
    modifier: Modifier = Modifier,
    onClick: () -> Unit,
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .height(80.dp)
            .background(
                color = backgroundColor,
                shape = CircleShape
            )
            .debouncedClickable { onClick() }
            .padding(horizontal = 16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Icon
        IconFontText(
            modifier = Modifier.padding(6.dp),
            iconText = icon,
            iconSize = 20.dp,
            iconColor = iconTextColor
        )

        Column(
            modifier = Modifier
                .weight(1f)
                .padding(start = 10.dp)
        ) {
            Text(
                text = title,
                style = TextStyles.labelMedium(),
                color = titleTextColor
            )
            Text(
                text = subtitle,
                style = TextStyles.bodySmall(),
                color = contentTextColor
            )
        }
    }
}

/**
 * Quiet time settings component
 */
@Composable
private fun QuietTimeSettings(
    fromTime: String,
    toTime: String,
    repetition: String,
    onFromTimeChanged: (String) -> Unit,
    onToTimeChanged: (String) -> Unit,
    onRepetitionChanged: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.background(
            color = colorResource(R.color.color_background_4_default),
            shape = RoundedCornerShape(16.dp)
        )
    ) {
        // From time setting
        QuietTimeSettingItem(
            label = stringResource(R.string.quiet_time_from),
            value = fromTime,
            onClick = { onFromTimeChanged(fromTime) }
        )
        Spacer(
            modifier = Modifier
                .fillMaxWidth()
                .height(0.5.dp)
                .padding(horizontal = 20.dp)
                .background(color = colorResource(R.color.color_outline_2_default))
        )

        // To time setting
        QuietTimeSettingItem(
            label = stringResource(R.string.quiet_time_to),
            value = toTime,
            onClick = { onToTimeChanged(toTime) }
        )

        Spacer(
            modifier = Modifier
                .fillMaxWidth()
                .height(0.5.dp)
                .padding(horizontal = 20.dp)
                .background(color = colorResource(R.color.color_outline_2_default))
        )
        // Repetition setting
        QuietTimeSettingItem(
            label = stringResource(R.string.quiet_time_repetition),
            value = repetition,
            onClick = { onRepetitionChanged(repetition) }
        )
    }
}

/**
 * Individual quiet time setting item
 */
@Composable
private fun QuietTimeSettingItem(
    label: String,
    value: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .debouncedClickable { onClick() }
            .padding(horizontal = 20.dp)
            .height(48.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = label,
            style = TextStyles.labelMedium(),
            color = R.color.text_white_secondary.asColor()
        )

        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = value,
                style = TextStyles.bodyMedium(),
                color = R.color.text_white_secondary.asColor()
            )

            IconFontText(
                modifier = Modifier.padding(start = 8.dp),
                iconText = stringResource(R.string.ic_arrow_right),
                iconSize = 16.dp,
                iconColor = R.color.text_white_secondary.asColor()
            )
        }
    }
}


/**
 * 解析时间字符串
 */
private fun parseTimeString(timeString: String): Pair<Int, Int> {
    return try {
        if (timeString.isNotEmpty()) {
            val parts = timeString.split(":")
            if (parts.size == 2) {
                val hour = parts[0].toInt()
                val minute = parts[1].toInt()
                hour to minute
            } else {
                9 to 0
            }
        } else {
            9 to 0
        }
    } catch (e: Exception) {
        9 to 0
    }
}

/**
 * Mode types enum
 */
enum class ModeType {
    AUTOPLAY,
    QUIET
}

/**
 * Preview function
 */
@Composable
@Preview(showBackground = true)
private fun SelectQuiteModeScreenPreview() {
    SelectQuiteModeScreen(
        uiState = SelectQuiteModeUiState(
            selectedMode = ModeType.QUIET,
            isQuietTimeEnabled = true,
            quietTimeFrom = "08:00",
            quietTimeTo = "10:00",
            quietTimeRepetition = "Daily"
        )
    )
}

@Composable
@Preview(showBackground = true)
private fun SelectQuiteModeScreenAutoplayPreview() {
    SelectQuiteModeScreen(
        uiState = SelectQuiteModeUiState(
            selectedMode = ModeType.AUTOPLAY,
            isQuietTimeEnabled = false
        )
    )
}
