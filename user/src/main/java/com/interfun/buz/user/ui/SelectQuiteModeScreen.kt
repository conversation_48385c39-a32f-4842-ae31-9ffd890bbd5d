package com.interfun.buz.user.ui

import androidx.compose.animation.core.animateIntOffsetAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import com.interfun.buz.compose.R
import com.interfun.buz.compose.components.*
import com.interfun.buz.compose.ktx.VerticalSpace
import com.interfun.buz.compose.ktx.asColor
import com.interfun.buz.compose.modifier.debouncedClickable
import com.interfun.buz.compose.styles.TextStyles

/**
 * Select Mode Screen UI State
 */
data class SelectQuiteModeUiState(
    val selectedMode: ModeType = ModeType.AUTOPLAY,
    val isQuietTimeEnabled: Boolean = false,
    val quietTimeFrom: String = "9:00",
    val quietTimeTo: String = "17:00",
    val quietTimeRepetition: String = "Weekdays",
    val uiEvent: SelectQuiteModeUiEvent = SelectQuiteModeUiEvent()
)

/**
 * Select Mode Screen UI Events
 */
data class SelectQuiteModeUiEvent(
    val onBackClick: () -> Unit = {},
    val onModeSelected: (ModeType) -> Unit = {},
    val onQuietTimeEnabledChanged: (Boolean) -> Unit = {},
    val onQuietTimeFromChanged: (String) -> Unit = {},
    val onQuietTimeToChanged: (String) -> Unit = {},
    val onQuietTimeRepetitionChanged: (String) -> Unit = {}
)

/**
 * Select Mode Screen
 * 选择模式界面，包含自动播放模式和安静模式的选择
 */
@Composable
fun SelectQuiteModeScreen(
    uiState: SelectQuiteModeUiState,
    modifier: Modifier = Modifier
) {
    val density = LocalDensity.current
    val scrollState = rememberScrollState()

    // 用于测量内容高度和屏幕高度
    var contentHeight by remember { mutableIntStateOf(0) }
    var screenHeight by remember { mutableIntStateOf(0) }

    // 判断是否需要滚动（内容超过屏幕）
    val needsScroll = contentHeight > screenHeight

    // 动画偏移量，用于处理 Quiet Time 开关时的位置调整
    val targetOffset = if (uiState.isQuietTimeEnabled && !needsScroll) 0 else 0
    val animatedOffset by animateIntOffsetAsState(
        targetValue = IntOffset(0, targetOffset),
        animationSpec = tween(durationMillis = 300),
        label = "content_offset"
    )

    Box(
        modifier = modifier
            .fillMaxSize()
            .background(R.color.color_background_1_default.asColor())
            .statusBarsPadding()
            .navigationBarsPadding()
            .onGloballyPositioned { coordinates ->
                screenHeight = coordinates.size.height
            }
    ) {
        // Close Button
        IconFontText(
            modifier = Modifier
                .align(Alignment.TopStart)
                .padding(20.dp)
                .size(24.dp)
                .debouncedClickable { uiState.uiEvent.onBackClick() },
            iconRes = R.string.ic_exit,
            iconSize = 24.dp,
            iconColor = R.color.text_white_main.asColor()
        )

        // Main Content
        val contentModifier = if (needsScroll) {
            Modifier
                .fillMaxSize()
                .verticalScroll(scrollState)
                .padding(top = 64.dp) // 为关闭按钮留出空间
        } else {
            Modifier
                .fillMaxSize()
                .offset { animatedOffset }
        }

        Column(
            modifier = contentModifier
                .onGloballyPositioned { coordinates ->
                    contentHeight = coordinates.size.height + with(density) { 64.dp.toPx().toInt() }
                },
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = if (needsScroll) Arrangement.Top else Arrangement.Center
        ) {

            // Title
            Text(
                text = "Select Mode",
                style = TextStyles.titleLarge(),
                color = R.color.text_white_main.asColor(),
                modifier = Modifier.padding(bottom = 32.dp)
            )

            // Mode Selection
            Column(
                modifier = Modifier.padding(horizontal = 20.dp)
            ) {
                // Autoplay Mode
                ModeSelectionItem(
                    icon = stringResource(R.string.ic_sound_open),
                    title = "Autoplay Mode",
                    subtitle = "Voice messages autoplays when received.",
                    isSelected = uiState.selectedMode == ModeType.AUTOPLAY,
                    onClick = { uiState.uiEvent.onModeSelected(ModeType.AUTOPLAY) }
                )

                VerticalSpace(16.dp)

                // Quiet Mode
                ModeSelectionItem(
                    icon = stringResource(R.string.ic_mute),
                    title = "Quiet Mode",
                    subtitle = "Synced with \"Focus\"",
                    isSelected = uiState.selectedMode == ModeType.QUIET,
                    onClick = { uiState.uiEvent.onModeSelected(ModeType.QUIET) }
                )
            }

            VerticalSpace(40.dp)

            // Set Quiet Time Section
            Column(
                modifier = Modifier.padding(horizontal = 20.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column {
                        Text(
                            text = "Set Quiet Time",
                            style = TextStyles.labelLarge(),
                            color = R.color.text_white_main.asColor()
                        )
                        Text(
                            text = "Automatically disable voice message autoplay in chosen time slots.",
                            style = TextStyles.bodySmall(),
                            color = R.color.text_white_secondary.asColor()
                        )
                    }

                    CommonSwitch(
                        checked = uiState.isQuietTimeEnabled,
                        onCheckedChange = uiState.uiEvent.onQuietTimeEnabledChanged
                    )
                }

                if (uiState.isQuietTimeEnabled) {
                    VerticalSpace(24.dp)

                    // Time Settings
                    QuietTimeSettings(
                        fromTime = uiState.quietTimeFrom,
                        toTime = uiState.quietTimeTo,
                        repetition = uiState.quietTimeRepetition,
                        onFromTimeChanged = uiState.uiEvent.onQuietTimeFromChanged,
                        onToTimeChanged = uiState.uiEvent.onQuietTimeToChanged,
                        onRepetitionChanged = uiState.uiEvent.onQuietTimeRepetitionChanged
                    )
                }
            }

            // 底部留白，确保在滚动模式下有足够空间
            if (needsScroll) {
                VerticalSpace(40.dp)
            }
        }
    }
}

/**
 * Mode selection item component
 */
@Composable
private fun ModeSelectionItem(
    icon: String,
    title: String,
    subtitle: String,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .background(
                color = if (isSelected) {
                    R.color.color_foreground_highlight_default.asColor().copy(alpha = 0.1f)
                } else {
                    R.color.color_background_2_default.asColor()
                },
                shape = RoundedCornerShape(16.dp)
            )
            .debouncedClickable { onClick() }
            .padding(20.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Icon
        IconFontText(
            iconText = icon,
            iconSize = 24.dp,
            iconColor = if (isSelected) {
                R.color.color_foreground_highlight_default.asColor()
            } else {
                R.color.text_white_main.asColor()
            }
        )

        Column(
            modifier = Modifier
                .weight(1f)
                .padding(start = 16.dp)
        ) {
            Text(
                text = title,
                style = TextStyles.labelMedium(),
                color = R.color.text_white_main.asColor()
            )
            Text(
                text = subtitle,
                style = TextStyles.bodySmall(),
                color = R.color.text_white_secondary.asColor()
            )
        }

        // Selection indicator (radio button style)
        Box(
            modifier = Modifier
                .size(20.dp)
                .background(
                    color = if (isSelected) {
                        R.color.color_foreground_highlight_default.asColor()
                    } else {
                        Color.Transparent
                    },
                    shape = RoundedCornerShape(10.dp)
                )
                .then(
                    if (!isSelected) {
                        Modifier.background(
                            color = Color.Transparent,
                            shape = RoundedCornerShape(10.dp)
                        )
                    } else Modifier
                ),
            contentAlignment = Alignment.Center
        ) {
            if (!isSelected) {
                Box(
                    modifier = Modifier
                        .size(20.dp)
                        .background(
                            color = Color.Transparent,
                            shape = RoundedCornerShape(10.dp)
                        )
                        .background(
                            color = R.color.color_foreground_neutral_important_default.asColor(),
                            shape = RoundedCornerShape(10.dp)
                        )
                ) {
                    Box(
                        modifier = Modifier
                            .size(18.dp)
                            .align(Alignment.Center)
                            .background(
                                color = R.color.color_background_2_default.asColor(),
                                shape = RoundedCornerShape(9.dp)
                            )
                    )
                }
            } else {
                IconFontText(
                    iconText = stringResource(R.string.ic_correct),
                    iconSize = 12.dp,
                    iconColor = R.color.color_background_1_default.asColor()
                )
            }
        }
    }
}

/**
 * Quiet time settings component
 */
@Composable
private fun QuietTimeSettings(
    fromTime: String,
    toTime: String,
    repetition: String,
    onFromTimeChanged: (String) -> Unit,
    onToTimeChanged: (String) -> Unit,
    onRepetitionChanged: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        // From time setting
        QuietTimeSettingItem(
            label = "From",
            value = fromTime,
            onClick = { onFromTimeChanged(fromTime) }
        )

        VerticalSpace(16.dp)

        // To time setting
        QuietTimeSettingItem(
            label = "To",
            value = toTime,
            onClick = { onToTimeChanged(toTime) }
        )

        VerticalSpace(16.dp)

        // Repetition setting
        QuietTimeSettingItem(
            label = "Repetition",
            value = repetition,
            onClick = { onRepetitionChanged(repetition) }
        )
    }
}

/**
 * Individual quiet time setting item
 */
@Composable
private fun QuietTimeSettingItem(
    label: String,
    value: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .background(
                color = R.color.color_background_2_default.asColor(),
                shape = RoundedCornerShape(12.dp)
            )
            .debouncedClickable { onClick() }
            .padding(horizontal = 16.dp, vertical = 16.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = label,
            style = TextStyles.labelMedium(),
            color = R.color.text_white_secondary.asColor()
        )

        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = value,
                style = TextStyles.labelMedium(),
                color = R.color.text_white_main.asColor()
            )

            IconFontText(
                modifier = Modifier.padding(start = 8.dp),
                iconText = stringResource(R.string.ic_arrow_right),
                iconSize = 16.dp,
                iconColor = R.color.text_white_secondary.asColor()
            )
        }
    }
}

/**
 * Mode types enum
 */
enum class ModeType {
    AUTOPLAY,
    QUIET
}

/**
 * Preview function
 */
@Composable
@Preview(showBackground = true)
private fun SelectQuiteModeScreenPreview() {
    SelectQuiteModeScreen(
        uiState = SelectQuiteModeUiState(
            selectedMode = ModeType.QUIET,
            isQuietTimeEnabled = true,
            quietTimeFrom = "9:00",
            quietTimeTo = "17:00",
            quietTimeRepetition = "Weekdays"
        )
    )
}

@Composable
@Preview(showBackground = true)
private fun SelectQuiteModeScreenAutoplayPreview() {
    SelectQuiteModeScreen(
        uiState = SelectQuiteModeUiState(
            selectedMode = ModeType.AUTOPLAY,
            isQuietTimeEnabled = false
        )
    )
}
