package com.interfun.buz.user.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.base.ktx.isNull
import com.interfun.buz.base.ktx.launchIO
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.common.arouter.routerServicesForce
import com.interfun.buz.common.database.entity.ExistPlaceType
import com.interfun.buz.common.database.entity.PlaceType
import com.interfun.buz.common.ktx.uid
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.core.widget_liveplace.R
import com.interfun.buz.core.widget_liveplace.state.LivePlaceEntranceUIState
import com.interfun.buz.core.widget_liveplace.state.toEntranceUIState
import com.interfun.buz.core.widget_liveplace.state.toEntranceUIStateSync
import com.interfun.buz.liveplace.manager.LivePlaceCacheHelper
import com.interfun.buz.liveplace.repository.LivePlaceBaseInfoRepositoryImpl
import com.interfun.buz.liveplace.repository.MyLivePlaceRedDotRepository
import com.interfun.buz.liveplace.track.LivePlaceTracker
import com.interfun.buz.onair.standard.IGlobalOnAirController
import com.interfun.buz.onair.standard.OnAirLifecycle.DESTROY
import com.interfun.buz.onair.standard.OnAirLifecycle.PEDDING_JOIN_OR_START
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import javax.inject.Inject

/**
 * <AUTHOR>
 * @date 2025/1/6
 * @desc
 */
@HiltViewModel
class MyLivePlaceEntranceViewModel @Inject constructor(
    private val infoRepo: LivePlaceBaseInfoRepositoryImpl,
    private val redDotRepo: MyLivePlaceRedDotRepository,
) : ViewModel() {

    companion object {
        const val TAG = "MyLivePlaceEntranceViewModel"
    }

    private val myUid get() = UserSessionManager.uid
    private val onAirController by routerServicesForce<IGlobalOnAirController>()
    private var requestResult: Boolean? = null

    private val isMyLivePlaceLiveFlow = onAirController.getOnMimeFlow()
        .flatMapLatest { room ->
            room?.obtainOnAirLifecycleFLow() ?: flowOf(null)
        }
        .mapLatest { lifeCycle ->
            lifeCycle ?: return@mapLatest false
            lifeCycle.serial >= PEDDING_JOIN_OR_START.serial && lifeCycle.serial < DESTROY.serial
        }
        .distinctUntilChanged()
        .stateIn(viewModelScope, SharingStarted.WhileSubscribed(5000L), false)

    val myLivePlaceUiStateFlow = combine(
        infoRepo.livePlaceForUserFlow(myUid),
        infoRepo.getLivePlaceExistInfoFlow(myUid, PlaceType.PRIVATE)
    ) { livePlaceInfo, existInfo ->
        livePlaceInfo to (existInfo?.existPlaceType ?: ExistPlaceType.PENDING)
    }.flatMapLatest { pair ->
        val (livePlaceInfo, existType) = pair
        logInfo(TAG, "myLivePlaceInfo is null: ${livePlaceInfo.isNull()}, existType: $existType")
        if (livePlaceInfo == null) {
            val uiState = existType.toEntranceUIStateSync(myUid, PlaceType.PRIVATE)
            flowOf(uiState)
        } else {
            isMyLivePlaceLiveFlow
                .mapLatest { isLive ->
                    LivePlaceEntranceUIState.Created(
                        topic = livePlaceInfo.topic,
                        name = null, // 设置页不展示名字
                        bgImgUrl = livePlaceInfo.bgImgUrl,
                        isCustomizeBg = livePlaceInfo.isCustomizeBg,
                        btnText = R.string.live_place_enter_my.asString(),
                        isLive = isLive
                    )
                }
        }
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000L),
        initialValue = LivePlaceCacheHelper
            .getExistsInfoFromMem(myUid)
            .toEntranceUIState(myUid, PlaceType.PRIVATE)
    )

    val showRedDotFlow = redDotRepo.showRedDotFlow.stateIn(
        viewModelScope, SharingStarted.WhileSubscribed(5000L), false
    )

    fun requestLivePlace() = launchIO {
        requestResult = infoRepo.requestLivePlace(uid = myUid)
        logInfo(TAG, "requestLivePlace result: $requestResult")
    }

    fun refreshLivePlace() {
        //处理第一次网络无数据情况下，页面再曝光时再请求
        if (requestResult != null) {
            if (requestResult == false
                || myLivePlaceUiStateFlow.value is LivePlaceEntranceUIState.Unknown
            ) {
                logInfo(TAG, "refreshLivePlace")
                requestLivePlace()
            }
        }
    }

    fun setHasShowRedDot() = launchIO {
        redDotRepo.setHasShowRedDot()
    }

    val createLivePlaceByRouterFlow = MutableStateFlow(false)

    fun trackOnPageView() = launchIO {
        val existType = infoRepo.getLivePlaceExistInfo(myUid, PlaceType.PRIVATE)
        LivePlaceTracker.onAVS2022091402(
            hasCreated = existType == ExistPlaceType.EXIST,
            isLive = isMyLivePlaceLiveFlow.value
        )
    }
}