package com.interfun.buz.user.ui

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.SystemBarStyle
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.viewModels
import androidx.compose.runtime.*
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.alibaba.android.arouter.facade.annotation.Route
import com.interfun.buz.common.constants.PATH_USER_ACTIVITY_SET_QUITE_MODE
import com.interfun.buz.compose.R
import com.interfun.buz.compose.base.BaseComposeActivity
import com.interfun.buz.compose.ktx.asColor
import dagger.hilt.android.AndroidEntryPoint

/**
 * Activity for Select Mode Screen
 * 选择模式界面的Activity示例
 */
@AndroidEntryPoint
@Route(path = PATH_USER_ACTIVITY_SET_QUITE_MODE)
class SelectQuiteModeActivity : BaseComposeActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        enableEdgeToEdge(
            statusBarStyle = SystemBarStyle.dark(android.graphics.Color.TRANSPARENT),
            navigationBarStyle = SystemBarStyle.dark(android.graphics.Color.TRANSPARENT),
        )
        super.onCreate(savedInstanceState)
    }

    override fun setSystemBarImmersive() {
    }

    @Composable
    override fun ComposeContent() {
        SelectQuiteModeScreen {
            finish()
        }
    }

}


