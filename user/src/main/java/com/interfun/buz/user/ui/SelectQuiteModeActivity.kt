package com.interfun.buz.user.ui

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.runtime.*
import androidx.compose.ui.tooling.preview.Preview
import com.interfun.buz.compose.R
import com.interfun.buz.compose.ktx.asColor

/**
 * Activity for Select Mode Screen
 * 选择模式界面的Activity示例
 */
class SelectQuiteModeActivity : ComponentActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            SelectQuiteModeContent()
        }
    }
}

@Composable
private fun SelectQuiteModeContent() {
    var selectedMode by remember { mutableStateOf(ModeType.AUTOPLAY) }
    var isQuietTimeEnabled by remember { mutableStateOf(false) }
    var quietTimeFrom by remember { mutableStateOf("9:00") }
    var quietTimeTo by remember { mutableStateOf("17:00") }
    var quietTimeRepetition by remember { mutableStateOf("Weekdays") }

    val uiState = SelectQuiteModeUiState(
        selectedMode = selectedMode,
        isQuietTimeEnabled = isQuietTimeEnabled,
        quietTimeFrom = quietTimeFrom,
        quietTimeTo = quietTimeTo,
        quietTimeRepetition = quietTimeRepetition,
        uiEvent = SelectQuiteModeUiEvent(
            onBackClick = {
                // Handle back navigation
            },
            onModeSelected = { mode ->
                selectedMode = mode
            },
            onQuietTimeEnabledChanged = { enabled ->
                isQuietTimeEnabled = enabled
            },
            onQuietTimeFromChanged = { time ->
                quietTimeFrom = time
                // Here you would typically show a time picker dialog
            },
            onQuietTimeToChanged = { time ->
                quietTimeTo = time
                // Here you would typically show a time picker dialog
            },
            onQuietTimeRepetitionChanged = { repetition ->
                quietTimeRepetition = repetition
                // Here you would typically show a repetition selection dialog
            }
        )
    )

    SelectQuiteModeScreen(uiState = uiState)
}

@Preview(showBackground = true)
@Composable
private fun SelectQuiteModeActivityPreview() {
    SelectQuiteModeContent()
}
