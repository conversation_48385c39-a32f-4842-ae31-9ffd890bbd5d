<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout"
    tools:background="@color/color_background_1_default">

    <!--状态栏-->
    <androidx.legacy.widget.Space
        android:id="@+id/spaceStatusBar"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_height="44dp" />

    <androidx.legacy.widget.Space
        android:id="@+id/spaceTitleBar"
        android:layout_width="0dp"
        android:layout_height="@dimen/title_bar_height"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/spaceStatusBar" />

    <!--头像入口-->
    <View
        android:id="@+id/viewPortraitClickArea"
        android:layout_width="65dp"
        android:layout_height="40dp"
        app:layout_constraintBottom_toBottomOf="@+id/spaceTitleBar"
        app:layout_constraintStart_toStartOf="@+id/spaceTitleBar"
        app:layout_constraintTop_toTopOf="@+id/spaceTitleBar"
        tools:background="@color/color_4C4C4C" />

    <com.interfun.buz.common.widget.view.PortraitImageView
        android:id="@+id/ivMinePortrait"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_marginStart="20dp"
        app:layout_constraintBottom_toBottomOf="@+id/spaceTitleBar"
        app:layout_constraintStart_toStartOf="@+id/spaceTitleBar"
        app:layout_constraintTop_toTopOf="@+id/spaceTitleBar"
        tools:src="@drawable/common_user_default_portrait_round" />

    <View
        android:id="@+id/viewQuietDotBg"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:background="@drawable/common_rect_background_2_default_top_r30"
        app:layout_constraintBottom_toBottomOf="@+id/viewQuietDot"
        app:layout_constraintEnd_toEndOf="@+id/viewQuietDot"
        app:layout_constraintStart_toStartOf="@+id/viewQuietDot"
        app:layout_constraintTop_toTopOf="@+id/viewQuietDot" />

    <View
        android:id="@+id/viewQuietDot"
        android:layout_width="8dp"
        android:layout_height="8dp"
        android:layout_marginStart="26dp"
        android:background="@drawable/common_oval_basic_primary"
        app:layout_constraintBottom_toBottomOf="@+id/ivMinePortrait"
        app:layout_constraintStart_toStartOf="@+id/ivMinePortrait" />

    <View
        android:id="@+id/viewPortraitRedDot"
        android:layout_width="6dp"
        android:layout_height="6dp"
        android:background="@drawable/common_oval_red_point"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@+id/ivMinePortrait"
        app:layout_constraintEnd_toEndOf="@+id/ivMinePortrait"
        app:layout_constraintStart_toEndOf="@+id/ivMinePortrait"
        app:layout_constraintTop_toTopOf="@+id/ivMinePortrait"
        tools:visibility="visible" />

    <!--头像的红点算上 LivePlace，已经有三处会控制它了，很难改动为combine到一起，所以干脆多写一个红点不影响原本的-->
    <View
        android:id="@+id/viewLivePlaceRedDot"
        android:layout_width="6dp"
        android:layout_height="6dp"
        android:background="@drawable/common_oval_red_point"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@+id/ivMinePortrait"
        app:layout_constraintEnd_toEndOf="@+id/ivMinePortrait"
        app:layout_constraintStart_toEndOf="@+id/ivMinePortrait"
        app:layout_constraintTop_toTopOf="@+id/ivMinePortrait"
        tools:visibility="visible" />

    <com.interfun.buz.base.widget.round.RoundLinearLayout
        android:id="@+id/llQuiteMode"
        android:layout_width="120dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="5dp"
        android:background="@color/color_keys_purple_20"
        android:orientation="horizontal"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="@+id/spaceTitleBar"
        app:layout_constraintEnd_toStartOf="@id/imLoadingStatusView"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toEndOf="@+id/viewPortraitClickArea"
        app:layout_constraintTop_toTopOf="@+id/spaceTitleBar"
        app:round_radius="50dp">

        <!--静音入口-->
        <com.interfun.buz.chat.wt.view.AutoPlayToggleView
            android:id="@+id/autoPlayToggleView"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:padding="3dp" />

        <TextView
            android:id="@+id/tvQuiteMode"
            style="@style/text_label_small"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginStart="3dp"
            android:layout_weight="1"
            android:gravity="center"
            android:textColor="@color/color_foreground_dnd_default"
            android:maxLines="2"
            tools:text="quite"
            android:textSize="12sp" />

        <LinearLayout
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_gravity="center"
            android:layout_marginStart="2dp"
            android:layout_marginEnd="3dp"
            android:gravity="center"
            android:orientation="vertical">

            <com.interfun.buz.common.widget.view.IconFontTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/ic_arrow_up"
                android:textColor="@color/color_keys_purple_60"
                android:textSize="10dp"
                tools:ignore="SpUsage" />

            <com.interfun.buz.common.widget.view.IconFontTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/ic_arrow_down"
                android:textColor="@color/color_keys_purple_60"
                android:layout_marginTop="-3dp"
                android:textSize="10dp"
                tools:ignore="SpUsage" />
        </LinearLayout>

    </com.interfun.buz.base.widget.round.RoundLinearLayout>

    <!--通讯录入口-->
    <View
        android:id="@+id/vContactExpandArea"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginEnd="15dp"
        app:layout_constraintBottom_toBottomOf="@+id/spaceTitleBar"
        app:layout_constraintEnd_toEndOf="@+id/spaceTitleBar"
        app:layout_constraintTop_toTopOf="@+id/spaceTitleBar"
        tools:background="@color/color_4C4C4C" />

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftvContact"
        style="@style/iconfont_base"
        android:layout_width="28dp"
        android:layout_height="28dp"
        android:text="@string/ic_contact_add_solid"
        android:textColor="@color/color_text_white_primary"
        android:textSize="28dp"
        app:layout_constraintBottom_toBottomOf="@+id/vContactExpandArea"
        app:layout_constraintEnd_toEndOf="@+id/vContactExpandArea"
        app:layout_constraintStart_toStartOf="@+id/vContactExpandArea"
        app:layout_constraintTop_toTopOf="@+id/vContactExpandArea"
        tools:ignore="SpUsage" />

    <TextView
        android:id="@+id/unReadFriendRequestCount"
        android:layout_width="wrap_content"
        android:layout_height="20dp"
        android:layout_marginEnd="4dp"
        android:background="@drawable/common_unread_count_bubble"
        android:gravity="center"
        android:includeFontPadding="false"
        android:paddingHorizontal="6dp"
        android:textColor="@color/text_white_important"
        android:textSize="12sp"
        android:visibility="gone"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toTopOf="@+id/iftvContact"
        app:layout_constraintEnd_toEndOf="@+id/iftvContact"
        app:layout_constraintStart_toEndOf="@+id/iftvContact"
        app:layout_constraintTop_toTopOf="@+id/iftvContact"
        app:layout_constraintWidth_min="20dp"
        tools:text="1"
        tools:visibility="visible" />


    <!--ai 入口-->
    <View
        android:id="@+id/vifAIExpandArea"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginEnd="10dp"
        app:layout_constraintBottom_toBottomOf="@+id/spaceTitleBar"
        app:layout_constraintEnd_toStartOf="@+id/vContactExpandArea"
        app:layout_constraintTop_toTopOf="@+id/spaceTitleBar"
        tools:background="@color/color_4C4C4C" />

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/ifAI"
        style="@style/iconfont_base"
        android:layout_width="28dp"
        android:layout_height="28dp"
        android:text="@string/ic_ai_solid"
        android:textColor="@color/color_text_white_primary"
        android:textSize="28dp"
        app:layout_constraintBottom_toBottomOf="@+id/vifAIExpandArea"
        app:layout_constraintEnd_toEndOf="@id/vifAIExpandArea"
        app:layout_constraintStart_toStartOf="@id/vifAIExpandArea"
        app:layout_constraintTop_toTopOf="@+id/vifAIExpandArea"
        tools:ignore="SpUsage" />

    <View
        android:id="@+id/aiRedDot"
        android:layout_width="6dp"
        android:layout_height="6dp"
        android:layout_marginTop="7dp"
        android:layout_marginEnd="7dp"
        android:background="@drawable/common_oval_red_point"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@+id/vifAIExpandArea"
        app:layout_constraintTop_toTopOf="@+id/vifAIExpandArea"
        tools:visibility="visible" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupAI"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone"
        app:constraint_referenced_ids="aiRedDot,ifAI,vifAIExpandArea"
        tools:visibility="visible" />


    <!--搜索 入口-->
    <View
        android:id="@+id/vifSearchExpandArea"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginEnd="10dp"
        app:layout_constraintBottom_toBottomOf="@+id/spaceTitleBar"
        app:layout_constraintEnd_toStartOf="@+id/vifAIExpandArea"
        app:layout_constraintTop_toTopOf="@+id/spaceTitleBar"
        tools:background="@color/color_4C4C4C" />

    <com.interfun.buz.common.widget.view.IconFontTextView
        android:id="@+id/iftvSearch"
        style="@style/iconfont_base"
        android:layout_width="28dp"
        android:layout_height="28dp"
        android:text="@string/ic_search_input"
        android:textColor="@color/color_text_white_primary"
        android:textSize="28dp"
        app:layout_constraintBottom_toBottomOf="@+id/vifSearchExpandArea"
        app:layout_constraintEnd_toEndOf="@id/vifSearchExpandArea"
        app:layout_constraintStart_toStartOf="@id/vifSearchExpandArea"
        app:layout_constraintTop_toTopOf="@+id/vifSearchExpandArea"
        tools:ignore="SpUsage" />


    <com.interfun.buz.chat.wt.view.QuiteModeLightBgView
        android:id="@+id/ivQuiteLight"
        android:layout_width="186dp"
        android:layout_height="72dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/spaceTitleBar"
        app:layout_constraintStart_toStartOf="@+id/spaceTitleBar"
        app:layout_constraintTop_toTopOf="@+id/spaceTitleBar"
        app:off_src="@drawable/home_quite_mode_light_on"
        app:on_src="@drawable/home_quite_mode_light_off"
        app:round_top_end_radius="@dimen/bottom_sheet_dialog_top_radius"
        app:round_top_start_radius="@dimen/bottom_sheet_dialog_top_radius"
        tools:src="@drawable/chat_quiet_model_light_on"
        tools:visibility="gone" />


    <com.interfun.buz.chat.common.view.widget.IMLoadingStatusView
        android:id="@+id/imLoadingStatusView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="invisible"
        android:layout_marginEnd="10dp"
        app:layout_constraintBottom_toBottomOf="@+id/spaceTitleBar"
        app:layout_constraintTop_toTopOf="@id/spaceTitleBar"
        app:layout_constraintEnd_toStartOf="@id/vifSearchExpandArea"/>

</merge>