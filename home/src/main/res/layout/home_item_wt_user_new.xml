<?xml version="1.0" encoding="utf-8"?>
<com.interfun.buz.base.widget.view.ClipChildrenConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    android:clipChildren="false"
    tools:background="@color/neutral_black"
    tools:layout_height="200dp">

    <com.interfun.buz.base.widget.view.ClipChildrenConstraintLayout
        android:id="@+id/clPortrait"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:clipChildren="false"
        app:layout_constraintBottom_toBottomOf="@+id/spacePortrait"
        app:layout_constraintEnd_toEndOf="@+id/spacePortrait"
        app:layout_constraintStart_toStartOf="@+id/spacePortrait"
        app:layout_constraintTop_toTopOf="@+id/spacePortrait">

        <com.interfun.buz.common.widget.view.BuzLottie
            android:id="@+id/lottiePlaying"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_gravity="center"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHeight_percent="1.36"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:lottie_autoPlay="false"
            app:lottie_loop="true"
            app:lottie_rawRes="@raw/home_wt_item_recording" />

        <com.interfun.buz.core.widget_record.view.VoiceFilterLottie
            android:id="@+id/voiceFilterAnimView"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_gravity="center"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHeight_percent="2"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <com.interfun.buz.common.widget.view.PortraitImageView
            android:id="@+id/ivContactPortrait"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@tools:sample/avatars" />

        <FrameLayout
            android:id="@+id/flMuteSound"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <View
                android:id="@+id/viewMuteSoundBg"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/common_oval_overlay_light" />

            <com.interfun.buz.common.widget.view.IconFontTextView
                android:id="@+id/iftvMuteSound"
                style="@style/font_base"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="@string/ic_sound_close"
                android:textSize="36dp"
                tools:ignore="SpUsage" />

        </FrameLayout>

        <com.interfun.buz.core.widget_liveplace.view.LivePlaceTagView
            android:id="@+id/viewLivePlaceTag"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="1.16"
            tools:visibility="visible" />

    </com.interfun.buz.base.widget.view.ClipChildrenConstraintLayout>

    <com.interfun.buz.base.widget.view.ClipChildrenConstraintLayout
        android:id="@+id/clName"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:transformPivotY="0dp"
        app:layout_constraintBottom_toBottomOf="@+id/spaceName"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/spaceName">

        <FrameLayout
            android:id="@+id/flTag"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="4dp"
            app:layout_constraintBottom_toBottomOf="@+id/tvContactName"
            app:layout_constraintEnd_toStartOf="@+id/tvContactName"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tvContactName">

            <View
                android:id="@+id/viewGreenDot"
                android:layout_width="6dp"
                android:layout_height="6dp"
                android:background="@drawable/common_oval_basic_primary"
                android:visibility="gone"
                tools:visibility="visible" />

            <com.interfun.buz.common.widget.view.IconFontTextView
                android:id="@+id/iftvOfficialTag"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/ic_official"
                android:textColor="@color/basic_primary"
                android:textSize="18sp"
                android:visibility="gone" />


        </FrameLayout>

        <TextView
            android:id="@+id/tvContactName"
            style="@style/text_title_medium"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:ellipsize="end"
            android:gravity="center_horizontal|bottom"
            android:letterSpacing="0.0"
            android:maxLines="1"
            android:textColor="@color/text_white_important"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/iftvMuteNotification"
            app:layout_constraintStart_toEndOf="@+id/flTag"
            tools:text="User Name" />

        <com.interfun.buz.common.widget.view.IconFontTextView
            android:id="@+id/iftvMuteNotification"
            style="@style/iconfont_16"
            android:layout_marginStart="1dp"
            android:text="@string/ic_ring_off"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/tvContactName"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toEndOf="@+id/tvContactName"
            app:layout_constraintTop_toTopOf="@+id/tvContactName"
            tools:visibility="visible" />

    </com.interfun.buz.base.widget.view.ClipChildrenConstraintLayout>

    <com.interfun.buz.base.widget.view.ClipChildrenConstraintLayout
        android:id="@+id/clContent"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:transformPivotY="0dp"
        app:layout_constraintBottom_toBottomOf="@+id/spaceContent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/spaceContent">

        <com.interfun.buz.common.widget.button.CommonButton
            android:id="@+id/btnAdd"
            android:layout_width="0dp"
            android:layout_height="30dp"
            android:layout_marginBottom="1dp"
            android:paddingStart="10dp"
            android:paddingEnd="10dp"
            android:visibility="invisible"
            app:iconFont="@string/ic_contact_add"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_default="wrap"
            app:text="@string/add"
            app:type="primary_small"
            tools:visibility="visible" />

        <LinearLayout
            android:id="@+id/llUserState"
            android:layout_width="0dp"
            android:layout_height="30dp"
            android:clipChildren="false"
            android:gravity="center"
            android:orientation="horizontal"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_default="wrap">

            <View
                android:id="@+id/viewGreenDotNew"
                android:layout_width="7dp"
                android:layout_height="7dp"
                android:layout_marginEnd="4.5dp"
                android:background="@drawable/common_oval_basic_primary"
                android:visibility="gone"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tvUserState"
                style="@style/text_label_small"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:singleLine="true"
                android:textColor="@color/color_text_highlight_default"
                android:visibility="gone"
                tools:text="3 Members Active"
                tools:visibility="visible" />
        </LinearLayout>

    </com.interfun.buz.base.widget.view.ClipChildrenConstraintLayout>

    <include layout='@layout/home_item_wt_common' />

</com.interfun.buz.base.widget.view.ClipChildrenConstraintLayout>