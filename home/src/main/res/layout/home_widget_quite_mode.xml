<?xml version="1.0" encoding="utf-8"?>
<com.interfun.buz.base.widget.round.RoundLinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/llQuiteMode"
    android:layout_width="120dp"
    android:layout_height="wrap_content"
    android:layout_marginStart="5dp"
    android:background="@color/color_keys_purple_20"
    android:orientation="horizontal"
    app:layout_constrainedWidth="true"
    app:layout_constraintBottom_toBottomOf="@+id/spaceTitleBar"
    app:layout_constraintEnd_toStartOf="@id/imLoadingStatusView"
    app:layout_constraintHorizontal_bias="0"
    app:layout_constraintStart_toEndOf="@+id/viewPortraitClickArea"
    app:layout_constraintTop_toTopOf="@+id/spaceTitleBar"
    app:round_radius="50dp">

    <!--静音入口-->
    <com.interfun.buz.chat.wt.view.AutoPlayToggleView
        android:id="@+id/autoPlayToggleView"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:padding="3dp" />

    <TextView
        android:id="@+id/tvQuiteMode"
        style="@style/text_label_small"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginStart="3dp"
        android:layout_weight="1"
        android:gravity="center"
        android:textColor="@color/color_foreground_dnd_default"
        android:maxLines="2"
        tools:text="quite"
        android:textSize="12sp" />

    <LinearLayout
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_gravity="center"
        android:layout_marginStart="2dp"
        android:layout_marginEnd="3dp"
        android:gravity="center"
        android:orientation="vertical">

        <com.interfun.buz.common.widget.view.IconFontTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/ic_arrow_up"
            android:textColor="@color/color_keys_purple_60"
            android:textSize="10dp"
            tools:ignore="SpUsage" />

        <com.interfun.buz.common.widget.view.IconFontTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/ic_arrow_down"
            android:textColor="@color/color_keys_purple_60"
            android:layout_marginTop="-3dp"
            android:textSize="10dp"
            tools:ignore="SpUsage" />
    </LinearLayout>

</com.interfun.buz.base.widget.round.RoundLinearLayout>