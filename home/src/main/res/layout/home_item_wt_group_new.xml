<?xml version="1.0" encoding="utf-8"?>
<com.interfun.buz.base.widget.view.ClipChildrenConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    android:clipChildren="false"
    tools:background="@color/neutral_black"
    tools:layout_height="200dp">

    <com.interfun.buz.base.widget.view.ClipChildrenConstraintLayout
        android:id="@+id/clPortrait"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:clipChildren="false"
        app:layout_constraintBottom_toBottomOf="@+id/spacePortrait"
        app:layout_constraintEnd_toEndOf="@+id/spacePortrait"
        app:layout_constraintStart_toStartOf="@+id/spacePortrait"
        app:layout_constraintTop_toTopOf="@+id/spacePortrait">

        <com.interfun.buz.common.widget.view.BuzLottie
            android:id="@+id/lottiePlaying"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_gravity="center"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHeight_percent="1.36"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:lottie_autoPlay="false"
            app:lottie_loop="true"
            app:lottie_rawRes="@raw/home_wt_item_recording" />

        <com.interfun.buz.core.widget_record.view.VoiceFilterLottie
            android:id="@+id/voiceFilterAnimView"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_gravity="center"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHeight_percent="2"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <View
            android:id="@+id/viewGroupPortraitBg"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@drawable/common_oval_black"
            app:layout_constraintBottom_toBottomOf="@+id/ivContactPortrait"
            app:layout_constraintEnd_toEndOf="@+id/ivContactPortrait"
            app:layout_constraintStart_toStartOf="@+id/ivContactPortrait"
            app:layout_constraintTop_toTopOf="@+id/ivContactPortrait" />

        <com.interfun.buz.common.widget.view.PortraitImageView
            android:id="@+id/ivContactPortrait"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@tools:sample/avatars" />

        <com.interfun.buz.common.widget.view.PortraitImageView
            android:id="@+id/ivAddressedUser"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <FrameLayout
            android:id="@+id/flMuteSound"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <View
                android:id="@+id/viewMuteSoundBg"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/common_oval_overlay_light" />

            <com.interfun.buz.common.widget.view.IconFontTextView
                android:id="@+id/iftvMuteSound"
                style="@style/font_base"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="@string/ic_sound_close"
                android:textSize="36dp"
                tools:ignore="SpUsage" />

        </FrameLayout>

        <com.interfun.buz.base.widget.view.ClipChildrenConstraintLayout
            android:id="@+id/clPlayingPortrait"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible">

            <View
                android:id="@+id/viewPlayingPortraitBg"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/common_oval_black"
                app:layout_constraintBottom_toBottomOf="@+id/ivPlayingPortrait"
                app:layout_constraintEnd_toEndOf="@+id/ivPlayingPortrait"
                app:layout_constraintStart_toStartOf="@+id/ivPlayingPortrait"
                app:layout_constraintTop_toTopOf="@+id/ivPlayingPortrait" />

            <com.interfun.buz.common.widget.view.PortraitImageView
                android:id="@+id/ivPlayingPortrait"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintWidth_percent="0.66"
                tools:src="@tools:sample/avatars" />

            <androidx.legacy.widget.Space
                android:id="@+id/spacePlayingPortraitTopStart"
                android:layout_width="2dp"
                android:layout_height="2dp"
                app:layout_constraintBottom_toTopOf="@+id/ivPlayingPortrait"
                app:layout_constraintEnd_toStartOf="@+id/ivPlayingPortrait" />

            <androidx.legacy.widget.Space
                android:id="@+id/spacePlayingPortraitBottomEnd"
                android:layout_width="2dp"
                android:layout_height="2dp"
                app:layout_constraintStart_toEndOf="@+id/ivPlayingPortrait"
                app:layout_constraintTop_toBottomOf="@+id/ivPlayingPortrait" />

            <View
                android:id="@+id/viewPlayingPortraitBoard"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/common_oval_stroke_2_white"
                app:layout_constraintBottom_toBottomOf="@+id/spacePlayingPortraitBottomEnd"
                app:layout_constraintEnd_toEndOf="@+id/spacePlayingPortraitBottomEnd"
                app:layout_constraintStart_toStartOf="@+id/spacePlayingPortraitTopStart"
                app:layout_constraintTop_toTopOf="@+id/spacePlayingPortraitTopStart" />

        </com.interfun.buz.base.widget.view.ClipChildrenConstraintLayout>

        <com.interfun.buz.core.widget_liveplace.view.LivePlaceTagView
            android:id="@+id/viewLivePlaceTag"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="1.1"
            tools:visibility="visible" />

    </com.interfun.buz.base.widget.view.ClipChildrenConstraintLayout>

    <com.interfun.buz.base.widget.view.ClipChildrenConstraintLayout
        android:id="@+id/clName"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:transformPivotY="0dp"
        app:layout_constraintBottom_toBottomOf="@+id/spaceName"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/spaceName">

        <View
            android:id="@+id/viewGreenDot"
            android:layout_width="6dp"
            android:layout_height="6dp"
            android:layout_marginEnd="4dp"
            android:background="@drawable/common_oval_basic_primary"
            app:layout_constraintBottom_toBottomOf="@+id/tvContactName"
            app:layout_constraintEnd_toStartOf="@+id/tvContactName"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tvContactName" />

        <TextView
            android:id="@+id/tvContactName"
            style="@style/text_title_medium"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:ellipsize="end"
            android:gravity="center_horizontal|bottom"
            android:letterSpacing="0.0"
            android:maxLines="1"
            android:textColor="@color/text_white_important"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/iftvMuteNotification"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toEndOf="@+id/viewGreenDot"
            tools:text="Group Name" />

        <com.interfun.buz.common.widget.view.IconFontTextView
            android:id="@+id/iftvMuteNotification"
            style="@style/iconfont_16"
            android:layout_marginStart="1dp"
            android:text="@string/ic_ring_off"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/tvContactName"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toEndOf="@+id/tvContactName"
            app:layout_constraintTop_toTopOf="@+id/tvContactName"
            tools:visibility="visible" />

    </com.interfun.buz.base.widget.view.ClipChildrenConstraintLayout>

    <com.interfun.buz.base.widget.view.ClipChildrenConstraintLayout
        android:id="@+id/clContent"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:transformPivotY="0dp"
        app:layout_constraintBottom_toBottomOf="@+id/spaceContent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/spaceContent">

        <com.interfun.buz.base.widget.view.ClipChildrenConstraintLayout
            android:id="@+id/clRemoved"
            android:layout_width="250dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="3dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.interfun.buz.common.widget.view.IconFontTextView
                android:id="@+id/iftvWarn"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_marginStart="14dp"
                android:text="@string/ic_warning_solid"
                android:textColor="@color/text_white_default"
                android:textSize="16sp"
                app:layout_constraintBottom_toBottomOf="@+id/tvBeRemoveTip"
                app:layout_constraintStart_toStartOf="@+id/tvBeRemoveTip"
                app:layout_constraintTop_toTopOf="@+id/tvBeRemoveTip" />

            <TextView
                android:id="@+id/tvBeRemoveTip"
                style="@style/body"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/common_r30_white_04"
                android:paddingStart="36dp"
                android:paddingEnd="6dp"
                android:singleLine="true"
                android:text="@string/chat_be_removed_tip"
                android:textColor="@color/text_white_default"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </com.interfun.buz.base.widget.view.ClipChildrenConstraintLayout>

        <FrameLayout
            android:id="@+id/flGroupOnline"
            android:layout_width="190dp"
            android:layout_height="32dp"
            android:clipChildren="false"
            android:gravity="center"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <LinearLayout
                android:id="@+id/llGroupOnline"
                android:layout_width="190dp"
                android:layout_height="32dp"
                android:clipChildren="false"
                android:gravity="center"
                android:orientation="horizontal"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                tools:visibility="gone">

                <FrameLayout
                    android:id="@+id/flGroupOnlineMember1"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:background="@drawable/common_oval_neutral_black"
                    tools:visibility="gone">

                    <com.interfun.buz.common.widget.view.PortraitImageView
                        android:id="@+id/ivGroupOnlineMember1"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_gravity="center"
                        tools:background="#33F96432" />

                </FrameLayout>

                <FrameLayout
                    android:id="@+id/flGroupOnlineMember2"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_marginStart="-8dp"
                    android:background="@drawable/common_oval_neutral_black"
                    tools:visibility="gone">

                    <com.interfun.buz.common.widget.view.PortraitImageView
                        android:id="@+id/ivGroupOnlineMember2"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_gravity="center"
                        tools:background="#33F96432" />

                </FrameLayout>

                <FrameLayout
                    android:id="@+id/flGroupOnlineMember3"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_marginStart="-8dp"
                    android:background="@drawable/common_oval_neutral_black"
                    tools:visibility="gone">

                    <com.interfun.buz.common.widget.view.PortraitImageView
                        android:id="@+id/ivGroupOnlineMember3"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_gravity="center"
                        tools:background="#33F96432" />

                </FrameLayout>

                <FrameLayout
                    android:id="@+id/flGroupOnlineMember4"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_marginStart="-8dp"
                    android:background="@drawable/common_oval_neutral_black"
                    tools:visibility="gone">

                    <com.interfun.buz.common.widget.view.PortraitImageView
                        android:id="@+id/ivGroupOnlineMember4"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_gravity="center"
                        tools:background="#33F96432" />

                </FrameLayout>

                <FrameLayout
                    android:id="@+id/flGroupOnlineMember5"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_marginStart="-8dp"
                    android:background="@drawable/common_oval_neutral_black"
                    tools:visibility="gone">

                    <com.interfun.buz.common.widget.view.PortraitImageView
                        android:id="@+id/ivGroupOnlineMember5"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_gravity="center"
                        tools:background="#33F96432" />

                </FrameLayout>

                <TextView
                    android:id="@+id/tvGroupMemberCount"
                    style="@style/body"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_marginStart="-8dp"
                    android:background="@drawable/home_wt_bg_offline"
                    android:gravity="center"
                    android:lines="1"
                    android:textColor="@color/text_white_main"
                    android:textSize="12sp"
                    android:visibility="gone"
                    app:roundPercent="1"
                    tools:text="9" />

                <LinearLayout
                    android:id="@+id/flGroupRealTimeCallNumber"
                    android:layout_width="wrap_content"
                    android:layout_height="28dp"
                    android:background="@drawable/home_wt_vc_member_bg"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingHorizontal="10dp">

                    <com.interfun.buz.common.widget.view.IconFontTextView
                        style="@style/iconfont_14"
                        android:text="@string/ic_group_member_solid"
                        android:textColor="@color/color_text_white_primary" />

                    <TextView
                        android:id="@+id/tvGroupRealTimeCallMember"
                        style="@style/text_label_small"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="2dp"
                        android:layout_marginEnd="4dp"
                        android:lines="1"
                        android:textColor="@color/text_white_main"
                        android:textSize="12dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/ivGroupRealTimeCallMember"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="9" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/flGroupJoinRealTimeCall"
                    android:layout_width="wrap_content"
                    android:layout_height="28dp"
                    android:gravity="center_vertical"
                    android:layout_marginStart="-10dp"
                    android:orientation="horizontal"
                    android:background="@drawable/home_wt_vc_btn_bg"
                    android:paddingHorizontal="10dp">

                    <com.interfun.buz.common.widget.view.IconFontTextView
                        android:id="@+id/iftvRealTimeCall"
                        style="@style/iconfont_14"
                        android:text="@string/ic_tel"
                        android:textColor="@color/color_text_black_primary" />

                    <TextView
                        android:id="@+id/tvJoin"
                        style="@style/text_label_small"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="2dp"
                        android:lines="1"
                        android:textColor="@color/color_text_black_primary"
                        android:textSize="12dp"
                        android:text="@string/join"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/ivGroupRealTimeCallMember"
                        app:layout_constraintTop_toTopOf="parent" />

                </LinearLayout>

                <TextView
                    android:id="@+id/tvCallConnecting"
                    style="@style/text_label_small"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/chat_rect_overlay_background_5_default_radius_30"
                    android:gravity="center"
                    android:maxLines="1"
                    android:minHeight="24dp"
                    android:paddingHorizontal="16dp"
                    android:text="@string/common_connecting"
                    android:textColor="@color/color_foreground_neutral_important_pressed"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />


            </LinearLayout>


            <LinearLayout
                android:id="@+id/llActiveMemberCount"
                android:layout_width="190dp"
                android:layout_height="32dp"
                android:clipChildren="false"
                android:gravity="center"
                android:visibility="gone"
                tools:visibility="visible"
                android:orientation="horizontal">

                <View
                    android:id="@+id/viewGreenDotNew"
                    android:layout_width="7dp"
                    android:layout_height="7dp"
                    android:layout_marginEnd="4.5dp"
                    android:background="@drawable/common_oval_basic_primary" />

                <TextView
                    android:id="@+id/tvActiveMemberCount"
                    style="@style/text_label_small"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:singleLine="true"
                    android:textColor="@color/color_text_highlight_default"
                    tools:text="3 Members Active" />
            </LinearLayout>
        </FrameLayout>

        <View
            android:id="@+id/vJoinClickArea"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:visibility="visible"
            tools:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </com.interfun.buz.base.widget.view.ClipChildrenConstraintLayout>

    <include layout='@layout/home_item_wt_common' />

</com.interfun.buz.base.widget.view.ClipChildrenConstraintLayout>