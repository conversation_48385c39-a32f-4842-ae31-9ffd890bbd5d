<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/transparent"
    android:clipChildren="false"
    tools:background="@color/home_conv_list_bg"
    tools:layout_height="@dimen/home_preview_list_item_height"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <View
        android:id="@+id/space_top"
        android:layout_width="match_parent"
        android:layout_height="10dp" />

    <com.interfun.buz.common.widget.view.MiddleEllipsizeTextView
        android:id="@+id/tvFileName"
        style="@style/text_body_large"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/home_wt_item_preview_horizontal_margin"
        android:layout_marginEnd="12dp"
        android:maxLines="1"
        android:textColor="@color/color_text_white_secondary"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toTopOf="@id/tvFileSize"
        app:layout_constraintEnd_toStartOf="@id/ivFile"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/ivFile"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="test_script.pdf" />

    <TextView
        android:id="@+id/tvFileSize"
        style="@style/text_body_medium"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/home_wt_item_preview_horizontal_margin"
        android:layout_marginTop="2dp"
        android:layout_marginEnd="12dp"
        android:gravity="start"
        android:textColor="@color/color_text_white_tertiary"
        android:textSize="14sp"
        app:layout_constraintBottom_toBottomOf="@id/ivFile"
        app:layout_constraintEnd_toStartOf="@id/ivFile"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvFileName"
        tools:text="100KB" />

    <ImageView
        android:id="@+id/ivFile"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/home_preview_file_image_height_max"
        android:layout_marginEnd="@dimen/home_wt_item_preview_horizontal_margin"
        android:adjustViewBounds="true"
        android:src="@drawable/file_preview_image_drawable"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/space_top" />

    <com.interfun.buz.common.widget.view.AutoDirectionTextView
        android:id="@+id/tvFilExt"
        style="@style/text_body_medium"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="4dp"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:textColor="@color/color_text_white_primary"
        android:textSize="10sp"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="@id/ivFile"
        app:layout_constraintEnd_toEndOf="@id/ivFile"
        app:layout_constraintStart_toStartOf="@id/ivFile"
        app:layout_constraintTop_toTopOf="@id/ivFile"
        tools:text="PDF" />

    <com.interfun.buz.common.widget.media.UploadMediaButton
        android:id="@+id/uploadMediaButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:bz_uploadBgSize="32dp"
        app:bz_uploadIconSize="16sp"
        app:bz_uploadProgressCircular="32dp"
        app:bz_uploadTrackThickness="3dp"
        app:layout_constraintBottom_toBottomOf="@id/ivFile"
        app:layout_constraintEnd_toEndOf="@id/ivFile"
        app:layout_constraintStart_toStartOf="@id/ivFile"
        app:layout_constraintTop_toTopOf="@id/ivFile" />

    <com.interfun.buz.common.widget.media.DownloadFileButton
        android:id="@+id/downloadFileButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:bz_downloadBgSize="32dp"
        app:bz_downloadIconSize="16sp"
        app:bz_downloadProgressCircular="32dp"
        app:bz_downloadTrackThickness="3dp"
        app:layout_constraintBottom_toBottomOf="@id/ivFile"
        app:layout_constraintEnd_toEndOf="@id/ivFile"
        app:layout_constraintStart_toStartOf="@id/ivFile"
        app:layout_constraintTop_toTopOf="@id/ivFile" />
</merge>