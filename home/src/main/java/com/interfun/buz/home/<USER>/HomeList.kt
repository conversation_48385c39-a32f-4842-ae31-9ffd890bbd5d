package com.interfun.buz.home.entity

import com.interfun.buz.chat.wt.entity.HomeMsgPreviewModel

data class HomeList(
    val mainList: List<HomeItem>,
    val previewList: List<HomeMsgPreviewModel>
) {
    override fun equals(other: Any?): <PERSON><PERSON>an {
        if (other === this) {
            return true
        }
        if (other !is HomeList) {
            return false
        }
        return other.mainList === mainList && other.previewList === previewList
    }

    override fun hashCode(): Int {
        var code = mainList.hashCode()
        code = 31 * code + previewList.hashCode()
        return code
    }
}