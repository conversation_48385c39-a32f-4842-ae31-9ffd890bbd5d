package com.interfun.buz.home.data.main

import com.interfun.buz.base.coroutine.main
import com.interfun.buz.common.ktx.isMe
import com.interfun.buz.common.widget.portrait.group.UserPortrait
import com.interfun.buz.domain.im.social.entity.UserConversation
import com.interfun.buz.domain.im.social.entity.displayName
import com.interfun.buz.domain.im.social.entity.portrait
import com.interfun.buz.home.data.entity.MainLiveInfo
import com.interfun.buz.home.entity.HomeConversationItem
import com.interfun.buz.home.entity.HomeUserItem
import com.interfun.buz.im.ktx.getConvTargetIdLong
import com.interfun.buz.onair.bean.isLivePlaceOpen
import com.interfun.buz.social.entity.UserOnlineStatus
import com.interfun.buz.social.entity.convertStatusToState
import com.lizhi.im5.sdk.conversation.IM5ConversationType

class UserConversationItemCreator : MainItemCreator<UserConversation> {
    override fun create(
        conversation: UserConversation,
        mainLiveInfo: MainLiveInfo
    ): HomeConversationItem {
        val (playingMsg, playingPortrait) = mainLiveInfo.playingTargetInfo
        val isPlaying =
            playingMsg?.conversationType == IM5ConversationType.PRIVATE && playingMsg.getConvTargetIdLong() == conversation.convId
        val userComposite = conversation.userComposite
        val relationInfo = userComposite?.relationInfo
        val isLivePlaceOpen = mainLiveInfo.convChannelInfo[conversation.convId]?.isLivePlaceOpen() == true
        val user = userComposite?.user
        //官号跟自己不展示在线状态，旧逻辑
        val onlineInfo =
            if (user?.isOfficial == true || user?.userId.isMe()) null else mainLiveInfo.userOnlineStatus[conversation.convId]
        val userOnlineStatus = UserOnlineStatus(
            conversation.convId,
            onlineInfo?.quietMode ?: 0,
            onlineInfo?.onlineTime ?: 0,
            onlineInfo?.offlineTime ?: 0
        )
        return HomeUserItem(
            conversation.convId,
            conversation.displayName,
            conversation.portrait as UserPortrait,
            isRecordingTo = mainLiveInfo.recordingTarget?.targetId == conversation.convId,
            relation = relationInfo?.relationEnum,
            userType = user?.userType,
            isRequestingFriend = mainLiveInfo.addingFriendsSet.contains(conversation.convId),
            unReadCount = conversation.unReadCount,
            mentionMeCount = conversation.mentionMeCount,
            isMuteNotification = relationInfo?.isMuteNotification ?: false,
            isMuteMessages = relationInfo?.isMuteMessages ?: false,
            isPlaying = isPlaying,
            isOnLivePlace = isLivePlaceOpen,
            onlineStatus = userOnlineStatus,
            userStateInfo = userOnlineStatus.convertStatusToState()
        )
    }
}