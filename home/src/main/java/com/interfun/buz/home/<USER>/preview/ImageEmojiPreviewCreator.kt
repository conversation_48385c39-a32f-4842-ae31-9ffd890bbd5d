package com.interfun.buz.home.data.preview

import com.interfun.buz.chat.voicemoji.ktx.convertToVoiceEmojiCategoryType
import com.interfun.buz.chat.wt.entity.HomeMsgPreviewModel
import com.interfun.buz.chat.wt.entity.HomeMsgPreviewModel.ImageVoiceEmojiPreview
import com.interfun.buz.domain.im.social.entity.Conversation
import com.interfun.buz.home.data.entity.PreviewLiveInfo
import com.interfun.buz.im.entity.IMType
import com.interfun.buz.im.ktx.isSameMessage
import com.interfun.buz.im.message.WTVoiceEmojiImgMsg
import com.lizhi.im5.sdk.message.IMessage

class ImageEmojiPreviewCreator : MessagePreviewCreator<WTVoiceEmojiImgMsg>() {
    override val type: Int = IMType.TYPE_VOICE_EMOJI_IMG

    override fun createMsgPreview(
        conv: Conversation,
        liveInfo: PreviewLiveInfo,
        msg: IMessage,
        content: WTVoiceEmojiImgMsg,
        baseMsgInfo: HomeMsgPreviewModel.BaseMsgInfo
    ): HomeMsgPreviewModel.IMsgPreview {
        return ImageVoiceEmojiPreview(
            content.emojiIcon,
            content.emojiCategoryType.convertToVoiceEmojiCategoryType(),
            baseMsgInfo,
            duration = content.duration,
            isPlaying = msg.isSameMessage(liveInfo.playingMsg),
            voiceFilterName = null,
            voiceFilterColor = null
        )
    }
}
