package com.interfun.buz.home.data.preview

import com.interfun.buz.chat.wt.entity.HomeMsgPreviewModel
import com.interfun.buz.chat.wt.entity.HomeMsgPreviewModel.VoiceGifPreview
import com.interfun.buz.domain.im.social.entity.Conversation
import com.interfun.buz.home.data.entity.PreviewLiveInfo
import com.interfun.buz.im.entity.IMType
import com.interfun.buz.im.ktx.isSameMessage
import com.interfun.buz.im.message.BuzVoiceGifMsg
import com.lizhi.im5.sdk.message.IMessage

class VoiceGifPreviewCreator : MessagePreviewCreator<BuzVoiceGifMsg>() {
    override val type: Int = IMType.TYPE_VOICE_GIF

    override fun createMsgPreview(
        conv: Conversation,
        liveInfo: PreviewLiveInfo,
        msg: IMessage,
        content: BuzVoiceGifMsg,
        baseMsgInfo: HomeMsgPreviewModel.BaseMsgInfo
    ): HomeMsgPreviewModel.IMsgPreview {
        return VoiceGifPreview(
            content.videoUrl,
            content.animationUrl,
            content.thumbnailUrl,
            content.width,
            content.height,
            baseMsgInfo,
            duration = content.duration,
            isPlaying = msg.isSameMessage(liveInfo.playingMsg),
            voiceFilterName = null,
            voiceFilterColor = null
        )
    }
}