package com.interfun.buz.home.entity

import com.interfun.buz.social.db.entity.BuzGroupComposite
import com.interfun.buz.social.db.entity.BuzUserComposite
import com.lizhi.im5.sdk.conversation.IM5ConversationType

/**
 * 请不要在这里加一些可能会变化的变量，比如用户名称之类的，这里只保留targetId跟类型即可，
 * 有需要其他的数据自行collect对应的flow去查询即可，因为选中的target是一个核心数据，其余数据是非核心数据，
 * 不要耦合在一起，不然这里不会触发变更，用了后以后会导致越来越臃肿，导致一些更新问题
 */
sealed interface HomeSelectedItem {
    data object None : HomeSelectedItem
    data object AddButton : HomeSelectedItem
    sealed interface Conversation : HomeSelectedItem {
        val targetId: Long
    }

    data class User(override val targetId: Long) : Conversation, HomeSelectedItem
    data class Group(override val targetId: Long) : Conversation, HomeSelectedItem
}

fun HomeSelectedItem.isNoneOrAddButton() = this is HomeSelectedItem.None || this is HomeSelectedItem.AddButton


fun HomeSelectedItem.targetIdOrNull(): Long? {
    return when (this) {
        is HomeSelectedItem.User -> targetId
        is HomeSelectedItem.Group -> targetId
        else -> null
    }
}

fun HomeSelectedItem.imConvTypeOrNull(): IM5ConversationType? {
    return when (this) {
        is HomeSelectedItem.User -> IM5ConversationType.PRIVATE
        is HomeSelectedItem.Group -> IM5ConversationType.GROUP
        else -> null
    }
}

fun HomeSelectedItem.isGroup(): Boolean {
    return this is HomeSelectedItem.Group
}

fun HomeSelectedItem.isUser(): Boolean {
    return this is HomeSelectedItem.User
}

sealed interface HomeSelectedItemBasicInfo {
    data object None : HomeSelectedItemBasicInfo
    data object AddButton : HomeSelectedItemBasicInfo
    sealed interface Conversation : HomeSelectedItemBasicInfo {
        val targetId: Long
    }
    data class User(override val targetId: Long, val userComposite: BuzUserComposite?) :
        HomeSelectedItemBasicInfo,Conversation

    data class Group(override val targetId: Long, val groupComposite: BuzGroupComposite?) :
        HomeSelectedItemBasicInfo,Conversation
}

fun HomeSelectedItemBasicInfo.isNoneOrAddButton() = this is HomeSelectedItemBasicInfo.None || this is HomeSelectedItemBasicInfo.AddButton

fun HomeSelectedItemBasicInfo.targetIdOrNull(): Long? {
    return when (this) {
        is HomeSelectedItemBasicInfo.User -> targetId
        is HomeSelectedItemBasicInfo.Group -> targetId
        else -> null
    }
}