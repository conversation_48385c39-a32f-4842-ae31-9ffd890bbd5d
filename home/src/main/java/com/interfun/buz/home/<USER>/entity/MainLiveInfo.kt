package com.interfun.buz.home.data.entity

import com.interfun.buz.common.widget.portrait.group.UserPortrait
import com.interfun.buz.home.entity.HomeSelectedItem
import com.interfun.buz.onair.bean.ConvChannelInfo
import com.interfun.buz.social.db.entity.BotExtra
import com.interfun.buz.social.entity.GroupOnlineMembersInfo
import com.interfun.buz.social.entity.SimpleBuzUser
import com.interfun.buz.social.entity.UserOnlineStatus
import com.lizhi.im5.sdk.message.IMessage

data class MainLiveInfo(
    val recordingTarget : HomeSelectedItem.Conversation?,
    val playingTargetInfo : Pair<IMessage?,UserPortrait?>,
    val userOnlineStatus : Map<Long, UserOnlineStatus>,
    val groupOnlineStatus : Map<Long, GroupOnlineMembersInfo?>,
    val mentionedBotInfo : Pair<Long?, SimpleBuzUser?>,
    val convChannelInfo : Map<Long,ConvChannelInfo>,
    val robotInfo: Map<Long, BotExtra?>,
    val addingFriendsSet: Set<Long>,
)