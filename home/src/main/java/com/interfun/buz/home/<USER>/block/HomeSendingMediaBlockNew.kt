package com.interfun.buz.home.view.block

import android.animation.Animator
import android.animation.AnimatorSet
import android.animation.ValueAnimator
import android.view.animation.AccelerateInterpolator
import androidx.core.animation.doOnEnd
import coil.load
import coil.request.ErrorResult
import coil.request.ImageRequest
import coil.request.SuccessResult
import coil.size.Size
import com.interfun.buz.album.bean.MediaItem
import com.interfun.buz.base.ktx.*
import com.interfun.buz.base.utils.BusUtil
import com.interfun.buz.chat.R
import com.interfun.buz.chat.databinding.ChatSendingMediaAnimationLayoutBinding
import com.interfun.buz.common.base.binding.BaseBindingBlock
import com.interfun.buz.common.constants.RouterParamKey
import com.interfun.buz.common.eventbus.album.StartHomeSendingMediaAnimationEvent
import com.interfun.buz.common.ktx.getLongDefault
import com.interfun.buz.common.ktx.loadImageSupportVideo
import com.interfun.buz.common.ktx.loadWithThumbnailNew
import com.interfun.buz.home.databinding.ChatFragmentHomeNewBinding
import com.interfun.buz.home.entity.HomeSelectedItem
import com.interfun.buz.home.view.fragment.ChatHomeFragmentNew
import com.interfun.buz.home.view.viewmodel.WTViewModelNew
import com.interfun.buz.media.bean.MediaType
import com.lizhi.fm.e2ee.aes.AesComponent
import com.yibasan.lizhifm.lzlogan.Logz
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlin.text.isEmpty

class HomeSendingMediaBlockNew(
    val fragment: ChatHomeFragmentNew,
    binding: ChatFragmentHomeNewBinding,
    val wtViewModel: WTViewModelNew
) :
    BaseBindingBlock<ChatFragmentHomeNewBinding>(binding) {

    companion object {
        const val TAG = "HomeSendingMediaBlock"
    }

    private var originalImageSize: Int = 0
    private var isPlayingAnimation: Boolean = false

    private val imagePreviewLayout by lazy { binding.sendingMediaImagePreviewViewStub.inflate() }
    private val sendingMediaImagePreview by lazy { imagePreviewBinding.sendingMediaImagePreview }
    private val ivSendingMediaImagePreview by lazy { imagePreviewBinding.ivSendingMediaImagePreview }
    private val multiSendingMediaImagePreview by lazy { imagePreviewBinding.multiSendingMediaImagePreview }
    private val imagePreviewBinding by lazy { ChatSendingMediaAnimationLayoutBinding.bind(imagePreviewLayout) }

    override fun initView() {
        super.initView()
        resetMedia()
    }

    override fun initData() {
        super.initData()
        observeMapSentEvent()
        BusUtil.observe<StartHomeSendingMediaAnimationEvent>(fragment.viewLifecycleOwner) { event ->
            val convData = wtViewModel.selectedItemFlow.value as? HomeSelectedItem.Conversation
            convData?.let {
                if (isPlayingAnimation.not()) {
                    val source = event.source
                    val firstItem = event.firstItem as MediaItem
                    originalImageSize = ivSendingMediaImagePreview.measuredWidth
                    var width = multiSendingMediaImagePreview.measuredWidth
                    var height = multiSendingMediaImagePreview.measuredHeight

                    if (ivSendingMediaImagePreview.measuredWidth == 0) {
                        originalImageSize = 250.dp
                    }
                    if (width == 0) {
                        width = if (source == RouterParamKey.Album.ALBUM_SEND_SOURCE_MEDIA_PREVIEW_FRAGMENT)
                            fragment.requireContext.screenWidth
                        else
                            200.dp
                    }
                    if (height == 0) {
                        height = if (source == RouterParamKey.Album.ALBUM_SEND_SOURCE_MEDIA_PREVIEW_FRAGMENT)
                            fragment.requireContext.screenHeight
                        else
                            200.dp
                    }

                    resetMedia()
                    sendingMediaImagePreview.visible()

                    val imageSize = if (width > 0 && height > 0){
                        Size(width, height)
                    }else{
                        logError(TAG,"ivCoverParams width = $width, height = $height")
                        null
                    }

                    val onErrorCallback: TwoParamCallback<ImageRequest, ErrorResult> = { _, error ->
                        Logz.tag(TAG).d("Animation failed to start: ${error.throwable}")
                    }
                    val onSuccessCallback: TwoParamCallback<ImageRequest, SuccessResult> = { _, _ ->
                        Logz.tag(TAG).d("Start Animation")
                        Logz.tag(TAG).d("originalImageSize = $originalImageSize width = $width, height = $height")
                        if (event.multipleImages) {
                            multiSendingMediaImagePreview.visible()
                        }
                        val firstAnimation =
                            if (source == RouterParamKey.Album.ALBUM_SEND_SOURCE_MEDIA_PREVIEW_FRAGMENT)
                                prepareFirstAnimationScaleOut()
                            else
                                prepareFirstAnimationSlideIn()
                        playAnimation(firstAnimation)
                    }

                    if (firstItem.mediaUri.isNotEmpty()) {
                        if (firstItem.type == MediaType.Video) {
                            Logz.tag(TAG).d("Video")
                            if (firstItem.thumbnailUrl.isEmpty()) {
                                Logz.tag(TAG).d("mediaUri: ${firstItem.mediaUri} ")
                                ivSendingMediaImagePreview.loadImageSupportVideo(
                                    url = firstItem.mediaUri,
                                    onError = onErrorCallback,
                                    onSuccess = onSuccessCallback
                                ){
                                    imageSize?.let {
                                        size(imageSize.width, imageSize.height)
                                    }
                                }
                            } else {
                                Logz.tag(TAG).d("thumbnailUrl: ${firstItem.thumbnailUrl} ")
                                ivSendingMediaImagePreview.loadWithThumbnailNew(
                                    thumbnailUrl = firstItem.thumbnailUrl,
                                    intermediateUrl = firstItem.hdCoverUrl.imageUrlWithSize(width, height),
                                    finalUrl = firstItem.hdCoverUrl,
                                    aesComponent = firstItem.combineAesIv?.let { AesComponent(it) },
                                    thumbnailRequest = ImageRequest.Builder(ivSendingMediaImagePreview.context).apply {
                                        listener(
                                            onSuccess = onSuccessCallback,
                                            onError = onErrorCallback
                                        )
                                    }.build(),
                                    finalRequest = ImageRequest.Builder(ivSendingMediaImagePreview.context).apply {
                                        listener(
                                            onSuccess = onSuccessCallback,
                                            onError = onErrorCallback
                                        )
                                    }.build()
                                )
                            }
                        } else {
                            Logz.tag(TAG).d("Image")
                            Logz.tag(TAG).d("mediaUri: ${firstItem.mediaUri} ")
                            ivSendingMediaImagePreview.loadWithThumbnailNew(
                                intermediateUrl = firstItem.mediaUri.imageUrlWithSize(width, height),
                                finalUrl = firstItem.mediaUri,
                                aesComponent = firstItem.combineAesIv?.let { AesComponent(it) },
                                thumbnailRequest = ImageRequest.Builder(ivSendingMediaImagePreview.context).apply {
                                    listener(
                                        onSuccess = onSuccessCallback,
                                        onError = onErrorCallback
                                    )
                                }.build(),
                                finalRequest = ImageRequest.Builder(ivSendingMediaImagePreview.context).apply {
                                    listener(
                                        onSuccess = onSuccessCallback,
                                        onError = onErrorCallback
                                    )
                                }.build()
                            )
                        }
                    } else {
                        sendingMediaImagePreview.gone()
                        multiSendingMediaImagePreview.invisible()
                        ivSendingMediaImagePreview.setImageDrawable(null)
                    }
                }
            }
        }
    }

    private fun observeMapSentEvent() {
        fragment.viewLifecycleScope.launch {
            wtViewModel.onSendLocationFlow.collectLatest {
                resetMedia()
                sendingMediaImagePreview.visible()
                ivSendingMediaImagePreview.load(R.drawable.chat_map_anim)
                val firstAnimation = prepareFirstAnimationScaleOut()
                playAnimation(firstAnimation)
            }
        }
    }

    private fun resetMedia(){
        sendingMediaImagePreview.invisible()
        multiSendingMediaImagePreview.invisible()
        sendingMediaImagePreview.translationY = 0f
        binding.sendingMediaImagePreviewViewStub.z = 0f
        fragment.bindingCenter.rvWtList.z = 0f
        ivSendingMediaImagePreview.setImageBitmap(null)
        ivSendingMediaImagePreview.layoutWidth(originalImageSize)
        ivSendingMediaImagePreview.layoutHeight(originalImageSize)
        multiSendingMediaImagePreview.layoutWidth(originalImageSize)
        multiSendingMediaImagePreview.layoutHeight(originalImageSize)
        sendingMediaImagePreview.gone()
    }

    private fun playAnimation(firstAnimation : Animator){
        isPlayingAnimation = true
        firstAnimation.apply {
            doOnEnd {
                fragment.bindingCenter.rvWtList.z = 5f
                binding.sendingMediaImagePreviewViewStub.z = 10f
                prepareMediaSecondAnimation().apply {
                    doOnEnd {
                        wtViewModel.updatePortraitAnimationState(
                            wtViewModel.getCurrentItemTargetId().getLongDefault(), true
                        )
                        resetMedia()
                        isPlayingAnimation = false
                    }
                }.start()
            }
        }.start()
    }

    // First animation animation (size = full size -> 200)
    private fun prepareFirstAnimationScaleOut(): AnimatorSet{
        val preImagePreviewWidthAnimation = ValueAnimator.ofInt(
            fragment.requireContext.screenWidth,
            200.dp
        ).apply {
            addUpdateListener { valueAnimator ->
                val value = valueAnimator.animatedValue as Int
                ivSendingMediaImagePreview.layoutWidth(value)
            }
        }

        val preImagePreviewHeightAnimation = ValueAnimator.ofInt(
            fragment.requireContext.screenHeight,
            200.dp
        ).apply {
            addUpdateListener { valueAnimator ->
                val value = valueAnimator.animatedValue as Int
                ivSendingMediaImagePreview.layoutHeight(value)
            }
        }

        return AnimatorSet().apply {
            duration = 250
            interpolator = AccelerateInterpolator()
            playTogether(
                preImagePreviewWidthAnimation,
                preImagePreviewHeightAnimation
            )
        }
    }

    // First animation animation (size = full size -> 200)
    private fun prepareFirstAnimationSlideIn(): AnimatorSet{
        val preImagePreviewWidthAnimation = ValueAnimator.ofInt(
            150.dp,
            200.dp
        ).apply {
            addUpdateListener { valueAnimator ->
                val value = valueAnimator.animatedValue as Int
                ivSendingMediaImagePreview.layoutWidth(value)
                multiSendingMediaImagePreview.layoutWidth(value)
                sendingMediaImagePreview.layoutWidth(value + 50.dp)
            }
        }

        val preImagePreviewHeightAnimation = ValueAnimator.ofInt(
            150.dp,
            200.dp
        ).apply {
            addUpdateListener { valueAnimator ->
                val value = valueAnimator.animatedValue as Int
                ivSendingMediaImagePreview.layoutHeight(value)
                multiSendingMediaImagePreview.layoutHeight(value)
                sendingMediaImagePreview.layoutHeight(value + 50.dp)
            }
        }

        val preImagePreviewSlidingUpAnimation = ValueAnimator.ofFloat(
            ((fragment.requireContext.screenHeight - sendingMediaImagePreview.height)/2).toFloat(),
            0f
        ).apply {
            addUpdateListener { valueAnimator ->
                val value = valueAnimator.animatedValue as Float
                sendingMediaImagePreview.translationY = value
            }
        }

        return AnimatorSet().apply {
            duration = 150
            playTogether(
                preImagePreviewWidthAnimation,
                preImagePreviewHeightAnimation,
                preImagePreviewSlidingUpAnimation
            )
        }
    }

    // Second animation animation (size = 200 -> 40, z-index (elevation + translationZ) = 10dp -> 0dp, y-index (top + translationY) = center of screen -> center of user portrait)
    private fun prepareMediaSecondAnimation(): AnimatorSet{
        val imagePreviewSizeAnimation = ValueAnimator.ofInt(
            200.dp,
            40.dp
        ).apply {
            addUpdateListener { valueAnimator ->
                val value = valueAnimator.animatedValue as Int
                ivSendingMediaImagePreview.layoutWidth(value)
                ivSendingMediaImagePreview.layoutHeight(value)

                multiSendingMediaImagePreview.layoutWidth(value)
                multiSendingMediaImagePreview.layoutHeight(value)

                sendingMediaImagePreview.layoutWidth(value + 50.dp)
                sendingMediaImagePreview.layoutHeight(value + 50.dp)
            }
        }
        val imagePreviewTranslationZAnimation = ValueAnimator.ofFloat(
            binding.sendingMediaImagePreviewViewStub.z,
            0F
        ).apply {
            addUpdateListener { valueAnimator ->
                val value = valueAnimator.animatedValue as Float
                binding.sendingMediaImagePreviewViewStub.z = value
                sendingMediaImagePreview.z = value
            }
        }
        val imagePreviewTranslationYAnimation = ValueAnimator.ofFloat(
            0f,
            fragment.requireContext.screenHeight/2f - (fragment.bindingCenter.viewWtCenterCircle.y + fragment.bindingCenter.viewWtCenterCircle.height/2f)
        ).apply {
            addUpdateListener { valueAnimator ->
                val value = valueAnimator.animatedValue as Float
                sendingMediaImagePreview.translationY = -value
            }
        }

        return AnimatorSet().apply {
            duration = 150
            playTogether(
                imagePreviewSizeAnimation,
                imagePreviewTranslationZAnimation,
                imagePreviewTranslationYAnimation
            )
        }
    }
}