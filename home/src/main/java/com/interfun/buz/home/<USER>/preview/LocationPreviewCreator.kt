package com.interfun.buz.home.data.preview

import com.interfun.buz.chat.wt.entity.HomeMsgPreviewModel
import com.interfun.buz.chat.wt.entity.HomeMsgPreviewModel.LocationPreview
import com.interfun.buz.domain.im.social.entity.Conversation
import com.interfun.buz.home.data.entity.PreviewLiveInfo
import com.interfun.buz.im.entity.IMType
import com.interfun.buz.im.message.BuzLocationMessage
import com.lizhi.im5.sdk.message.IMessage

class LocationPreviewCreator : MessagePreviewCreator<BuzLocationMessage>() {
    override val type: Int = IMType.TYPE_LOCATION_MSG

    override fun createMsgPreview(
        conv: Conversation,
        liveInfo: PreviewLiveInfo,
        msg: IMessage,
        content: BuzLocationMessage,
        baseMsgInfo: HomeMsgPreviewModel.BaseMsgInfo
    ): HomeMsgPreviewModel.IMsgPreview {
        return LocationPreview(content.locationName, baseMsgInfo)
    }

}