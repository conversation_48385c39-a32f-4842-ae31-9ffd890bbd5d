package com.interfun.buz.home.view.block

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.os.Bundle
import android.view.Gravity
import android.widget.Toast
import androidx.lifecycle.Lifecycle
import coil.load
import com.interfun.buz.base.ktx.*
import com.interfun.buz.base.utils.BusUtil
import com.interfun.buz.base.utils.VibratorUtil
import com.interfun.buz.chat.DND.DNDManager
import com.interfun.buz.chat.common.manager.SmartTransManager
import com.interfun.buz.chat.common.utils.DNDTracker
import com.interfun.buz.chat.wt.manager.DriveModelManager
import com.interfun.buz.chat.wt.manager.WTQuietModeManager
import com.interfun.buz.chat.wt.manager.WTQuietModeManager.SET_QUIET_MODE_FROM_HOME_SWITCH
import com.interfun.buz.chat.wt.manager.isBluetooth
import com.interfun.buz.chat.wt.manager.isWired
import com.interfun.buz.chat.wt.utils.WTTracker
import com.interfun.buz.common.base.binding.BaseBindingBlock
import com.interfun.buz.common.constants.CommonMMKV
import com.interfun.buz.common.constants.JumpToChatHomeSource
import com.interfun.buz.common.constants.TrackConstant
import com.interfun.buz.common.eventbus.HomePageJumpSourceEvent
import com.interfun.buz.common.ktx.getBooleanDefault
import com.interfun.buz.common.ktx.setStyleBodyMedium
import com.interfun.buz.common.ktx.setStyleTitleMedium
import com.interfun.buz.common.utils.BuzTracker
import com.interfun.buz.common.widget.button.CommonButton
import com.interfun.buz.common.widget.dialog.CommonAlertDialog
import com.interfun.buz.common.widget.dialog.delegate.DefaultAlertDialogDelegate
import com.interfun.buz.common.widget.toast.BuzToast
import com.interfun.buz.home.R
import com.interfun.buz.home.databinding.ChatFragmentHomeNewBinding
import com.interfun.buz.home.view.fragment.ChatHomeFragmentNew
import com.interfun.buz.home.view.viewmodel.WTViewModelNew
import kotlinx.coroutines.launch

/**
 * <AUTHOR>
 * @date 2023/4/6
 * @desc
 */
class WTQuietModeBlockNew(
    private val wtViewModel: WTViewModelNew,
    private val fragment: ChatHomeFragmentNew,
    binding: ChatFragmentHomeNewBinding
) : BaseBindingBlock<ChatFragmentHomeNewBinding>(binding) {

    private val isQuietModeEnable get() = WTQuietModeManager.isQuietModeEnable
//    private val pagSwitchToAvailable by lazy {
//        getPagFile("pag/chat_wt_quiet_mode_1.pag", 84.dpFloat, 40.dpFloat)
//    }
//    private val pagSwitchToQuiet by lazy {
//        getPagFile("pag/chat_wt_quiet_mode_2.pag", 84.dpFloat, 40.dpFloat)
//    }
    private var customToast: Toast? = null
    private var isChangePagByClick = false
    private var isFromShowMuteGuideTip = false
    private var hasDisplayByPassDNDDialog = false
    private var isFirstOpen = true // 是否首次打开


    companion object {
        const val TAG = "WTQuietModeBlock"
        const val KEY_FIRST_OPEN = "key_first_open"
        const val KEY_HAS_DISPLAY_BY_PASS = "key_has_display_by_pass_dialog"
    }

    @SuppressLint("SuspiciousIndentation")
    override fun initView() {
        fragment.bindingTop.autoPlayToggleView.setHeadset(DriveModelManager.headsetData)
        fragment.bindingTop.autoPlayToggleView.setChecked(!isQuietModeEnable, animate = false)
        BusUtil.observe<HomePageJumpSourceEvent>(fragment) {
            if (it.source == JumpToChatHomeSource.FromShowMuteGuideTip.value) {
                isFromShowMuteGuideTip = true
            }
        }
        //WTQuietModeManager.cacheLatestQuietModeByUserSwitch(isQuietModeEnable)
        fragment.bindingTop.viewQuietClickArea.click {
            if (canShowByPassDNDDialog()) {
                if (null == topActivity) return@click
                val dialog = CommonAlertDialog(
                    topActivity!!,
                    canceledOnTouchOutside = false,
                    title = R.string.chat_bypass_DND_title.asString(),
                    tips = R.string.chat_bypass_DND_content.asString(),
                    positiveText = R.string.chat_bypass_right_btn.asString(),
                    negativeText = R.string.cancel.asString(),
                    positiveButtonType = CommonButton.TYPE_SECONDARY_MEDIUM,
                    negativeCallback = { it.dismiss() },
                    positiveCallback = {
                        hasDisplayByPassDNDDialog = true
                        if (isQuietModeEnable) {
                            onChangeQuietModeByClick()
                        }
                        DNDTracker.onClickAC2024081509()
                        it.dismiss()
                    },
                    cancelable = false
                )
                val dialogBinding = (dialog.delegate as DefaultAlertDialogDelegate).binding
                dialogBinding.tvTitle.setStyleTitleMedium()
                dialogBinding.tvTitle.setTextColor(R.color.text_white_main.asColor())
                dialogBinding.tvTips.setStyleBodyMedium()
                dialogBinding.tvTips.setTextColor(R.color.text_white_default.asColor())
                dialog.show()

            } else {
                onChangeQuietModeByClick()
            }
        }
        fragment.bindingTop.ivQuiteLight.initView()
        updateWtRvBg()
    }

    private fun onChangeQuietModeByClick() {
        isChangePagByClick = true
        VibratorUtil.vibrator(from = TAG + "onChangeQuietModeByClick")
        //WTQuietModeManager.cacheLatestQuietModeByUserSwitch(!isQuietModeEnable)
        WTQuietModeManager.setQuietModeEnable(isEnable = !isQuietModeEnable, SET_QUIET_MODE_FROM_HOME_SWITCH)
        toastQuietModeChange()
        showLightChange()
        updateWtRvBg()
        WTTracker.postOnQuietModeClick(isFromShowMuteGuideTip, isInDND = wtViewModel.isDNDEnable)
        isFromShowMuteGuideTip = false
    }

    private fun updateWtRvBg() {
        val bgRes = if (isQuietModeEnable) {
            R.drawable.home_wt_rv_bg_quite
        } else {
            R.drawable.home_wt_rv_bg_normal
        }
        fragment.bindingCenter.viewWTRvBg.setBackgroundResource(bgRes)
    }

    private fun canShowByPassDNDDialog(): Boolean {
        return !hasDisplayByPassDNDDialog && isQuietModeEnable && wtViewModel.isDNDEnable
    }

    private fun showLightChange() {
        if (isQuietModeEnable) {
            binding.ivLight.load(R.drawable.chat_home_light_off){
                size(fragment.requireContext.screenWidth)
            }
        }else{
            binding.ivLight.load(R.drawable.chat_home_light_on){
                size(fragment.requireContext.screenWidth)
            }
        }

        val imageView = binding.ivLight
        imageView.visible()
        // 创建透明度从0到1的动画，持续200ms
        val fadeIn = ObjectAnimator.ofFloat(imageView, "alpha", 0f, 1f).apply {
            duration = 200
        }

        // 创建透明度从1到0的动画，持续1500ms
        val fadeOut = ObjectAnimator.ofFloat(imageView, "alpha", 1f, 0f).apply {
            duration = 1500
        }

        // 将两个动画组合在一起
        val animatorSet = AnimatorSet().apply {
            playSequentially(fadeIn, fadeOut)
        }

        // 开始动画
        animatorSet.start()
    }

    override fun initData() {
        super.initData()
        WTQuietModeManager.quietModeFlow.collectLatestIn(fragment.viewLifecycleOwner, Lifecycle.State.CREATED) {
            updateQuietModeUI()
        }
        fragment.viewLifecycleScope.launch {
            DriveModelManager.headsetFlow.collect {
                logDebug(TAG, "currentState: ${fragment.viewLifecycleOwner.lifecycle.currentState}")
                fragment.bindingTop.autoPlayToggleView.setHeadset(it, refreshView = true)
            }
        }

//        if (isFirstOpen) {
//            setQuietModelStateFromDND()
//            isFirstOpen = false
//        }
    }

//    // 每次启动app 根据DND与绑卡开关设置静音模式
//    private fun setQuietModelStateFromDND() {
//        var hasBind = CommonMMKV.settingSyncDND
//        logInfo(TAG,"has settingSyncDND: ${hasBind}")
//        if (hasBind) {
//            WTQuietModeManager.setQuietModelStateFromDND()
//        }
//    }

    private fun updateQuietModeUI() {
        if (isQuietModeEnable) {
            fragment.bindingTop.viewQuietDot.setBackgroundResource(R.drawable.common_oval_secondary_purple)
        } else {
            fragment.bindingTop.viewQuietDot.setBackgroundResource(R.drawable.common_oval_basic_primary)
        }
        fragment.bindingTop.ivQuiteLight.updateQuietModeUI(isQuietModeEnable)
        fragment.bindingTop.autoPlayToggleView.setChecked(!isQuietModeEnable, true)

        trackOnExpose()
    }

    private fun toastQuietModeChange() {
        logInfo("toastQuietModeChange")
        customToast?.cancel()
        val title: String
        val content: String
        var lottieFile: String = ""
        var textValue = ""
        if (isQuietModeEnable) {
            title = R.string.auto_play_off.asString()
            content = if(SmartTransManager.isSmartTransEnabled) {
                R.string.auto_play_off_tips.asString()
            } else {
                R.string.auto_play_off_tips_2.asString()
            }
            lottieFile = "lottie/chat_switch_auto_play_to_off.json"
        } else {
            title = R.string.auto_play_on.asString()
            content = if(SmartTransManager.isSmartTransEnabled) {
                R.string.auto_play_and_asr.asString()
            } else {
                R.string.auto_play_on_tips.asString()
            }
            when {
                DriveModelManager.headsetData.isBluetooth() -> {
                    textValue = R.string.ic_bluetooth.asString()
                }
                DriveModelManager.headsetData.isWired() -> {
                    textValue = R.string.ic_earphone.asString()
                }
                else -> {
                    lottieFile = "lottie/chat_switch_auto_play_to_on.json"
                }
            }

        }
        customToast = Toast(context).apply {
            this.duration = Toast.LENGTH_SHORT
            setGravity(Gravity.CENTER, 0, 0)
            this.view = BuzToast.generateIconFontCustomViewStyleLottie(
                context,
                content,
                R.color.text_white_secondary.asColor(),
                title,
                R.color.text_white_main.asColor(),
                lottieFile = lottieFile,
                textValue = textValue
            )
            fixBadTokenException()
        }
        customToast?.show()
    }

    private fun trackOnExpose() {
        BuzTracker.onElementExposure {
            put(TrackConstant.KEY_EXCLUSIVE_ID, "EE2023062902")
            put(TrackConstant.KEY_TITLE, "首页")
            put(TrackConstant.KEY_ELEMENT_CONTENT, "mode")
        }
    }

    override fun onSaveInstanceState(outState: Bundle?) {
        outState?.putBoolean(KEY_FIRST_OPEN, isFirstOpen)
        outState?.putBoolean(KEY_HAS_DISPLAY_BY_PASS, hasDisplayByPassDNDDialog)
    }

    override fun onRestoreInstanceState(savedInstanceState: Bundle?) {
        isFirstOpen = savedInstanceState?.getBoolean(KEY_FIRST_OPEN).getBooleanDefault(true)
        hasDisplayByPassDNDDialog = savedInstanceState?.getBoolean(KEY_HAS_DISPLAY_BY_PASS).getBooleanDefault(false)

    }

    override fun onDestroy() {
        super.onDestroy()
    }
}