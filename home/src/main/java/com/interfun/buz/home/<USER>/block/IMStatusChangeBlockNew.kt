package com.interfun.buz.home.view.block

import androidx.fragment.app.viewModels
import com.interfun.buz.base.ktx.*
import com.interfun.buz.base.utils.BusUtil
import com.interfun.buz.chat.R
import com.interfun.buz.common.base.binding.BaseBindingBlock
import com.interfun.buz.common.eventbus.im5.IMServerStateChangeEvent
import com.interfun.buz.im.signal.PushAgentManager
import com.interfun.buz.home.databinding.ChatFragmentHomeNewBinding
import com.interfun.buz.home.view.fragment.ChatHomeFragmentNew
import com.interfun.buz.home.view.viewmodel.WTViewModelNew
import com.interfun.buz.im.IMAgent
import com.lizhi.component.net.websocket.model.ConnStatus
import com.lizhi.im5.sdk.auth.AuthStatus
import com.lizhi.im5.sdk.base.IM5ConnectStatus
import com.lizhi.im5.sdk.base.IM5ServiceStatus
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch

class IMStatusChangeBlockNew(val fragment: ChatHomeFragmentNew, binding: ChatFragmentHomeNewBinding) : BaseBindingBlock<ChatFragmentHomeNewBinding>(binding) {

    private val networkObserver = NetworkAvailableLiveData()
    private var onResumeTime: Long? = null
    private val wtViewModel by fragment.viewModels<WTViewModelNew>()
    private var mIMServerStatus: IM5ServiceStatus = IM5ServiceStatus.UNKNOWN

    override fun onResume() {
        super.onResume()
        onResumeTime = System.currentTimeMillis()
    }

    override fun onPause() {
        super.onPause()
        onResumeTime = null
    }

    override fun initData() {
        super.initData()
        IMAgent.authStatusFlow.collectIn(fragment.viewLifecycleOwner){
            logInfo("IMStatusChangeBlock", "IMConnectStateChangedEvent->${it}")
            updateStatus()
        }

        BusUtil.observeSticky<IMServerStateChangeEvent>(fragment) {
            logInfo("IMStatusChangeBlock", "IMServerStateChangeEvent->${it.serverStatus}")
            mIMServerStatus = it.serverStatus?: IM5ServiceStatus.UNKNOWN
            val timeGapBetweenResume = System.currentTimeMillis() - (onResumeTime ?: 0L)
            if (it.serverStatus == IM5ServiceStatus.SERVER_DISCONNECTED && timeGapBetweenResume < 1000) {
                log("IMStatusChangeBlock", "delay 1000")
                fragment.viewLifecycleScope.launch {
                    //优化后台回来时会闪一下失败的页面，做个延时显示，https://project.feishu.cn/businesscenter/issue/detail/11713493?parentUrl=%2Fbusinesscenter%2FstoryView%2FSlhfggrVg
                    delay(1000)
                    updateStatus()
                }
            } else if (it.serverStatus == IM5ServiceStatus.SERVER_RECOVERED
                || it.serverStatus == IM5ServiceStatus.END_LOADING
                || it.serverStatus == IM5ServiceStatus.SERVER_DISCONNECTED){
                updateStatus()
            }
        }
        networkObserver.observe(fragment.viewLifecycleOwner){ hasNetwork ->
            logInfo("IMStatusChangeBlock", "networkObserver->hasNetwork:${hasNetwork}")
            updateStatus(hasNetwork)
        }
        wtViewModel.homeList.map { it?.size }.distinctUntilChanged().collectIn(fragment.viewLifecycleOwner) { size ->
            val isLoading = IMAgent.isLoading()
            if (isLoading) {
                updateStatus()
            }
        }
       PushAgentManager.netStatusLive.observe(fragment.viewLifecycleOwner){ status->
           logInfo("IMStatusChangeBlock", "PushAgentManager->netStatusLive:${status}")
           updateStatus()
       }
    }

    private fun updateStatus(networkEnable: Boolean = isNetworkAvailable) {
        val isConnecting =
            IMAgent.authStatusFlow.value == AuthStatus.LOGINING || IMAgent.authStatusFlow.value == AuthStatus.UNLOGIN
        val isLoading = IMAgent.isLoading()
        val isServerDisconnected = mIMServerStatus == IM5ServiceStatus.SERVER_DISCONNECTED
        //罗马状态
        val lmNetStatus = PushAgentManager.netStatusLive.value
        // logMine(
        //     "updateStatus networkEnable = $networkEnable, " +
        //             "isConnecting = $isConnecting, " +
        //             "isLoading = $isLoading, " +
        //             "isServerDisconnected = $isServerDisconnected, " +
        //             "mIMServerStatus = $mIMServerStatus"
        // )
        // 优先级 connecting > disconnect > loading
        if (isConnecting) {
            fragment.bindingTop.imLoadingStatusView.showConnecting(fragment.viewLifecycleScope)
            fragment.bindingCenter.imDisconnectStatusView.gone()
        } else if (
        //IM链接上那么一定是有网的
            IMAgent.imConnectStatus != IM5ConnectStatus.Connected &&
            //这个API不一定准
            networkEnable.not() &&
            //罗马链接一定是有网
            lmNetStatus != ConnStatus.CONNECTED &&
            //罗马如果连接中就不处理，可能弱网
            lmNetStatus != ConnStatus.CONNECTING
        ) {
            showImDisconnectStatusView(R.string.common_waiting_for_network.asString())
        } else if (isServerDisconnected) {
            showImDisconnectStatusView(R.string.common_connect_service_fail.asString())
        } else if (isLoading && getWtItemSize() > 1) {
            fragment.bindingTop.imLoadingStatusView.showLoading(fragment.viewLifecycleScope)
            fragment.bindingCenter.imDisconnectStatusView.gone()
        } else {
            fragment.bindingTop.imLoadingStatusView.hide()
            fragment.bindingCenter.imDisconnectStatusView.gone()
        }
    }

    private fun showImDisconnectStatusView(statusString: String) {
        val statusBinding = fragment.bindingCenter.imDisconnectStatusView.binding
        statusBinding.tvTitle.text = statusString
        statusBinding.root.setRadius(
            if (statusBinding.tvTitle.lineCount == 1) statusBinding.root.height.toFloat() / 2 else R.dimen.guide_layout_radius_min.asDimension()
        )
        fragment.bindingCenter.imDisconnectStatusView.visible()
        fragment.bindingTop.imLoadingStatusView.hide()
    }

    private fun getWtItemSize(): Int {
        return wtViewModel.homeList.value?.size ?: 0
    }
}