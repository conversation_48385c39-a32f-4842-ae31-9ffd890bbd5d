package com.interfun.buz.home.view.itemview.wtlist

import android.view.View
import com.interfun.buz.base.ktx.*
import com.interfun.buz.chat.R
import com.interfun.buz.common.bean.user.BuzUserRelationValue
import com.interfun.buz.common.manager.ABTestManager
import com.interfun.buz.common.widget.button.CommonButton
import com.interfun.buz.domain.chat.ktx.updateUserState
import com.interfun.buz.home.databinding.HomeItemWtUserNewBinding
import com.interfun.buz.home.entity.HomeItemPayloadType
import com.interfun.buz.home.entity.HomeUserItem
import com.interfun.buz.home.view.itemview.callback.WTUserItemCallbackNew
import com.interfun.buz.social.entity.OnlineState
import com.interfun.buz.social.entity.UserStateInfo

/**
 * <AUTHOR>
 * @date 2024/10/20
 * @desc 部分通用逻辑已挪动到基类BaseWTItemView中
 * 后续维护请区分是通用业务还是专有业务（区分user\robot\group），尽量避免重复代码
 */
class WTUserItemViewNew(override val callback: WTUserItemCallbackNew<HomeUserItem>) :
    BaseWTItemView<HomeItemWtUserNewBinding,HomeUserItem>(callback) {

    companion object {
        const val TAG = "WTUserItemViewNew"
    }

    override fun onViewHolderCreated(holder: BindingViewHolder<HomeItemWtUserNewBinding>) {
        super.onViewHolderCreated(holder)
        holder.onClick(this, holder.binding.btnAdd, vibrate = true) { binding, item, pos ->
            callback.onAddClick(item, pos)
        }
    }

    override fun onBindViewHolder(
        binding: HomeItemWtUserNewBinding,
        item: HomeUserItem,
        position: Int
    ) {
        super.onBindViewHolder(binding, item, position)
        updateBtnAddStatus(binding, item.targetId, item.relation)
        updateOfficialTag(binding, item.isOfficial)
        updateOnlineStatus(binding, item.userStateInfo)
        updateBtnAddLoadingStatus(binding, item.isRequestingFriend)
        updateLivePlaceStatus(binding, item)
    }

    override fun handlePayloadType(
        binding: HomeItemWtUserNewBinding,
        item: HomeUserItem,
        type: HomeItemPayloadType
    ) {
        super.handlePayloadType(binding, item, type)
        when (type) {
            HomeItemPayloadType.UpdateRelation -> {
                updateBtnAddStatus(binding, item.targetId, item.relation)
            }

            HomeItemPayloadType.UpdateIsRequestingFriend -> {
                updateBtnAddLoadingStatus(binding, item.isRequestingFriend)
            }

            HomeItemPayloadType.UpdateOnlineStatus -> {
                updateOnlineStatus(binding, item.userStateInfo)
            }

            HomeItemPayloadType.UpdateUserType -> {
                updateOfficialTag(binding, item.isOfficial)
            }

            else -> {}
        }
    }

    private fun updateOfficialTag(binding: HomeItemWtUserNewBinding, isOfficial: Boolean) {
        binding.iftvOfficialTag.visibleIf(isOfficial)
    }

    private fun updateOnlineStatus(
        binding: HomeItemWtUserNewBinding,
        userStateInfo: UserStateInfo
    ) {
        val isOnline = userStateInfo is OnlineState
        val isQuiet = userStateInfo is OnlineState.Quiet

        val targetView = if (ABTestManager.isUserStatePlanA) {
            binding.viewGreenDot.gone()
            binding.viewGreenDotNew
        } else {
            binding.viewGreenDotNew.gone()
            binding.viewGreenDot
        }

        if (isOnline) {
            targetView.visible()
            val resId = if (isQuiet) {
                R.drawable.common_oval_secondary_purple
            } else {
                R.drawable.common_oval_basic_primary
            }
            targetView.setBackgroundResource(resId)
        } else {
            targetView.gone()
        }

        userStateInfo.updateUserState(binding.tvUserState,null)

    }

    private fun updateBtnAddStatus(
        binding: HomeItemWtUserNewBinding,
        targetId : Long,
        userRelation: BuzUserRelationValue?
    ) {
        logInfo(TAG, "updateBtnAddStatus, id:${targetId}, userRelation: $userRelation")
        binding.btnAdd.hideLoading(true)
        when (userRelation) {
            BuzUserRelationValue.NO_RELATION -> {
                // show btn 'Add'
                binding.btnAdd.apply {
                    setText(R.string.add.asString())
                    setIconFontText(R.string.ic_contact_add.asString())
                    setType(CommonButton.TYPE_PRIMARY_SMALL)
                    visible()
                }
            }

            BuzUserRelationValue.BEING_FRIEND_REQUEST -> {
                // show btn 'Accept'
                binding.btnAdd.apply {
                    setText(R.string.accept.asString())
                    setIconFontText(R.string.ic_check.asString())
                    setType(CommonButton.TYPE_PRIMARY_SMALL)
                    visible()
                }
            }

            BuzUserRelationValue.FRIEND_REQUEST -> {
                // show btn 'Pending'
                binding.btnAdd.apply {
                    setText(R.string.pending.asString())
                    setIconFontText(R.string.ic_check.asString())
                    setType(CommonButton.TYPE_TERTIARY_SMALL)
                    visible()
                }
            }

            else -> binding.btnAdd.gone()
        }
    }

    private fun updateBtnAddLoadingStatus(
        binding: HomeItemWtUserNewBinding,
        isRequestingFriend: Boolean
    ) {
        if (isRequestingFriend) {
            binding.btnAdd.showLoading(true)
        } else {
            binding.btnAdd.hideLoading(true)
        }
    }

    override fun onViewRecycled(holder: BindingViewHolder<HomeItemWtUserNewBinding>) {
        super.onViewRecycled(holder)
        holder.binding.btnAdd.hideLoading(true)
    }

    override fun getPortraitView(binding: HomeItemWtUserNewBinding): View {
        return binding.ivContactPortrait
    }

    override fun getPortraitRelatedViews(binding: HomeItemWtUserNewBinding): List<View> {
        return listOf(getPortraitView(binding), binding.viewLivePlaceTag, binding.flMuteSound)
    }
}