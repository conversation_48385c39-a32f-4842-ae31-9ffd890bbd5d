package com.interfun.buz.home.data.preview

import com.interfun.buz.chat.wt.entity.HomeMsgPreviewModel
import com.interfun.buz.domain.im.social.entity.ContactType
import com.interfun.buz.domain.im.social.entity.Conversation
import com.interfun.buz.home.data.entity.PreviewLiveInfo
import com.interfun.buz.im.entity.IMType
import com.interfun.buz.im.message.BuzShareContactMessage
import com.lizhi.im5.sdk.message.IMessage

/**
 * Author: ChenYouSheng
 * Date: 2025/7/10
 * Email: <EMAIL>
 * Desc:
 */
class ShareContactMsgPreviewCreator : MessagePreviewCreator<BuzShareContactMessage>() {
    override val type: Int = IMType.TYPE_SHARE_CONTACT_MSG

    override fun createMsgPreview(
        conv: Conversation,
        liveInfo: PreviewLiveInfo,
        msg: IMessage,
        content: BuzShareContactMessage,
        baseMsgInfo: HomeMsgPreviewModel.BaseMsgInfo
    ): HomeMsgPreviewModel.IMsgPreview? {
        return HomeMsgPreviewModel.ShareContactPreview(
            displayName = content.name,
            portrait = content.portrait,
            isGroupContact = content.contactType == ContactType.Group.type,
            baseMsgInfo = baseMsgInfo,
        )
    }
}