package com.interfun.buz.home.data.transform

import com.interfun.buz.common.manager.ChannelStatusManager
import com.interfun.buz.domain.im.social.entity.Conversation
import com.interfun.buz.onair.bean.ConvChannelInfo
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

class ChannelStatusTransform @Inject constructor() :
    ConversationTransform<Map<Long, ConvChannelInfo>> {
    override fun transform(originList: List<Conversation>): Flow<Map<Long, ConvChannelInfo>> {
        return ChannelStatusManager.convChannelInfoFlow
    }
}