package com.interfun.buz.home.data.transform

import com.interfun.buz.common.ktx.isMe
import com.interfun.buz.domain.im.social.entity.Conversation
import com.interfun.buz.im.message.CommandMsg
import com.interfun.buz.im.message.OnAirCommand
import com.interfun.buz.social.db.entity.BuzUserComposite
import com.interfun.buz.social.repo.UserRepository
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

class PreviewMsgUserInfoUpdateTransform @Inject constructor(
    private val userRepository: UserRepository
) : ConversationTransform<Map<Long, BuzUserComposite>> {

    override fun transform(originList: List<Conversation>): Flow<Map<Long, BuzUserComposite>> {
        val userIds = HashSet<Long>()
        originList.forEach { conversation ->
            val lastMsg = conversation.lastMessage
            val msgContent = lastMsg?.content
            when (msgContent) {
                is CommandMsg -> {
                    val command = msgContent.command
                    if (command is OnAirCommand) {
                        val userId = command.getOnAirStartUserId()
                        if (userId != null && !userId.isMe()) {
                            userIds.add(userId)
                        }
                    }
                }
            }
        }
        return userRepository.getUserCompositeFlow(userIds.toList())
    }
}