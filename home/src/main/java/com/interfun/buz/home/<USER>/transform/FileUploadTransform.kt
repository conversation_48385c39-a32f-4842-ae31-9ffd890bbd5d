package com.interfun.buz.home.data.transform

import com.interfun.buz.chat.wt.entity.HomeMsgPreviewModel.ConversationPreview
import com.interfun.buz.chat.wt.entity.HomeMsgPreviewModel.FilePreview
import com.interfun.buz.common.ktx.isMe
import com.interfun.buz.common.ktx.uid
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.domain.im.social.entity.Conversation
import com.interfun.buz.im.IMAgent
import com.interfun.buz.im.ktx.BuzSendingState
import com.interfun.buz.im.message.BuzFileMessage
import com.interfun.buz.im.util.IMMsgIdentity
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.debounce
import javax.inject.Inject

class FileUploadTransform @Inject constructor() :
    ConversationTransform<Map<IMMsgIdentity, BuzSendingState>> {

    override fun transform(originList: List<Conversation>): Flow<Map<IMMsgIdentity, BuzSendingState>> {
        val sendingFileMsgIds = hashSetOf<IMMsgIdentity>()
        originList.forEach { conv ->
            val lastMsg = conv.lastMessage
            if (lastMsg?.content is BuzFileMessage && lastMsg.fromId.isMe()) {
                sendingFileMsgIds.add(
                    IMMsgIdentity(
                        lastMsg.msgId,
                        lastMsg.conversationType
                    )
                )
            }
        }
        return IMAgent.getMessageSendStateFlow(sendingFileMsgIds).debounce(200L)
    }
}