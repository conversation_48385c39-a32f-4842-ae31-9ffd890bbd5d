package com.interfun.buz.home.data.preview

import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.chat.common.manager.TranslationMessageManager
import com.interfun.buz.chat.common.manager.TranslationMessageManager.translateResult
import com.interfun.buz.chat.wt.entity.HomeMsgPreviewModel.*
import com.interfun.buz.chat.wt.utils.enableTranslate
import com.interfun.buz.common.utils.language.TranslateLanguageManager
import com.interfun.buz.domain.im.social.entity.Conversation
import com.interfun.buz.home.data.entity.PreviewLiveInfo
import com.interfun.buz.im.entity.IMType
import com.interfun.buz.im.entity.IMMessageContentExtra
import com.interfun.buz.im.entity.ShowTranslateTextOp
import com.interfun.buz.im.entity.isAllMetadataNullExceptIcon
import com.interfun.buz.im.entity.translation.TranslateState
import com.interfun.buz.im.ktx.detectLanguageCode
import com.interfun.buz.im.ktx.isGroup
import com.interfun.buz.im.ktx.isReceive
import com.interfun.buz.im.ktx.mentionedInfoText
import com.interfun.buz.im.message.BuzTextMsg
import com.lizhi.im5.sdk.message.IMessage

class TextPreviewCreator : MessagePreviewCreator<BuzTextMsg>() {
    private val TAG = "TextPreviewCreator"

    override val type: Int = IMType.TYPE_TEXT_MSG

    override fun createMsgPreview(
        conv: Conversation,
        liveInfo: PreviewLiveInfo,
        msg: IMessage,
        content: BuzTextMsg,
        baseMsgInfo: BaseMsgInfo
    ): IMsgPreview {
        val extra = IMMessageContentExtra.parseFromJson(content.extra)
        val translateResult = msg.translateResult

        val isTranslationEnabled = TranslationMessageManager.openTranslationFunctionality()
        val isMessageTranslatable = msg.enableTranslate()
        val isManualClose = translateResult.userShowTranslateText() == ShowTranslateTextOp.MANUAL_CLOSE
        val isManualOpen = translateResult.userShowTranslateText() == ShowTranslateTextOp.MANUAL_OPEN
        val containText = msg.content.mentionedInfoText().isNotEmpty()
        val translateState = translateResult.state.takeIf {
            isTranslationEnabled && (isMessageTranslatable || isManualOpen) && !isManualClose
        } ?: TranslateState.Idle

        val isAutoTranslate = TranslationMessageManager.getConversationAutoTranslateSwitch(baseMsgInfo.convTargetId)

        val showTranslateButton = containText &&
                msg.isReceive &&
                !isAutoTranslate &&
                TranslateLanguageManager.isUnknownLanguage(msg.content.detectLanguageCode).not() &&
                msg.content.detectLanguageCode != TranslateLanguageManager.getTargetLanguageCode() &&
                (translateState == TranslateState.Idle || translateState == TranslateState.TranslateFail)

        logDebug(
            TAG,
            "detectLanguageCode: ${msg.content.detectLanguageCode}, " +
            "translateState: ${translateState}, " +
            "translateText: ${msg.translateResult.translateText}, " +
            "showTranslateButton: $showTranslateButton"
        )

        extra.contentStyleExtra?.hyperlinkMetadataExtra?.let { hyperlinkMetadataExtra ->
            if (hyperlinkMetadataExtra.isAllMetadataNullExceptIcon().not()) {
                return HyperlinkPreview(
                    msgContent = msg.content,
                    translateText = if (translateState == TranslateState.Idle) null else msg.translateResult.translateText,
                    translateState = translateState,
                    linkUrl = hyperlinkMetadataExtra.linkUrl,
                    linkImagePath = hyperlinkMetadataExtra.linkImagePath,
                    baseMsgInfo = baseMsgInfo,
                    showTranslateButton = showTranslateButton
                )
            }
        }
        return TextPreview(
            msgContent = msg.content,
            translateText = if (translateState == TranslateState.Idle) null else msg.translateResult.translateText,
            translateState = translateState,
            baseMsgInfo = baseMsgInfo,
            showTranslateButton = showTranslateButton
        )
    }

}