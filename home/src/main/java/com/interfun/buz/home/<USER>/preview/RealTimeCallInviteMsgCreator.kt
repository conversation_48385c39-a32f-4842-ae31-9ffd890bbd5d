package com.interfun.buz.home.data.preview

import com.interfun.buz.chat.wt.entity.HomeMsgPreviewModel
import com.interfun.buz.domain.im.social.entity.Conversation
import com.interfun.buz.home.data.entity.PreviewLiveInfo
import com.interfun.buz.im.entity.IMType
import com.interfun.buz.im.message.RealTimeCallInviteMsg
import com.lizhi.im5.sdk.message.IMessage

/**
 * Author: ChenYouSheng
 * Date: 2025/3/12
 * Email: <EMAIL>
 * Desc:
 */
class RealTimeCallInviteMsgCreator(override val type: Int = IMType.TYPE_REAL_TIME_CALL) :
    MessagePreviewCreator<RealTimeCallInviteMsg>() {

    override fun createMsgPreview(
        conv: Conversation,
        liveInfo: PreviewLiveInfo,
        msg: IMessage,
        content: RealTimeCallInviteMsg,
        baseMsgInfo: HomeMsgPreviewModel.BaseMsgInfo
    ): HomeMsgPreviewModel.IMsgPreview {
        return HomeMsgPreviewModel.RealTimeCallInvitePreview(
            text = content.title,
            baseMsgInfo = baseMsgInfo
        )
    }
}