package com.interfun.buz.home.data.usecase

import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.chat.wt.entity.IMPushMessage
import com.interfun.buz.chat.wt.manager.MessageState.PLAYING
import com.interfun.buz.chat.wt.manager.WTMessageManager
import com.interfun.buz.home.manager.HomePreviewPlayer
import com.interfun.buz.home.view.viewmodel.WTViewModelNew.Companion.TAG
import com.interfun.buz.im.util.toMsgIdentity
import com.lizhi.im5.sdk.message.IMessage
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import javax.inject.Inject

class HomePlayingMsgUseCase @Inject constructor() {
    private val homePreviewPlayer = HomePreviewPlayer

    operator fun invoke(): Flow<IMessage?> = combine(
        WTMessageManager.playingMsgFlow,
        homePreviewPlayer.playingPreview
    ) { globalPlayingMsg, playingPreviewMsg ->
        val result = globalPlayingMsg ?: playingPreviewMsg
        logInfo(TAG, "playingMsg Change:${result?.toMsgIdentity()}")
        result
    }
}