package com.interfun.buz.home.data.preview

import com.interfun.buz.chat.wt.entity.HomeMsgPreviewModel
import com.interfun.buz.chat.wt.entity.HomeMsgPreviewModel.ImagePreview
import com.interfun.buz.domain.im.social.entity.Conversation
import com.interfun.buz.home.data.entity.PreviewLiveInfo
import com.interfun.buz.im.entity.IMType
import com.interfun.buz.im.message.BuzImageMessage
import com.lizhi.im5.sdk.message.IMessage

class ImagePreviewCreator : MessagePreviewCreator<BuzImageMessage>() {

    override val type: Int = IMType.TYPE_IMAGE_MSG

    override fun createMsgPreview(
        conv: Conversation,
        liveInfo: PreviewLiveInfo,
        msg: IMessage,
        content: BuzImageMessage,
        baseMsgInfo: HomeMsgPreviewModel.BaseMsgInfo
    ): HomeMsgPreviewModel.IMsgPreview {
        val url = content.thumbUrl ?: if (content.localPath.isNullOrEmpty()) {
            content.remoteUrl
        } else {
            content.localPath
        }
        return ImagePreview(url, baseMsgInfo)
    }
}