package com.interfun.buz.home.entity

import com.lizhi.im5.sdk.conversation.IM5ConversationType

data class ScrollEvent(
    val targetId: Long,
    var isConsumed: Boolean = false,
    val canPending: Boolean = false,
    //如果是pending的话根据这个参数设定超时
    val expiredUptimeMillsWhenPending: Long? = null,
    val afterAction: Action? = null
) {
    sealed interface Action{
        data class OpenVEPanel(val targetId: Long,val convType: IM5ConversationType) : Action
    }
}

class HomeUIState(
    val homeList: List<HomeCombinedItem>?,
    val scrollEvent: ScrollEvent?,
)