package com.interfun.buz.home.data.preview

import com.interfun.buz.chat.wt.entity.HomeMsgPreviewModel.IMsgPreview
import com.interfun.buz.domain.im.social.entity.Conversation
import com.interfun.buz.home.data.entity.PreviewLiveInfo
import com.lizhi.im5.sdk.message.IMessage

interface MsgPreviewContentCreator {
    val type: Int
    fun create(conversation: Conversation, liveInfo: PreviewLiveInfo, msg: IMessage): IMsgPreview?
}