package com.interfun.buz.home.data.usecase

import androidx.recyclerview.widget.RecyclerView
import com.interfun.buz.chat.ai.topic.popup.TopicItem
import com.interfun.buz.chat.wt.entity.IMPushMessage
import com.interfun.buz.chat.wt.manager.MessageState.PLAYING
import com.interfun.buz.chat.wt.manager.WTMessageManager
import com.interfun.buz.common.ktx.getBooleanDefault
import com.interfun.buz.common.widget.translator.TranslatorLangBar.LanguageBarStatus.*
import com.interfun.buz.domain.im.social.entity.Conversation
import com.interfun.buz.domain.im.social.entity.UserConversation
import com.interfun.buz.domain.im.social.entity.isRobot
import com.interfun.buz.domain.record.helper.RecordStatusHelper
import com.interfun.buz.home.entity.HomeRobotExtra
import com.interfun.buz.home.entity.HomeRobotState
import com.interfun.buz.home.entity.HomeRobotTopicState
import com.interfun.buz.home.entity.TranslatorLangBarStatus
import com.interfun.buz.im.entity.IMMessageContentExtra
import com.interfun.buz.im.entity.IMType
import com.interfun.buz.im.ktx.getConvTargetIdLong
import com.interfun.buz.im.ktx.isSendType
import com.interfun.buz.social.db.entity.BotInfo
import com.interfun.buz.social.repo.BotRepository
import com.lizhi.im5.sdk.message.IMessage
import com.lizhi.im5.sdk.message.MessageStatus
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.*
import javax.inject.Inject

class HomeRobotStateUseCase @Inject constructor(
    private val botRepository: BotRepository
) {

    private val globalPlayingTargetIdFlow = WTMessageManager.messageFlow.map { (msg, state) ->
        if (state == PLAYING) {
            (msg as? IMPushMessage)?.message?.getConvTargetIdLong()
        } else {
            null
        }
    }

    operator fun invoke(conv: Conversation?): Flow<HomeRobotState?> {
        if (conv == null) {
            return flowOf(null)
        }
        val isRobot = conv is UserConversation && conv.isRobot == true
        if (!isRobot) {
            return flowOf(null)
        }
        return botRepository.getBotInfoCacheFlow(conv.convId).flatMapLatest { botInfo ->
            val isTranslator = botInfo?.botExtraInfo?.botUIConfig?.showTranslation == true
            val showTopic = botInfo?.botExtraInfo?.botUIConfig?.showTopic == true
            val topicItems =
                botInfo?.botExtraInfo?.topics?.take(2)?.map { TopicItem(it.title, it.id) }
            val topicState = if (showTopic && topicItems?.isNotEmpty() == true) {
                HomeRobotTopicState(topicItems, botInfo.buzUser.userName ?: "")
            } else {
                null
            }
            if (!isTranslator) {
                combine(
                    isLoadingFlow(conv.lastMessage),
                    RecordStatusHelper.isRecordingFlow
                ) { isWaitingResponse, isRecording ->
                    if (isRecording) {
                        null
                    } else {
                        HomeRobotState(conv.convId, isWaitingResponse, topicState, null)
                    }
                }
                isLoadingFlow(conv.lastMessage).map {
                    HomeRobotState(
                        conv.convId,
                        it,
                        topicState,
                        null
                    )
                }
            } else {
                translatorStateFlow(conv.convId, botInfo, conv.lastMessage, topicState)
            }
        }.flowOn(Dispatchers.Default)
    }

    private fun translatorStateFlow(
        targetId: Long,
        botInfo: BotInfo?,
        lastMessage: IMessage?,
        topicState: HomeRobotTopicState?
    ): Flow<HomeRobotState?> = combine(
        botRepository.getBotSettingsCacheFlow(targetId),
        RecordStatusHelper.isRecordingFlow,
        globalPlayingTargetIdFlow,
        isLoadingFlow(lastMessage),
    ) { botSetting, isRecording, playingTargetId, isWaitingResponse ->
        val language = botInfo?.botExtraInfo?.getTranslatorLanguageOrDefault(botSetting)
            ?: return@combine null
        if (botSetting?.targetLanguage == null) {
            botSetting?.botUserId?.let { botId ->
                botRepository.updateLocalBotSettings(
                    botId,
                    targetLanguage = language.targetLanguage.code
                )
            }
        }
        val status = if (isRecording) {
            HIGHLIGHT_BOTH
        } else if (playingTargetId == targetId) {
            //playing
            val translatorReplyTargetLanguageCode =
                getTranslatorReplyTargetLanguage(lastMessage)
            if (language.sourceLanguage.code == language.targetLanguage.code) {
                HIGHLIGHT_TARGET
            } else if (translatorReplyTargetLanguageCode == language.sourceLanguage.code) {
                HIGHLIGHT_SOURCE
            } else {
                HIGHLIGHT_TARGET
            }
        } else if (isWaitingResponse) {
            LOADING
        } else {
            DEFAULT
        }
        val extra = HomeRobotExtra.Translator(
            TranslatorLangBarStatus(
                targetId,
                language,
                status
            )
        )
        return@combine HomeRobotState(targetId, isRecording, topicState, extra)
    }.flowOn(Dispatchers.Default)

    private fun isLoadingFlow(lastMessage: IMessage?) = flow<Boolean> {
        val isLoading = shouldShowLoadingAni(lastMessage)
        if (isLoading) {
            emit(true)
            delay(60_000L)
            emit(false)
        } else {
            emit(false)
        }
    }

    private fun shouldShowLoadingAni(lastMessage: IMessage?): Boolean {
        if (lastMessage == null) {
            return false
        }
        val isLastMsgIsSendType = lastMessage.isSendType
        val ifLastMsgFiled = lastMessage.status == MessageStatus.FAILED
        if (isLastMsgIsSendType &&
            ifLastMsgFiled.not() &&
            IMType.isSystemType(lastMessage.msgType).not()
        ) {
            val createTimeToNow = System.currentTimeMillis() - lastMessage.createTime
            if (createTimeToNow < 60 * 1000L) {
                // 消息创建时间距离现在小于60秒再检查content的extra是否有已回复标记, 如果没有回复,那么才需要插入waiting消息
                val isReplayed = IMMessageContentExtra.parseFromJson(lastMessage.content.extra).serverExtra?.isReply
                return isReplayed.getBooleanDefault().not()
            }
        }
        return false
    }

    private fun getTranslatorReplyTargetLanguage(lastMessage: IMessage?): String? {
        lastMessage ?: return null
        return IMMessageContentExtra.parseFromJson(lastMessage.content.extra).serverExtra?.targetLanguage
    }
}