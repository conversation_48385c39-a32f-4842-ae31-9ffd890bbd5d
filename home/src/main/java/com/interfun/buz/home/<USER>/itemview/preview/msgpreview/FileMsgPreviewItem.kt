package com.interfun.buz.home.view.itemview.preview.msgpreview

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import com.interfun.buz.base.ktx.*
import com.interfun.buz.chat.R
import com.interfun.buz.chat.wt.entity.HomeMsgPreviewModel
import com.interfun.buz.chat.wt.entity.HomeMsgPreviewModel.FilePreview
import com.interfun.buz.chat.wt.entity.PreviewPayloadType
import com.interfun.buz.chat.wt.entity.PreviewPayloadType.UPDATE_FILE_DOWNLOAD_PROGRESS
import com.interfun.buz.chat.wt.entity.PreviewPayloadType.UPDATE_FILE_UPLOAD_PROGRESS
import com.interfun.buz.common.utils.StorageUtil.toReadableFileSizeLeast1KB
import com.interfun.buz.common.widget.media.MediaUploadState
import com.interfun.buz.download.bean.DownloadStatus
import com.interfun.buz.download.bean.DownloadStatus.FAILURE
import com.interfun.buz.download.bean.DownloadStatus.PAUSED
import com.interfun.buz.download.bean.DownloadStatus.PENDING
import com.interfun.buz.download.bean.DownloadStatus.STARTED
import com.interfun.buz.home.databinding.ChatPreviewItemFileMessageBinding
import com.interfun.buz.home.view.utils.WTRvConstant
import com.interfun.buz.im.ktx.BuzSendingState
import com.interfun.buz.im.ktx.BuzSendingState.*
import com.lizhi.im5.sdk.message.MessageStatus.SUCCESS
import kotlinx.coroutines.CoroutineScope

class FileMsgPreviewItem @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null) :
    BaseMsgPreviewItem<FilePreview>(context, attrs) {

    companion object {
        const val TAG = "FileMsgPreviewItem"
    }

    private val binding by lazy {
        ChatPreviewItemFileMessageBinding.inflate(LayoutInflater.from(context), this)
    }
    private var currentMsgUploadState: MediaUploadState = MediaUploadState.NULL
    private var currentDownloadStatus: DownloadStatus = DownloadStatus.IDLE
    private var currentFilePreview: FilePreview? = null

    var onClickFileUploadButton: TwoParamCallback<MediaUploadState, FilePreview>? = null
    var onClickFileDownloadButton: OneParamCallback<FilePreview>? = null

    override fun setMessage(
        previewData: FilePreview,
        scope: CoroutineScope?,
        info: HomeMsgPreviewModel.ConvInfo
    ) {
        currentFilePreview = previewData
        
        binding.tvFileName.maxLines = WTRvConstant.instance.previewMsgItemTextLines - 1
        binding.tvFileName.text = previewData.fileName
        binding.tvFileSize.text = previewData.fileSize.toReadableFileSizeLeast1KB()
        binding.tvFilExt.text = previewData.fileExtension.uppercase()
        binding.ivFile.layoutHeight(WTRvConstant.instance.previewFileImageHeight)
        binding.uploadMediaButton.setProgressSize(
            indicatorSize = WTRvConstant.instance.previewFileProgressSize,
            trackWidth = WTRvConstant.instance.previewFileProgressTrackWidth,
            iconSize = WTRvConstant.instance.previewFileIconTextSize
        )
        binding.downloadFileButton.setProgressSize(
            indicatorSize = WTRvConstant.instance.previewFileProgressSize,
            trackWidth = WTRvConstant.instance.previewFileProgressTrackWidth,
            iconSize = WTRvConstant.instance.previewFileIconTextSize
        )

        if (previewData.needDownload) {
            updateDownloadState(
                status = previewData.downloadFileStatus,
                downloadProgress = previewData.downloadProgress
            )
        } else {
            updateFileUploadState(previewData.buzSendingState)
        }

        setupClickListeners()
        super.setMessage(previewData, scope, info)
    }

    override fun updateContent(previewData: FilePreview, types: List<PreviewPayloadType>) {
        currentFilePreview = previewData
        
        types.forEach { payloadType ->
            if (payloadType == UPDATE_FILE_UPLOAD_PROGRESS && !previewData.needDownload) {
                updateFileUploadState(previewData.buzSendingState)
            }
            if (payloadType == UPDATE_FILE_DOWNLOAD_PROGRESS && previewData.needDownload) {
                updateDownloadState(
                    status = previewData.downloadFileStatus,
                    downloadProgress = previewData.downloadProgress
                )
            }
        }
    }

    private fun setupClickListeners() {
        binding.ivFile.click {
            val preview = currentFilePreview ?: return@click
            val canOpen = if (preview.needDownload) {
                if (currentDownloadStatus == DownloadStatus.IDLE) {
                    toast(R.string.file_not_found.asString())
                    updateDownloadState(DownloadStatus.IDLE) // File requires download again, reset status to IDLE
                }
                currentDownloadStatus == DownloadStatus.SUCCESS
            } else {
                currentMsgUploadState == MediaUploadState.NULL || preview.baseMsgInfo.msgState == SUCCESS
            }
            if (!canOpen) {
                return@click
            }
            onPreviewClickListener?.invoke(preview)
        }
        
        binding.downloadFileButton.expandClickArea(25.dp, 25.dp)
        binding.downloadFileButton.click {
            val preview = currentFilePreview ?: return@click
            if (preview.needDownload) {
                onClickFileDownloadButton?.invoke(preview)
            }
        }
        
        binding.uploadMediaButton.click {
            val preview = currentFilePreview ?: return@click
            if (!preview.needDownload) {
                onClickFileUploadButton?.invoke(currentMsgUploadState, preview)
            }
        }
    }

    private fun updateFileUploadState(state: BuzSendingState) {
        when (state) {
            Canceled, UploadPaused -> {
                binding.uploadMediaButton.visible()
                binding.uploadMediaButton.updateState(MediaUploadState.PAUSE)
                currentMsgUploadState = MediaUploadState.PAUSE
            }

            is Uploading -> {
                binding.uploadMediaButton.visible()
                val progress = state.uploadInfo.progress
                currentMsgUploadState = if (progress == 0) {
                    binding.uploadMediaButton.updateState(MediaUploadState.LOADING)
                    MediaUploadState.LOADING
                } else {
                    binding.uploadMediaButton.updateState(MediaUploadState.PROGRESSING, progress)
                    MediaUploadState.PROGRESSING
                }
            }
            Compressing, Sending -> {
                binding.uploadMediaButton.updateState(MediaUploadState.LOADING)
                currentMsgUploadState = MediaUploadState.LOADING
            }

            Succeed, Failed -> {
                binding.uploadMediaButton.invisible()
                binding.uploadMediaButton.updateState(MediaUploadState.NULL)
                currentMsgUploadState = MediaUploadState.NULL
            }

            else -> {}
        }
    }

    private fun updateDownloadState(status: DownloadStatus, downloadProgress: Int = 0) {
        currentDownloadStatus = status
        binding.downloadFileButton.goneIf(status == DownloadStatus.SUCCESS)
        binding.tvFilExt.alpha = if (status == DownloadStatus.SUCCESS) 1f else 0.6f
        binding.ivFile.alpha = if (status == DownloadStatus.SUCCESS) 1f else 0.6f
        when (status) {
            DownloadStatus.SUCCESS -> {
                // File downloaded successfully - hide download button
                binding.downloadFileButton.hideDownloadButton()
            }
            PENDING, STARTED -> {
                // File is downloading - show downloading state with progress
                binding.downloadFileButton.showDownloading(downloadProgress)
            }
            PAUSED -> {
                // Download paused - show ready state (download icon without progress)
                binding.downloadFileButton.showDownloadReady()
            }
            else -> { // IDLE, CANCEL, FAILURE
                // Ready to download - show download icon without progress
                binding.downloadFileButton.showDownloadReady()
                if (status is FAILURE) {
                    logError(TAG, "Download failed: ${status.error}")
                }
            }
        }
    }
}