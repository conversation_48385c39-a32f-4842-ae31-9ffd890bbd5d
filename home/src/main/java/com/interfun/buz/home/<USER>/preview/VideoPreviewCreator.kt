package com.interfun.buz.home.data.preview

import com.interfun.buz.chat.wt.entity.HomeMsgPreviewModel
import com.interfun.buz.chat.wt.entity.HomeMsgPreviewModel.IMsgPreview
import com.interfun.buz.chat.wt.entity.HomeMsgPreviewModel.VideoPreview
import com.interfun.buz.domain.im.social.entity.Conversation
import com.interfun.buz.home.data.entity.PreviewLiveInfo
import com.interfun.buz.im.entity.IMType
import com.interfun.buz.im.ktx.coverThumbUrl
import com.interfun.buz.im.message.BuzVideoMsg
import com.lizhi.im5.sdk.message.IMessage

class VideoPreviewCreator : MessagePreviewCreator<BuzVideoMsg>() {
    override val type: Int = IMType.TYPE_VIDEO_MSG
    override fun createMsgPreview(
        conv: Conversation,
        liveInfo: PreviewLiveInfo,
        msg: IMessage,
        content: BuzVideoMsg,
        baseMsgInfo: HomeMsgPreviewModel.BaseMsgInfo
    ): IMsgPreview {
        return VideoPreview(content.coverThumbUrl() ?: "", content.duration, baseMsgInfo)
    }
}