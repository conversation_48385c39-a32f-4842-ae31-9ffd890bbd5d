package com.interfun.buz.home.data.main

import com.interfun.buz.domain.im.social.entity.Conversation
import com.interfun.buz.domain.im.social.entity.GroupConversation
import com.interfun.buz.domain.im.social.entity.UserConversation
import com.interfun.buz.domain.im.social.entity.isRobot
import com.interfun.buz.home.data.entity.MainLiveInfo
import com.interfun.buz.home.entity.HomeConversationItem

object MainItemFactory {

    private val robotConversationItemCreator = RobotConversationItemCreator()
    private val userConversationItemCreator = UserConversationItemCreator()
    private val groupConversationItemCreator = GroupConversationItemCreator()

    fun create(conversation: Conversation, mainLiveInfo: MainLiveInfo): HomeConversationItem {
        return when (conversation) {
            is UserConversation -> if (conversation.isRobot == true) {
                robotConversationItemCreator.create(conversation, mainLiveInfo)
            } else {
                userConversationItemCreator.create(conversation, mainLiveInfo)
            }

            is GroupConversation -> groupConversationItemCreator.create(
                conversation,
                mainLiveInfo
            )
        }
    }
}