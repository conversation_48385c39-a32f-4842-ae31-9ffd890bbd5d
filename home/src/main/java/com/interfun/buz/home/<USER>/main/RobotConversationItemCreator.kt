package com.interfun.buz.home.data.main

import com.interfun.buz.common.widget.portrait.group.UserPortrait
import com.interfun.buz.domain.im.social.entity.UserConversation
import com.interfun.buz.domain.im.social.entity.displayName
import com.interfun.buz.domain.im.social.entity.portrait
import com.interfun.buz.home.data.entity.MainLiveInfo
import com.interfun.buz.home.entity.HomeConversationItem
import com.interfun.buz.home.entity.HomeRobotItem
import com.interfun.buz.im.ktx.getConvTargetIdLong
import com.interfun.buz.onair.bean.isLivePlaceOpen
import com.lizhi.im5.sdk.conversation.IM5ConversationType

class RobotConversationItemCreator : MainItemCreator<UserConversation> {
    override fun create(
        conversation: UserConversation,
        mainLiveInfo: MainLiveInfo
    ): HomeConversationItem {
        val (playingMsg, playingPortrait) = mainLiveInfo.playingTargetInfo
        val isPlaying =
            playingMsg?.conversationType == IM5ConversationType.PRIVATE && playingMsg.getConvTargetIdLong() == conversation.convId
        val userComposite = conversation.userComposite
        val relationInfo = userComposite?.relationInfo
        val isLivePlaceOpen = mainLiveInfo.convChannelInfo[conversation.convId]?.isLivePlaceOpen() == true
        val botInfo = mainLiveInfo.robotInfo[conversation.convId]
        return HomeRobotItem(
            conversation.convId,
            conversation.displayName,
            conversation.portrait as UserPortrait,
            isRecordingTo = mainLiveInfo.recordingTarget?.targetId == conversation.convId,
            unReadCount = conversation.unReadCount,
            mentionMeCount = conversation.mentionMeCount,
            isMuteNotification = relationInfo?.isMuteNotification ?: false,
            isMuteMessages = relationInfo?.isMuteMessages ?: false,
            isPlaying = isPlaying,
            isOnLivePlace = isLivePlaceOpen,
            useRemotePortrait = botInfo?.botUIConfig?.useRemotePortrait == true
        )
    }

}