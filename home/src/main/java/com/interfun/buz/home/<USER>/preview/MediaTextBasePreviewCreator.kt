package com.interfun.buz.home.data.preview

import com.interfun.buz.chat.wt.entity.HomeMsgPreviewModel
import com.interfun.buz.domain.im.social.entity.Conversation
import com.interfun.buz.home.data.entity.PreviewLiveInfo
import com.interfun.buz.im.entity.IMType
import com.interfun.buz.im.message.MediaTextMsg
import com.lizhi.im5.sdk.message.IMessage

abstract class MediaTextBasePreviewCreator : com.interfun.buz.home.data.preview.MessagePreviewCreator<MediaTextMsg>() {
    override fun createMsgPreview(
        conv: Conversation,
        liveInfo: PreviewLiveInfo,
        msg: IMessage,
        content: MediaTextMsg,
        baseMsgInfo: HomeMsgPreviewModel.BaseMsgInfo
    ): HomeMsgPreviewModel.IMsgPreview {
        return HomeMsgPreviewModel.MediaTextPreview(
            text = if (content.title.isEmpty()) content.title else content.text,
            baseMsgInfo = baseMsgInfo
        )
    }
}

class MediaTextPreviewCreator() : com.interfun.buz.home.data.preview.MediaTextBasePreviewCreator() {
    override val type: Int = IMType.TYPE_MEDIA_TEXT
}

class MediaTextNewPreviewCreator() : com.interfun.buz.home.data.preview.MediaTextBasePreviewCreator() {
    override val type: Int = IMType.TYPE_MEDIA_TEXT_NEW
}