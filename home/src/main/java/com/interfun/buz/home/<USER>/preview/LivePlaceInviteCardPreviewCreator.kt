package com.interfun.buz.home.data.preview

import com.interfun.buz.chat.wt.entity.HomeMsgPreviewModel
import com.interfun.buz.domain.im.social.entity.Conversation
import com.interfun.buz.home.data.entity.PreviewLiveInfo
import com.interfun.buz.im.entity.IMType
import com.interfun.buz.im.message.BuzLivePlaceShareMessage
import com.lizhi.im5.sdk.message.IMessage

class LivePlaceInviteCardPreviewCreator : MessagePreviewCreator<BuzLivePlaceShareMessage>() {
    override val type: Int = IMType.TYPE_LIVE_PLACE_SHARE

    override fun createMsgPreview(
        conv: Conversation,
        liveInfo: PreviewLiveInfo,
        msg: IMessage,
        content: BuzLivePlaceShareMessage,
        baseMsgInfo: HomeMsgPreviewModel.BaseMsgInfo
    ): HomeMsgPreviewModel.IMsgPreview {
        return HomeMsgPreviewModel.LivePlaceInviteCardPreview(content.topic, baseMsgInfo)
    }

}