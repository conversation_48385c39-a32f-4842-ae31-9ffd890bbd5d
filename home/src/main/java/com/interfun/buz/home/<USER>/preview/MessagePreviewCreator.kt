package com.interfun.buz.home.data.preview

import com.interfun.buz.chat.wt.entity.HomeMsgPreviewModel
import com.interfun.buz.chat.wt.entity.HomeMsgPreviewModel.IMsgPreview
import com.interfun.buz.domain.im.social.entity.Conversation
import com.interfun.buz.home.data.entity.PreviewLiveInfo
import com.interfun.buz.im.ktx.getConvTargetIdLong
import com.lizhi.im5.sdk.message.IMessage

abstract class MessagePreviewCreator<T> : MsgPreviewContentCreator {

    @Suppress("UNCHECKED_CAST")
    override fun create(
        conversation: Conversation,
        liveInfo: PreviewLiveInfo,
        msg: IMessage
    ): IMsgPreview? {
        val content = msg.content as? T ?: return PreviewUtils.createUnSupportMsgPreview(msg)
        val baseMsgInfo: HomeMsgPreviewModel.BaseMsgInfo = PreviewUtils.createBaseMsgInfo(msg)
        return createMsgPreview(conversation,liveInfo, msg, content, baseMsgInfo)
    }

    abstract fun createMsgPreview(
        conv: Conversation,
        liveInfo: PreviewLiveInfo,
        msg: IMessage,
        content: T,
        baseMsgInfo: HomeMsgPreviewModel.BaseMsgInfo
    ): IMsgPreview?
}