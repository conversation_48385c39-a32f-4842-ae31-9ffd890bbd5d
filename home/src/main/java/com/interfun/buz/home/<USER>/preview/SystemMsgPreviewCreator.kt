package com.interfun.buz.home.data.preview

import com.interfun.buz.base.ktx.asString
import com.interfun.buz.base.ktx.logError
import com.interfun.buz.chat.R
import com.interfun.buz.chat.wt.entity.HomeMsgPreviewModel
import com.interfun.buz.chat.wt.entity.HomeMsgPreviewModel.IMsgPreview
import com.interfun.buz.chat.wt.entity.HomeMsgPreviewModel.SystemMsgPreview
import com.interfun.buz.common.ktx.getDisplayName
import com.interfun.buz.common.ktx.uid
import com.interfun.buz.common.manager.UserSessionManager
import com.interfun.buz.domain.im.social.entity.Conversation
import com.interfun.buz.home.data.entity.PreviewLiveInfo
import com.interfun.buz.im.entity.IMCommandSubBusinessType
import com.interfun.buz.im.entity.IMType
import com.interfun.buz.im.message.AiDescriptionInfoCommand
import com.interfun.buz.im.message.ClickableCenterNotifyCommand
import com.interfun.buz.im.message.CommandMsg
import com.interfun.buz.im.message.GroupInviteNotifyCommand
import com.interfun.buz.im.message.HasBeenInviteToGroupCommand
import com.interfun.buz.im.message.NotSupportCommand
import com.interfun.buz.im.message.OnAirCommand
import com.interfun.buz.im.message.OnLiveCommand
import com.interfun.buz.im.message.RefreshConvCommand
import com.interfun.buz.im.message.UpdateGroupNameNotifyCommand
import com.interfun.buz.im.message.VoiceCallCommand
import com.interfun.buz.im.message.decodeNormal
import com.lizhi.im5.sdk.message.IMessage
import com.yibasan.lizhifm.sdk.platformtools.ResUtil

class SystemMsgPreviewCreator : MessagePreviewCreator<CommandMsg>() {
    override val type: Int = IMType.TYPE_COMMAND

    override fun createMsgPreview(
        conv: Conversation,
        liveInfo: PreviewLiveInfo,
        msg: IMessage,
        content: CommandMsg,
        baseMsgInfo: HomeMsgPreviewModel.BaseMsgInfo
    ): IMsgPreview? {
        val baseCommand = content.decodeNormal()
        if (baseCommand == null) {
            return PreviewUtils.createUnSupportMsgPreview(msg)
        } else {
            val text = when (baseCommand) {
                is RefreshConvCommand -> {
                    return null
                }

                is GroupInviteNotifyCommand -> {
                    if (baseCommand.isInviteToGroupType || baseCommand.isNewInviteToGroupType) {
                         baseCommand.getTipMsg()
                    } else {
                        return PreviewUtils.createUnSupportMsgPreview(msg)
                    }
                }
                is OnLiveCommand ->baseCommand.obtainTip(msg)
                is UpdateGroupNameNotifyCommand -> baseCommand.content
                is HasBeenInviteToGroupCommand -> R.string.chat_welcome_to_new_group.asString()
                is ClickableCenterNotifyCommand -> baseCommand.digest
                is AiDescriptionInfoCommand -> baseCommand.tipMsg
                is OnAirCommand -> return createOnAirCommandPreview(conv,liveInfo,baseCommand, baseMsgInfo)
                is VoiceCallCommand -> return createVoiceCallCommandPreview(
                    conv,
                    liveInfo,
                    baseCommand,
                    baseMsgInfo
                )

                is NotSupportCommand -> return PreviewUtils.createUnSupportMsgPreview(msg)
            }
            return SystemMsgPreview(text = text, baseMsgInfo)
        }
    }

    private fun createVoiceCallCommandPreview(
        conv: Conversation,
        liveInfo: PreviewLiveInfo,
        command: VoiceCallCommand,
        baseMsgInfo: HomeMsgPreviewModel.BaseMsgInfo
    ): IMsgPreview {
        when (val uid = command.userId) {
            null -> {
                return SystemMsgPreview(command.rawTips, baseMsgInfo)
            }

            UserSessionManager.uid -> {
                val text = try {
                    String.format(command.rawTips, com.interfun.buz.common.R.string.you.asString())
                } catch (t: Throwable) {
                    logError(t)
                    command.rawTips
                }
                return SystemMsgPreview(text, baseMsgInfo)
            }

            else -> {
                val name = liveInfo.msgRelativeUser.get(uid)?.firstNickName ?: ""
                val text = try {
                    String.format(command.rawTips, name)
                } catch (t: Throwable) {
                    logError(t)
                    command.rawTips
                }
                return SystemMsgPreview(text, baseMsgInfo)
            }
        }
    }

    private fun createOnAirCommandPreview(
        conv: Conversation,
        liveInfo: PreviewLiveInfo,
        command: OnAirCommand,
        baseMsgInfo: HomeMsgPreviewModel.BaseMsgInfo
    ): IMsgPreview {
        val fixedStr = when (command.subBusinessType) {
            IMCommandSubBusinessType.TYPE_ON_AIR_START -> {
                when (val uid = command.getOnAirStartUserId()) {
                    null -> {
                        ResUtil.getString(
                            com.interfun.buz.common.R.string.air_somebody_started_onair,
                            ""
                        )
                    }

                    UserSessionManager.uid -> {
                        ResUtil.getString(
                            com.interfun.buz.common.R.string.air_somebody_started_onair,
                            com.interfun.buz.common.R.string.you.asString()
                        )
                    }

                    else -> {
                        val name = liveInfo.msgRelativeUser.get(uid)?.firstNickName ?: ""
                        ResUtil.getString(
                            com.interfun.buz.common.R.string.air_somebody_started_onair,
                            name
                        )
                    }
                }
            }

            IMCommandSubBusinessType.TYPE_ON_AIR_MISS -> {
                com.interfun.buz.common.R.string.air_miss_air_invited.asString()
            }

            IMCommandSubBusinessType.TYPE_ON_AIR_BUSY -> {
                com.interfun.buz.common.R.string.line_busy.asString()
            }

            IMCommandSubBusinessType.TYPE_ON_AIR_END -> {
                com.interfun.buz.common.R.string.air_air_end.asString()
            }

            else -> ""
        }
        return SystemMsgPreview(text = fixedStr, baseMsgInfo)
    }

}