package com.interfun.buz.home.data.usecase

import com.interfun.buz.chat.common.manager.SmartTransManager
import com.interfun.buz.chat.common.manager.TranslationMessageManager
import com.interfun.buz.im.IMAgent
import com.interfun.buz.im.ktx.isVoiceMsg
import com.lizhi.im5.sdk.message.IMessage
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.flow.*
import javax.inject.Inject

class LivePlayingMsgUseCase @Inject constructor(val homePlayingMsgUseCase: HomePlayingMsgUseCase) {
    private val refreshAsrAndTranslationFlow = merge(
        SmartTransManager.asrUpdateFlow,
        TranslationMessageManager.translateMessageFlow.mapNotNull { if (it?.msg?.isVoiceMsg == true) it.msg else null }
    )
    operator fun invoke(): Flow<IMessage?> = channelFlow {
        val flow = refreshAsrAndTranslationFlow.shareIn(this, SharingStarted.Lazily)
        homePlayingMsgUseCase().flatMapLatest { msg ->
            if (msg == null) return@flatMapLatest flowOf(null)
            flow.onSubscription {
                emit(msg)
            }.filter { it.conversationType == msg.conversationType && it.msgId == msg.msgId }
        }.buffer(capacity = 1, onBufferOverflow = BufferOverflow.DROP_OLDEST)
            .collect {
                if (it == null) {
                    send(null)
                } else {
                    send(IMAgent.getMessageSync(it.conversationType, it.msgId))
                }
            }
    }
}