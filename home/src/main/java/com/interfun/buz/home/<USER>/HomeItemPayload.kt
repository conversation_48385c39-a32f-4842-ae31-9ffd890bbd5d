package com.interfun.buz.home.entity

class HomeItemPayload(val types: List<HomeItemPayloadType>)

enum class HomeItemPayloadType {
    //conversation(base)
    UpdateName,
    UpdatePortrait,
    UpdateUnReadCount,
    UpdateMentionMeCount,
    UpdateIsMuteNotification,
    UpdateIsMuteMessages,
    UpdateIsPlaying,
    UpdateIsOnLivePlace,
    UpdateIsRecordingTo,
    UpdateSendMsgAnim,

    //user
    UpdateOnlineStatus,
    UpdateRelation,
    UpdateUserType,
    UpdateIsRequestingFriend,

    //group
    UpdateOnlineMembers,
    UpdateOnlineMembersCount,
    UpdateAddressedUserPortrait,
    UpdateUserStatus,
    UpdateCallInfo,
}