package com.interfun.buz.home.data.transform

import com.interfun.buz.chat.wt.manager.WTMessageManager
import com.interfun.buz.common.ktx.combine6
import com.interfun.buz.common.ktx.combine7
import com.interfun.buz.common.ktx.combine8
import com.interfun.buz.common.manager.ChannelStatusManager
import com.interfun.buz.common.widget.portrait.group.UserPortrait
import com.interfun.buz.domain.im.social.entity.Conversation
import com.interfun.buz.domain.im.social.entity.GroupConversation
import com.interfun.buz.domain.im.social.entity.UserConversation
import com.interfun.buz.domain.im.social.entity.isRobot
import com.interfun.buz.home.data.entity.MainLiveInfo
import com.interfun.buz.home.entity.HomeSelectedItem
import com.interfun.buz.im.ktx.isSend
import com.interfun.buz.im.ktx.isSendType
import com.interfun.buz.social.entity.SimpleBuzUser
import com.interfun.buz.social.repo.BotRepository
import com.interfun.buz.social.repo.GroupOnlineMembersRepository
import com.interfun.buz.social.repo.UserOnlineStatusRepository
import com.interfun.buz.social.repo.UserRepository
import com.lizhi.im5.sdk.message.IMessage
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.map
import javax.inject.Inject

class MainListCombinedTransform @Inject constructor(
    private val botRepository: BotRepository,
    private val userRepository: UserRepository,
    private val userOnlineStatusRepository: UserOnlineStatusRepository,
    private val groupOnlineMembersRepository: GroupOnlineMembersRepository,
) {
    fun transform(
        addingFriendSetFlow: MutableStateFlow<Set<Long>>,
        conversationList: List<Conversation>,
        recordingTargetFlow: Flow<HomeSelectedItem.Conversation?>,
        currentMentionedBotFlow: Flow<Pair<Long?, SimpleBuzUser?>>
    ): Flow<MainLiveInfo> {
        val groupIds =
            conversationList.mapNotNull { if (it is GroupConversation) it.convId else null }
        val userIds =
            conversationList.mapNotNull { if (it is UserConversation) it.convId else null }
        val botIds =
            conversationList.mapNotNull { if (it is UserConversation && it.isRobot == true) it.convId else null }
        val playingMsgWithPortraitFlow = WTMessageManager.playingMsgFlow.map { msg ->
            if (msg?.isSendType == true){
                return@map null to null
            }
            val userId = msg?.fromId?.toLongOrNull() ?: return@map msg to null
            val portraitUrl =
                userRepository.getUserFromCache(userId)?.portrait ?: msg.userInfo?.portraitURL
            msg to UserPortrait(userId, portraitUrl)
        }
        val botInfoFlow = botRepository.getBotExtraFlow(botIds)
        return combine8(
            recordingTargetFlow,
            playingMsgWithPortraitFlow,
            userOnlineStatusRepository.getStatusInfoFlow(userIds),
            groupOnlineMembersRepository.getOnlineGroupMembersFromCache(groupIds),
            currentMentionedBotFlow,
            ChannelStatusManager.convChannelInfoFlow,
            botInfoFlow,
            addingFriendSetFlow,
        ) { recordingTarget, playingMsgWithPortrait, userOnlineMap, groupOnlineMap, mentionedBot, channelStatusMap, botInfoMap,addingFriendSet ->
            MainLiveInfo(
                recordingTarget = recordingTarget,
                playingTargetInfo = playingMsgWithPortrait,
                userOnlineStatus = userOnlineMap,
                groupOnlineStatus = groupOnlineMap,
                mentionedBotInfo = mentionedBot,
                convChannelInfo = channelStatusMap,
                robotInfo = botInfoMap,
                addingFriendsSet = addingFriendSet,
            )
        }
    }
}