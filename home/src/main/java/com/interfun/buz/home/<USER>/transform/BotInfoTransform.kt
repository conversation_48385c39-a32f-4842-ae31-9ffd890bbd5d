package com.interfun.buz.home.data.transform

import com.interfun.buz.domain.im.social.entity.Conversation
import com.interfun.buz.domain.im.social.entity.isRobot
import com.interfun.buz.social.db.entity.BotExtra
import com.interfun.buz.social.repo.BotRepository
import kotlinx.coroutines.flow.Flow
import java.util.LinkedList
import javax.inject.Inject

class BotInfoTransform @Inject constructor(
    private val botRepository: BotRepository
) : ConversationTransform<Map<Long, BotExtra>> {
    override fun transform(originList: List<Conversation>): Flow<Map<Long, BotExtra>> {
        val list = LinkedList<Long>()
        originList.forEach { conv ->
            if (conv.isRobot == true) {
                list.add(conv.convId)
            }
        }
        return botRepository.getBotExtraFlow(list)
    }
}