package com.interfun.buz.home.data.transform

import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.chat.wt.entity.HomeMsgPreviewModel.ConversationPreview
import com.interfun.buz.chat.wt.entity.HomeMsgPreviewModel.FilePreview
import com.interfun.buz.common.bean.DataUpdate
import com.interfun.buz.common.ktx.isMe
import com.interfun.buz.domain.im.social.entity.Conversation
import com.interfun.buz.download.bean.DownloadStateInfo
import com.interfun.buz.download.bean.FileIdentity
import com.interfun.buz.home.view.viewmodel.InitialDownloadData
import com.interfun.buz.home.view.viewmodel.UpdateDownloadData
import com.interfun.buz.im.message.BuzFileMessage
import com.interfun.buz.im.repo.IMFileDownloadRepository
import kotlinx.coroutines.flow.*
import java.io.File
import javax.inject.Inject

class FileDownloadTransform @Inject constructor(
    private val imFileDownloadRepository: IMFileDownloadRepository
): ConversationTransform<Map<FileIdentity, DownloadStateInfo>> {

    private val TAG = "FileDownloadTransform"

    private fun getFileDownloadStatusAndProgressFlow(fileIdentitySet: Set<FileIdentity>): Flow<Map<FileIdentity, DownloadStateInfo>> =
        channelFlow {
            if (fileIdentitySet.isEmpty()) {
                send(emptyMap())
                return@channelFlow
            }
            val result = mutableMapOf<FileIdentity, DownloadStateInfo>()

            // Transform the repository flow to emit DataUpdate objects
            val dataUpdateFlow: Flow<DataUpdate<InitialDownloadData, UpdateDownloadData>> =
                imFileDownloadRepository.stateFlow
                    .filter { downloadStateInfo ->
                        val fileIdentity = FileIdentity.from(downloadStateInfo)
                        fileIdentitySet.any { (url, fileName) ->
                            url == fileIdentity.url && fileName == fileIdentity.fileName
                        }
                    }
                    .map { downloadStateInfo ->
                        // Convert updates to DataUpdate.Update
                        val fileIdentity = FileIdentity.from(downloadStateInfo)
                        DataUpdate.Update(fileIdentity to downloadStateInfo)
                    }

            dataUpdateFlow.shareIn(this, SharingStarted.Lazily).onSubscription {
                val initialStates =
                    ArrayList<Pair<FileIdentity, DownloadStateInfo>>(fileIdentitySet.size)
                fileIdentitySet.forEach { fileIdentity ->
                    val currentState = imFileDownloadRepository.getDownloadStateInfo(
                        fileIdentity.url,
                        fileIdentity.fileName,
                        checkDatabase = true
                    )
                    initialStates.add(fileIdentity to currentState)
                }
                emit(DataUpdate.Init(initialStates))
            }.collect { dataUpdate ->
                when (dataUpdate) {
                    is DataUpdate.Init -> {
                        dataUpdate.data.forEach { (fileIdentity, downloadState) ->
                            result[fileIdentity] = downloadState
                        }
                        send(result.toMap())
                    }

                    is DataUpdate.Update -> {
                        val (fileIdentity, downloadState) = dataUpdate.data
                        result[fileIdentity] = downloadState
                        send(result.toMap())
                    }
                }
            }
        }

    override fun transform(originList: List<Conversation>): Flow<Map<FileIdentity, DownloadStateInfo>> {
        val downloadingFileInfos = hashSetOf<FileIdentity>()
        originList.forEach { conv ->
            val lastMsg = conv.lastMessage
            val lastMsgContent = lastMsg?.content
            if (lastMsgContent is BuzFileMessage) {
                val needDownload = !lastMsg.fromId.isMe() || lastMsgContent.localPath.isNullOrEmpty() || File(lastMsgContent.localPath).exists().not()
                if (!needDownload) {
                    return@forEach
                }
                val fileMsg = lastMsg.content as BuzFileMessage
                val fileIdentity = FileIdentity(fileMsg.remoteUrl ?: "", fileMsg.name ?: "")
                downloadingFileInfos.add(fileIdentity)
            }
        }
        return getFileDownloadStatusAndProgressFlow(downloadingFileInfos).debounce(200L)
    }
}