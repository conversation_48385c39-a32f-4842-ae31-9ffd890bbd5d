package com.interfun.buz.home.entity

import com.interfun.buz.chat.wt.entity.HomeMsgPreviewModel
import com.interfun.buz.common.bean.user.BuzUserRelationValue
import com.interfun.buz.common.widget.portrait.group.BuzPortrait
import com.interfun.buz.common.widget.portrait.group.GroupPortrait
import com.interfun.buz.common.widget.portrait.group.UserPortrait
import com.interfun.buz.onair.bean.ConvChannelInfo
import com.interfun.buz.social.db.entity.BuzUser.Companion.USER_TYPE_BUZ_OFFICIAL
import com.interfun.buz.social.db.entity.BuzUser.Companion.USER_TYPE_BUZ_RESEARCH
import com.interfun.buz.social.entity.UserOnlineStatus
import com.interfun.buz.social.entity.UserStateInfo

data class HomeCombinedItem(
    val mainItem: HomeItem,
    val preview: HomeMsgPreviewModel
)

sealed interface HomeItem {
    fun diffPayload(oldItem: HomeItem, newItem: HomeItem): HomeItemPayload?
}

sealed interface HomeConversationItem : HomeItem {
    val targetId: Long
    val name: String
    val portrait: BuzPortrait
    val isRecordingTo: Boolean
    val unReadCount: Int
    val mentionMeCount: Int
    val isMuteNotification: Boolean
    val isMuteMessages: Boolean
    val isPlaying: Boolean
    val isOnLivePlace: Boolean

    override fun diffPayload(oldItem: HomeItem, newItem: HomeItem): HomeItemPayload? {
        if (oldItem !is HomeConversationItem || newItem !is HomeConversationItem) {
            return null
        }
        if (oldItem.targetId != newItem.targetId) {
            return null
        }
        val result = mutableListOf<HomeItemPayloadType>()
        if (oldItem.name != newItem.name) {
            result.add(HomeItemPayloadType.UpdateName)
        }
        if (oldItem.portrait != newItem.portrait) {
            result.add(HomeItemPayloadType.UpdatePortrait)
        }
        if (oldItem.isRecordingTo != newItem.isRecordingTo) {
            result.add(HomeItemPayloadType.UpdateIsRecordingTo)
        }
        if (oldItem.unReadCount != newItem.unReadCount) {
            result.add(HomeItemPayloadType.UpdateUnReadCount)
        }
        if (oldItem.mentionMeCount != newItem.mentionMeCount) {
            result.add(HomeItemPayloadType.UpdateMentionMeCount)
        }
        if (oldItem.isMuteNotification != newItem.isMuteNotification) {
            result.add(HomeItemPayloadType.UpdateIsMuteNotification)
        }
        if (oldItem.isMuteMessages != newItem.isMuteMessages) {
            result.add(HomeItemPayloadType.UpdateIsMuteMessages)
        }
        if (oldItem.isPlaying != newItem.isPlaying) {
            result.add(HomeItemPayloadType.UpdateIsPlaying)
        }
        if (oldItem.isOnLivePlace != newItem.isOnLivePlace) {
            result.add(HomeItemPayloadType.UpdateIsOnLivePlace)
        }
        return if (result.isEmpty()) {
            null
        } else {
            HomeItemPayload(result)
        }
    }
}

data class HomeUserItem(
    override val targetId: Long,
    override val name: String,
    override val portrait: UserPortrait,
    override val isRecordingTo: Boolean,
    override val unReadCount: Int,
    override val mentionMeCount: Int,
    override val isMuteNotification: Boolean,
    override val isMuteMessages: Boolean,
    override val isPlaying: Boolean,
    override val isOnLivePlace: Boolean,
    val onlineStatus: UserOnlineStatus,
    val userStateInfo: UserStateInfo,
    val relation: BuzUserRelationValue?,
    val userType: Int?,
    val isRequestingFriend: Boolean,
) : HomeConversationItem {
    val isOfficial get() = userType == USER_TYPE_BUZ_RESEARCH || userType == USER_TYPE_BUZ_OFFICIAL

    override fun diffPayload(oldItem: HomeItem, newItem: HomeItem): HomeItemPayload? {
        val superPayload = super.diffPayload(oldItem, newItem)
        if (oldItem !is HomeUserItem || newItem !is HomeUserItem) {
            return superPayload
        }
        val result = superPayload?.types?.toMutableList() ?: mutableListOf()
        if (oldItem.onlineStatus != newItem.onlineStatus) {
            result.add(HomeItemPayloadType.UpdateOnlineStatus)
        }
        if (oldItem.relation != newItem.relation) {
            result.add(HomeItemPayloadType.UpdateRelation)
        }
        if (oldItem.userType != newItem.userType) {
            result.add(HomeItemPayloadType.UpdateUserType)
        }
        if (oldItem.isRequestingFriend != newItem.isRequestingFriend) {
            result.add(HomeItemPayloadType.UpdateIsRequestingFriend)
        }
        return if (result.isEmpty()) {
            null
        } else {
            HomeItemPayload(result)
        }
    }
}

data class HomeRobotItem(
    override val targetId: Long,
    override val name: String,
    override val portrait: UserPortrait,
    override val isRecordingTo: Boolean,
    override val unReadCount: Int,
    override val mentionMeCount: Int,
    override val isMuteNotification: Boolean,
    override val isMuteMessages: Boolean,
    override val isPlaying: Boolean,
    override val isOnLivePlace: Boolean,
    val useRemotePortrait: Boolean
) : HomeConversationItem {
    override fun diffPayload(oldItem: HomeItem, newItem: HomeItem): HomeItemPayload? {
        val superPayload = super.diffPayload(oldItem, newItem)
        if (oldItem !is HomeRobotItem || newItem !is HomeRobotItem) {
            return superPayload
        }
        val result = superPayload?.types?.toMutableList() ?: mutableListOf()
        if (oldItem.useRemotePortrait != newItem.useRemotePortrait) {
            result.add(HomeItemPayloadType.UpdatePortrait)
        }
        return if (result.isEmpty()) {
            null
        } else {
            HomeItemPayload(result)
        }
    }
}

data class HomeItemCallInfo(
    val convChannelInfo: ConvChannelInfo,
    val isJoining: Boolean
)

data class HomeGroupItem(
    override val targetId: Long,
    override val name: String,
    override val portrait: GroupPortrait,
    override val isRecordingTo: Boolean,
    override val unReadCount: Int,
    override val mentionMeCount: Int,
    override val isMuteNotification: Boolean,
    override val isMuteMessages: Boolean,
    override val isPlaying: Boolean,
    override val isOnLivePlace: Boolean,
    val playingUserPortrait: UserPortrait?,
    val isBigGroup: Boolean,
    val onlineMembers: List<String>?,
    val onlineMembersCount: Int?,
    val addressedUserPortrait: UserPortrait?,
    val userStatus: Int?,
    val callInfo: HomeItemCallInfo?
) : HomeConversationItem {

    override fun diffPayload(oldItem: HomeItem, newItem: HomeItem): HomeItemPayload? {
        val superPayload = super.diffPayload(oldItem, newItem)
        if (oldItem !is HomeGroupItem || newItem !is HomeGroupItem) {
            return superPayload
        }
        val result = superPayload?.types?.toMutableList() ?: mutableListOf()
        if (oldItem.onlineMembers != newItem.onlineMembers) {
            result.add(HomeItemPayloadType.UpdateOnlineMembers)
        }
        if (oldItem.onlineMembersCount != newItem.onlineMembersCount) {
            result.add(HomeItemPayloadType.UpdateOnlineMembersCount)
        }
        if (oldItem.addressedUserPortrait != newItem.addressedUserPortrait) {
            result.add(HomeItemPayloadType.UpdateAddressedUserPortrait)
        }
        if (oldItem.userStatus != newItem.userStatus) {
            result.add(HomeItemPayloadType.UpdateUserStatus)
        }
        if (oldItem.callInfo != newItem.callInfo) {
            result.add(HomeItemPayloadType.UpdateCallInfo)
        }
        return if (result.isEmpty()) {
            null
        } else {
            HomeItemPayload(result)
        }
    }
}

data object HomeAddBtnItem : HomeItem {
    override fun diffPayload(oldItem: HomeItem, newItem: HomeItem): HomeItemPayload? {
        return null
    }
}

fun HomeItem.isSameTarget(other : HomeItem): Boolean{
    return when (this){
        is HomeUserItem -> other is HomeUserItem && this.targetId == other.targetId
        is HomeGroupItem -> other is HomeGroupItem && this.targetId == other.targetId
        is HomeRobotItem -> other is HomeRobotItem && this.targetId == other.targetId
        HomeAddBtnItem -> other is HomeAddBtnItem
    }
}