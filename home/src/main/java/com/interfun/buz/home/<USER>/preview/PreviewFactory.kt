package com.interfun.buz.home.data.preview

import com.interfun.buz.assertutil.buzAssertNotMain
import com.interfun.buz.chat.wt.entity.HomeMsgPreviewModel.*
import com.interfun.buz.domain.im.social.entity.Conversation
import com.interfun.buz.domain.im.social.entity.isGroup
import com.interfun.buz.home.data.entity.PreviewLiveInfo
import com.interfun.buz.im.ktx.getConvTargetIdLong
import com.lizhi.im5.sdk.conversation.IM5Conversation
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import com.lizhi.im5.sdk.message.IMessage
import okhttp3.internal.toLongOrDefault

object PreviewFactory {
    private val map = HashMap<Int, com.interfun.buz.home.data.preview.MsgPreviewContentCreator>()

    init {
        register(TextPreviewCreator())
        register(VoicePreviewCreator())
        register(ImageEmojiPreviewCreator())
        register(ImagePreviewCreator())
        register(LocationPreviewCreator())
        register(com.interfun.buz.home.data.preview.MediaTextPreviewCreator())
        register(com.interfun.buz.home.data.preview.MediaTextNewPreviewCreator())
        register(RecallMsgCreator())
        register(SystemMsgPreviewCreator())
        register(UnicodeEmojiPreviewCreator())
        register(VideoPreviewCreator())
        register(WTVoiceMsgPreviewCreator())
        register(VoiceTextPreviewCreator())
        register(VoiceTextNewPreviewCreator())
        register(VoiceGifPreviewCreator())
        register(LivePlaceInviteCardPreviewCreator())
        register(RealTimeCallInviteMsgCreator())
        register(FilePreviewCreator())
        register(ShareContactMsgPreviewCreator())
    }

    private fun register(previewCreator: com.interfun.buz.home.data.preview.MsgPreviewContentCreator) {
        map[previewCreator.type] = previewCreator
    }

    private fun getCreator(type: Int): com.interfun.buz.home.data.preview.MsgPreviewContentCreator? {
        return map[type]
    }


    fun create(
        conversation: Conversation,
        liveInfo: PreviewLiveInfo
    ): ConversationPreview {
        val targetInfo = ConvInfo(
            isGroup = conversation.isGroup,
            convTargetId = conversation.convId,
            unreadCount = conversation.unReadCount,
            mentionMeCount = conversation.mentionMeCount
        )
        val lastMessage = conversation.lastMessage
        //优先展示播放的消息
        val currentConvPlayingMsg =
            if (conversation.convId == liveInfo.playingMsg?.getConvTargetIdLong()) liveInfo.playingMsg else null
        val msg = currentConvPlayingMsg ?: lastMessage
        val lastMsgPreview =
            if (msg == null) null else createMsgPreviewContent(conversation, liveInfo, msg)
        return ConversationPreview(targetInfo, lastMsgPreview)
    }

    fun createMsgPreviewContent(
        conversation: Conversation,
        liveInfo: PreviewLiveInfo,
        msg: IMessage
    ): IMsgPreview? {
        buzAssertNotMain()
        val creator = getCreator(msg.msgType) ?: return PreviewUtils.createUnSupportMsgPreview(msg)
//        if (creator == null && BuildConfig.DEBUG) {
//            throw IllegalStateException("Do you forget to register a PreviewCreator for message type ${msg.msgType}?")
//        }
        return creator.create(conversation, liveInfo, msg)
    }
}