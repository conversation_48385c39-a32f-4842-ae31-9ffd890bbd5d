package com.interfun.buz.home.view.block

import android.os.Build
import android.provider.Settings
import androidx.fragment.app.viewModels
import com.interfun.buz.base.ktx.*
import com.interfun.buz.base.ktx.activityViewModels
import com.interfun.buz.base.ktx.appContext
import com.interfun.buz.base.ktx.asString
import com.interfun.buz.base.ktx.collect
import com.interfun.buz.base.ktx.isNotNull
import com.interfun.buz.base.ktx.launchIO
import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.base.ktx.registerInterfaceBridge
import com.interfun.buz.base.ktx.topActivity
import com.interfun.buz.base.ktx.unregisterInterfaceBridge
import com.interfun.buz.base.ktx.viewLifecycleScope
import com.interfun.buz.base.ktx.withMainContext
import com.interfun.buz.base.utils.BusUtil
import com.interfun.buz.base.utils.FloatPermissionUtils
import com.interfun.buz.chat.common.view.activity.ChatHomeActivity
import com.interfun.buz.chat.common.view.block.guidance.AutoPlayTooltip
import com.interfun.buz.chat.privy.view.activity.PrivateChatActivity
import com.interfun.buz.chat.wt.entity.IMPushMessage
import com.interfun.buz.chat.wt.manager.*
import com.interfun.buz.common.arouter.routerServices
import com.interfun.buz.common.base.binding.BaseBindingBlock
import com.interfun.buz.im.entity.IMType
import com.interfun.buz.common.eventbus.wt.WTGuidanceForceCloseGuideDialogEvent
import com.interfun.buz.common.eventbus.wt.WTGuidanceOverlayPermissionEvent
import com.interfun.buz.common.ktx.CODE_NET_ERROR
import com.interfun.buz.common.ktx.getBooleanDefault
import com.interfun.buz.common.ktx.isOfficialAccount
import com.interfun.buz.common.ktx.isSuccess
import com.interfun.buz.common.manager.MuteInfoManager
import com.interfun.buz.common.manager.chat.*
import com.interfun.buz.common.service.ChatService
import com.interfun.buz.common.utils.CommonTracker
import com.interfun.buz.common.widget.toast.BuzToast
import com.interfun.buz.home.R
import com.interfun.buz.home.databinding.ChatFragmentHomeNewBinding
import com.interfun.buz.home.entity.HomeUserItem
import com.interfun.buz.home.view.dialog.WTGuidanceDialog
import com.interfun.buz.home.view.fragment.ChatHomeFragmentNew
import com.interfun.buz.home.view.utils.FTUEHomeTracker
import com.interfun.buz.home.view.viewmodel.WTGuidanceModel
import com.interfun.buz.home.view.viewmodel.WTViewModelNew
import com.interfun.buz.im.entity.BuzNotifyType
import com.interfun.buz.im.IMAgent
import com.interfun.buz.im.entity.IMMessageContentExtra
import com.interfun.buz.im.entity.ServerExtra
import com.interfun.buz.im.ktx.isReceive
import com.interfun.buz.im.ktx.userId
import com.interfun.buz.social.db.entity.BuzUser
import com.interfun.buz.user.entity.QuiteModeFromType
import com.lizhi.im5.sdk.message.IMessage
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * <AUTHOR> Yu Feng
 * @date 2024/10/23
 * @desc 首页新手引导
 * 需求地址https://vocalbeats.sg.larksuite.com/docx/KkpMdIg0moY3Fcx3GCql8GTYgjh?preview_comment_id=7427848218001539104
 * 云端服务方案：https://vocalbeats.sg.larksuite.com/wiki/RjbxwvciRibXcXketYwlqR4cgJe
 */

class WTGuidanceBlock(
    val fragment: ChatHomeFragmentNew,
    binding: ChatFragmentHomeNewBinding
) : BaseBindingBlock<ChatFragmentHomeNewBinding>(binding) {
    val TAG = "WTGuidanceBlock"
    private val chatRouterService by lazy { routerServices<ChatService>().value }
    private val wtGuidanceModel by fragment.viewModels<WTGuidanceModel>()
    private val wtViewModel by fragment.viewModels<WTViewModelNew>()
//    private var guidanceStepOfficialVoice: IMessage? = null
    private var guidanceStepVoiceMsgOrVE: IMessage? = null
    private val GUIDANCE_STEP_TYPE_OFFICIAL_VOICE = 1 // 第二步：  2. 官号发送语音消息
    private val GUIDANCE_STEP_TYPE_OFFICIAL_VOICE_EMOJI = 2 // 第四步：  2. 官号发送VE
    private val GUIDANCE_STEP_TYPE_OFFICIAL_SUCCESS_TEXT = 3 // 正常结束和异常结束都会有官号引导文字消息，TYPE都为3
    private val GUIDANCE_STEP_TYPE_OFFICIAL_SUCCESS_MEDIA_TEXT = 4 // 正常结束有官号引导卡片消息，TYPE都为4
    private val GUIDANCE_STEP_TYPE_OFFICIAL_EXCEPTION_MEDIA_TEXT = 5 // 异常结束有官号引导卡片消息，TYPE都为5
    private val autoPlayTooltip by lazy {
        AutoPlayTooltip()
    }
    private var wtGuidanceDialog: WTGuidanceDialog? = null

    private var enterGuidanceType: GuideStepFromType = GuideStepFromType.FROM_DEFAULT

    override fun initData() {
        super.initData()

        fragment.bindingTop.autoPlayToggleView.post {
            AutoPlayTooltip.initAnchorViewParams(fragment.bindingTop.autoPlayToggleView)
        }

        WTGuidanceManager.guideStepStateFlow.collect(fragment.viewLifecycleOwner)  { step ->
            logDebug(TAG, "guideStepStateFlow show dialog ${step?.stepType?.name}")
            step?.run {
                when {
                    isStepDialog() -> {
                        WTGuidanceManager.setStartRecordInGuidancePTT(false)
                        wtGuidanceModel.emitShowPressRecordTipsState(false)
                        showGuidanceDialog()
                        enterGuidanceType = step.from
                        wtGuidanceModel.launchIO {
                            reportEnterGuidanceType()
                            if (step.from == GuideStepFromType.FROM_CHAT_HISTORY_CLICK_REVISIT_ONBOARDING_BTN) {
                                reportRevisitOnboardingInFTUE()
                            }
                        }
                    }
                    isStepAutoPlay() -> {
                        if (null == fragment.activity) return@collect
                        autoPlayTooltip.showToolTipsInHomePage(
                            fragment.requireActivity(),
                            anchorView = fragment.bindingTop.autoPlayToggleView,
                            parent = fragment.binding.root,
                            fragment.bindingTop.viewQuietClickArea
                        )
                    }
                    isStepReportWillComplete() -> {
                        wtGuidanceModel.reportRequestGuidanceComplete()
                    }
                }
            }
        }


        WTMessageManager.messageFlow.collect(fragment.viewLifecycleOwner) { (msg, state) ->
            logDebug(TAG, "messageFlow: ${msg}, state: $state")
            if (WTGuidanceManager.isStepPTT()) {
                if (msg is IMPushMessage) {
                    if (state == MessageState.PLAYING) {
                        guidanceStepVoiceMsgOrVE = msg.message
                    }
                } else {
                    if (guidanceStepVoiceMsgOrVE != null && state == MessageState.IDLE)  {
                        guidanceStepVoiceMsgOrVE = null
                        gotoStepAutoPlay(from = "messageFlow")
                        wtGuidanceModel.emitShowPressRecordTipsState(true)
                    }
                }
            }
        }


        WTMessageManager.quietModeReceiveMsg.collect(fragment.viewLifecycleOwner) { (type, msg) ->
            gotoStepAutoPlay(from = "quietModeReceiveMsg")
            wtGuidanceModel.emitShowPressRecordTipsState(true)
        }

        WTMessageManager.muteMessageReceiveMsg.collect(fragment.viewLifecycleOwner) { (type, msg) ->
            gotoStepAutoPlay(from = "muteMessageReceiveMsg")
            wtGuidanceModel.emitShowPressRecordTipsState(true)
        }

        WTQuietModeManager.quietModeFlowFrom.collect(fragment.viewLifecycleOwner) { (quietMode, from) ->
            if (topActivity !is ChatHomeActivity) return@collect
            // 显示Autoplay tips 时，切换首页静音模式也等于点击“got it”
            if (from == QuiteModeFromType.Manual && WTGuidanceManager.isStepAutoPlay()) {
               if (autoPlayTooltip.isShowing()) {
                   autoPlayTooltip.dismiss()
                   val businessType = if (quietMode) "turn_on" else "turn_off"
                   CommonTracker.onClickAC2024102102(businessType = businessType)
               }
               WTGuidanceManager.gotoStepReportWillComplete()
           }
        }


        WTLeaveMsgPlayerManager.playStateFlow.collect(fragment.viewLifecycleOwner) { (playState, msg) ->
            if (WTGuidanceManager.isStepPTT()
                && topActivity is PrivateChatActivity
                && msg?.userId.isOfficialAccount()
                && msg?.isReceive == true
            ) {
                if (msg?.msgType == IMType.TYPE_VOICE_EMOJI && playState == LMPlayerState.PLAY_WT_MSG) {
                    guidanceStepVoiceMsgOrVE = msg
                } else {
                    if (guidanceStepVoiceMsgOrVE != null && playState == LMPlayerState.IDLE) {
                        gotoStepAutoPlay(from = "WTLeaveMsgPlayerManager.playStateFlow")
                        wtGuidanceModel.emitShowPressRecordTipsState(true)
                    }
                }
            }
        }
        IMAgent.msgReceivedFlow.collect(fragment) { (notifyType,messages) ->
            if (notifyType == BuzNotifyType.NewMsg) {
                messages.forEach { msg ->
                    wtGuidanceModel.launchIO {
                        val isOfficialAccount = msg.userId.isOfficialAccount()
                        if (WTGuidanceManager.isStepPTT()
                            && topActivity is PrivateChatActivity
                            && isOfficialAccount
                            && msg.isReceive
                            && msg.msgType == IMType.TYPE_VOICE_EMOJI
                            && (WTQuietModeManager.isQuietModeEnable || MuteInfoManager.isMessageMuted(msg, TAG))
                        ) {
                            gotoStepAutoPlay(from = "addMessageNotifyObserver, receive ChatHomeActivity")
                            wtGuidanceModel.emitShowPressRecordTipsState(true)
                        }
                        // 引导模式且为官号消息，才会上报
                        if ((WTGuidanceManager.isInGuideMode() || WTGuidanceManager.isStepCompleteSuccess()) && isOfficialAccount) {
                            reportOfficialAccountReceiveMsgInGuidanceMode(msg)
                            reportReceiveOnboardingMessage(msg)
                        }
                    }
                }
            }
        }

        BusUtil.observe<WTGuidanceOverlayPermissionEvent>(fragment.viewLifecycleOwner) {
            fragment.activity?.let {
                if (it.isDestroyed || it.isFinishing) {
                    return@let
                }
                wtGuidanceModel.launchIO {
                    delay(100)
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                        if (!Settings.canDrawOverlays(it)) {
                            FloatPermissionUtils.requestPermission(it)
                        } else {
                            BuzToast.showToast(appContext, R.string.has_setup_complete.asString())
                        }
                    } else {
                        BuzToast.showToast(appContext, R.string.system_version_not_support.asString())
                    }
                }
            }
        }

        BusUtil.observe<WTGuidanceForceCloseGuideDialogEvent>(fragment.viewLifecycleOwner) {
            wtGuidanceDialog?.apply {
                if (isDialogShowing()) {
                    dismissAllowingStateLoss()
                    wtGuidanceModel.reportRequestGuidanceCompleteException("WTGuidanceForceCloseGuideDialogEvent")
                }
            }
        }

        wtGuidanceModel.startJudgeGuidanceModel()
        WTGuidanceDialog.getFragmentByTag(fragment.childFragmentManager)?.apply {
            logInfo(TAG, "Restored WTGuidanceDialog onGuideDialogDismiss")
            setOkCallBack {
                onGuideDialogDismiss(this)
            }
        }
    }


    override fun onResume() {
        super.onResume()
        logDebug(TAG, "onResume-")
    }

    private fun gotoStepAutoPlay(from: String) {
        logInfo(TAG, "gotoStepAutoPlay, hasStartRecordInGuidancePTT= ${WTGuidanceManager.getStartRecordInGuidancePTT()}, from: $from")
        if (WTGuidanceManager.isStepPTT() && WTGuidanceManager.getStartRecordInGuidancePTT()) {
            WTGuidanceManager.setStartRecordInGuidancePTT(false)
            WTGuidanceManager.gotoStepAutoPlay()
        }
    }

    // 收到的是否是云端引导消息
    private fun getGuidanceMsgServerExtra(msg: IMessage): ServerExtra? {
        logDebug(TAG, "getGuidanceMsgServerExtra msg.message.content?.extra: ${msg.content?.extra}")
        msg.content?.extra?.let { extra ->
            IMMessageContentExtra.parseFromJson(extra).serverExtra?.let { serverExtra ->
                return serverExtra
            }
        }
        return null
    }

    // 显示第一步引导弹窗
    private fun showGuidanceDialog() {
        val dialogOld = WTGuidanceDialog.getFragmentByTag(fragment.childFragmentManager)
        logDebug(TAG, "isFirstStepDialog show dialog ${dialogOld.isNotNull()}")
        if (dialogOld == null) {
            wtGuidanceDialog = WTGuidanceDialog.newInstance().apply {
                setOkCallBack {
                    onGuideDialogDismiss(this)
                }
                showDialog(fragment.childFragmentManager)
            }
        } else {
            wtGuidanceDialog = dialogOld
            dialogOld.apply {
                setOkCallBack {
                    onGuideDialogDismiss(this)
                }
                dialogOld.showDialog(fragment.childFragmentManager)
            }
        }

    }

    private fun onGuideDialogDismiss(dialog: WTGuidanceDialog) {
        logDebug(TAG, "onGuideDialogDismiss0")
        getOfficialAccountTargetId()?.let { targetId ->
            wtViewModel.scrollToTarget(targetId)
        }
        wtGuidanceModel.launchIO {
            wtGuidanceModel.reportRequestOfficialVoice().apply {
                when(code) {
                    CODE_NET_ERROR -> {
                        BuzToast.showToast(appContext, R.string.tips_network_error.asString())
                    }
                }
                if (!code.isSuccess) {
                    wtGuidanceModel.emitShowPressRecordTipsState(true)
                }
            }
            withMainContext {
                dialog.dismissAllowingStateLoss()
                wtGuidanceDialog = null
            }
        }
    }

    private fun getOfficialAccountTargetId(): Long? {
        val item = wtViewModel.homeList.value?.find {
            (it.mainItem as? HomeUserItem)?.userType == BuzUser.USER_TYPE_BUZ_OFFICIAL
        }
        if (item == null) {
            return null
        }
        return (item.mainItem as HomeUserItem).targetId
    }

    private fun reportRevisitOnboardingInFTUE() {
        wtViewModel.reportRevisitOnboardingInFTUE()
    }

    private fun reportEnterGuidanceType() {
        val source = getSourceFromEnterGuidanceType()
        FTUEHomeTracker.onResultRB2024102102(source = source)
    }

    // 引导模式且为官号消息，才会上报
    private fun reportOfficialAccountReceiveMsgInGuidanceMode(msg: IMessage) {
        val receiveMsgType = when (msg.msgType) {
            IMType.TYPE_TEXT_MSG -> {
                "text"
            }
            IMType.TYPE_VOICE_MSG -> {
                "voice_msg"
            }
            IMType.TYPE_VOICE_EMOJI -> {
                "voicemoji"
            }

            IMType.TYPE_MEDIA_TEXT_NEW,
            IMType.TYPE_MEDIA_TEXT -> {
                "media_text"
            }
            else -> {
                "other"
            }
        }
        val source = getSourceFromEnterGuidanceType()
        FTUEHomeTracker.onResultRB2024102103(source = source, receiveMsgType = receiveMsgType)
    }
    // 引导模式下收到官号发来的卡片消息
    private fun reportReceiveOnboardingMessage(msg: IMessage) {
        getGuidanceMsgServerExtra(msg)?.let { extra ->
            when (extra.guidanceStepType) {
                GUIDANCE_STEP_TYPE_OFFICIAL_SUCCESS_MEDIA_TEXT, -> {
                    wtViewModel.reportResultRB2024102106(businessType = 1)
                }

                GUIDANCE_STEP_TYPE_OFFICIAL_EXCEPTION_MEDIA_TEXT -> {
                    wtViewModel.reportResultRB2024102106(businessType = 0)
                }
            }

        }
    }

    private fun getSourceFromEnterGuidanceType(): String {
        return  when (enterGuidanceType) {
            GuideStepFromType.FROM_CHAT_HISTORY_CLICK_REVISIT_ONBOARDING_BTN -> {
                "OA_guidance"
            }
            else -> {
                "default"
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        WTGuidanceManager.clearGuideStepStateFlow("$TAG, onDestroy")
    }

}