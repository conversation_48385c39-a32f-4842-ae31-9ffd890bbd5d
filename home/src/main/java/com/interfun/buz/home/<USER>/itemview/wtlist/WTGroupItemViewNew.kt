package com.interfun.buz.home.view.itemview.wtlist

import android.view.View
import androidx.core.view.doOnPreDraw
import androidx.core.view.isVisible
import androidx.lifecycle.findViewTreeLifecycleOwner
import androidx.lifecycle.lifecycleScope
import androidx.transition.AutoTransition
import androidx.transition.TransitionManager
import com.interfun.buz.base.ktx.*
import com.interfun.buz.common.bean.voicecall.CallType
import com.interfun.buz.common.database.entity.chat.GroupUserStatus
import com.interfun.buz.common.ktx.getBooleanDefault
import com.interfun.buz.common.manager.ABTestManager
import com.interfun.buz.common.manager.voicecall.VoiceCallPortal
import com.interfun.buz.common.voicecall.CallEntryHelper
import com.interfun.buz.home.R
import com.interfun.buz.home.databinding.HomeItemWtGroupNewBinding
import com.interfun.buz.home.entity.HomeGroupItem
import com.interfun.buz.home.entity.HomeItemPayloadType
import com.interfun.buz.home.view.itemview.callback.WTGroupItemCallbackNew
import com.interfun.buz.onair.bean.isLivePlaceOpen
import com.interfun.buz.onair.bean.isVideoCallOpen
import com.interfun.buz.onair.bean.isVoiceCallOpen
import com.interfun.buz.voicecall.util.VoiceCallTracker
import kotlinx.coroutines.delay

/**
 * <AUTHOR>
 * @date 2024/10/20
 * @desc 部分通用逻辑已挪动到基类BaseWTItemView中
 * 后续维护请区分是通用业务还是专有业务（区分user\robot\group），尽量避免重复代码
 */
class WTGroupItemViewNew(override val callback: WTGroupItemCallbackNew<HomeGroupItem>) :
    BaseWTItemView<HomeItemWtGroupNewBinding,HomeGroupItem>(callback) {

    companion object {
        const val TAG = "WTGroupItemViewNew"
    }

    override fun onViewHolderCreated(holder: BindingViewHolder<HomeItemWtGroupNewBinding>) {
        super.onViewHolderCreated(holder)
        holder.onClick(this, holder.binding.llGroupOnline, vibrate = true) { binding, item, pos ->
            callback.onItemClick(item, pos)
        }

        holder.onClick(this, holder.binding.vJoinClickArea) { binding, item, pos ->
            // 模拟按钮按下效果
            binding.flGroupJoinRealTimeCall.isPressed = true
            binding.flGroupJoinRealTimeCall.findViewTreeLifecycleOwner()?.lifecycleScope?.launchMain {
                delay(100L)
                binding.flGroupJoinRealTimeCall.isPressed = false
            }
            binding.flGroupJoinRealTimeCall.performClick()
        }

        holder.onClick(this, holder.binding.flGroupJoinRealTimeCall) { binding, item, pos ->
            callback.onJoinRealTimeCall(holder.binding, item, pos, null)
        }
    }

    override fun onBindViewHolder(
        binding: HomeItemWtGroupNewBinding,
        item: HomeGroupItem,
        position: Int
    ) {
        super.onBindViewHolder(binding, item, position)
        updateBottomBarStatus(binding, item)
    }

    override fun handlePayloadType(
        binding: HomeItemWtGroupNewBinding,
        item: HomeGroupItem,
        type: HomeItemPayloadType
    ) {
        super.handlePayloadType(binding, item, type)
        when (type) {
            HomeItemPayloadType.UpdateOnlineMembers -> {
                updateBottomBarStatus(binding, item)
            }

            HomeItemPayloadType.UpdateOnlineMembersCount -> {
                updateBottomBarStatus(binding, item)
            }

            HomeItemPayloadType.UpdateCallInfo -> {
                updateBottomBarStatus(binding, item)
            }

            HomeItemPayloadType.UpdateAddressedUserPortrait -> {
                updatePortrait(binding, item, type)
            }

            else -> {}
        }
    }

    private fun updateBottomBarStatus(binding: HomeItemWtGroupNewBinding, item: HomeGroupItem) {
        updateLivePlaceStatus(binding, item)
        updateGroupMember(binding, item)
    }


    private fun updateGroupMember(binding: HomeItemWtGroupNewBinding, item: HomeGroupItem) {
        val isOnAirOpen = item.callInfo?.convChannelInfo?.isLivePlaceOpen() == true
        val isUserStatePlanA = ABTestManager.isUserStatePlanA
        log(
            TAG,
            "updateGroupMember:in group = ${item.userStatus == GroupUserStatus.InGroup.value}，" +
                    "isOnAirOpen = $isOnAirOpen, isUserStatePlanA =$isUserStatePlanA"
        )
        binding.tvCallConnecting.gone()
        binding.flGroupRealTimeCallNumber.gone()

        if (item.userStatus == GroupUserStatus.NotInGroup.value || item.userStatus == GroupUserStatus.Kicked.value) {
            binding.llGroupOnline.gone()
            binding.clRemoved.visible()
        } else {
            binding.llGroupOnline.visibleIf(isOnAirOpen || isUserStatePlanA.not())
            if (isUserStatePlanA && (item.onlineMembersCount ?: 0) > 0) {
                binding.llActiveMemberCount.visible()
                if (item.onlineMembersCount == 1) {
                    binding.tvActiveMemberCount.text =
                        R.string.group_online_member_count_one.asString()
                } else {
                    binding.tvActiveMemberCount.text = String.format(
                        R.string.group_online_member_count_more_than_one.asString(),
                        item.onlineMembersCount
                    )
                }
            }else{
                binding.llActiveMemberCount.gone()
            }
            binding.clRemoved.gone()
        }
        val flOnlineMembers = listOf(
            binding.flGroupOnlineMember1,
            binding.flGroupOnlineMember2,
            binding.flGroupOnlineMember3,
            binding.flGroupOnlineMember4,
            binding.flGroupOnlineMember5,
        )
        val ivOnlineMembers = listOf(
            binding.ivGroupOnlineMember1,
            binding.ivGroupOnlineMember2,
            binding.ivGroupOnlineMember3,
            binding.ivGroupOnlineMember4,
            binding.ivGroupOnlineMember5,
        )
        val onlineMemberCount = item.onlineMembersCount ?: 0
        binding.viewGreenDot.visibleIf(onlineMemberCount > 1 && isUserStatePlanA.not())
        val convChannelInfo = item.callInfo?.convChannelInfo
        val isVoiceCallAvailable = convChannelInfo?.isVoiceCallOpen().getBooleanDefault()
                && CallEntryHelper.isRealTimeCallEntryEnable(
            item.isBigGroup,
            CallType.TYPE_VOICE
        )
        val isVideoCallAvailable = convChannelInfo?.isVideoCallOpen().getBooleanDefault()
                && CallEntryHelper.isRealTimeCallEntryEnable(
            item.isBigGroup,
            CallType.TYPE_VIDEO
        )
        val isRealTimeCallAvailable = isVoiceCallAvailable || isVideoCallAvailable
        if (convChannelInfo != null && isRealTimeCallAvailable) {
            // handle voice call
            logDebug(TAG, "updateGroupMember: render real time call")

            val onlineMemberCount = convChannelInfo.channelInfo?.memberCount ?: 0

            flOnlineMembers.forEachIndexed { index, flMember ->
                flMember.gone()
            }
            binding.tvGroupMemberCount.gone()

            binding.flGroupRealTimeCallNumber.visible()
            binding.tvGroupRealTimeCallMember.text = onlineMemberCount.toString()

            logInfo(
                TAG,
                "updateGroupCurrentPlaying: item.targetId = ${item.targetId}, flGroupJoinRealTimeCall isVisible = ${binding.flGroupJoinRealTimeCall.isVisible()}"
            )
            val beforeIsVisible = binding.flGroupJoinRealTimeCall.isVisible()
            val nowVoiceCallChannel = VoiceCallPortal.currentChannelId
            val afterIsVisible =
                (convChannelInfo.isVoiceCallOpen() || convChannelInfo.isVideoCallOpen())
//                        && nowVoiceCallChannel != convChannelInfo.channelId
            if (binding.llGroupOnline.isVisible() && beforeIsVisible.not() && afterIsVisible) {
                VoiceCallTracker.onHomeGroupJoinExposure(item.targetId.toString())
            }
            if (afterIsVisible) {
                if (convChannelInfo.vcCallType== CallType.TYPE_VOICE) {
                    binding.iftvRealTimeCall.text = R.string.ic_tel.asString()
                    binding.tvCallConnecting.text = R.string.common_connecting.asString()
                } else {
                    binding.iftvRealTimeCall.text = R.string.ic_video.asString()
                    binding.tvCallConnecting.text = R.string.loading.asString()
                }
            }

            binding.vJoinClickArea.visibleIf(afterIsVisible)
            binding.flGroupJoinRealTimeCall.visibleIf(afterIsVisible)
            binding.vJoinClickArea.doOnPreDraw {
                it.layoutWidth(binding.flGroupJoinRealTimeCall.width + 14.dp)
            }
        } else {
            binding.vJoinClickArea.gone()
            binding.flGroupJoinRealTimeCall.gone()
            var showMemberSize = 0
            val showMemberList = when {
                isOnAirOpen -> {
                    val channelInfo = convChannelInfo?.channelInfo
                    showMemberSize = channelInfo?.memberCount ?: 0
                    channelInfo?.topMembers ?: emptyList()
                }
                isUserStatePlanA.not() -> {
                    showMemberSize = onlineMemberCount
                    item.onlineMembers ?: emptyList()
                }
                else -> emptyList() // A组：不需要展示群成员头像
            }

            val showMemberListSize = showMemberList.size

            logInfo(
                TAG,
                "updateGroupMember:onlineMemberListSize = ${item.onlineMembersCount}"
            )


            flOnlineMembers.forEachIndexed { index, flMember ->
                if (index < showMemberListSize) {
                    val portrait = showMemberList[index]
                    if (portrait == null) {
                        flMember.gone()
                        return@forEachIndexed
                    }
                    val ivPortrait = ivOnlineMembers[index]
                    ivPortrait.setPortrait(portrait)
                    flMember.visible()
                } else {
                    flMember.gone()
                }
            }
            if (showMemberListSize == 0) {
                binding.tvGroupMemberCount.gone()
            } else {
                binding.tvGroupMemberCount.visible()
                binding.tvGroupMemberCount.text = showMemberSize.toString()
            }

            if (isOnAirOpen) {
                binding.tvGroupMemberCount.background =
                    R.drawable.home_wt_bg_on_air_open.asDrawable()
                binding.tvGroupMemberCount.setTextColor(R.color.text_black_main.asColor())
            } else if (isUserStatePlanA.not()) {
                binding.tvGroupMemberCount.background =
                    R.drawable.home_wt_bg_offline.asDrawable()
                binding.tvGroupMemberCount.setTextColor(R.color.text_white_main.asColor())
            }
        }
    }

    override fun updatePortrait(
        binding: HomeItemWtGroupNewBinding,
        item: HomeGroupItem,
        payloadType: HomeItemPayloadType?
    ) {
        val showAnim = payloadType == HomeItemPayloadType.UpdateAddressedUserPortrait
        if (showAnim) {
            TransitionManager.beginDelayedTransition(binding.root, AutoTransition().apply {
                duration = 200
                addTarget(binding.ivAddressedUser)
            })
        }
        val addressedUserPortrait = item.addressedUserPortrait
        if (addressedUserPortrait == null) {
            binding.ivContactPortrait.setPortrait(item.portrait)
            binding.ivContactPortrait.visible()
            binding.ivAddressedUser.setImageDrawable(null)
            binding.ivAddressedUser.gone()
        } else {
            binding.ivAddressedUser.setPortrait(addressedUserPortrait)
            binding.ivAddressedUser.visible()
            binding.ivContactPortrait.gone()
        }
    }

    override fun updatePlayingState(
        binding: HomeItemWtGroupNewBinding,
        item: HomeGroupItem
    ) {
        super.updatePlayingState(binding, item)
        val isPlaying = item.isPlaying
        if (isPlaying) {
            item.playingUserPortrait?.let {
                binding.ivPlayingPortrait.setPortrait(item.playingUserPortrait)
            }
            binding.clPlayingPortrait.visible()
        }
        if (binding.clPlayingPortrait.isVisible) {
            val scale = if (isPlaying) 1f else 0f
            binding.clPlayingPortrait
                .animate()
                .scaleX(scale)
                .scaleY(scale)
                .setDuration(100)
                .withEndAction {
                    if (!isPlaying) {
                        binding.ivPlayingPortrait.setImageDrawable(null)
                        binding.clPlayingPortrait.gone()
                    }
                }.start()
        }
    }

    override fun getPortraitView(binding: HomeItemWtGroupNewBinding): View {
        return binding.ivContactPortrait
    }

    override fun getPortraitRelatedViews(binding: HomeItemWtGroupNewBinding): List<View> {
        return listOf(
            getPortraitView(binding),
            binding.viewGroupPortraitBg,
            binding.ivAddressedUser,
            binding.viewLivePlaceTag,
            binding.flMuteSound
        )
    }
}