package com.interfun.buz.home.view.widget

import android.animation.ArgbEvaluator
import android.animation.ValueAnimator
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import androidx.transition.AutoTransition
import androidx.transition.TransitionManager
import com.interfun.buz.base.ktx.click
import com.interfun.buz.chat.wt.manager.DriveModelManager
import com.interfun.buz.chat.wt.manager.HeadsetData
import com.interfun.buz.chat.wt.view.interpolator.AutoPlayToggleInterpolator
import com.interfun.buz.home.databinding.HomeWidgetQuiteModeBinding

class HomeQuiteModeView
@JvmOverloads constructor(context: Context, attrs: AttributeSet? = null) :
    FrameLayout(context, attrs) {

    private val binding = HomeWidgetQuiteModeBinding.inflate(LayoutInflater.from(context), this,true)
    private var isQuietModeEnable = false
    private val colorAnimator = ValueAnimator()
    var onClickSwitchQuiteMode : () -> Unit = {}
    var onClickQuiteModeTips : () -> Unit = {}

    init {
        binding.autoPlayToggleView.click {

        }
    }

    fun update(isQuietModeEnable: Boolean, tips: String, headsetData: HeadsetData?) {
        if (isQuietModeEnable != this.isQuietModeEnable) {
            binding.autoPlayToggleView.setChecked(!isQuietModeEnable, true)
            binding.llQuiteMode.setBackgroundColor()
        }
        binding.autoPlayToggleView.setHeadset(headsetData, true)
        binding.tvQuiteMode.text = tips
    }

    private fun setColorWithAnim(){
        TransitionManager.beginDelayedTransition(this,AutoTransition())
        colorAnimator.setIntValues(startBackgroundColor, endBackgroundColor)
        colorAnimator.setEvaluator(ArgbEvaluator())
        colorAnimator.duration = 200
        colorAnimator.interpolator = AutoPlayToggleInterpolator()
        colorAnimator.addUpdateListener { animation ->
            currentBackgroundColor = animation.animatedValue as Int
            invalidate()
        }
        colorAnimator.start()
    }
}