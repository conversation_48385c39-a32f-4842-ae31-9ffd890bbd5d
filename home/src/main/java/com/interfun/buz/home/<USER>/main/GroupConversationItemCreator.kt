package com.interfun.buz.home.data.main

import com.interfun.buz.common.widget.portrait.group.GroupPortrait
import com.interfun.buz.common.widget.portrait.group.UserPortrait
import com.interfun.buz.domain.im.social.entity.GroupConversation
import com.interfun.buz.domain.im.social.entity.displayName
import com.interfun.buz.domain.im.social.entity.portrait
import com.interfun.buz.home.data.entity.MainLiveInfo
import com.interfun.buz.home.entity.HomeConversationItem
import com.interfun.buz.home.entity.HomeGroupItem
import com.interfun.buz.home.entity.HomeItemCallInfo
import com.interfun.buz.im.ktx.getConvTargetIdLong
import com.interfun.buz.onair.bean.isLivePlaceOpen
import com.lizhi.im5.sdk.conversation.IM5ConversationType

class GroupConversationItemCreator : MainItemCreator<GroupConversation> {
    override fun create(
        conversation: GroupConversation,
        mainLiveInfo: MainLiveInfo
    ): HomeConversationItem {
        val (playingMsg, playingPortrait) = mainLiveInfo.playingTargetInfo
        val isPlaying =
            playingMsg?.conversationType == IM5ConversationType.GROUP && playingMsg.getConvTargetIdLong() == conversation.convId
        val group = conversation.groupComposite?.buzGroup
        val groupExtra = conversation.groupComposite?.buzGroupExtra
        val groupId = conversation.convId
        val groupOnlineMap = mainLiveInfo.groupOnlineStatus
        val onlineInfo = groupOnlineMap[groupId]
        val mentionedUser =
            if (groupId == mainLiveInfo.mentionedBotInfo.first) mainLiveInfo.mentionedBotInfo.second else null
        val channelStatus = mainLiveInfo.convChannelInfo[groupId]
        val isLivePlaceOpen =
            mainLiveInfo.convChannelInfo[conversation.convId]?.isLivePlaceOpen() == true
        return HomeGroupItem(
            conversation.convId,
            conversation.displayName,
            conversation.portrait as GroupPortrait,
            isRecordingTo = mainLiveInfo.recordingTarget?.targetId == conversation.convId,
            unReadCount = conversation.unReadCount,
            mentionMeCount = conversation.mentionMeCount,
            isMuteNotification = groupExtra?.isMuteNotification ?: false,
            isMuteMessages = groupExtra?.isMuteMessages ?: false,
            isPlaying = isPlaying,
            playingUserPortrait = if (isPlaying) playingPortrait else null,
            isBigGroup = group?.isBigGroup ?: false,
            onlineMembers = onlineInfo?.onlineMembers?.map { it.portrait ?: "" },
            onlineMembersCount = onlineInfo?.onlineMemberCount,
            addressedUserPortrait = mentionedUser?.let { UserPortrait(it.userId, it.portrait) },
            userStatus = groupExtra?.userStatus,
            callInfo = if (channelStatus == null) null else HomeItemCallInfo(channelStatus, false),
            isOnLivePlace = isLivePlaceOpen,
        )
    }

}