package com.interfun.buz.home.view.widget

import android.view.Gravity
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.chat.common.utils.ChatTracker
import com.interfun.buz.common.ktx.isMe
import com.interfun.buz.common.widget.view.BuzToolTips
import com.interfun.buz.home.entity.HomeSelectedItemBasicInfo

class PressRecordToolTip(
    private val anchorView: View,
    private val parent: ConstraintLayout,
    offsetY: Int = 0,
) {
    private val TAG = "PressRecordToolTip"
    private val pressToRecordTips by lazy {
        BuzToolTips.BuzToolTipsBuilder(anchorView,parent)
            .titleDescGravity(Gravity.CENTER)
            .offsetY(offsetY)
            .build()
    }

    private var isShowFinish = false

    fun startShow(item: HomeSelectedItemBasicInfo){
        isShowFinish = false
        updateToolTipsDesc(item)
    }

    fun resetIsShow(){
        isShowFinish = false
    }

    fun dismiss(finish:Boolean = false){
        isShowFinish = finish
        pressToRecordTips.dismiss()
    }

    fun updateToolTipsDesc(item: HomeSelectedItemBasicInfo) {
        logInfo(TAG, "updateToolTipsDesc: $isShowFinish.  ${item}")
        if (isShowFinish) return
        if (item is HomeSelectedItemBasicInfo.AddButton || item is HomeSelectedItemBasicInfo.None) {
            dismiss(false)
            return
        }
        val user = (item as? HomeSelectedItemBasicInfo.User)?.userComposite?.user
        if (user?.isOfficial == true || user?.userId?.isMe() == true || user?.isRobot == true){
            dismiss(false)
            return
        }

        val title =  when(item){
            is HomeSelectedItemBasicInfo.Group -> item.groupComposite?.buzGroup?.groupName ?: ""
            is HomeSelectedItemBasicInfo.User -> item.userComposite?.firstNickName ?: ""
            else -> ""
        }

        logInfo(TAG,"updateToolTipsDesc: title = $title")

        if (title.isNotEmpty()){
            pressToRecordTips.updateDesc(title)
        }

        if (!pressToRecordTips.isShowing() && title.isNotEmpty()){
            ChatTracker.onExposePressRecordTip()
            pressToRecordTips.show(anchorView.context)
        }
    }
}