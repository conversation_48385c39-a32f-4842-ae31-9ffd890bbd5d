package com.interfun.buz.home.data.transform

import com.interfun.buz.chat.common.manager.TranslationMessageManager
import com.interfun.buz.domain.im.social.entity.Conversation
import com.interfun.buz.home.data.entity.PreviewLiveInfo
import com.lizhi.im5.sdk.message.IMessage
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.*
import javax.inject.Inject

class PreviewCombinedTransform @Inject constructor(
    private val userInfoUpdateTransform: PreviewMsgUserInfoUpdateTransform,
    private val fileUploadTransform: FileUploadTransform,
    private val fileDownloadTransform: FileDownloadTransform
){
    fun transform(
        originList: List<Conversation>,
        livePlayingMsgFlow: Flow<IMessage?>
    ): Flow<PreviewLiveInfo> =
        combine(
            livePlayingMsgFlow,
            userInfoUpdateTransform.transform(originList),
            fileUploadTransform.transform(originList),
            fileDownloadTransform.transform(originList),
            TranslationMessageManager.autoTranslateChangeFlow.map { Unit }.onStart { emit(Unit) },//触发一下更新，因为不高频，目前数据都是里面直接读了
        ) { livePlayingMsg, userInfo, fileUpload, fileDownload, _ ->
            PreviewLiveInfo(fileDownload, fileUpload, userInfo, livePlayingMsg)
        }.flowOn(Dispatchers.Default)
}