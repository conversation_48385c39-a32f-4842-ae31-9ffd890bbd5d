package com.interfun.buz.home.data.preview

import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.chat.wt.entity.HomeMsgPreviewModel.BaseMsgInfo
import com.interfun.buz.chat.wt.entity.HomeMsgPreviewModel.FilePreview
import com.interfun.buz.chat.wt.entity.HomeMsgPreviewModel.IMsgPreview
import com.interfun.buz.domain.im.social.entity.Conversation
import com.interfun.buz.download.bean.DownloadStatus
import com.interfun.buz.download.bean.FileIdentity
import com.interfun.buz.home.data.entity.PreviewLiveInfo
import com.interfun.buz.im.entity.IMType
import com.interfun.buz.im.ktx.buzSendingState
import com.interfun.buz.im.message.BuzFileMessage
import com.interfun.buz.im.util.IMMsgIdentity
import com.lizhi.im5.sdk.message.IMessage

class FilePreviewCreator : MessagePreviewCreator<BuzFileMessage>() {

    override val type: Int = IMType.TYPE_FILE_MSG

    override fun createMsgPreview(
        conv: Conversation,
        liveInfo: PreviewLiveInfo,
        msg: IMessage,
        content: BuzFileMessage,
        baseMsgInfo: BaseMsgInfo
    ): IMsgPreview {
        val fileRemoteUrl = content.remoteUrl ?: ""
        val fileLocalPath = content.localPath ?: ""
        val fileName = content.name ?: ""
        val fileIdentity = FileIdentity.from(fileRemoteUrl, fileName)
        val downloadInfo = liveInfo.downloadState[fileIdentity]
        val imMsgIdentity =
            IMMsgIdentity(msg.msgId, msg.conversationType)
        val buzSendingState = liveInfo.uploadState[imMsgIdentity]
        return FilePreview(
            fileLocalPath = fileLocalPath,
            fileRemoteUrl = fileRemoteUrl,
            fileName = fileName,
            fileSize = content.totalBytes,
            fileExtension = content.extName ?: "",
            buzSendingState = buzSendingState ?: msg.buzSendingState,
            downloadProgress = downloadInfo?.progress ?: 0,
            downloadFileStatus = downloadInfo?.state ?: DownloadStatus.IDLE,
            baseMsgInfo = baseMsgInfo
        )
    }
}