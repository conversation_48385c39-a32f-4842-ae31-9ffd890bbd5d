<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.luck.picture.lib">

    <application>
        <provider
            android:name="com.luck.picture.lib.basic.PictureFileProvider"
            android:authorities="${applicationId}.luckProvider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/ps_file_paths" />
        </provider>

        <activity
            android:name=".basic.PictureSelectorSupporterActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:theme="@style/Base.Theme.NoActionBar">

        </activity>

        <activity
            android:name=".basic.PictureSelectorTransparentActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:theme="@style/Picture.Theme.Translucent">

        </activity>

    </application>

</manifest>