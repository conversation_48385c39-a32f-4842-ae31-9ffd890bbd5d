package com.luck.picture.lib.config;

/**
 * @author：luck
 * @date：2021/12/1 6:49 下午
 * @describe：CustomIntentKey
 */
public class CustomIntentKey {
    /**
     * 自定义数据
     */
    public static final String EXTRA_CUSTOM_EXTRA_DATA = "customExtraData";
    /**
     * 输出的路径
     */
    public static final String EXTRA_OUT_PUT_PATH = "outPutPath";

    /**
     * 图片宽度
     */
    public static final String EXTRA_IMAGE_WIDTH = "imageWidth";
    /**
     * 图片高度
     */
    public static final String EXTRA_IMAGE_HEIGHT = "imageHeight";
    /**
     * 图片X轴偏移量
     */
    public static final String EXTRA_OFFSET_X = "offsetX";
    /**
     * 图片Y轴偏移量
     */
    public static final String EXTRA_OFFSET_Y = "offsetY";
    /**
     * 图片旋转比例
     */
    public static final String EXTRA_ASPECT_RATIO = "aspectRatio";

    /**
     * uCrop的裁剪输出路径Key
     */
    public static final String EXTRA_OUTPUT_URI = "com.yalantis.ucrop.OutputUri";
}
