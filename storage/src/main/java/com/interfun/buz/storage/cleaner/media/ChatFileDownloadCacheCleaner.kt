package com.interfun.buz.storage.cleaner.media

import androidx.annotation.OptIn
import androidx.media3.common.util.UnstableApi
import com.interfun.buz.base.ktx.logError
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.common.database.entity.BuzMediaCacheEntity
import com.interfun.buz.common.manager.storage.BuzMediaRecordManager
import com.interfun.buz.download.db.entity.BuzDownloadCacheEntity
import com.interfun.buz.im.IMMediator
import com.interfun.buz.im.repo.IMFileDownloadRepository
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope

/**
 * Author: ChenYouSheng
 * Date: 2025/06/03
 * Email: <EMAIL>
 * Desc: 聊天页文件存清理实现类
 */
internal class ChatFileDownloadCacheCleaner(list: List<BuzMediaCacheEntity>) :
    BaseMediaCacheCleaner(list) {

    companion object {
        const val TAG = "ChatFileDownloadCacheCleaner"
    }

    private val imFileDownloadRepository: IMFileDownloadRepository
        get() {
            return IMMediator.getIMSingletonEntryPoint().fileDownloadRepository()
        }


    @OptIn(UnstableApi::class)
    override suspend fun delete() {
        val taskIdsSet = list.map { it.mediaIndex }.toSet() // mediaIndex = taskId
        logInfo(TAG, "delete==>list=${list.size}, taskIdsSet=${taskIdsSet.size}")
        excDelete(taskIdsSet)
    }

    @OptIn(UnstableApi::class)
    override suspend fun autoDelete(day: Int) {
        val deletableMediaSet = list
            .filter { canAutoDelete(it, day) }
            .map { it.mediaIndex }
            .toSet()
        excDelete(deletableMediaSet)
    }

    private suspend fun excDelete(taskIdSet: Set<String>) = coroutineScope {
        val successDeleteList = mutableListOf<BuzDownloadCacheEntity>()
        // 已下载的所有缓存
        val downloadCacheList = imFileDownloadRepository.queryAllDownloadCache()
        logInfo(TAG, "downloadCacheList=${downloadCacheList.size}, taskIdSet=${taskIdSet.size}")
        if (downloadCacheList.isEmpty() && taskIdSet.isNotEmpty()) {
            logInfo(TAG, "downloadCacheList is empty，clear buz_media_cache cache")
            val recordList = list.filter { it.mediaIndex in taskIdSet }
            BuzMediaRecordManager.removeMediaCacheRecord(recordList)
            return@coroutineScope
        }

        downloadCacheList.map { cache ->
            async {
                if (cache.taskId in taskIdSet) {
                    try {
                        val success = imFileDownloadRepository.deleteFile(
                            cache.remoteUrl,
                            cache.fileName
                        ) == true
                        if (success) {
                            successDeleteList.add(cache)
                        }
                    } catch (e: Exception) {
                        logError(TAG, e, "删除文件失败: ${cache.remoteUrl}, error: ${e.message}")
                    }
                }
            }
        }.awaitAll()

        handlePostDelete(successDeleteList)
    }

    private suspend fun handlePostDelete(successDeleteList: List<BuzDownloadCacheEntity>) {
        if (successDeleteList.isEmpty()) return
        val successTaskSet = successDeleteList.map { it.taskId }.toSet()
        val recordList = list.filter { it.mediaIndex in successTaskSet }
        // 清除缓存管理记录
        BuzMediaRecordManager.removeMediaCacheRecord(recordList)
        // 清除下载记录(兜底）
        imFileDownloadRepository.removeCacheList(successDeleteList)
    }


    @OptIn(UnstableApi::class)
    override suspend fun getSize(): Long {
        return imFileDownloadRepository.getAllDownloadFileSize()
    }
}