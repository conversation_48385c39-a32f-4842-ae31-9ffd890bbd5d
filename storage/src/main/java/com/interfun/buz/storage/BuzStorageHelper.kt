package com.interfun.buz.storage

import com.interfun.buz.base.ktx.launchIO
import com.interfun.buz.base.ktx.logDebug
import com.interfun.buz.base.ktx.logInfo
import com.interfun.buz.common.database.entity.BuzMediaCacheEntity
import com.interfun.buz.common.database.entity.ConvType
import com.interfun.buz.common.database.entity.MediaCacheType
import com.interfun.buz.common.interfaces.DeleteCacheType
import com.interfun.buz.common.interfaces.IStorageHelper
import com.interfun.buz.common.ktx.getLongDefault
import com.interfun.buz.common.manager.storage.BuzMediaRecordManager
import com.interfun.buz.common.manager.userLifecycleScope
import com.interfun.buz.common.utils.StorageUtil.toAccurateFileSize
import com.interfun.buz.im.IMAgent
import com.interfun.buz.storage.cleaner.ICacheCleaner
import com.interfun.buz.storage.cleaner.im.IMCacheCleaner
import com.interfun.buz.storage.cleaner.im.IMFileCacheCleaner
import com.interfun.buz.storage.cleaner.media.*
import com.interfun.buz.storage.cleaner.other.TempImageCacheCleaner
import com.interfun.buz.storage.cleaner.other.VideoCacheSpanCleaner
import com.interfun.buz.storage.cleaner.other.VideoShareCacheCleaner
import com.interfun.buz.storage.tracker.StorageTracker
import com.lizhi.im5.sdk.base.IM5CacheType
import com.lizhi.im5.sdk.conversation.IM5ConversationType
import kotlinx.coroutines.*

/**
 * Author: ChenYouSheng
 * Date: 2024/6/21
 * Email: <EMAIL>
 * Desc: 缓存清理帮助类 https://vocalbeats.sg.larksuite.com/wiki/KaRVwT4DXibVwEkycXXlt6fEgXf
 */
class BuzStorageHelper(private val userId: Long) : IStorageHelper {
    companion object {
        const val TAG = "BuzStorageHelper"
    }

    private var cacheCleanerList: List<ICacheCleaner>? = null

    /**
     * 初始化缓存数据
     */
    private suspend fun initUserCache(): List<ICacheCleaner> = withContext(Dispatchers.IO) {
        val mediaCacheEntityList =
            BuzMediaRecordManager.queryMediaCacheRecordByUserIdExcludeOthers(userId)
        logInfo(TAG,"initUserCache==>mediaCacheEntityList: ${mediaCacheEntityList.size},userId=${userId}")
        val collectList = ArrayList<ICacheCleaner>().apply {
            // 媒体缓存（业务侧使用图片、视频、音频、文件下载时产生的缓存）
            addAll(collectUserMediaCacheRecord(mediaCacheEntityList))
            // 设备缓存
            val deviceCacheList = collectDevicesCacheRecord()
            if (deviceCacheList.isNotEmpty()) {
                addAll(deviceCacheList)
            }
            // 其他缓存（例如IM）
            val userOtherCacheList = collectUserOtherCacheList()
            if (userOtherCacheList.isNotEmpty()) {
                addAll(userOtherCacheList)
            }
        }
        collectList
    }


    /**
     * 获取当前用户可删除的缓存大小
     */
    override suspend fun getUserCacheSize(): Long = withContext(Dispatchers.IO) {
        if (cacheCleanerList == null) {
            cacheCleanerList = initUserCache()
        }
        cacheCleanerList?.sumOf {
            logDebug(TAG, "$it, size: ${it.getSize().toAccurateFileSize()}")
            it.getSize()
        }.getLongDefault()
    }

    /**
     * 手动删除当前用户使用的媒体缓存
     */
    override suspend fun deleteUserCache() = withContext {
        var cacheSize = 0L
        val asyncMediaCacheRecordExcludeOthers = async {
            if (cacheCleanerList == null) {
                cacheCleanerList = initUserCache()
            }
            cacheCleanerList?.forEach {
                cacheSize += it.getSize()
                it.delete()
            }
        }

        val asyncSharedMediaCacheRecord = async {
            //删除该用户与其他用户公用媒体的列表数据
            val sharedRecordMediaList =
                BuzMediaRecordManager.querySharedMediaCacheRecordByUserId(userId)
            BuzMediaRecordManager.removeMediaCacheRecord(sharedRecordMediaList)
        }

        asyncMediaCacheRecordExcludeOthers.await()
        asyncSharedMediaCacheRecord.await()

        StorageTracker.onCacheClearedResult(false, cacheSize)
    }

    /**
     * 自动删除缓存
     */
    override fun autoDelete(day: Int) {
        if (day == -1) return
        launchIO {
            var cacheSize = 0L
            val asyncOutdatedMediaCacheRecord = async {
                val outdatedMediaCacheRecord =
                    BuzMediaRecordManager.queryOutdatedMediaCacheRecordExcludeOthers(userId, day)
                if (outdatedMediaCacheRecord.isNotEmpty()) {
                    // 删除缓存文件
                    collectUserMediaCacheRecord(outdatedMediaCacheRecord).forEach {
                        cacheSize += it.getSize()
                        it.autoDelete(day)
                    }
                }
            }

            val asyncSharedMediaCacheRecord = async {
                val sharedRecordMediaList =
                    BuzMediaRecordManager.queryOutdatedSharedMediaCacheRecord(userId, day)
                //删除该用户与其他用户公用媒体的列表数据
                BuzMediaRecordManager.removeMediaCacheRecord(sharedRecordMediaList)
            }

            // 删除其他缓存文件
            val asyncOtherCacheRecord = async {
                (collectDevicesCacheRecord() + collectUserOtherCacheList()).forEach {
                    cacheSize += it.getSize()
                    it.autoDelete(day)
                }
            }

            asyncOutdatedMediaCacheRecord.await()
            asyncSharedMediaCacheRecord.await()
            asyncOtherCacheRecord.await()

            StorageTracker.onCacheClearedResult(true, cacheSize)
        }
    }

    /**
     * 删除单条缓存，触发时机（1）删除IM消息，2）撤回IM消息
     */
    override fun deleteSingleMsgCache(
        userId: Long,
        targetId: Long,
        convType: Int,
        servMsgId: Long
    ) {
        launchIO {
            var cacheSize = 0L
            val asyncMediaCacheEntityList = async {
                // 移除缓存文件&移除记录
                val mediaCacheEntityList =
                    BuzMediaRecordManager.queryMediaCacheRecordExcludeOthers(
                        userId = userId,
                        targetId = targetId,
                        convType = convType,
                        servMsgId = servMsgId
                    )
                collectUserMediaCacheRecord(mediaCacheEntityList).forEach {
                    cacheSize += it.getSize()
                    it.delete()
                }
            }
            val asyncSharedRecordMediaList = async {
                //删除该用户与其他用户公用媒体的列表数据
                val sharedRecordMediaList = BuzMediaRecordManager.querySharedMediaCacheRecord(
                    userId = userId,
                    targetId = targetId,
                    convType = convType,
                    servMsgId = servMsgId
                )
                BuzMediaRecordManager.removeMediaCacheRecord(sharedRecordMediaList)
            }

            asyncMediaCacheEntityList.await()
            asyncSharedRecordMediaList.await()

            StorageTracker.onCacheClearedResult(false, cacheSize)
        }
    }


    /**
     * @param deleteCacheType :批量删除条缓存，触发时机（1）清除聊天记录，2）删除好友，3）退出群聊，4）注销账号 5）删会话
     */
    override fun deleteBatchMsgCache(
        userId: Long,
        deleteCacheType: DeleteCacheType,
        targetId: Long,
        convType: Int
    ) {
        launchIO {
            logInfo(TAG,"deleteBatchMsgCache: $deleteCacheType, targetId=$targetId, convType=$convType")
            var cacheSize = 0L
            // 查询当前用户和这个会话targetId有关的所有缓存记录,但是不包含和其他用户共用的记录
            val asyncMediaCacheEntityList = async {
                // 执行文件清理操作
                val recordsToClean =
                    BuzMediaRecordManager.queryMediaCacheRecordByTargetIdExcludeOthers(
                        userId,
                        targetId,
                        convType
                    )
                collectUserMediaCacheRecord(recordsToClean).forEach {
                    cacheSize += it.getSize()
                    it.delete()
                }
            }
            val asyncSharedRecordMediaList = async {
                //删除该用户与其他用户公用媒体的列表数据
                val sharedRecordMediaList =
                    BuzMediaRecordManager.querySharedMediaCacheRecordByTargetId(
                        userId,
                        targetId,
                        convType
                    )
                BuzMediaRecordManager.removeMediaCacheRecord(sharedRecordMediaList)
            }

            val removeIMCacheCleaner =
                if (deleteCacheType == DeleteCacheType.DeleteFriend ||
                    deleteCacheType == DeleteCacheType.ExitGroup ||
                    deleteCacheType == DeleteCacheType.DeleteConv) {
                    val conversationType =
                        if (convType == ConvType.PrivateChat.value)
                            IM5ConversationType.PRIVATE
                        else
                            IM5ConversationType.GROUP
                    async {
                        cacheSize += IMAgent.getCacheByTargetId(
                            targetId = targetId.toString(),
                            convType = conversationType,
                            cacheType = IM5CacheType.ALL
                        )
                        IMAgent.removeCacheByTargetId(
                            targetId = targetId.toString(),
                            convType = conversationType,
                            cacheType = IM5CacheType.ALL
                        )
                        // 暂停上传消息
                        IMAgent.pauseUploadMediaMessageByConversation(
                            targetId = targetId.toString(),
                            convType = conversationType
                        )
                    }
                } else null

            asyncMediaCacheEntityList.await()
            asyncSharedRecordMediaList.await()
            removeIMCacheCleaner?.await()

            StorageTracker.onCacheClearedResult(false, cacheSize)
        }
    }

    override suspend fun deleteUserCacheAsync() = withContext {
        var cacheSize = 0L
        val asyncMediaCacheRecordExcludeOthers = async {
            if (cacheCleanerList == null) {
                cacheCleanerList = initUserCache()
            }
            cacheCleanerList?.forEach {
                cacheSize += it.getSize()
                it.delete()
            }
        }

        val asyncSharedMediaCacheRecord = async {
            //删除该用户与其他用户公用媒体的列表数据
            val sharedRecordMediaList =
                BuzMediaRecordManager.querySharedMediaCacheRecordByUserId(userId)
            BuzMediaRecordManager.removeMediaCacheRecord(sharedRecordMediaList)
        }

        asyncMediaCacheRecordExcludeOthers.await()
        asyncSharedMediaCacheRecord.await()
        logInfo(TAG,"deleteUserCacheAsync done: $userId")
        StorageTracker.onCacheClearedResult(false, cacheSize)
    }

    /**
     * 收集当前用户的媒体消息缓存，这部分存在[BuzMediaCacheEntity]缓存记录表中
     */
    private fun collectUserMediaCacheRecord(list: List<BuzMediaCacheEntity>): List<BaseMediaCacheCleaner> {
        val groupedCache = list.groupBy { it.mediaType }
        return MediaCacheType.entries
            .filter { it != MediaCacheType.Unknown }
            .mapNotNull { mediaType ->
                val filteredCache = groupedCache[mediaType] ?: emptyList()
                when (mediaType) {
                    MediaCacheType.Image -> ImageMediaCacheCleaner(filteredCache)
                    MediaCacheType.Video -> VideoMediaCacheCleaner(filteredCache)
                    MediaCacheType.Audio -> AudioMediaCacheCleaner(filteredCache)
                    MediaCacheType.Gif -> GifMediaCacheCleaner(filteredCache)
                    MediaCacheType.File -> ChatFileDownloadCacheCleaner(filteredCache)
                    else -> null
                }
            }
    }

    /**
     * 收集不在缓存记录表[BuzMediaCacheEntity]中的和当前用户有关的缓存，例如：IM的缓存
     */
    private fun collectUserOtherCacheList(): Collection<ICacheCleaner> {
        return mutableListOf<ICacheCleaner>().apply {
            // IM缓存
            add(IMCacheCleaner())
            add(IMFileCacheCleaner())
        }
    }

    /**
     * 收集设备维度的缓存,这部分没有维护在[BuzMediaCacheEntity]中,也就是和其他用户共用目录的，例如：视频分享产生的mp4文件
     */
    private fun collectDevicesCacheRecord(): List<ICacheCleaner> {
        return mutableListOf<ICacheCleaner>().apply {
            // 视频分享缓存
            add(VideoShareCacheCleaner())
            // 图片缓存(取消拍照或者取消录频遗留的）
            add(TempImageCacheCleaner())
            // 视频块缓存(没有维护在缓存记录表中的，正常情况下不会有，这里是兜底处理)
            add(VideoCacheSpanCleaner())
        }
    }

    @OptIn(DelicateCoroutinesApi::class)
    private fun launchIO(block: suspend CoroutineScope.() -> Unit): Job =
        (userLifecycleScope ?: GlobalScope).launchIO(block)

    private suspend fun withContext(block: suspend CoroutineScope.() -> Unit) {
        withContext(userLifecycleScope?.coroutineContext?: Dispatchers.IO) {
            block.invoke(this)
        }
    }
}